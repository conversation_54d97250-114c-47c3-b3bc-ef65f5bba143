#!/bin/bash
# Auto-healing pipeline runner wrapper script

echo "🤖 LexGenius Auto-Healing Pipeline Runner"
echo "=========================================="

# Default values
CONFIG="transform.yml"
MAX_RETRIES=5
RETRY_DELAY=30
WATCH_INTERVAL=300

# Parse arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --config) CONFIG="$2"; shift ;;
        --max-retries) MAX_RETRIES="$2"; shift ;;
        --retry-delay) RETRY_DELAY="$2"; shift ;;
        --watch-interval) WATCH_INTERVAL="$2"; shift ;;
        --help) 
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --config <file>         Pipeline config file (default: transform.yml)"
            echo "  --max-retries <num>     Maximum retry attempts (default: 5)"
            echo "  --retry-delay <sec>     Delay between retries (default: 30)"
            echo "  --watch-interval <sec>  Interval between runs (default: 300)"
            echo "  --help                  Show this help message"
            exit 0
            ;;
        *) echo "Unknown parameter: $1"; exit 1 ;;
    esac
    shift
done

echo "📋 Configuration:"
echo "   Config file: $CONFIG"
echo "   Max retries: $MAX_RETRIES"
echo "   Retry delay: ${RETRY_DELAY}s"
echo "   Watch interval: ${WATCH_INTERVAL}s"
echo ""

# Run the auto-healing pipeline
python3 auto_heal_pipeline.py \
    --config "$CONFIG" \
    --max-retries "$MAX_RETRIES" \
    --retry-delay "$RETRY_DELAY" \
    --watch-interval "$WATCH_INTERVAL"