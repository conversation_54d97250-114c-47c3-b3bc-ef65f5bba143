{"mcpServers": {"filesystem": {"command": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/PycharmProjects", "/Users/<USER>/Documents"], "env": {"PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin:/usr/local/bin:/usr/bin:/bin", "NODE_PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/lib/node_modules"}}, "memory": {"command": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin:/usr/local/bin:/usr/bin:/bin", "NODE_PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/lib/node_modules"}}, "puppeteer": {"command": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {"PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin:/usr/local/bin:/usr/bin:/bin", "NODE_PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/lib/node_modules"}}, "github": {"command": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin:/usr/local/bin:/usr/bin:/bin", "NODE_PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/lib/node_modules", "GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "Context7": {"command": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin/npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/bin:/usr/local/bin:/usr/bin:/bin", "NODE_PATH": "/Users/<USER>/.nvm/versions/node/v23.10.0/lib/node_modules"}}, "MCP_DOCKER": {"command": "docker", "args": ["run", "-l", "mcp.client=claude-desktop", "--rm", "-i", "alpine/socat", "STDIO", "TCP:host.docker.internal:8811"]}}}