#!/usr/bin/env python3
"""
Test script to validate the query page field recovery mechanism.
This simulates the scenario where navigation back to query page fails initially.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unittest.mock import <PERSON><PERSON>, AsyncMock, MagicMock
from src.pacer.components.processing.sequential_docket_processor import ReturnAndContinueStep


async def test_query_page_recovery():
    """Test the query page validation with field recovery."""
    
    print("Testing query page validation recovery mechanism...")
    print("=" * 70)
    
    # Create test step
    step = ReturnAndContinueStep()
    
    # Mock page and elements
    mock_page = Mock()
    mock_query_input = Mock()
    mock_submit_button = Mock()
    
    # Mock logger
    mock_logger = Mock()
    mock_logger.debug = Mock()
    mock_logger.info = Mock()
    mock_logger.warning = Mock()
    mock_logger.error = Mock()
    
    # Mock context
    mock_context = Mock()
    mock_context.docket_number = "1:25-cv-9786"
    
    # Configure mocks for page locators
    mock_page.locator.side_effect = lambda selector: {
        "input[name='case_num']": mock_query_input,
        "input[type='submit'], button[type='submit']": mock_submit_button
    }.get(selector, Mock())
    
    # Configure wait_for_timeout
    mock_page.wait_for_timeout = AsyncMock()
    
    # Test Scenario 1: First attempt fails, recovery succeeds
    print("📋 Test 1: Initial validation fails, recovery succeeds")
    
    # First attempt - submit button not visible (timeout)
    mock_submit_button.wait_for = AsyncMock(side_effect=asyncio.TimeoutError("Submit button timeout"))
    mock_query_input.wait_for = AsyncMock()
    mock_query_input.is_visible = AsyncMock(return_value=True)
    mock_submit_button.is_visible = AsyncMock(return_value=False)
    
    # Recovery attempt setup
    mock_query_input.input_value = AsyncMock(return_value="1:25-cv-9777")  # Old value
    mock_query_input.clear = AsyncMock()
    mock_query_input.fill = AsyncMock()
    mock_query_input.dispatch_event = AsyncMock()
    
    # After recovery, submit button becomes visible
    async def side_effect_recovery(*args, **kwargs):
        # First call fails (initial), second call succeeds (after recovery)
        if not hasattr(side_effect_recovery, 'call_count'):
            side_effect_recovery.call_count = 0
        side_effect_recovery.call_count += 1
        
        if side_effect_recovery.call_count <= 2:  # First two calls fail
            raise asyncio.TimeoutError("Submit button timeout")
        else:  # Third call succeeds
            return True
    
    mock_submit_button.wait_for = AsyncMock(side_effect=side_effect_recovery)
    
    # After recovery, elements should be visible
    mock_submit_button.is_visible = AsyncMock(return_value=True)
    
    try:
        result = await step._validate_query_page_ready(mock_page, mock_logger, mock_context)
        print(f"✅ PASS: Recovery validation returned: {result}")
        
        # Verify recovery actions were called
        mock_query_input.clear.assert_called_once()
        mock_query_input.fill.assert_called_once()
        mock_query_input.dispatch_event.assert_called_with('blur')
        
        print("✅ PASS: Field clearing and re-filling actions were executed")
        
    except Exception as e:
        print(f"❌ FAIL: Recovery test failed: {e}")
        return False
    
    print()
    
    # Test Scenario 2: Both attempts fail
    print("📋 Test 2: Both initial and recovery attempts fail")
    
    # Reset mocks
    mock_submit_button.wait_for = AsyncMock(side_effect=asyncio.TimeoutError("Submit button timeout"))
    mock_submit_button.is_visible = AsyncMock(return_value=False)
    
    try:
        result = await step._validate_query_page_ready(mock_page, mock_logger, mock_context)
        print(f"❌ FAIL: Should have thrown exception but returned: {result}")
        return False
        
    except Exception as e:
        print(f"✅ PASS: Correctly threw exception after recovery failure: {type(e).__name__}")
    
    print()
    
    # Test Scenario 3: First attempt succeeds (no recovery needed)
    print("📋 Test 3: Initial validation succeeds (no recovery needed)")
    
    # Reset mocks for success case
    mock_query_input.wait_for = AsyncMock()
    mock_submit_button.wait_for = AsyncMock()
    mock_query_input.is_visible = AsyncMock(return_value=True)
    mock_submit_button.is_visible = AsyncMock(return_value=True)
    
    try:
        result = await step._validate_query_page_ready(mock_page, mock_logger, mock_context)
        print(f"✅ PASS: Initial validation succeeded: {result}")
        
        # Verify recovery was not attempted
        recovery_calls = mock_query_input.clear.call_count
        if recovery_calls == 1:  # Only from previous test
            print("✅ PASS: Recovery was not attempted for successful validation")
        else:
            print(f"⚠️ WARNING: Unexpected recovery calls: {recovery_calls}")
        
    except Exception as e:
        print(f"❌ FAIL: Initial success test failed: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 All query page recovery tests completed successfully!")
    return True


async def test_docket_formatting_in_recovery():
    """Test that docket numbers are properly formatted during recovery."""
    
    print("\nTesting docket number formatting during recovery...")
    print("=" * 50)
    
    step = ReturnAndContinueStep()
    
    # Mock setup
    mock_page = Mock()
    mock_query_input = Mock()
    mock_submit_button = Mock()
    mock_logger = Mock()
    mock_logger.debug = Mock()
    mock_logger.info = Mock()
    mock_logger.warning = Mock()
    
    mock_page.locator.side_effect = lambda selector: {
        "input[name='case_num']": mock_query_input,
        "input[type='submit'], button[type='submit']": mock_submit_button
    }.get(selector, Mock())
    
    mock_page.wait_for_timeout = AsyncMock()
    
    test_cases = [
        ("1:25-cv-9777", "1:25-cv-09777", "Missing leading zero"),
        ("1:25-cv-123", "1:25-cv-00123", "Short case number"),
        ("2:23-cr-1", "2:23-cr-00001", "Single digit case number"),
    ]
    
    for input_docket, expected_formatted, description in test_cases:
        print(f"📋 Testing: {description}")
        
        # Mock context with test docket
        mock_context = Mock()
        mock_context.docket_number = input_docket
        
        # Setup for recovery scenario
        mock_submit_button.wait_for = AsyncMock(side_effect=asyncio.TimeoutError("Initial timeout"))
        mock_query_input.wait_for = AsyncMock()
        mock_query_input.input_value = AsyncMock(return_value=input_docket)
        mock_query_input.clear = AsyncMock()
        mock_query_input.fill = AsyncMock()
        mock_query_input.dispatch_event = AsyncMock()
        mock_query_input.is_visible = AsyncMock(return_value=True)
        
        # Second attempt succeeds
        async def success_on_retry(*args, **kwargs):
            if not hasattr(success_on_retry, 'calls'):
                success_on_retry.calls = 0
            success_on_retry.calls += 1
            if success_on_retry.calls <= 1:
                raise asyncio.TimeoutError("First attempt fails")
            return True
        
        mock_submit_button.wait_for = AsyncMock(side_effect=success_on_retry)
        mock_submit_button.is_visible = AsyncMock(return_value=True)
        
        try:
            result = await step._validate_query_page_ready(mock_page, mock_logger, mock_context)
            
            # Check that fill was called with properly formatted docket
            fill_calls = mock_query_input.fill.call_args_list
            if fill_calls:
                filled_value = fill_calls[-1][0][0]  # Get the argument to the last fill() call
                if filled_value == expected_formatted:
                    print(f"✅ PASS: {description} - Filled with: {filled_value}")
                else:
                    print(f"❌ FAIL: {description} - Expected: {expected_formatted}, Got: {filled_value}")
            else:
                print(f"❌ FAIL: {description} - No fill() calls made")
                
        except Exception as e:
            print(f"❌ FAIL: {description} - Exception: {e}")
        
        # Reset for next test
        mock_query_input.fill.reset_mock()
        success_on_retry.calls = 0
    
    print("🎉 Docket formatting tests completed!")


async def main():
    """Run all tests."""
    print("🚀 Starting Query Page Recovery Tests")
    print("=" * 80)
    
    success1 = await test_query_page_recovery()
    await test_docket_formatting_in_recovery()
    
    print("\n" + "=" * 80)
    if success1:
        print("🎉 All tests completed successfully!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(result)