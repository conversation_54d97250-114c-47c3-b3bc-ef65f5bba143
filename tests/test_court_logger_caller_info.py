#!/usr/bin/env python3
"""
Test script to verify that the CourtLogger shows proper class/method names.
"""

import sys
import os
import asyncio
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.pacer.utils.court_logger import CourtLogger


class TestService:
    """Test service to demonstrate logging with proper class/method names."""
    
    def __init__(self, court_logger):
        self.court_logger = court_logger
    
    def process_something(self):
        """Test method that logs messages."""
        self.court_logger.info("✅ MATCHED ignore_download entry: {'test': 'data'}")
        self.court_logger.debug("Case matched on: attorneys=0, defendants=12")
    
    async def async_process(self):
        """Async test method."""
        self.court_logger.info("Starting async processing")
        await asyncio.sleep(0.1)
        self.court_logger.warning("Test warning from async method")
        self.court_logger.error("Test error from async method")


def standalone_function(logger):
    """Standalone function to test logging."""
    logger.info("Log from standalone function")
    logger.debug("Debug message from function")


async def main():
    """Main test function."""
    # Create court logger
    court_id = "test"
    iso_date = datetime.now().strftime("%Y%m%d")
    config = {"DATA_DIR": os.path.join(os.getcwd(), "data")}
    
    court_logger = CourtLogger.get_instance(court_id, iso_date, config)
    
    print("\n" + "="*60)
    print("Testing CourtLogger with Caller Information")
    print("="*60)
    print(f"Log file: data/{iso_date}/logs/pacer/{court_id}.log")
    print("="*60 + "\n")
    
    # Test from a class method
    print("1. Testing from class methods:")
    service = TestService(court_logger)
    service.process_something()
    
    # Test from async method
    print("\n2. Testing from async methods:")
    await service.async_process()
    
    # Test from standalone function
    print("\n3. Testing from standalone function:")
    standalone_function(court_logger)
    
    # Direct logging
    print("\n4. Testing direct logging:")
    court_logger.info("Direct log message from main")
    court_logger.critical("Critical message from main")
    
    print("\n" + "="*60)
    print("Test complete! Check the log file for results.")
    print(f"Log file: data/{iso_date}/logs/pacer/{court_id}.log")
    print("="*60 + "\n")
    
    # Show last few lines of the log file
    log_file = os.path.join(config["DATA_DIR"], iso_date, "logs", "pacer", f"{court_id}.log")
    if os.path.exists(log_file):
        print("Last 10 lines of log file:")
        print("-"*60)
        with open(log_file, 'r') as f:
            lines = f.readlines()
            for line in lines[-10:]:
                print(line.rstrip())
    
    court_logger.close()


if __name__ == "__main__":
    asyncio.run(main())