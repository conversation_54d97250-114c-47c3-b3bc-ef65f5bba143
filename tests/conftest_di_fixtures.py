"""
Dependency Injection fixtures for testing.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from dependency_injector import containers, providers

# Import DI container
try:
    from src.infrastructure.di.container import ApplicationContainer
    from src.models.config.base import WorkflowConfig
except ImportError:
    ApplicationContainer = None
    WorkflowConfig = None

@pytest.fixture
def mock_container():
    """Create a mock DI container for testing."""
    if ApplicationContainer is None:
        # Fallback mock container
        container = Mock()
        container.wire = Mock()
        container.unwire = Mock()
        return container
    
    container = ApplicationContainer()
    
    # Create mock config
    mock_config = Mock(spec=WorkflowConfig) if WorkflowConfig else Mock()
    mock_config.config_name = "test_config"
    mock_config.iso_date = "20240101"
    mock_config.DATA_DIR = "/tmp/test"
    
    # Override providers with mocks
    container.workflow_config_provider.override(mock_config)
    container.shutdown_event.override(asyncio.Event())
    
    # Mock service providers
    container.main_orchestrator.override(providers.Factory(Mock))
    container.scraping_orchestrator.override(providers.Factory(Mock))
    container.processing_orchestrator.override(providers.Factory(Mock))
    container.upload_orchestrator.override(providers.Factory(Mock))
    container.fb_ads_orchestrator_wrapper.override(providers.Factory(Mock))
    container.reports_orchestrator_wrapper.override(providers.Factory(Mock))
    
    # Mock infrastructure providers
    container.async_storage.override(providers.Factory(Mock))
    container.pacer_repository.override(providers.Factory(Mock))
    container.law_firms_repository.override(providers.Factory(Mock))
    container.fb_archive_repository.override(providers.Factory(Mock))
    container.district_courts_repository.override(providers.Factory(Mock))
    
    # Mock AI services
    container.deepseek_service.override(providers.Factory(Mock))
    container.openai_client.override(providers.Factory(Mock))
    container.mistral_service.override(providers.Factory(Mock))
    
    yield container
    
    # Cleanup
    try:
        container.unwire()
    except:
        pass

@pytest.fixture
def mock_deepseek_service():
    """Create a mock DeepSeek service for testing."""
    service = AsyncMock()
    service.classify_ad.return_value = {
        'is_litigation': True,
        'practice_area': 'Personal Injury',
        'confidence': 0.95
    }
    service.extract_summary.return_value = "Test summary"
    service.extract_attorney_info.return_value = {
        'attorneys': ['John Doe', 'Jane Smith'],
        'law_firm': 'Test Law Firm'
    }
    return service

@pytest.fixture
def mock_service_factory():
    """Create a mock service factory for legacy tests."""
    factory = Mock()
    factory.create_async_storage = Mock(return_value=AsyncMock())
    factory.create_pacer_repository = Mock(return_value=AsyncMock())
    factory.create_law_firms_repository = Mock(return_value=AsyncMock())
    factory.create_fb_archive_repository = Mock(return_value=AsyncMock())
    factory.create_district_courts_repository = Mock(return_value=AsyncMock())
    factory.create_deepseek_service = Mock(return_value=AsyncMock())
    return factory

@pytest.fixture
def mock_workflow_config():
    """Create a mock workflow configuration."""
    if WorkflowConfig:
        config = Mock(spec=WorkflowConfig)
    else:
        config = Mock()
    
    config.config_name = "test_config"
    config.iso_date = "20240101"
    config.DATA_DIR = "/tmp/test"
    config.scraper = False
    config.post_process = False
    config.upload = False
    config.fb_ads = False
    config.report_generator = False
    
    return config