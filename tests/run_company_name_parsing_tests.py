"""
Test Runner for Company Name Parsing Validation

Executes comprehensive test suites for company name parsing validation and generates reports.
Part of the HIVE MIND TESTING VALIDATION MISSION.
"""

import sys
import os
import pytest
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class CompanyParsingTestRunner:
    """Orchestrates company name parsing test execution and reporting"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        self.coverage_data = {}
        self.validation_report = {
            "timestamp": datetime.now().isoformat(),
            "mission": "HIVE_MIND_COMPANY_NAME_PARSING_VALIDATION",
            "target_issue": "3M Company, f/k/a Minnesota Mining and Manufacturing Company parsing",
            "test_suites": [],
            "overall_results": {},
            "coverage_metrics": {},
            "recommendations": []
        }
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests for company name parsing"""
        print("🧪 Running Unit Tests for Company Name Parsing...")
        
        unit_test_paths = [
            "tests/unit/services/html/test_company_name_parsing_validation.py",
            "tests/unit/services/html/test_defendant_parsing_edge_cases.py"
        ]
        
        unit_results = {}
        
        for test_path in unit_test_paths:
            full_path = self.project_root / test_path
            if full_path.exists():
                print(f"  📋 Executing {test_path}...")
                
                # Run pytest with detailed output
                result = pytest.main([
                    str(full_path),
                    "-v",
                    "--tb=short",
                    f"--junitxml={self.project_root}/test_reports/unit_{Path(test_path).stem}.xml",
                    "--capture=no"
                ])
                
                unit_results[test_path] = {
                    "exit_code": result,
                    "status": "PASSED" if result == 0 else "FAILED"
                }
                
                print(f"    ✅ Result: {unit_results[test_path]['status']}")
            else:
                print(f"  ❌ Test file not found: {test_path}")
                unit_results[test_path] = {
                    "exit_code": -1,
                    "status": "NOT_FOUND"
                }
        
        return unit_results
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests for company name parsing pipeline"""
        print("🔗 Running Integration Tests for Company Parsing Pipeline...")
        
        integration_test_path = "tests/integration/test_company_parsing_pipeline.py"
        full_path = self.project_root / integration_test_path
        
        integration_results = {}
        
        if full_path.exists():
            print(f"  📋 Executing {integration_test_path}...")
            
            result = pytest.main([
                str(full_path),
                "-v",
                "--tb=short", 
                f"--junitxml={self.project_root}/test_reports/integration_company_parsing.xml",
                "--capture=no"
            ])
            
            integration_results[integration_test_path] = {
                "exit_code": result,
                "status": "PASSED" if result == 0 else "FAILED"
            }
            
            print(f"    ✅ Result: {integration_results[integration_test_path]['status']}")
        else:
            print(f"  ❌ Integration test file not found: {integration_test_path}")
            integration_results[integration_test_path] = {
                "exit_code": -1,
                "status": "NOT_FOUND"
            }
        
        return integration_results
    
    def run_regression_tests(self) -> Dict[str, Any]:
        """Run specific regression tests for known issues"""
        print("🔄 Running Regression Tests for Known Issues...")
        
        # Run specific regression markers if available
        regression_command = [
            str(self.project_root / "tests"),
            "-v",
            "-m", "regression",
            "--tb=short",
            f"--junitxml={self.project_root}/test_reports/regression_tests.xml"
        ]
        
        print("  📋 Executing regression-marked tests...")
        result = pytest.main(regression_command)
        
        regression_results = {
            "regression_tests": {
                "exit_code": result,
                "status": "PASSED" if result == 0 else "FAILED"
            }
        }
        
        print(f"    ✅ Regression Tests Result: {regression_results['regression_tests']['status']}")
        
        return regression_results
    
    def generate_coverage_report(self) -> Dict[str, Any]:
        """Generate coverage report for parsing methods"""
        print("📊 Generating Coverage Report...")
        
        # Run tests with coverage
        coverage_command = [
            str(self.project_root / "tests/unit/services/html/"),
            "--cov=src.services.transformer._docket_components.html_processor_utils",
            "--cov-report=html:test_reports/coverage_html",
            "--cov-report=json:test_reports/coverage.json",
            "--cov-report=term",
            "--cov-branch"
        ]
        
        print("  📋 Running tests with coverage analysis...")
        result = pytest.main(coverage_command)
        
        coverage_results = {
            "coverage_generation": {
                "exit_code": result,
                "status": "COMPLETED" if result in [0, 1] else "FAILED"  # Allow test failures but coverage completion
            }
        }
        
        # Try to load coverage data
        coverage_json_path = self.project_root / "test_reports/coverage.json"
        if coverage_json_path.exists():
            try:
                with open(coverage_json_path, 'r') as f:
                    coverage_data = json.load(f)
                
                # Extract key metrics
                if 'totals' in coverage_data:
                    totals = coverage_data['totals']
                    coverage_results['metrics'] = {
                        "line_coverage": f"{totals.get('percent_covered', 0):.1f}%",
                        "lines_covered": totals.get('covered_lines', 0),
                        "total_lines": totals.get('num_statements', 0),
                        "missing_lines": totals.get('missing_lines', 0),
                        "branch_coverage": f"{totals.get('percent_covered_branch', 0):.1f}%"
                    }
                    
                    print(f"    📈 Line Coverage: {coverage_results['metrics']['line_coverage']}")
                    print(f"    📈 Branch Coverage: {coverage_results['metrics']['branch_coverage']}")
                
            except Exception as e:
                print(f"  ⚠️  Could not parse coverage data: {e}")
                coverage_results['metrics'] = {"error": str(e)}
        
        return coverage_results
    
    def validate_specific_cases(self) -> Dict[str, Any]:
        """Validate specific problematic cases"""
        print("🎯 Validating Specific Problematic Cases...")
        
        # Import the actual implementation
        try:
            from src.services.transformer._docket_components.html_processor_utils import HTMLProcessorUtils
            from unittest.mock import Mock
            
            # Create instance for testing
            mock_logger = Mock()
            html_utils = HTMLProcessorUtils(logger=mock_logger)
            
            # Test specific cases
            test_cases = [
                {
                    "name": "3M Company with f/k/a",
                    "input": "3M Company, f/k/a Minnesota Mining and Manufacturing Company,",
                    "expected_features": ["3M", "f/k/a", "Minnesota Mining"]
                },
                {
                    "name": "AT&T with f/k/a", 
                    "input": "AT&T Inc., f/k/a American Telephone and Telegraph Company",
                    "expected_features": ["AT&T", "f/k/a", "American Telephone"]
                },
                {
                    "name": "Johnson & Johnson",
                    "input": "Johnson & Johnson",
                    "expected_features": ["Johnson & Johnson"]
                },
                {
                    "name": "Company with parentheses",
                    "input": "Apple Inc. (Delaware Corporation)",
                    "expected_features": ["Apple Inc."],
                    "should_not_contain": ["(", ")"]
                }
            ]
            
            validation_results = {
                "total_cases": len(test_cases),
                "passed_cases": 0,
                "failed_cases": 0,
                "case_results": []
            }
            
            for test_case in test_cases:
                case_name = test_case["name"]
                input_text = test_case["input"]
                expected_features = test_case.get("expected_features", [])
                should_not_contain = test_case.get("should_not_contain", [])
                
                print(f"  🧪 Testing: {case_name}")
                
                try:
                    cleaned = html_utils._clean_party_name(input_text)
                    
                    case_result = {
                        "name": case_name,
                        "input": input_text,
                        "output": cleaned,
                        "status": "UNKNOWN"
                    }
                    
                    if cleaned is None:
                        case_result["status"] = "FAILED"
                        case_result["reason"] = "Cleaned result is None"
                        validation_results["failed_cases"] += 1
                        print(f"    ❌ FAILED: Result is None")
                    else:
                        # Check expected features
                        missing_features = []
                        for feature in expected_features:
                            if feature.lower() not in cleaned.lower():
                                missing_features.append(feature)
                        
                        # Check should not contain
                        unwanted_found = []
                        for unwanted in should_not_contain:
                            if unwanted in cleaned:
                                unwanted_found.append(unwanted)
                        
                        if missing_features or unwanted_found:
                            case_result["status"] = "FAILED"
                            case_result["missing_features"] = missing_features
                            case_result["unwanted_found"] = unwanted_found
                            validation_results["failed_cases"] += 1
                            print(f"    ❌ FAILED: Missing {missing_features}, Unwanted {unwanted_found}")
                        else:
                            case_result["status"] = "PASSED"
                            validation_results["passed_cases"] += 1
                            print(f"    ✅ PASSED: {cleaned}")
                    
                    validation_results["case_results"].append(case_result)
                
                except Exception as e:
                    case_result = {
                        "name": case_name,
                        "input": input_text,
                        "output": None,
                        "status": "ERROR",
                        "error": str(e)
                    }
                    validation_results["case_results"].append(case_result)
                    validation_results["failed_cases"] += 1
                    print(f"    💥 ERROR: {e}")
            
            # Calculate success rate
            if validation_results["total_cases"] > 0:
                validation_results["success_rate"] = (
                    validation_results["passed_cases"] / validation_results["total_cases"] * 100
                )
                print(f"  📊 Success Rate: {validation_results['success_rate']:.1f}%")
            
            return validation_results
            
        except ImportError as e:
            print(f"  ❌ Could not import implementation: {e}")
            return {"error": "Could not import implementation", "details": str(e)}
    
    def generate_validation_report(self, unit_results, integration_results, regression_results, coverage_results, validation_results):
        """Generate comprehensive validation report"""
        print("📝 Generating Comprehensive Validation Report...")
        
        self.validation_report.update({
            "test_suites": [
                {
                    "name": "Unit Tests",
                    "results": unit_results,
                    "total_suites": len(unit_results),
                    "passed_suites": sum(1 for r in unit_results.values() if r["status"] == "PASSED"),
                    "failed_suites": sum(1 for r in unit_results.values() if r["status"] == "FAILED")
                },
                {
                    "name": "Integration Tests", 
                    "results": integration_results,
                    "total_suites": len(integration_results),
                    "passed_suites": sum(1 for r in integration_results.values() if r["status"] == "PASSED"),
                    "failed_suites": sum(1 for r in integration_results.values() if r["status"] == "FAILED")
                },
                {
                    "name": "Regression Tests",
                    "results": regression_results,
                    "total_suites": len(regression_results),
                    "passed_suites": sum(1 for r in regression_results.values() if r["status"] == "PASSED"),
                    "failed_suites": sum(1 for r in regression_results.values() if r["status"] == "FAILED")
                }
            ],
            "coverage_metrics": coverage_results,
            "specific_case_validation": validation_results
        })
        
        # Calculate overall success
        total_passed = sum(suite["passed_suites"] for suite in self.validation_report["test_suites"])
        total_suites = sum(suite["total_suites"] for suite in self.validation_report["test_suites"])
        
        self.validation_report["overall_results"] = {
            "total_test_suites": total_suites,
            "passed_test_suites": total_passed,
            "failed_test_suites": total_suites - total_passed,
            "overall_success_rate": (total_passed / total_suites * 100) if total_suites > 0 else 0
        }
        
        # Generate recommendations
        recommendations = []
        
        if self.validation_report["overall_results"]["overall_success_rate"] < 90:
            recommendations.append("CRITICAL: Overall success rate below 90%. Investigate failing tests immediately.")
        
        if validation_results.get("success_rate", 0) < 100:
            recommendations.append("WARNING: Specific case validation failures detected. Review _clean_party_name method.")
        
        if "metrics" in coverage_results and coverage_results["metrics"].get("line_coverage", "0%") < "80%":
            recommendations.append("INFO: Line coverage below 80%. Consider adding more test cases.")
        
        if not recommendations:
            recommendations.append("SUCCESS: All validation criteria met. Company name parsing is working correctly.")
        
        self.validation_report["recommendations"] = recommendations
        
        # Save report
        report_path = self.project_root / "test_reports/company_name_parsing_validation_report.json"
        os.makedirs(report_path.parent, exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(self.validation_report, f, indent=2)
        
        print(f"  💾 Report saved to: {report_path}")
        return self.validation_report
    
    def print_summary(self):
        """Print test execution summary"""
        print("\n" + "="*80)
        print("🎯 HIVE MIND TESTING VALIDATION MISSION SUMMARY")
        print("="*80)
        
        overall = self.validation_report["overall_results"]
        print(f"📊 Overall Success Rate: {overall['overall_success_rate']:.1f}%")
        print(f"✅ Passed Test Suites: {overall['passed_test_suites']}")
        print(f"❌ Failed Test Suites: {overall['failed_test_suites']}")
        print(f"📈 Total Test Suites: {overall['total_test_suites']}")
        
        if "specific_case_validation" in self.validation_report:
            validation = self.validation_report["specific_case_validation"]
            if "success_rate" in validation:
                print(f"🎯 Specific Case Success Rate: {validation['success_rate']:.1f}%")
        
        if "coverage_metrics" in self.validation_report:
            coverage = self.validation_report["coverage_metrics"]
            if "metrics" in coverage:
                metrics = coverage["metrics"]
                print(f"📈 Line Coverage: {metrics.get('line_coverage', 'N/A')}")
                print(f"📈 Branch Coverage: {metrics.get('branch_coverage', 'N/A')}")
        
        print("\n🎯 RECOMMENDATIONS:")
        for rec in self.validation_report.get("recommendations", []):
            print(f"  • {rec}")
        
        print("\n" + "="*80)
        
        # Return exit code based on overall success
        return 0 if overall["overall_success_rate"] >= 90 else 1


def main():
    """Main execution function"""
    print("🚀 Starting HIVE MIND Company Name Parsing Validation Mission")
    print("="*80)
    
    # Create test reports directory
    test_reports_dir = project_root / "test_reports"
    os.makedirs(test_reports_dir, exist_ok=True)
    
    # Initialize test runner
    runner = CompanyParsingTestRunner()
    
    try:
        # Execute all test phases
        unit_results = runner.run_unit_tests()
        integration_results = runner.run_integration_tests()
        regression_results = runner.run_regression_tests()
        coverage_results = runner.generate_coverage_report()
        validation_results = runner.validate_specific_cases()
        
        # Generate comprehensive report
        runner.generate_validation_report(
            unit_results, integration_results, regression_results, 
            coverage_results, validation_results
        )
        
        # Print summary and return exit code
        return runner.print_summary()
        
    except Exception as e:
        print(f"💥 CRITICAL ERROR during test execution: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)