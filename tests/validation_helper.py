"""
Relentless Validator Helper - Real-time Python file validation
ZERO TOLERANCE FOR ERRORS!
"""
import ast
import py_compile
import subprocess
import sys
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional


class ValidationResult:
    def __init__(self, file_path: str, success: bool, errors: List[str] = None):
        self.file_path = file_path
        self.success = success
        self.errors = errors or []
        
    def __str__(self):
        status = "✅ PASS" if self.success else "❌ FAIL"
        error_details = f"\n  Errors: {'; '.join(self.errors)}" if self.errors else ""
        return f"{status}: {self.file_path}{error_details}"


class RelentlessValidator:
    """
    ZERO TOLERANCE validation system for Python file conversions
    """
    
    def __init__(self):
        self.validated_files = set()
        self.error_count = 0
        
    def validate_syntax(self, file_path: str) -> ValidationResult:
        """Validate Python syntax using py_compile"""
        try:
            py_compile.compile(file_path, doraise=True)
            return ValidationResult(file_path, True)
        except py_compile.PyCompileError as e:
            return ValidationResult(file_path, False, [str(e)])
        except Exception as e:
            return ValidationResult(file_path, False, [f"Unexpected error: {str(e)}"])
    
    def validate_ast(self, file_path: str) -> ValidationResult:
        """Validate Python AST structure"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            ast.parse(content)
            return ValidationResult(file_path, True)
        except SyntaxError as e:
            return ValidationResult(file_path, False, [f"AST Syntax Error: {e}"])
        except Exception as e:
            return ValidationResult(file_path, False, [f"AST Error: {e}"])
    
    def check_imports(self, file_path: str) -> ValidationResult:
        """Check for import errors by attempting to import the module"""
        try:
            # Get module path relative to project root
            path_obj = Path(file_path)
            if not path_obj.exists():
                return ValidationResult(file_path, False, ["File does not exist"])
            
            # For now, just validate that imports can be parsed
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    imports.extend([alias.name for alias in node.names])
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
            
            return ValidationResult(file_path, True)
        except Exception as e:
            return ValidationResult(file_path, False, [f"Import check failed: {e}"])
    
    def validate_match_case_syntax(self, file_path: str) -> ValidationResult:
        """Specifically validate match/case syntax (Python 3.10+)"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if file contains match/case statements
            if 'match ' not in content or 'case ' not in content:
                return ValidationResult(file_path, True, ["No match/case statements found"])
            
            # Parse AST to validate match/case syntax
            tree = ast.parse(content)
            
            # Look for Match nodes (Python 3.10+)
            for node in ast.walk(tree):
                if hasattr(ast, 'Match') and isinstance(node, ast.Match):
                    # Found match statement - syntax is valid since AST parsed
                    pass
            
            return ValidationResult(file_path, True, ["Match/case syntax validated"])
        except SyntaxError as e:
            return ValidationResult(file_path, False, [f"Match/case syntax error: {e}"])
        except Exception as e:
            return ValidationResult(file_path, False, [f"Match/case validation error: {e}"])
    
    def comprehensive_validate(self, file_path: str) -> Dict[str, ValidationResult]:
        """Run all validation checks on a file"""
        results = {
            'syntax': self.validate_syntax(file_path),
            'ast': self.validate_ast(file_path),
            'imports': self.check_imports(file_path),
            'match_case': self.validate_match_case_syntax(file_path)
        }
        
        # Track validation
        if file_path not in self.validated_files:
            self.validated_files.add(file_path)
        
        # Count errors
        for result in results.values():
            if not result.success:
                self.error_count += 1
        
        return results
    
    def report_validation(self, file_path: str, results: Dict[str, ValidationResult]) -> str:
        """Generate validation report"""
        report_lines = [f"\n🔍 VALIDATION REPORT: {file_path}"]
        
        all_passed = True
        for check_name, result in results.items():
            report_lines.append(f"  {check_name.upper()}: {result}")
            if not result.success:
                all_passed = False
        
        if all_passed:
            report_lines.append("✅ ALL VALIDATIONS PASSED")
        else:
            report_lines.append("❌ VALIDATION FAILURES DETECTED - BLOCKING CONVERSION!")
            
        return '\n'.join(report_lines)
    
    def validate_file(self, file_path: str) -> bool:
        """Main validation entry point - returns True if all checks pass"""
        results = self.comprehensive_validate(file_path)
        report = self.report_validation(file_path, results)
        print(report)
        
        # Return True only if ALL validations pass
        return all(result.success for result in results.values())


# Global validator instance
validator = RelentlessValidator()


def validate_conversion(file_path: str) -> bool:
    """Quick validation function for use by conversion agents"""
    return validator.validate_file(file_path)


def get_validation_stats() -> Dict[str, Any]:
    """Get current validation statistics"""
    return {
        'files_validated': len(validator.validated_files),
        'errors_detected': validator.error_count,
        'validated_files': list(validator.validated_files)
    }