#!/usr/bin/env python3
"""
Integration test for HTML parsing enhancements in the JSON transformation pipeline.
"""

import sys
import asyncio
import os
import aiohttp
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Change to project root directory
os.chdir(str(project_root))

from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
from src.services.html.case_parser_service import CaseParserService

async def test_integration_html_transformation():
    """Test the integration of HTML parsing with the transformation pipeline."""
    
    # Initialize basic logger
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("test_integration")
    
    # Sample case details similar to what would come from transformation pipeline
    case_details = {
        'docket_num': '1:25-cv-09787',
        'court_id': 'ilnd',
        'new_filename': 'ilnd_25_09787_<PERSON><PERSON>_<PERSON>_v_Godrej_SON_Holdings_Inc.json',
        'plaintiff': [],  # Empty initially - should be populated by HTML
        'defendant': [],  # Empty initially - should be populated by HTML
        'attorney': [],   # Empty initially - should be populated by HTML
    }
    
    # Fetch real HTML content
    url = "https://cdn.lexgenius.ai/20250818/html/ilnd_25_09787_Debra_Muir_v_Godrej_SON_Holdings_Inc.html"
    
    print(f"Fetching HTML content from: {url}")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            if response.status == 200:
                html_content = await response.text()
                print(f"Successfully fetched {len(html_content)} characters of HTML content")
            else:
                print(f"Failed to fetch HTML content: HTTP {response.status}")
                return
    
    # Initialize HTML Processing Orchestrator
    orchestrator = HTMLProcessingOrchestrator(
        logger=logger,
        config={},
        court_id='ilnd'
    )
    
    print("\n=== Testing HTML Processing Integration ===")
    
    # Process HTML content through the orchestrator
    result = await orchestrator.process_html_content(
        case_details=case_details,
        html_content=html_content,
        json_path="/20250818/json/ilnd_25_09787_Debra_Muir_v_Godrej_SON_Holdings_Inc.json"
    )
    
    print("\n=== Integration Test Results ===")
    
    # Check the target fields
    print("=== New HTML Parsing Fields ===")
    jury_demand = result.get('jury_demand')
    is_mdl = result.get('is_mdl')
    lead_case = result.get('lead_case')
    
    print(f"🎯 jury_demand: '{jury_demand}'")
    print(f"🎯 is_mdl: {is_mdl}")
    print(f"🎯 lead_case: '{lead_case}'")
    
    # Check other important fields
    print(f"\n=== Other Extracted Fields ===")
    print(f"docket_num: '{result.get('docket_num')}'")
    print(f"versus: '{result.get('versus')}'")
    print(f"assigned_to: '{result.get('assigned_to')}'")
    print(f"date_filed: '{result.get('date_filed')}'")
    print(f"nos: '{result.get('nos')}'")
    print(f"jurisdiction: '{result.get('jurisdiction')}'")
    print(f"cause: '{result.get('cause')}'")
    
    # Check party and attorney extraction
    plaintiffs = result.get('plaintiff', [])
    defendants = result.get('defendant', [])
    attorneys = result.get('attorney', [])
    
    print(f"\n=== Party and Attorney Data ===")
    print(f"Plaintiffs: {plaintiffs}")
    print(f"Defendants count: {len(defendants)}")
    print(f"Attorneys count: {len(attorneys)}")
    
    if attorneys:
        print("Sample attorney:")
        for attorney in attorneys[:1]:  # Show first attorney as sample
            print(f"  Name: {attorney.get('attorney_name', 'N/A')}")
            print(f"  Firm: {attorney.get('law_firm', 'N/A')}")
            print(f"  Represents: {attorney.get('represents', 'N/A')}")
    
    # Validate success
    print(f"\n=== Validation Results ===")
    success_count = 0
    total_tests = 3
    
    if jury_demand:
        print("✅ jury_demand field populated")
        success_count += 1
    else:
        print("❌ jury_demand field missing")
    
    if is_mdl is not None:
        print("✅ is_mdl field populated")
        success_count += 1
    else:
        print("❌ is_mdl field missing")
    
    if lead_case:
        print("✅ lead_case field populated")
        success_count += 1
    else:
        print("❌ lead_case field missing")
    
    print(f"\nIntegration Test Summary: {success_count}/{total_tests} fields successfully integrated")
    
    # Test data transformation completeness
    key_fields = ['docket_num', 'versus', 'plaintiff', 'defendant']
    transformation_success = all(result.get(field) for field in key_fields)
    
    if transformation_success:
        print("✅ Core transformation fields all populated")
    else:
        print("⚠️ Some core transformation fields missing")
    
    if success_count == total_tests and transformation_success:
        print("\n🎉 INTEGRATION TEST PASSED!")
        print("HTML parsing enhancements are fully integrated into the JSON transformation pipeline!")
    else:
        print("\n⚠️ Integration test completed with some issues")
    
    return result

def main():
    """Run the integration test."""
    try:
        result = asyncio.run(test_integration_html_transformation())
        return result
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()