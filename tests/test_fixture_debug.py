#!/usr/bin/env python3
import pytest
import sys
import os

def test_fixture_container(test_container):
    """Test that the test_container fixture provides the pacer attribute."""
    print(f'Container type: {type(test_container)}')
    print(f'Has pacer: {hasattr(test_container, "pacer")}')
    
    if hasattr(test_container, "pacer"):
        print(f'Pacer type: {type(test_container.pacer)}')
        analytics = test_container.pacer.data.analytics_service()
        print(f'Analytics type: {type(analytics)}')
        assert analytics is not None
        print('SUCCESS: test_container fixture works!')
    else:
        print('ERROR: No pacer attribute found')
        print(f'Available attributes: {[attr for attr in dir(test_container) if not attr.startswith("_")]}')
        pytest.fail("test_container fixture missing pacer attribute")