#!/usr/bin/env python
"""
Test script to verify JSON transformation is working during PACER scraping.
This tests the full integration with FileOperationsService.
"""

import asyncio
import json
import logging
from pathlib import Path
from datetime import datetime

# Import the services
from src.pacer.components.file_operations.file_operations import FileOperations
from src.transformer.components.transformation.json_transformer import JSONTransformationService

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_json_transformation_integration():
    """Test that JSON transformation works through FileOperationsService."""
    
    print("Testing JSON Transformation Integration...")
    print("=" * 60)
    
    # Sample case data with fields that should be transformed
    case_data = {
        "court_id": "ilnd",
        "docket_num": "2:25-cv-00123",
        "case_title": "Smith v. Jones Corp",
        "parsed_defendants": [{"name": "Jones Corp"}, {"name": "Doe Inc"}],
        "parsed_plaintiffs": [{"name": "<PERSON>"}, {"name": "<PERSON>"}],
        "attorney": [
            {
                "attorney_name": "<PERSON>",
                "law_firm": "Smith & Associates",
                "email": "<EMAIL>"
            },
            {
                "attorney_name": "Bob Lawyer",
                "law_firm": "BROWN GREER PLC",  # Should be filtered
                "email": "<EMAIL>"
            }
        ],
        "law_firms": ["Smith & Associates", "BROWN GREER PLC", "Jones Law"],
        "relevance_reason": "test",
        "relevance_score": 0.8,
        "_preprocessing_metadata": {"test": "data"},  # Should be removed
    }
    
    # Create services
    config = {
        "enable_json_transformation": True  # Enable transformation
    }
    
    # Create JSON transformer
    json_transformer = JSONTransformationService(logger=logger, config=config)
    await json_transformer.initialize()
    
    # Create file ops service with transformer
    file_ops = FileOperationsService(
        logger=logger,
        config=config,
        json_transformer=json_transformer
    )
    await file_ops.initialize()
    
    # Set up test directory
    iso_date = datetime.now().strftime("%Y%m%d")
    test_dir = Path("./data/test_" + iso_date)
    
    try:
        # Save the case data (should apply transformation)
        saved_path = await file_ops.save_case_data(
            case_data=case_data,
            iso_date="test_" + iso_date
        )
        
        print(f"✅ Saved file to: {saved_path}")
        
        # Read back the saved file
        with open(saved_path, 'r') as f:
            saved_data = json.load(f)
        
        # Verify transformations
        checks = []
        
        # Check 1: versus field created from case_title
        if 'versus' in saved_data and saved_data['versus'] == "Smith v. Jones Corp":
            checks.append("✅ 'versus' field created correctly")
        else:
            checks.append("❌ 'versus' field NOT created or incorrect")
        
        # Check 2: plaintiff field as list
        if 'plaintiff' in saved_data and isinstance(saved_data['plaintiff'], list):
            if len(saved_data['plaintiff']) == 2:
                checks.append(f"✅ 'plaintiff' field is list with {len(saved_data['plaintiff'])} items")
            else:
                checks.append(f"⚠️ 'plaintiff' field is list but has {len(saved_data['plaintiff'])} items")
        else:
            checks.append("❌ 'plaintiff' field NOT created or not a list")
        
        # Check 3: defendant field created
        if 'defendant' in saved_data:
            checks.append("✅ 'defendant' field created")
        else:
            checks.append("❌ 'defendant' field NOT created")
        
        # Check 4: Brown Greer filtered from attorneys
        attorneys = saved_data.get('attorney', [])
        has_brown_greer = any('brown greer' in a.get('law_firm', '').lower() for a in attorneys)
        if not has_brown_greer and len(attorneys) == 1:
            checks.append("✅ Brown Greer filtered from attorneys")
        else:
            checks.append(f"❌ Brown Greer NOT filtered (found {len(attorneys)} attorneys)")
        
        # Check 5: Brown Greer filtered from law_firms
        law_firms = saved_data.get('law_firms', [])
        has_brown_greer_firm = any('brown greer' in firm.lower() for firm in law_firms)
        if not has_brown_greer_firm:
            checks.append("✅ Brown Greer filtered from law_firms")
        else:
            checks.append("❌ Brown Greer NOT filtered from law_firms")
        
        # Check 6: Unwanted fields deleted
        unwanted_present = []
        for field in ['case_title', 'parsed_defendants', 'parsed_plaintiffs', 
                      'relevance_reason', 'relevance_score', '_preprocessing_metadata']:
            if field in saved_data:
                unwanted_present.append(field)
        
        if not unwanted_present:
            checks.append("✅ All unwanted fields deleted")
        else:
            checks.append(f"❌ Unwanted fields still present: {unwanted_present}")
        
        # Check 7: Title and allegations set to None
        if saved_data.get('title') is None and saved_data.get('allegations') is None:
            checks.append("✅ 'title' and 'allegations' set to None")
        else:
            checks.append("❌ 'title' and/or 'allegations' NOT set to None")
        
        print("\nTransformation Checks:")
        print("-" * 40)
        for check in checks:
            print(check)
        
        # Overall result
        success_count = len([c for c in checks if c.startswith("✅")])
        total_count = len(checks)
        
        print("\n" + "=" * 60)
        if success_count == total_count:
            print(f"🎉 SUCCESS: All {total_count} transformation checks passed!")
            return True
        else:
            print(f"⚠️ PARTIAL: {success_count}/{total_count} transformation checks passed")
            print("\nSaved data keys:", list(saved_data.keys())[:10], "...")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up test directory
        if test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)
            print(f"\n🧹 Cleaned up test directory: {test_dir}")


if __name__ == "__main__":
    result = asyncio.run(test_json_transformation_integration())
    exit(0 if result else 1)