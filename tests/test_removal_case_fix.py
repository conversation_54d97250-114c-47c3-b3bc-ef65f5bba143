"""
Test to verify that removal case handling correctly clicks attachment links in column 3, not column 2.

This test validates the fix for the bug where the system was clicking the document number link 
in column 2 instead of the attachment link in column 3 for removal cases.
"""

import asyncio
from unittest.mock import Mock, patch, AsyncMock
import pytest
from src.services.pacer.download_orchestration_service import PacerDownloadOrchestrationService

# Sample HTML structure mimicking a removal case docket sheet
REMOVAL_CASE_HTML = """
<table width="99%" border="1">
  <tr>
    <th>Date</th>
    <th>Doc#</th>
    <th>Docket Text</th>
  </tr>
  <tr>
    <td>06/27/2025</td>
    <td><a href="/doc_link/1">1</a></td>
    <td>NOTICE OF REMOVAL from SUPREME COURT... (Attachments: # <a href="/attachment_link/1">1</a> Exhibit A-Complaint)</td>
  </tr>
  <tr>
    <td>06/28/2025</td>
    <td><a href="/doc_link/2">2</a></td>
    <td>ORDER setting hearing date</td>
  </tr>
</table>
"""


@pytest.mark.asyncio
async def test_removal_case_clicks_correct_attachment_link():
    """Test that removal cases click the attachment link in column 3, not the document link in column 2."""
    
    # Setup mocks
    mock_logger = Mock()
    mock_config = {
        "iso_date": "20250627",
        "court_id": "test_court",
        "html_only": False,
        "trigger_action_initial_delay_ms": 100
    }
    
    # Create service instance
    service = PacerDownloadOrchestrationService(
        logger=mock_logger,
        config=mock_config,
        court_id="test_court"
    )
    
    # Create mock navigator with page
    mock_page = AsyncMock()
    mock_page.content = AsyncMock(return_value=REMOVAL_CASE_HTML)
    mock_page.url = "http://test.com/docket"
    mock_page.wait_for_timeout = AsyncMock()
    
    # Create mock locators
    mock_table_locator = AsyncMock()
    mock_rows = []
    
    # Mock row 1 (header row)
    mock_header_row = AsyncMock()
    
    # Mock row 2 (removal case row)
    mock_removal_row = AsyncMock()
    mock_removal_td_locators = [
        AsyncMock(),  # Date TD
        AsyncMock(),  # Doc# TD with wrong link
        AsyncMock()   # Docket text TD with correct attachment link
    ]
    
    # Setup the 3rd TD (index 2) to contain the removal text and attachment link
    mock_removal_td_locators[2].text_content = AsyncMock(
        return_value="NOTICE OF REMOVAL from SUPREME COURT... (Attachments: # 1 Exhibit A-Complaint)"
    )
    mock_removal_td_locators[2].count = AsyncMock(return_value=1)
    
    # Mock attachment link in 3rd TD
    mock_attachment_link = AsyncMock()
    mock_attachment_link.text_content = AsyncMock(return_value="1")
    mock_attachment_link.wait_for = AsyncMock()
    mock_attachment_link.click = AsyncMock()
    
    mock_attachment_links = AsyncMock()
    mock_attachment_links.count = AsyncMock(return_value=1)
    mock_attachment_links.nth = Mock(return_value=mock_attachment_link)
    
    mock_removal_td_locators[2].locator = Mock(return_value=mock_attachment_links)
    
    # Setup row locator to return TDs
    mock_removal_row.locator = Mock(side_effect=lambda selector: 
        Mock(nth=Mock(side_effect=lambda idx: mock_removal_td_locators[idx]))
        if selector == "td" else None
    )
    
    # Mock row 3 (non-removal row)
    mock_other_row = AsyncMock()
    mock_other_td = AsyncMock()
    mock_other_td.text_content = AsyncMock(return_value="ORDER setting hearing date")
    mock_other_td.count = AsyncMock(return_value=1)
    mock_other_row.locator = Mock(return_value=Mock(nth=Mock(return_value=mock_other_td)))
    
    mock_rows = [mock_header_row, mock_removal_row, mock_other_row]
    
    # Setup table rows locator
    mock_table_rows = AsyncMock()
    mock_table_rows.count = AsyncMock(return_value=3)
    mock_table_rows.nth = Mock(side_effect=lambda idx: mock_rows[idx])
    
    mock_page.locator = Mock(return_value=mock_table_rows)
    
    # Setup navigator
    mock_navigator = Mock()
    mock_navigator.page = mock_page
    service.navigator = mock_navigator
    
    # Mock DeepSeek service to return attachment number "1"
    mock_deepseek = AsyncMock()
    mock_deepseek.analyze_removal_attachment = AsyncMock(return_value="1")
    service.deepseek_service = mock_deepseek
    
    # Test the _click_removal_attachment_link method
    result = await service._click_removal_attachment_link("1", "test_docket")
    
    # Verify the attachment link in column 3 was clicked, not the document link in column 2
    mock_attachment_link.click.assert_called_once()
    assert result == True
    
    # Verify logs indicate correct column was used
    log_calls = [call[0][0] for call in mock_logger.info.call_args_list]
    assert any("3rd column" in log for log in log_calls)
    assert any("removal row" in log for log in log_calls)


@pytest.mark.asyncio  
async def test_specialized_removal_method_finds_correct_row():
    """Test that the specialized removal method finds the removal row, not just the first row."""
    
    # Setup mocks
    mock_logger = Mock()
    mock_config = {
        "iso_date": "20250627",
        "court_id": "test_court",
        "html_only": False,
        "trigger_action_initial_delay_ms": 100
    }
    
    # Create service instance
    service = PacerDownloadOrchestrationService(
        logger=mock_logger,
        config=mock_config,
        court_id="test_court"
    )
    
    # Create mock page with removal case NOT in first data row
    mock_page = AsyncMock()
    mock_page.url = "http://test.com/docket"
    mock_page.wait_for_timeout = AsyncMock()
    mock_page.on = Mock()  # Mock download event listener
    
    # Mock table with removal case in second data row
    mock_table = AsyncMock()
    mock_rows = []
    
    # Row 0: Header
    mock_header = AsyncMock()
    mock_rows.append(mock_header)
    
    # Row 1: Non-removal case
    mock_row1 = AsyncMock()
    mock_td1 = AsyncMock()
    mock_td1.text_content = AsyncMock(return_value="COMPLAINT filed by plaintiff")
    mock_td1.count = AsyncMock(return_value=1)
    mock_row1.locator = Mock(return_value=Mock(nth=Mock(side_effect=lambda idx: mock_td1 if idx == 2 else AsyncMock())))
    mock_rows.append(mock_row1)
    
    # Row 2: Removal case
    mock_row2 = AsyncMock()
    mock_td2 = AsyncMock()
    mock_td2.text_content = AsyncMock(return_value="NOTICE OF REMOVAL with attachments")
    mock_td2.count = AsyncMock(return_value=1)
    mock_row2.locator = Mock(return_value=Mock(nth=Mock(side_effect=lambda idx: mock_td2 if idx == 2 else AsyncMock())))
    mock_rows.append(mock_row2)
    
    # Setup all_row_locators
    all_row_locators = mock_rows[1:]  # Exclude header
    
    # Mock DeepSeek to return attachment number
    mock_deepseek = AsyncMock()
    mock_deepseek.analyze_removal_attachment = AsyncMock(return_value="1")
    service.deepseek_service = mock_deepseek
    service._get_removal_attachment_number = AsyncMock(return_value="1")
    
    # Mock attachment link click
    service._click_removal_attachment_link = AsyncMock(return_value=True)
    
    # Mock navigator
    mock_navigator = Mock()
    mock_navigator.page = mock_page
    service.navigator = mock_navigator
    
    # Test the specialized removal method
    on_download_event = Mock()
    download_complete_event = asyncio.Event()
    
    # Mock the table locator
    mock_table_locator = AsyncMock()
    mock_table_locator.first.wait_for = AsyncMock()
    mock_table_locator.locator = Mock(return_value=AsyncMock(all=AsyncMock(return_value=all_row_locators)))
    mock_page.locator = Mock(return_value=mock_table_locator)
    
    result = await service._trigger_download_action_removal(
        "test_docket",
        "test_case",
        on_download_event,
        download_complete_event,
        "[TEST]"
    )
    
    # Verify that the removal row was found (not just first row)
    log_calls = [call[0][0] for call in mock_logger.info.call_args_list]
    assert any("Found removal row" in log for log in log_calls)
    
    
if __name__ == "__main__":
    # Run the tests
    asyncio.run(test_removal_case_clicks_correct_attachment_link())
    asyncio.run(test_specialized_removal_method_finds_correct_row())
    print("✅ All removal case tests passed!")