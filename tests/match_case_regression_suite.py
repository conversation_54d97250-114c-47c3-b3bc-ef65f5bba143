#!/usr/bin/env python3
"""
Match/Case Conversion Regression Test Suite
===========================================

Comprehensive regression testing for match/case conversions to ensure
behavioral equivalence and prevent regressions.
"""

import ast
import sys
import unittest
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Callable, Tuple
from dataclasses import dataclass
from validation_framework import Match<PERSON><PERSON>Valida<PERSON>, ConversionCandidate

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


@dataclass
class TestScenario:
    """Test scenario for behavioral equivalence testing"""
    name: str
    test_inputs: List[Any]
    expected_outputs: List[Any]
    original_function: Callable
    converted_function: Callable


class MatchCaseRegressionTester:
    """Comprehensive regression tester for match/case conversions"""
    
    def __init__(self):
        self.validator = MatchCaseValidator()
        self.test_results = []
    
    def create_behavioral_tests(self) -> List[TestScenario]:
        """Create behavioral test scenarios for common patterns"""
        
        scenarios = []
        
        # Scenario 1: Action Dispatcher Pattern
        def original_action_dispatcher(action: str, data: dict = None) -> str:
            if action == 'read':
                return f"Reading data: {data}"
            elif action == 'write':
                return f"Writing data: {data}"
            elif action == 'update':
                return f"Updating data: {data}"
            elif action == 'delete':
                return f"Deleting data: {data}"
            else:
                return f"Unknown action: {action}"
        
        def converted_action_dispatcher(action: str, data: dict = None) -> str:
            match action:
                case 'read':
                    return f"Reading data: {data}"
                case 'write':
                    return f"Writing data: {data}"
                case 'update':
                    return f"Updating data: {data}"
                case 'delete':
                    return f"Deleting data: {data}"
                case _:
                    return f"Unknown action: {action}"
        
        scenarios.append(TestScenario(
            name="Action Dispatcher",
            test_inputs=[
                ('read', {'id': 1}),
                ('write', {'name': 'test'}),
                ('update', {'status': 'active'}),
                ('delete', {'id': 999}),
                ('invalid', {}),
                ('', None),
            ],
            expected_outputs=[
                "Reading data: {'id': 1}",
                "Writing data: {'name': 'test'}",
                "Updating data: {'status': 'active'}",
                "Deleting data: {'id': 999}",
                "Unknown action: invalid",
                "Unknown action: ",
            ],
            original_function=original_action_dispatcher,
            converted_function=converted_action_dispatcher
        ))
        
        # Scenario 2: Status Processing Pattern
        def original_status_processor(status: str) -> dict:
            if status == 'pending':
                return {'code': 1, 'message': 'Processing pending'}
            elif status == 'approved':
                return {'code': 2, 'message': 'Processing approved'}
            elif status == 'rejected':
                return {'code': 3, 'message': 'Processing rejected'}
            elif status == 'cancelled':
                return {'code': 0, 'message': 'Processing cancelled'}
            else:
                return {'code': -1, 'message': f'Unknown status: {status}'}
        
        def converted_status_processor(status: str) -> dict:
            match status:
                case 'pending':
                    return {'code': 1, 'message': 'Processing pending'}
                case 'approved':
                    return {'code': 2, 'message': 'Processing approved'}
                case 'rejected':
                    return {'code': 3, 'message': 'Processing rejected'}
                case 'cancelled':
                    return {'code': 0, 'message': 'Processing cancelled'}
                case _:
                    return {'code': -1, 'message': f'Unknown status: {status}'}
        
        scenarios.append(TestScenario(
            name="Status Processor",
            test_inputs=[
                'pending', 'approved', 'rejected', 'cancelled', 
                'unknown', '', None, 123
            ],
            expected_outputs=[
                {'code': 1, 'message': 'Processing pending'},
                {'code': 2, 'message': 'Processing approved'},
                {'code': 3, 'message': 'Processing rejected'},
                {'code': 0, 'message': 'Processing cancelled'},
                {'code': -1, 'message': 'Unknown status: unknown'},
                {'code': -1, 'message': 'Unknown status: '},
                {'code': -1, 'message': 'Unknown status: None'},
                {'code': -1, 'message': 'Unknown status: 123'},
            ],
            original_function=original_status_processor,
            converted_function=converted_status_processor
        ))
        
        # Scenario 3: File Extension Handler Pattern  
        def original_file_handler(ext: str) -> str:
            if ext == '.json':
                return "JSON Parser"
            elif ext == '.xml':
                return "XML Parser"
            elif ext == '.yaml' or ext == '.yml':
                return "YAML Parser"
            elif ext == '.csv':
                return "CSV Parser"
            else:
                return f"Unknown format: {ext}"
        
        def converted_file_handler(ext: str) -> str:
            match ext:
                case '.json':
                    return "JSON Parser"
                case '.xml':
                    return "XML Parser"
                case '.yaml' | '.yml':
                    return "YAML Parser"
                case '.csv':
                    return "CSV Parser"
                case _:
                    return f"Unknown format: {ext}"
        
        scenarios.append(TestScenario(
            name="File Handler",
            test_inputs=[
                '.json', '.xml', '.yaml', '.yml', '.csv',
                '.txt', '', '.unknown', None
            ],
            expected_outputs=[
                "JSON Parser", "XML Parser", "YAML Parser", "YAML Parser", "CSV Parser",
                "Unknown format: .txt", "Unknown format: ", "Unknown format: .unknown", "Unknown format: None"
            ],
            original_function=original_file_handler,
            converted_function=converted_file_handler
        ))
        
        # Scenario 4: Complex Variable Assignment (Edge Case)
        def original_complex_assignment(value: int) -> tuple:
            if value > 0:
                result = "positive"
                multiplier = 2
            elif value < 0:
                result = "negative" 
                multiplier = -1
            else:
                result = "zero"
                multiplier = 0
            
            return result, multiplier * value
        
        def converted_complex_assignment(value: int) -> tuple:
            match value:
                case val if val > 0:
                    result = "positive"
                    multiplier = 2
                case val if val < 0:
                    result = "negative"
                    multiplier = -1
                case _:
                    result = "zero"
                    multiplier = 0
            
            return result, multiplier * value
        
        scenarios.append(TestScenario(
            name="Complex Assignment",
            test_inputs=[5, -3, 0, 100, -50],
            expected_outputs=[
                ("positive", 10), ("negative", 3), ("zero", 0),
                ("positive", 200), ("negative", 50)
            ],
            original_function=original_complex_assignment,
            converted_function=converted_complex_assignment
        ))
        
        return scenarios
    
    def run_behavioral_tests(self, scenarios: List[TestScenario]) -> bool:
        """Run behavioral equivalence tests"""
        
        print("🧪 Running Behavioral Equivalence Tests")
        print("=" * 45)
        
        all_passed = True
        
        for scenario in scenarios:
            print(f"\n📋 Testing: {scenario.name}")
            print("-" * (len(scenario.name) + 10))
            
            scenario_passed = True
            
            for i, test_input in enumerate(scenario.test_inputs):
                try:
                    # Handle different input signatures
                    if isinstance(test_input, tuple):
                        original_output = scenario.original_function(*test_input)
                        converted_output = scenario.converted_function(*test_input)
                    else:
                        original_output = scenario.original_function(test_input)
                        converted_output = scenario.converted_function(test_input)
                    
                    expected_output = scenario.expected_outputs[i]
                    
                    # Verify original matches expected
                    if original_output != expected_output:
                        print(f"  ❌ Test {i+1}: Original output mismatch")
                        print(f"     Input: {test_input}")
                        print(f"     Expected: {expected_output}")
                        print(f"     Got: {original_output}")
                        scenario_passed = False
                        continue
                    
                    # Verify converted matches original
                    if converted_output != original_output:
                        print(f"  ❌ Test {i+1}: Behavioral difference detected")
                        print(f"     Input: {test_input}")
                        print(f"     Original: {original_output}")
                        print(f"     Converted: {converted_output}")
                        scenario_passed = False
                    else:
                        print(f"  ✅ Test {i+1}: Input {test_input} → {original_output}")
                
                except Exception as e:
                    print(f"  ❌ Test {i+1}: Exception occurred")
                    print(f"     Input: {test_input}")
                    print(f"     Error: {e}")
                    scenario_passed = False
            
            if scenario_passed:
                print(f"  🎯 {scenario.name}: All tests PASSED")
            else:
                print(f"  💥 {scenario.name}: Some tests FAILED")
                all_passed = False
            
            self.test_results.append({
                'scenario': scenario.name,
                'passed': scenario_passed,
                'test_count': len(scenario.test_inputs)
            })
        
        return all_passed
    
    def run_syntax_validation_tests(self) -> bool:
        """Run syntax validation tests on various code patterns"""
        
        print("\n🔍 Running Syntax Validation Tests")
        print("=" * 35)
        
        test_patterns = [
            # Valid simple pattern
            ConversionCandidate(
                file_path="test_simple.py",
                line_start=1, line_end=5,
                original_code="""if x == 1:
    y = 'one'
elif x == 2:
    y = 'two'
else:
    y = 'other'""",
                converted_code="""match x:
    case 1:
        y = 'one'
    case 2:
        y = 'two'
    case _:
        y = 'other'"""
            ),
            
            # Valid complex pattern with guards
            ConversionCandidate(
                file_path="test_guards.py",
                line_start=1, line_end=8,
                original_code="""if isinstance(x, int) and x > 0:
    result = 'positive_int'
elif isinstance(x, str):
    result = 'string'
else:
    result = 'other'""",
                converted_code="""match x:
    case int() if x > 0:
        result = 'positive_int'
    case str():
        result = 'string'
    case _:
        result = 'other'"""
            ),
            
            # Invalid syntax case
            ConversionCandidate(
                file_path="test_invalid.py",
                line_start=1, line_end=4,
                original_code="""if x == 1:
    y = 'one'
else:
    y = 'other'""",
                converted_code="""match x:
    case 1
        y = 'one'  # Missing colon - syntax error
    case _:
        y = 'other'"""
            ),
        ]
        
        all_passed = True
        
        for i, candidate in enumerate(test_patterns, 1):
            print(f"\n📋 Syntax Test {i}: {candidate.file_path}")
            validated = self.validator.validate_conversion(candidate)
            
            syntax_passed = True
            for rule_name, result, message in validated.validation_results:
                icon = "✅" if result.value == "PASS" else "❌" if result.value == "FAIL" else "⚠️"
                print(f"  {icon} {rule_name.replace('_validate_', '').replace('_', ' ').title()}: {message}")
                
                if result.value == "FAIL" and "syntax" in rule_name:
                    syntax_passed = False
            
            # Expected results
            if i == 3:  # Invalid syntax case
                if syntax_passed:
                    print(f"  ❌ Expected syntax error but validation passed")
                    all_passed = False
                else:
                    print(f"  ✅ Correctly detected syntax error")
            else:  # Valid cases
                if not syntax_passed:
                    print(f"  ❌ Unexpected syntax validation failure")
                    all_passed = False
                else:
                    print(f"  ✅ Syntax validation passed as expected")
        
        return all_passed
    
    def run_edge_case_tests(self) -> bool:
        """Test edge cases and boundary conditions"""
        
        print("\n⚠️  Running Edge Case Tests")  
        print("=" * 27)
        
        edge_cases = []
        
        # Edge Case 1: Exception Handling Preservation
        def original_exception_handler(op: str):
            if op == "divide":
                try:
                    return 10 / 0
                except ZeroDivisionError:
                    return "Division by zero"
            elif op == "access":
                try:
                    return {}["nonexistent"]
                except KeyError:
                    return "Key not found"
            else:
                return "No operation"
        
        def converted_exception_handler(op: str):
            match op:
                case "divide":
                    try:
                        return 10 / 0
                    except ZeroDivisionError:
                        return "Division by zero"
                case "access":
                    try:
                        return {}["nonexistent"]
                    except KeyError:
                        return "Key not found"
                case _:
                    return "No operation"
        
        edge_cases.append(TestScenario(
            name="Exception Handling",
            test_inputs=["divide", "access", "other"],
            expected_outputs=["Division by zero", "Key not found", "No operation"],
            original_function=original_exception_handler,
            converted_function=converted_exception_handler
        ))
        
        # Edge Case 2: Side Effects Preservation
        side_effects_log = []
        
        def original_side_effects(action: str):
            if action == "log":
                side_effects_log.append("logged")
                return "logged"
            elif action == "clear":
                side_effects_log.clear()
                return "cleared"
            else:
                return "no action"
        
        def converted_side_effects(action: str):
            match action:
                case "log":
                    side_effects_log.append("logged")
                    return "logged"
                case "clear":
                    side_effects_log.clear()
                    return "cleared"
                case _:
                    return "no action"
        
        edge_cases.append(TestScenario(
            name="Side Effects",
            test_inputs=["log", "log", "clear", "other"],
            expected_outputs=["logged", "logged", "cleared", "no action"],
            original_function=original_side_effects,
            converted_function=converted_side_effects
        ))
        
        # Test edge cases
        all_passed = True
        for edge_case in edge_cases:
            print(f"\n📋 Edge Case: {edge_case.name}")
            print("-" * (len(edge_case.name) + 13))
            
            # Reset side effects log for each test
            if edge_case.name == "Side Effects":
                side_effects_log.clear()
            
            case_passed = True
            
            for i, test_input in enumerate(edge_case.test_inputs):
                try:
                    original_output = edge_case.original_function(test_input)
                    original_side_effect = side_effects_log.copy() if edge_case.name == "Side Effects" else None
                    
                    # Reset for converted test
                    if edge_case.name == "Side Effects":
                        side_effects_log.clear()
                    
                    converted_output = edge_case.converted_function(test_input)
                    converted_side_effect = side_effects_log.copy() if edge_case.name == "Side Effects" else None
                    
                    expected_output = edge_case.expected_outputs[i]
                    
                    # Check output equivalence
                    if original_output != expected_output or converted_output != original_output:
                        print(f"  ❌ Test {i+1}: Output mismatch")
                        print(f"     Input: {test_input}")
                        print(f"     Expected: {expected_output}")
                        print(f"     Original: {original_output}")
                        print(f"     Converted: {converted_output}")
                        case_passed = False
                    # Check side effect equivalence
                    elif edge_case.name == "Side Effects" and original_side_effect != converted_side_effect:
                        print(f"  ❌ Test {i+1}: Side effect mismatch")
                        print(f"     Original side effects: {original_side_effect}")
                        print(f"     Converted side effects: {converted_side_effect}")
                        case_passed = False
                    else:
                        print(f"  ✅ Test {i+1}: {test_input} → {original_output}")
                
                except Exception as e:
                    print(f"  ❌ Test {i+1}: Exception - {e}")
                    case_passed = False
            
            if case_passed:
                print(f"  🎯 {edge_case.name}: All edge case tests PASSED")
            else:
                print(f"  💥 {edge_case.name}: Some edge case tests FAILED")
                all_passed = False
        
        return all_passed
    
    def run_performance_tests(self) -> bool:
        """Run performance comparison tests"""
        
        print("\n⚡ Running Performance Tests")
        print("=" * 28)
        
        import time
        
        # Create test functions with many conditions
        def original_many_conditions(value: int) -> str:
            if value == 1: return "one"
            elif value == 2: return "two"
            elif value == 3: return "three"
            elif value == 4: return "four"
            elif value == 5: return "five"
            elif value == 6: return "six"
            elif value == 7: return "seven"
            elif value == 8: return "eight"
            elif value == 9: return "nine"
            elif value == 10: return "ten"
            else: return "other"
        
        def converted_many_conditions(value: int) -> str:
            match value:
                case 1: return "one"
                case 2: return "two"
                case 3: return "three"
                case 4: return "four"
                case 5: return "five"
                case 6: return "six"
                case 7: return "seven"
                case 8: return "eight"
                case 9: return "nine"
                case 10: return "ten"
                case _: return "other"
        
        # Test performance
        test_values = list(range(1, 11)) * 1000 + [999] * 1000  # Test both matches and default case
        
        # Time original implementation
        start_time = time.time()
        for value in test_values:
            original_many_conditions(value)
        original_time = time.time() - start_time
        
        # Time converted implementation  
        start_time = time.time()
        for value in test_values:
            converted_many_conditions(value)
        converted_time = time.time() - start_time
        
        print(f"📊 Performance Comparison ({len(test_values)} iterations):")
        print(f"  Original if/elif: {original_time:.4f}s")
        print(f"  Converted match:  {converted_time:.4f}s")
        
        performance_improvement = (original_time - converted_time) / original_time * 100
        print(f"  Performance change: {performance_improvement:+.2f}%")
        
        if performance_improvement > -10:  # Allow up to 10% slowdown
            print(f"  ✅ Performance within acceptable range")
            return True
        else:
            print(f"  ❌ Significant performance degradation detected")
            return False
    
    def generate_test_report(self) -> dict:
        """Generate comprehensive test report"""
        
        total_scenarios = len(self.test_results)
        passed_scenarios = sum(1 for r in self.test_results if r['passed'])
        total_tests = sum(r['test_count'] for r in self.test_results)
        
        return {
            'total_scenarios': total_scenarios,
            'passed_scenarios': passed_scenarios,
            'failed_scenarios': total_scenarios - passed_scenarios,
            'success_rate': (passed_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0,
            'total_individual_tests': total_tests,
            'scenarios': self.test_results
        }
    
    def run_comprehensive_test_suite(self) -> bool:
        """Run the complete test suite"""
        
        print("🧪 Match/Case Conversion - Comprehensive Test Suite")
        print("=" * 55)
        
        # Run all test categories
        behavioral_passed = self.run_behavioral_tests(self.create_behavioral_tests())
        syntax_passed = self.run_syntax_validation_tests()
        edge_case_passed = self.run_edge_case_tests()
        performance_passed = self.run_performance_tests()
        
        # Generate final report
        print("\n📊 Final Test Report")
        print("=" * 20)
        
        report = self.generate_test_report()
        
        print(f"Behavioral Tests: {'✅ PASSED' if behavioral_passed else '❌ FAILED'}")
        print(f"Syntax Tests: {'✅ PASSED' if syntax_passed else '❌ FAILED'}")
        print(f"Edge Case Tests: {'✅ PASSED' if edge_case_passed else '❌ FAILED'}")
        print(f"Performance Tests: {'✅ PASSED' if performance_passed else '❌ FAILED'}")
        print()
        print(f"Scenario Success Rate: {report['success_rate']:.1f}% ({report['passed_scenarios']}/{report['total_scenarios']})")
        print(f"Total Individual Tests: {report['total_individual_tests']}")
        
        all_passed = all([behavioral_passed, syntax_passed, edge_case_passed, performance_passed])
        
        if all_passed:
            print("\n🎉 ALL TESTS PASSED! Match/case conversions are safe to deploy.")
        else:
            print("\n⚠️  SOME TESTS FAILED! Review conversions before deployment.")
        
        return all_passed


def main():
    """Main test runner"""
    tester = MatchCaseRegressionTester()
    success = tester.run_comprehensive_test_suite()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()