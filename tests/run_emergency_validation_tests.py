#!/usr/bin/env python3
"""
Emergency validation test runner for the failing case:
nysd_25_06740_<PERSON>_et_al_v_The_3M_Company_et_al.json

This script runs targeted tests to validate:
1. S3 link generation format
2. HTML content stripping
3. Base filename preservation
4. DynamoDB size optimization
"""

import pytest
import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def run_emergency_tests():
    """Run emergency validation tests."""
    print("🚨 RUNNING EMERGENCY VALIDATION TESTS 🚨")
    print("=" * 60)
    
    # Test files to run
    test_files = [
        "tests/unit/services/html/test_emergency_failing_case_validation.py",
        "tests/unit/services/html/test_html_processing_orchestrator_s3_fallback.py::TestURLParsingFunctionality::test_extract_date_from_json_path_valid",
        "tests/unit/services/html/test_html_processing_orchestrator_s3_fallback.py::TestDirectS3ObjectRetrieval::test_successful_direct_retrieval"
    ]
    
    # Run specific emergency tests
    for test_file in test_files:
        print(f"\n🔍 Running: {test_file}")
        print("-" * 40)
        
        # Run pytest with verbose output
        exit_code = pytest.main([
            "-v", 
            "-s",
            "--tb=short",
            test_file
        ])
        
        if exit_code != 0:
            print(f"❌ FAILED: {test_file}")
            return exit_code
        else:
            print(f"✅ PASSED: {test_file}")
    
    print("\n" + "=" * 60)
    print("🎉 ALL EMERGENCY TESTS PASSED!")
    print("✅ S3 link generation format validated")
    print("✅ HTML content stripping validated") 
    print("✅ Base filename preservation validated")
    print("✅ DynamoDB size optimization validated")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    exit_code = run_emergency_tests()
    sys.exit(exit_code)