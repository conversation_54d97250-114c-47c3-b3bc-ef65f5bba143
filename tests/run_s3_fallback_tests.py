#!/usr/bin/env python3
"""
S3 Fallback Test Runner

Comprehensive test runner for S3 HTML content retrieval fallback functionality.
Provides detailed test execution, coverage reporting, and performance metrics.

Usage:
    python tests/run_s3_fallback_tests.py [options]

Options:
    --unit          Run only unit tests
    --integration   Run only integration tests
    --performance   Include performance tests
    --coverage      Generate coverage report
    --verbose       Verbose output
    --parallel      Run tests in parallel
    --markers       Run tests with specific markers
"""

import argparse
import asyncio
import sys
import time
from pathlib import Path
from typing import List, Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import pytest
    import coverage
except ImportError as e:
    print(f"Required package not found: {e}")
    print("Please install required packages: pip install pytest coverage pytest-asyncio pytest-cov")
    sys.exit(1)


class S3FallbackTestRunner:
    """Comprehensive test runner for S3 fallback functionality."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_root = self.project_root / "tests"
        self.coverage_dir = self.test_root / "coverage_reports"
        self.coverage_dir.mkdir(exist_ok=True)
        
    def run_tests(self, args: argparse.Namespace) -> int:
        """Run tests based on provided arguments."""
        print("🚀 Starting S3 Fallback Test Suite")
        print("=" * 60)
        
        # Prepare pytest arguments
        pytest_args = self._build_pytest_args(args)
        
        # Run tests
        start_time = time.time()
        result = pytest.main(pytest_args)
        end_time = time.time()
        
        # Print summary
        self._print_summary(result, end_time - start_time, args)
        
        return result
    
    def _build_pytest_args(self, args: argparse.Namespace) -> List[str]:
        """Build pytest command line arguments."""
        pytest_args = []
        
        # Test selection
        if args.unit and not args.integration:
            pytest_args.extend([
                str(self.test_root / "unit" / "services" / "html" / "test_html_processing_orchestrator_s3_fallback.py")
            ])
        elif args.integration and not args.unit:
            pytest_args.extend([
                str(self.test_root / "integration" / "test_html_orchestrator_s3_integration.py")
            ])
        else:
            # Run both unit and integration tests
            pytest_args.extend([
                str(self.test_root / "unit" / "services" / "html" / "test_html_processing_orchestrator_s3_fallback.py"),
                str(self.test_root / "integration" / "test_html_orchestrator_s3_integration.py")
            ])
        
        # Performance tests
        if args.performance:
            pytest_args.extend(["-m", "performance or not performance"])
        else:
            pytest_args.extend(["-m", "not performance"])
        
        # Markers
        if args.markers:
            pytest_args.extend(["-m", args.markers])
        
        # Coverage
        if args.coverage:
            coverage_file = self.coverage_dir / f"s3_fallback_coverage_{int(time.time())}.xml"
            pytest_args.extend([
                "--cov=src.services.html.html_processing_orchestrator",
                "--cov-report=xml:" + str(coverage_file),
                "--cov-report=term-missing",
                "--cov-report=html:" + str(self.coverage_dir / "htmlcov")
            ])
        
        # Verbosity
        if args.verbose:
            pytest_args.extend(["-v", "-s"])
        else:
            pytest_args.append("-q")
        
        # Parallel execution
        if args.parallel:
            pytest_args.extend(["-n", "auto"])
        
        # Other useful options
        pytest_args.extend([
            "--tb=short",  # Shorter traceback format
            "--strict-markers",  # Strict marker checking
            "--disable-warnings",  # Disable warnings for cleaner output
        ])
        
        return pytest_args
    
    def _print_summary(self, result: int, duration: float, args: argparse.Namespace):
        """Print test execution summary."""
        print("\n" + "=" * 60)
        print("📊 Test Execution Summary")
        print("=" * 60)
        
        # Result interpretation
        if result == 0:
            print("✅ All tests passed successfully")
        elif result == 1:
            print("❌ Some tests failed")
        elif result == 2:
            print("⚠️  Test execution was interrupted")
        elif result == 3:
            print("🔧 Internal error occurred")
        elif result == 4:
            print("🚫 pytest usage error")
        elif result == 5:
            print("🔍 No tests were collected")
        
        print(f"⏱️  Execution time: {duration:.2f} seconds")
        
        # Test categories run
        categories = []
        if args.unit:
            categories.append("Unit")
        if args.integration:
            categories.append("Integration")
        if args.performance:
            categories.append("Performance")
        
        if not categories or (args.unit and args.integration):
            categories = ["Unit", "Integration"]
        
        print(f"🏷️  Test categories: {', '.join(categories)}")
        
        # Coverage info
        if args.coverage:
            print(f"📈 Coverage report generated in: {self.coverage_dir}")
        
        print("\n" + "=" * 60)
    
    def validate_environment(self) -> bool:
        """Validate test environment setup."""
        print("🔍 Validating test environment...")
        
        # Check required files exist
        required_files = [
            self.test_root / "unit" / "services" / "html" / "test_html_processing_orchestrator_s3_fallback.py",
            self.test_root / "integration" / "test_html_orchestrator_s3_integration.py",
            self.test_root / "mocks" / "mock_s3_enhanced.py",
            self.test_root / "unit" / "services" / "html" / "conftest.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(str(file_path))
        
        if missing_files:
            print("❌ Missing required test files:")
            for file_path in missing_files:
                print(f"   - {file_path}")
            return False
        
        # Check Python modules can be imported
        try:
            from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
            from tests.mocks.mock_s3_enhanced import MockS3AsyncStorage
            print("✅ All required modules can be imported")
        except ImportError as e:
            print(f"❌ Import error: {e}")
            return False
        
        print("✅ Environment validation passed")
        return True
    
    def list_available_tests(self):
        """List all available tests."""
        print("📋 Available S3 Fallback Tests")
        print("=" * 60)
        
        test_categories = {
            "Unit Tests": [
                "URL parsing functionality",
                "Direct S3 object retrieval", 
                "Prefix search fallback mechanisms",
                "Complete failure scenarios",
                "Edge cases and error handling",
                "DI container compliance",
                "Logging integration"
            ],
            "Integration Tests": [
                "S3 integration workflows",
                "Error handling integration",
                "S3 link construction",
                "Performance and scalability",
                "Data validation and sanitization"
            ]
        }
        
        for category, tests in test_categories.items():
            print(f"\n🏷️  {category}:")
            for test in tests:
                print(f"   • {test}")
        
        print("\n" + "=" * 60)
    
    def run_specific_test_class(self, test_class: str) -> int:
        """Run a specific test class."""
        print(f"🎯 Running specific test class: {test_class}")
        
        pytest_args = [
            "-v",
            "-k", test_class,
            str(self.test_root / "unit" / "services" / "html"),
            str(self.test_root / "integration")
        ]
        
        return pytest.main(pytest_args)


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(
        description="S3 Fallback Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python tests/run_s3_fallback_tests.py --unit --coverage
  python tests/run_s3_fallback_tests.py --integration --verbose
  python tests/run_s3_fallback_tests.py --performance --parallel
  python tests/run_s3_fallback_tests.py --markers="s3_fallback and not slow"
        """
    )
    
    # Test selection options
    parser.add_argument(
        "--unit", 
        action="store_true", 
        help="Run unit tests only"
    )
    parser.add_argument(
        "--integration", 
        action="store_true", 
        help="Run integration tests only"
    )
    parser.add_argument(
        "--performance", 
        action="store_true", 
        help="Include performance tests"
    )
    
    # Execution options
    parser.add_argument(
        "--coverage", 
        action="store_true", 
        help="Generate coverage report"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Verbose output"
    )
    parser.add_argument(
        "--parallel", 
        action="store_true", 
        help="Run tests in parallel"
    )
    
    # Filtering options
    parser.add_argument(
        "--markers", 
        type=str, 
        help="Run tests with specific markers"
    )
    parser.add_argument(
        "--test-class", 
        type=str, 
        help="Run specific test class"
    )
    
    # Utility options
    parser.add_argument(
        "--list-tests", 
        action="store_true", 
        help="List available tests"
    )
    parser.add_argument(
        "--validate", 
        action="store_true", 
        help="Validate test environment"
    )
    
    args = parser.parse_args()
    
    # Create test runner
    runner = S3FallbackTestRunner()
    
    # Handle utility options
    if args.list_tests:
        runner.list_available_tests()
        return 0
    
    if args.validate:
        is_valid = runner.validate_environment()
        return 0 if is_valid else 1
    
    # Validate environment before running tests
    if not runner.validate_environment():
        print("❌ Environment validation failed. Please fix issues before running tests.")
        return 1
    
    # Run specific test class if requested
    if args.test_class:
        return runner.run_specific_test_class(args.test_class)
    
    # Run main test suite
    return runner.run_tests(args)


if __name__ == "__main__":
    sys.exit(main())
