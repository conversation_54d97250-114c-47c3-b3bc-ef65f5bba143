"""
Test to verify that transferred cases skip ignore_download_service and stop processing.

This test validates the fix that ensures cases with is_transferred=true immediately
stop processing after being saved, without running ignore_download_service or
continuing to the verification phase.
"""

import asyncio
from unittest.mock import Mock, AsyncMock, patch
import pytest
from src.services.pacer.docket_processing_orchestrator_service import (
    PacerDocketProcessingOrchestratorService
)


@pytest.mark.asyncio
async def test_transferred_case_skips_ignore_download_service():
    """Test that transferred cases return early and don't run ignore_download_service."""
    
    # Setup mocks
    mock_logger = Mock()
    mock_config = {
        "court_id": "test_court",
        "iso_date": "20250120",
    }
    
    # Create orchestrator service
    orchestrator = PacerDocketProcessingOrchestratorService(
        logger=mock_logger,
        config=mock_config,
        court_id="test_court",
        iso_date="20250120"
    )
    
    # Mock the classification service
    mock_classification_service = Mock()
    mock_classification_service.should_process_as_transfer = Mock(return_value=True)
    mock_classification_service.process_transfer_case = AsyncMock()
    mock_classification_service.should_process_as_removal = Mock(return_value=False)
    
    # Mock save_case_details
    orchestrator._save_case_details = AsyncMock()
    
    # Mock the ignore_download_service to track if it's called
    mock_ignore_download_service = Mock()
    mock_ignore_download_service.check_ignore_download = Mock()
    
    # Mock case verification service with ignore_download_service
    mock_verification_service = Mock()
    mock_verification_service.verify_case = AsyncMock()
    mock_verification_service.ignore_download_service = mock_ignore_download_service
    
    # Inject mocked services
    orchestrator.case_classification_service = mock_classification_service
    orchestrator.case_verification_service = mock_verification_service
    orchestrator.ignore_download_service = mock_ignore_download_service
    
    # Test case 1: Transferred case WITH S3 link
    case_with_s3 = {
        "docket_num": "TEST-001",
        "is_transferred": True,
        "transferred_in": True,
        "s3_link": "s3://bucket/file.pdf"
    }
    
    mock_classification_service.process_transfer_case.return_value = case_with_s3
    
    result = await orchestrator._apply_relevance_and_classification_filters(case_with_s3)
    
    # Verify case was saved
    orchestrator._save_case_details.assert_called_once()
    
    # Verify early return happened
    assert result["_transfer_completed"] == True
    assert result["_processing_notes"] == "Transfer case processed - S3 link inherited"
    
    # Verify ignore_download_service was NOT called
    mock_ignore_download_service.check_ignore_download.assert_not_called()
    
    # Reset mocks for next test
    orchestrator._save_case_details.reset_mock()
    mock_ignore_download_service.check_ignore_download.reset_mock()
    
    # Test case 2: Transferred case WITHOUT S3 link
    case_without_s3 = {
        "docket_num": "TEST-002",
        "is_transferred": True,
        "transferred_in": True
    }
    
    mock_classification_service.process_transfer_case.return_value = case_without_s3
    
    result = await orchestrator._apply_relevance_and_classification_filters(case_without_s3)
    
    # Verify case was saved
    orchestrator._save_case_details.assert_called_once()
    
    # Verify early return happened
    assert result["_transfer_completed"] == True
    assert result["_processing_notes"] == "Transfer case processed - no S3 link available"
    
    # Verify ignore_download_service was NOT called
    mock_ignore_download_service.check_ignore_download.assert_not_called()
    
    print("✅ All tests passed - transferred cases correctly skip ignore_download_service")


@pytest.mark.asyncio
async def test_non_transferred_case_runs_normally():
    """Test that non-transferred cases continue to run normally through all phases."""
    
    # Setup mocks
    mock_logger = Mock()
    mock_config = {
        "court_id": "test_court",
        "iso_date": "20250120",
    }
    
    # Create orchestrator service
    orchestrator = PacerDocketProcessingOrchestratorService(
        logger=mock_logger,
        config=mock_config,
        court_id="test_court",
        iso_date="20250120"
    )
    
    # Mock the classification service
    mock_classification_service = Mock()
    mock_classification_service.should_process_as_transfer = Mock(return_value=True)
    mock_classification_service.process_transfer_case = AsyncMock()
    mock_classification_service.should_process_as_removal = Mock(return_value=False)
    
    # Mock save_case_details
    orchestrator._save_case_details = AsyncMock()
    
    # Inject mocked services
    orchestrator.case_classification_service = mock_classification_service
    
    # Test case: Transfer-like case but is_transferred=false
    case_not_transferred = {
        "docket_num": "TEST-003",
        "is_transferred": False,  # Key difference
        "transferor_court_id": "some_court"
    }
    
    mock_classification_service.process_transfer_case.return_value = case_not_transferred
    
    result = await orchestrator._apply_relevance_and_classification_filters(case_not_transferred)
    
    # Verify case was NOT saved (because is_transferred=false)
    orchestrator._save_case_details.assert_not_called()
    
    # Verify NO early return happened (no _transfer_completed flag)
    assert "_transfer_completed" not in result
    
    # Verify case continues processing (no processing_notes about transfer)
    assert "_processing_notes" not in result or "Transfer case processed" not in result.get("_processing_notes", "")
    
    print("✅ Non-transferred cases continue processing normally")


if __name__ == "__main__":
    # Run the tests
    asyncio.run(test_transferred_case_skips_ignore_download_service())
    asyncio.run(test_non_transferred_case_runs_normally())