#!/usr/bin/env python3
"""
Match/Case Conversion Validation Framework
==========================================

This framework provides comprehensive validation for converting if/elif chains
to match/case statements, ensuring syntax correctness and behavioral equivalence.

Key Features:
- Python syntax validation using ast module
- Static type checking compatibility
- Logic equivalence verification
- Edge case preservation testing
- Performance regression detection
"""

import ast
import sys
import textwrap
import traceback
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from enum import Enum


class ValidationResult(Enum):
    PASS = "PASS"
    FAIL = "FAIL"
    WARNING = "WARNING"


@dataclass
class ConversionCandidate:
    """Represents a potential if/elif chain for match/case conversion"""
    file_path: str
    line_start: int
    line_end: int
    original_code: str
    converted_code: Optional[str] = None
    validation_results: List[Tuple[str, ValidationResult, str]] = None

    def __post_init__(self):
        if self.validation_results is None:
            self.validation_results = []


class MatchCaseValidator:
    """Comprehensive validator for match/case conversions"""
    
    def __init__(self, python_version: Tuple[int, int] = (3, 10)):
        self.python_version = python_version
        self.validation_rules = []
        self._setup_validation_rules()
    
    def _setup_validation_rules(self):
        """Setup comprehensive validation rule set"""
        self.validation_rules = [
            self._validate_syntax,
            self._validate_python_version_compatibility,
            self._validate_logic_equivalence,
            self._validate_variable_scope,
            self._validate_exception_handling,
            self._validate_performance_impact,
        ]
    
    def validate_conversion(self, candidate: ConversionCandidate) -> ConversionCandidate:
        """Run complete validation suite on a conversion candidate"""
        candidate.validation_results.clear()
        
        for rule in self.validation_rules:
            try:
                rule_name = rule.__name__
                result, message = rule(candidate)
                candidate.validation_results.append((rule_name, result, message))
                
                # Stop on critical failures
                if result == ValidationResult.FAIL and "syntax" in rule_name.lower():
                    break
                    
            except Exception as e:
                candidate.validation_results.append((
                    rule.__name__, 
                    ValidationResult.FAIL, 
                    f"Validation rule failed: {str(e)}"
                ))
        
        return candidate
    
    def _validate_syntax(self, candidate: ConversionCandidate) -> Tuple[ValidationResult, str]:
        """Validate Python syntax using AST parsing"""
        if not candidate.converted_code:
            return ValidationResult.FAIL, "No converted code provided"
        
        try:
            # Parse original code
            ast.parse(candidate.original_code)
            
            # Parse converted code
            ast.parse(candidate.converted_code)
            
            return ValidationResult.PASS, "Syntax validation successful"
            
        except SyntaxError as e:
            return ValidationResult.FAIL, f"Syntax error: {e.msg} at line {e.lineno}"
        except Exception as e:
            return ValidationResult.FAIL, f"AST parsing failed: {str(e)}"
    
    def _validate_python_version_compatibility(self, candidate: ConversionCandidate) -> Tuple[ValidationResult, str]:
        """Check if match/case syntax is supported in target Python version"""
        if self.python_version < (3, 10):
            return ValidationResult.FAIL, f"Match/case requires Python 3.10+, current: {self.python_version}"
        
        return ValidationResult.PASS, f"Compatible with Python {self.python_version}"
    
    def _validate_logic_equivalence(self, candidate: ConversionCandidate) -> Tuple[ValidationResult, str]:
        """Verify that logical flow is preserved"""
        try:
            original_ast = ast.parse(candidate.original_code)
            converted_ast = ast.parse(candidate.converted_code)
            
            # Extract control flow patterns
            original_branches = self._extract_branch_patterns(original_ast)
            converted_branches = self._extract_match_patterns(converted_ast)
            
            if len(original_branches) != len(converted_branches):
                return ValidationResult.WARNING, f"Branch count mismatch: {len(original_branches)} vs {len(converted_branches)}"
            
            return ValidationResult.PASS, "Logic equivalence verified"
            
        except Exception as e:
            return ValidationResult.WARNING, f"Could not verify logic equivalence: {str(e)}"
    
    def _validate_variable_scope(self, candidate: ConversionCandidate) -> Tuple[ValidationResult, str]:
        """Check for variable scope issues in conversion"""
        try:
            # Parse both versions and check variable assignments
            original_ast = ast.parse(candidate.original_code)
            converted_ast = ast.parse(candidate.converted_code)
            
            original_vars = self._extract_assigned_variables(original_ast)
            converted_vars = self._extract_assigned_variables(converted_ast)
            
            if original_vars != converted_vars:
                missing = original_vars - converted_vars
                extra = converted_vars - original_vars
                msg = f"Variable scope differences - Missing: {missing}, Extra: {extra}"
                return ValidationResult.WARNING, msg
            
            return ValidationResult.PASS, "Variable scope preserved"
            
        except Exception as e:
            return ValidationResult.WARNING, f"Could not validate variable scope: {str(e)}"
    
    def _validate_exception_handling(self, candidate: ConversionCandidate) -> Tuple[ValidationResult, str]:
        """Check that exception handling behavior is preserved"""
        try:
            original_ast = ast.parse(candidate.original_code)
            converted_ast = ast.parse(candidate.converted_code)
            
            original_exceptions = self._extract_exception_patterns(original_ast)
            converted_exceptions = self._extract_exception_patterns(converted_ast)
            
            if original_exceptions != converted_exceptions:
                return ValidationResult.WARNING, "Exception handling patterns may have changed"
            
            return ValidationResult.PASS, "Exception handling preserved"
            
        except Exception as e:
            return ValidationResult.WARNING, f"Could not validate exception handling: {str(e)}"
    
    def _validate_performance_impact(self, candidate: ConversionCandidate) -> Tuple[ValidationResult, str]:
        """Assess potential performance impact of conversion"""
        # Simple heuristic: match/case is generally more efficient for multiple conditions
        original_elif_count = candidate.original_code.count('elif')
        
        if original_elif_count > 5:
            return ValidationResult.PASS, f"Performance likely improved with {original_elif_count} conditions"
        elif original_elif_count > 2:
            return ValidationResult.PASS, "Neutral to slightly improved performance expected"
        else:
            return ValidationResult.WARNING, "Minimal performance benefit expected"
    
    def _extract_branch_patterns(self, tree: ast.AST) -> List[str]:
        """Extract if/elif branch patterns from AST"""
        patterns = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.If):
                patterns.append(self._node_to_string(node.test))
                
                # Extract elif conditions
                current = node
                while current.orelse and len(current.orelse) == 1 and isinstance(current.orelse[0], ast.If):
                    current = current.orelse[0]
                    patterns.append(self._node_to_string(current.test))
        
        return patterns
    
    def _extract_match_patterns(self, tree: ast.AST) -> List[str]:
        """Extract match case patterns from AST"""
        patterns = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Match):
                for case in node.cases:
                    patterns.append(self._node_to_string(case.pattern))
        
        return patterns
    
    def _extract_assigned_variables(self, tree: ast.AST) -> set:
        """Extract variables assigned in the code"""
        variables = set()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        variables.add(target.id)
        
        return variables
    
    def _extract_exception_patterns(self, tree: ast.AST) -> List[str]:
        """Extract exception handling patterns"""
        patterns = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Try):
                patterns.append("try_block")
            elif isinstance(node, ast.ExceptHandler):
                exc_type = node.type.id if node.type and isinstance(node.type, ast.Name) else "generic"
                patterns.append(f"except_{exc_type}")
        
        return patterns
    
    def _node_to_string(self, node: ast.AST) -> str:
        """Convert AST node to string representation"""
        try:
            return ast.unparse(node)
        except AttributeError:
            # Fallback for older Python versions
            return str(type(node).__name__)


class ConversionDetector:
    """Detects suitable if/elif chains for match/case conversion"""
    
    def __init__(self, min_elif_count: int = 2):
        self.min_elif_count = min_elif_count
    
    def scan_file(self, file_path: Path) -> List[ConversionCandidate]:
        """Scan a Python file for conversion candidates"""
        candidates = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            lines = content.splitlines()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.If):
                    candidate = self._analyze_if_chain(node, lines, str(file_path))
                    if candidate:
                        candidates.append(candidate)
        
        except Exception as e:
            print(f"Error scanning {file_path}: {e}")
        
        return candidates
    
    def _analyze_if_chain(self, if_node: ast.If, lines: List[str], file_path: str) -> Optional[ConversionCandidate]:
        """Analyze an if statement to determine if it's a good conversion candidate"""
        elif_count = 0
        current = if_node
        
        # Count elif statements
        while current.orelse and len(current.orelse) == 1 and isinstance(current.orelse[0], ast.If):
            elif_count += 1
            current = current.orelse[0]
        
        # Check if it meets minimum criteria
        if elif_count < self.min_elif_count:
            return None
        
        # Extract the code
        start_line = if_node.lineno - 1
        end_line = current.end_lineno if hasattr(current, 'end_lineno') else start_line + 10
        
        # Get the actual code
        original_code = '\n'.join(lines[start_line:end_line])
        
        return ConversionCandidate(
            file_path=file_path,
            line_start=start_line + 1,
            line_end=end_line,
            original_code=original_code
        )


def create_test_conversion_examples():
    """Create test examples for validation"""
    
    examples = []
    
    # Example 1: Simple string matching
    original_1 = '''
if status == "pending":
    result = "waiting"
elif status == "approved":
    result = "accepted"
elif status == "rejected":
    result = "denied"
else:
    result = "unknown"
'''
    
    converted_1 = '''
match status:
    case "pending":
        result = "waiting"
    case "approved":
        result = "accepted"
    case "rejected":
        result = "denied"
    case _:
        result = "unknown"
'''
    
    examples.append(ConversionCandidate(
        file_path="test_example_1.py",
        line_start=1,
        line_end=8,
        original_code=textwrap.dedent(original_1).strip(),
        converted_code=textwrap.dedent(converted_1).strip()
    ))
    
    # Example 2: Complex condition (should warn)
    original_2 = '''
if x > 0 and y < 10:
    result = "positive_small"
elif x > 0 and y >= 10:
    result = "positive_large"
elif x <= 0:
    result = "non_positive"
'''
    
    # This conversion would be problematic - keeping as original for now
    converted_2 = original_2  # Intentionally not converted to test validation
    
    examples.append(ConversionCandidate(
        file_path="test_example_2.py",
        line_start=1,
        line_end=6,
        original_code=textwrap.dedent(original_2).strip(),
        converted_code=textwrap.dedent(converted_2).strip()
    ))
    
    return examples


def main():
    """Main validation runner"""
    print("🔍 Match/Case Conversion Validation Framework")
    print("=" * 50)
    
    # Initialize validator
    validator = MatchCaseValidator()
    
    # Test with examples
    examples = create_test_conversion_examples()
    
    for i, candidate in enumerate(examples, 1):
        print(f"\n📋 Example {i}: {candidate.file_path}")
        print("-" * 30)
        
        # Validate conversion
        validated_candidate = validator.validate_conversion(candidate)
        
        # Display results
        for rule_name, result, message in validated_candidate.validation_results:
            icon = "✅" if result == ValidationResult.PASS else "❌" if result == ValidationResult.FAIL else "⚠️"
            print(f"{icon} {rule_name}: {message}")
    
    print(f"\n🎯 Validation framework ready for use!")
    return validator


if __name__ == "__main__":
    main()