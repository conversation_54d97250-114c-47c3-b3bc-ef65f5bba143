"""
Test Suite: Browser Context DI Integration

Validates the browser context fixes and proper dependency injection 
container integration across orchestration components.

Test Focus Areas:
- Browser service validation and DI injection 
- Context creation with proper error handling
- Logger inheritance from component_base.py
- DI container wiring validation
- Error scenarios and defensive programming
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, AsyncMock, MagicMock, patch
from typing import Any, Dict

from src.pacer.jobs.job_runner_service import PacerJobRunnerService
from src.pacer.jobs.jobs_models import PacerJob
from src.pacer.components.orchestration.orchestrator_facade import PacerOrchestratorFacade
from src.pacer.components.orchestration.report_workflow_engine import PacerReportWorkflowEngine
from src.pacer.pacer_browser_service import PacerBrowserService


class TestBrowserContextDIIntegration:
    """Test suite for browser context DI fixes and validation."""

    @pytest.fixture
    def mock_logger(self):
        """Mock logger that inherits from component_base patterns."""
        logger = Mock(spec=logging.Logger)
        logger.info = Mock()
        logger.debug = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.exception = Mock()
        return logger

    @pytest.fixture
    def mock_config(self):
        """Mock configuration dictionary."""
        return {
            "headless": True,
            "timeout_ms": 30000,
            "data_dir": "/tmp/test",
            "report_timeout": 300,
            "delay_between_courts": 1.0
        }

    @pytest.fixture
    def mock_browser_service(self):
        """Mock browser service with proper async methods."""
        browser_service = AsyncMock(spec=PacerBrowserService)
        browser_service.new_context = AsyncMock()
        browser_service.close = AsyncMock()
        return browser_service

    @pytest.fixture
    def mock_browser_service_factory(self, mock_browser_service):
        """Mock browser service factory for DI injection."""
        def factory(*args, **kwargs):
            # Validate that logger is passed to factory
            assert 'logger' in kwargs, "Logger must be passed to browser service factory"
            return mock_browser_service
        return factory

    @pytest.fixture
    def mock_orchestrator(self, mock_logger, mock_config):
        """Mock orchestrator with required methods."""
        orchestrator = Mock()
        orchestrator.get_download_path = Mock(return_value="/tmp/test/downloads")
        orchestrator.process_single_court_job = AsyncMock(return_value={"status": "success", "metrics": {}})
        return orchestrator

    @pytest.fixture
    def mock_job(self):
        """Mock PacerJob instance."""
        job = Mock(spec=PacerJob)
        job.job_id = "test-job-123"
        job.court_id = "test-court"
        job.iso_date = "20240101"
        job.docket_list_input = None
        job.config_snapshot = {
            "headless": True,
            "timeout_ms": 30000
        }
        job.start_timer = Mock()
        job.end_timer = Mock()
        job.update_status = Mock()
        job.set_error = Mock()
        job.results = {}
        job.metrics = {}
        job.status = "PENDING"
        return job

    @pytest.fixture
    def job_runner_service(self, mock_logger, mock_config, mock_browser_service_factory):
        """Create JobRunnerService instance with DI."""
        return PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            browser_service_factory=mock_browser_service_factory
        )

    def test_browser_service_factory_injection(self, job_runner_service, mock_browser_service_factory):
        """Test that browser service factory is properly injected via DI."""
        assert job_runner_service.browser_service_factory is mock_browser_service_factory
        assert job_runner_service.browser_service_factory is not None

    def test_logger_inheritance_from_component_base(self, job_runner_service, mock_logger):
        """Test that all components use logger from component_base.py pattern."""
        # Verify logger is properly set
        assert job_runner_service.logger is mock_logger
        
        # Test that logger methods are available
        assert hasattr(job_runner_service.logger, 'info')
        assert hasattr(job_runner_service.logger, 'debug') 
        assert hasattr(job_runner_service.logger, 'warning')
        assert hasattr(job_runner_service.logger, 'error')

    @pytest.mark.asyncio
    async def test_valid_browser_context_creation_flow(self, job_runner_service, mock_job, mock_orchestrator, mock_browser_service, mock_logger):
        """Test successful browser context creation with valid browser service."""
        # Set up orchestrator
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        # Mock successful context creation
        mock_context = AsyncMock()
        mock_browser_service.new_context.return_value = mock_context
        
        # Run job
        result = await job_runner_service.run_job(mock_job)
        
        # Verify successful flow
        assert result == mock_job
        mock_logger.info.assert_called()
        mock_logger.debug.assert_called()
        
        # Verify browser service creation was logged
        debug_calls = [call[0][0] for call in mock_logger.debug.call_args_list]
        browser_creation_logged = any("Successfully created browser service" in msg for msg in debug_calls)
        assert browser_creation_logged, "Browser service creation should be logged"
        
        # Verify context creation was logged  
        context_creation_logged = any("Successfully created browser context" in msg for msg in debug_calls)
        assert context_creation_logged, "Browser context creation should be logged"

    @pytest.mark.asyncio
    async def test_null_browser_service_handling(self, mock_logger, mock_config, mock_job):
        """Test proper error handling when browser service factory returns None."""
        # Create factory that returns None
        def null_factory(*args, **kwargs):
            return None
        
        job_runner_service = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            browser_service_factory=null_factory
        )
        
        # Set up orchestrator
        mock_orchestrator = Mock()
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        # Run job - should handle null browser service gracefully
        result = await job_runner_service.run_job(mock_job)
        
        # Verify error was logged and job failed
        mock_logger.error.assert_called()
        error_call = mock_logger.error.call_args[0][0]
        assert "Failed to create browser service" in error_call
        assert "Factory: NoneType" in error_call
        
        # Verify job status was set to error
        mock_job.set_error.assert_called()

    @pytest.mark.asyncio
    async def test_null_context_handling(self, job_runner_service, mock_job, mock_orchestrator, mock_browser_service, mock_logger):
        """Test proper error handling when context creation returns None."""
        # Set up orchestrator
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        # Mock context creation returning None
        mock_browser_service.new_context.return_value = None
        
        # Run job
        result = await job_runner_service.run_job(mock_job)
        
        # Verify error was handled
        mock_logger.error.assert_called()
        mock_job.set_error.assert_called()
        
        # Verify error message includes context details
        error_call = mock_job.set_error.call_args[0][0]
        assert "Failed to create browser context" in error_call

    @pytest.mark.asyncio
    async def test_browser_service_fallback_creation(self, mock_logger, mock_config, mock_job):
        """Test fallback browser service creation when factory is None."""
        # Create service without factory
        job_runner_service = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            browser_service_factory=None
        )
        
        # Set up orchestrator
        mock_orchestrator = Mock()
        mock_orchestrator.get_download_path.return_value = "/tmp/test"
        mock_orchestrator.process_single_court_job = AsyncMock(return_value={"status": "success", "metrics": {}})
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        # Mock PacerBrowserService creation
        with patch('src.pacer.jobs.job_runner_service.PacerBrowserService') as mock_browser_class:
            mock_browser_instance = AsyncMock()
            mock_browser_instance.new_context = AsyncMock()
            mock_browser_instance.close = AsyncMock()
            mock_browser_class.return_value = mock_browser_instance
            
            mock_context = AsyncMock()
            mock_browser_instance.new_context.return_value = mock_context
            
            # Run job
            result = await job_runner_service.run_job(mock_job)
            
            # Verify fallback browser service was created
            mock_browser_class.assert_called_once()
            call_kwargs = mock_browser_class.call_args[1]
            assert call_kwargs['logger'] is mock_logger
            assert call_kwargs['headless'] is True
            assert call_kwargs['timeout_ms'] == 30000

    @pytest.mark.asyncio
    async def test_di_container_wiring_validation(self, mock_logger, mock_config):
        """Test that DI container properly wires dependencies."""
        # Test factory-based creation 
        mock_factory = Mock(return_value=AsyncMock())
        
        service = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            browser_service_factory=mock_factory
        )
        
        # Verify dependencies are properly injected
        assert service.config is mock_config
        assert service.logger is mock_logger
        assert service.browser_service_factory is mock_factory
        assert service.pacer_orchestrator is None  # Starts as None to avoid circular dependency

    def test_orchestrator_dependency_injection_pattern(self, job_runner_service, mock_orchestrator):
        """Test the orchestrator dependency injection pattern."""
        # Initially None to break circular dependency
        assert job_runner_service.pacer_orchestrator is None
        
        # Set orchestrator to break circular dependency
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        # Verify orchestrator is now set
        assert job_runner_service.pacer_orchestrator is mock_orchestrator

    @pytest.mark.asyncio
    async def test_missing_orchestrator_error_handling(self, job_runner_service, mock_job, mock_browser_service):
        """Test error handling when orchestrator is not set."""
        # Don't set orchestrator (circular dependency not resolved)
        
        # Run job - should fail with RuntimeError
        result = await job_runner_service.run_job(mock_job)
        
        # Verify error was set on job
        mock_job.set_error.assert_called()
        error_message = mock_job.set_error.call_args[0][0]
        assert "PacerOrchestratorService not set" in error_message

    @pytest.mark.asyncio
    async def test_comprehensive_error_handling_flow(self, job_runner_service, mock_job, mock_orchestrator, mock_browser_service, mock_logger):
        """Test comprehensive error handling across all failure points."""
        # Set up orchestrator
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        # Mock context creation to raise exception
        mock_browser_service.new_context.side_effect = Exception("Context creation failed")
        
        # Run job
        result = await job_runner_service.run_job(mock_job)
        
        # Verify comprehensive error handling
        mock_logger.error.assert_called()
        mock_job.set_error.assert_called()
        
        # Verify cleanup was attempted
        mock_browser_service.close.assert_called()

    def test_defensive_null_checks_implementation(self, job_runner_service):
        """Test that defensive null checks are properly implemented."""
        # These should not raise exceptions due to null checks
        assert job_runner_service.config is not None
        assert job_runner_service.logger is not None
        
        # Browser service factory can be None (fallback pattern)
        job_runner_service.browser_service_factory = None
        # Should not raise exception

    @pytest.mark.asyncio
    async def test_error_message_enhancement(self, job_runner_service, mock_job, mock_orchestrator, mock_browser_service, mock_logger):
        """Test that error messages provide sufficient debugging information."""
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        # Mock context creation failure
        mock_browser_service.new_context.side_effect = Exception("Test error")
        
        await job_runner_service.run_job(mock_job)
        
        # Verify error messages contain useful debugging info
        error_calls = [call[0][0] for call in mock_logger.error.call_args_list]
        assert any("job test-job-123" in msg for msg in error_calls)
        assert any("court test-court" in msg for msg in error_calls)

    def test_component_base_logger_integration(self, mock_logger, mock_config):
        """Test integration with component_base.py logging patterns."""
        # Create orchestrator facade to test component_base integration
        facade = PacerOrchestratorFacade(
            logger=mock_logger,
            config=mock_config
        )
        
        # Verify logger inheritance
        assert facade.logger is mock_logger
        
        # Test component_base logging methods are available
        assert hasattr(facade, 'log_info')
        assert hasattr(facade, 'log_debug')
        assert hasattr(facade, 'log_warning')
        assert hasattr(facade, 'log_error')

    def test_report_workflow_engine_context_validation(self, mock_logger, mock_config):
        """Test that report workflow engine properly validates contexts."""
        engine = PacerReportWorkflowEngine(
            logger=mock_logger,
            config=mock_config
        )
        
        # Verify logger inheritance from component_base
        assert engine.logger is mock_logger
        
        # Test component_base methods are inherited
        assert hasattr(engine, 'log_info')
        assert hasattr(engine, 'log_debug')


class TestBrowserContextErrorScenarios:
    """Test suite for browser context error scenarios and edge cases."""

    @pytest.fixture
    def mock_logger(self):
        """Mock logger for error testing."""
        logger = Mock(spec=logging.Logger)
        logger.info = Mock()
        logger.debug = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.exception = Mock()
        return logger

    @pytest.fixture
    def mock_config(self):
        """Mock configuration for error tests."""
        return {
            "headless": True,
            "timeout_ms": 30000,
            "data_dir": "/tmp/test"
        }

    @pytest.mark.asyncio
    async def test_browser_service_creation_exception(self, mock_logger, mock_config):
        """Test exception handling during browser service creation."""
        def failing_factory(*args, **kwargs):
            raise Exception("Factory initialization failed")
        
        job_runner_service = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            browser_service_factory=failing_factory
        )
        
        mock_orchestrator = Mock()
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        mock_job = Mock()
        mock_job.job_id = "failing-job"
        mock_job.court_id = "failing-court"
        mock_job.config_snapshot = {"headless": True}
        mock_job.start_timer = Mock()
        mock_job.end_timer = Mock()
        mock_job.set_error = Mock()
        mock_job.metrics = {}
        
        result = await job_runner_service.run_job(mock_job)
        
        # Verify exception was handled
        mock_logger.error.assert_called()
        mock_job.set_error.assert_called()

    @pytest.mark.asyncio
    async def test_context_creation_timeout(self, mock_logger, mock_config):
        """Test handling of context creation timeouts."""
        async def timeout_factory(*args, **kwargs):
            browser_service = AsyncMock()
            browser_service.new_context = AsyncMock()
            browser_service.close = AsyncMock()
            
            # Simulate timeout by never resolving
            browser_service.new_context.side_effect = asyncio.TimeoutError("Context creation timed out")
            return browser_service
        
        job_runner_service = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            browser_service_factory=timeout_factory
        )
        
        mock_orchestrator = Mock()
        mock_orchestrator.get_download_path.return_value = "/tmp/test"
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        mock_job = Mock()
        mock_job.job_id = "timeout-job"
        mock_job.court_id = "timeout-court"
        mock_job.config_snapshot = {"headless": True, "timeout_ms": 1000}
        mock_job.start_timer = Mock()
        mock_job.end_timer = Mock()
        mock_job.set_error = Mock()
        mock_job.metrics = {}
        
        result = await job_runner_service.run_job(mock_job)
        
        # Verify timeout was handled gracefully
        mock_logger.error.assert_called()
        mock_job.set_error.assert_called()

    def test_invalid_configuration_handling(self, mock_logger):
        """Test handling of invalid configuration parameters."""
        # Test with missing required config
        invalid_config = {}
        
        job_runner_service = PacerJobRunnerService(
            config=invalid_config,
            logger=mock_logger,
            browser_service_factory=None
        )
        
        # Should not raise exception during initialization
        assert job_runner_service.config == invalid_config
        assert job_runner_service.logger is mock_logger

    @pytest.mark.asyncio
    async def test_resource_cleanup_on_failure(self, mock_logger, mock_config):
        """Test that resources are properly cleaned up on failure."""
        mock_browser_service = AsyncMock()
        mock_context = AsyncMock()
        
        def factory(*args, **kwargs):
            return mock_browser_service
        
        mock_browser_service.new_context.return_value = mock_context
        
        job_runner_service = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            browser_service_factory=factory
        )
        
        mock_orchestrator = Mock()
        mock_orchestrator.get_download_path.return_value = "/tmp/test"
        mock_orchestrator.process_single_court_job = AsyncMock(side_effect=Exception("Processing failed"))
        job_runner_service.set_orchestrator(mock_orchestrator)
        
        mock_job = Mock()
        mock_job.job_id = "cleanup-job"
        mock_job.court_id = "cleanup-court"
        mock_job.config_snapshot = {"headless": True}
        mock_job.start_timer = Mock()
        mock_job.end_timer = Mock()
        mock_job.set_error = Mock()
        mock_job.metrics = {}
        
        result = await job_runner_service.run_job(mock_job)
        
        # Verify cleanup was called even on failure
        mock_context.close.assert_called_once()
        mock_browser_service.close.assert_called_once()


class TestDIContainerValidation:
    """Test suite for DI container wiring and validation."""

    def test_orchestration_container_structure(self):
        """Test that orchestration container has proper structure."""
        from src.containers.orchestration_container import OrchestrationContainer
        
        container = OrchestrationContainer()
        
        # Test key providers exist
        assert hasattr(container, 'job_runner_service')
        assert hasattr(container, 'browser_service_factory')
        assert hasattr(container, 'pacer_orchestrator_service')
        
        # Test configuration exists
        assert hasattr(container, 'config')
        assert hasattr(container, 'logger')

    def test_dependency_injection_patterns(self):
        """Test that DI patterns are properly implemented."""
        from src.containers.orchestration_container import OrchestrationContainer
        
        container = OrchestrationContainer()
        
        # Test that dependencies are properly declared
        job_runner = container.job_runner_service
        assert job_runner is not None
        
        # Test provider types
        from dependency_injector import providers
        assert isinstance(job_runner, providers.Factory)

    @pytest.mark.asyncio
    async def test_container_wiring_integration(self):
        """Test full container wiring with mock dependencies."""
        from src.containers.orchestration_container import OrchestrationContainer
        
        container = OrchestrationContainer()
        
        # Override with mocks for testing
        container.config.from_dict({
            "headless": True,
            "timeout_ms": 30000,
            "data_dir": "/tmp/test"
        })
        
        container.logger.override(Mock(spec=logging.Logger))
        container.browser_service_factory.override(Mock())
        
        # Test that services can be created
        job_runner = container.job_runner_service()
        assert job_runner is not None
        assert hasattr(job_runner, 'browser_service_factory')
        
        container.unwire()