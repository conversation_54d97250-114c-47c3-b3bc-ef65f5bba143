import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "src"))
import pytest

from dependency_injector import providers
from unittest.mock import Mock, AsyncMock, patch


@pytest.fixture
def test_container(test_container):
    """Create test DI container with proper mocking."""
    from tests.conftest_di_updated import TestApplicationContainer
    
    container = TestApplicationContainer()
    
    # Setup default mock configuration
    mock_config_data = {
        'config_name': 'test_config',
        'iso_date': '20240101',
        'DATA_DIR': '/tmp/test',
        'scraper': False,
        'post_process': False,
        'upload': False,
        'fb_ads': False,
        'report_generator': False
    }
    
    container.workflow_specific_config.from_dict(mock_config_data)
    
    yield container
    
    # Cleanup
    try:
        container.unwire()
    except:
        pass

"""Tests for S3 async initialization and connection handling."""
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from botocore.exceptions import C<PERSON><PERSON>rror, NoCredentialsError
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.protocols.exceptions import ServiceError

class TestS3AsyncInitialization:
    """Test S3 async initialization and connection management."""

    @pytest.fixture
    def mock_logger(self):
        """Mock logger implementing LoggerProtocol."""
        logger = MagicMock(spec=LoggerProtocol)
        logger.debug = Mock()
        logger.info = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.exception = Mock()
        return logger

    @pytest.fixture
    def s3_config(self):
        """Test S3 configuration."""
        return {
            'bucket_name': 'test-bucket',
            'aws_access_key': 'test-access-key',
            'aws_secret_key': 'test-secret-key',
            'aws_region': 'us-west-2'
        }

    @pytest.fixture
    def s3_storage(self, s3_config, mock_logger):
        """Create S3AsyncStorage instance."""
        return S3AsyncStorage(
            logger=mock_logger,
            bucket_name=s3_config['bucket_name'],
            aws_access_key_id=s3_config['aws_access_key_id'],
            aws_secret_access_key=s3_config['aws_secret_access_key'],
            aws_region=s3_config['aws_region']
        )

    def test_init_with_explicit_region(self, mock_logger, test_container):
        """Test initialization with explicit region."""
        storage = S3AsyncStorage(
            logger=mock_logger,
            bucket_name='test-bucket',
            aws_access_key_id='key',
            aws_secret_access_key='secret',
            aws_region='eu-west-1'
        )
        
        assert storage.bucket_name == 'test-bucket'
        assert storage.aws_region == 'eu-west-1'
        assert storage.max_pool_connections == 50  # default

    def test_init_with_custom_pool_size(self, mock_logger, test_container):
        """Test initialization with custom connection pool size."""
        storage = S3AsyncStorage(
            logger=mock_logger,
            bucket_name='test-bucket',
            aws_access_key_id='key',
            aws_secret_access_key='secret',
            max_pool_connections=100
        )
        
        assert storage.max_pool_connections == 100

    @patch.dict('os.environ', {'AWS_REGION': 'us-east-1'})
    def test_init_region_from_environment(self, mock_logger, test_container):
        """Test region detection from environment variables."""
        storage = S3AsyncStorage(
            logger=mock_logger,
            bucket_name='test-bucket',
            aws_access_key_id='key',
            aws_secret_access_key='secret'
        )
        
        assert storage.aws_region == 'us-east-1'

    @patch.dict('os.environ', {'LEXGENIUS_AWS_REGION': 'us-west-2'})
    def test_init_region_from_lexgenius_env(self, mock_logger, test_container):
        """Test region detection from LEXGENIUS_AWS_REGION."""
        storage = S3AsyncStorage(
            logger=mock_logger,
            bucket_name='test-bucket',
            aws_access_key_id='key',
            aws_secret_access_key='secret'
        )
        
        assert storage.aws_region == 'us-west-2'

    @patch.dict('os.environ', {}, clear=True)
    def test_init_default_region(self, mock_logger, test_container):
        """Test default region when no environment variables."""
        storage = S3AsyncStorage(
            logger=mock_logger,
            bucket_name='test-bucket',
            aws_access_key_id='key',
            aws_secret_access_key='secret'
        )
        
        assert storage.aws_region == 'us-west-2'

    @pytest.mark.asyncio
    @patch('src.infrastructure.storage.s3_async.aioboto3.Session')
    @pytest.mark.asyncio
    async def test_initialize_success(self, mock_session_class, s3_storage, test_container):
        """Test successful S3 client initialization."""
        # Mock session and client  
        mock_session = Mock()
        mock_client = AsyncMock()
        mock_session_class.return_value = mock_session
        
        # Mock the client context manager properly
        mock_client_context = AsyncMock()
        mock_client_context.__aenter__ = AsyncMock(return_value=mock_client)
        mock_session.client.return_value = mock_client_context
        
        await s3_storage.initialize()
        
        # Verify session creation
        mock_session_class.assert_called_once_with(
            aws_access_key_id='test-access-key',
            aws_secret_access_key='test-secret-key',
            region_name='us-west-2'
        )
        
        # Verify client creation
        mock_session.client.assert_called_once()
        assert s3_storage._client == mock_client

    @pytest.mark.asyncio
    @patch('src.infrastructure.storage.s3_async.aioboto3.Session')
    @pytest.mark.asyncio
    async def test_initialize_failure(self, mock_session_class, s3_storage, test_container):
        """Test S3 client initialization failure."""
        # Mock session creation failure
        mock_session_class.side_effect = Exception("Connection failed")
        
        with pytest.raises(ServiceError, match="Failed to initialize"):
            await s3_storage.initialize()

    @pytest.mark.asyncio
    async def test_close_with_client(self, s3_storage, test_container):
        """Test closing S3 client when client exists."""
        # Mock initialized client
        mock_client = AsyncMock()
        s3_storage._client = mock_client
        
        await s3_storage.cleanup()
        
        # Verify client cleanup
        mock_client.__aexit__.assert_called_once_with(None, None, None)
        assert s3_storage._client is None
        assert s3_storage._session is None

    @pytest.mark.asyncio
    async def test_close_without_client(self, s3_storage, test_container):
        """Test closing when no client exists."""
        # Should not raise any exceptions
        await s3_storage.cleanup()
        
        assert s3_storage._client is None
        assert s3_storage._session is None

    @pytest.mark.asyncio
    async def test_file_exists_not_initialized(self, s3_storage, test_container):
        """Test file_exists when client not initialized."""
        # Client is None, so should handle gracefully
        with patch.object(s3_storage, '_client', None):
            # This should return False since client is None
            result = await s3_storage.file_exists('test-key')
            assert result is False

    @pytest.mark.asyncio
    async def test_file_exists_success(self, s3_storage, test_container):
        """Test successful file existence check."""
        # Initialize the service first
        await s3_storage.initialize()
        
        # Mock the client that gets created during initialization
        mock_client = AsyncMock()
        s3_storage._client = mock_client
        mock_client.head_object.return_value = {}
        
        result = await s3_storage.file_exists('test-key')
        
        assert result is True
        mock_client.head_object.assert_called_once_with(
            Bucket='test-bucket',
            Key='test-key'
        )

    @pytest.mark.asyncio
    async def test_file_exists_not_found(self, s3_storage, test_container):
        """Test file existence check when file not found."""
        # Mock initialized client
        mock_client = AsyncMock()
        s3_storage._client = mock_client
        
        # Mock 404 error
        error = ClientError(
            {'Error': {'Code': '404'}},
            'HeadObject'
        )
        mock_client.head_object.side_effect = error
        
        result = await s3_storage.file_exists('test-key')
        
        assert result is False

    @pytest.mark.asyncio
    async def test_file_exists_access_denied(self, s3_storage, test_container):
        """Test file existence check with access denied."""
        # Mock initialized client
        mock_client = AsyncMock()
        s3_storage._client = mock_client
        
        # Mock 403 error
        error = ClientError(
            {'Error': {'Code': '403'}},
            'HeadObject'
        )
        mock_client.head_object.side_effect = error
        
        result = await s3_storage.file_exists('test-key')
        
        assert result is False

    @pytest.mark.asyncio
    async def test_file_exists_other_client_error(self, s3_storage, test_container):
        """Test file existence check with other client error."""
        # Mock initialized client
        mock_client = AsyncMock()
        s3_storage._client = mock_client
        
        # Mock other error
        error = ClientError(
            {'Error': {'Code': 'InternalError'}},
            'HeadObject'
        )
        mock_client.head_object.side_effect = error
        
        result = await s3_storage.file_exists('test-key')
        
        assert result is False

    @pytest.mark.asyncio
    async def test_file_exists_generic_exception(self, s3_storage, test_container):
        """Test file existence check with generic exception."""
        # Mock initialized client
        mock_client = AsyncMock()
        s3_storage._client = mock_client
        mock_client.head_object.side_effect = Exception("Network error")
        
        result = await s3_storage.file_exists('test-key')
        
        assert result is False

    def test_file_exists_empty_key(self, s3_storage, test_container):
        """Test file existence check with empty key."""
        # Should be handled by the method directly
        import asyncio
        result = asyncio.run(s3_storage.file_exists(''))
        
        assert result is False

    def test_file_exists_none_key(self, s3_storage, test_container):
        """Test file existence check with None key."""
        import asyncio
        result = asyncio.run(s3_storage.file_exists(None))
        
        assert result is False

class TestS3AsyncConnectionManagement:
    """Test connection management and resource cleanup."""

    @pytest.fixture
    def mock_logger(self):
        """Mock logger implementing LoggerProtocol."""
        logger = MagicMock(spec=LoggerProtocol)
        logger.debug = Mock()
        logger.info = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.exception = Mock()
        return logger

    @pytest.mark.asyncio
    async def test_multiple_operations_same_client(self, mock_logger, test_container):
        """Test that multiple operations use the same client instance."""
        storage = S3AsyncStorage(
            logger=mock_logger,
            bucket_name='test-bucket',
            aws_access_key_id='key',
            aws_secret_access_key='secret'
        )
        
        with patch('src.infrastructure.storage.s3_async.aioboto3.Session') as mock_session_class:
            mock_session = Mock()
            mock_client = AsyncMock()
            mock_session_class.return_value = mock_session
            
            # Mock the client context manager properly
            mock_client_context = AsyncMock()
            mock_client_context.__aenter__ = AsyncMock(return_value=mock_client)
            mock_session.client.return_value = mock_client_context
            
            # Initialize once
            await storage.initialize()
            
            # Multiple file operations
            mock_client.head_object.return_value = {}
            
            await storage.file_exists('key1')
            await storage.file_exists('key2')
            await storage.file_exists('key3')
            
            # Should only create session/client once
            mock_session_class.assert_called_once()
            mock_session.client.assert_called_once()
            
            # But head_object should be called for each check
            assert mock_client.head_object.call_count == 3

    @pytest.mark.asyncio
    async def test_reinitialize_after_close(self, mock_logger, test_container):
        """Test re-initialization after closing."""
        storage = S3AsyncStorage(
            logger=mock_logger,
            bucket_name='test-bucket',
            aws_access_key_id='key',
            aws_secret_access_key='secret'
        )
        
        with patch('src.infrastructure.storage.s3_async.aioboto3.Session') as mock_session_class:
            mock_session = Mock()
            mock_client = AsyncMock()
            mock_session_class.return_value = mock_session
            
            # Create an async context manager mock
            mock_client_context = AsyncMock()
            mock_client_context.__aenter__ = AsyncMock(return_value=mock_client)
            mock_client_context.__aexit__ = AsyncMock(return_value=None)
            mock_session.client.return_value = mock_client_context
            
            # Initialize
            await storage.initialize()
            assert storage._client == mock_client
            
            # Close
            await storage.cleanup()
            assert storage._client is None
            
            # Re-initialize
            await storage.initialize()
            assert storage._client == mock_client
            
            # Should have been called twice
            assert mock_session_class.call_count == 2

if __name__ == '__main__':
    pytest.main([__file__])