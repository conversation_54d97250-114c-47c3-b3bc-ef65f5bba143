"""
Unit tests for PacerCaseProcessingService.

Tests cover:
- Case HTML processing and data extraction
- Component coordination (CaseDataProcessor, CaseWorkflowManager, CaseMetadataExtractor)
- Error handling and recovery
- Integration with DI container

All tests use DI container injection following existing patterns.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import date, datetime

from src.pacer.components.case_processing_service import PacerCaseProcessingService
from src.infrastructure.protocols.exceptions import PacerServiceError
from playwright.async_api import Page


@pytest.fixture
def case_processing_service(test_container):
    """Get case processing service with DI container injection."""
    config = {
        'iso_date': '20240101',
        'court_id': 'nys',
        'timeout': 30000
    }
    
    # Create service with mocked dependencies
    service = PacerCaseProcessingService(
        logger=test_container.pacer.mock_config().logger,
        config=config,
        court_id='nys',
        classification_service=test_container.pacer.data.case_classification_service(),
        html_processing_service=test_container.pacer.data.html_processing_service()
    )
    
    return service


@pytest.fixture
def mock_page():
    """Mock Playwright page."""
    page = AsyncMock(spec=Page)
    page.content = AsyncMock(return_value="<html><body>Test Case Content</body></html>")
    page.wait_for_load_state = AsyncMock()
    page.wait_for_selector = AsyncMock()
    page.url = "https://test.pacer.gov/case"
    return page


@pytest.fixture
def sample_case_details():
    """Sample case details for testing."""
    return {
        'court_id': 'nys',
        'docket_num': '1:23-cv-00001',
        'case_name': 'Test Case v. Defendant',
        'filed_date': '2023-01-15',
        'nature_of_suit': '190',
        'judge': 'Test Judge',
        'status': 'Open',
        'demand': '$50,000',
        'jury_demand': 'Yes'
    }


@pytest.fixture
def initial_case_details():
    """Initial case details from report."""
    return {
        'court_id': 'nys',
        'docket_num': '1:23-cv-00001',
        'case_name': 'Test Case v. Defendant',
        'filed_date': '2023-01-15'
    }


class TestPacerCaseProcessingService:
    """Test cases for PacerCaseProcessingService."""

    class TestInitialization:
        """Test service initialization."""

        def test_init_with_dependencies(self, test_container):
            """Test initialization with injected dependencies."""
            config = {'court_id': 'nys', 'iso_date': '20240101'}
            
            service = PacerCaseProcessingService(
                logger=test_container.pacer.mock_config().logger,
                config=config,
                court_id='nys',
                classification_service=test_container.pacer.data.case_classification_service(),
                html_processing_service=test_container.pacer.data.html_processing_service()
            )
            
            assert service.court_id == 'nys'
            assert service.classification_service is not None
            assert service.html_processing_service is not None
            assert hasattr(service, 'case_data_processor')
            assert hasattr(service, 'case_workflow_manager')
            assert hasattr(service, 'case_metadata_extractor')

        def test_init_creates_components(self, case_processing_service):
            """Test that initialization creates all required components."""
            assert case_processing_service.case_data_processor is not None
            assert case_processing_service.case_workflow_manager is not None
            assert case_processing_service.case_metadata_extractor is not None
            
            # Verify components have correct configuration
            assert case_processing_service.case_data_processor.court_id == 'nys'
            assert case_processing_service.case_workflow_manager.court_id == 'nys'
            assert case_processing_service.case_metadata_extractor.court_id == 'nys'

    class TestExecuteAction:
        """Test _execute_action method routing."""

        @pytest.mark.asyncio
        async def test_execute_action_workflow_actions(self, case_processing_service, mock_page, sample_case_details):
            """Test execute_action routing to workflow manager."""
            data = {
                'action': 'wait_for_page_content',
                'page': mock_page,
                'case_details': sample_case_details
            }
            
            with patch.object(case_processing_service.case_workflow_manager, 'perform_action') as mock_workflow:
                mock_workflow.return_value = "Page content loaded"
                
                result = await case_processing_service._execute_action(data)
                
                assert result == "Page content loaded"
                mock_workflow.assert_called_once_with(data)

        @pytest.mark.asyncio
        async def test_execute_action_data_processor_actions(self, case_processing_service, sample_case_details):
            """Test execute_action routing to data processor."""
            data = {
                'action': 'update_case_details',
                'html_content': '<html>Test</html>',
                'case_details': sample_case_details
            }
            
            with patch.object(case_processing_service.case_data_processor, 'perform_action') as mock_processor:
                mock_processor.return_value = {**sample_case_details, 'updated': True}
                
                result = await case_processing_service._execute_action(data)
                
                assert result['updated'] is True
                mock_processor.assert_called_once_with(data)

        @pytest.mark.asyncio
        async def test_execute_action_metadata_extractor_actions(self, case_processing_service, sample_case_details):
            """Test execute_action routing to metadata extractor."""
            data = {
                'action': 'process_mdl_flags',
                'case_details': sample_case_details
            }
            
            with patch.object(case_processing_service.case_metadata_extractor, 'perform_action') as mock_extractor:
                mock_extractor.return_value = {**sample_case_details, 'mdl_processed': True}
                
                result = await case_processing_service._execute_action(data)
                
                assert result['mdl_processed'] is True
                mock_extractor.assert_called_once_with(data)

        @pytest.mark.asyncio
        async def test_execute_action_process_case_html(self, case_processing_service, mock_page, sample_case_details, initial_case_details):
            """Test execute_action with process_case_html action."""
            data = {
                'action': 'process_case_html',
                'page': mock_page,
                'case_details': sample_case_details,
                'initial_details': initial_case_details,
                'is_explicitly_requested': True
            }
            
            with patch.object(case_processing_service, 'process_case_html') as mock_process:
                mock_process.return_value = {**sample_case_details, 'processed': True}
                
                result = await case_processing_service._execute_action(data)
                
                assert result['processed'] is True
                mock_process.assert_called_once_with(
                    mock_page, sample_case_details, initial_case_details, True
                )

        @pytest.mark.asyncio
        async def test_execute_action_invalid_action(self, case_processing_service):
            """Test execute_action with invalid action."""
            data = {
                'action': 'invalid_action',
                'case_details': {}
            }
            
            with pytest.raises(PacerServiceError, match="Invalid action data provided"):
                await case_processing_service._execute_action(data)

        @pytest.mark.asyncio
        async def test_execute_action_invalid_data_type(self, case_processing_service):
            """Test execute_action with invalid data type."""
            data = "invalid_string_data"
            
            with pytest.raises(PacerServiceError, match="Invalid action data provided"):
                await case_processing_service._execute_action(data)

    class TestProcessCaseHtml:
        """Test main case HTML processing method."""

        @pytest.mark.asyncio
        async def test_process_case_html_success(self, case_processing_service, mock_page, sample_case_details, initial_case_details):
            """Test successful case HTML processing."""
            with patch.object(case_processing_service.case_workflow_manager, 'orchestrate_case_processing') as mock_orchestrate:
                mock_orchestrate.return_value = {
                    **sample_case_details,
                    'html_processed': True,
                    'metadata_extracted': True,
                    'processing_complete': True
                }
                
                result = await case_processing_service.process_case_html(
                    mock_page, sample_case_details, initial_case_details, is_explicitly_requested=True
                )
                
                assert result['html_processed'] is True
                assert result['metadata_extracted'] is True
                assert result['processing_complete'] is True
                
                # Verify orchestration was called with all dependencies
                mock_orchestrate.assert_called_once_with(
                    page=mock_page,
                    case_details=sample_case_details,
                    initial_details=initial_case_details,
                    is_explicitly_requested=True,
                    case_data_processor=case_processing_service.case_data_processor,
                    case_metadata_extractor=case_processing_service.case_metadata_extractor,
                    html_processing_service=case_processing_service.html_processing_service,
                    classification_service=case_processing_service.classification_service
                )

        @pytest.mark.asyncio
        async def test_process_case_html_failure(self, case_processing_service, mock_page, sample_case_details, initial_case_details):
            """Test case HTML processing failure."""
            with patch.object(case_processing_service.case_workflow_manager, 'orchestrate_case_processing') as mock_orchestrate:
                mock_orchestrate.side_effect = Exception("Processing failed")
                
                with pytest.raises(Exception, match="Processing failed"):
                    await case_processing_service.process_case_html(
                        mock_page, sample_case_details, initial_case_details
                    )

    class TestBackwardCompatibilityMethods:
        """Test backward compatibility methods."""

        @pytest.mark.asyncio
        async def test_wait_for_page_content(self, case_processing_service, mock_page, sample_case_details):
            """Test wait_for_page_content delegation."""
            with patch.object(case_processing_service.case_workflow_manager, 'wait_for_page_content') as mock_wait:
                mock_wait.return_value = "Content loaded"
                
                result = await case_processing_service.wait_for_page_content(mock_page, sample_case_details)
                
                assert result == "Content loaded"
                mock_wait.assert_called_once_with(mock_page, sample_case_details)

        def test_update_case_details(self, case_processing_service, sample_case_details):
            """Test update_case_details delegation."""
            html_content = "<html><body>Test case content</body></html>"
            
            with patch.object(case_processing_service.case_data_processor, 'update_case_details') as mock_update:
                mock_update.return_value = {**sample_case_details, 'updated_from_html': True}
                
                result = case_processing_service.update_case_details(html_content, sample_case_details)
                
                assert result['updated_from_html'] is True
                mock_update.assert_called_once_with(html_content, sample_case_details)

        def test_process_mdl_flags(self, case_processing_service, sample_case_details):
            """Test process_mdl_flags delegation."""
            with patch.object(case_processing_service.case_metadata_extractor, 'process_mdl_flags') as mock_mdl:
                mock_mdl.return_value = {**sample_case_details, 'mdl_flags_processed': True}
                
                result = case_processing_service.process_mdl_flags(sample_case_details)
                
                assert result['mdl_flags_processed'] is True
                mock_mdl.assert_called_once_with(sample_case_details)

        def test_check_notice_of_removal(self, case_processing_service):
            """Test check_notice_of_removal delegation."""
            html_content = "<html><body>Notice of removal content</body></html>"
            
            with patch.object(case_processing_service.case_metadata_extractor, 'check_notice_of_removal') as mock_removal:
                mock_removal.return_value = {'is_removal': True, 'removal_type': 'federal_question'}
                
                result = case_processing_service.check_notice_of_removal(html_content)
                
                assert result['is_removal'] is True
                assert result['removal_type'] == 'federal_question'
                mock_removal.assert_called_once_with(html_content)

        def test_determine_removal_status(self, case_processing_service, initial_case_details):
            """Test determine_removal_status delegation."""
            html_content = "<html><body>Removal case content</body></html>"
            
            with patch.object(case_processing_service.case_metadata_extractor, 'determine_removal_status') as mock_status:
                mock_status.return_value = True
                
                result = case_processing_service.determine_removal_status(
                    html_content, initial_case_details, is_explicitly_requested=True
                )
                
                assert result is True
                assert case_processing_service.is_removal is True  # Instance state updated
                mock_status.assert_called_once_with(html_content, initial_case_details, True)

        def test_create_base_filename(self, case_processing_service, sample_case_details):
            """Test create_base_filename delegation."""
            with patch.object(case_processing_service.case_metadata_extractor, 'create_base_filename') as mock_filename:
                mock_filename.return_value = "nys_1_23_cv_00001"
                
                result = case_processing_service.create_base_filename(sample_case_details)
                
                assert result == "nys_1_23_cv_00001"
                mock_filename.assert_called_once_with(sample_case_details)

        def test_clean_dict_values(self, case_processing_service):
            """Test clean_dict_values delegation."""
            dirty_data = {'field1': '  value1  ', 'field2': 'value2\n', 'field3': None}
            
            with patch.object(case_processing_service.case_data_processor, 'clean_dict_values') as mock_clean:
                mock_clean.return_value = {'field1': 'value1', 'field2': 'value2', 'field3': ''}
                
                result = case_processing_service.clean_dict_values(dirty_data)
                
                assert result['field1'] == 'value1'
                assert result['field2'] == 'value2'
                assert result['field3'] == ''
                mock_clean.assert_called_once_with(dirty_data)

    class TestStaticMethods:
        """Test static backward compatibility methods."""

        def test_check_no_proceedings(self):
            """Test static check_no_proceedings method."""
            html_with_proceedings = "<html><body>Case proceedings found</body></html>"
            html_no_proceedings = "<html><body>No proceedings available</body></html>"
            
            with patch('src.pacer.components.case_processing_service.CaseWorkflowManager') as mock_manager_class:
                mock_manager = Mock()
                mock_manager.check_no_proceedings.return_value = False
                mock_manager_class.return_value = mock_manager
                
                result = PacerCaseProcessingService.check_no_proceedings(html_with_proceedings)
                
                assert result is False
                mock_manager.check_no_proceedings.assert_called_once_with(html_with_proceedings)

        def test_prepare_case_for_no_proceedings(self, sample_case_details, initial_case_details):
            """Test static prepare_case_for_no_proceedings method."""
            with patch('src.pacer.components.case_processing_service.CaseWorkflowManager') as mock_manager_class:
                mock_manager = Mock()
                mock_manager.prepare_case_for_no_proceedings.return_value = {
                    **sample_case_details, 
                    'no_proceedings': True
                }
                mock_manager_class.return_value = mock_manager
                
                result = PacerCaseProcessingService.prepare_case_for_no_proceedings(
                    sample_case_details, initial_case_details, is_explicitly_requested=True
                )
                
                assert result['no_proceedings'] is True
                mock_manager.prepare_case_for_no_proceedings.assert_called_once_with(
                    sample_case_details, initial_case_details, True
                )

        def test_add_processing_metadata(self, sample_case_details):
            """Test static add_processing_metadata method."""
            with patch('src.pacer.components.case_processing_service.CaseWorkflowManager') as mock_manager_class:
                mock_manager = Mock()
                mock_manager.add_processing_metadata.return_value = {
                    **sample_case_details, 
                    'processing_metadata': {'processed_at': '2024-01-01T12:00:00Z'}
                }
                mock_manager_class.return_value = mock_manager
                
                result = PacerCaseProcessingService.add_processing_metadata(sample_case_details)
                
                assert 'processing_metadata' in result
                mock_manager.add_processing_metadata.assert_called_once_with(sample_case_details)

    class TestComponentAccess:
        """Test component access methods."""

        def test_get_case_data_processor(self, case_processing_service):
            """Test get_case_data_processor method."""
            processor = case_processing_service.get_case_data_processor()
            assert processor == case_processing_service.case_data_processor

        def test_get_case_workflow_manager(self, case_processing_service):
            """Test get_case_workflow_manager method."""
            manager = case_processing_service.get_case_workflow_manager()
            assert manager == case_processing_service.case_workflow_manager

        def test_get_case_metadata_extractor(self, case_processing_service):
            """Test get_case_metadata_extractor method."""
            extractor = case_processing_service.get_case_metadata_extractor()
            assert extractor == case_processing_service.case_metadata_extractor

    class TestServiceLifecycle:
        """Test service lifecycle methods."""

        @pytest.mark.asyncio
        async def test_initialize_service(self, case_processing_service):
            """Test service initialization."""
            # Mock component initialization methods
            case_processing_service.case_data_processor.initialize = AsyncMock()
            case_processing_service.case_workflow_manager.initialize = AsyncMock()
            case_processing_service.case_metadata_extractor.initialize = AsyncMock()
            
            await case_processing_service._initialize_service()
            
            # Verify all components were initialized
            case_processing_service.case_data_processor.initialize.assert_called_once()
            case_processing_service.case_workflow_manager.initialize.assert_called_once()
            case_processing_service.case_metadata_extractor.initialize.assert_called_once()

        @pytest.mark.asyncio
        async def test_cleanup_service(self, case_processing_service):
            """Test service cleanup."""
            # Mock component cleanup methods
            case_processing_service.case_data_processor.cleanup = AsyncMock()
            case_processing_service.case_workflow_manager.cleanup = AsyncMock()
            case_processing_service.case_metadata_extractor.cleanup = AsyncMock()
            
            await case_processing_service._cleanup_service()
            
            # Verify all components were cleaned up
            case_processing_service.case_data_processor.cleanup.assert_called_once()
            case_processing_service.case_workflow_manager.cleanup.assert_called_once()
            case_processing_service.case_metadata_extractor.cleanup.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_docket_exists_in_db(self, case_processing_service):
        """Test check_docket_exists_in_db delegation."""
        court_id = 'nys'
        docket_num = '1:23-cv-00001'
        
        with patch.object(case_processing_service.case_workflow_manager, 'check_docket_exists_in_db') as mock_check:
            mock_check.return_value = True
            
            result = await case_processing_service._check_docket_exists_in_db(court_id, docket_num)
            
            assert result is True
            mock_check.assert_called_once_with(court_id, docket_num)


class TestPacerCaseProcessingIntegration:
    """Integration tests for PacerCaseProcessingService."""

    @pytest.mark.asyncio
    async def test_full_case_processing_workflow(self, test_container, mock_page, sample_case_details, initial_case_details):
        """Test complete case processing workflow with DI container."""
        # Create service with container dependencies
        service = PacerCaseProcessingService(
            logger=test_container.pacer.mock_config().logger,
            config={'court_id': 'nys', 'iso_date': '20240101'},
            court_id='nys',
            classification_service=test_container.pacer.data.case_classification_service(),
            html_processing_service=test_container.pacer.data.html_processing_service()
        )
        
        # Mock all component orchestration
        with patch.object(service.case_workflow_manager, 'orchestrate_case_processing') as mock_orchestrate:
            mock_orchestrate.return_value = {
                **sample_case_details,
                'html_processed': True,
                'metadata_extracted': True,
                'classification_complete': True,
                'processing_time': 45.2
            }
            
            result = await service.process_case_html(
                mock_page, sample_case_details, initial_case_details, is_explicitly_requested=True
            )
            
            assert result['docket_num'] == '1:23-cv-00001'
            assert result['html_processed'] is True
            assert result['metadata_extracted'] is True
            assert result['classification_complete'] is True
            
            # Verify orchestration included all services
            call_kwargs = mock_orchestrate.call_args.kwargs
            assert call_kwargs['case_data_processor'] == service.case_data_processor
            assert call_kwargs['case_metadata_extractor'] == service.case_metadata_extractor
            assert call_kwargs['classification_service'] == service.classification_service
            assert call_kwargs['html_processing_service'] == service.html_processing_service

    def test_di_container_service_wiring(self, test_container):
        """Test that DI container properly wires case processing service."""
        service = PacerCaseProcessingService(
            logger=test_container.pacer.mock_config().logger,
            config={'court_id': 'nys'},
            court_id='nys',
            classification_service=test_container.pacer.data.case_classification_service(),
            html_processing_service=test_container.pacer.data.html_processing_service()
        )
        
        # Verify all dependencies are properly injected
        assert service.classification_service is not None
        assert service.html_processing_service is not None
        assert service.case_data_processor is not None
        assert service.case_workflow_manager is not None
        assert service.case_metadata_extractor is not None
        
        # Verify service configuration
        assert service.court_id == 'nys'
        assert hasattr(service, 'logger')
        assert hasattr(service, 'config')

    @pytest.mark.asyncio
    async def test_component_coordination(self, case_processing_service, mock_page, sample_case_details):
        """Test that components are properly coordinated."""
        # Test that action routing works correctly
        with patch.object(case_processing_service.case_workflow_manager, 'perform_action') as mock_workflow, \
             patch.object(case_processing_service.case_data_processor, 'perform_action') as mock_processor, \
             patch.object(case_processing_service.case_metadata_extractor, 'perform_action') as mock_extractor:
            
            # Test workflow action
            await case_processing_service._execute_action({
                'action': 'wait_for_page_content',
                'page': mock_page,
                'case_details': sample_case_details
            })
            mock_workflow.assert_called_once()
            
            # Test data processor action
            await case_processing_service._execute_action({
                'action': 'update_case_details',
                'html_content': '<html>test</html>',
                'case_details': sample_case_details
            })
            mock_processor.assert_called_once()
            
            # Test metadata extractor action
            await case_processing_service._execute_action({
                'action': 'process_mdl_flags',
                'case_details': sample_case_details
            })
            mock_extractor.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])