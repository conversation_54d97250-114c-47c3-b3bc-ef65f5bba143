"""
Unit tests for PACER orchestration components.

Tests cover:
- PacerOrchestrator (main facade)
- ConfigurationManager
- ServiceCoordinator
- ResourceManager
- WorkflowManager
- JobScheduler
- ProgressTracker
- ErrorRecoveryManager
- ResultAggregator
- MetricsCollector

All tests use DI container injection following existing patterns.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from pathlib import Path

from src.pacer.components.orchestration.pacer_orchestrator import PacerOrchestrator
from src.infrastructure.protocols.exceptions import PacerServiceError, ServiceError
from playwright.async_api import BrowserContext


@pytest.fixture
def pacer_orchestrator(test_container):
    """Get PACER orchestrator with DI container injection."""
    config = {
        'iso_date': '********',
        'data_path': '/tmp/test_data',
        'timeout': 30000,
        'court_ids': ['nys', 'cand'],
        'concurrent_pacer_jobs': 3,
        'max_browser_contexts': 5
    }
    
    # Get services from container
    config_service = test_container.pacer.orchestration.configuration_service()
    service_factory = test_container.pacer.orchestration.service_factory()
    court_processing_service = test_container.pacer.orchestration.court_processing_service()
    deepseek_service = test_container.deepseek_service()
    
    return PacerOrchestrator(
        logger=test_container.pacer.mock_config().logger,
        config=config,
        config_service=config_service,
        service_factory=service_factory,
        court_processing_service=court_processing_service,
        deepseek_service=deepseek_service
    )


@pytest.fixture
def mock_browser_context():
    """Mock browser context for testing."""
    context = AsyncMock(spec=BrowserContext)
    context.new_page = AsyncMock()
    context.close = AsyncMock()
    context.pages = []
    return context


@pytest.fixture
def workflow_config():
    """Configuration for workflow tests."""
    return {
        'iso_date': '********',
        'court_ids': ['nys', 'cand'],
        'concurrent_jobs': 3,
        'timeout': 30000,
        'retry_attempts': 3,
        'batch_size': 10
    }


@pytest.fixture
def sample_pacer_job():
    """Sample PACER job for testing."""
    return {
        'job_id': 'test-job-001',
        'court_id': 'nys',
        'iso_date': '********',
        'job_type': 'court_processing',
        'status': 'pending',
        'priority': 'normal',
        'created_at': datetime.utcnow(),
        'config': {
            'timeout': 30000,
            'retry_attempts': 3
        }
    }


class TestPacerOrchestrator:
    """Test cases for PacerOrchestrator facade."""

    class TestInitialization:
        """Test orchestrator initialization."""

        def test_init_with_container_injection(self, test_container):
            """Test initialization using DI container."""
            config = {
                'iso_date': '********',
                'data_path': '/tmp/test_data'
            }
            
            orchestrator = PacerOrchestrator(
                logger=test_container.pacer.mock_config().logger,
                config=config,
                config_service=test_container.pacer.orchestration.configuration_service(),
                service_factory=test_container.pacer.orchestration.service_factory()
            )
            
            assert orchestrator is not None
            assert hasattr(orchestrator, 'configuration_manager')
            assert hasattr(orchestrator, 'service_coordinator')
            assert hasattr(orchestrator, 'resource_manager')
            assert hasattr(orchestrator, 'workflow_manager')
            assert hasattr(orchestrator, 'job_scheduler')

        def test_init_creates_all_components(self, pacer_orchestrator):
            """Test that initialization creates all required components."""
            # Verify all orchestration components are created
            assert pacer_orchestrator.configuration_manager is not None
            assert pacer_orchestrator.service_coordinator is not None
            assert pacer_orchestrator.resource_manager is not None
            assert pacer_orchestrator.workflow_manager is not None
            assert pacer_orchestrator.job_scheduler is not None
            assert pacer_orchestrator.progress_tracker is not None
            assert pacer_orchestrator.error_recovery_manager is not None
            assert pacer_orchestrator.result_aggregator is not None
            assert pacer_orchestrator.metrics_collector is not None

        def test_setup_component_dependencies(self, pacer_orchestrator):
            """Test that component dependencies are properly set up."""
            # Test that workflow manager has service coordinator dependency
            assert hasattr(pacer_orchestrator.workflow_manager, '_dependencies')
            
            # Test that all components have configuration manager dependency
            components = [
                pacer_orchestrator.service_coordinator,
                pacer_orchestrator.resource_manager,
                pacer_orchestrator.workflow_manager,
                pacer_orchestrator.job_scheduler
            ]
            
            for component in components:
                assert hasattr(component, '_dependencies')

    class TestServiceInitialization:
        """Test service initialization workflow."""

        @pytest.mark.asyncio
        async def test_initialize_service_success(self, pacer_orchestrator):
            """Test successful service initialization."""
            # Mock all component initialization methods
            components = [
                pacer_orchestrator.configuration_manager,
                pacer_orchestrator.metrics_collector,
                pacer_orchestrator.error_recovery_manager,
                pacer_orchestrator.resource_manager,
                pacer_orchestrator.service_coordinator,
                pacer_orchestrator.progress_tracker,
                pacer_orchestrator.result_aggregator,
                pacer_orchestrator.job_scheduler,
                pacer_orchestrator.workflow_manager
            ]
            
            for component in components:
                component.initialize = AsyncMock()
            
            pacer_orchestrator.metrics_collector.record_counter = AsyncMock()
            
            await pacer_orchestrator._initialize_service()
            
            # Verify all components were initialized
            for component in components:
                component.initialize.assert_called_once()
            
            # Verify metrics were recorded
            pacer_orchestrator.metrics_collector.record_counter.assert_called_once()

        @pytest.mark.asyncio
        async def test_initialize_service_failure(self, pacer_orchestrator):
            """Test service initialization failure handling."""
            # Make configuration manager fail
            pacer_orchestrator.configuration_manager.initialize = AsyncMock(
                side_effect=Exception("Config initialization failed")
            )
            pacer_orchestrator.error_recovery_manager.handle_error = AsyncMock()
            
            with pytest.raises(PacerServiceError, match="Failed to initialize PACER orchestrator"):
                await pacer_orchestrator._initialize_service()
            
            # Verify error was handled
            pacer_orchestrator.error_recovery_manager.handle_error.assert_called_once()

    class TestExecuteAction:
        """Test _execute_action method routing."""

        @pytest.mark.asyncio
        async def test_execute_action_workflow_actions(self, pacer_orchestrator):
            """Test execute_action routing to workflow manager."""
            data = {
                'action': 'process_courts',
                'court_ids': ['nys', 'cand'],
                'iso_date': '********'
            }
            
            with patch.object(pacer_orchestrator.workflow_manager, 'perform_action') as mock_workflow:
                mock_workflow.return_value = {'status': 'success', 'courts_processed': 2}
                
                result = await pacer_orchestrator._execute_action(data)
                
                assert result['status'] == 'success'
                assert result['courts_processed'] == 2
                mock_workflow.assert_called_once_with(data)

        @pytest.mark.asyncio
        async def test_execute_action_resource_actions(self, pacer_orchestrator):
            """Test execute_action routing to resource manager."""
            data = {
                'action': 'ensure_s3',
                'bucket_name': 'test-bucket'
            }
            
            with patch.object(pacer_orchestrator.resource_manager, 'perform_action') as mock_resource:
                mock_resource.return_value = {'s3_initialized': True}
                
                result = await pacer_orchestrator._execute_action(data)
                
                assert result['s3_initialized'] is True
                mock_resource.assert_called_once_with(data)

        @pytest.mark.asyncio
        async def test_execute_action_job_scheduler_actions(self, pacer_orchestrator, sample_pacer_job):
            """Test execute_action routing to job scheduler."""
            data = {
                'action': 'schedule_job',
                'job': sample_pacer_job
            }
            
            with patch.object(pacer_orchestrator.job_scheduler, 'perform_action') as mock_scheduler:
                mock_scheduler.return_value = {'job_id': 'test-job-001', 'scheduled': True}
                
                result = await pacer_orchestrator._execute_action(data)
                
                assert result['job_id'] == 'test-job-001'
                assert result['scheduled'] is True
                mock_scheduler.assert_called_once_with(data)

        @pytest.mark.asyncio
        async def test_execute_action_invalid_data_type(self, pacer_orchestrator):
            """Test execute_action with invalid data type."""
            data = "invalid_string_data"
            
            with pytest.raises(ServiceError, match="Orchestrator action data must be a dictionary"):
                await pacer_orchestrator._execute_action(data)

        @pytest.mark.asyncio
        async def test_execute_action_unknown_action(self, pacer_orchestrator):
            """Test execute_action with unknown action."""
            data = {
                'action': 'unknown_action',
                'data': 'test'
            }
            
            with pytest.raises(ServiceError, match="Unknown orchestrator action: unknown_action"):
                await pacer_orchestrator._execute_action(data)

    class TestProcessCourts:
        """Test process_courts workflow method."""

        @pytest.mark.asyncio
        async def test_process_courts_success(self, pacer_orchestrator, mock_browser_context, workflow_config):
            """Test successful court processing workflow."""
            court_ids = ['nys', 'cand']
            iso_date = '********'
            
            # Mock all orchestration components
            with patch.object(pacer_orchestrator.progress_tracker, 'start_session') as mock_start_progress, \
                 patch.object(pacer_orchestrator.result_aggregator, 'create_result_set') as mock_create_results, \
                 patch.object(pacer_orchestrator.metrics_collector, 'start_timer') as mock_start_timer, \
                 patch.object(pacer_orchestrator, '_track_browser_context') as mock_track, \
                 patch.object(pacer_orchestrator.workflow_manager, 'process_courts') as mock_process, \
                 patch.object(pacer_orchestrator.progress_tracker, 'complete_session') as mock_complete_progress, \
                 patch.object(pacer_orchestrator.result_aggregator, 'add_result') as mock_add_result, \
                 patch.object(pacer_orchestrator.metrics_collector, 'record_court_processed') as mock_record_metric, \
                 patch.object(pacer_orchestrator.metrics_collector, 'stop_timer') as mock_stop_timer, \
                 patch.object(pacer_orchestrator, '_untrack_browser_context') as mock_untrack:
                
                # Setup mocks
                mock_start_progress.return_value = 'progress-session-001'
                mock_create_results.return_value = 'result-set-001'
                mock_start_timer.return_value = 'timer-001'
                mock_process.return_value = {
                    'successful_courts': 2,
                    'failed_courts': 0,
                    'courts_processed': {
                        'nys': {'status': 'success', 'cases_found': 10},
                        'cand': {'status': 'success', 'cases_found': 8}
                    }
                }
                
                result = await pacer_orchestrator.process_courts(
                    mock_browser_context, iso_date, workflow_config, court_ids
                )
                
                assert result['successful_courts'] == 2
                assert result['failed_courts'] == 0
                assert 'courts_processed' in result
                
                # Verify orchestration flow
                mock_start_progress.assert_called_once()
                mock_create_results.assert_called_once()
                mock_track.assert_called_once_with(mock_browser_context)
                mock_process.assert_called_once_with(mock_browser_context, iso_date, workflow_config, court_ids)
                mock_complete_progress.assert_called_once()
                mock_add_result.assert_called()
                mock_stop_timer.assert_called_once()
                mock_untrack.assert_called_once_with(mock_browser_context)

        @pytest.mark.asyncio
        async def test_process_courts_failure_handling(self, pacer_orchestrator, mock_browser_context, workflow_config):
            """Test court processing with failure handling."""
            court_ids = ['nys']
            iso_date = '********'
            
            with patch.object(pacer_orchestrator.progress_tracker, 'start_session') as mock_start_progress, \
                 patch.object(pacer_orchestrator.result_aggregator, 'create_result_set') as mock_create_results, \
                 patch.object(pacer_orchestrator.metrics_collector, 'start_timer') as mock_start_timer, \
                 patch.object(pacer_orchestrator, '_track_browser_context') as mock_track, \
                 patch.object(pacer_orchestrator.workflow_manager, 'process_courts') as mock_process, \
                 patch.object(pacer_orchestrator.error_recovery_manager, 'handle_error') as mock_error, \
                 patch.object(pacer_orchestrator.progress_tracker, 'fail_session') as mock_fail_progress, \
                 patch.object(pacer_orchestrator.result_aggregator, 'add_result') as mock_add_result, \
                 patch.object(pacer_orchestrator.metrics_collector, 'record_error') as mock_record_error, \
                 patch.object(pacer_orchestrator.metrics_collector, 'stop_timer') as mock_stop_timer, \
                 patch.object(pacer_orchestrator, '_untrack_browser_context') as mock_untrack:
                
                # Setup mocks
                mock_start_progress.return_value = 'progress-session-001'
                mock_create_results.return_value = 'result-set-001'
                mock_start_timer.return_value = 'timer-001'
                mock_process.side_effect = Exception("Court processing failed")
                mock_error.return_value = {'should_retry': False, 'error_handled': True}
                
                with pytest.raises(Exception, match="Court processing failed"):
                    await pacer_orchestrator.process_courts(
                        mock_browser_context, iso_date, workflow_config, court_ids
                    )
                
                # Verify error handling flow
                mock_error.assert_called_once()
                mock_fail_progress.assert_called_once()
                mock_add_result.assert_called()
                mock_record_error.assert_called_once()
                mock_stop_timer.assert_called_once()
                mock_untrack.assert_called_once()

    class TestProcessSingleDocket:
        """Test process_single_docket workflow method."""

        @pytest.mark.asyncio
        async def test_process_single_docket_success(self, pacer_orchestrator, mock_browser_context, workflow_config):
            """Test successful single docket processing."""
            court_id = 'nys'
            docket_num = '1:23-cv-00001'
            iso_date = '********'
            
            with patch.object(pacer_orchestrator.progress_tracker, 'start_session') as mock_start_progress, \
                 patch.object(pacer_orchestrator.result_aggregator, 'create_result_set') as mock_create_results, \
                 patch.object(pacer_orchestrator.metrics_collector, 'start_timer') as mock_start_timer, \
                 patch.object(pacer_orchestrator, '_track_browser_context') as mock_track, \
                 patch.object(pacer_orchestrator.workflow_manager, 'process_single_docket') as mock_process, \
                 patch.object(pacer_orchestrator.progress_tracker, 'update_progress') as mock_update_progress, \
                 patch.object(pacer_orchestrator.progress_tracker, 'complete_session') as mock_complete_progress, \
                 patch.object(pacer_orchestrator.metrics_collector, 'record_docket_processed') as mock_record_metric, \
                 patch.object(pacer_orchestrator.metrics_collector, 'stop_timer') as mock_stop_timer, \
                 patch.object(pacer_orchestrator, '_untrack_browser_context') as mock_untrack:
                
                # Setup mocks
                mock_start_progress.return_value = 'progress-session-001'
                mock_create_results.return_value = 'result-set-001'
                mock_start_timer.return_value = 'timer-001'
                mock_process.return_value = {
                    'court_id': court_id,
                    'docket_num': docket_num,
                    'status': 'success',
                    'cases_found': 1,
                    'documents_processed': 5
                }
                
                result = await pacer_orchestrator.process_single_docket(
                    court_id, docket_num, mock_browser_context, iso_date, workflow_config
                )
                
                assert result['court_id'] == court_id
                assert result['docket_num'] == docket_num
                assert result['status'] == 'success'
                assert result['cases_found'] == 1
                
                # Verify workflow orchestration
                mock_process.assert_called_once_with(court_id, docket_num, mock_browser_context, iso_date, workflow_config)
                mock_update_progress.assert_called_once()
                mock_complete_progress.assert_called_once()
                mock_record_metric.assert_called_once_with(court_id, docket_num, True)

        @pytest.mark.asyncio
        async def test_process_single_docket_failure(self, pacer_orchestrator, mock_browser_context, workflow_config):
            """Test single docket processing failure handling."""
            court_id = 'nys'
            docket_num = '1:23-cv-00001'
            iso_date = '********'
            
            with patch.object(pacer_orchestrator.progress_tracker, 'start_session') as mock_start_progress, \
                 patch.object(pacer_orchestrator.result_aggregator, 'create_result_set') as mock_create_results, \
                 patch.object(pacer_orchestrator.metrics_collector, 'start_timer') as mock_start_timer, \
                 patch.object(pacer_orchestrator, '_track_browser_context') as mock_track, \
                 patch.object(pacer_orchestrator.workflow_manager, 'process_single_docket') as mock_process, \
                 patch.object(pacer_orchestrator.error_recovery_manager, 'handle_error') as mock_error, \
                 patch.object(pacer_orchestrator.progress_tracker, 'fail_session') as mock_fail_progress, \
                 patch.object(pacer_orchestrator.metrics_collector, 'record_docket_processed') as mock_record_metric, \
                 patch.object(pacer_orchestrator.metrics_collector, 'record_error') as mock_record_error, \
                 patch.object(pacer_orchestrator.metrics_collector, 'stop_timer') as mock_stop_timer, \
                 patch.object(pacer_orchestrator, '_untrack_browser_context') as mock_untrack:
                
                # Setup mocks
                mock_start_progress.return_value = 'progress-session-001'
                mock_create_results.return_value = 'result-set-001'
                mock_start_timer.return_value = 'timer-001'
                mock_process.side_effect = Exception("Docket processing failed")
                mock_error.return_value = {'should_retry': False, 'error_handled': True}
                
                with pytest.raises(Exception, match="Docket processing failed"):
                    await pacer_orchestrator.process_single_docket(
                        court_id, docket_num, mock_browser_context, iso_date, workflow_config
                    )
                
                # Verify error handling
                mock_error.assert_called_once()
                mock_fail_progress.assert_called_once()
                mock_record_metric.assert_called_once_with(court_id, docket_num, False)
                mock_record_error.assert_called_once()

    class TestBackwardCompatibility:
        """Test backward compatibility methods."""

        def test_get_loaded_configuration(self, pacer_orchestrator):
            """Test get_loaded_configuration backward compatibility."""
            expected_config = {
                'iso_date': '********',
                'court_ids': ['nys', 'cand'],
                'timeout': 30000
            }
            
            with patch.object(pacer_orchestrator.configuration_manager, 'get_loaded_configuration') as mock_config:
                mock_config.return_value = expected_config
                
                result = pacer_orchestrator.get_loaded_configuration()
                
                assert result == expected_config
                mock_config.assert_called_once()

        def test_get_s3_storage(self, pacer_orchestrator):
            """Test get_s3_storage backward compatibility."""
            mock_s3 = AsyncMock()
            
            with patch.object(pacer_orchestrator.resource_manager, 'get_s3_storage') as mock_s3_getter:
                mock_s3_getter.return_value = mock_s3
                
                result = pacer_orchestrator.get_s3_storage()
                
                assert result == mock_s3
                mock_s3_getter.assert_called_once()

        @pytest.mark.asyncio
        async def test_ensure_s3_initialized(self, pacer_orchestrator):
            """Test ensure_s3_initialized backward compatibility."""
            with patch.object(pacer_orchestrator.resource_manager, 'ensure_s3_initialized') as mock_ensure:
                mock_ensure.return_value = True
                
                result = await pacer_orchestrator.ensure_s3_initialized()
                
                assert result is True
                mock_ensure.assert_called_once()

        @pytest.mark.asyncio
        async def test_track_untrack_browser_context(self, pacer_orchestrator, mock_browser_context):
            """Test browser context tracking backward compatibility."""
            with patch.object(pacer_orchestrator.resource_manager, 'track_browser_context') as mock_track, \
                 patch.object(pacer_orchestrator.resource_manager, 'untrack_browser_context') as mock_untrack:
                
                await pacer_orchestrator._track_browser_context(mock_browser_context)
                await pacer_orchestrator._untrack_browser_context(mock_browser_context)
                
                mock_track.assert_called_once_with(mock_browser_context)
                mock_untrack.assert_called_once_with(mock_browser_context)

        @pytest.mark.asyncio
        async def test_ensure_deepseek_service_initialized(self, pacer_orchestrator):
            """Test ensure_deepseek_service_initialized backward compatibility."""
            with patch.object(pacer_orchestrator.service_coordinator, 'ensure_deepseek_service_initialized') as mock_ensure:
                mock_ensure.return_value = True
                
                result = await pacer_orchestrator._ensure_deepseek_service_initialized()
                
                assert result is True
                mock_ensure.assert_called_once()

    class TestStatusAndMonitoring:
        """Test status and monitoring methods."""

        @pytest.mark.asyncio
        async def test_get_orchestrator_status(self, pacer_orchestrator):
            """Test get_orchestrator_status method."""
            # Mock all component status methods
            with patch.object(pacer_orchestrator.configuration_manager, 'get_loaded_configuration') as mock_config, \
                 patch.object(pacer_orchestrator.service_coordinator, 'check_service_health') as mock_health, \
                 patch.object(pacer_orchestrator.resource_manager, 'get_resource_status') as mock_resources, \
                 patch.object(pacer_orchestrator.workflow_manager, 'get_workflow_status') as mock_workflow, \
                 patch.object(pacer_orchestrator.job_scheduler, 'get_queue_status') as mock_jobs, \
                 patch.object(pacer_orchestrator.progress_tracker, 'get_all_sessions') as mock_progress, \
                 patch.object(pacer_orchestrator.error_recovery_manager, 'get_error_statistics') as mock_errors, \
                 patch.object(pacer_orchestrator.result_aggregator, 'get_results_summary') as mock_results, \
                 patch.object(pacer_orchestrator.metrics_collector, 'get_metrics_summary') as mock_metrics:
                
                # Setup mock returns
                mock_config.return_value = {'iso_date': '********'}
                mock_health.return_value = {'all_healthy': True}
                mock_resources.return_value = {'s3_available': True, 'browser_contexts': 0}
                mock_workflow.return_value = {'active_workflows': 0}
                mock_jobs.return_value = {'pending_jobs': 0, 'running_jobs': 0}
                mock_progress.return_value = []
                mock_errors.return_value = {'total_errors': 0}
                mock_results.return_value = {'total_results': 0}
                mock_metrics.return_value = {'uptime_seconds': 3600}
                
                pacer_orchestrator._initialized = True
                
                status = await pacer_orchestrator.get_orchestrator_status()
                
                assert status['orchestrator']['initialized'] is True
                assert status['orchestrator']['status'] == 'healthy'
                assert 'components' in status
                assert 'timestamp' in status
                
                # Verify all component status methods were called
                mock_config.assert_called_once()
                mock_health.assert_called_once()
                mock_resources.assert_called_once()

        def test_set_court_specific_dockets(self, pacer_orchestrator):
            """Test set_court_specific_dockets method."""
            court_dockets = {
                'nys': [
                    {'docket_num': '1:23-cv-00001', 'case_name': 'Test Case 1'},
                    {'docket_num': '1:23-cv-00002', 'case_name': 'Test Case 2'}
                ],
                'cand': [
                    {'docket_num': '3:23-cv-00001', 'case_name': 'Test Case 3'}
                ]
            }
            
            with patch.object(pacer_orchestrator.workflow_manager, 'set_court_specific_dockets') as mock_set:
                pacer_orchestrator.set_court_specific_dockets(court_dockets)
                
                assert pacer_orchestrator._court_specific_dockets == court_dockets
                mock_set.assert_called_once_with(court_dockets)

    class TestCleanup:
        """Test cleanup functionality."""

        @pytest.mark.asyncio
        async def test_cleanup_service(self, pacer_orchestrator):
            """Test _cleanup_service method."""
            # Mock all component cleanup methods
            components = [
                pacer_orchestrator.workflow_manager,
                pacer_orchestrator.job_scheduler,
                pacer_orchestrator.result_aggregator,
                pacer_orchestrator.progress_tracker,
                pacer_orchestrator.service_coordinator,
                pacer_orchestrator.resource_manager,
                pacer_orchestrator.error_recovery_manager,
                pacer_orchestrator.metrics_collector,
                pacer_orchestrator.configuration_manager
            ]
            
            for component in components:
                component.cleanup = AsyncMock()
            
            await pacer_orchestrator._cleanup_service()
            
            # Verify all components were cleaned up
            for component in components:
                component.cleanup.assert_called_once()
            
            # Verify local state was cleared
            assert len(pacer_orchestrator._current_court_sessions) == 0
            assert len(pacer_orchestrator._court_specific_dockets) == 0


class TestPacerOrchestratorIntegration:
    """Integration tests for PacerOrchestrator."""

    @pytest.mark.asyncio
    async def test_full_orchestration_workflow(self, test_container, mock_browser_context):
        """Test complete orchestration workflow with DI container."""
        # Create orchestrator with container services
        config = {
            'iso_date': '********',
            'court_ids': ['nys'],
            'timeout': 30000
        }
        
        orchestrator = PacerOrchestrator(
            logger=test_container.pacer.mock_config().logger,
            config=config,
            config_service=test_container.pacer.orchestration.configuration_service(),
            service_factory=test_container.pacer.orchestration.service_factory(),
            court_processing_service=test_container.pacer.orchestration.court_processing_service(),
            deepseek_service=test_container.deepseek_service()
        )
        
        # Mock all components for integration test
        for component in [
            orchestrator.configuration_manager,
            orchestrator.metrics_collector,
            orchestrator.error_recovery_manager,
            orchestrator.resource_manager,
            orchestrator.service_coordinator,
            orchestrator.progress_tracker,
            orchestrator.result_aggregator,
            orchestrator.job_scheduler,
            orchestrator.workflow_manager
        ]:
            component.initialize = AsyncMock()
        
        orchestrator.metrics_collector.record_counter = AsyncMock()
        
        # Test initialization
        await orchestrator._initialize_service()
        
        # Test action execution
        with patch.object(orchestrator.workflow_manager, 'perform_action') as mock_workflow:
            mock_workflow.return_value = {'status': 'success', 'courts_processed': 1}
            
            result = await orchestrator._execute_action({
                'action': 'process_courts',
                'court_ids': ['nys'],
                'iso_date': '********'
            })
            
            assert result['status'] == 'success'
            assert result['courts_processed'] == 1

    @pytest.mark.asyncio
    async def test_async_context_manager(self, pacer_orchestrator):
        """Test async context manager functionality."""
        # Mock initialization and cleanup
        pacer_orchestrator._initialize_service = AsyncMock()
        pacer_orchestrator._cleanup_service = AsyncMock()
        
        async with pacer_orchestrator as orchestrator:
            assert orchestrator == pacer_orchestrator
            pacer_orchestrator._initialize_service.assert_called_once()
        
        pacer_orchestrator._cleanup_service.assert_called_once()

    def test_di_container_component_wiring(self, test_container):
        """Test that DI container properly wires all orchestrator components."""
        config = {
            'iso_date': '********',
            'data_path': '/tmp/test_data'
        }
        
        orchestrator = PacerOrchestrator(
            logger=test_container.pacer.mock_config().logger,
            config=config,
            config_service=test_container.pacer.orchestration.configuration_service(),
            service_factory=test_container.pacer.orchestration.service_factory(),
            court_processing_service=test_container.pacer.orchestration.court_processing_service(),
            deepseek_service=test_container.deepseek_service()
        )
        
        # Verify all components are properly wired
        assert orchestrator.configuration_manager is not None
        assert orchestrator.service_coordinator is not None
        assert orchestrator.resource_manager is not None
        assert orchestrator.workflow_manager is not None
        assert orchestrator.job_scheduler is not None
        assert orchestrator.progress_tracker is not None
        assert orchestrator.error_recovery_manager is not None
        assert orchestrator.result_aggregator is not None
        assert orchestrator.metrics_collector is not None
        
        # Verify configuration is properly passed
        assert hasattr(orchestrator, 'config')
        assert orchestrator.config['iso_date'] == '********'


class TestLegacyFileOperations:
    """Test legacy file operations for backward compatibility."""

    @pytest.mark.asyncio
    async def test_load_docket_report_log_success(self, pacer_orchestrator):
        """Test loading docket report log file."""
        iso_date = '********'
        court_id = 'nys'
        
        mock_cases = [
            {'docket_num': '1:23-cv-00001', 'case_name': 'Test Case 1'},
            {'docket_num': '1:23-cv-00002', 'case_name': 'Test Case 2'}
        ]
        
        with patch.object(pacer_orchestrator.configuration_manager, 'get_loaded_configuration') as mock_config, \
             patch('pathlib.Path.exists') as mock_exists, \
             patch('builtins.open', mock_open(read_data='{"cases": ' + str(mock_cases).replace("'", '"') + '}')):
            
            mock_config.return_value = {'data_path': '/tmp/test_data'}
            mock_exists.return_value = True
            
            result = await pacer_orchestrator.load_docket_report_log(iso_date, court_id)
            
            assert result == mock_cases
            mock_config.assert_called_once()

    @pytest.mark.asyncio
    async def test_load_docket_report_log_file_not_found(self, pacer_orchestrator):
        """Test loading docket report log file when file doesn't exist."""
        iso_date = '********'
        court_id = 'nys'
        
        with patch.object(pacer_orchestrator.configuration_manager, 'get_loaded_configuration') as mock_config, \
             patch('pathlib.Path.exists') as mock_exists:
            
            mock_config.return_value = {'data_path': '/tmp/test_data'}
            mock_exists.return_value = False
            
            result = await pacer_orchestrator.load_docket_report_log(iso_date, court_id)
            
            assert result is None

    @pytest.mark.asyncio
    async def test_check_docket_exists_locally(self, pacer_orchestrator):
        """Test checking if docket exists locally."""
        court_id = 'nys'
        docket_num = '1:23-cv-00001'
        
        with patch.object(pacer_orchestrator.configuration_manager, 'get_loaded_configuration') as mock_config, \
             patch('pathlib.Path.exists') as mock_exists, \
             patch('pathlib.Path.glob') as mock_glob, \
             patch.object(pacer_orchestrator, '_extract_clean_docket_pattern') as mock_pattern:
            
            mock_config.return_value = {
                'data_path': '/tmp/test_data',
                'iso_dates': ['********']
            }
            mock_pattern.return_value = '23_00001'
            mock_glob.return_value = [Path('/tmp/test_data/********/dockets/23_00001.json')]
            mock_exists.return_value = True
            
            result = await pacer_orchestrator._check_docket_exists_locally(court_id, docket_num)
            
            assert result is True
            mock_pattern.assert_called_once_with(docket_num)

    def test_extract_clean_docket_pattern(self, pacer_orchestrator):
        """Test extracting clean docket pattern."""
        with patch('src.utils.docket_utils.normalize_docket_number') as mock_normalize:
            mock_normalize.return_value = '1:23-cv-00001'
            
            result = pacer_orchestrator._extract_clean_docket_pattern('1:23-cv-00001')
            
            assert '23' in result and '00001' in result
            mock_normalize.assert_called_once_with('1:23-cv-00001')


def mock_open(read_data):
    """Helper function to create mock_open with read_data."""
    from unittest.mock import mock_open as original_mock_open
    return original_mock_open(read_data=read_data)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])