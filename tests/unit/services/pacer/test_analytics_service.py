import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, date
from pathlib import Path
import json
import pandas as pd
from src.services.pacer.analytics_service import PacerAnalyticsService
from src.infrastructure.protocols.exceptions import PacerServiceError


@pytest.fixture
def analytics_service(test_container):
    """Get analytics service from container."""
    return test_container.pacer.data.analytics_service()


@pytest.fixture
def mock_config(test_container):
    """Get mock config from container."""
    return test_container.pacer.mock_config()


@pytest.fixture
def mock_repository(test_container):
    """Get mock repository from container."""
    return test_container.pacer.mock_repository()


@pytest.fixture
def sample_pacer_records():
    """Sample PACER records for testing."""
    return [
        {
            'CourtId': 'njd',
            'DocketNum': '3:24-cv-12345',
            'FilingDate': '20240115',
            'MdlNum': '2738',
            'LawFirm': 'Smith & Associates',
            'Plaintiff': '<PERSON>',
            'Defendant': 'Company Inc.',
            'PdfCount': 5,
            'AddedOn': '20240115'
        },
        {
            'CourtId': 'nysd',
            'DocketNum': '1:24-cv-67890',
            'FilingDate': '20240115',
            'MdlNum': '2738',
            'LawFirm': 'Jones Law Firm',
            'Plaintiff': 'Jane Doe',
            'Defendant': 'Another Corp',
            'PdfCount': 3,
            'AddedOn': '20240115'
        }
    ]


class TestAnalyticsService:
    """Test cases for PacerAnalyticsService."""

    class TestInitialization:
        """Test service initialization."""

        def test_init_with_config(self, analytics_service, mock_config, test_container):
            """Test initialization with config."""
            service = analytics_service
            assert service.config is not None
            assert service.logger is not None

        def test_init_without_config(self, analytics_service, test_container):
            """Test initialization without config."""
            service = analytics_service
            assert service.logger is not None

    class TestMDLAnalytics:
        """Test MDL analytics methods."""

        @pytest.mark.asyncio
        async def test_get_mdl_count_in_date_range(self, analytics_service, mock_repository, sample_pacer_records, test_container):
            """Test MDL count in date range."""
            mock_repository.query_by_mdl_and_date_range.return_value = sample_pacer_records
            result = await analytics_service.get_mdl_count_in_date_range('2738', '20240101', '20240131')
            assert result == 2
            mock_repository.query_by_mdl_and_date_range.assert_called_once_with('2738', '20240101', '20240131')

        @pytest.mark.asyncio
        async def test_get_filings_by_court(self, analytics_service, mock_repository, sample_pacer_records, test_container):
            """Test filings by court."""
            mock_repository.query_by_mdl_and_date_range.return_value = sample_pacer_records
            result = await analytics_service.get_filings_by_court('2738', '20240101', '20240131')
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 2  # Two unique courts
            assert 'CourtId' in result.columns
            assert 'Count' in result.columns

        @pytest.mark.asyncio
        async def test_summarize_filings_by_law_firm_for_mdl(self, analytics_service, mock_repository, sample_pacer_records, test_container):
            """Test filings summary by law firm for MDL."""
            mock_repository.query_by_mdl_and_date_range.return_value = sample_pacer_records
            df, total_filings = await analytics_service.summarize_filings_by_law_firm_for_mdl('2738', '20240101', '20240131')
            assert isinstance(df, pd.DataFrame)
            assert total_filings == 2
            assert 'LawFirm' in df.columns
            assert 'FilingCount' in df.columns

    class TestDataFrameOperations:
        """Test DataFrame operations."""

        @pytest.mark.asyncio
        async def test_get_pacer_records_df(self, analytics_service, mock_repository, sample_pacer_records, test_container):
            """Test getting PACER records as DataFrame."""
            mock_repository.scan_all.return_value = sample_pacer_records
            result = await analytics_service.get_pacer_records_df()
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 2
            mock_repository.scan_all.assert_called_once()

        @pytest.mark.asyncio
        async def test_scan_to_dataframe(self, analytics_service, mock_repository, sample_pacer_records, test_container):
            """Test scan to DataFrame."""
            mock_repository.scan_all.return_value = sample_pacer_records
            result = await analytics_service.scan_to_dataframe()
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 2
            mock_repository.scan_all.assert_called_once()

    class TestDuplicationAnalysis:
        """Test duplication analysis."""

        @pytest.mark.asyncio
        async def test_count_identical_court_docket_combinations(self, analytics_service, mock_repository, test_container):
            """Test counting identical court-docket combinations."""
            # Mock data with duplicates
            mock_records = [
                {'CourtId': 'njd', 'DocketNum': '3:24-cv-12345'},
                {'CourtId': 'njd', 'DocketNum': '3:24-cv-12345'},  # Duplicate
                {'CourtId': 'nysd', 'DocketNum': '1:24-cv-67890'},
            ]
            mock_repository.scan_all.return_value = mock_records
            result = await analytics_service.count_identical_court_docket_combinations()
            # Should only return duplicates
            assert len(result) == 1
            assert ('njd', '3:24-cv-12345') in result
            assert result[('njd', '3:24-cv-12345')] == 2

    class TestMDLSummaries:
        """Test MDL summary methods."""

        @pytest.mark.asyncio
        async def test_get_mdl_summary(self, analytics_service, mock_repository, sample_pacer_records, test_container):
            """Test MDL summary."""
            mock_repository.query_by_filing_date.return_value = sample_pacer_records
            result = await analytics_service.get_mdl_summary('20240115')
            assert isinstance(result, pd.DataFrame)
            assert 'MDL' in result.columns
            assert 'TotalFilings' in result.columns
            assert 'UniqueCourtCount' in result.columns
            assert 'TotalPDFs' in result.columns

        @pytest.mark.asyncio
        async def test_get_mdl_summary2(self, analytics_service, mock_repository, sample_pacer_records, test_container):
            """Test MDL summary version 2."""
            mock_repository.query_by_filing_date.return_value = sample_pacer_records
            result = await analytics_service.get_mdl_summary2('20240115')
            assert isinstance(result, pd.DataFrame)
            assert 'MDL' in result.columns
            assert 'Filings' in result.columns
            assert 'UniqueCourts' in result.columns
            assert 'UniqueLawFirms' in result.columns
            assert 'AvgPDFsPerFiling' in result.columns

    class TestLawFirmExtraction:
        """Test law firm extraction methods."""

        @pytest.mark.asyncio
        async def test_get_unique_law_firms(self, analytics_service, mock_repository, test_container):
            """Test unique law firm extraction."""
            mock_records = [
                {'LawFirm': 'Smith & Associates', 'PlaintiffAttorneys': 'Jones Law Firm'},
                {'LawFirm': 'Smith & Associates', 'Attorneys': ['Brown & Co.', 'Green Law']},
                {'LawFirms': ['Wilson LLC', 'Davis Firm']}
            ]
            mock_repository.scan_all.return_value = mock_records
            result = await analytics_service.get_unique_law_firms()
            assert isinstance(result, list)
            assert 'Smith & Associates' in result
            assert 'Jones Law Firm' in result
            # Should be sorted
            assert result == sorted(result)

    class TestExecuteAction:
        """Test execute action method."""

        @pytest.mark.asyncio
        async def test_execute_action_get_mdl_count(self, analytics_service, mock_repository, sample_pacer_records, test_container):
            """Test execute action for MDL count."""
            mock_repository.query_by_mdl_and_date_range.return_value = sample_pacer_records
            data = {
                'action': 'get_mdl_count_in_date_range',
                'mdl_num': '2738',
                'start_date': '20240101',
                'end_date': '20240131'
            }
            result = await analytics_service._execute_action(data)
            assert result == 2

        @pytest.mark.asyncio
        async def test_execute_action_invalid(self, analytics_service, test_container):
            """Test execute action with invalid action."""
            with pytest.raises(PacerServiceError):
                await analytics_service._execute_action({'action': 'invalid_action'})
            
            with pytest.raises(PacerServiceError):
                await analytics_service._execute_action('invalid_data')


if __name__ == '__main__':
    pytest.main([__file__, '-v'])