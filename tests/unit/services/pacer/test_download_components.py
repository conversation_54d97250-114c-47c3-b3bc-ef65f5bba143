"""
Unit tests for PACER download components.

Tests cover:
- DownloadOrchestrator (main facade)
- DownloadSessionManager
- DownloadQueueManager
- DownloadProgressTracker
- DownloadErrorHandler
- DownloadValidator
- DownloadFileProcessor
- DownloadMetricsCollector

All tests use DI container injection following existing patterns.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from src.pacer.components.download.managers.download_orchestrator import DownloadOrchestrator
from src.infrastructure.protocols.exceptions import PacerServiceError
from playwright.async_api import Page


@pytest.fixture
def download_orchestrator(test_container):
    """Get download orchestrator with DI container injection."""
    # Create minimal config for download orchestrator
    config = {
        'iso_date': '20240101',
        'court_id': 'nys',
        'timeout': 30000,
        'headless': True,
        'html_only': False
    }
    
    # Mock required services
    file_operations_service = test_container.pacer.verification.file_operations_service()
    navigation_service = test_container.pacer.browser.navigation_service_factory_callable()
    pacer_repository = test_container.pacer.pacer_repository()
    
    return DownloadOrchestrator(
        logger=test_container.pacer.mock_config().logger,
        config=config,
        court_id='nys',
        file_operations_service=file_operations_service,
        navigation_service=navigation_service,
        pacer_repository=pacer_repository
    )


@pytest.fixture
def mock_page():
    """Mock page for testing."""
    page = AsyncMock(spec=Page)
    page.goto = AsyncMock()
    page.wait_for_load_state = AsyncMock()
    page.close = AsyncMock()
    page.url = "https://test.pacer.gov"
    return page


@pytest.fixture
def sample_case_details():
    """Sample case details for testing."""
    return {
        'court_id': 'nys',
        'docket_num': '1:23-cv-00001',
        'case_name': 'Test Case v. Defendant',
        'filed_date': '2023-01-15',
        'nature_of_suit': '190',
        'judge': 'Test Judge',
        'status': 'Open',
        'demand': '$50,000',
        'jury_demand': 'Yes',
        'has_documents': True,
        'document_count': 5
    }


@pytest.fixture
def download_config():
    """Configuration for download tests."""
    return {
        'iso_date': '20240101',
        'court_id': 'nys',
        'timeout': 30000,
        'headless': True,
        'html_only': False,
        'download_directory': '/tmp/test_downloads',
        'stability_config': {
            'timeout_s': 45,
            'check_interval_s': 0.5,
            'required_duration_s': 2.0
        }
    }


class TestDownloadOrchestrator:
    """Test cases for DownloadOrchestrator facade."""

    class TestInitialization:
        """Test orchestrator initialization."""

        def test_init_with_valid_config(self, test_container, download_config):
            """Test initialization with valid configuration."""
            orchestrator = DownloadOrchestrator(
                logger=test_container.pacer.mock_config().logger,
                config=download_config,
                court_id='nys'
            )
            
            assert orchestrator.court_id == 'nys'
            assert orchestrator.iso_date == '20240101'
            assert orchestrator.html_only is False
            assert orchestrator.is_downloaded is False
            assert orchestrator.stability_config['timeout_s'] == 45

        def test_init_without_iso_date_raises_error(self, test_container):
            """Test initialization without iso_date raises error."""
            config = {'court_id': 'nys'}
            
            with pytest.raises(PacerServiceError, match="No iso_date found in config"):
                DownloadOrchestrator(
                    logger=test_container.pacer.mock_config().logger,
                    config=config,
                    court_id='nys'
                )

        def test_init_creates_all_managers(self, download_orchestrator):
            """Test that initialization creates all required managers."""
            assert hasattr(download_orchestrator, 'session_manager')
            assert hasattr(download_orchestrator, 'queue_manager')
            assert hasattr(download_orchestrator, 'progress_tracker')
            assert hasattr(download_orchestrator, 'error_handler')
            assert hasattr(download_orchestrator, 'validator')
            assert hasattr(download_orchestrator, 'file_processor')
            assert hasattr(download_orchestrator, 'metrics_collector')
            assert hasattr(download_orchestrator, 'cache_manager')
            assert hasattr(download_orchestrator, 'retry_manager')

    class TestExecuteDownloadWorkflow:
        """Test execute_download_workflow method."""

        @pytest.mark.asyncio
        async def test_execute_download_workflow_success(self, download_orchestrator, sample_case_details, mock_page):
            """Test successful download workflow execution."""
            with patch.object(download_orchestrator.validator, 'validate_download_requirements') as mock_validate, \
                 patch.object(download_orchestrator.validator, 'should_skip_download') as mock_skip, \
                 patch.object(download_orchestrator.session_manager, 'setup_session_from_page') as mock_setup, \
                 patch.object(download_orchestrator, '_execute_full_download_workflow') as mock_execute:
                
                # Setup mocks
                mock_validate.return_value = True
                mock_skip.return_value = (False, "")
                mock_execute.return_value = {
                    **sample_case_details,
                    'is_downloaded': True,
                    '_processing_notes': 'Download completed successfully'
                }
                
                result = await download_orchestrator.execute_download_workflow(
                    sample_case_details, is_explicitly_requested=True, page=mock_page
                )
                
                assert result['docket_num'] == '1:23-cv-00001'
                assert result['is_downloaded'] is True
                assert '_processing_notes' in result
                
                # Verify method calls
                mock_validate.assert_called_once_with(sample_case_details)
                mock_skip.assert_called_once_with(sample_case_details)
                mock_setup.assert_called_once_with(mock_page)

        @pytest.mark.asyncio
        async def test_execute_download_workflow_validation_failure(self, download_orchestrator, sample_case_details):
            """Test download workflow with validation failure."""
            with patch.object(download_orchestrator.validator, 'validate_download_requirements') as mock_validate:
                mock_validate.return_value = False
                
                result = await download_orchestrator.execute_download_workflow(sample_case_details)
                
                assert result['_processing_error'] == 'Validation failed'
                mock_validate.assert_called_once_with(sample_case_details)

        @pytest.mark.asyncio
        async def test_execute_download_workflow_should_skip(self, download_orchestrator, sample_case_details):
            """Test download workflow when download should be skipped."""
            with patch.object(download_orchestrator.validator, 'validate_download_requirements') as mock_validate, \
                 patch.object(download_orchestrator.validator, 'should_skip_download') as mock_skip:
                
                mock_validate.return_value = True
                mock_skip.return_value = (True, "Case already processed")
                
                result = await download_orchestrator.execute_download_workflow(sample_case_details)
                
                assert result['_processing_notes'] == 'Case already processed'

        @pytest.mark.asyncio
        async def test_execute_download_workflow_html_only(self, download_orchestrator, sample_case_details):
            """Test download workflow in HTML-only mode."""
            download_orchestrator.html_only = True
            
            with patch.object(download_orchestrator.validator, 'validate_download_requirements') as mock_validate, \
                 patch.object(download_orchestrator.validator, 'should_skip_download') as mock_skip, \
                 patch.object(download_orchestrator, '_process_html_only_case') as mock_html:
                
                mock_validate.return_value = True
                mock_skip.return_value = (False, "")
                mock_html.return_value = {
                    **sample_case_details,
                    'html_only': True,
                    '_processing_notes': 'Processed in HTML-only mode'
                }
                
                result = await download_orchestrator.execute_download_workflow(sample_case_details)
                
                assert result['html_only'] is True
                assert result['_processing_notes'] == 'Processed in HTML-only mode'

        @pytest.mark.asyncio
        async def test_execute_download_workflow_exception_handling(self, download_orchestrator, sample_case_details):
            """Test download workflow exception handling."""
            with patch.object(download_orchestrator.validator, 'validate_download_requirements') as mock_validate, \
                 patch.object(download_orchestrator.error_handler, 'handle_download_failure') as mock_error:
                
                mock_validate.side_effect = Exception("Test exception")
                mock_error.return_value = {
                    **sample_case_details,
                    '_processing_error': 'Test exception',
                    'status': 'failed'
                }
                
                result = await download_orchestrator.execute_download_workflow(sample_case_details)
                
                assert result['status'] == 'failed'
                assert result['_processing_error'] == 'Test exception'
                mock_error.assert_called_once()

    class TestExecuteAction:
        """Test _execute_action method."""

        @pytest.mark.asyncio
        async def test_execute_action_download_workflow(self, download_orchestrator, sample_case_details, mock_page):
            """Test execute_action with download_workflow action."""
            data = {
                'action': 'download_workflow',
                'case_details': sample_case_details,
                'is_explicitly_requested': True,
                'page': mock_page
            }
            
            with patch.object(download_orchestrator, 'execute_download_workflow') as mock_workflow:
                mock_workflow.return_value = {'status': 'success'}
                
                result = await download_orchestrator._execute_action(data)
                
                assert result['status'] == 'success'
                mock_workflow.assert_called_once_with(sample_case_details, True, mock_page)

        @pytest.mark.asyncio
        async def test_execute_action_handle_pdf_download(self, download_orchestrator, sample_case_details, mock_page):
            """Test execute_action with handle_pdf_download action."""
            data = {
                'action': 'handle_pdf_download',
                'case_details': sample_case_details,
                'page': mock_page
            }
            
            with patch.object(download_orchestrator, 'handle_pdf_download_async') as mock_pdf:
                mock_pdf.return_value = True
                
                result = await download_orchestrator._execute_action(data)
                
                assert result is True
                mock_pdf.assert_called_once_with(sample_case_details, mock_page)

        @pytest.mark.asyncio
        async def test_execute_action_unknown_action(self, download_orchestrator, sample_case_details):
            """Test execute_action with unknown action."""
            data = {
                'action': 'unknown_action',
                'case_details': sample_case_details
            }
            
            with pytest.raises(PacerServiceError, match="Unknown action: unknown_action"):
                await download_orchestrator._execute_action(data)

    class TestSessionManagement:
        """Test session management methods."""

        def test_inject_navigator_from_page(self, download_orchestrator, mock_page):
            """Test inject_navigator_from_page method."""
            with patch.object(download_orchestrator.session_manager, 'inject_navigator_from_page') as mock_inject:
                mock_inject.return_value = {'navigator': 'mock_navigator'}
                
                result = download_orchestrator.inject_navigator_from_page(
                    mock_page, screenshot_dir='/tmp/screenshots', timeout_ms=30000
                )
                
                assert result['navigator'] == 'mock_navigator'
                mock_inject.assert_called_once_with(mock_page, '/tmp/screenshots', 30000)

        def test_reset_download_state(self, download_orchestrator):
            """Test reset_download_state method."""
            # Set some state
            download_orchestrator.is_downloaded = True
            download_orchestrator.current_page = mock_page
            
            with patch.object(download_orchestrator.session_manager, 'reset_session_state') as mock_reset:
                download_orchestrator.reset_download_state()
                
                assert download_orchestrator.is_downloaded is False
                assert download_orchestrator.current_page is None
                mock_reset.assert_called_once()

    class TestValidationMethods:
        """Test validation-related methods."""

        def test_validate_download_requirements(self, download_orchestrator, sample_case_details):
            """Test validate_download_requirements method."""
            with patch.object(download_orchestrator.validator, 'validate_download_requirements') as mock_validate:
                mock_validate.return_value = True
                
                result = download_orchestrator.validate_download_requirements(sample_case_details)
                
                assert result is True
                mock_validate.assert_called_once_with(sample_case_details)

        def test_should_skip_download(self, download_orchestrator, sample_case_details):
            """Test should_skip_download method."""
            with patch.object(download_orchestrator.validator, 'should_skip_download') as mock_skip:
                mock_skip.return_value = (True, "Already processed")
                
                should_skip, reason = download_orchestrator.should_skip_download(sample_case_details)
                
                assert should_skip is True
                assert reason == "Already processed"
                mock_skip.assert_called_once_with(sample_case_details)

    class TestFileProcessing:
        """Test file processing methods."""

        @pytest.mark.asyncio
        async def test_handle_pdf_download_async(self, download_orchestrator, sample_case_details, mock_page):
            """Test handle_pdf_download_async method."""
            with patch.object(download_orchestrator.file_processor, 'handle_pdf_download_async') as mock_pdf:
                mock_pdf.return_value = True
                
                result = await download_orchestrator.handle_pdf_download_async(sample_case_details, mock_page)
                
                assert result is True
                mock_pdf.assert_called_once_with(sample_case_details, mock_page)

    class TestMetricsAndSummary:
        """Test metrics and summary methods."""

        def test_get_download_summary(self, download_orchestrator, sample_case_details):
            """Test get_download_summary method."""
            expected_summary = {
                'docket_num': '1:23-cv-00001',
                'download_status': 'success',
                'files_downloaded': 5,
                'total_size_mb': 2.5
            }
            
            with patch.object(download_orchestrator.metrics_collector, 'get_download_summary') as mock_summary:
                mock_summary.return_value = expected_summary
                
                result = download_orchestrator.get_download_summary(sample_case_details)
                
                assert result == expected_summary
                mock_summary.assert_called_once_with(sample_case_details)


class TestDownloadOrchestratorIntegration:
    """Integration tests for DownloadOrchestrator."""

    @pytest.mark.asyncio
    async def test_full_download_workflow_integration(self, test_container, sample_case_details, mock_page):
        """Test complete download workflow integration with DI container."""
        # Create orchestrator with container-provided services
        config = {
            'iso_date': '20240101',
            'court_id': 'nys',
            'timeout': 30000,
            'html_only': False
        }
        
        file_operations_service = test_container.pacer.verification.file_operations_service()
        navigation_service = test_container.pacer.browser.navigation_service_factory_callable()
        
        orchestrator = DownloadOrchestrator(
            logger=test_container.pacer.mock_config().logger,
            config=config,
            court_id='nys',
            file_operations_service=file_operations_service,
            navigation_service=navigation_service
        )
        
        # Mock all manager operations for integration test
        with patch.object(orchestrator.validator, 'validate_download_requirements', return_value=True), \
             patch.object(orchestrator.validator, 'should_skip_download', return_value=(False, "")), \
             patch.object(orchestrator.session_manager, 'setup_session_from_page'), \
             patch.object(orchestrator, '_execute_full_download_workflow') as mock_execute:
            
            mock_execute.return_value = {
                **sample_case_details,
                'is_downloaded': True,
                'download_summary': {
                    'files_downloaded': 3,
                    'total_size_mb': 1.5
                }
            }
            
            result = await orchestrator.execute_download_workflow(
                sample_case_details, is_explicitly_requested=True, page=mock_page
            )
            
            assert result['docket_num'] == '1:23-cv-00001'
            assert result['is_downloaded'] is True
            assert 'download_summary' in result

    @pytest.mark.asyncio
    async def test_manager_coordination(self, download_orchestrator):
        """Test that all managers are properly coordinated."""
        # Test that all managers are initialized and available
        managers = [
            download_orchestrator.session_manager,
            download_orchestrator.queue_manager,
            download_orchestrator.progress_tracker,
            download_orchestrator.error_handler,
            download_orchestrator.validator,
            download_orchestrator.file_processor,
            download_orchestrator.metrics_collector,
            download_orchestrator.cache_manager,
            download_orchestrator.retry_manager
        ]
        
        for manager in managers:
            assert manager is not None
            assert hasattr(manager, 'logger')
            assert hasattr(manager, 'config')
            assert manager.court_id == 'nys'
            assert manager.iso_date == '20240101'

    def test_di_container_dependency_injection(self, test_container):
        """Test that DI container properly injects dependencies."""
        config = {
            'iso_date': '20240101',
            'court_id': 'nys'
        }
        
        # Get services from container
        file_ops = test_container.pacer.verification.file_operations_service()
        nav_service = test_container.pacer.browser.navigation_service_factory_callable()
        pacer_repo = test_container.pacer.pacer_repository()
        
        orchestrator = DownloadOrchestrator(
            logger=test_container.pacer.mock_config().logger,
            config=config,
            court_id='nys',
            file_operations_service=file_ops,
            navigation_service=nav_service,
            pacer_repository=pacer_repo
        )
        
        # Verify injected dependencies
        assert orchestrator.file_operations_service == file_ops
        assert orchestrator.navigation_service == nav_service
        assert orchestrator.pacer_repository == pacer_repo


class TestDownloadValidatorComponent:
    """Test download validator component specifically."""

    def test_validate_download_requirements_missing_fields(self, download_orchestrator):
        """Test validation with missing required fields."""
        incomplete_case = {
            'court_id': 'nys',
            # Missing docket_num and other required fields
        }
        
        with patch.object(download_orchestrator.validator, 'validate_download_requirements') as mock_validate:
            mock_validate.return_value = False
            
            result = download_orchestrator.validate_download_requirements(incomplete_case)
            
            assert result is False

    def test_should_skip_download_already_processed(self, download_orchestrator, sample_case_details):
        """Test skip logic for already processed cases."""
        with patch.object(download_orchestrator.validator, 'should_skip_download') as mock_skip:
            mock_skip.return_value = (True, "Case already exists in database")
            
            should_skip, reason = download_orchestrator.should_skip_download(sample_case_details)
            
            assert should_skip is True
            assert "already exists" in reason


class TestDownloadErrorHandling:
    """Test error handling in download components."""

    @pytest.mark.asyncio
    async def test_error_handler_integration(self, download_orchestrator, sample_case_details):
        """Test error handler integration with download workflow."""
        test_exception = Exception("Network timeout")
        
        with patch.object(download_orchestrator.error_handler, 'handle_download_failure') as mock_handler:
            mock_handler.return_value = {
                **sample_case_details,
                'status': 'failed',
                'error': 'Network timeout',
                'retry_suggested': True
            }
            
            result = await mock_handler(sample_case_details, test_exception)
            
            assert result['status'] == 'failed'
            assert result['error'] == 'Network timeout'
            assert result['retry_suggested'] is True


if __name__ == '__main__':
    pytest.main([__file__, '-v'])