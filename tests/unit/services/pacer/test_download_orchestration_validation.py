"""
Download Orchestration Validation Tests - HiveTester-1
Comprehensive testing for PACER download workflows and validation.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path
from datetime import datetime, timedelta
import tempfile
import shutil
import hashlib


@pytest.fixture
def mock_download_config():
    """Mock download configuration."""
    config = Mock()
    config.download_directory = "/tmp/test_downloads"
    config.max_concurrent_downloads = 5
    config.download_timeout = 30
    config.retry_attempts = 3
    config.retry_delay = 1.0
    config.max_file_size = 100 * 1024 * 1024  # 100MB
    config.allowed_extensions = [".pdf", ".doc", ".docx", ".txt"]
    config.verify_downloads = True
    return config


@pytest.fixture
def mock_document_queue():
    """Mock document download queue."""
    return [
        {
            "doc_id": "12345-1",
            "case_number": "1:23-cv-12345",
            "document_number": "1",
            "description": "Complaint",
            "url": "https://ecf.court.gov/doc1/12345-1",
            "expected_size": 1024 * 50,  # 50KB
            "priority": "high"
        },
        {
            "doc_id": "12345-2", 
            "case_number": "1:23-cv-12345",
            "document_number": "2",
            "description": "Answer",
            "url": "https://ecf.court.gov/doc1/12345-2",
            "expected_size": 1024 * 75,  # 75KB
            "priority": "medium"
        },
        {
            "doc_id": "12346-1",
            "case_number": "1:23-cv-12346", 
            "document_number": "1",
            "description": "Motion",
            "url": "https://ecf.court.gov/doc1/12346-1",
            "expected_size": 1024 * 100,  # 100KB
            "priority": "low"
        }
    ]


@pytest.fixture
def temp_download_directory():
    """Create temporary download directory."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


class TestDownloadOrchestrationValidation:
    """Comprehensive download orchestration and validation tests."""

    class TestDownloadQueueManagement:
        """Test download queue operations."""

        @pytest.mark.asyncio
        async def test_queue_initialization(self, test_container, mock_download_config):
            """Test download queue initialization."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock queue initialization
            download_service.initialize_queue = AsyncMock(return_value={
                "queue_size": 0,
                "max_concurrent": mock_download_config.max_concurrent_downloads,
                "status": "ready"
            })
            
            result = await download_service.initialize_queue(mock_download_config)
            
            assert result["status"] == "ready"
            assert result["max_concurrent"] == 5
            download_service.initialize_queue.assert_called_once()

        @pytest.mark.asyncio
        async def test_document_queue_addition(self, test_container, mock_document_queue):
            """Test adding documents to download queue."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock queue addition
            download_service.add_to_queue = AsyncMock(return_value={
                "added": len(mock_document_queue),
                "queue_size": len(mock_document_queue),
                "duplicates": 0
            })
            
            result = await download_service.add_to_queue(mock_document_queue)
            
            assert result["added"] == 3
            assert result["queue_size"] == 3
            assert result["duplicates"] == 0

        @pytest.mark.asyncio
        async def test_priority_queue_ordering(self, test_container, mock_document_queue):
            """Test priority-based queue ordering."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock priority sorting
            expected_order = ["high", "medium", "low"]
            sorted_queue = sorted(mock_document_queue, 
                                key=lambda x: {"high": 0, "medium": 1, "low": 2}[x["priority"]])
            
            download_service.sort_queue_by_priority = AsyncMock(return_value=sorted_queue)
            
            result = await download_service.sort_queue_by_priority(mock_document_queue)
            
            assert len(result) == 3
            assert result[0]["priority"] == "high"
            assert result[1]["priority"] == "medium"
            assert result[2]["priority"] == "low"

        @pytest.mark.asyncio
        async def test_duplicate_detection(self, test_container):
            """Test detection of duplicate documents in queue."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock documents with duplicates
            documents_with_duplicates = [
                {"doc_id": "12345-1", "url": "https://example.com/doc1"},
                {"doc_id": "12345-2", "url": "https://example.com/doc2"},
                {"doc_id": "12345-1", "url": "https://example.com/doc1"},  # Duplicate
                {"doc_id": "12345-3", "url": "https://example.com/doc3"}
            ]
            
            download_service.detect_duplicates = AsyncMock(return_value={
                "unique_documents": 3,
                "duplicates_found": 1,
                "duplicate_ids": ["12345-1"]
            })
            
            result = await download_service.detect_duplicates(documents_with_duplicates)
            
            assert result["unique_documents"] == 3
            assert result["duplicates_found"] == 1
            assert "12345-1" in result["duplicate_ids"]

    class TestDownloadExecution:
        """Test download execution workflows."""

        @pytest.mark.asyncio
        async def test_single_document_download(self, test_container, temp_download_directory):
            """Test downloading a single document."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            document = {
                "doc_id": "test-doc-1",
                "url": "https://example.com/test.pdf",
                "case_number": "1:23-cv-12345"
            }
            
            expected_path = Path(temp_download_directory) / "test-doc-1.pdf"
            
            download_service.download_document = AsyncMock(return_value={
                "success": True,
                "doc_id": "test-doc-1",
                "file_path": str(expected_path),
                "file_size": 1024 * 50,
                "download_time": 2.5,
                "checksum": "abc123def456"
            })
            
            result = await download_service.download_document(document, temp_download_directory)
            
            assert result["success"] is True
            assert result["doc_id"] == "test-doc-1"
            assert expected_path.name in result["file_path"]
            assert result["file_size"] > 0
            assert result["download_time"] > 0

        @pytest.mark.asyncio
        async def test_concurrent_downloads(self, test_container, mock_document_queue, temp_download_directory):
            """Test concurrent document downloads."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock concurrent download results
            download_results = [
                {
                    "success": True,
                    "doc_id": doc["doc_id"],
                    "file_path": f"{temp_download_directory}/{doc['doc_id']}.pdf",
                    "file_size": doc["expected_size"],
                    "download_time": 1.0 + (i * 0.5)
                }
                for i, doc in enumerate(mock_document_queue)
            ]
            
            async def mock_download_concurrent(documents, max_concurrent=5):
                # Simulate concurrent downloads with semaphore
                semaphore = asyncio.Semaphore(max_concurrent)
                
                async def download_one(doc, index):
                    async with semaphore:
                        await asyncio.sleep(0.1)  # Simulate download time
                        return download_results[index]
                
                tasks = [download_one(doc, i) for i, doc in enumerate(documents)]
                return await asyncio.gather(*tasks)
            
            download_service.download_concurrent = AsyncMock(side_effect=mock_download_concurrent)
            
            results = await download_service.download_concurrent(mock_document_queue, max_concurrent=3)
            
            assert len(results) == 3
            assert all(result["success"] for result in results)
            
            # Verify all document IDs are present
            result_doc_ids = {result["doc_id"] for result in results}
            expected_doc_ids = {doc["doc_id"] for doc in mock_document_queue}
            assert result_doc_ids == expected_doc_ids

        @pytest.mark.asyncio
        async def test_download_progress_tracking(self, test_container, mock_document_queue):
            """Test download progress tracking."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock progress tracking
            progress_updates = []
            
            async def mock_download_with_progress(documents):
                total_docs = len(documents)
                
                for i, doc in enumerate(documents):
                    progress = {
                        "current_doc": i + 1,
                        "total_docs": total_docs,
                        "percentage": ((i + 1) / total_docs) * 100,
                        "doc_id": doc["doc_id"],
                        "status": "downloading"
                    }
                    progress_updates.append(progress)
                    await asyncio.sleep(0.01)  # Simulate download time
                
                return {"completed": total_docs, "progress_updates": progress_updates}
            
            download_service.download_with_progress = AsyncMock(side_effect=mock_download_with_progress)
            
            result = await download_service.download_with_progress(mock_document_queue)
            
            assert result["completed"] == 3
            assert len(result["progress_updates"]) == 3
            
            # Verify progress percentages
            percentages = [update["percentage"] for update in result["progress_updates"]]
            assert percentages == [33.333333333333336, 66.66666666666667, 100.0]

        @pytest.mark.asyncio
        async def test_download_size_validation(self, test_container, mock_download_config):
            """Test file size validation during download."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock documents with various sizes
            test_documents = [
                {"doc_id": "small-doc", "expected_size": 1024},  # 1KB - OK
                {"doc_id": "normal-doc", "expected_size": 1024 * 1024},  # 1MB - OK  
                {"doc_id": "large-doc", "expected_size": 200 * 1024 * 1024},  # 200MB - Too large
            ]
            
            async def mock_validate_size(doc, config):
                if doc["expected_size"] > config.max_file_size:
                    return {"valid": False, "reason": "File too large", "max_size": config.max_file_size}
                return {"valid": True}
            
            download_service.validate_file_size = AsyncMock(side_effect=mock_validate_size)
            
            for doc in test_documents:
                result = await download_service.validate_file_size(doc, mock_download_config)
                
                if doc["doc_id"] == "large-doc":
                    assert result["valid"] is False
                    assert "too large" in result["reason"].lower()
                else:
                    assert result["valid"] is True

    class TestDownloadValidation:
        """Test download validation and verification."""

        @pytest.mark.asyncio
        async def test_file_integrity_verification(self, test_container, temp_download_directory):
            """Test file integrity verification after download."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Create test file
            test_file = Path(temp_download_directory) / "test-document.pdf"
            test_content = b"PDF test content for integrity verification"
            test_file.write_bytes(test_content)
            
            # Calculate expected checksum
            expected_checksum = hashlib.md5(test_content).hexdigest()
            
            async def mock_verify_integrity(file_path, expected_checksum=None):
                file_path_obj = Path(file_path)
                if file_path_obj.exists():
                    content = file_path_obj.read_bytes()
                    actual_checksum = hashlib.md5(content).hexdigest()
                    
                    return {
                        "file_exists": True,
                        "file_size": len(content),
                        "checksum": actual_checksum,
                        "checksum_match": actual_checksum == expected_checksum if expected_checksum else True,
                        "is_valid": True
                    }
                return {"file_exists": False, "is_valid": False}
            
            download_service.verify_file_integrity = AsyncMock(side_effect=mock_verify_integrity)
            
            result = await download_service.verify_file_integrity(str(test_file), expected_checksum)
            
            assert result["file_exists"] is True
            assert result["file_size"] == len(test_content)
            assert result["checksum"] == expected_checksum
            assert result["checksum_match"] is True
            assert result["is_valid"] is True

        @pytest.mark.asyncio
        async def test_pdf_content_validation(self, test_container, temp_download_directory):
            """Test PDF content validation."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock PDF validation
            async def mock_validate_pdf(file_path):
                file_path_obj = Path(file_path)
                
                if not file_path_obj.exists():
                    return {"valid": False, "reason": "File not found"}
                
                # Simulate PDF validation
                if file_path_obj.suffix.lower() != ".pdf":
                    return {"valid": False, "reason": "Not a PDF file"}
                
                # Mock PDF structure validation
                content = file_path_obj.read_bytes()
                if not content.startswith(b"%PDF"):
                    return {"valid": False, "reason": "Invalid PDF header"}
                
                return {
                    "valid": True,
                    "pdf_version": "1.4",
                    "page_count": 5,
                    "text_extractable": True,
                    "encrypted": False
                }
            
            download_service.validate_pdf = AsyncMock(side_effect=mock_validate_pdf)
            
            # Test valid PDF
            valid_pdf = Path(temp_download_directory) / "valid.pdf"
            valid_pdf.write_bytes(b"%PDF-1.4\n%Test PDF content")
            
            result = await download_service.validate_pdf(str(valid_pdf))
            
            assert result["valid"] is True
            assert result["pdf_version"] == "1.4"
            assert result["page_count"] == 5
            assert result["text_extractable"] is True
            
            # Test invalid file
            invalid_file = Path(temp_download_directory) / "invalid.txt"
            invalid_file.write_bytes(b"Not a PDF file")
            
            result = await download_service.validate_pdf(str(invalid_file))
            assert result["valid"] is False

        @pytest.mark.asyncio
        async def test_bulk_validation(self, test_container, temp_download_directory):
            """Test bulk validation of downloaded files."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Create test files
            test_files = []
            for i in range(5):
                file_path = Path(temp_download_directory) / f"test-doc-{i}.pdf"
                content = f"PDF content for document {i}".encode()
                file_path.write_bytes(content)
                test_files.append({
                    "file_path": str(file_path),
                    "doc_id": f"test-doc-{i}",
                    "expected_size": len(content)
                })
            
            async def mock_bulk_validate(file_list):
                validation_results = []
                
                for file_info in file_list:
                    file_path = Path(file_info["file_path"])
                    
                    if file_path.exists():
                        actual_size = file_path.stat().st_size
                        size_match = actual_size == file_info["expected_size"]
                        
                        validation_results.append({
                            "doc_id": file_info["doc_id"],
                            "file_path": file_info["file_path"],
                            "exists": True,
                            "size_match": size_match,
                            "actual_size": actual_size,
                            "expected_size": file_info["expected_size"],
                            "valid": size_match
                        })
                    else:
                        validation_results.append({
                            "doc_id": file_info["doc_id"],
                            "file_path": file_info["file_path"],
                            "exists": False,
                            "valid": False
                        })
                
                return {
                    "total_files": len(file_list),
                    "valid_files": sum(1 for r in validation_results if r["valid"]),
                    "invalid_files": sum(1 for r in validation_results if not r["valid"]),
                    "results": validation_results
                }
            
            download_service.bulk_validate = AsyncMock(side_effect=mock_bulk_validate)
            
            result = await download_service.bulk_validate(test_files)
            
            assert result["total_files"] == 5
            assert result["valid_files"] == 5
            assert result["invalid_files"] == 0
            assert len(result["results"]) == 5
            assert all(r["valid"] for r in result["results"])

    class TestErrorHandlingAndRecovery:
        """Test error handling and recovery mechanisms."""

        @pytest.mark.asyncio
        async def test_download_timeout_handling(self, test_container, mock_download_config):
            """Test handling of download timeouts."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            document = {"doc_id": "timeout-test", "url": "https://slow-server.com/doc.pdf"}
            
            async def mock_download_with_timeout(doc, config):
                # Simulate timeout
                await asyncio.sleep(0.1)
                raise asyncio.TimeoutError("Download timeout")
            
            download_service.download_with_timeout = AsyncMock(side_effect=mock_download_with_timeout)
            
            with pytest.raises(asyncio.TimeoutError, match="Download timeout"):
                await download_service.download_with_timeout(document, mock_download_config)

        @pytest.mark.asyncio
        async def test_retry_mechanism(self, test_container):
            """Test retry mechanism for failed downloads."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            document = {"doc_id": "retry-test", "url": "https://unreliable.com/doc.pdf"}
            
            attempt_count = 0
            
            async def mock_download_with_retries(doc, max_retries=3):
                nonlocal attempt_count
                attempt_count += 1
                
                if attempt_count <= 2:  # First two attempts fail
                    raise Exception(f"Network error on attempt {attempt_count}")
                
                # Third attempt succeeds
                return {
                    "success": True,
                    "doc_id": doc["doc_id"],
                    "attempts": attempt_count,
                    "file_path": "/tmp/retry-test.pdf"
                }
            
            download_service.download_with_retries = AsyncMock(side_effect=mock_download_with_retries)
            
            result = await download_service.download_with_retries(document, max_retries=3)
            
            assert result["success"] is True
            assert result["attempts"] == 3
            assert attempt_count == 3

        @pytest.mark.asyncio
        async def test_partial_download_recovery(self, test_container, temp_download_directory):
            """Test recovery from partial downloads."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Create partial file
            partial_file = Path(temp_download_directory) / "partial-download.pdf"
            partial_content = b"Partial PDF content"
            partial_file.write_bytes(partial_content)
            
            document = {
                "doc_id": "partial-test",
                "file_path": str(partial_file),
                "expected_size": 1024 * 10  # 10KB expected, but only partial downloaded
            }
            
            async def mock_resume_download(doc):
                current_size = Path(doc["file_path"]).stat().st_size
                remaining_size = doc["expected_size"] - current_size
                
                if remaining_size > 0:
                    # Simulate resuming download
                    additional_content = b"x" * remaining_size
                    Path(doc["file_path"]).write_bytes(partial_content + additional_content)
                    
                    return {
                        "resumed": True,
                        "original_size": current_size,
                        "final_size": doc["expected_size"],
                        "bytes_downloaded": remaining_size
                    }
                
                return {"resumed": False, "reason": "File already complete"}
            
            download_service.resume_download = AsyncMock(side_effect=mock_resume_download)
            
            result = await download_service.resume_download(document)
            
            assert result["resumed"] is True
            assert result["original_size"] == len(partial_content)
            assert result["final_size"] == document["expected_size"]
            assert result["bytes_downloaded"] > 0

        @pytest.mark.asyncio
        async def test_disk_space_validation(self, test_container, mock_download_config):
            """Test disk space validation before downloads."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            large_documents = [
                {"doc_id": f"large-{i}", "expected_size": 50 * 1024 * 1024}  # 50MB each
                for i in range(10)
            ]
            
            async def mock_check_disk_space(documents, download_dir):
                total_required = sum(doc["expected_size"] for doc in documents)
                available_space = 100 * 1024 * 1024  # 100MB available
                
                return {
                    "available_space": available_space,
                    "required_space": total_required,
                    "sufficient_space": available_space >= total_required,
                    "shortage": max(0, total_required - available_space)
                }
            
            download_service.check_disk_space = AsyncMock(side_effect=mock_check_disk_space)
            
            result = await download_service.check_disk_space(large_documents, "/tmp/downloads")
            
            total_required = sum(doc["expected_size"] for doc in large_documents)
            
            assert result["required_space"] == total_required
            assert result["available_space"] == 100 * 1024 * 1024
            assert result["sufficient_space"] is False  # 500MB required, 100MB available
            assert result["shortage"] == total_required - (100 * 1024 * 1024)

    class TestDownloadMetricsAndReporting:
        """Test download metrics collection and reporting."""

        @pytest.mark.asyncio
        async def test_download_metrics_collection(self, test_container, mock_document_queue):
            """Test collection of download metrics."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock metrics collection
            async def mock_collect_metrics(documents):
                start_time = datetime.now()
                
                metrics = {
                    "session_id": "test-session-123",
                    "start_time": start_time.isoformat(),
                    "total_documents": len(documents),
                    "successful_downloads": len(documents) - 1,  # One failure
                    "failed_downloads": 1,
                    "total_bytes_downloaded": sum(doc["expected_size"] for doc in documents),
                    "average_download_speed": 1024 * 1024,  # 1MB/s
                    "total_duration": 45.5,  # seconds
                    "concurrent_connections": 3,
                    "retry_count": 2
                }
                
                return metrics
            
            download_service.collect_metrics = AsyncMock(side_effect=mock_collect_metrics)
            
            result = await download_service.collect_metrics(mock_document_queue)
            
            assert result["total_documents"] == 3
            assert result["successful_downloads"] == 2
            assert result["failed_downloads"] == 1
            assert result["total_bytes_downloaded"] > 0
            assert result["average_download_speed"] > 0
            assert result["retry_count"] == 2

        @pytest.mark.asyncio
        async def test_performance_analysis(self, test_container):
            """Test download performance analysis."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock performance data
            performance_data = {
                "download_times": [1.2, 2.1, 0.8, 3.5, 1.9],  # seconds
                "file_sizes": [1024*50, 1024*75, 1024*30, 1024*120, 1024*60],  # bytes
                "success_rates": [1.0, 1.0, 0.0, 1.0, 1.0],  # 1 failure
                "concurrent_downloads": 3
            }
            
            async def mock_analyze_performance(data):
                import statistics
                
                download_times = data["download_times"]
                file_sizes = data["file_sizes"]
                success_rates = data["success_rates"]
                
                return {
                    "average_download_time": statistics.mean(download_times),
                    "median_download_time": statistics.median(download_times),
                    "fastest_download": min(download_times),
                    "slowest_download": max(download_times),
                    "total_bytes": sum(file_sizes),
                    "average_file_size": statistics.mean(file_sizes),
                    "success_rate": statistics.mean(success_rates) * 100,
                    "throughput_mbps": (sum(file_sizes) / sum(download_times)) / (1024 * 1024),
                    "recommendations": []
                }
            
            download_service.analyze_performance = AsyncMock(side_effect=mock_analyze_performance)
            
            result = await download_service.analyze_performance(performance_data)
            
            assert result["average_download_time"] > 0
            assert result["success_rate"] == 80.0  # 4/5 successful
            assert result["throughput_mbps"] > 0
            assert result["fastest_download"] == 0.8
            assert result["slowest_download"] == 3.5

        @pytest.mark.asyncio
        async def test_failure_analysis(self, test_container):
            """Test analysis of download failures."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock failure data
            failure_data = [
                {
                    "doc_id": "fail-1",
                    "error_type": "TimeoutError",
                    "error_message": "Download timeout after 30 seconds",
                    "attempt_count": 3,
                    "file_size": 1024 * 1024 * 50  # 50MB
                },
                {
                    "doc_id": "fail-2", 
                    "error_type": "NetworkError",
                    "error_message": "Connection refused",
                    "attempt_count": 1,
                    "file_size": 1024 * 100  # 100KB
                },
                {
                    "doc_id": "fail-3",
                    "error_type": "AuthenticationError", 
                    "error_message": "Session expired",
                    "attempt_count": 2,
                    "file_size": 1024 * 500  # 500KB
                }
            ]
            
            async def mock_analyze_failures(failures):
                error_types = {}
                for failure in failures:
                    error_type = failure["error_type"]
                    error_types[error_type] = error_types.get(error_type, 0) + 1
                
                large_file_failures = sum(1 for f in failures if f["file_size"] > 1024 * 1024 * 10)
                
                return {
                    "total_failures": len(failures),
                    "error_breakdown": error_types,
                    "most_common_error": max(error_types.items(), key=lambda x: x[1])[0],
                    "large_file_failures": large_file_failures,
                    "average_attempts": sum(f["attempt_count"] for f in failures) / len(failures),
                    "recommendations": [
                        "Increase timeout for large files",
                        "Implement session refresh mechanism",
                        "Add network retry logic"
                    ]
                }
            
            download_service.analyze_failures = AsyncMock(side_effect=mock_analyze_failures)
            
            result = await download_service.analyze_failures(failure_data)
            
            assert result["total_failures"] == 3
            assert "TimeoutError" in result["error_breakdown"]
            assert "NetworkError" in result["error_breakdown"]
            assert "AuthenticationError" in result["error_breakdown"]
            assert result["large_file_failures"] == 1  # Only fail-1 is >10MB
            assert len(result["recommendations"]) > 0


@pytest.mark.integration
class TestDownloadWorkflowIntegration:
    """Integration tests for complete download workflows."""

    @pytest.mark.asyncio
    async def test_end_to_end_download_workflow(self, test_container, temp_download_directory):
        """Test complete download workflow from queue to validation."""
        
        # Get services
        download_service = test_container.pacer.verification.pacer_download_service()
        verification_service = test_container.pacer.verification.pacer_verification_service()
        
        # Mock the complete workflow
        test_documents = [
            {"doc_id": "e2e-1", "url": "https://example.com/doc1.pdf"},
            {"doc_id": "e2e-2", "url": "https://example.com/doc2.pdf"}
        ]
        
        # Mock each stage of the workflow
        download_service.initialize_queue = AsyncMock(return_value={"status": "ready"})
        download_service.add_to_queue = AsyncMock(return_value={"added": 2})
        download_service.download_concurrent = AsyncMock(return_value=[
            {"success": True, "doc_id": "e2e-1", "file_path": f"{temp_download_directory}/e2e-1.pdf"},
            {"success": True, "doc_id": "e2e-2", "file_path": f"{temp_download_directory}/e2e-2.pdf"}
        ])
        verification_service.verify_downloads = AsyncMock(return_value={
            "total_files": 2,
            "valid_files": 2,
            "invalid_files": 0
        })
        
        # Execute workflow
        await download_service.initialize_queue({})
        await download_service.add_to_queue(test_documents)
        download_results = await download_service.download_concurrent(test_documents)
        verification_results = await verification_service.verify_downloads(download_results)
        
        # Verify workflow completion
        assert len(download_results) == 2
        assert all(result["success"] for result in download_results)
        assert verification_results["valid_files"] == 2
        assert verification_results["invalid_files"] == 0