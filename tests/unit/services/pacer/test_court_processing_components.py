"""
Unit tests for PACER court processing components.

Tests cover:
- CourtProcessingService (facade)
- CourtSessionManager
- CourtDataProcessor
- CourtWorkflowCoordinator
- CourtValidator
- CourtMetadataManager

All tests use DI container injection following existing patterns.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path
from datetime import date

from src.pacer.components.court_processing.court_processing_service import CourtProcessingService
from src.infrastructure.protocols.exceptions import PacerServiceError
from playwright.async_api import BrowserContext, Page


@pytest.fixture
def court_processing_service(test_container):
    """Get court processing service with DI container injection."""
    return test_container.pacer.orchestration.court_processing_service()


@pytest.fixture
def mock_browser_context():
    """Mock browser context for testing."""
    context = AsyncMock(spec=BrowserContext)
    context.new_page = AsyncMock()
    context.close = AsyncMock()
    return context


@pytest.fixture
def mock_page():
    """Mock page for testing."""
    page = AsyncMock(spec=Page)
    page.goto = AsyncMock()
    page.wait_for_load_state = AsyncMock()
    page.close = AsyncMock()
    return page


@pytest.fixture
def court_processing_config():
    """Configuration for court processing tests."""
    return {
        'iso_date': '20240101',
        'data_path': '/tmp/test_data',
        'timeout': 30000,
        'court_ids': ['nys', 'cand'],
        'headless': True
    }


class TestCourtProcessingService:
    """Test cases for CourtProcessingService facade."""

    class TestInitialization:
        """Test service initialization with DI."""

        def test_init_with_container_injection(self, test_container):
            """Test initialization using DI container."""
            service = test_container.pacer.orchestration.court_processing_service()
            assert service is not None
            assert hasattr(service, 'logger')
            assert hasattr(service, 'config')

        def test_init_with_mock_dependencies(self, test_container):
            """Test initialization with mocked dependencies."""
            # Override specific dependencies in container
            mock_deepseek = AsyncMock()
            test_container.pacer.mock_config.override(Mock(return_value={'iso_date': '20240101'}))
            
            service = test_container.pacer.orchestration.court_processing_service()
            assert service is not None

        def test_init_validates_required_config(self, test_container):
            """Test that initialization validates required configuration."""
            # Test with minimal valid config
            service = test_container.pacer.orchestration.court_processing_service()
            assert service is not None

    class TestProcessCourt:
        """Test process_court method."""

        @pytest.mark.asyncio
        async def test_process_court_success(self, court_processing_service, mock_browser_context, mock_page):
            """Test successful court processing."""
            court_id = 'nys'
            date_str = '20240101'
            
            # Configure mocks for successful processing
            with patch.object(court_processing_service, 'validate_processing_setup') as mock_validate, \
                 patch.object(court_processing_service.metadata_manager, 'get_court_metadata') as mock_metadata:
                
                mock_validate.return_value = {
                    'all_prerequisites_met': True,
                    'critical_issues': []
                }
                mock_metadata.return_value = {'court_id': court_id, 'status': 'active'}
                
                result = await court_processing_service.process_court(
                    court_id, date_str, mock_browser_context, mock_page
                )
                
                assert result['court_id'] == court_id
                assert result['date_str'] == date_str
                assert result['status'] == 'success'
                assert 'metadata' in result
                mock_validate.assert_called_once_with(court_id)

        @pytest.mark.asyncio
        async def test_process_court_validation_failure(self, court_processing_service):
            """Test court processing with validation failure."""
            court_id = 'invalid'
            date_str = '20240101'
            
            with patch.object(court_processing_service, 'validate_processing_setup') as mock_validate:
                mock_validate.return_value = {
                    'all_prerequisites_met': False,
                    'critical_issues': ['Missing configuration', 'Invalid court ID']
                }
                
                result = await court_processing_service.process_court(court_id, date_str)
                
                assert result['court_id'] == court_id
                assert result['status'] == 'failed'
                assert result['error'] == 'Processing prerequisites not met'
                assert 'validation_issues' in result

        @pytest.mark.asyncio
        async def test_process_court_exception_handling(self, court_processing_service):
            """Test court processing with exception handling."""
            court_id = 'nys'
            date_str = '20240101'
            
            with patch.object(court_processing_service, 'validate_processing_setup') as mock_validate:
                mock_validate.side_effect = Exception("Test exception")
                
                result = await court_processing_service.process_court(court_id, date_str)
                
                assert result['court_id'] == court_id
                assert result['status'] == 'failed'
                assert 'Test exception' in result['error']

    class TestProcessSingleCourtTask:
        """Test process_single_court_task method."""

        @pytest.mark.asyncio
        async def test_process_single_court_task_success(self, court_processing_service, mock_browser_context):
            """Test successful single court task processing."""
            court_id = 'nys'
            iso_date = '20240101'
            start_date = date(2024, 1, 1)
            end_date = date(2024, 1, 1)
            processor_config = {'mode': 'report', 'timeout': 30000}
            
            with patch.object(court_processing_service.data_processor, 'perform_action') as mock_data_processor, \
                 patch.object(court_processing_service.workflow_coordinator, 'perform_action') as mock_workflow:
                
                mock_data_processor.return_value = {**processor_config, 'enhanced': True}
                mock_workflow.return_value = {
                    'court_id': court_id,
                    'status': 'success',
                    'rows_processed': 50,
                    'cases_found': 10
                }
                
                result = await court_processing_service.process_single_court_task(
                    court_id, mock_browser_context, iso_date, start_date, end_date, processor_config
                )
                
                assert result['court_id'] == court_id
                assert result['status'] == 'success'
                assert result['rows_processed'] == 50
                assert result['cases_found'] == 10
                
                # Verify delegation calls
                mock_data_processor.assert_called_once()
                mock_workflow.assert_called_once()

        @pytest.mark.asyncio
        async def test_process_single_court_task_failure(self, court_processing_service, mock_browser_context):
            """Test single court task processing failure."""
            court_id = 'nys'
            iso_date = '20240101'
            start_date = date(2024, 1, 1)
            end_date = date(2024, 1, 1)
            processor_config = {'mode': 'report'}
            
            with patch.object(court_processing_service.data_processor, 'perform_action') as mock_data_processor:
                mock_data_processor.side_effect = Exception("Data processor error")
                
                result = await court_processing_service.process_single_court_task(
                    court_id, mock_browser_context, iso_date, start_date, end_date, processor_config
                )
                
                assert result['court_id'] == court_id
                assert result['status'] == 'failed'
                assert result['rows_processed'] == 0
                assert result['cases_found'] == 0
                assert 'Data processor error' in result['error']

    class TestProcessMultipleDockets:
        """Test process_multiple_dockets_for_court_task method."""

        @pytest.mark.asyncio
        async def test_process_multiple_dockets_success(self, court_processing_service, mock_browser_context):
            """Test successful multiple dockets processing."""
            court_id = 'nys'
            docket_numbers = ['1:23-cv-00001', '1:23-cv-00002']
            iso_date = '20240101'
            processor_config = {'mode': 'multi_docket'}
            mock_relevance_engine = AsyncMock()
            
            with patch.object(court_processing_service.data_processor, 'perform_action') as mock_data_processor, \
                 patch.object(court_processing_service.workflow_coordinator, 'perform_action') as mock_workflow:
                
                mock_data_processor.return_value = {**processor_config, 'enhanced': True}
                mock_workflow.return_value = {
                    'court_id': court_id,
                    'status': 'success',
                    'dockets_requested': 2,
                    'dockets_processed': 2,
                    'dockets_found': 2
                }
                
                result = await court_processing_service.process_multiple_dockets_for_court_task(
                    court_id, docket_numbers, mock_browser_context, iso_date, 
                    processor_config, mock_relevance_engine
                )
                
                assert result['court_id'] == court_id
                assert result['status'] == 'success'
                assert result['dockets_requested'] == 2
                assert result['dockets_processed'] == 2
                assert result['dockets_found'] == 2

    class TestValidateProcessingSetup:
        """Test validate_processing_setup method."""

        @pytest.mark.asyncio
        async def test_validate_processing_setup_success(self, court_processing_service):
            """Test successful processing setup validation."""
            court_id = 'nys'
            
            with patch.object(court_processing_service.validator, 'perform_action') as mock_validator, \
                 patch.object(court_processing_service.metadata_manager, 'validate_configuration_integrity') as mock_config_val:
                
                mock_validator.return_value = {
                    'all_prerequisites_met': True,
                    'critical_issues': [],
                    'validation_details': {'config_valid': True}
                }
                mock_config_val.return_value = {
                    'is_valid': True,
                    'validation_errors': []
                }
                
                result = await court_processing_service.validate_processing_setup(court_id)
                
                assert result['all_prerequisites_met'] is True
                assert len(result['critical_issues']) == 0
                assert 'configuration_validation' in result

        @pytest.mark.asyncio
        async def test_validate_processing_setup_failure(self, court_processing_service):
            """Test processing setup validation failure."""
            court_id = 'invalid'
            
            with patch.object(court_processing_service.validator, 'perform_action') as mock_validator, \
                 patch.object(court_processing_service.metadata_manager, 'validate_configuration_integrity') as mock_config_val:
                
                mock_validator.return_value = {
                    'all_prerequisites_met': False,
                    'critical_issues': ['Missing data path'],
                    'validation_details': {}
                }
                mock_config_val.return_value = {
                    'is_valid': False,
                    'validation_errors': ['Invalid configuration format']
                }
                
                result = await court_processing_service.validate_processing_setup(court_id)
                
                assert result['all_prerequisites_met'] is False
                assert len(result['critical_issues']) == 2
                assert 'Missing data path' in result['critical_issues']
                assert 'Invalid configuration format' in result['critical_issues']

    class TestExecuteAction:
        """Test _execute_action method."""

        @pytest.mark.asyncio
        async def test_execute_action_process_court(self, court_processing_service, mock_browser_context, mock_page):
            """Test execute_action with process_court action."""
            data = {
                'action': 'process_court',
                'court_id': 'nys',
                'date_str': '20240101',
                'browser_context': mock_browser_context,
                'page': mock_page
            }
            
            with patch.object(court_processing_service, 'process_court') as mock_process:
                mock_process.return_value = {'status': 'success'}
                
                result = await court_processing_service._execute_action(data)
                
                assert result['status'] == 'success'
                mock_process.assert_called_once_with('nys', '20240101', mock_browser_context, mock_page)

        @pytest.mark.asyncio
        async def test_execute_action_invalid_action(self, court_processing_service):
            """Test execute_action with invalid action."""
            data = {
                'action': 'invalid_action',
                'court_id': 'nys'
            }
            
            with pytest.raises(PacerServiceError, match="Invalid action data provided"):
                await court_processing_service._execute_action(data)

        @pytest.mark.asyncio
        async def test_execute_action_invalid_data_type(self, court_processing_service):
            """Test execute_action with invalid data type."""
            data = "invalid_data_type"
            
            with pytest.raises(PacerServiceError, match="Invalid action data provided"):
                await court_processing_service._execute_action(data)

    class TestLegacyCompatibility:
        """Test legacy compatibility methods."""

        @pytest.mark.asyncio
        async def test_get_async_storage(self, court_processing_service):
            """Test legacy get_async_storage method."""
            mock_storage = AsyncMock()
            
            with patch.object(court_processing_service.metadata_manager, 'get_async_storage') as mock_method:
                mock_method.return_value = mock_storage
                
                result = await court_processing_service.get_async_storage()
                
                assert result == mock_storage
                mock_method.assert_called_once()

        @pytest.mark.asyncio
        async def test_get_pacer_repo(self, court_processing_service):
            """Test legacy get_pacer_repo method."""
            mock_repo = AsyncMock()
            
            with patch.object(court_processing_service.metadata_manager, 'get_pacer_repository') as mock_method:
                mock_method.return_value = mock_repo
                
                result = await court_processing_service.get_pacer_repo()
                
                assert result == mock_repo
                mock_method.assert_called_once()

        def test_setup_download_paths(self, court_processing_service):
            """Test legacy setup_download_paths method."""
            court_id = 'nys'
            iso_date = '20240101'
            mode = 'report'
            expected_path = '/tmp/test_downloads'
            
            with patch.object(court_processing_service.data_processor, 'setup_download_paths') as mock_method:
                mock_method.return_value = expected_path
                
                result = court_processing_service.setup_download_paths(court_id, iso_date, mode)
                
                assert result == expected_path
                mock_method.assert_called_once_with(court_id, iso_date, mode)

    class TestCleanup:
        """Test cleanup functionality."""

        @pytest.mark.asyncio
        async def test_cleanup_success(self, court_processing_service):
            """Test successful cleanup of all components."""
            # Mock all component cleanup methods
            components = [
                court_processing_service.session_manager,
                court_processing_service.data_processor,
                court_processing_service.workflow_coordinator,
                court_processing_service.data_extractor,
                court_processing_service.validator,
                court_processing_service.metadata_manager
            ]
            
            for component in components:
                component.cleanup = AsyncMock()
            
            await court_processing_service.cleanup()
            
            # Verify all components were cleaned up
            for component in components:
                component.cleanup.assert_called_once()

        @pytest.mark.asyncio
        async def test_cleanup_with_exception(self, court_processing_service):
            """Test cleanup with component exception."""
            # Make one component fail during cleanup
            court_processing_service.session_manager.cleanup = AsyncMock(side_effect=Exception("Cleanup error"))
            court_processing_service.data_processor.cleanup = AsyncMock()
            court_processing_service.workflow_coordinator.cleanup = AsyncMock()
            court_processing_service.data_extractor.cleanup = AsyncMock()
            court_processing_service.validator.cleanup = AsyncMock()
            court_processing_service.metadata_manager.cleanup = AsyncMock()
            
            # Cleanup should not raise exception
            await court_processing_service.cleanup()
            
            # Other components should still be cleaned up
            court_processing_service.data_processor.cleanup.assert_called_once()


class TestCourtProcessingServiceIntegration:
    """Integration tests for CourtProcessingService."""

    @pytest.mark.asyncio
    async def test_full_court_processing_workflow(self, test_container, mock_browser_context, mock_page):
        """Test complete court processing workflow with DI container."""
        # Get service from container
        service = test_container.pacer.orchestration.court_processing_service()
        
        # Mock all dependencies for end-to-end test
        with patch.object(service, 'validate_processing_setup') as mock_validate, \
             patch.object(service.metadata_manager, 'get_court_metadata') as mock_metadata, \
             patch.object(service.data_processor, 'perform_action') as mock_data_processor, \
             patch.object(service.workflow_coordinator, 'perform_action') as mock_workflow:
            
            # Setup mocks
            mock_validate.return_value = {
                'all_prerequisites_met': True,
                'critical_issues': []
            }
            mock_metadata.return_value = {'court_id': 'nys', 'status': 'active'}
            mock_data_processor.return_value = {'mode': 'report', 'enhanced': True}
            mock_workflow.return_value = {
                'court_id': 'nys',
                'status': 'success',
                'rows_processed': 100,
                'cases_found': 25
            }
            
            # Test process_court
            court_result = await service.process_court('nys', '20240101', mock_browser_context, mock_page)
            assert court_result['status'] == 'success'
            
            # Test process_single_court_task
            task_result = await service.process_single_court_task(
                'nys', mock_browser_context, '20240101', 
                date(2024, 1, 1), date(2024, 1, 1), {'mode': 'report'}
            )
            assert task_result['status'] == 'success'
            assert task_result['rows_processed'] == 100

    @pytest.mark.asyncio
    async def test_di_container_service_wiring(self, test_container):
        """Test that DI container properly wires all services."""
        service = test_container.pacer.orchestration.court_processing_service()
        
        # Verify all injected dependencies are available
        assert service.session_manager is not None
        assert service.data_processor is not None
        assert service.workflow_coordinator is not None
        assert service.data_extractor is not None
        assert service.validator is not None
        assert service.metadata_manager is not None
        
        # Verify service is properly initialized
        assert hasattr(service, 'logger')
        assert hasattr(service, 'config')


if __name__ == '__main__':
    pytest.main([__file__, '-v'])