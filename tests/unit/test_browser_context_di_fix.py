"""
Test browser context dependency injection fix.

This test validates that the DI container architecture properly injects
browser services into orchestration components and that the critical fixes
for NavigationHandler and browser service factory resolution work correctly.
"""

import asyncio
import pytest
import logging
from unittest.mock import Mock, AsyncMock, patch

from src.containers.pacer import get_container
from src.containers.browser_container import BrowserContainer
from src.containers.orchestration_container import OrchestrationContainer
from src.pacer.components.orchestration.browser_manager import PacerBrowserManager
from src.pacer.pacer_browser_service import PacerBrowserService
from src.pacer.components.browser.navigation_handler import NavigationHandler
from src.infrastructure.protocols.exceptions import PacerServiceError


class TestBrowserContextDIFix:
    """Test browser context dependency injection fixes."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for testing."""
        return {
            'headless': True,
            'timeout_ms': 30000,
            'run_parallel': True,
            'num_workers': 4,
            'data_path': './test_data'
        }
        
    @pytest.fixture
    def mock_logger(self):
        """Mock logger for testing."""
        return Mock()
        
    def test_browser_manager_set_browser_service_validation(self, mock_logger, mock_config):
        """Test that browser manager validates browser service injection."""
        browser_manager = PacerBrowserManager(mock_logger, mock_config)
        
        # Test None injection fails
        with pytest.raises(PacerServiceError, match="Cannot inject None as browser service"):
            browser_manager.set_browser_service(None)
            
        # Test valid injection succeeds  
        mock_browser_service = Mock()
        browser_manager.set_browser_service(mock_browser_service)
        assert browser_manager.browser_service == mock_browser_service
        
    @pytest.mark.asyncio
    async def test_browser_manager_context_creation_without_service(self, mock_logger, mock_config):
        """Test that browser manager fails context creation without service."""
        browser_manager = PacerBrowserManager(mock_logger, mock_config)
        
        with pytest.raises(PacerServiceError, match="Browser service not available"):
            await browser_manager.create_browser_context_with_download_path("test_court", "20240101")
            
    @pytest.mark.asyncio 
    async def test_browser_manager_context_creation_with_service(self, mock_logger, mock_config):
        """Test that browser manager creates context with proper service."""
        browser_manager = PacerBrowserManager(mock_logger, mock_config)
        
        # Mock browser service with create_context method
        mock_browser_service = Mock()
        mock_context = Mock()
        mock_browser_service.create_context = AsyncMock(return_value=mock_context)
        
        browser_manager.set_browser_service(mock_browser_service)
        
        # Should succeed with proper browser service
        context = await browser_manager.create_browser_context_with_download_path(
            "test_court", "20240101"
        )
        
        assert context == mock_context
        mock_browser_service.create_context.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_browser_manager_fallback_patterns(self, mock_logger, mock_config):
        """Test browser manager fallback patterns for different service types."""
        browser_manager = PacerBrowserManager(mock_logger, mock_config)
        
        # Test direct create_context method (primary pattern)
        mock_browser_service = Mock()
        mock_context = Mock()
        
        # Configure direct create_context method
        mock_browser_service.create_context = AsyncMock(return_value=mock_context)
        
        browser_manager.set_browser_service(mock_browser_service)
        
        # Should use direct create_context
        context = await browser_manager.create_browser_context_with_download_path(
            "test_court", "20240101"
        )
        
        assert context == mock_context
        mock_browser_service.create_context.assert_called_once()
        
    @pytest.mark.skipif(
        True, 
        reason="Integration test - requires full container setup"
    )
    def test_container_browser_service_wiring(self):
        """Test that container properly wires browser service dependencies."""
        # This would be an integration test requiring full container setup
        container = get_container()
        
        # Validate that orchestration container gets proper browser service
        orchestration = container.orchestration
        browser_service_factory = orchestration.browser_service_factory
        
        assert browser_service_factory is not None
        
        # Validate that browser service factory resolves to pacer_browser_service
        browser_service = browser_service_factory()
        assert browser_service is not None
        
    def test_orchestration_browser_manager_creation(self, mock_logger, mock_config):
        """Test orchestration browser manager creation with dependency injection."""
        # This tests the container factory pattern
        from src.containers.orchestration_container import OrchestrationContainer
        
        container = OrchestrationContainer()
        container.config.from_dict(mock_config)
        container.logger.override(mock_logger)
        
        # Mock browser service factory
        mock_browser_service = Mock()
        container.browser_service_factory.override(lambda: mock_browser_service)
        
        # Create orchestrator browser manager
        browser_manager = container.orchestrator_browser_manager()
        
        assert browser_manager is not None
        
    def test_browser_service_injector_pattern(self, mock_logger):
        """Test the browser service injector helper class."""
        from src.containers.orchestration_container import OrchestrationContainer
        
        # Create browser service injector
        mock_browser_service = Mock()
        mock_factory = Mock(return_value=mock_browser_service)
        mock_browser_service_factory = Mock(return_value=mock_factory)
        
        # Test the injector class directly
        injector = OrchestrationContainer.BrowserServiceInjector(
            mock_logger, mock_browser_service_factory
        )
        
        mock_browser_manager = Mock()
        mock_browser_manager.set_browser_service = Mock()
        
        # Test injection
        asyncio.run(injector.inject_browser_service(mock_browser_manager))
        
        mock_browser_service_factory.assert_called_once()
        mock_factory.assert_called_once()
        mock_browser_manager.set_browser_service.assert_called_once_with(mock_browser_service)

    # CRITICAL FIX TESTS - Test the specific fixes implemented

    def test_browser_container_navigation_handler_factory(self, mock_logger, mock_config):
        """Test that BrowserContainer creates NavigationHandler factory correctly."""
        container = BrowserContainer()
        container.config.override(mock_config)
        container.logger.override(mock_logger)
        
        # Get navigation handler factory
        nav_factory_provider = container.navigation_handler_factory
        nav_factory = nav_factory_provider()
        
        assert nav_factory is not None
        assert callable(nav_factory)
        
        # Create mock page
        mock_page = Mock()
        mock_page.is_closed.return_value = False
        mock_page.url = "https://example.com"
        
        # Create navigation handler with valid page should work
        nav_handler = nav_factory(
            page=mock_page,
            screenshot_dir="/tmp/screenshots"
        )
        
        assert nav_handler is not None
        assert isinstance(nav_handler, NavigationHandler)
        assert nav_handler.page == mock_page
        
    def test_navigation_handler_factory_none_page_validation(self, mock_logger, mock_config):
        """Test that NavigationHandler factory validates None page."""
        container = BrowserContainer()
        container.config.override(mock_config)
        container.logger.override(mock_logger)
        
        nav_factory = container.navigation_handler_factory()
        
        # Should raise ValueError when page is None
        with pytest.raises(ValueError, match="NavigationHandler requires a valid Page instance"):
            nav_factory(page=None)

    def test_pacer_browser_service_dependency_validation(self, mock_logger):
        """Test that PacerBrowserService validates its dependencies."""
        # Test with valid dependencies
        mock_browser_manager = Mock()
        mock_auth_handler = Mock()
        
        service = PacerBrowserService(
            browser_manager=mock_browser_manager,
            auth_handler=mock_auth_handler,
            navigation_handler=None,  # Can be None, created at runtime
            logger=mock_logger
        )
        
        assert service._browser_manager == mock_browser_manager
        assert service._auth_handler == mock_auth_handler
        
        # Test with None browser_manager should fail
        with pytest.raises(ValueError, match="BrowserManager cannot be None"):
            PacerBrowserService(
                browser_manager=None,
                auth_handler=mock_auth_handler,
                logger=mock_logger
            )
            
        # Test with None auth_handler should fail
        with pytest.raises(ValueError, match="AuthenticationHandler cannot be None"):
            PacerBrowserService(
                browser_manager=mock_browser_manager,
                auth_handler=None,
                logger=mock_logger
            )

    def test_orchestration_container_browser_service_factory_validation(self, mock_logger):
        """Test OrchestrationContainer browser service factory validation."""
        container = OrchestrationContainer()
        container.config.from_dict({
            'headless': True,
            'timeout_ms': 30000
        })
        container.logger.override(mock_logger)
        
        # Mock a failing browser service factory
        def mock_failing_factory():
            return None
            
        container.browser_service_factory.override(mock_failing_factory)
        
        # Validation should catch None factory
        with pytest.raises(RuntimeError, match="Browser service factory returned None"):
            validated_factory = container.validated_browser_service_factory()
            validated_factory()

    def test_browser_service_injector_validation(self, mock_logger):
        """Test BrowserServiceInjector validation."""
        from src.containers.orchestration_container import OrchestrationContainer
        
        # Should fail with None logger
        with pytest.raises(ValueError, match="Logger cannot be None"):
            OrchestrationContainer.BrowserServiceInjector(
                logger=None,
                browser_service_factory=Mock()
            )
            
        # Should fail with None factory
        with pytest.raises(ValueError, match="Browser service factory cannot be None"):
            OrchestrationContainer.BrowserServiceInjector(
                logger=mock_logger,
                browser_service_factory=None
            )

    def test_navigation_handler_page_none_warning(self, mock_logger, caplog):
        """Test that NavigationHandler warns when initialized with None page."""
        with caplog.at_level(logging.WARNING):
            nav_handler = NavigationHandler(
                page=None,
                config={},
                logger=mock_logger
            )
            
        # Should create handler but log warning
        assert nav_handler is not None
        assert nav_handler.page is None
        
        # Check that warning was logged
        assert "NavigationHandler initialized with page=None" in caplog.text

    def test_browser_service_injector_full_validation(self, mock_logger):
        """Test BrowserServiceInjector with full validation flow."""
        from src.containers.orchestration_container import OrchestrationContainer
        
        # Create mock browser service and factory
        mock_browser_service = Mock()
        mock_factory = Mock(return_value=mock_browser_service)
        mock_browser_service_factory = Mock(return_value=mock_factory)
        
        # Create injector
        injector = OrchestrationContainer.BrowserServiceInjector(
            logger=mock_logger,
            browser_service_factory=mock_browser_service_factory
        )
        
        # Create mock browser manager
        mock_browser_manager = Mock()
        mock_browser_manager.set_browser_service = Mock()
        
        # Test successful injection
        result = asyncio.run(injector.inject_browser_service(mock_browser_manager))
        
        assert result == mock_browser_manager
        mock_browser_service_factory.assert_called_once()
        mock_factory.assert_called_once()
        mock_browser_manager.set_browser_service.assert_called_once_with(mock_browser_service)

    def test_browser_service_injector_factory_failure_handling(self, mock_logger):
        """Test BrowserServiceInjector handles factory failures."""
        from src.containers.orchestration_container import OrchestrationContainer
        
        # Create failing factory
        def failing_factory():
            return None  # Returns None factory
            
        mock_browser_service_factory = Mock(side_effect=failing_factory)
        
        injector = OrchestrationContainer.BrowserServiceInjector(
            logger=mock_logger,
            browser_service_factory=mock_browser_service_factory
        )
        
        mock_browser_manager = Mock()
        
        # Should raise RuntimeError with clear message
        with pytest.raises(RuntimeError, match="Browser service factory resolution returned None"):
            asyncio.run(injector.inject_browser_service(mock_browser_manager))


if __name__ == "__main__":
    pytest.main([__file__])