#!/usr/bin/env python3
"""
Test to investigate if versus regex patterns are causing company name splitting.
"""

import re
from src.services.html.case_parser_service import CaseParserService
from unittest.mock import Mock

def test_versus_pattern_issue():
    """Test if versus patterns could split company names."""
    
    # Mock logger
    mock_logger = Mock()
    parser = CaseParserService(logger=mock_logger)
    
    # Test HTML with a company name that might be split by versus patterns
    html_content = """
    <html>
    <body>
        <div id="cmecfMainContent">
            <h3 align="center">
                U.S. District Court<br/>
                Northern District of California<br/>
                CIVIL DOCKET FOR CASE #: 3:25-cv-05328
            </h3>
            <table cellpadding="1">
                <tr>
                    <td valign="top">
                        Some Plaintiff v. 3M Company, Inc. fka Minnesota Mining and Manufacturing Company
                    </td>
                </tr>
            </table>
        </div>
    </body>
    </html>
    """
    
    # Test versus regex patterns directly
    versus_patterns = [
        r'([A-Z][a-zA-Z\s,]+)\s+v\.?\s+([A-Z][a-zA-Z\s,]+)',
        r'([A-Z][a-zA-Z\s,]+)\s+vs\.?\s+([A-Z][a-zA-Z\s,]+)',
        r'([A-Z][a-zA-Z\s,]+)\s+versus\s+([A-Z][a-zA-Z\s,]+)'
    ]
    
    test_text = "Some Plaintiff v. 3M Company, Inc. fka Minnesota Mining and Manufacturing Company"
    print(f"Testing text: {test_text}")
    
    for i, pattern in enumerate(versus_patterns):
        match = re.search(pattern, test_text)
        if match:
            print(f"Pattern {i+1} matched:")
            print(f"  Plaintiff: '{match.group(1).strip()}'")
            print(f"  Defendant: '{match.group(2).strip()}'")
            print()
    
    # Parse the HTML and check results
    parser.set_html(html_content)
    result = parser.parse()
    
    print("HTML Parse Results:")
    print(f"Versus: {result.get('case_info', {}).get('versus', 'None')}")
    print(f"Plaintiffs: {result.get('plaintiffs', [])}")
    print(f"Defendants: {result.get('defendants', [])}")

if __name__ == "__main__":
    test_versus_pattern_issue()