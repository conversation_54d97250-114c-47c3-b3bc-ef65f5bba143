"""
Test Dependency Injection Container

This module provides a test-specific DI container configuration
with mock services for unit and integration testing.
"""
from typing import Optional, Dict, Any
from unittest.mock import Mock, AsyncMock

from dependency_injector import containers, providers

# Create test logger
class MockLogger:
    """Mock logger for testing."""

    def __init__(self):
        self.messages = []

    def log_info(self, msg):
        self.messages.append(('info', msg))

    def log_error(self, msg, **kwargs):
        self.messages.append(('error', msg))

    def log_warning(self, msg):
        self.messages.append(('warning', msg))

    def log_debug(self, msg):
        self.messages.append(('debug', msg))

    def info(self, msg):
        self.log_info(msg)

    def error(self, msg, **kwargs):
        self.log_error(msg, **kwargs)

    def warning(self, msg):
        self.log_warning(msg)

    def debug(self, msg):
        self.log_debug(msg)

class TestContainer(containers.DeclarativeContainer):
    """Test dependency injection container with mock services."""

    # Core infrastructure
    logger = providers.Factory(MockLogger)

    # Storage services
    s3_storage = Mock()
    dynamodb_storage = Mock()

    # Repositories
    pacer_repository = Mock()
    fb_archive_repository = Mock()
    law_firms_repository = Mock()
    district_courts_repository = Mock()

    # AI Services
    deepseek_service = AsyncMock()
    mistral_service = AsyncMock()
    prompt_manager = Mock()
    ai_orchestrator = AsyncMock()

    # PACER Services
    browser_service = AsyncMock()
    case_processing_service = AsyncMock()
    court_processing_service = AsyncMock()
    document_download_service = AsyncMock()
    file_management_service = AsyncMock()
    html_processing_service = AsyncMock()

    # Facebook Ad Services
    ad_processing_service = AsyncMock()
    data_validation_service = AsyncMock()
    error_handling_service = AsyncMock()
    workflow_service = AsyncMock()

    # Transformer Services
    law_firm_processor = AsyncMock()
    mdl_processor = AsyncMock()
    transfer_handler = AsyncMock()

    # Configuration
    config = providers.Configuration()

def create_test_container(config: Optional[Dict[str, Any]] = None) -> TestContainer:
    """
    Create and configure a test DI container.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured TestContainer instance
    """
    container = TestContainer()

    if config:
        container.config.from_dict(config)
    else:
        # Default test configuration
        container.config.from_dict({
            'environment': 'test',
            'debug': True,
            'async_enabled': True
        })

    return container

def wire_test_container(container: TestContainer, modules: Optional[list] = None):
    """
    Wire the test container to specified modules.
    
    Args:
        container: The test container to wire
        modules: List of modules to wire (defaults to common test modules)
    """
    if modules is None:
        modules = [
            'src.services.ai.ai_orchestrator',
            'src.services.pacer.court_processing_service',
            'src.services.fb_ads.orchestrator',
            'src.services.transformer.transfer_handler',
            'tests.conftest'
        ]

    container.wire(modules=modules)

def unwire_test_container(container: TestContainer):
    """Unwire the test container."""
    container.unwire()
