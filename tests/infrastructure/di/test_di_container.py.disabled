"""
Test Dependency Injection Container using dependency-injector

This module provides a test-specific DI container configuration
with mock services for unit and integration testing.
"""
from typing import Optional, Dict, Any, List
from unittest.mock import Mock, AsyncMock

from dependency_injector import containers, providers
from tests.test_infrastructure.test_containers import test_container, override_providers

class MockLogger:
    """Mock logger for testing."""

    def __init__(self):
        self.messages = []

    def log_info(self, msg):
        self.messages.append(('info', msg))

    def log_error(self, msg, **kwargs):
        self.messages.append(('error', msg))

    def log_warning(self, msg):
        self.messages.append(('warning', msg))

    def log_debug(self, msg):
        self.messages.append(('debug', msg))

    def info(self, msg):
        self.log_info(msg)

    def error(self, msg, **kwargs):
        self.log_error(msg, **kwargs)

    def warning(self, msg):
        self.log_warning(msg)

    def debug(self, msg):
        self.log_debug(msg)

class TestContainer(containers.DeclarativeContainer):
    """Test dependency injection container with mock services."""

    # Configuration
    config = providers.Configuration()

    # Core infrastructure
    logger = providers.Factory(MockLogger)

    # Storage services - Mock async services
    dynamodb_storage = providers.Factory(
        lambda: AsyncMock(spec=['query', 'put_item', 'get_item', 'delete_item', 'update_item'])
    )

    s3_async_storage = providers.Factory(
        lambda: AsyncMock(spec=['upload_content', 'download_content', 'exists', 'delete'])
    )

    # External clients - Mock async services
    deepseek_client = providers.Factory(
        lambda: AsyncMock(spec=['chat_completion', 'generate_text'])
    )

    openai_client = providers.Factory(
        lambda: AsyncMock(spec=['chat_completion', 'generate_text'])
    )

    # MistralClient not needed - MistralService uses mistralai library directly

    llava_client = providers.Factory(
        lambda: AsyncMock(spec=['analyze_image', 'extract_text'])
    )

    # Repositories - Mock async repositories
    pacer_repository = providers.Factory(
        lambda: AsyncMock(spec=['find_by_date', 'find_by_court', 'save', 'update', 'delete'])
    )

    fb_archive_repository = providers.Factory(
        lambda: AsyncMock(spec=['find_by_date_range', 'find_by_page_id', 'save', 'update', 'delete'])
    )

    law_firms_repository = providers.Factory(
        lambda: AsyncMock(spec=['find_by_id', 'find_by_name', 'save', 'update', 'delete'])
    )

    district_courts_repository = providers.Factory(
        lambda: AsyncMock(spec=['find_by_court_id', 'find_by_mdl', 'save', 'update', 'delete'])
    )

    fb_image_hash_repository = providers.Factory(
        lambda: AsyncMock(spec=['find_by_hash', 'find_by_ad_id', 'save', 'update', 'delete'])
    )

    # AI Services - Mock async services
    prompt_manager = providers.Factory(
        lambda: Mock(spec=['get_prompt', 'format_prompt', 'load_prompts'])
    )

    deepseek_service = providers.Factory(
        lambda: AsyncMock(spec=['classify_ad', 'extract_summary', 'extract_attorney_info', 'generate_text'])
    )

    mistral_service = providers.Factory(
        lambda: AsyncMock(spec=['generate_text', 'extract_entities', 'summarize'])
    )

    local_image_queue = providers.Factory(
        lambda: AsyncMock(spec=['add_image', 'get_next_image', 'mark_processed'])
    )

    ai_orchestrator = providers.Factory(
        lambda: AsyncMock(spec=['process_text', 'process_batch', 'analyze_image'])
    )

    # PACER Services - Mock async services
    browser_service = providers.Factory(
        lambda: AsyncMock(spec=['start', 'stop', 'navigate', 'wait_for_selector', 'get_text'])
    )

    case_processing_service = providers.Factory(
        lambda: AsyncMock(spec=['process_case', 'extract_case_details', 'validate_case'])
    )

    court_processing_service = providers.Factory(
        lambda: AsyncMock(spec=['process_court', 'get_court_cases', 'validate_court'])
    )

    document_download_service = providers.Factory(
        lambda: AsyncMock(spec=['download_document', 'validate_download', 'get_download_path'])
    )

    file_management_service = providers.Factory(
        lambda: AsyncMock(spec=['save_file', 'read_file', 'delete_file', 'file_exists'])
    )

    html_processing_service = providers.Factory(
        lambda: AsyncMock(spec=['parse_html', 'extract_data', 'validate_html'])
    )

    analytics_service = providers.Factory(
        lambda: AsyncMock(spec=['analyze_data', 'generate_report', 'get_statistics'])
    )

    # FB Ads Services - Mock async services
    ad_processing_service = providers.Factory(
        lambda: AsyncMock(spec=['process_ad', 'classify_ad', 'extract_ad_data'])
    )

    data_validation_service = providers.Factory(
        lambda: AsyncMock(spec=['validate_data', 'clean_data', 'check_required_fields'])
    )

    error_handling_service = providers.Factory(
        lambda: AsyncMock(spec=['handle_error', 'log_error', 'get_error_summary'])
    )

    workflow_service = providers.Factory(
        lambda: AsyncMock(spec=['run_workflow', 'validate_workflow', 'get_workflow_status'])
    )

    job_runner_service = providers.Factory(
        lambda: AsyncMock(spec=['run_job', 'get_job_status', 'cancel_job'])
    )

    # Transformer Services - Mock async services
    law_firm_processor = providers.Factory(
        lambda: AsyncMock(spec=['process_law_firms', 'extract_law_firms', 'normalize_law_firm'])
    )

    mdl_processor = providers.Factory(
        lambda: AsyncMock(spec=['process_mdl', 'extract_mdl_info', 'validate_mdl'])
    )

    transfer_handler = providers.Factory(
        lambda: AsyncMock(spec=['process_transfers', 'validate_transfer', 'get_transfer_info'])
    )

def create_test_container(config: Optional[Dict[str, Any]] = None) -> TestContainer:
    """
    Create and configure a test DI container.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured TestContainer instance
    """
    container = TestContainer()

    if config:
        container.config.from_dict(config)
    else:
        # Default test configuration
        container.config.from_dict({
            'environment': 'test',
            'debug': True,
            'async_enabled': True,
            'aws_region': 'us-west-2',
            'dynamodb_endpoint': 'http://localhost:8000',
            's3_bucket_name': 'test-bucket',
            'deepseek_api_key': 'test-key',
            'openai_api_key': 'test-key',
            'mistral_api_key': 'test-key',
            'llava_base_url': 'http://localhost:11434',
            'llava_model': 'test-model',
            'llava_timeout': 30,
            'image_queue_dir': '/tmp/test-queue'
        })

    return container

def wire_test_container(container: TestContainer, modules: Optional[List[str]] = None):
    """
    Wire the test container to specified modules.
    
    Args:
        container: The test container to wire
        modules: List of module paths to wire (defaults to common test modules)
    """
    if modules is None:
        # Default modules that use @inject decorator
        modules = [
            'src.services.ai.ai_orchestrator',
            'src.services.ai.deepseek_service',
            'src.services.ai.mistral_service',
            'src.services.ai.prompt_manager',
            'src.services.pacer.analytics_service',
            'src.services.pacer.browser_service',
            'src.services.pacer.case_processing_service',
            'src.services.pacer.court_processing_service',
            'src.services.pacer.document_download_service',
            'src.services.pacer.file_management_service',
            'src.services.pacer.html_processing_service',
            'src.services.fb_ads.ad_processing_service',
            'src.services.fb_ads.data_validation_service',
            'src.services.fb_ads.error_handling_service',
            'src.services.fb_ads.workflow_service',
            'src.services.fb_ads.jobs.job_runner_service',
            'src.services.fb_ads.orchestrator',
            'src.services.transformer.law_firm_processor',
            'src.services.transformer.mdl_processor',
            'src.services.transformer.transfer_handler',
            'src.services.uploader.s3_upload_service',
            'src.services.uploader.upload_service',
            'src.services.transformer.file_handler',
            'src.services.transformer.docket_processor',
            'src.services.transformer.component_factory',
            'src.services.law_firms.query_service',
            'src.services.html.case_parser_service',
            'src.services.html.data_updater_service',
            'tests.conftest'
        ]

    container.wire(modules=modules)

def unwire_test_container(container: TestContainer):
    """Unwire the test container."""
    container.unwire()

def override_providers(container: TestContainer, overrides: Dict[str, Any]):
    """
    Override specific providers in the test container.
    
    Args:
        container: The test container
        overrides: Dictionary mapping provider names to override values/factories
        
    Example:
        override_providers(logger=lambda: CustomMockLogger(), deepseek_service=lambda: CustomDeepSeekMock())
    """
    for provider_name, override_value in overrides.items():
        if hasattr(container, provider_name):
            provider = getattr(container, provider_name)
            if callable(override_value):
                provider.override(providers.Factory(override_value))
            else:
                provider.override(providers.Object(override_value))
        else:
            raise AttributeError(f"Container has no provider named '{provider_name}'")
