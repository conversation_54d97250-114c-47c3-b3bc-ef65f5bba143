#!/usr/bin/env python3
"""
Test HTML parsing enhancements with real HTML content from the provided URL.
"""

import sys
import asyncio
import os
import aiohttp
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Change to project root directory
os.chdir(str(project_root))

from src.services.html.case_parser_service import CaseParserService

async def test_real_html_parsing():
    """Test the HTML parsing with real content from the URL."""
    
    # Initialize basic logger
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("test_real_html_parsing")
    
    # Fetch the real HTML content
    url = "https://cdn.lexgenius.ai/20250818/html/ilnd_25_09787_Debra_Muir_v_Godrej_SON_Holdings_Inc.html"
    
    print(f"Fetching HTML content from: {url}")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            if response.status == 200:
                html_content = await response.text()
                print(f"Successfully fetched {len(html_content)} characters of HTML content")
            else:
                print(f"Failed to fetch HTML content: HTTP {response.status}")
                return
    
    # Initialize CaseParserService
    parser = CaseParserService(logger=logger, html_content=html_content)
    
    # Parse the HTML content
    result = parser.parse()
    
    # Print results
    print("\n=== Real HTML Parsing Test Results ===")
    case_info = result.get('case_info', {})
    
    print("=== All Extracted Fields ===")
    for key, value in case_info.items():
        print(f"{key}: {value}")
    
    print("\n=== Target Field Validation ===")
    
    # Test jury_demand
    jury_demand = case_info.get('jury_demand')
    print(f"🎯 Jury Demand: '{jury_demand}'")
    
    # Test is_mdl
    is_mdl = case_info.get('is_mdl')
    print(f"🎯 Is MDL: {is_mdl}")
    
    # Test lead_case
    lead_case = case_info.get('lead_case')
    print(f"🎯 Lead Case: '{lead_case}'")
    
    # Additional information
    plaintiffs = result.get('plaintiffs', [])
    defendants = result.get('defendants', [])
    attorneys = result.get('attorney', [])
    
    print(f"\n=== Additional Parsing Results ===")
    print(f"Plaintiffs found: {len(plaintiffs)}")
    print(f"Defendants found: {len(defendants)}")
    print(f"Attorneys found: {len(attorneys)}")
    
    print("\n=== Test Summary ===")
    success_count = 0
    total_tests = 3
    
    if jury_demand:
        print("✅ Jury Demand extracted successfully")
        success_count += 1
    else:
        print("❌ Jury Demand not found")
    
    if is_mdl is not None:
        print(f"✅ MDL detection completed (result: {is_mdl})")
        success_count += 1
    else:
        print("❌ MDL detection failed")
    
    if lead_case:
        print("✅ Lead Case extracted successfully")
        success_count += 1
    else:
        print("❌ Lead Case not found")
    
    print(f"\nSuccessfully extracted {success_count}/{total_tests} target fields")
    
    if success_count == total_tests:
        print("🎉 All target fields successfully parsed from real HTML!")
    else:
        print("⚠️ Some fields may need additional parsing logic")

def main():
    """Run the test."""
    try:
        asyncio.run(test_real_html_parsing())
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()