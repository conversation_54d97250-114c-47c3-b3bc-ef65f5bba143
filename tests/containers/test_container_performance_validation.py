"""
Container Performance Validation Tests

Tests to ensure container initialization and service resolution meet performance requirements:
- Container initialization under 100ms
- Service resolution under 50ms
- Bulk operations under reasonable time limits
- Memory usage within acceptable bounds
"""

import pytest
import time
import psutil
import os
from contextlib import contextmanager
from unittest.mock import Mock

from src.containers.pacer import Pacer<PERSON>ontainer, get_container


@contextmanager
def performance_timer():
    """Context manager to measure execution time."""
    start_time = time.perf_counter()
    yield lambda: (time.perf_counter() - start_time) * 1000  # Return ms
    

@contextmanager 
def memory_monitor():
    """Context manager to monitor memory usage."""
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    yield lambda: (process.memory_info().rss / 1024 / 1024) - initial_memory  # MB delta


class TestContainerPerformanceRequirements:
    """Test performance requirements for container operations."""
    
    def test_container_initialization_under_100ms(self):
        """Test that PacerContainer initialization completes under 100ms."""
        with performance_timer() as get_duration:
            container = PacerContainer()
            
        duration = get_duration()
        assert duration < 100, f"Container initialization took {duration:.1f}ms, should be < 100ms"
        assert container is not None
    
    def test_get_container_factory_performance(self):
        """Test that get_container() completes within reasonable time."""
        with performance_timer() as get_duration:
            container = get_container()
            
        duration = get_duration()
        # get_container() does more work (config loading, storage setup), so allow more time
        assert duration < 500, f"get_container() took {duration:.1f}ms, should be < 500ms"
        assert container is not None
    
    def test_service_resolution_performance(self):
        """Test that service resolution is fast."""
        # Arrange
        container = PacerContainer()
        container.config.from_dict({'test': True})
        container.logger.override(Mock())
        container.storage_container.override(Mock())
        
        # Test fast service resolution
        with performance_timer() as get_duration:
            config_service = container.configuration_service
            
        duration = get_duration()
        assert duration < 50, f"Service resolution took {duration:.1f}ms, should be < 50ms"
        assert config_service is not None
    
    def test_bulk_service_resolution_performance(self):
        """Test performance when resolving multiple services."""
        # Arrange
        container = PacerContainer()
        container.config.from_dict({'test': True})
        container.logger.override(Mock())
        container.storage_container.override(Mock())
        
        # Define services to test
        services_to_test = [
            'configuration_service',
            'pacer_browser_service', 
            'authentication_service',
            'deepseek_service',
            'case_parser_service',
            'pacer_file_service',
            'pacer_verification_service'
        ]
        
        # Act
        resolved_services = {}
        with performance_timer() as get_duration:
            for service_name in services_to_test:
                try:
                    service_provider = getattr(container, service_name)
                    resolved_services[service_name] = service_provider
                except AttributeError:
                    # Service might not exist, skip
                    pass
                    
        duration = get_duration()
        
        # Assert
        assert duration < 200, f"Bulk service resolution took {duration:.1f}ms, should be < 200ms" 
        assert len(resolved_services) > 0, "Should resolve at least some services"
    
    def test_domain_container_resolution_performance(self):
        """Test that domain container resolution is fast."""
        # Arrange
        container = PacerContainer()
        container.config.from_dict({'test': True})
        container.logger.override(Mock())
        container.storage_container.override(Mock())
        
        # Act
        with performance_timer() as get_duration:
            browser_container = container.browser
            data_container = container.data
            verification_container = container.verification
            orchestration_container = container.orchestration
            
        duration = get_duration()
        
        # Assert
        assert duration < 50, f"Domain container resolution took {duration:.1f}ms, should be < 50ms"
        assert browser_container is not None
        assert data_container is not None  
        assert verification_container is not None
        assert orchestration_container is not None
    
    def test_memory_usage_reasonable(self):
        """Test that container creation doesn't use excessive memory."""
        with memory_monitor() as get_memory_delta:
            # Create multiple containers to test memory usage
            containers = []
            for _ in range(5):
                container = PacerContainer()
                container.config.from_dict({'test': True})
                container.logger.override(Mock())
                container.storage_container.override(Mock())
                containers.append(container)
                
        memory_delta = get_memory_delta()
        
        # Should use less than 50MB for 5 containers
        assert memory_delta < 50, f"Created 5 containers using {memory_delta:.1f}MB, should be < 50MB"
        assert len(containers) == 5
    
    def test_repeated_service_resolution_performance(self):
        """Test that repeated service resolution doesn't degrade performance."""
        # Arrange
        container = PacerContainer()
        container.config.from_dict({'test': True}) 
        container.logger.override(Mock())
        container.storage_container.override(Mock())
        
        # Act - Resolve same service multiple times
        service_name = 'configuration_service'
        durations = []
        
        for _ in range(10):
            with performance_timer() as get_duration:
                service = getattr(container, service_name)
                assert service is not None
            durations.append(get_duration())
        
        # Assert - Performance should be consistent
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        
        assert avg_duration < 10, f"Average service resolution time {avg_duration:.1f}ms should be < 10ms"
        assert max_duration < 50, f"Max service resolution time {max_duration:.1f}ms should be < 50ms"
    
    def test_cross_container_dependency_resolution_performance(self):
        """Test that cross-container dependencies resolve efficiently."""
        # Arrange
        container = PacerContainer()
        container.config.from_dict({'test': True})
        container.logger.override(Mock())
        container.storage_container.override(Mock())
        
        # Act - Test services that have cross-container dependencies
        with performance_timer() as get_duration:
            try:
                # These services have cross-container dependencies
                orchestration_service = container.pacer_orchestrator
                browser_service = container.pacer_browser_service
                
                assert orchestration_service is not None
                assert browser_service is not None
                
            except Exception:
                # Dependencies might fail due to missing external resources
                # But the resolution itself should still be fast
                pass
                
        duration = get_duration()
        
        # Assert
        assert duration < 100, f"Cross-container dependency resolution took {duration:.1f}ms, should be < 100ms"


class TestContainerScalabilityValidation:
    """Test container behavior under various load conditions."""
    
    def test_concurrent_container_creation(self):
        """Test creating multiple containers concurrently."""
        import threading
        import queue
        
        # Arrange
        num_threads = 5
        results = queue.Queue()
        
        def create_container():
            try:
                with performance_timer() as get_duration:
                    container = PacerContainer()
                    container.config.from_dict({'test': True})
                    container.logger.override(Mock())
                    container.storage_container.override(Mock())
                    
                duration = get_duration()
                results.put(('success', duration, container))
            except Exception as e:
                results.put(('error', 0, str(e)))
        
        # Act
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=create_container)
            threads.append(thread)
            thread.start()
            
        for thread in threads:
            thread.join()
        
        # Assert
        successful_creations = 0
        total_duration = 0
        
        while not results.empty():
            status, duration, result = results.get()
            if status == 'success':
                successful_creations += 1
                total_duration += duration
                assert result is not None
            
        assert successful_creations == num_threads, f"Expected {num_threads} successful creations, got {successful_creations}"
        
        avg_duration = total_duration / successful_creations
        assert avg_duration < 200, f"Average concurrent creation time {avg_duration:.1f}ms should be < 200ms"
    
    def test_stress_service_resolution(self):
        """Test service resolution under stress."""
        # Arrange
        container = PacerContainer()
        container.config.from_dict({'test': True})
        container.logger.override(Mock())
        container.storage_container.override(Mock())
        
        # Get list of available services
        service_names = [
            attr for attr in dir(container)
            if not attr.startswith('_') and 
            hasattr(getattr(container, attr), 'provider')
        ]
        
        # Act - Resolve services rapidly
        with performance_timer() as get_duration:
            for _ in range(100):  # 100 iterations
                service_name = service_names[_ % len(service_names)]
                try:
                    service = getattr(container, service_name)
                    assert service is not None
                except Exception:
                    # Some services might fail, that's OK for stress testing
                    pass
                    
        duration = get_duration()
        
        # Assert
        assert duration < 1000, f"Stress test took {duration:.1f}ms, should be < 1000ms"
    
    def test_memory_stability_under_load(self):
        """Test that memory usage remains stable under load."""
        with memory_monitor() as get_memory_delta:
            # Create and destroy containers repeatedly
            for _ in range(20):
                container = PacerContainer()
                container.config.from_dict({'test': True})
                container.logger.override(Mock())
                container.storage_container.override(Mock())
                
                # Resolve some services
                try:
                    config_service = container.configuration_service
                    browser_service = container.pacer_browser_service
                except Exception:
                    pass
                    
                # Let container go out of scope for GC
                del container
                
        memory_delta = get_memory_delta()
        
        # Memory should not grow significantly
        assert memory_delta < 20, f"Memory grew by {memory_delta:.1f}MB under load, should be < 20MB"


class TestContainerResourceEfficiency:
    """Test resource efficiency of container operations."""
    
    def test_lazy_loading_behavior(self):
        """Test that services are loaded lazily, not eagerly."""
        # Arrange - Create container but don't resolve services
        with memory_monitor() as get_memory_delta:
            container = PacerContainer()
            container.config.from_dict({'test': True})
            container.logger.override(Mock())
            container.storage_container.override(Mock())
            
        initial_memory_delta = get_memory_delta()
        
        # Act - Now resolve services
        with memory_monitor() as get_memory_delta_after:
            services = []
            service_names = ['configuration_service', 'pacer_browser_service', 
                           'authentication_service', 'deepseek_service']
            
            for service_name in service_names:
                try:
                    service = getattr(container, service_name)
                    services.append(service)
                except Exception:
                    pass
                    
        service_resolution_memory_delta = get_memory_delta_after()
        
        # Assert - Resolving services should use more memory than just creating container
        # This indicates lazy loading (services created when needed, not upfront)
        assert len(services) > 0, "Should have resolved some services"
        
        # The exact memory comparison depends on implementation, so we just check reasonableness
        assert initial_memory_delta < 20, f"Initial container creation should use < 20MB, used {initial_memory_delta:.1f}MB"
        assert service_resolution_memory_delta >= 0, "Service resolution should use some memory"
    
    def test_service_singleton_behavior(self):
        """Test that singleton services are only created once."""
        # Arrange
        container = PacerContainer()
        container.config.from_dict({'test': True})
        container.logger.override(Mock())
        container.storage_container.override(Mock())
        
        # Act - Resolve same service multiple times
        service_name = 'configuration_service'
        
        services = []
        for _ in range(5):
            try:
                service = getattr(container, service_name)()  # Call to get instance
                services.append(service)
            except Exception:
                pass
        
        # Assert - For factory services, instances might be different
        # For singleton services, instances should be the same
        # This test mainly checks that multiple resolutions work
        assert len(services) > 0, "Should resolve at least one service instance"
    
    def test_container_cleanup_efficiency(self):
        """Test that container cleanup is efficient."""
        containers = []
        
        # Create containers
        with memory_monitor() as get_memory_delta:
            for _ in range(10):
                container = PacerContainer()
                container.config.from_dict({'test': True})
                container.logger.override(Mock()) 
                container.storage_container.override(Mock())
                containers.append(container)
                
        creation_memory_delta = get_memory_delta()
        
        # Clean up containers  
        with memory_monitor() as get_memory_delta_cleanup:
            containers.clear()  # Remove references
            
        cleanup_memory_delta = get_memory_delta_cleanup()
        
        # Assert
        assert creation_memory_delta > 0, "Creating containers should use memory"
        # Cleanup might not immediately free memory due to GC, so we don't assert on cleanup_memory_delta