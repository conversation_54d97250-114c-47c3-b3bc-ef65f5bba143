"""
PACER Performance Benchmarks - HiveTester-1
Performance testing and benchmarking for PACER system components.
"""

import pytest
import asyncio
import time
import psutil
import os
from unittest.mock import Mock, AsyncMock
from contextlib import asynccontextmanager
from dataclasses import dataclass
from typing import List, Dict, Any
import statistics


@dataclass
class PerformanceMetrics:
    """Performance metrics collection."""
    operation_name: str
    duration: float
    memory_usage: int
    cpu_percent: float
    throughput: float
    success_rate: float
    error_count: int
    
    @property
    def operations_per_second(self) -> float:
        return 1.0 / self.duration if self.duration > 0 else 0.0


class PerformanceMonitor:
    """Performance monitoring utility."""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.start_time = None
        self.start_memory = None
        self.start_cpu = None
        
    @asynccontextmanager
    async def measure(self, operation_name: str):
        """Context manager for measuring performance."""
        self.start_time = time.perf_counter()
        self.start_memory = self.process.memory_info().rss
        self.start_cpu = self.process.cpu_percent()
        
        try:
            yield self
        finally:
            end_time = time.perf_counter()
            end_memory = self.process.memory_info().rss
            end_cpu = self.process.cpu_percent()
            
            self.last_metrics = PerformanceMetrics(
                operation_name=operation_name,
                duration=end_time - self.start_time,
                memory_usage=end_memory - self.start_memory,
                cpu_percent=end_cpu,
                throughput=0.0,  # Will be calculated separately
                success_rate=100.0,  # Default success
                error_count=0
            )


@pytest.fixture
def performance_monitor():
    """Provide performance monitoring."""
    return PerformanceMonitor()


@pytest.fixture
def mock_large_dataset():
    """Generate large dataset for performance testing."""
    return [
        {
            "case_id": f"case_{i:06d}",
            "case_number": f"1:23-cv-{i:05d}",
            "title": f"Test Case {i} v. Defendant {i}",
            "filed_date": f"2023-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}",
            "documents": [
                {"doc_id": f"doc_{i}_{j}", "description": f"Document {j}"}
                for j in range(3)  # 3 documents per case
            ]
        }
        for i in range(1000)  # 1000 cases
    ]


@pytest.mark.performance
class TestPacerPerformanceBenchmarks:
    """Performance benchmarks for PACER system."""

    class TestSearchPerformance:
        """Performance tests for search operations."""

        @pytest.mark.asyncio
        async def test_case_search_performance(self, test_container, performance_monitor, mock_large_dataset):
            """Benchmark case search performance."""
            court_service = test_container.pacer.orchestration.court_processing_service()
            
            # Mock search with realistic delay
            async def mock_search(*args, **kwargs):
                await asyncio.sleep(0.1)  # Simulate network/processing time
                return mock_large_dataset[:100]  # Return subset
            
            court_service.search_cases = AsyncMock(side_effect=mock_search)
            
            # Benchmark search operations
            search_times = []
            for i in range(10):
                async with performance_monitor.measure("case_search"):
                    results = await court_service.search_cases({
                        "court": "nysd",
                        "query": f"test_query_{i}"
                    })
                    
                search_times.append(performance_monitor.last_metrics.duration)
                assert len(results) == 100
            
            # Performance assertions
            avg_time = statistics.mean(search_times)
            max_time = max(search_times)
            
            assert avg_time < 0.2, f"Average search time {avg_time:.3f}s exceeds 0.2s threshold"
            assert max_time < 0.5, f"Max search time {max_time:.3f}s exceeds 0.5s threshold"
            
            # Calculate throughput
            throughput = 100 / avg_time  # cases per second
            assert throughput > 500, f"Throughput {throughput:.1f} cases/sec below 500 threshold"

        @pytest.mark.asyncio
        async def test_concurrent_search_performance(self, test_container, performance_monitor):
            """Benchmark concurrent search operations."""
            court_service = test_container.pacer.orchestration.court_processing_service()
            
            # Mock concurrent searches
            async def mock_concurrent_search(query_id):
                await asyncio.sleep(0.05)  # Simulate processing
                return [{"case_id": f"result_{query_id}_{i}"} for i in range(10)]
            
            court_service.search_cases = AsyncMock(side_effect=mock_concurrent_search)
            
            # Test with different concurrency levels
            concurrency_levels = [1, 5, 10, 20]
            results = {}
            
            for concurrency in concurrency_levels:
                async with performance_monitor.measure(f"concurrent_search_{concurrency}"):
                    tasks = [
                        court_service.search_cases(f"query_{i}")
                        for i in range(concurrency)
                    ]
                    concurrent_results = await asyncio.gather(*tasks)
                
                total_results = sum(len(result) for result in concurrent_results)
                throughput = total_results / performance_monitor.last_metrics.duration
                
                results[concurrency] = {
                    "duration": performance_monitor.last_metrics.duration,
                    "throughput": throughput,
                    "memory_usage": performance_monitor.last_metrics.memory_usage
                }
            
            # Verify scalability
            assert results[5]["throughput"] > results[1]["throughput"] * 3
            assert results[10]["throughput"] > results[5]["throughput"] * 1.5

        @pytest.mark.asyncio
        async def test_search_memory_efficiency(self, test_container, performance_monitor, mock_large_dataset):
            """Test memory efficiency of search operations."""
            court_service = test_container.pacer.orchestration.court_processing_service()
            
            # Mock memory-intensive search
            court_service.search_cases = AsyncMock(return_value=mock_large_dataset)
            
            initial_memory = psutil.Process().memory_info().rss
            
            # Perform multiple searches
            for i in range(5):
                async with performance_monitor.measure("memory_search"):
                    results = await court_service.search_cases({"large_query": i})
                    
                memory_growth = performance_monitor.last_metrics.memory_usage
                
                # Memory growth should be reasonable
                assert memory_growth < 50 * 1024 * 1024, f"Memory growth {memory_growth / 1024 / 1024:.1f}MB too high"
            
            final_memory = psutil.Process().memory_info().rss
            total_growth = final_memory - initial_memory
            
            # Total memory growth should be controlled
            assert total_growth < 100 * 1024 * 1024, f"Total memory growth {total_growth / 1024 / 1024:.1f}MB excessive"

    class TestDownloadPerformance:
        """Performance tests for download operations."""

        @pytest.mark.asyncio
        async def test_document_download_throughput(self, test_container, performance_monitor):
            """Benchmark document download throughput."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock download operations with realistic timing
            async def mock_download(doc_info):
                file_size = doc_info.get("size", 1024 * 100)  # 100KB default
                delay = file_size / (1024 * 1024 * 10)  # 10MB/s simulated speed
                await asyncio.sleep(delay)
                return {
                    "success": True,
                    "file_path": f"/tmp/{doc_info['doc_id']}.pdf",
                    "size": file_size
                }
            
            download_service.download_document = AsyncMock(side_effect=mock_download)
            
            # Test documents of varying sizes
            test_documents = [
                {"doc_id": f"small_{i}", "size": 50 * 1024}  # 50KB
                for i in range(20)
            ] + [
                {"doc_id": f"large_{i}", "size": 5 * 1024 * 1024}  # 5MB
                for i in range(5)
            ]
            
            async with performance_monitor.measure("download_throughput"):
                download_tasks = [
                    download_service.download_document(doc)
                    for doc in test_documents
                ]
                results = await asyncio.gather(*download_tasks)
            
            # Calculate throughput metrics
            total_size = sum(doc["size"] for doc in test_documents)
            duration = performance_monitor.last_metrics.duration
            throughput_mbps = (total_size / (1024 * 1024)) / duration
            
            successful_downloads = sum(1 for result in results if result["success"])
            
            assert successful_downloads == len(test_documents)
            assert throughput_mbps > 5.0, f"Download throughput {throughput_mbps:.2f} MB/s below 5 MB/s threshold"

        @pytest.mark.asyncio
        async def test_concurrent_download_performance(self, test_container, performance_monitor):
            """Benchmark concurrent download performance."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock concurrent downloads with backpressure
            semaphore = asyncio.Semaphore(5)  # Limit concurrent downloads
            
            async def mock_concurrent_download(doc_id):
                async with semaphore:
                    await asyncio.sleep(0.1)  # Simulate download time
                    return {"doc_id": doc_id, "success": True, "size": 1024 * 100}
            
            download_service.download_document = AsyncMock(side_effect=mock_concurrent_download)
            
            # Test increasing batch sizes
            batch_sizes = [10, 25, 50, 100]
            performance_results = {}
            
            for batch_size in batch_sizes:
                documents = [{"doc_id": f"batch_{batch_size}_{i}"} for i in range(batch_size)]
                
                async with performance_monitor.measure(f"concurrent_download_{batch_size}"):
                    tasks = [
                        download_service.download_document(doc)
                        for doc in documents
                    ]
                    results = await asyncio.gather(*tasks)
                
                success_rate = sum(1 for r in results if r["success"]) / len(results) * 100
                throughput = len(results) / performance_monitor.last_metrics.duration
                
                performance_results[batch_size] = {
                    "duration": performance_monitor.last_metrics.duration,
                    "throughput": throughput,
                    "success_rate": success_rate
                }
                
                assert success_rate >= 99.0, f"Success rate {success_rate:.1f}% below 99% threshold"
            
            # Verify performance scaling
            assert performance_results[50]["throughput"] > performance_results[10]["throughput"] * 2

        @pytest.mark.asyncio
        async def test_download_error_recovery_performance(self, test_container, performance_monitor):
            """Test performance under error conditions."""
            download_service = test_container.pacer.verification.pacer_download_service()
            
            # Mock downloads with intermittent failures
            failure_rate = 0.2  # 20% failure rate
            retry_count = 0
            
            async def mock_unreliable_download(doc_info):
                nonlocal retry_count
                retry_count += 1
                
                if retry_count % 5 == 0:  # Every 5th call fails
                    raise Exception("Simulated network error")
                
                await asyncio.sleep(0.05)
                return {"doc_id": doc_info["doc_id"], "success": True}
            
            download_service.download_with_retry = AsyncMock(side_effect=mock_unreliable_download)
            
            documents = [{"doc_id": f"error_test_{i}"} for i in range(50)]
            
            async with performance_monitor.measure("error_recovery"):
                successful_downloads = 0
                failed_downloads = 0
                
                for doc in documents:
                    try:
                        await download_service.download_with_retry(doc)
                        successful_downloads += 1
                    except Exception:
                        failed_downloads += 1
            
            success_rate = successful_downloads / len(documents) * 100
            error_rate = failed_downloads / len(documents) * 100
            
            # Should handle errors gracefully with good performance
            assert performance_monitor.last_metrics.duration < 5.0
            assert success_rate >= 80.0  # Should recover from most errors

    class TestDataProcessingPerformance:
        """Performance tests for data processing operations."""

        @pytest.mark.asyncio
        async def test_html_processing_performance(self, test_container, performance_monitor):
            """Benchmark HTML processing performance."""
            html_service = test_container.pacer.data.html_processing_service()
            
            # Mock HTML processing with realistic complexity
            large_html_content = "<html>" + "<div>Case data</div>" * 1000 + "</html>"
            
            async def mock_html_processing(content):
                # Simulate CPU-intensive processing
                await asyncio.sleep(0.01)  # Base processing time
                complexity_factor = len(content) / 10000  # Scale with content size
                await asyncio.sleep(complexity_factor * 0.001)
                
                return {
                    "extracted_fields": ["case_number", "title", "parties"],
                    "processing_time": 0.01 + complexity_factor * 0.001,
                    "content_size": len(content)
                }
            
            html_service.process_html = AsyncMock(side_effect=mock_html_processing)
            
            # Test different content sizes
            content_sizes = [1000, 5000, 10000, 50000]  # Characters
            processing_times = []
            
            for size in content_sizes:
                content = large_html_content[:size]
                
                async with performance_monitor.measure(f"html_processing_{size}"):
                    result = await html_service.process_html(content)
                
                processing_time = performance_monitor.last_metrics.duration
                processing_times.append(processing_time)
                
                # Performance should scale reasonably with content size
                chars_per_second = size / processing_time
                assert chars_per_second > 10000, f"Processing speed {chars_per_second:.0f} chars/sec too slow"
            
            # Verify linear scaling (approximately)
            assert processing_times[-1] < processing_times[0] * 10  # Should not be more than 10x slower

        @pytest.mark.asyncio
        async def test_bulk_data_transformation_performance(self, test_container, performance_monitor, mock_large_dataset):
            """Benchmark bulk data transformation performance."""
            analytics_service = test_container.pacer.data.analytics_service()
            
            # Mock bulk transformation
            async def mock_transform_batch(data_batch):
                await asyncio.sleep(len(data_batch) * 0.001)  # Scale with batch size
                return [
                    {
                        "case_id": item["case_id"],
                        "transformed": True,
                        "fields_extracted": 5,
                        "processing_time": 0.001
                    }
                    for item in data_batch
                ]
            
            analytics_service.transform_data_batch = AsyncMock(side_effect=mock_transform_batch)
            
            # Test different batch sizes
            batch_sizes = [10, 50, 100, 500]
            batch_performance = {}
            
            for batch_size in batch_sizes:
                batch_data = mock_large_dataset[:batch_size]
                
                async with performance_monitor.measure(f"transform_batch_{batch_size}"):
                    results = await analytics_service.transform_data_batch(batch_data)
                
                records_per_second = len(results) / performance_monitor.last_metrics.duration
                batch_performance[batch_size] = {
                    "duration": performance_monitor.last_metrics.duration,
                    "throughput": records_per_second,
                    "memory_usage": performance_monitor.last_metrics.memory_usage
                }
                
                assert len(results) == batch_size
                assert records_per_second > 100, f"Throughput {records_per_second:.1f} records/sec below 100 threshold"
            
            # Verify efficient batching
            assert batch_performance[500]["throughput"] > batch_performance[10]["throughput"] * 2

    class TestConcurrencyPerformance:
        """Performance tests for concurrent operations."""

        @pytest.mark.asyncio
        async def test_browser_session_concurrency(self, test_container, performance_monitor):
            """Test browser session concurrency performance."""
            browser_service = test_container.pacer.browser.pacer_browser_service()
            
            # Mock browser session management
            active_sessions = {}
            
            async def mock_create_session(session_id):
                await asyncio.sleep(0.1)  # Session creation time
                active_sessions[session_id] = {
                    "created_at": time.time(),
                    "requests": 0
                }
                return session_id
            
            async def mock_process_request(session_id, request):
                if session_id in active_sessions:
                    active_sessions[session_id]["requests"] += 1
                    await asyncio.sleep(0.02)  # Request processing time
                    return {"session": session_id, "result": "success"}
                raise Exception("Session not found")
            
            browser_service.create_session = AsyncMock(side_effect=mock_create_session)
            browser_service.process_request = AsyncMock(side_effect=mock_process_request)
            
            # Test concurrent session creation and usage
            num_sessions = 10
            requests_per_session = 5
            
            async with performance_monitor.measure("concurrent_sessions"):
                # Create sessions concurrently
                session_creation_tasks = [
                    browser_service.create_session(f"session_{i}")
                    for i in range(num_sessions)
                ]
                session_ids = await asyncio.gather(*session_creation_tasks)
                
                # Process requests concurrently across all sessions
                request_tasks = []
                for session_id in session_ids:
                    for req_num in range(requests_per_session):
                        task = browser_service.process_request(session_id, f"request_{req_num}")
                        request_tasks.append(task)
                
                results = await asyncio.gather(*request_tasks)
            
            total_requests = num_sessions * requests_per_session
            requests_per_second = total_requests / performance_monitor.last_metrics.duration
            
            assert len(results) == total_requests
            assert requests_per_second > 20, f"Request throughput {requests_per_second:.1f} req/sec below 20 threshold"
            assert len(active_sessions) == num_sessions

        @pytest.mark.asyncio
        async def test_resource_pool_performance(self, test_container, performance_monitor):
            """Test resource pool management performance."""
            # Mock resource pool (e.g., database connections, browser contexts)
            resource_pool = asyncio.Queue(maxsize=5)
            
            # Initialize pool
            for i in range(5):
                await resource_pool.put(f"resource_{i}")
            
            async def mock_use_resource(operation_id):
                # Acquire resource
                resource = await resource_pool.get()
                try:
                    # Simulate work with resource
                    await asyncio.sleep(0.05)
                    return {"operation": operation_id, "resource": resource, "success": True}
                finally:
                    # Return resource to pool
                    await resource_pool.put(resource)
            
            # Test high concurrency with limited resources
            num_operations = 50
            
            async with performance_monitor.measure("resource_pool"):
                tasks = [
                    mock_use_resource(f"op_{i}")
                    for i in range(num_operations)
                ]
                results = await asyncio.gather(*tasks)
            
            success_rate = sum(1 for r in results if r["success"]) / len(results) * 100
            operations_per_second = len(results) / performance_monitor.last_metrics.duration
            
            assert success_rate == 100.0
            assert operations_per_second > 15  # Should handle queuing efficiently
            assert resource_pool.qsize() == 5  # All resources returned

    class TestMemoryAndResourceUsage:
        """Performance tests for memory and resource usage."""

        @pytest.mark.asyncio
        async def test_memory_leak_detection(self, test_container, performance_monitor):
            """Test for memory leaks during extended operations."""
            court_service = test_container.pacer.orchestration.court_processing_service()
            
            # Mock operations that might cause memory leaks
            async def mock_process_case(case_data):
                # Simulate some data processing
                processed_data = {
                    "case_id": case_data["case_id"],
                    "processed_at": time.time(),
                    "large_data": "x" * 1000  # Some data that should be cleaned up
                }
                await asyncio.sleep(0.01)
                return processed_data
            
            court_service.process_case = AsyncMock(side_effect=mock_process_case)
            
            initial_memory = psutil.Process().memory_info().rss
            memory_measurements = []
            
            # Run many iterations to detect leaks
            for iteration in range(20):
                batch_size = 50
                case_batch = [{"case_id": f"iter_{iteration}_case_{i}"} for i in range(batch_size)]
                
                async with performance_monitor.measure(f"iteration_{iteration}"):
                    tasks = [court_service.process_case(case) for case in case_batch]
                    await asyncio.gather(*tasks)
                
                current_memory = psutil.Process().memory_info().rss
                memory_growth = current_memory - initial_memory
                memory_measurements.append(memory_growth)
                
                # Force garbage collection
                import gc
                gc.collect()
            
            # Analyze memory growth pattern
            final_memory_growth = memory_measurements[-1]
            average_growth = statistics.mean(memory_measurements[10:])  # Exclude warmup
            
            # Memory growth should stabilize (not grow indefinitely)
            growth_rate = (memory_measurements[-1] - memory_measurements[10]) / 10
            assert growth_rate < 1024 * 1024, f"Memory growth rate {growth_rate / 1024 / 1024:.2f} MB/iteration too high"
            
            # Total memory growth should be reasonable
            assert final_memory_growth < 200 * 1024 * 1024, f"Total memory growth {final_memory_growth / 1024 / 1024:.1f} MB excessive"

        @pytest.mark.asyncio
        async def test_cpu_usage_efficiency(self, test_container, performance_monitor):
            """Test CPU usage efficiency under load."""
            court_service = test_container.pacer.orchestration.court_processing_service()
            
            # Mock CPU-intensive operations
            async def mock_cpu_intensive_task(task_id):
                # Simulate CPU work with some async waiting
                start_time = time.perf_counter()
                
                # CPU-bound work simulation
                result = sum(i * i for i in range(1000))
                
                # Some async I/O simulation
                await asyncio.sleep(0.01)
                
                end_time = time.perf_counter()
                return {
                    "task_id": task_id,
                    "result": result,
                    "cpu_time": end_time - start_time
                }
            
            court_service.cpu_intensive_task = AsyncMock(side_effect=mock_cpu_intensive_task)
            
            # Test different concurrency levels
            concurrency_levels = [1, 2, 4, 8]
            cpu_efficiency = {}
            
            for concurrency in concurrency_levels:
                tasks_per_level = 20
                
                async with performance_monitor.measure(f"cpu_test_{concurrency}"):
                    tasks = [
                        court_service.cpu_intensive_task(f"conc_{concurrency}_task_{i}")
                        for i in range(tasks_per_level)
                    ]
                    results = await asyncio.gather(*tasks)
                
                total_cpu_time = sum(r["cpu_time"] for r in results)
                wall_clock_time = performance_monitor.last_metrics.duration
                cpu_efficiency_ratio = total_cpu_time / wall_clock_time
                
                cpu_efficiency[concurrency] = {
                    "wall_clock_time": wall_clock_time,
                    "total_cpu_time": total_cpu_time,
                    "efficiency_ratio": cpu_efficiency_ratio,
                    "cpu_percent": performance_monitor.last_metrics.cpu_percent
                }
                
                assert len(results) == tasks_per_level
            
            # Higher concurrency should improve CPU utilization up to a point
            assert cpu_efficiency[4]["efficiency_ratio"] > cpu_efficiency[1]["efficiency_ratio"]


@pytest.mark.load
class TestPacerLoadTesting:
    """Load testing for PACER system under stress."""

    @pytest.mark.asyncio
    async def test_sustained_load_performance(self, test_container, performance_monitor):
        """Test system performance under sustained load."""
        court_service = test_container.pacer.orchestration.court_processing_service()
        download_service = test_container.pacer.verification.pacer_download_service()
        
        # Mock sustained operations
        operation_count = 0
        
        async def mock_sustained_operation(operation_type, data):
            nonlocal operation_count
            operation_count += 1
            
            if operation_type == "search":
                await asyncio.sleep(0.02)
                return [{"case_id": f"load_test_case_{operation_count}"}]
            elif operation_type == "download":
                await asyncio.sleep(0.05)
                return {"success": True, "file_size": 1024}
        
        court_service.search_cases = AsyncMock(side_effect=lambda x: mock_sustained_operation("search", x))
        download_service.download_document = AsyncMock(side_effect=lambda x: mock_sustained_operation("download", x))
        
        # Run sustained load for extended period
        load_duration = 10  # seconds
        start_time = time.time()
        
        async with performance_monitor.measure("sustained_load"):
            operations = []
            
            while time.time() - start_time < load_duration:
                # Mix of operations
                search_task = court_service.search_cases({"query": "load_test"})
                download_task = download_service.download_document({"doc_id": "load_test"})
                
                operations.extend([search_task, download_task])
                
                # Don't overwhelm the system
                await asyncio.sleep(0.1)
            
            # Wait for all operations to complete
            results = await asyncio.gather(*operations)
        
        operations_per_second = len(results) / performance_monitor.last_metrics.duration
        
        # System should maintain reasonable performance under sustained load
        assert operations_per_second > 10
        assert performance_monitor.last_metrics.duration < load_duration + 2  # Allow for completion time
        
        # Memory usage should be stable
        assert performance_monitor.last_metrics.memory_usage < 100 * 1024 * 1024  # <100MB growth

    @pytest.mark.asyncio
    async def test_burst_load_handling(self, test_container, performance_monitor):
        """Test system response to burst loads."""
        court_service = test_container.pacer.orchestration.court_processing_service()
        
        # Mock burst processing
        async def mock_burst_operation(burst_id, item_id):
            await asyncio.sleep(0.01)
            return {"burst": burst_id, "item": item_id, "processed": True}
        
        court_service.process_item = AsyncMock(side_effect=mock_burst_operation)
        
        # Test different burst sizes
        burst_sizes = [10, 50, 100, 200]
        burst_results = {}
        
        for burst_size in burst_sizes:
            async with performance_monitor.measure(f"burst_{burst_size}"):
                # Create burst of operations
                burst_tasks = [
                    court_service.process_item(burst_size, i)
                    for i in range(burst_size)
                ]
                
                results = await asyncio.gather(*burst_tasks)
            
            burst_results[burst_size] = {
                "duration": performance_monitor.last_metrics.duration,
                "throughput": len(results) / performance_monitor.last_metrics.duration,
                "success_count": len(results),
                "memory_usage": performance_monitor.last_metrics.memory_usage
            }
            
            assert len(results) == burst_size
        
        # System should handle larger bursts efficiently
        assert burst_results[200]["throughput"] > burst_results[10]["throughput"] * 5
        
        # Memory usage should scale reasonably
        memory_ratio = burst_results[200]["memory_usage"] / burst_results[10]["memory_usage"]
        assert memory_ratio < 5  # Should not use 5x more memory for 20x more items