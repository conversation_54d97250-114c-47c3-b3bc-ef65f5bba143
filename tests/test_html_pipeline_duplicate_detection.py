#!/usr/bin/env python3
"""
Comprehensive test suite for duplicate detection changes in process_html_pipeline.py

This test validates the modified logic around lines 245-270 in extract_case_data() method,
ensuring:
1. DynamoDB check happens before scraping
2. Dockets existing in DynamoDB with court_id='njd' are filtered out
3. Pipeline skips scraping for existing dockets
4. Error handling works if DynamoDB check fails
5. Performance improvement (no unnecessary scraping)
"""

import asyncio
import pytest
import sqlite3
import tempfile
import os
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch, call
from typing import List, Dict, Any

import sys
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Import the class under test
from scripts.analysis.talc.process_html_pipeline import HTMLPipeline


class TestHTMLPipelineDuplicateDetection:
    """Test suite for duplicate detection functionality in HTMLPipeline"""
    
    @pytest.fixture
    def mock_html_content(self):
        """Sample HTML content with Johnson & Johnson cases"""
        return """
        <!DOCTYPE html>
        <html>
        <body>
            <table>
                <tr>
                    <td>
                        <a href="/public/case/12345/">JOHNSON & JOHNSON TALC CASE</a>
                        <span>Case Num:</span>
                        <span class="bkattrib">2:20-cv-12345</span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <a href="/public/case/67890/">JOHNSON & JOHNSON ANOTHER TALC CASE</a>
                        <span>Case Num:</span>
                        <span class="bkattrib">2:21-cv-67890</span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <a href="/public/case/11111/">JOHNSON & JOHNSON EXISTING CASE</a>
                        <span>Case Num:</span>
                        <span class="bkattrib">2:19-cv-11111</span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <a href="/public/case/22222/">Non-JJ Case Should Be Filtered</a>
                        <span>Case Num:</span>
                        <span class="bkattrib">2:22-cv-22222</span>
                    </td>
                </tr>
            </table>
        </body>
        </html>
        """
    
    @pytest.fixture
    def temp_html_file(self, mock_html_content):
        """Create temporary HTML file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
            f.write(mock_html_content)
            temp_file = f.name
        yield temp_file
        os.unlink(temp_file)
    
    @pytest.fixture
    def temp_db_file(self):
        """Create temporary SQLite database"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_db = f.name
        
        # Create the database schema
        conn = sqlite3.connect(temp_db)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS pacermon_searches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                docket_num TEXT NOT NULL,
                search_query TEXT NOT NULL,
                results TEXT,
                status TEXT,
                case_data TEXT,
                html_file_path TEXT,
                exists_dynamo INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Insert test data - some cases already exist in SQLite with DynamoDB flags
        test_data = [
            ('2:19-cv-11111', 'test query', '{}', 'success', '{}', None, 1),  # Exists in DynamoDB
            ('2:18-cv-88888', 'test query', '{}', 'success', '{}', None, 0),  # Not in DynamoDB
        ]
        
        cursor.executemany("""
            INSERT INTO pacermon_searches 
            (docket_num, search_query, results, status, case_data, html_file_path, exists_dynamo)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, test_data)
        
        conn.commit()
        conn.close()
        
        yield temp_db
        os.unlink(temp_db)
    
    @pytest.fixture
    def pipeline(self, temp_html_file):
        """Create HTMLPipeline instance"""
        return HTMLPipeline(temp_html_file)
    
    @pytest.fixture
    def mock_pacer_repo(self):
        """Mock PacerRepository"""
        mock_repo = AsyncMock()
        mock_repo.check_docket_exists = AsyncMock()
        return mock_repo
    
    @pytest.fixture
    def mock_dynamo_storage(self):
        """Mock AsyncDynamoDBStorage"""
        mock_storage = AsyncMock()
        mock_storage.__aenter__ = AsyncMock(return_value=mock_storage)
        return mock_storage

    @pytest.mark.asyncio
    async def test_sqlite_filtering_before_html_parsing(self, pipeline, temp_db_file, mock_pacer_repo):
        """Test 1: Verify SQLite filtering happens before HTML parsing"""
        
        # Mock the database path
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            with patch.object(pipeline, '_init_dynamo_if_needed', AsyncMock()):
                pipeline.pacer_repo = mock_pacer_repo
                mock_pacer_repo.check_docket_exists.return_value = False
                
                cases = await pipeline.extract_case_data()
                
                # Verify that case 2:19-cv-11111 was filtered out (exists_dynamo = 1)
                docket_nums = [case['docket_num'] for case in cases]
                assert '2:19-cv-11111' not in docket_nums
                
                # Verify other Johnson & Johnson cases are present
                assert '2:20-cv-12345' in docket_nums
                assert '2:21-cv-67890' in docket_nums

    @pytest.mark.asyncio
    async def test_dynamodb_check_before_scraping(self, pipeline, temp_db_file, mock_pacer_repo, mock_dynamo_storage):
        """Test 2: Verify DynamoDB check happens before scraping for each case"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            with patch.object(pipeline, '_init_dynamo_if_needed', AsyncMock()) as mock_init:
                pipeline.pacer_repo = mock_pacer_repo
                pipeline.dynamo_storage = mock_dynamo_storage
                
                # Configure mock to return True for one case (should be filtered)
                async def mock_check_docket_exists(court_id, docket_num):
                    if docket_num == '2:20-cv-12345':
                        return True  # This case exists in DynamoDB
                    return False
                
                mock_pacer_repo.check_docket_exists.side_effect = mock_check_docket_exists
                
                cases = await pipeline.extract_case_data()
                
                # Verify _init_dynamo_if_needed was called
                mock_init.assert_called()
                
                # Verify DynamoDB checks were made with correct parameters
                expected_calls = [
                    call('njd', '2:20-cv-12345'),
                    call('njd', '2:21-cv-67890'),
                ]
                
                # Check that DynamoDB was queried for all relevant cases
                actual_calls = mock_pacer_repo.check_docket_exists.call_args_list
                for expected_call in expected_calls:
                    assert expected_call in actual_calls
                
                # Verify case that exists in DynamoDB was filtered out
                docket_nums = [case['docket_num'] for case in cases]
                assert '2:20-cv-12345' not in docket_nums  # Should be filtered
                assert '2:21-cv-67890' in docket_nums      # Should remain

    @pytest.mark.asyncio
    async def test_court_id_njd_filtering(self, pipeline, temp_db_file, mock_pacer_repo):
        """Test 3: Verify dockets existing in DynamoDB with court_id='njd' are filtered out"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            with patch.object(pipeline, '_init_dynamo_if_needed', AsyncMock()):
                pipeline.pacer_repo = mock_pacer_repo
                
                # Mock all cases as existing in DynamoDB with court_id='njd'
                mock_pacer_repo.check_docket_exists.return_value = True
                
                cases = await pipeline.extract_case_data()
                
                # All Johnson & Johnson cases should be filtered out
                assert len(cases) == 0
                
                # Verify all calls used court_id='njd'
                for call_args in mock_pacer_repo.check_docket_exists.call_args_list:
                    args, kwargs = call_args
                    assert args[0] == 'njd'  # First argument should be 'njd'

    @pytest.mark.asyncio
    async def test_pipeline_skips_scraping_for_existing_dockets(self, pipeline, temp_db_file, mock_pacer_repo):
        """Test 4: Verify pipeline skips scraping for existing dockets"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            with patch.object(pipeline, '_init_dynamo_if_needed', AsyncMock()):
                pipeline.pacer_repo = mock_pacer_repo
                
                # Configure some cases to exist in DynamoDB
                existing_dockets = {'2:20-cv-12345', '2:21-cv-67890'}
                
                async def mock_check_exists(court_id, docket_num):
                    return docket_num in existing_dockets
                
                mock_pacer_repo.check_docket_exists.side_effect = mock_check_exists
                
                cases = await pipeline.extract_case_data()
                
                # Verify existing dockets are not in the cases list for scraping
                docket_nums = set(case['docket_num'] for case in cases)
                for existing_docket in existing_dockets:
                    assert existing_docket not in docket_nums
                
                # Verify the pipeline would only scrape non-existing cases
                assert len(cases) == 0  # All J&J cases are marked as existing

    @pytest.mark.asyncio
    async def test_error_handling_dynamodb_check_failure(self, pipeline, temp_db_file, mock_pacer_repo):
        """Test 5: Verify error handling works if DynamoDB check fails"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            with patch.object(pipeline, '_init_dynamo_if_needed', AsyncMock()):
                pipeline.pacer_repo = mock_pacer_repo
                
                # Configure DynamoDB check to raise an exception
                mock_pacer_repo.check_docket_exists.side_effect = Exception("DynamoDB connection failed")
                
                # This should not raise an exception - pipeline should continue
                cases = await pipeline.extract_case_data()
                
                # Verify that cases are still processed despite DynamoDB failure
                # (fallback behavior - don't block pipeline)
                assert len(cases) > 0
                
                # Verify all Johnson & Johnson cases are present when DynamoDB fails
                docket_nums = [case['docket_num'] for case in cases]
                assert '2:20-cv-12345' in docket_nums
                assert '2:21-cv-67890' in docket_nums

    @pytest.mark.asyncio
    async def test_performance_improvement_no_unnecessary_scraping(self, pipeline, temp_db_file, mock_pacer_repo):
        """Test 6: Verify performance improvement by avoiding unnecessary scraping"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            with patch.object(pipeline, '_init_dynamo_if_needed', AsyncMock()) as mock_init:
                pipeline.pacer_repo = mock_pacer_repo
                
                # Track the number of DynamoDB calls
                call_count = 0
                
                async def counting_check_exists(court_id, docket_num):
                    nonlocal call_count
                    call_count += 1
                    return True  # All cases exist - should skip all
                
                mock_pacer_repo.check_docket_exists.side_effect = counting_check_exists
                
                cases = await pipeline.extract_case_data()
                
                # Verify no cases returned (all filtered as existing)
                assert len(cases) == 0
                
                # Verify DynamoDB was only called for cases that passed initial filtering
                # Should be called for each Johnson & Johnson case not already in SQLite
                assert call_count == 2  # 2:20-cv-12345 and 2:21-cv-67890
                
                # Verify initialization happened (may be called multiple times in loop, that's OK)
                assert mock_init.call_count >= 1

    @pytest.mark.asyncio
    async def test_async_changes_work_correctly(self, pipeline, temp_db_file, mock_pacer_repo, mock_dynamo_storage):
        """Test 7: Verify async changes work correctly"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            # Test that _init_dynamo_if_needed properly initializes async resources
            with patch('scripts.analysis.talc.process_html_pipeline.AsyncDynamoDBStorage', return_value=mock_dynamo_storage):
                with patch('scripts.analysis.talc.process_html_pipeline.PacerRepository', return_value=mock_pacer_repo):
                    
                    # Initially, pacer_repo should be None
                    assert pipeline.pacer_repo is None
                    assert pipeline.dynamo_storage is None
                    
                    # Call _init_dynamo_if_needed
                    await pipeline._init_dynamo_if_needed()
                    
                    # Verify async resources are initialized
                    assert pipeline.dynamo_storage is not None
                    assert pipeline.pacer_repo is not None
                    
                    # Verify async context manager was entered
                    mock_dynamo_storage.__aenter__.assert_called_once()

    @pytest.mark.asyncio
    async def test_case_filtering_johnson_and_johnson_only(self, pipeline, temp_db_file, mock_pacer_repo):
        """Test 8: Verify only Johnson & Johnson cases are processed"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            with patch.object(pipeline, '_init_dynamo_if_needed', AsyncMock()):
                pipeline.pacer_repo = mock_pacer_repo
                mock_pacer_repo.check_docket_exists.return_value = False
                
                cases = await pipeline.extract_case_data()
                
                # Verify only Johnson & Johnson cases are in results
                for case in cases:
                    title = case['title'].upper()
                    assert 'JOHNSON & JOHNSON' in title or 'JOHNSON &AMP; JOHNSON' in title
                
                # Verify non-J&J case was filtered out during HTML parsing
                titles = [case['title'] for case in cases]
                assert not any('Non-JJ Case' in title for title in titles)

    @pytest.mark.asyncio
    async def test_docket_number_extraction_and_validation(self, pipeline, temp_db_file, mock_pacer_repo):
        """Test 9: Verify docket number extraction and validation logic"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            with patch.object(pipeline, '_init_dynamo_if_needed', AsyncMock()):
                pipeline.pacer_repo = mock_pacer_repo
                mock_pacer_repo.check_docket_exists.return_value = False
                
                cases = await pipeline.extract_case_data()
                
                # Verify docket numbers are properly extracted
                expected_dockets = {'2:20-cv-12345', '2:21-cv-67890'}
                actual_dockets = {case['docket_num'] for case in cases}
                
                assert expected_dockets == actual_dockets
                
                # Verify each case has required fields
                for case in cases:
                    assert 'url' in case
                    assert 'title' in case
                    assert 'docket_num' in case
                    assert 'href' in case
                    assert case['url'].startswith('https://www.pacermonitor.com')

    @pytest.mark.asyncio
    async def test_integration_with_real_dynamodb_init_flow(self, pipeline, temp_db_file):
        """Test 10: Test integration with real DynamoDB initialization flow"""
        
        with patch('scripts.analysis.talc.process_html_pipeline.DB_FILE', temp_db_file):
            # Mock environment variables
            with patch.dict(os.environ, {
                'AWS_REGION': 'us-east-1',
                'AWS_ACCESS_KEY_ID': 'test-key',
                'AWS_SECRET_ACCESS_KEY': 'test-secret'
            }):
                # Mock the actual classes that would be instantiated
                with patch('scripts.analysis.talc.process_html_pipeline.AsyncDynamoDBStorage') as mock_storage_class:
                    with patch('scripts.analysis.talc.process_html_pipeline.PacerRepository') as mock_repo_class:
                        
                        mock_storage_instance = AsyncMock()
                        mock_repo_instance = AsyncMock()
                        
                        mock_storage_class.return_value = mock_storage_instance
                        mock_repo_class.return_value = mock_repo_instance
                        mock_repo_instance.check_docket_exists.return_value = False
                        
                        # Test initialization
                        await pipeline._init_dynamo_if_needed()
                        
                        # Verify proper initialization with environment config
                        expected_config = {
                            'region_name': 'us-east-1',
                            'aws_access_key_id': 'test-key',
                            'aws_secret_access_key': 'test-secret'
                        }
                        
                        mock_storage_class.assert_called_once()
                        call_args = mock_storage_class.call_args
                        config_arg = call_args[0][0]  # First positional argument
                        
                        assert config_arg == expected_config
                        mock_storage_instance.__aenter__.assert_called_once()
                        
                        # Verify PacerRepository was instantiated with storage and logger
                        mock_repo_class.assert_called_once()
                        repo_call_args = mock_repo_class.call_args
                        assert repo_call_args[0][0] == mock_storage_instance  # Storage instance
                        # Logger is the second argument - just verify it exists
                        assert repo_call_args[0][1] is not None


if __name__ == '__main__':
    # Run the tests
    pytest.main([__file__, '-v', '--asyncio-mode=auto'])