#!/usr/bin/env python3
"""
Test the aggressive field preparation approach for PACER query pages.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from unittest.mock import Mock, AsyncMock
from src.pacer.components.processing.sequential_docket_processor import ReturnAndContinueStep


async def test_aggressive_field_preparation():
    """Test the aggressive field preparation approach."""
    
    print("🧹 Testing Aggressive Field Preparation")
    print("=" * 60)
    
    # Create test step
    step = ReturnAndContinueStep()
    
    # Mock page and elements
    mock_page = Mock()
    mock_query_input = Mock()
    mock_submit_button = Mock()
    
    # Mock logger
    mock_logger = Mock()
    mock_logger.debug = Mock()
    mock_logger.info = Mock()
    mock_logger.warning = Mock()
    mock_logger.error = Mock()
    
    # Mock context
    mock_context = Mock()
    mock_context.docket_number = "1:25-cv-9777"  # The problematic case
    
    # Configure mocks
    mock_page.locator.side_effect = lambda selector: {
        "input[name='case_num']": mock_query_input,
        "input[type='submit'], button[type='submit']": mock_submit_button
    }.get(selector, Mock())
    
    mock_page.wait_for_timeout = AsyncMock()
    
    # Test Scenario: Stale field gets cleared and properly filled
    print("📋 Test: Clearing stale field and filling with 13-char docket")
    
    # Setup mock responses
    mock_query_input.wait_for = AsyncMock()
    mock_query_input.input_value = AsyncMock(side_effect=["1:25-cv-9777", "", "1:25-cv-09777"])
    mock_query_input.clear = AsyncMock()
    mock_query_input.fill = AsyncMock()
    mock_query_input.dispatch_event = AsyncMock()
    
    mock_submit_button.wait_for = AsyncMock()
    mock_submit_button.is_visible = AsyncMock(return_value=True)
    
    try:
        result = await step._prepare_query_page_for_docket(mock_page, mock_logger, mock_context)
        
        print(f"✅ PASS: Preparation result: {result}")
        
        # Verify the sequence of operations
        mock_query_input.clear.assert_called_once()
        mock_query_input.fill.assert_called_once_with("1:25-cv-09777")
        mock_query_input.dispatch_event.assert_called_once_with('blur')
        
        print("✅ PASS: All preparation steps executed correctly")
        print("   - Field was cleared")
        print("   - Field was filled with 13-character docket")
        print("   - Blur event was triggered")
        print("   - Submit button visibility was verified")
        
    except Exception as e:
        print(f"❌ FAIL: Preparation test failed: {e}")
        return False
    
    print()
    
    # Test with different docket numbers
    test_dockets = [
        ("1:25-cv-123", "1:25-cv-00123"),
        ("2:23-cr-1", "2:23-cr-00001"),
        ("3:24-bk-99999", "3:24-bk-99999"),
    ]
    
    for input_docket, expected_output in test_dockets:
        print(f"📋 Testing docket formatting: {input_docket} → {expected_output}")
        
        # Reset mocks
        mock_query_input.fill.reset_mock()
        mock_context.docket_number = input_docket
        
        try:
            result = await step._prepare_query_page_for_docket(mock_page, mock_logger, mock_context)
            
            # Check that fill was called with the correctly formatted docket
            fill_calls = mock_query_input.fill.call_args_list
            if fill_calls:
                filled_value = fill_calls[0][0][0]
                if filled_value == expected_output and len(filled_value) == 13:
                    print(f"✅ PASS: Correctly formatted and filled: {filled_value}")
                else:
                    print(f"❌ FAIL: Expected {expected_output}, got {filled_value}")
            else:
                print("❌ FAIL: No fill() calls made")
                
        except Exception as e:
            print(f"❌ FAIL: Test failed for {input_docket}: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Aggressive field preparation tests completed!")
    return True


async def demonstrate_new_approach():
    """Demonstrate the new aggressive approach."""
    
    print("\n🚀 New Aggressive Approach")
    print("=" * 40)
    
    print("📝 Key Changes:")
    print("1. ❌ Removed: Complex recovery logic with fallbacks")
    print("2. ✅ Added: Simple, aggressive field preparation")
    print("3. 🧹 Always: Clear field completely")
    print("4. 📝 Always: Fill with 13-character formatted docket")
    print("5. ⚡ Always: Trigger blur event")
    print("6. ⏱️ Extended: Longer timeouts for JavaScript processing")
    print()
    
    print("🔄 Process Flow:")
    print("1. Navigate to query page")
    print("2. Wait for case_num input field")
    print("3. Read current value (for logging)")
    print("4. Clear field completely")
    print("5. Format docket to exactly 13 characters")
    print("6. Fill field with formatted docket")
    print("7. Trigger blur event")
    print("8. Wait 1000ms for JavaScript")
    print("9. Check submit button visibility (8s timeout)")
    print("10. Return success/failure")
    print()
    
    print("💪 Why This Should Work:")
    print("- No assumptions about initial state")
    print("- Always starts fresh with empty field")
    print("- Guarantees 13-character requirement")
    print("- Forces JavaScript form validation")
    print("- More robust timeouts")
    
    return True


if __name__ == "__main__":
    print("🧹 Testing Aggressive Field Preparation Approach\n")
    
    success = asyncio.run(test_aggressive_field_preparation())
    asyncio.run(demonstrate_new_approach())
    
    print("\n" + "=" * 70)
    if success:
        print("✅ All tests passed! The aggressive approach should resolve the query failures.")
    else:
        print("❌ Tests failed!")
    
    sys.exit(0 if success else 1)