"""
Updated Dependency Injection fixtures for testing with proper DI patterns.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, MagicMock
from dependency_injector import containers, providers
from typing import Dict, Any, Optional

# Import DI container
try:
    from src.infrastructure.di.container import ApplicationContainer
    from src.models.config.base import WorkflowConfig
    DI_AVAILABLE = True
except ImportError:
    ApplicationContainer = None
    WorkflowConfig = None
    DI_AVAILABLE = False


class TestApplicationContainer(containers.DeclarativeContainer):
    """Test container with proper DI patterns for mocking."""
    
    # Configuration providers
    workflow_specific_config = providers.Configuration()
    
    # Mock workflow config provider
    workflow_config_provider = providers.Factory(
        Mock,
        spec=WorkflowConfig if WorkflowConfig else object
    )
    
    # Infrastructure providers
    shutdown_event = providers.Factory(asyncio.Event)
    
    # Storage providers
    async_storage = AsyncMock()
    s3_storage = AsyncMock()
    
    # Repository providers
    pacer_repository = AsyncMock()
    law_firms_repository = AsyncMock()
    fb_archive_repository = AsyncMock()
    district_courts_repository = AsyncMock()
    fb_image_hash_repository = AsyncMock()
    
    # AI service providers
    deepseek_service = AsyncMock()
    openai_client = AsyncMock()
    mistral_service = AsyncMock()
    ai_orchestrator = AsyncMock()
    
    # PACER service providers
    pacer_orchestrator_service = AsyncMock()
    browser_service = AsyncMock()
    authentication_service = AsyncMock()
    navigation_service = AsyncMock()
    case_processing_service = AsyncMock()
    download_orchestration_service = AsyncMock()
    file_management_service = AsyncMock()
    
    # Facebook Ads service providers
    facebook_ads_orchestrator = AsyncMock()
    job_orchestration_service = AsyncMock()
    job_runner_service = AsyncMock()
    ad_processor = AsyncMock()
    image_handler = AsyncMock()
    
    # Transformer service providers
    data_transformer = AsyncMock()
    docket_processor = AsyncMock()
    mdl_processor = AsyncMock()
    litigation_classifier = AsyncMock()
    law_firm_processor = AsyncMock()
    
    # Reports service providers
    reports_orchestrator_service = AsyncMock()
    data_loader_service = AsyncMock()
    rendering_service = AsyncMock()
    publishing_service = AsyncMock()
    
    # Orchestration providers
    main_orchestrator = AsyncMock()
    scraping_orchestrator = AsyncMock()
    processing_orchestrator = AsyncMock()
    upload_orchestrator = AsyncMock()
    fb_ads_orchestrator_wrapper = AsyncMock()
    reports_orchestrator_wrapper = AsyncMock()


@pytest.fixture
def test_container():
    """Create a test DI container with proper mocking."""
    container = TestApplicationContainer()
    
    # Setup default mock configuration
    mock_config_data = {
        'config_name': 'test_config',
        'iso_date': '20240101',
        'DATA_DIR': '/tmp/test',
        'scraper': False,
        'post_process': False,
        'upload': False,
        'fb_ads': False,
        'report_generator': False,
        'run_parallel': False,
        'process_single_court': [],
        'timeout': 30000,
        'pacer_username': 'test_user',
        'pacer_password': 'test_pass'
    }
    
    container.workflow_specific_config.from_dict(mock_config_data)
    
    # Configure mock workflow config
    mock_workflow_config = Mock(spec=WorkflowConfig if WorkflowConfig else object)
    for key, value in mock_config_data.items():
        setattr(mock_workflow_config, key, value)
    
    container.workflow_config_provider.override(mock_workflow_config)
    
    yield container
    
    # Cleanup
    try:
        container.unwire()
    except:
        pass


@pytest.fixture
def mock_container():
    """Backward compatibility fixture."""
    return test_container()


@pytest.fixture
def di_container(test_container):
    """Alias for test_container for clarity."""
    return test_container


@pytest.fixture
def mock_service_dependencies():
    """Create mock service dependencies using DI patterns."""
    return {
        'logger': Mock(),
        'config': Mock(),
        'async_storage': AsyncMock(),
        'pacer_repository': AsyncMock(),
        'law_firms_repository': AsyncMock(),
        'fb_archive_repository': AsyncMock(),
        'district_courts_repository': AsyncMock(),
        'deepseek_service': AsyncMock(),
        'openai_client': AsyncMock(),
        'mistral_service': AsyncMock(),
        's3_storage': AsyncMock(),
        'browser_service': AsyncMock(),
        'authentication_service': AsyncMock(),
        'navigation_service': AsyncMock(),
        'case_processing_service': AsyncMock(),
        'download_orchestration_service': AsyncMock(),
        'file_management_service': AsyncMock()
    }


@pytest.fixture
def mock_workflow_config():
    """Create a mock workflow configuration with proper DI patterns."""
    if WorkflowConfig:
        config = Mock(spec=WorkflowConfig)
    else:
        config = Mock()
    
    # Set default attributes
    config.config_name = "test_config"
    config.iso_date = "20240101"
    config.DATA_DIR = "/tmp/test"
    config.scraper = False
    config.post_process = False
    config.upload = False
    config.fb_ads = False
    config.report_generator = False
    config.run_parallel = False
    config.process_single_court = []
    config.timeout = 30000
    config.pacer_username = "test_user"
    config.pacer_password = "test_pass"
    
    return config


@pytest.fixture
def mock_deepseek_service():
    """Create a mock DeepSeek service with proper DI patterns."""
    service = AsyncMock()
    service.classify_ad.return_value = {
        'is_litigation': True,
        'practice_area': 'Personal Injury',
        'confidence': 0.95
    }
    service.extract_summary.return_value = "Test summary"
    service.extract_attorney_info.return_value = {
        'attorneys': ['John Doe', 'Jane Smith'],
        'law_firm': 'Test Law Firm'
    }
    service.close_session = AsyncMock()
    return service


@pytest.fixture
def mock_pacer_orchestrator():
    """Create a mock PACER orchestrator with proper DI patterns."""
    orchestrator = AsyncMock()
    orchestrator.process_courts.return_value = {
        'failed_courts': [],
        'court_results': [
            {
                'CourtID': 'test_court',
                'attempted': 5,
                'successful': 4,
                'downloaded': 3,
                'failed': 1
            }
        ]
    }
    return orchestrator


@pytest.fixture
def mock_facebook_ads_orchestrator():
    """Create a mock Facebook Ads orchestrator with proper DI patterns."""
    orchestrator = AsyncMock()
    orchestrator.execute.return_value = {
        'status': 'success',
        'firms_processed': 10,
        'ads_processed': 150,
        'errors': []
    }
    return orchestrator


@pytest.fixture
def mock_reports_orchestrator():
    """Create a mock Reports orchestrator with proper DI patterns."""
    orchestrator = AsyncMock()
    orchestrator.generate_report.return_value = {
        'status': 'success',
        'reports_generated': 3,
        'publish_results': {
            'local': {'status': 'success'},
            's3': {'status': 'success'}
        }
    }
    return orchestrator


@pytest.fixture
def mock_job_orchestration_service():
    """Create a mock job orchestration service for FB ads testing."""
    service = AsyncMock()
    service.process_firms_as_jobs.return_value = True
    return service


@pytest.fixture
def mock_job_runner_service():
    """Create a mock job runner service for FB ads testing."""
    service = AsyncMock()
    
    # Mock ProcessFirmJob
    mock_job = Mock()
    mock_job.firm_id = "test_firm"
    mock_job.firm_name = "Test Law Firm"
    mock_job.status = "COMPLETED"
    mock_job.metrics = {
        'ads_fetched': 10,
        'ads_processed': 8,
        'db_inserts': 8,
        'duration_sec': 45.2
    }
    
    service.run_job.return_value = mock_job
    return service


@pytest.fixture
def mock_browser_service():
    """Create a mock browser service with proper DI patterns."""
    service = AsyncMock()
    
    # Mock browser context
    mock_context = AsyncMock()
    mock_context.new_page.return_value = AsyncMock()
    mock_context.close.return_value = None
    
    service.new_context.return_value = mock_context
    service.close.return_value = None
    service.setup_download_paths.return_value = "/tmp/test/downloads"
    
    return service


@pytest.fixture
def mock_repositories():
    """Create mock repositories with proper DI patterns."""
    return {
        'pacer_repository': AsyncMock(),
        'law_firms_repository': AsyncMock(),
        'fb_archive_repository': AsyncMock(),
        'district_courts_repository': AsyncMock(),
        'fb_image_hash_repository': AsyncMock()
    }


@pytest.fixture
def mock_ai_services():
    """Create mock AI services with proper DI patterns."""
    return {
        'deepseek_service': AsyncMock(),
        'openai_client': AsyncMock(),
        'mistral_service': AsyncMock(),
        'ai_orchestrator': AsyncMock()
    }


@pytest.fixture
def mock_storage_services():
    """Create mock storage services with proper DI patterns."""
    async_storage = AsyncMock()
    async_storage.get_item.return_value = {'test': 'data'}
    async_storage.put_item.return_value = True
    async_storage.query.return_value = [{'test': 'data'}]
    async_storage.scan.return_value = [{'test': 'data'}]
    
    s3_storage = AsyncMock()
    s3_storage.put_object.return_value = {'ETag': 'test-etag'}
    s3_storage.get_object.return_value = {'Body': Mock(read=lambda: b'test content')}
    
    return {
        'async_storage': async_storage,
        's3_storage': s3_storage
    }


def create_mock_service_with_di(service_class, **kwargs):
    """Helper function to create mock services with DI patterns."""
    mock_service = AsyncMock(spec=service_class)
    
    # Set default attributes
    for key, value in kwargs.items():
        setattr(mock_service, key, value)
    
    # Add common async methods
    if not hasattr(mock_service, 'close'):
        mock_service.close = AsyncMock()
    if not hasattr(mock_service, '__aenter__'):
        mock_service.__aenter__ = AsyncMock(return_value=mock_service)
    if not hasattr(mock_service, '__aexit__'):
        mock_service.__aexit__ = AsyncMock()
    
    return mock_service


def setup_container_for_test(container, overrides: Dict[str, Any]):
    """Helper function to setup container overrides for specific tests."""
    for provider_name, mock_value in overrides.items():
        if hasattr(container, provider_name):
            provider = getattr(container, provider_name)
            if isinstance(mock_value, type):
                # If it's a class, create a Factory provider
                provider.override(providers.Factory(mock_value))
            else:
                # If it's an instance, override directly
                provider.override(mock_value)


# Backward compatibility fixtures for legacy tests
@pytest.fixture
def mock_service_factory():
    """Legacy fixture for backward compatibility."""
    factory = Mock()
    factory.create_async_storage = Mock(return_value=AsyncMock())
    factory.create_pacer_repository = Mock(return_value=AsyncMock())
    factory.create_law_firms_repository = Mock(return_value=AsyncMock())
    factory.create_fb_archive_repository = Mock(return_value=AsyncMock())
    factory.create_district_courts_repository = Mock(return_value=AsyncMock())
    factory.create_deepseek_service = Mock(return_value=AsyncMock())
    factory.create_browser_service = Mock(return_value=AsyncMock())
    return factory