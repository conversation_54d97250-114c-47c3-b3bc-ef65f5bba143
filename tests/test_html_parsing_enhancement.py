#!/usr/bin/env python3
"""
Test HTML parsing enhancements for jury_demand, is_mdl, and lead_case fields.
"""

import sys
import asyncio
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Change to project root directory
os.chdir(str(project_root))

import logging
from src.services.html.case_parser_service import CaseParserService

async def test_html_parsing_enhancements():
    """Test the new HTML parsing fields."""
    
    # Initialize basic logger
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("test_html_parsing")
    
    # Sample HTML content based on the actual HTML structure
    html_content = """
    <html>
    <head><title>PACER Case</title></head>
    <body>
        <div id="cmecfMainContent">
            <h3 align="center">
                U.S. District Court<br/>
                Northern District of Illinois (Chicago)<br/>
                CIVIL DOCKET FOR CASE #: 1:25-cv-09787
            </h3>
            <table>
                <tr><td valign="top">
                    <PERSON><PERSON> Muir v. Godrej SON Holdings Inc.<br/>
                    Assigned to: District Judge Virginia M. Kendall<br/>
                    Referred to: Magistrate Judge JANTZ,MDL 3060<br/>
                    Date Filed: 12/27/2024<br/>
                    Jury Demand: Plaintiff<br/>
                    Nature of Suit: 190 Other Contract<br/>
                    Jurisdiction: Federal Question<br/>
                    Lead case: [1:23-cv-00818]<br/>
                    Cause: 15:1692 Consumer Product Safety Act
                </td></tr>
            </table>
            <table>
                <tr>
                    <td><b><u>Plaintiff</u></b></td>
                    <td align="center">represented by</td>
                    <td><b><u>Attorney</u></b></td>
                </tr>
                <tr>
                    <td><b>Debra Muir</b></td>
                    <td align="center">represented by</td>
                    <td>
                        <b>John Smith</b><br/>
                        Law Firm LLP<br/>
                        123 Main Street<br/>
                        Chicago, IL 60601<br/>
                        Phone: ************<br/>
                        ATTORNEY TO BE NOTICED
                    </td>
                </tr>
            </table>
        </div>
    </body>
    </html>
    """
    
    # Initialize CaseParserService
    parser = CaseParserService(logger=logger, html_content=html_content)
    
    # Parse the HTML content
    result = parser.parse()
    
    # Print results
    print("=== HTML Parsing Enhancement Test Results ===")
    print(f"Parsed case info: {result.get('case_info', {})}")
    print()
    
    # Check specific fields
    case_info = result.get('case_info', {})
    
    print("=== Field Validation ===")
    
    # Test jury_demand
    jury_demand = case_info.get('jury_demand')
    print(f"Jury Demand: '{jury_demand}' (Expected: 'Plaintiff')")
    assert jury_demand == 'Plaintiff', f"Expected 'Plaintiff', got '{jury_demand}'"
    print("✅ Jury Demand extraction successful")
    
    # Test is_mdl
    is_mdl = case_info.get('is_mdl')
    print(f"Is MDL: {is_mdl} (Expected: True)")
    assert is_mdl == True, f"Expected True, got {is_mdl}"
    print("✅ MDL detection successful")
    
    # Test lead_case
    lead_case = case_info.get('lead_case')
    print(f"Lead Case: '{lead_case}' (Expected: '[1:23-cv-00818]')")
    assert lead_case == '[1:23-cv-00818]', f"Expected '[1:23-cv-00818]', got '{lead_case}'"
    print("✅ Lead Case extraction successful")
    
    # Test other existing fields still work
    docket_num = case_info.get('docket_num')
    print(f"Docket Number: '{docket_num}' (Expected: '1:25-cv-09787')")
    
    assigned_to = case_info.get('assigned_to')
    print(f"Assigned To: '{assigned_to}' (Expected: contains 'Kendall')")
    
    print("\n=== Test Completed Successfully ===")
    print("All new HTML parsing fields are working correctly!")

def main():
    """Run the test."""
    try:
        asyncio.run(test_html_parsing_enhancements())
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()