"""
Test Browser Pool Integration with Parallel Jobs Architecture

This test verifies that the browser pool integration works correctly
with the parallel jobs execution system.
"""

import asyncio
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock
from datetime import date

from src.pacer.jobs.job_runner_service import PacerJobRunnerService
from src.pacer.jobs.job_orchestration_service import PacerJobOrchestrationService
from src.pacer.jobs.jobs_models import PacerJob
from src.pacer.components.browser.browser_pool import BrowserPoolManager


@pytest.fixture
def mock_config():
    """Mock configuration with browser pool settings."""
    return {
        'use_browser_pool': True,
        'browser_pool': {
            'min_size': 1,
            'max_size': 3,
            'max_contexts_per_browser': 2,
            'browser_ttl_minutes': 30,
            'browser_idle_minutes': 10
        },
        'max_parallel_courts': 2,
        'cleanup_timeout_seconds': 10,
        'headless': True,
        'timeout_ms': 30000,
        'data_path': str(tempfile.gettempdir())
    }


@pytest.fixture
def mock_logger():
    """Mock logger."""
    logger = Mock()
    logger.info = Mock()
    logger.warning = Mock() 
    logger.error = Mock()
    return logger


@pytest.fixture
def mock_pacer_orchestrator():
    """Mock PACER orchestrator."""
    orchestrator = Mock()
    orchestrator.process_single_court_job = AsyncMock(return_value={
        'status': 'success',
        'metrics': {'cases_processed': 5}
    })
    return orchestrator


@pytest.fixture
async def browser_pool_manager(mock_config, mock_logger):
    """Create browser pool manager for testing."""
    pool = BrowserPoolManager(logger=mock_logger, config=mock_config)
    
    # Mock the browser pool methods for testing
    pool.acquire_context = AsyncMock()
    pool.release_context = AsyncMock()
    pool.initialize = AsyncMock()
    pool.shutdown = AsyncMock()
    
    return pool


@pytest.fixture
def sample_jobs():
    """Create sample PacerJob instances for testing."""
    return [
        PacerJob(
            court_id='cand',
            iso_date='20250120',
            start_date=date(2025, 1, 15),
            end_date=date(2025, 1, 20),
            config_snapshot={'test': True}
        ),
        PacerJob(
            court_id='ilnd', 
            iso_date='20250120',
            start_date=date(2025, 1, 15),
            end_date=date(2025, 1, 20),
            config_snapshot={'test': True}
        )
    ]


class TestBrowserPoolIntegration:
    """Test browser pool integration with parallel jobs."""
    
    @pytest.mark.asyncio
    async def test_job_runner_service_uses_browser_pool(
        self, 
        mock_config, 
        mock_logger, 
        mock_pacer_orchestrator,
        browser_pool_manager,
        sample_jobs
    ):
        """Test that job runner service uses browser pool when available."""
        
        # Mock successful context acquisition
        mock_context = Mock()
        download_path = Path(tempfile.gettempdir()) / "test_download"
        browser_pool_manager.acquire_context.return_value = mock_context
        
        # Create job runner service with browser pool
        job_runner = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            pacer_orchestrator=mock_pacer_orchestrator,
            browser_pool_manager=browser_pool_manager
        )
        
        # Test that pool is detected and used
        assert job_runner.use_browser_pool is True
        assert job_runner.browser_pool_manager is not None
        
        # Run a job
        job = sample_jobs[0]
        result = await job_runner.run_job(job)
        
        # Verify pool methods were called
        browser_pool_manager.acquire_context.assert_called_once()
        browser_pool_manager.release_context.assert_called_once()
        
        # Verify job completed successfully
        assert result.status == "COMPLETED"
        assert result.metrics.get('used_browser_pool') is True


    @pytest.mark.asyncio
    async def test_job_runner_fallback_when_pool_unavailable(
        self,
        mock_config,
        mock_logger, 
        mock_pacer_orchestrator,
        sample_jobs
    ):
        """Test fallback to individual browser when pool is unavailable."""
        
        # Create job runner without browser pool
        job_runner = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            pacer_orchestrator=mock_pacer_orchestrator,
            browser_pool_manager=None  # No pool provided
        )
        
        # Test that fallback is used
        assert job_runner.use_browser_pool is False
        assert job_runner.browser_pool_manager is None


    @pytest.mark.asyncio 
    async def test_parallel_execution_with_browser_pool(
        self,
        mock_config,
        mock_logger,
        mock_pacer_orchestrator,
        browser_pool_manager,
        sample_jobs
    ):
        """Test parallel job execution using browser pool."""
        
        # Mock successful context acquisition
        mock_context = Mock()
        browser_pool_manager.acquire_context.return_value = mock_context
        
        # Create job runner with pool
        job_runner = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            pacer_orchestrator=mock_pacer_orchestrator,
            browser_pool_manager=browser_pool_manager
        )
        
        # Create orchestration service  
        orchestration_service = PacerJobOrchestrationService(
            config=mock_config,
            job_runner_service=job_runner,
            logger=mock_logger
        )
        
        # Test parallel processing
        results = await orchestration_service.process_courts_as_jobs(
            court_ids=['cand', 'ilnd'],
            iso_date='20250120',
            start_date=date(2025, 1, 15),
            end_date=date(2025, 1, 20)
        )
        
        # Verify results
        assert len(results) == 2
        assert all(job.status == "COMPLETED" for job in results)
        
        # Verify pool was used for both jobs
        assert browser_pool_manager.acquire_context.call_count == 2
        assert browser_pool_manager.release_context.call_count == 2


    @pytest.mark.asyncio
    async def test_download_path_isolation_with_pool(
        self,
        mock_config,
        mock_logger,
        mock_pacer_orchestrator,
        browser_pool_manager,
        sample_jobs
    ):
        """Test that download paths are properly isolated when using pool."""
        
        # Mock context acquisition to track download paths
        acquired_paths = []
        
        async def mock_acquire_context(job_id, download_path):
            acquired_paths.append(download_path)
            return Mock()
            
        browser_pool_manager.acquire_context.side_effect = mock_acquire_context
        
        # Create job runner
        job_runner = PacerJobRunnerService(
            config=mock_config,
            logger=mock_logger,
            pacer_orchestrator=mock_pacer_orchestrator,
            browser_pool_manager=browser_pool_manager
        )
        
        # Run multiple jobs
        job1 = sample_jobs[0]  # cand court
        job2 = sample_jobs[1]  # ilnd court
        
        await job_runner.run_job(job1)
        await job_runner.run_job(job2)
        
        # Verify download paths are unique and court-specific
        assert len(acquired_paths) == 2
        assert 'cand_ctx_dl_report' in acquired_paths[0]
        assert 'ilnd_ctx_dl_report' in acquired_paths[1]
        assert acquired_paths[0] != acquired_paths[1]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])