"""
Test containers for dependency injection in tests.
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from unittest.mock import MagicMock, AsyncMock

from dependency_injector import containers, providers

from src.containers import MainContainer, CoreContainer
from src.containers.storage import StorageContainer
from src.containers.pacer import Pacer<PERSON>ontainer
from src.containers.fb_ads import FbAdsContainer
from src.containers.transformer import TransformerContainer
from src.containers.reports import ReportsContainer

class TestCoreContainer(containers.DeclarativeContainer):
    """Test container for core services."""
    
    config = providers.Configuration()
    
    # Mock logger
    logger = providers.Factory(
        lambda: MagicMock(spec=logging.Logger)
    )
    
    # Mock HTTP session
    http_session = providers.Factory(
        lambda: AsyncMock()
    )
    
    # Mock shutdown event
    shutdown_event = providers.Factory(
        asyncio.Event
    )

class TestStorageContainer(containers.DeclarativeContainer):
    """Test container for storage services."""
    
    config = providers.Configuration()
    logger = providers.Dependency()
    
    # Mock repositories
    pacer_repository = providers.Factory(
        lambda: AsyncMock()
    )
    
    fb_archive_repository = providers.Factory(
        lambda: AsyncMock()
    )
    
    fb_image_hash_repository = providers.Factory(
        lambda: AsyncMock()
    )
    
    law_firms_repository = providers.Factory(
        lambda: AsyncMock()
    )
    
    district_courts_repository = providers.Factory(
        lambda: AsyncMock()
    )
    
    # Mock storage services
    s3_async_storage = providers.Factory(
        lambda: AsyncMock()
    )
    
    dynamodb_storage = providers.Factory(
        lambda: AsyncMock()
    )

class TestMainContainer(containers.DeclarativeContainer):
    """Main test container that wires everything together."""
    
    config = providers.Configuration()
    
    # Core container with test implementations
    core = providers.Container(
        TestCoreContainer,
        config=config
    )
    
    # Storage container with mocks
    storage = providers.Container(
        TestStorageContainer,
        config=config,
        logger=core.logger
    )
    
    # Other containers can use real implementations or be mocked as needed
    pacer = providers.Container(
        PacerContainer,
        config=config,
        logger=core.logger,
        storage_container=storage,
        shutdown_event=core.shutdown_event
    )
    
    fb_ads = providers.Container(
        FbAdsContainer,
        config=config,
        logger=core.logger,
        storage_container=storage
    )
    
    transformer = providers.Container(
        TransformerContainer,
        config=config,
        logger=core.logger,
        storage_container=storage,
        pacer_container=pacer
    )
    
    reports = providers.Container(
        ReportsContainer,
        config=config,
        logger=core.logger,
        storage_container=storage,
        fb_ads_container=fb_ads,
        pacer_container=pacer
    )

# Global test container instance
test_container = TestMainContainer()

def override_providers(**overrides):
    """
    Override specific providers in the test container.
    
    Usage:
        with override_providers(logger=mock_logger):
            # test code here
    """
    overrides_map = {}
    
    # Map of common provider names to their container paths
    provider_paths = {
        'logger': 'core.logger',
        'config': 'core.config',
        'http_session': 'core.http_session',
        'shutdown_event': 'core.shutdown_event',
        'pacer_repository': 'storage.pacer_repository',
        'fb_archive_repository': 'storage.fb_archive_repository',
        'fb_image_hash_repository': 'storage.fb_image_hash_repository',
        'law_firms_repository': 'storage.law_firms_repository',
        'district_courts_repository': 'storage.district_courts_repository',
        's3_async_storage': 'storage.s3_async_storage',
        'dynamodb_storage': 'storage.dynamodb_storage',
        'deepseek_service': 'transformer.deepseek_service',
        'openai_client': 'transformer.openai_client',
        'mistral_service': 'transformer.mistral_service',
        'llava_client': 'transformer.llava_client',
        'prompt_manager': 'transformer.prompt_manager',
    }
    
    for key, value in overrides.items():
        # Get the full path
        if key in provider_paths:
            path = provider_paths[key]
        elif '.' in key:
            path = key
        else:
            # Unknown provider, skip
            continue
        
        # Navigate to the provider
        parts = path.split('.')
        provider = test_container
        for part in parts:
            provider = getattr(provider, part)
        
        overrides_map[provider] = value
    
    # Create context manager for overrides
    overrides = []
    for provider, value in overrides_map.items():
        overrides.append(provider.override(value))
    
    from contextlib import ExitStack
    stack = ExitStack()
    for override in overrides:
        stack.enter_context(override)
    
    return stack

def _module_exists(module_name: str) -> bool:
    """Check if a module exists without importing it."""
    import importlib.util
    try:
        spec = importlib.util.find_spec(module_name)
        return spec is not None
    except (ImportError, ModuleNotFoundError):
        return False


def setup_test_container(config: Optional[Dict[str, Any]] = None):
    """
    Setup test container with configuration.
    
    Args:
        config: Optional configuration dictionary
    """
    if config:
        test_container.config.from_dict(config)
    
    # Wire the container to test modules
    import sys
    modules_to_wire = []
    
    # Only add modules that exist
    test_modules = [
        "tests",
        "tests.unit",
        "tests.integration",
        "tests.unit.services",
        "tests.unit.services.ai",
        "tests.unit.services.pacer",
        "tests.unit.services.transformer",
    ]
    
    for module in test_modules:
        if module in sys.modules or _module_exists(module):
            modules_to_wire.append(module)
    
    if modules_to_wire:
        test_container.wire(modules=modules_to_wire)
    
    return test_container

def teardown_test_container():
    """Teardown test container and unwire modules."""
    test_container.unwire()
