#!/usr/bin/env python3
"""
Comprehensive test for company name parsing issues.
This tests various scenarios where company names might be incorrectly split.
"""

import pytest
from bs4 import BeautifulSoup
from src.services.html.case_parser_service import CaseParserService
from unittest.mock import Mock

def test_complex_defendant_parsing_scenarios():
    """Test complex defendant parsing scenarios that might trigger comma splitting."""
    
    # Mock logger
    mock_logger = Mock()
    parser = CaseParserService(logger=mock_logger)
    
    # Complex HTML with various defendant name patterns
    html_content = """
    <html>
    <body>
        <div id="cmecfMainContent">
            <h3 align="center">
                U.S. District Court<br/>
                Northern District of California (San Francisco)<br/>
                CIVIL DOCKET FOR CASE #: 3:25-cv-05328-WHO
            </h3>
            <table cellpadding="1">
                <tr>
                    <td valign="top">Plaintiff v. Multiple Complex Defendants</td>
                </tr>
            </table>
            <table>
                <tr><td>Defendant</td></tr>
                <tr>
                    <td><b>XYZ Company, Inc., a Delaware Corporation</b></td>
                </tr>
                <tr>
                    <td><b>Johnson, Smith & Associates, P.C.</b></td>
                </tr>
                <tr>
                    <td><b>ABC Corp (d/b/a Multiple Names, LLC)</b></td>
                </tr>
                <tr>
                    <td><b>International Business Machines Corporation, IBM Corporation, and IBM Services, Inc.</b></td>
                </tr>
                <tr>
                    <td><b>Smith, Jones, Williams & Partners (Holdings), Ltd.</b></td>
                </tr>
            </table>
        </div>
    </body>
    </html>
    """
    
    # Parse the HTML
    parser.set_html(html_content)
    result = parser.parse()
    
    # Print results for debugging
    print("\n=== DETAILED PARSING TEST RESULTS ===")
    print("Parsed defendants:")
    defendants = result.get('defendants', [])
    for i, defendant in enumerate(defendants):
        print(f"  [{i+1}] '{defendant}'")
    
    print(f"\nTotal defendants found: {len(defendants)}")
    
    # Expected company names (should NOT be split by commas)
    expected_defendants = [
        "XYZ Company, Inc., a Delaware Corporation",
        "Johnson, Smith & Associates, P.C.",
        "ABC Corp (d/b/a Multiple Names, LLC)",
        "International Business Machines Corporation, IBM Corporation, and IBM Services, Inc.",
        "Smith, Jones, Williams & Partners (Holdings), Ltd."
    ]
    
    print(f"\nExpected defendants: {len(expected_defendants)}")
    for i, expected in enumerate(expected_defendants):
        print(f"  [{i+1}] '{expected}'")
    
    # Check that we have the right number of defendants
    if len(defendants) != len(expected_defendants):
        print(f"\n❌ MISMATCH: Expected {len(expected_defendants)} defendants, got {len(defendants)}")
        print("This might indicate comma-splitting is occurring.")
        
        # Show which ones we're missing
        for expected in expected_defendants:
            found = any(expected in defendant for defendant in defendants)
            if not found:
                print(f"  ❌ Missing: '{expected}'")
                
        # Show extra defendants that might be split results
        for defendant in defendants:
            is_part_of_expected = any(defendant in expected for expected in expected_defendants)
            if not is_part_of_expected:
                print(f"  ⚠️ Unexpected (possible split result): '{defendant}'")
    else:
        print(f"\n✅ Correct number of defendants found")
        for expected in expected_defendants:
            found = expected in defendants
            print(f"  {'✅' if found else '❌'} '{expected}' - {'FOUND' if found else 'MISSING'}")

def test_versus_field_parsing():
    """Test versus field parsing to see if it contains company names with commas."""
    
    # Mock logger  
    mock_logger = Mock()
    parser = CaseParserService(logger=mock_logger)
    
    html_content = """
    <html>
    <body>
        <div id="cmecfMainContent">
            <h3 align="center">
                U.S. District Court<br/>
                Northern District of California<br/>
                CIVIL DOCKET FOR CASE #: 3:25-cv-05328-WHO
            </h3>
            <table cellpadding="1">
                <tr>
                    <td valign="top">John Doe v. ABC Company, Inc., XYZ Corp, LLC, and Johnson & Associates, P.C.</td>
                </tr>
            </table>
        </div>
    </body>
    </html>
    """
    
    parser.set_html(html_content)
    result = parser.parse()
    
    print("\n=== VERSUS FIELD PARSING TEST ===")
    versus = result.get('case_info', {}).get('versus', '')
    print(f"Versus field: '{versus}'")
    
    defendants = result.get('defendants', [])
    print(f"Defendants extracted: {defendants}")
    
    # Check if versus field processing is affecting defendant names
    if versus and any(',' in name for name in defendants if isinstance(name, str)):
        print("✅ Company names with commas preserved in defendants")
    elif defendants:
        print("⚠️ Check if comma-containing names were processed correctly")

if __name__ == "__main__":
    test_complex_defendant_parsing_scenarios()
    test_versus_field_parsing()
    print("\n✅ Detailed parsing tests completed")