# Service Consolidation Preparation Tests

This directory contains tests that validate service interactions before consolidation to ensure consolidated services maintain the same behavior.

## Purpose

These tests prepare for the consolidation of 131 services to ~95 services by testing the interaction patterns and dependencies between services that will be merged.

## Test Categories

### PACER Service Interactions
- Test interactions between 24 PACER services before consolidation
- Validate service dependency chains
- Ensure data flow between PACER components
- Test browser service coordination with file management

### FB Ads Service Dependencies
- Test dependencies between 20 FB ads services before merging
- Validate job orchestration service interactions
- Ensure image processing service coordination
- Test ad classification service dependencies

### Transformer Service Behavior
- Test interactions between 31 transformer services before consolidation
- Validate data transformation pipeline coordination
- Ensure service-to-service data passing
- Test MDL and docket processing service interactions

### Cross-Service Communication
- Test service-to-service calls across domains
- Validate async communication patterns
- Ensure error propagation and handling
- Test service lifecycle coordination

## Running Service Consolidation Tests

```bash
pytest tests/service_consolidation/ -v
pytest tests/service_consolidation/ -m integration
```

## Consolidation Strategy

Service consolidation will merge services into larger, more cohesive units:
- **PACER**: 24 → 12 services (merge browser, file, processing services)
- **FB Ads**: 20 → 10 services (merge processing, media, job services)
- **Transformer**: 31 → 15 services (merge docket, MDL, validation services)

These tests ensure the consolidated services maintain all current functionality.