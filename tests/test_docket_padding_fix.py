#!/usr/bin/env python3
"""
Test script to verify the docket number padding fix.
This validates that all docket numbers are formatted to exactly 13 characters.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.pacer_utils import validate_and_format_docket_number


def test_docket_padding():
    """Test the docket number padding fix with various inputs."""
    
    test_cases = [
        # (input, expected_output, description)
        ("1:25-cv-9777", "1:25-cv-09777", "Problem case from error log"),
        ("1:25-cv-09777", "1:25-cv-09777", "Already correctly formatted"),
        ("3:25-cv-06587", "3:25-cv-06587", "Already 13 characters"),
        ("1:24-cv-123", "1:24-cv-00123", "Need padding"),
        ("2:23-cr-1", "2:23-cr-00001", "Single digit case number"),
        ("5:22-bk-12345", "5:22-bk-12345", "5-digit case number"),
        ("9:21-md-99999", "9:21-md-99999", "Max 5-digit case number"),
    ]
    
    print("Testing docket number padding fix...")
    print("=" * 60)
    
    all_passed = True
    
    for input_docket, expected, description in test_cases:
        result = validate_and_format_docket_number(input_docket)
        
        # Check if result matches expected
        success = result == expected
        
        # Check if result is exactly 13 characters
        length_check = len(result) == 13 if result else False
        
        status = "✅ PASS" if success and length_check else "❌ FAIL"
        
        print(f"{status} {description}")
        print(f"   Input:    '{input_docket}' ({len(input_docket)} chars)")
        print(f"   Expected: '{expected}' ({len(expected)} chars)")
        print(f"   Got:      '{result}' ({len(result) if result else 0} chars)")
        print()
        
        if not success or not length_check:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 All tests PASSED! Docket padding fix is working correctly.")
    else:
        print("💥 Some tests FAILED! Please check the implementation.")
    
    return all_passed


def test_edge_cases():
    """Test edge cases and invalid inputs."""
    
    print("\nTesting edge cases...")
    print("=" * 40)
    
    edge_cases = [
        ("", None, "Empty string"),
        (None, None, "None input"),
        ("invalid", None, "Invalid format"),
        ("1:25cv-123", None, "Missing dash"),
        ("1:25-cv", None, "Missing case number"),
        ("12:25-cv-123", None, "Two-digit court number"),
    ]
    
    for input_val, expected, description in edge_cases:
        try:
            result = validate_and_format_docket_number(input_val)
            success = result == expected
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {description}: '{input_val}' -> '{result}'")
        except Exception as e:
            print(f"❌ FAIL {description}: Exception - {e}")


if __name__ == "__main__":
    success = test_docket_padding()
    test_edge_cases()
    
    sys.exit(0 if success else 1)