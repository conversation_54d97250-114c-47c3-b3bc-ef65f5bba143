#!/usr/bin/env python3\n\"\"\"\nJSON Transformation Service Test Runner\n\nThis script runs comprehensive tests for the JsonTransformationService,\ndemonstrating all key functionality including:\n1. Key deletion rules\n2. Value transformations  \n3. Attorney list filtering and cleaning\n4. HTML parsing for jury_demand, is_mdl, lead_case\n5. Final formatting (None values, key ordering)\n6. Integration with sample JSON data\n\nUsage:\n    python tests/run_json_transformation_tests.py\n    \nEnvironment Variables:\n    VERBOSE=1 - Enable verbose output\n    SAMPLE_DATA_PATH - Path to sample JSON file (optional)\n\"\"\"\n\nimport os\nimport sys\nimport asyncio\nimport json\nimport time\nfrom pathlib import Path\nfrom typing import Dict, Any, List\nfrom unittest.mock import Mock, AsyncMock\n\n# Add project root to Python path\nproject_root = Path(__file__).parent.parent\nsys.path.insert(0, str(project_root))\n\nfrom src.services.json.json_transformation_service import JsonTransformationService\nfrom src.pacer.components.case_processing.html_parser import HtmlParser\n\n\nclass JsonTransformationTestRunner:\n    \"\"\"Test runner for JSON transformation functionality.\"\"\"\n    \n    def __init__(self, verbose: bool = False):\n        self.verbose = verbose\n        self.logger = self._create_mock_logger()\n        self.html_parser = self._create_mock_html_parser()\n        self.service = None\n        self.test_results = []\n        \n    def _create_mock_logger(self):\n        \"\"\"Create mock logger for testing.\"\"\"\n        logger = Mock()\n        if self.verbose:\n            logger.info = lambda msg, *args, **kwargs: print(f\"[INFO] {msg}\")\n            logger.debug = lambda msg, *args, **kwargs: print(f\"[DEBUG] {msg}\")\n            logger.warning = lambda msg, *args, **kwargs: print(f\"[WARNING] {msg}\")\n            logger.error = lambda msg, *args, **kwargs: print(f\"[ERROR] {msg}\")\n        else:\n            for level in ['info', 'debug', 'warning', 'error']:\n                setattr(logger, level, Mock())\n        return logger\n    \n    def _create_mock_html_parser(self):\n        \"\"\"Create mock HTML parser with realistic responses.\"\"\"\n        parser = Mock(spec=HtmlParser)\n        \n        async def mock_perform_action(data):\n            action = data.get(\"action\")\n            html_content = data.get(\"html_content\", \"\")\n            \n            # Simulate realistic HTML parsing responses\n            if action == \"extract_attorneys\":\n                if \"jury\" in html_content.lower():\n                    return {\n                        \"jury_demand\": \"Plaintiff\",\n                        \"has_jury_demand\": True,\n                        \"extracted_from\": \"HTML content\"\n                    }\n                elif \"mdl\" in html_content.lower():\n                    return {\n                        \"is_mdl\": True,\n                        \"mdl_number\": \"3114\",\n                        \"mdl_info\": \"MDL-3114\",\n                        \"extracted_from\": \"HTML content\"\n                    }\n                elif \"lead case\" in html_content.lower():\n                    return {\n                        \"lead_case\": \"2:18-mn-02873-RMG\",\n                        \"is_transferred\": True,\n                        \"lead_case_court\": \"District of South Carolina\",\n                        \"extracted_from\": \"HTML content\"\n                    }\n                elif \"attorney\" in html_content.lower():\n                    return [\n                        {\n                            \"attorney_name\": \"Extracted Attorney\",\n                            \"law_firm\": \"Extracted Firm\",\n                            \"email\": \"<EMAIL>\",\n                            \"source\": \"HTML parsing\"\n                        }\n                    ]\n                else:\n                    return []\n            \n            return {}\n        \n        parser.perform_action = AsyncMock(side_effect=mock_perform_action)\n        return parser\n    \n    def _get_sample_json_data(self) -> Dict[str, Any]:\n        \"\"\"Get comprehensive sample JSON data for testing.\"\"\"\n        return {\n            \"docket_num\": \"3:24-cv-02581\",\n            \"court_id\": \"txnd\",\n            \"versus\": \"raslavich v. att inc.\",  # lowercase for transformation\n            \"filing_date\": \"10/15/2024\",\n            \"cause\": \"28:1332 Diversity-Notice of Removal\",\n            \"nos\": \"360 Torts/Pers Inj\",\n            \"jurisdiction\": \"Diversity\",\n            \"presider\": \"Ada Brown\",\n            \"jury_demand\": \"Both\",\n            \"assigned_to\": \"Judge Ada Brown\",\n            \"court_name\": \"Northern District of Texas\",\n            \"flags\": [\"CLOSED\", \"MDL-3114\", \"MEMBER\"],\n            \"plaintiff\": [\"Benjamin Raslavich\", \"Benjamin Raslavich\"],  # Duplicate\n            \"defendant\": [\"AT&T Inc\", \"AT&T Inc\"],  # Duplicate\n            \"attorney\": [\n                {\n                    \"attorney_name\": \"Morgan Ashlee Dean\",\n                    \"law_firm\": \"Kuhn Raslavich PA\",\n                    \"email\": \"<EMAIL>\"\n                },\n                {\n                    \"attorney_name\": \"Joseph Patrick Rosier\",\n                    \"law_firm\": \"Kuhn Raslavich PA\",\n                    \"email\": \"<EMAIL>\"\n                },\n                # Duplicate with different case\n                {\n                    \"attorney_name\": \"MORGAN ASHLEE DEAN\",\n                    \"law_firm\": \"kuhn raslavich pa\",\n                    \"email\": \"<EMAIL>\"\n                }\n            ],\n            \"transferred_in\": True,\n            \"is_transferred\": True,\n            \"s3_html\": \"cdn.lexgenius.ai/20241015/html/txnd_24_02581_Raslavich_v_ATT_Inc.html\",\n            \"title\": \"AT&T Data Breach Litigation\",\n            \"mdl_num\": \"3114\",\n            \"allegations\": \"Plaintiff Benjamin Raslavich is suing Defendant AT&T...\",\n            \"s3_link\": \"https://cdn.lexgenius.ai/20241015/dockets/txnd_24_02581_Raslavich_v_ATT_Inc.pdf\",\n            \n            # Test fields for deletion\n            \"temp_processing_field\": \"DELETE_ME\",\n            \"internal_metadata\": \"DELETE_ME_TOO\",\n            \"temp_cache_data\": \"ALSO_DELETE\",\n            \n            # Test fields with null/empty values\n            \"null_field\": None,\n            \"empty_string_field\": \"\",\n            \"empty_list_field\": [],\n            \"empty_dict_field\": {},\n            \"zero_value\": 0,  # Should be preserved\n            \"false_value\": False,  # Should be preserved\n            \n            # HTML fields for parsing tests\n            \"case_summary\": \"<div>This case involves <b>jury demand by plaintiff</b> and is part of <strong>MDL-3114</strong></div>\",\n            \"party_details\": \"<table><tr><td>Lead Case:</td><td>2:18-mn-02873-RMG</td></tr><tr><td>Court:</td><td>District of South Carolina</td></tr></table>\",\n            \"attorney_html\": \"<div class='attorney-list'><p>Attorney: <span>Jane Smith</span> from <span>Smith Law Firm</span></p></div>\",\n            \"plain_text_field\": \"This is just plain text, no HTML\",\n            \n            # Nested structures with nulls\n            \"nested_data\": {\n                \"valid_nested\": \"keep_this\",\n                \"null_nested\": None,\n                \"empty_nested\": \"\",\n                \"deeply_nested\": {\n                    \"deep_valid\": \"keep_this_too\",\n                    \"deep_null\": None,\n                    \"deep_empty_list\": [],\n                    \"deep_zero\": 0,  # Should be preserved\n                    \"another_level\": {\n                        \"very_deep\": \"preserve_me\",\n                        \"very_deep_null\": None\n                    }\n                }\n            }\n        }\n    \n    def _get_transformation_options(self) -> Dict[str, Any]:\n        \"\"\"Get comprehensive transformation options for testing.\"\"\"\n        return {\n            \"keys_to_delete\": [\n                \"temp_processing_field\", \n                \"internal_metadata\", \n                \"temp_cache_data\",\n                \"nonexistent_key\"  # Should be ignored\n            ],\n            \"transformations\": {\n                \"versus\": str.title,  # Title case transformation\n                \"court_id\": lambda x: f\"court_{x.upper()}\",\n                \"plaintiff\": lambda x: list(set(x)),  # Remove duplicates\n                \"defendant\": lambda x: list(set(x)),  # Remove duplicates\n                \"flags\": lambda x: [flag.lower() for flag in x],  # Lowercase all\n                \"jury_demand\": {\n                    \"type\": \"mapping\",\n                    \"mapping\": {\n                        \"Both\": \"Both Parties\",\n                        \"Plaintiff\": \"Plaintiff Only\",\n                        \"Defendant\": \"Defendant Only\",\n                        \"None\": \"No Jury Demand\"\n                    }\n                }\n            },\n            \"fields_to_parse\": [\n                \"case_summary\", \n                \"party_details\", \n                \"attorney_html\",\n                \"plain_text_field\",  # Should not produce parsing results\n                \"nonexistent_field\"  # Should be ignored\n            ],\n            \"format_options\": {\n                \"remove_nulls\": True,\n                \"key_order\": [\n                    \"docket_num\", \"court_id\", \"versus\", \"filing_date\",\n                    \"plaintiff\", \"defendant\", \"attorney\", \"jury_demand\",\n                    \"is_mdl\", \"lead_case\"\n                ]\n            }\n        }\n    \n    async def test_key_deletion(self, sample_data: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Test key deletion functionality.\"\"\"\n        print(\"\\n=== Testing Key Deletion ===\")\n        \n        keys_to_delete = [\"temp_processing_field\", \"internal_metadata\", \"nonexistent_key\"]\n        \n        print(f\"Original keys: {len(sample_data)}\")\n        print(f\"Keys to delete: {keys_to_delete}\")\n        \n        result = await self.service.delete_keys(sample_data, keys_to_delete)\n        \n        # Validate results\n        deleted_count = 0\n        for key in keys_to_delete:\n            if key not in result and key in sample_data:\n                deleted_count += 1\n                print(f\"✅ Deleted key: {key}\")\n            elif key not in sample_data:\n                print(f\"⚠️  Key not in original data: {key}\")\n            else:\n                print(f\"❌ Failed to delete key: {key}\")\n        \n        print(f\"Final keys: {len(result)}\")\n        print(f\"Successfully deleted: {deleted_count} keys\")\n        \n        self.test_results.append({\n            \"test\": \"key_deletion\",\n            \"status\": \"passed\" if deleted_count >= 2 else \"failed\",\n            \"details\": f\"Deleted {deleted_count} keys\"\n        })\n        \n        return result\n    \n    async def test_value_transformations(self, sample_data: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Test value transformation functionality.\"\"\"\n        print(\"\\n=== Testing Value Transformations ===\")\n        \n        transformations = {\n            \"versus\": str.title,\n            \"court_id\": lambda x: f\"court_{x.upper()}\",\n            \"plaintiff\": lambda x: list(set(x)),  # Deduplicate\n            \"flags\": lambda x: [flag.lower() for flag in x],\n            \"jury_demand\": {\n                \"type\": \"mapping\",\n                \"mapping\": {\n                    \"Both\": \"Both Parties\",\n                    \"Plaintiff\": \"Plaintiff Only\"\n                }\n            }\n        }\n        \n        print(f\"Applying {len(transformations)} transformations...\")\n        \n        result = await self.service.transform_values(sample_data, transformations)\n        \n        # Validate transformations\n        validations = [\n            (\"versus\", \"title case\", lambda x: x.istitle()),\n            (\"court_id\", \"prefixed with 'court_'\", lambda x: x.startswith(\"court_\")),\n            (\"plaintiff\", \"deduplicated\", lambda x: len(x) == len(set(x))),\n            (\"flags\", \"lowercase\", lambda x: all(flag.islower() for flag in x)),\n            (\"jury_demand\", \"mapped value\", lambda x: x == \"Both Parties\")\n        ]\n        \n        passed_count = 0\n        for field, description, validator in validations:\n            if field in result:\n                try:\n                    if validator(result[field]):\n                        print(f\"✅ {field}: {description} - {result[field]}\")\n                        passed_count += 1\n                    else:\n                        print(f\"❌ {field}: {description} validation failed - {result[field]}\")\n                except Exception as e:\n                    print(f\"❌ {field}: validation error - {e}\")\n            else:\n                print(f\"⚠️  {field}: not found in result\")\n        \n        self.test_results.append({\n            \"test\": \"value_transformations\",\n            \"status\": \"passed\" if passed_count >= 4 else \"failed\",\n            \"details\": f\"Passed {passed_count}/{len(validations)} validations\"\n        })\n        \n        return result\n    \n    async def test_attorney_cleaning(self, sample_data: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Test attorney cleaning and deduplication.\"\"\"\n        print(\"\\n=== Testing Attorney Cleaning ===\")\n        \n        original_attorney_count = len(sample_data.get(\"attorney\", []))\n        print(f\"Original attorney count: {original_attorney_count}\")\n        \n        # Print original attorneys for reference\n        for i, attorney in enumerate(sample_data.get(\"attorney\", [])):\n            name = attorney.get(\"attorney_name\", \"Unknown\")\n            firm = attorney.get(\"law_firm\", \"Unknown\")\n            print(f\"  {i+1}. {name} at {firm}\")\n        \n        result = await self.service.clean_attorneys(sample_data)\n        \n        cleaned_attorney_count = len(result.get(\"attorney\", []))\n        print(f\"\\nCleaned attorney count: {cleaned_attorney_count}\")\n        \n        # Print cleaned attorneys\n        for i, attorney in enumerate(result.get(\"attorney\", [])):\n            name = attorney.get(\"attorney_name\", \"Unknown\")\n            firm = attorney.get(\"law_firm\", \"Unknown\")\n            print(f\"  {i+1}. {name} at {firm}\")\n        \n        # Validate deduplication\n        dedup_successful = cleaned_attorney_count < original_attorney_count\n        if dedup_successful:\n            print(f\"✅ Deduplication successful: {original_attorney_count} → {cleaned_attorney_count}\")\n        else:\n            print(f\"⚠️  No deduplication occurred: {original_attorney_count} → {cleaned_attorney_count}\")\n        \n        # Check for duplicate names (case-insensitive)\n        attorney_names = [a.get(\"attorney_name\", \"\").lower() for a in result.get(\"attorney\", [])]\n        unique_names = set(attorney_names)\n        no_duplicates = len(attorney_names) == len(unique_names)\n        \n        if no_duplicates:\n            print(\"✅ No duplicate attorney names found\")\n        else:\n            print(\"❌ Duplicate attorney names still present\")\n        \n        self.test_results.append({\n            \"test\": \"attorney_cleaning\",\n            \"status\": \"passed\" if dedup_successful and no_duplicates else \"failed\",\n            \"details\": f\"Reduced from {original_attorney_count} to {cleaned_attorney_count} attorneys\"\n        })\n        \n        return result\n    \n    async def test_html_parsing(self, sample_data: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Test HTML parsing integration.\"\"\"\n        print(\"\\n=== Testing HTML Parsing Integration ===\")\n        \n        fields_to_parse = [\"case_summary\", \"party_details\", \"attorney_html\", \"plain_text_field\"]\n        \n        print(f\"Fields to parse: {fields_to_parse}\")\n        print(\"HTML content samples:\")\n        for field in fields_to_parse:\n            if field in sample_data:\n                content = sample_data[field][:100] + \"...\" if len(sample_data[field]) > 100 else sample_data[field]\n                print(f\"  {field}: {content}\")\n        \n        result = await self.service.reparse_html_fields(sample_data, fields_to_parse)\n        \n        # Validate parsing results\n        parsed_fields = []\n        for field in fields_to_parse:\n            parsed_field = f\"{field}_parsed\"\n            if parsed_field in result:\n                parsed_fields.append(field)\n                print(f\"✅ {field}: HTML parsing generated {parsed_field}\")\n                if self.verbose:\n                    print(f\"    Parsed result: {result[parsed_field]}\")\n            else:\n                print(f\"⚠️  {field}: No parsed result generated\")\n        \n        # Check for specific extractions\n        jury_extracted = any(\"jury_demand\" in str(result.get(f\"{f}_parsed\", \"\")) for f in fields_to_parse)\n        mdl_extracted = any(\"mdl\" in str(result.get(f\"{f}_parsed\", \"\")).lower() for f in fields_to_parse)\n        lead_case_extracted = any(\"lead_case\" in str(result.get(f\"{f}_parsed\", \"\")) for f in fields_to_parse)\n        \n        extractions = {\n            \"jury_demand\": jury_extracted,\n            \"mdl_info\": mdl_extracted,\n            \"lead_case\": lead_case_extracted\n        }\n        \n        print(\"\\nSpecific extractions:\")\n        for extraction, found in extractions.items():\n            status = \"✅\" if found else \"❌\"\n            print(f\"  {status} {extraction}: {'Found' if found else 'Not found'}\")\n        \n        self.test_results.append({\n            \"test\": \"html_parsing\",\n            \"status\": \"passed\" if len(parsed_fields) >= 3 else \"failed\",\n            \"details\": f\"Parsed {len(parsed_fields)}/{len(fields_to_parse)} fields\"\n        })\n        \n        return result\n    \n    async def test_final_formatting(self, sample_data: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Test final formatting with null handling and key ordering.\"\"\"\n        print(\"\\n=== Testing Final Formatting ===\")\n        \n        # Count null/empty values before formatting\n        def count_nulls(data, path=\"\"):\n            count = 0\n            if isinstance(data, dict):\n                for key, value in data.items():\n                    current_path = f\"{path}.{key}\" if path else key\n                    if value is None or value == \"\" or value == []:\n                        count += 1\n                        if self.verbose:\n                            print(f\"    Null/empty at {current_path}: {type(value).__name__}\")\n                    elif isinstance(value, (dict, list)):\n                        count += count_nulls(value, current_path)\n            elif isinstance(data, list):\n                for i, item in enumerate(data):\n                    current_path = f\"{path}[{i}]\"\n                    if item is None or item == \"\" or item == []:\n                        count += 1\n                        if self.verbose:\n                            print(f\"    Null/empty at {current_path}: {type(item).__name__}\")\n                    elif isinstance(item, (dict, list)):\n                        count += count_nulls(item, current_path)\n            return count\n        \n        original_nulls = count_nulls(sample_data)\n        print(f\"Original null/empty values: {original_nulls}\")\n        \n        format_options = {\n            \"remove_nulls\": True,\n            \"key_order\": [\"docket_num\", \"court_id\", \"versus\", \"filing_date\", \"attorney\"]\n        }\n        \n        result = await self.service.format_final(sample_data, format_options)\n        \n        # Count remaining nulls\n        remaining_nulls = count_nulls(result)\n        print(f\"Remaining null/empty values: {remaining_nulls}\")\n        \n        # Check that important zero/false values are preserved\n        zero_preserved = result.get(\"zero_value\") == 0\n        false_preserved = result.get(\"false_value\") is False\n        \n        print(f\"Zero value preserved: {'✅' if zero_preserved else '❌'}\")\n        print(f\"False value preserved: {'✅' if false_preserved else '❌'}\")\n        \n        # Check key ordering\n        result_keys = list(result.keys())\n        expected_order = [\"docket_num\", \"court_id\", \"versus\", \"filing_date\", \"attorney\"]\n        \n        order_correct = True\n        print(\"\\nKey ordering:\")\n        for i, key in enumerate(expected_order[:-1]):\n            if key in result and expected_order[i+1] in result:\n                pos1 = result_keys.index(key)\n                pos2 = result_keys.index(expected_order[i+1])\n                if pos1 < pos2:\n                    print(f\"  ✅ {key} comes before {expected_order[i+1]}\")\n                else:\n                    print(f\"  ❌ {key} should come before {expected_order[i+1]}\")\n                    order_correct = False\n        \n        # Show first 10 keys in final order\n        print(f\"\\nFirst 10 keys in result: {result_keys[:10]}\")\n        \n        null_removal_successful = remaining_nulls < original_nulls\n        values_preserved = zero_preserved and false_preserved\n        \n        self.test_results.append({\n            \"test\": \"final_formatting\",\n            \"status\": \"passed\" if null_removal_successful and values_preserved and order_correct else \"failed\",\n            \"details\": f\"Nulls: {original_nulls}→{remaining_nulls}, Order: {'OK' if order_correct else 'FAIL'}\"\n        })\n        \n        return result\n    \n    async def test_complete_pipeline(self, sample_data: Dict[str, Any]) -> Dict[str, Any]:\n        \"\"\"Test complete transformation pipeline end-to-end.\"\"\"\n        print(\"\\n=== Testing Complete Transformation Pipeline ===\")\n        \n        options = self._get_transformation_options()\n        \n        print(\"Executing complete transformation pipeline...\")\n        print(f\"  - Keys to delete: {len(options['keys_to_delete'])}\")\n        print(f\"  - Value transformations: {len(options['transformations'])}\")\n        print(f\"  - HTML fields to parse: {len(options['fields_to_parse'])}\")\n        print(f\"  - Format options: {list(options['format_options'].keys())}\")\n        \n        start_time = time.time()\n        result = await self.service.transform_complete(sample_data, options)\n        end_time = time.time()\n        \n        processing_time = end_time - start_time\n        print(f\"\\nPipeline completed in {processing_time:.3f} seconds\")\n        \n        # Validate complete pipeline results\n        validations = {\n            \"Key deletion\": lambda r: \"temp_processing_field\" not in r,\n            \"Value transformation\": lambda r: r.get(\"versus\", \"\").istitle(),\n            \"Attorney deduplication\": lambda r: len(r.get(\"attorney\", [])) < len(sample_data.get(\"attorney\", [])),\n            \"HTML parsing\": lambda r: any(k.endswith(\"_parsed\") for k in r.keys()),\n            \"Null removal\": lambda r: \"null_field\" not in r and \"empty_string_field\" not in r,\n            \"Key ordering\": lambda r: list(r.keys()).index(\"docket_num\") < list(r.keys()).index(\"versus\") if \"docket_num\" in r and \"versus\" in r else True\n        }\n        \n        print(\"\\nPipeline validation:\")\n        passed_validations = 0\n        for validation_name, validator in validations.items():\n            try:\n                if validator(result):\n                    print(f\"  ✅ {validation_name}\")\n                    passed_validations += 1\n                else:\n                    print(f\"  ❌ {validation_name}\")\n            except Exception as e:\n                print(f\"  ❌ {validation_name}: Error - {e}\")\n        \n        pipeline_success = passed_validations >= 5\n        \n        print(f\"\\nPipeline validation: {passed_validations}/{len(validations)} passed\")\n        print(f\"Original data size: {len(sample_data)} keys\")\n        print(f\"Final data size: {len(result)} keys\")\n        \n        self.test_results.append({\n            \"test\": \"complete_pipeline\",\n            \"status\": \"passed\" if pipeline_success else \"failed\",\n            \"details\": f\"{passed_validations}/{len(validations)} validations passed, {processing_time:.3f}s\"\n        })\n        \n        return result\n    \n    def print_test_summary(self):\n        \"\"\"Print summary of all test results.\"\"\"\n        print(\"\\n\" + \"=\"*60)\n        print(\"JSON TRANSFORMATION SERVICE TEST SUMMARY\")\n        print(\"=\"*60)\n        \n        total_tests = len(self.test_results)\n        passed_tests = sum(1 for result in self.test_results if result[\"status\"] == \"passed\")\n        \n        print(f\"Total tests run: {total_tests}\")\n        print(f\"Tests passed: {passed_tests}\")\n        print(f\"Tests failed: {total_tests - passed_tests}\")\n        print(f\"Success rate: {(passed_tests / total_tests * 100):.1f}%\" if total_tests > 0 else \"No tests run\")\n        \n        print(\"\\nDetailed results:\")\n        for result in self.test_results:\n            status_icon = \"✅\" if result[\"status\"] == \"passed\" else \"❌\"\n            print(f\"  {status_icon} {result['test']}: {result['details']}\")\n        \n        if passed_tests == total_tests:\n            print(\"\\n🎉 All tests passed! JSON transformation service is working correctly.\")\n        else:\n            print(f\"\\n⚠️  {total_tests - passed_tests} test(s) failed. Please review the implementation.\")\n        \n        return passed_tests == total_tests\n    \n    async def run_all_tests(self) -> bool:\n        \"\"\"Run all JSON transformation tests.\"\"\"\n        print(\"JSON Transformation Service - Comprehensive Test Suite\")\n        print(\"=\" * 60)\n        \n        # Initialize service\n        config = {\n            \"transformation\": {\n                \"enable_html_parsing\": True,\n                \"attorney_deduplication\": {\n                    \"case_sensitive\": False,\n                    \"normalize_firms\": True\n                },\n                \"formatting\": {\n                    \"remove_nulls\": True,\n                    \"remove_empty_strings\": True,\n                    \"remove_empty_lists\": True\n                }\n            }\n        }\n        \n        self.service = JsonTransformationService(logger=self.logger, config=config)\n        self.service.set_html_parser(self.html_parser)\n        \n        # Get sample data\n        sample_data = self._get_sample_json_data()\n        \n        print(f\"Sample data loaded: {len(sample_data)} keys\")\n        print(f\"Sample attorneys: {len(sample_data.get('attorney', []))}\")\n        print(f\"Verbose mode: {'Enabled' if self.verbose else 'Disabled'}\")\n        \n        try:\n            # Run individual tests\n            await self.test_key_deletion(sample_data.copy())\n            await self.test_value_transformations(sample_data.copy())\n            await self.test_attorney_cleaning(sample_data.copy())\n            await self.test_html_parsing(sample_data.copy())\n            await self.test_final_formatting(sample_data.copy())\n            \n            # Run complete pipeline test\n            await self.test_complete_pipeline(sample_data.copy())\n            \n            # Print summary\n            success = self.print_test_summary()\n            \n            return success\n            \n        except Exception as e:\n            print(f\"\\n❌ Test execution failed with error: {e}\")\n            import traceback\n            traceback.print_exc()\n            return False\n        \n        finally:\n            # Cleanup\n            if self.service:\n                await self.service.cleanup()\n\n\nasync def main():\n    \"\"\"Main test runner function.\"\"\"\n    verbose = os.getenv(\"VERBOSE\", \"0\") == \"1\"\n    \n    runner = JsonTransformationTestRunner(verbose=verbose)\n    success = await runner.run_all_tests()\n    \n    # Exit with appropriate code\n    sys.exit(0 if success else 1)\n\n\nif __name__ == \"__main__\":\n    # Run the test suite\n    asyncio.run(main())\n"