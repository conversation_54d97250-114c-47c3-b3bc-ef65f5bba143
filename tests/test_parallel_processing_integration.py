#!/usr/bin/env python
"""
Test to verify that multiple browsers are created when processing multiple courts in parallel.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.pacer.jobs.job_runner_service import PacerJobRunnerService
from src.pacer.jobs.jobs_models import PacerJob


async def test_browser_creation():
    """Test that browser creation works with new architecture."""
    
    # Create a mock job
    from datetime import datetime, timedelta
    today = datetime.now()
    yesterday = today - timedelta(days=1)
    
    job = PacerJob(
        court_id="ilnd",
        iso_date="20250120",
        start_date=yesterday,
        end_date=today,
        config_snapshot={
            "headless": False,  # Visible browser for testing
            "timeout_ms": 60000
        }
    )
    
    # Create a minimal runner service (without full DI)
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    runner = PacerJobRunnerService(
        config={"use_browser_pool": False},
        logger=logger,
        pacer_orchestrator=None,  # We're just testing browser creation
        browser_pool_manager=None
    )
    
    print(f"Creating browser for job {job.job_id}...")
    
    try:
        # Test browser creation
        playwright, browser = await runner._create_isolated_browser(job, runner.logger)
        
        print(f"✅ Browser created successfully!")
        print(f"   Browser type: {browser.browser_type.name}")
        print(f"   Browser version: {browser.version}")
        
        # Create a context
        context = await runner._create_isolated_context(browser, job)
        print(f"✅ Context created successfully!")
        print(f"   Download path: {job.config_snapshot.get('actual_download_path')}")
        
        # Create a page (like workflow orchestrator does)
        page = await context.new_page()
        print(f"✅ Page created successfully!")
        
        # Now the workflow orchestrator would create PacerNavigator here
        from src.pacer.components.browser.navigator import PacerNavigator
        navigator = PacerNavigator(
            page=page,
            config=job.config_snapshot,
            screenshot_dir='./data/screenshots',
            logger=runner.logger
        )
        print(f"✅ PacerNavigator created successfully!")
        
        # Keep browser open for a moment so we can see it
        print("\n🔍 Browser window should be visible now...")
        await asyncio.sleep(2)
        
        # Cleanup
        await context.close()
        await browser.close()
        await playwright.stop()
        
        print("\n✅ All tests passed! Browser architecture is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_browser_creation())
    sys.exit(0 if success else 1)