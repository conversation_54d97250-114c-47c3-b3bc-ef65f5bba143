#!/usr/bin/env python3
"""
Test case for company name parsing fix.
This demonstrates the issue with comma-splitting in defendant names.
"""

import pytest
from bs4 import BeautifulSoup
from src.services.html.case_parser_service import CaseParserService
from unittest.mock import Mock

def test_defendant_company_name_preservation():
    """Test that defendant company names with commas are preserved intact."""
    
    # Mock logger
    mock_logger = Mock()
    parser = CaseParserService(logger=mock_logger)
    
    # Sample HTML with company names containing commas and special characters
    html_content = """
    <html>
    <body>
        <div id="cmecfMainContent">
            <h3 align="center">
                U.S. District Court<br/>
                Northern District of California (San Francisco)<br/>
                CIVIL DOCKET FOR CASE #: 3:25-cv-05328-WHO
            </h3>
            <table cellpadding="1">
                <tr>
                    <td valign="top">John Doe v. XYZ Company, Inc., et al</td>
                </tr>
            </table>
            <table>
                <tr><td>Plaintiff</td></tr>
                <tr>
                    <td><b><PERSON></b></td>
                    <td>&nbsp;</td>
                    <td>Attorney Info</td>
                </tr>
                <tr><td>Defendant</td></tr>
                <tr>
                    <td><b>XYZ Company, Inc.</b></td>
                </tr>
                <tr>
                    <td><b>ABC Corporation, LLC</b></td>
                </tr>
                <tr>
                    <td><b>Johnson & Associates, P.C.</b></td>
                </tr>
                <tr>
                    <td><b>Smith, Jones & Partners (Holding Company)</b></td>
                </tr>
            </table>
        </div>
    </body>
    </html>
    """
    
    # Parse the HTML
    parser.set_html(html_content)
    result = parser.parse()
    
    # Print results for debugging
    print("Parsed defendants:", result.get('defendants', []))
    
    # Verify that company names are preserved intact
    defendants = result.get('defendants', [])
    
    # Expected company names (should NOT be split by commas)
    expected_defendants = [
        "XYZ Company, Inc.",
        "ABC Corporation, LLC", 
        "Johnson & Associates, P.C.",
        "Smith, Jones & Partners (Holding Company)"
    ]
    
    assert len(defendants) == 4, f"Expected 4 defendants, got {len(defendants)}: {defendants}"
    
    for expected in expected_defendants:
        assert expected in defendants, f"Expected defendant '{expected}' not found in {defendants}"
    
    # Verify no comma-splitting occurred
    for defendant in defendants:
        if "," in defendant:
            # These should still contain commas - they shouldn't be split
            assert any(expected_name in defendant for expected_name in expected_defendants), \
                f"Defendant '{defendant}' appears to be incorrectly split"

if __name__ == "__main__":
    test_defendant_company_name_preservation()
    print("✅ Test completed - check output for current behavior")