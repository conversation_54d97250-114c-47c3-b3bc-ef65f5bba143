#!/usr/bin/env python3
"""
Debug script to identify filename normalization issues
"""

import asyncio
import sys
import os
from unittest.mock import Mock

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.services.html.html_processing_orchestrator import HTMLProcessingOrchestrator
from tests.mocks.mock_s3_enhanced import MockS3AsyncStorage

async def debug_filename_normalization():
    """Debug filename normalization in the HTML processing orchestrator"""
    
    # Setup
    mock_logger = Mock()
    mock_s3_storage = MockS3AsyncStorage()
    
    orchestrator = HTMLProcessingOrchestrator(
        logger=mock_logger,
        config={},
        court_id="nysd",
        s3_async_storage=mock_s3_storage
    )
    
    # Test case details with exact capitalization
    case_details = {
        "docket_num": "1:25-cv-06740",
        "court_id": "nysd",
        "base_filename": "nysd_25_06740_Williams_et_al_v_The_3M_Company_et_al",  # Note capitalization
        "new_filename": "nysd_25_06740_Williams_et_al_v_The_3M_Company_et_al.json"
    }
    
    json_path = "/data/20250814/processed/nysd_25_06740_Williams_et_al_v_The_3M_Company_et_al.json"
    
    print("Original case details:")
    print(f"  base_filename: {case_details['base_filename']}")
    print(f"  new_filename: {case_details['new_filename']}")
    
    # Add file to S3 storage with exact capitalization
    expected_s3_key = "20250814/html/nysd_25_06740_Williams_et_al_v_The_3M_Company_et_al.html"
    mock_s3_storage.add_test_file(expected_s3_key, "<html>test</html>")
    
    print(f"\nAdded S3 file with key: {expected_s3_key}")
    
    # Test S3 link construction
    s3_link = await orchestrator.construct_s3_html_link(json_path, case_details)
    
    print(f"\nGenerated S3 link: {s3_link}")
    
    # Check what's in S3 storage
    print(f"\nS3 storage keys:")
    for key in mock_s3_storage.storage.keys():
        print(f"  {key}")
    
    # Test file existence with exact key
    exists = await mock_s3_storage.file_exists(expected_s3_key)
    print(f"\nFile exists check for exact key: {exists}")
    
    # Let's trace through the S3 link construction manually
    print(f"\nTracing S3 link construction:")
    
    import re
    
    # Extract date
    match = re.search(r"(\d{8})", json_path)
    date_str = match.group(1) if match else None
    print(f"  Extracted date: {date_str}")
    
    # Try strategies
    new_filename = case_details.get("new_filename", "")
    base_filename = case_details.get("base_filename", "")
    
    print(f"  new_filename: {new_filename}")
    print(f"  base_filename: {base_filename}")
    
    if new_filename:
        html_filename = os.path.splitext(new_filename)[0] + ".html"
        print(f"  Constructed HTML filename from new_filename: {html_filename}")
    
    if base_filename:
        html_filename = f"{base_filename}.html"
        print(f"  Constructed HTML filename from base_filename: {html_filename}")
    
    # Manual S3 key construction
    if date_str and base_filename:
        manual_s3_key = f"{date_str}/html/{base_filename}.html"
        print(f"  Manual S3 key: {manual_s3_key}")
        
        manual_cdn_url = f"https://cdn.lexgenius.ai/{manual_s3_key}"
        print(f"  Manual CDN URL: {manual_cdn_url}")
        
        # Check existence
        manual_exists = await mock_s3_storage.file_exists(manual_s3_key)
        print(f"  Manual key exists: {manual_exists}")

if __name__ == "__main__":
    asyncio.run(debug_filename_normalization())