#!/usr/bin/env python3
"""
Real Match/Case Conversion Candidates Analysis
==============================================

This module identifies and validates actual if/elif chains from the codebase
that are good candidates for match/case conversion.
"""

import ast
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from validation_framework import MatchCaseValidator, ConversionCandidate, ConversionDetector

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class RealWorldConversionAnalyzer:
    """Analyzes real codebase patterns for match/case conversion opportunities"""
    
    def __init__(self):
        self.validator = MatchCaseValidator()
        self.detector = ConversionDetector(min_elif_count=2)
        self.project_root = Path("/Users/<USER>/PycharmProjects/lexgenius")
    
    def scan_project(self) -> List[ConversionCandidate]:
        """Scan the entire project for conversion candidates"""
        candidates = []
        
        # Scan Python files in key directories
        key_dirs = [
            "src",
            "scripts", 
            "tests"
        ]
        
        for dir_name in key_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                for py_file in dir_path.rglob("*.py"):
                    try:
                        file_candidates = self.detector.scan_file(py_file)
                        candidates.extend(file_candidates)
                    except Exception as e:
                        print(f"Error scanning {py_file}: {e}")
        
        return candidates
    
    def create_specific_test_cases(self) -> List[ConversionCandidate]:
        """Create test cases based on specific patterns found in the codebase"""
        
        test_cases = []
        
        # Test Case 1: Action-based dispatch pattern (from query_processor.py)
        action_dispatch_original = '''
if action == 'search_by_title':
    return await self.search_by_title(data['search_string'])
elif action == 'search_by_law_firm':
    return await self.search_by_law_firm(
        data['law_firm'], data.get('filing_date')
    )
elif action == 'search_by_law_firm_with_totals':
    return await self.search_by_law_firm_with_totals(data['law_firm'])
elif action == 'search_by_defendant':
    return await self.search_by_defendant(data['defendant'])
elif action == 'search_by_versus':
    return await self.search_by_versus(data['versus'])
elif action == 'search_by_summary':
    return await self.search_by_summary(data['summary'])
else:
    raise PacerServiceError("Unknown action")
'''
        
        action_dispatch_converted = '''
match action:
    case 'search_by_title':
        return await self.search_by_title(data['search_string'])
    case 'search_by_law_firm':
        return await self.search_by_law_firm(
            data['law_firm'], data.get('filing_date')
        )
    case 'search_by_law_firm_with_totals':
        return await self.search_by_law_firm_with_totals(data['law_firm'])
    case 'search_by_defendant':
        return await self.search_by_defendant(data['defendant'])
    case 'search_by_versus':
        return await self.search_by_versus(data['versus'])
    case 'search_by_summary':
        return await self.search_by_summary(data['summary'])
    case _:
        raise PacerServiceError("Unknown action")
'''
        
        test_cases.append(ConversionCandidate(
            file_path="src/pacer/components/query/query_processor.py",
            line_start=47,
            line_end=65,
            original_code=action_dispatch_original.strip(),
            converted_code=action_dispatch_converted.strip()
        ))
        
        # Test Case 2: Status/type checking pattern
        status_check_original = '''
if status == "pending":
    self.log_info("Processing pending item")
    result = self.process_pending()
elif status == "approved":
    self.log_info("Processing approved item")  
    result = self.process_approved()
elif status == "rejected":
    self.log_info("Processing rejected item")
    result = self.process_rejected()
elif status == "cancelled":
    self.log_info("Processing cancelled item")
    result = self.process_cancelled()
else:
    self.log_warning(f"Unknown status: {status}")
    result = self.process_unknown()
'''
        
        status_check_converted = '''
match status:
    case "pending":
        self.log_info("Processing pending item")
        result = self.process_pending()
    case "approved":
        self.log_info("Processing approved item")  
        result = self.process_approved()
    case "rejected":
        self.log_info("Processing rejected item")
        result = self.process_rejected()
    case "cancelled":
        self.log_info("Processing cancelled item")
        result = self.process_cancelled()
    case _:
        self.log_warning(f"Unknown status: {status}")
        result = self.process_unknown()
'''
        
        test_cases.append(ConversionCandidate(
            file_path="test_status_checking.py",
            line_start=1,
            line_end=16,
            original_code=status_check_original.strip(),
            converted_code=status_check_converted.strip()
        ))
        
        # Test Case 3: Complex condition pattern (should trigger warnings)
        complex_condition_original = '''
if x > 0 and isinstance(y, str) and len(y) > 5:
    result = "complex_condition_1"
elif x <= 0 and isinstance(y, int):
    result = "complex_condition_2"  
elif x > 10 or y is None:
    result = "complex_condition_3"
else:
    result = "default"
'''
        
        # This intentionally stays as if/elif because match/case isn't suitable
        complex_condition_converted = complex_condition_original
        
        test_cases.append(ConversionCandidate(
            file_path="test_complex_conditions.py", 
            line_start=1,
            line_end=8,
            original_code=complex_condition_original.strip(),
            converted_code=complex_condition_converted.strip()
        ))
        
        # Test Case 4: File type/extension handling
        file_type_original = '''
if file_ext == '.json':
    parser = JsonParser()
    result = parser.parse(data)
elif file_ext == '.xml':
    parser = XmlParser()
    result = parser.parse(data)
elif file_ext == '.yaml' or file_ext == '.yml':
    parser = YamlParser()
    result = parser.parse(data)  
elif file_ext == '.csv':
    parser = CsvParser()
    result = parser.parse(data)
else:
    raise ValueError(f"Unsupported file type: {file_ext}")
'''
        
        file_type_converted = '''
match file_ext:
    case '.json':
        parser = JsonParser()
        result = parser.parse(data)
    case '.xml':
        parser = XmlParser()
        result = parser.parse(data)
    case '.yaml' | '.yml':
        parser = YamlParser()
        result = parser.parse(data)
    case '.csv':
        parser = CsvParser()
        result = parser.parse(data)
    case _:
        raise ValueError(f"Unsupported file type: {file_ext}")
'''
        
        test_cases.append(ConversionCandidate(
            file_path="test_file_type_handling.py",
            line_start=1,
            line_end=14,
            original_code=file_type_original.strip(),
            converted_code=file_type_converted.strip()
        ))
        
        return test_cases
    
    def analyze_edge_cases(self) -> List[ConversionCandidate]:
        """Create test cases for edge cases and potential issues"""
        
        edge_cases = []
        
        # Edge Case 1: Variable assignment in conditions
        var_assign_original = '''
if (result := expensive_computation()) == "success":
    process_success(result)
elif (result := expensive_computation()) == "failure":
    process_failure(result)
elif (result := expensive_computation()) == "partial":
    process_partial(result)
else:
    process_unknown()
'''
        
        # This should NOT be converted - walrus operator behavior changes
        var_assign_converted = var_assign_original
        
        edge_cases.append(ConversionCandidate(
            file_path="test_walrus_operator.py",
            line_start=1,
            line_end=8,
            original_code=var_assign_original.strip(),
            converted_code=var_assign_converted.strip()
        ))
        
        # Edge Case 2: Exception handling within branches
        exception_handling_original = '''
if operation_type == "read":
    try:
        result = file.read()
    except IOError:
        result = None
elif operation_type == "write":
    try:
        result = file.write(data)
    except IOError:
        result = False
elif operation_type == "delete":
    try:
        result = file.delete()
    except IOError:
        result = False
else:
    result = None
'''
        
        exception_handling_converted = '''
match operation_type:
    case "read":
        try:
            result = file.read()
        except IOError:
            result = None
    case "write":
        try:
            result = file.write(data)
        except IOError:
            result = False
    case "delete":
        try:
            result = file.delete()
        except IOError:
            result = False
    case _:
        result = None
'''
        
        edge_cases.append(ConversionCandidate(
            file_path="test_exception_handling.py",
            line_start=1,
            line_end=20,
            original_code=exception_handling_original.strip(),
            converted_code=exception_handling_converted.strip()
        ))
        
        return edge_cases
    
    def run_comprehensive_analysis(self):
        """Run comprehensive analysis of match/case conversion opportunities"""
        
        print("🔍 Real-World Match/Case Conversion Analysis")
        print("=" * 60)
        
        # Test specific patterns
        print("\n📋 Testing Specific Conversion Patterns")
        print("-" * 40)
        
        test_cases = self.create_specific_test_cases()
        
        for i, candidate in enumerate(test_cases, 1):
            print(f"\n🎯 Test Case {i}: {candidate.file_path}")
            validated = self.validator.validate_conversion(candidate)
            self._print_validation_results(validated)
        
        # Test edge cases
        print("\n📋 Testing Edge Cases")
        print("-" * 25)
        
        edge_cases = self.analyze_edge_cases()
        
        for i, candidate in enumerate(edge_cases, 1):
            print(f"\n⚠️  Edge Case {i}: {candidate.file_path}")
            validated = self.validator.validate_conversion(candidate)
            self._print_validation_results(validated)
        
        # Scan actual project (sample only to avoid overwhelming output)
        print("\n📋 Scanning Project Files (Sample)")
        print("-" * 35)
        
        try:
            project_candidates = self.scan_project()
            print(f"Found {len(project_candidates)} potential conversion candidates")
            
            # Show top 3 candidates
            for i, candidate in enumerate(project_candidates[:3], 1):
                print(f"\n🔍 Project Candidate {i}: {Path(candidate.file_path).name}")
                print(f"Lines {candidate.line_start}-{candidate.line_end}")
                print("Code snippet:")
                print(candidate.original_code[:200] + "..." if len(candidate.original_code) > 200 else candidate.original_code)
                
        except Exception as e:
            print(f"Error scanning project: {e}")
        
        print(f"\n🎉 Analysis completed!")
    
    def _print_validation_results(self, candidate: ConversionCandidate):
        """Print validation results in a formatted way"""
        
        for rule_name, result, message in candidate.validation_results:
            icon = "✅" if result.value == "PASS" else "❌" if result.value == "FAIL" else "⚠️"
            rule_short = rule_name.replace("_validate_", "").replace("_", " ").title()
            print(f"  {icon} {rule_short}: {message}")


def main():
    """Main analysis runner"""
    analyzer = RealWorldConversionAnalyzer()
    analyzer.run_comprehensive_analysis()


if __name__ == "__main__":
    main()