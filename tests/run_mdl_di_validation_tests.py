#!/usr/bin/env python3
# /tests/run_mdl_di_validation_tests.py
"""
MDL DI Injection Validation Test Runner.

This script runs comprehensive tests for MDL dependency injection validation,
including service registration, data loading, integration, and warning elimination.
"""

import asyncio
import sys
import logging
import time
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import test modules
from tests.unit.services.transformer.test_mdl_di_integration import (
    TestMDLDIContainerIntegration,
    TestMDLDataLoadingIntegration,
    TestTransformerMDLIntegration,
    TestLoggerIntegration,
    TestWarningEliminationIntegration
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MDLDIValidationTestRunner:
    """Test runner for MDL DI validation tests."""
    
    def __init__(self):
        """Initialize the test runner."""
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'execution_time': 0.0
        }
    
    async def run_di_container_tests(self) -> Dict[str, Any]:
        """Run DI container integration tests."""
        logger.info("Running DI Container Integration Tests...")
        test_instance = TestMDLDIContainerIntegration()
        
        test_methods = [
            'test_mdl_lookup_service_registration',
            'test_mdl_service_injection_into_processor',
            'test_di_container_lifecycle_management',
            'test_service_dependency_resolution'
        ]
        
        results = await self._run_test_methods(test_instance, test_methods)
        logger.info(f"DI Container Tests: {results['passed']}/{results['total']} passed")
        return results
    
    async def run_data_loading_tests(self) -> Dict[str, Any]:
        """Run MDL data loading tests."""
        logger.info("Running MDL Data Loading Tests...")
        test_instance = TestMDLDataLoadingIntegration()
        
        test_methods = [
            'test_mdl_json_file_loading',
            'test_mdl_data_validation',
            'test_mdl_lookup_by_number',
            'test_mdl_litigation_search',
            'test_file_not_found_handling',
            'test_malformed_json_handling'
        ]
        
        results = await self._run_test_methods(test_instance, test_methods)
        logger.info(f"Data Loading Tests: {results['passed']}/{results['total']} passed")
        return results
    
    async def run_transformer_integration_tests(self) -> Dict[str, Any]:
        """Run transformer integration tests."""
        logger.info("Running Transformer Integration Tests...")
        test_instance = TestTransformerMDLIntegration()
        
        test_methods = [
            'test_data_processing_engine_mdl_integration',
            'test_mdl_processor_component_integration',
            'test_mdl_lookup_manager_integration',
            'test_warning_elimination_validation'
        ]
        
        results = await self._run_test_methods(test_instance, test_methods)
        logger.info(f"Transformer Integration Tests: {results['passed']}/{results['total']} passed")
        return results
    
    async def run_logger_integration_tests(self) -> Dict[str, Any]:
        """Run logger integration tests."""
        logger.info("Running Logger Integration Tests...")
        test_instance = TestLoggerIntegration()
        
        test_methods = [
            'test_component_base_logger_usage',
            'test_async_service_base_inheritance',
            'test_logger_configuration_in_di'
        ]
        
        results = await self._run_test_methods(test_instance, test_methods)
        logger.info(f"Logger Integration Tests: {results['passed']}/{results['total']} passed")
        return results
    
    async def run_warning_elimination_tests(self) -> Dict[str, Any]:
        """Run warning elimination tests."""
        logger.info("Running Warning Elimination Tests...")
        test_instance = TestWarningEliminationIntegration()
        
        test_methods = [
            'test_no_mdl_warning_with_proper_service',
            'test_specific_warning_elimination_in_data_processing_engine',
            'test_fallback_behavior_without_warnings',
            'test_legacy_warning_path_identification'
        ]
        
        results = await self._run_test_methods(test_instance, test_methods)
        logger.info(f"Warning Elimination Tests: {results['passed']}/{results['total']} passed")
        return results
    
    async def _run_test_methods(self, test_instance: Any, test_methods: List[str]) -> Dict[str, Any]:
        """Run a list of test methods on a test instance."""
        results = {
            'total': len(test_methods),
            'passed': 0,
            'failed': 0,
            'details': []
        }
        
        # Set up test instance if needed
        if hasattr(test_instance, 'asyncSetUp'):
            try:
                await test_instance.asyncSetUp()
            except Exception as e:
                logger.warning(f"Setup failed for {test_instance.__class__.__name__}: {e}")
        elif hasattr(test_instance, 'setup_method'):
            try:
                test_instance.setup_method()
            except Exception as e:
                logger.warning(f"Setup failed for {test_instance.__class__.__name__}: {e}")
        
        for method_name in test_methods:
            try:
                method = getattr(test_instance, method_name)
                
                start_time = time.time()
                if asyncio.iscoroutinefunction(method):
                    await method()
                else:
                    method()
                execution_time = time.time() - start_time
                
                results['passed'] += 1
                results['details'].append({
                    'test': method_name,
                    'status': 'PASSED',
                    'execution_time': execution_time,
                    'error': None
                })
                logger.info(f"✓ {method_name} passed ({execution_time:.3f}s)")
                
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'test': method_name,
                    'status': 'FAILED',
                    'execution_time': 0.0,
                    'error': str(e)
                })
                logger.error(f"✗ {method_name} failed: {e}")
        
        # Tear down test instance if needed
        if hasattr(test_instance, 'asyncTearDown'):
            try:
                await test_instance.asyncTearDown()
            except Exception as e:
                logger.warning(f"Teardown failed for {test_instance.__class__.__name__}: {e}")
        
        return results
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all MDL DI validation tests."""
        logger.info("Starting comprehensive MDL DI validation tests...")
        start_time = time.time()
        
        # Run all test suites
        test_suites = [
            ('DI Container', self.run_di_container_tests),
            ('Data Loading', self.run_data_loading_tests),
            ('Transformer Integration', self.run_transformer_integration_tests),
            ('Logger Integration', self.run_logger_integration_tests),
            ('Warning Elimination', self.run_warning_elimination_tests)
        ]
        
        all_results = {
            'test_suites': {},
            'summary': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0,
                'success_rate': 0.0
            },
            'execution_time': 0.0
        }
        
        for suite_name, test_func in test_suites:
            try:
                suite_results = await test_func()
                all_results['test_suites'][suite_name] = suite_results
                
                all_results['summary']['total_tests'] += suite_results['total']
                all_results['summary']['passed_tests'] += suite_results['passed']
                all_results['summary']['failed_tests'] += suite_results['failed']
                
            except Exception as e:
                logger.error(f"Test suite '{suite_name}' failed to execute: {e}")
                all_results['test_suites'][suite_name] = {
                    'total': 0,
                    'passed': 0,
                    'failed': 1,
                    'error': str(e)
                }
                all_results['summary']['failed_tests'] += 1
        
        # Calculate summary statistics
        total = all_results['summary']['total_tests']
        passed = all_results['summary']['passed_tests']
        all_results['summary']['success_rate'] = (passed / total * 100) if total > 0 else 0.0
        all_results['execution_time'] = time.time() - start_time
        
        return all_results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print test execution summary."""
        print("\n" + "="*80)
        print("MDL DI VALIDATION TEST RESULTS")
        print("="*80)
        
        summary = results['summary']
        print(f"Total Tests:    {summary['total_tests']}")
        print(f"Passed Tests:   {summary['passed_tests']}")
        print(f"Failed Tests:   {summary['failed_tests']}")
        print(f"Success Rate:   {summary['success_rate']:.1f}%")
        print(f"Execution Time: {results['execution_time']:.2f}s")
        
        print("\nTest Suite Results:")
        for suite_name, suite_results in results['test_suites'].items():
            if 'error' in suite_results:
                print(f"  {suite_name}: ERROR - {suite_results['error']}")
            else:
                passed = suite_results['passed']
                total = suite_results['total']
                print(f"  {suite_name}: {passed}/{total} passed")
        
        if summary['failed_tests'] > 0:
            print("\nFailed Tests:")
            for suite_name, suite_results in results['test_suites'].items():
                if 'details' in suite_results:
                    for detail in suite_results['details']:
                        if detail['status'] == 'FAILED':
                            print(f"  {suite_name}.{detail['test']}: {detail['error']}")
        
        print("\n" + "="*80)
        
        # Return overall success
        return summary['failed_tests'] == 0


async def main():
    """Main test execution function."""
    runner = MDLDIValidationTestRunner()
    
    try:
        results = await runner.run_all_tests()
        success = runner.print_summary(results)
        
        # Store results in memory (placeholder for hive memory integration)
        await store_test_results_in_memory(results)
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        return 1


async def store_test_results_in_memory(results: Dict[str, Any]):
    """Store test results in hive memory for validation tracking."""
    # This would integrate with the hive memory system
    # For now, just log the results
    logger.info("Storing test results in hive memory...")
    
    memory_data = {
        'timestamp': time.time(),
        'test_type': 'mdl_di_validation',
        'results': results,
        'validation_status': 'passed' if results['summary']['failed_tests'] == 0 else 'failed',
        'key_metrics': {
            'total_tests': results['summary']['total_tests'],
            'success_rate': results['summary']['success_rate'],
            'execution_time': results['execution_time']
        }
    }
    
    # Store under hive memory key: hive/testing/mdl_validation
    logger.info(f"Test results stored: {memory_data['key_metrics']}")
    
    # TODO: Integrate with actual hive memory system
    # await hive_memory.store('hive/testing/mdl_validation', memory_data)


if __name__ == '__main__':
    import sys
    sys.exit(asyncio.run(main()))
