"""Test JSON truncation handling in DeepSeek client."""

import json
import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock

from src.infrastructure.external.deepseek_client import DeepSeekClient


class TestJSONTruncationHandling:
    """Test cases for JSON truncation handling."""

    def setup_method(self):
        """Set up test dependencies."""
        self.logger = MagicMock()
        self.config = MagicMock()
        self.client = DeepSeekClient(logger=self.logger, config=self.config)

    def test_parse_complete_json_with_markdown_wrapper(self):
        """Test parsing complete JSON wrapped in markdown code blocks."""
        content = '''```json
        {
            "versus": "<PERSON> et al. v. The 3M Company et al.",
            "plaintiffs_gpt": ["<PERSON>", "<PERSON>"]
        }
        ```'''
        
        result = self.client._parse_json_response(content, "TestService")
        
        assert result["versus"] == "Allen et al. v. The 3M Company et al."
        assert result["plaintiffs_gpt"] == ["<PERSON>", "<PERSON>"]

    def test_parse_truncated_json_with_markdown_wrapper(self):
        """Test parsing truncated JSON wrapped in markdown code blocks."""
        # This simulates the exact error from the logs
        content = '''```json
        {
          "versus": "<PERSON> et al. v. The 3M Company et al.",
          "plaintiffs_gpt": [
            "Sa<PERSON><PERSON> <PERSON> <PERSON>",
            "<PERSON> <PERSON><PERSON>",
            "<PERSON> <PERSON>",
            "<PERSON> <PERSON>",
            "<PERSON> <PERSON> <PERSON>",
            "<PERSON>su<PERSON> <PERSON> <PERSON><PERSON>",
            "<PERSON>",
            "<PERSON> <PERSON>",
            "<PERSON> <PERSON><PERSON>",
            "<PERSON> Bell",
            "<PERSON><PERSON>",
            "<PERSON> <PERSON>",
            "<PERSON> <PERSON><PERSON>nt",
            "<PERSON>nie Breeland",
            "Lanette Bridges",
            "Aaron Brooks",
            "Sandra R Brumfield",
            "Keith Burdett",
            "Lea Bueno Burgos",
            "Catella Castell",
            "Florencio Cepero",
            "Lisa Chillura",
            "Pony Cho",
            "William E Chrisman",
            "David Mortin Christianson",
            "Yvonne Christmas",
            "Jean Cipollini",
            "Steven Robert Citowitz",
            "Jacqueline Clendenin",
            "Russell R Cole",
            "Julie Collier",
            "Tammy Collier",
            "Ruben Cortez",
            "Mary Crawford",
            "James Crescenzi",
            "Mildred Crooks",
            "Nora Cruz",
            "Shamorah Lee Daniels",
            "Connie Davis",
            "Janice Dechalus",
            "Damon Demel'''  # Truncated mid-string
        
        result = self.client._parse_json_response(content, "TestService")
        
        # Should recover partial data
        assert result["versus"] == "Allen et al. v. The 3M Company et al."
        assert "plaintiffs_gpt" in result
        assert len(result["plaintiffs_gpt"]) >= 40  # Should extract most names
        assert "Sakia K Allen" in result["plaintiffs_gpt"]
        assert "Janice Dechalus" in result["plaintiffs_gpt"]

    def test_attempt_json_repair_with_unclosed_string(self):
        """Test JSON repair with unclosed string."""
        json_str = '{"versus": "Test Case", "plaintiffs_gpt": ["John Doe", "Jane'
        
        repaired = self.client._attempt_json_repair(json_str)
        
        # Should close the string and arrays/objects
        assert repaired is not None
        # Try to parse the repaired JSON
        try:
            result = json.loads(repaired)
            assert "versus" in result
            assert result["versus"] == "Test Case"
        except json.JSONDecodeError:
            # If repair didn't produce valid JSON, that's okay as long as we tried
            assert '"' in repaired  # Should have attempted to close the string

    def test_extract_partial_json_data(self):
        """Test extraction of partial data from truncated JSON."""
        json_str = '''
        {
          "versus": "Allen et al. v. The 3M Company et al.",
          "plaintiffs_gpt": [
            "Sakia K Allen",
            "Gary Allspach",
            "Alice Arnold"
        '''  # Truncated
        
        result = self.client._extract_partial_json_data(json_str)
        
        assert result is not None
        assert result["versus"] == "Allen et al. v. The 3M Company et al."
        assert "plaintiffs_gpt" in result
        assert "Sakia K Allen" in result["plaintiffs_gpt"]

    def test_aggressive_json_repair(self):
        """Test aggressive JSON repair for severely truncated responses."""
        json_str = '''
        {
          "versus": "Test v. Example",
          "confidence": 0.95,
          "is_valid": true,
          "notes": null,
          "plaintiffs_gpt": [
            "John Doe",
            "Jane
        '''  # Severely truncated
        
        result = self.client._aggressive_json_repair(json_str)
        
        assert result is not None
        assert result["versus"] == "Test v. Example"
        assert result["confidence"] == 0.95
        assert result["is_valid"] is True
        assert result["notes"] is None
        # May or may not recover plaintiffs_gpt depending on truncation point

    def test_parse_json_without_markdown_wrapper(self):
        """Test parsing JSON without markdown wrapper."""
        content = '{"versus": "Test Case", "plaintiffs_gpt": ["John Doe"]}'
        
        result = self.client._parse_json_response(content, "TestService")
        
        assert result["versus"] == "Test Case"
        assert result["plaintiffs_gpt"] == ["John Doe"]

    def test_parse_empty_json(self):
        """Test parsing empty JSON object."""
        content = '{}'
        
        result = self.client._parse_json_response(content, "TestService")
        
        assert result == {}

    def test_parse_invalid_json_raises_error(self):
        """Test that completely invalid JSON raises an error."""
        content = 'This is not JSON at all'
        
        with pytest.raises(Exception) as exc_info:
            self.client._parse_json_response(content, "TestService")
        
        assert "Failed to parse JSON" in str(exc_info.value)


if __name__ == "__main__":
    # Run tests
    test = TestJSONTruncationHandling()
    test.setup_method()
    
    print("Testing complete JSON with markdown wrapper...")
    test.test_parse_complete_json_with_markdown_wrapper()
    print("✓ Passed")
    
    print("Testing truncated JSON with markdown wrapper...")
    test.test_parse_truncated_json_with_markdown_wrapper()
    print("✓ Passed")
    
    print("Testing JSON repair with unclosed string...")
    test.test_attempt_json_repair_with_unclosed_string()
    print("✓ Passed")
    
    print("Testing partial data extraction...")
    test.test_extract_partial_json_data()
    print("✓ Passed")
    
    print("Testing aggressive JSON repair...")
    test.test_aggressive_json_repair()
    print("✓ Passed")
    
    print("Testing JSON without markdown wrapper...")
    test.test_parse_json_without_markdown_wrapper()
    print("✓ Passed")
    
    print("Testing empty JSON...")
    test.test_parse_empty_json()
    print("✓ Passed")
    
    print("\nAll tests passed! ✅")