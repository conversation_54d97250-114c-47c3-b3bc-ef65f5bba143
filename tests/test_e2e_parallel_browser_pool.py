"""
End-to-End Test: Parallel Processing with Browser Pool

This test verifies the complete integration from configuration to execution,
ensuring that parallel processing uses the browser pool correctly.
"""

import asyncio
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from datetime import date

from src.pacer.facades.docket_orchestrator import DocketOrchestrator
from src.pacer.jobs.job_orchestration_service import PacerJobOrchestrationService
from src.pacer.jobs.job_runner_service import Pace<PERSON><PERSON><PERSON><PERSON>unnerService
from src.pacer.components.browser.browser_pool import Browser<PERSON>ool<PERSON>anager
from src.pacer.jobs.jobs_models import PacerJob


@pytest.fixture
def integration_config():
    """Configuration that matches scrape_pacer.yml settings."""
    return {
        'run_parallel': True,
        'max_parallel_courts': 3,
        'use_browser_pool': True,
        'browser_pool': {
            'min_size': 2,
            'max_size': 5,
            'max_contexts_per_browser': 3,
            'browser_ttl_minutes': 30,
            'browser_idle_minutes': 10
        },
        'process_single_court': ['ilnd', 'flnd', 'paed'],
        'headless': True,
        'timeout_ms': 60000,
        'data_path': str(tempfile.gettempdir())
    }


@pytest.fixture
def mock_logger():
    """Mock logger."""
    logger = Mock()
    logger.info = Mock()
    logger.warning = Mock() 
    logger.error = Mock()
    return logger


@pytest.fixture
async def browser_pool_manager(integration_config, mock_logger):
    """Create a real browser pool manager for integration testing."""
    pool = BrowserPoolManager(logger=mock_logger, config=integration_config)
    
    # Mock the low-level browser operations for testing
    pool.acquire_context = AsyncMock()
    pool.release_context = AsyncMock()
    pool.initialize = AsyncMock()
    pool.shutdown = AsyncMock()
    
    # Mock context acquisition to return a mock context
    async def mock_acquire_context(job_id, download_path):
        mock_context = Mock()
        mock_context.close = AsyncMock()
        return mock_context
    
    pool.acquire_context.side_effect = mock_acquire_context
    
    return pool


@pytest.fixture
def mock_pacer_orchestrator():
    """Mock PACER orchestrator for job processing."""
    orchestrator = Mock()
    
    async def mock_process_single_court_job(job, context):
        # Simulate successful processing
        return {
            'status': 'success',
            'legacy_final_data': [{'court': job.court_id, 'cases': 5}],
            'metrics': {'cases_processed': 5, 'duration': 45.2}
        }
    
    orchestrator.process_single_court_job = AsyncMock(side_effect=mock_process_single_court_job)
    return orchestrator


@pytest.fixture 
def mock_dependencies():
    """Mock all the required dependencies."""
    return {
        'court_processor': Mock(),
        'docket_processor': Mock(),
        'row_processor': Mock(),
        'download_manager': Mock(),
        'file_operations_service': Mock(),
        'navigation_facade': Mock(),
        'report_facade': Mock(),
        'state_validator': Mock(),
        'sequential_workflow_manager': Mock(),
        'return_and_continue_manager': Mock(),
        'court_logger_factory': Mock(),
        'pacer_repository': Mock(),
        'async_dynamodb_storage': Mock(),
    }


class TestE2EParallelBrowserPool:
    """End-to-end test for parallel processing with browser pool."""
    
    @pytest.mark.asyncio
    async def test_complete_parallel_processing_pipeline_with_browser_pool(
        self,
        integration_config,
        mock_logger,
        browser_pool_manager,
        mock_pacer_orchestrator,
        mock_dependencies
    ):
        """Test the complete pipeline from docket orchestrator to browser pool usage."""
        
        # Create job runner service with browser pool
        job_runner_service = PacerJobRunnerService(
            config=integration_config,
            logger=mock_logger,
            pacer_orchestrator=mock_pacer_orchestrator,
            browser_pool_manager=browser_pool_manager
        )
        
        # Create job orchestration service
        job_orchestration_service = PacerJobOrchestrationService(
            config=integration_config,
            job_runner_service=job_runner_service,
            logger=mock_logger
        )
        
        # Create docket orchestrator with job orchestration service
        docket_orchestrator = DocketOrchestrator(
            **mock_dependencies,
            logger=mock_logger,
            config=integration_config,
            job_orchestration_service=job_orchestration_service
        )
        
        # Execute the complete pipeline
        court_ids = integration_config['process_single_court']
        result = await docket_orchestrator.process_courts(
            court_ids=court_ids,
            iso_date='20250819',
            start_date='08/16/25',
            end_date='08/19/25',
            run_parallel=True
        )
        
        # Verify parallel processing was used
        assert result['workflow_status'] == 'completed_parallel'
        assert result['total_courts'] == len(court_ids)
        assert 'parallel_job_metrics' in result
        
        # Verify browser pool was used
        browser_pool_metrics = result['parallel_job_metrics']
        assert browser_pool_metrics['processing_mode'] == 'parallel'
        assert browser_pool_metrics['browser_pool_used'] is True
        
        # Verify browser pool methods were called
        # Note: initialize/shutdown are called by job orchestration service, not docket orchestrator
        assert browser_pool_manager.acquire_context.call_count == len(court_ids)
        assert browser_pool_manager.release_context.call_count == len(court_ids)
        
        # Verify all courts were processed successfully
        assert browser_pool_metrics['successful_jobs'] == len(court_ids)
        assert browser_pool_metrics['failed_jobs'] == 0

    @pytest.mark.asyncio
    async def test_browser_pool_context_isolation_per_court(
        self,
        integration_config,
        mock_logger,
        browser_pool_manager,
        mock_pacer_orchestrator,
        mock_dependencies
    ):
        """Test that each court gets its own isolated browser context."""
        
        # Track contexts acquired and download paths
        acquired_contexts = []
        download_paths = []
        
        async def track_acquire_context(job_id, download_path):
            acquired_contexts.append(job_id)
            download_paths.append(download_path)
            mock_context = Mock()
            mock_context.close = AsyncMock()
            return mock_context
        
        browser_pool_manager.acquire_context.side_effect = track_acquire_context
        
        # Create the complete pipeline
        job_runner_service = PacerJobRunnerService(
            config=integration_config,
            logger=mock_logger,
            pacer_orchestrator=mock_pacer_orchestrator,
            browser_pool_manager=browser_pool_manager
        )
        
        job_orchestration_service = PacerJobOrchestrationService(
            config=integration_config,
            job_runner_service=job_runner_service,
            logger=mock_logger
        )
        
        docket_orchestrator = DocketOrchestrator(
            **mock_dependencies,
            logger=mock_logger,
            config=integration_config,
            job_orchestration_service=job_orchestration_service
        )
        
        # Process courts
        court_ids = ['ilnd', 'flnd', 'paed']
        await docket_orchestrator.process_courts(
            court_ids=court_ids,
            iso_date='20250819',
            run_parallel=True
        )
        
        # Verify each court got its own context
        assert len(acquired_contexts) == len(court_ids)
        assert len(set(acquired_contexts)) == len(court_ids)  # All job IDs are unique
        
        # Verify download paths are court-specific and isolated
        assert len(download_paths) == len(court_ids)
        assert len(set(download_paths)) == len(court_ids)  # All paths are unique
        
        # Verify download path pattern includes court ID
        for i, court_id in enumerate(court_ids):
            assert f"{court_id}_ctx_dl_report" in download_paths[i]

    @pytest.mark.asyncio
    async def test_performance_monitoring_with_browser_pool(
        self,
        integration_config,
        mock_logger,
        browser_pool_manager,
        mock_pacer_orchestrator,
        mock_dependencies
    ):
        """Test that performance metrics are correctly tracked with browser pool."""
        
        # Add timing simulation to browser pool
        import time
        
        async def timed_acquire_context(job_id, download_path):
            await asyncio.sleep(0.1)  # Simulate context acquisition time
            mock_context = Mock()
            mock_context.close = AsyncMock()
            return mock_context
        
        async def timed_release_context(context, job_id):
            await asyncio.sleep(0.05)  # Simulate context release time
        
        browser_pool_manager.acquire_context.side_effect = timed_acquire_context
        browser_pool_manager.release_context.side_effect = timed_release_context
        
        # Create pipeline
        job_runner_service = PacerJobRunnerService(
            config=integration_config,
            logger=mock_logger,
            pacer_orchestrator=mock_pacer_orchestrator,
            browser_pool_manager=browser_pool_manager
        )
        
        job_orchestration_service = PacerJobOrchestrationService(
            config=integration_config,
            job_runner_service=job_runner_service,
            logger=mock_logger
        )
        
        docket_orchestrator = DocketOrchestrator(
            **mock_dependencies,
            logger=mock_logger,
            config=integration_config,
            job_orchestration_service=job_orchestration_service
        )
        
        # Process with timing
        start_time = time.time()
        result = await docket_orchestrator.process_courts(
            court_ids=['ilnd', 'flnd'],
            iso_date='20250819',
            run_parallel=True
        )
        end_time = time.time()
        
        # Verify performance metrics exist
        assert result['workflow_status'] == 'completed_parallel'
        
        # Check that parallel processing was faster than sequential would be
        # With 2 courts and 0.1s+ per court, parallel should be < 0.5s total
        total_time = end_time - start_time
        assert total_time < 1.0, f"Parallel processing took {total_time:.2f}s, should be < 1.0s"
        
        # Verify browser pool usage is tracked
        metrics = result['parallel_job_metrics']
        assert metrics['browser_pool_used'] is True
        assert metrics['processing_mode'] == 'parallel'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])