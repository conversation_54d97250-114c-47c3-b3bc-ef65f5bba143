#!/usr/bin/env python3
"""
Comprehensive Test Runner for Refactoring Protection Tests

This script runs all pre-refactoring protection tests to ensure the
architectural refactoring doesn't break existing functionality.

Usage:
    python tests/run_refactoring_protection_tests.py [--verbose] [--parallel] [--coverage]
"""
import subprocess
import sys
import argparse
from pathlib import Path


def run_test_category(test_path: str, description: str, verbose: bool = False) -> bool:
    """Run a category of tests and return success status."""
    print(f"\n{'='*60}")
    print(f"Running {description}")
    print(f"{'='*60}")
    
    cmd = ["python", "-m", "pytest", test_path, "-v" if verbose else "-q"]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            if verbose:
                print(result.stdout)
        else:
            print(f"❌ {description} - FAILED")
            print(result.stdout)
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"💥 {description} - ERROR: {e}")
        return False


def run_refactoring_protection_tests(verbose: bool = False, parallel: bool = False, coverage: bool = False):
    """Run all refactoring protection tests."""
    
    print("🔬 LexGenius Refactoring Protection Test Suite")
    print("=" * 80)
    print("Purpose: Ensure architectural refactoring preserves all functionality")
    print("Target: 131 → 95 services, 6-level → 2-3 level orchestrator hierarchy")
    print("=" * 80)
    
    test_categories = [
        # Priority 1: Critical Protection Tests
        ("tests/contracts/", "🏛️  Service Interface Contract Tests"),
        ("tests/critical_paths/", "🚨 Critical Path Validation Tests"),  
        ("tests/configuration/", "⚙️  Configuration System Tests"),
        
        # Priority 2: Integration and Business Logic
        ("tests/integration/test_orchestrator_hierarchy_workflow.py", "🔄 Orchestrator Integration Tests"),
        # ("tests/business_logic/", "💼 Business Logic Extraction Tests"),  # To be created
        # ("tests/service_consolidation/", "🔧 Service Consolidation Tests"),  # To be created
        
        # Priority 3: End-to-End and Performance  
        # ("tests/e2e/", "🎯 End-to-End Workflow Tests"),  # To be created
        # ("tests/performance/baselines/", "📊 Performance Baseline Tests"),  # To be created
        
        # Regression Detection
        # ("tests/regression/", "🔍 Regression Detection Tests"),  # To be created
    ]
    
    results = {}
    total_tests = len(test_categories)
    passed_tests = 0
    
    for test_path, description in test_categories:
        if Path(test_path).exists():
            success = run_test_category(test_path, description, verbose)
            results[description] = success
            if success:
                passed_tests += 1
        else:
            print(f"⚠️  {description} - PATH NOT FOUND: {test_path}")
            results[description] = False
    
    # Summary Report
    print(f"\n{'='*80}")
    print("📋 REFACTORING PROTECTION TEST SUMMARY")
    print(f"{'='*80}")
    
    for description, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {description}")
    
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n📊 Overall Results: {passed_tests}/{total_tests} test categories passed ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT: Ready for refactoring with high confidence")
        exit_code = 0
    elif success_rate >= 75:
        print("⚠️  GOOD: Most protection tests passing, review failures before refactoring")
        exit_code = 1
    elif success_rate >= 50:
        print("🚧 CAUTION: Significant test failures, address before refactoring")
        exit_code = 2
    else:
        print("🛑 DANGER: Major test failures, DO NOT proceed with refactoring")
        exit_code = 3
    
    print(f"\n{'='*80}")
    print("🔧 NEXT STEPS FOR REFACTORING:")
    print("1. Address any failing protection tests")
    print("2. Review critical path validations")
    print("3. Ensure service contract compatibility")
    print("4. Validate configuration consolidation readiness")
    print("5. Proceed with phased refactoring implementation")
    print(f"{'='*80}")
    
    return exit_code


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(
        description="Run refactoring protection tests",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument(
        "-v", "--verbose", 
        action="store_true",
        help="Enable verbose test output"
    )
    parser.add_argument(
        "-p", "--parallel",
        action="store_true", 
        help="Run tests in parallel (faster but less detailed output)"
    )
    parser.add_argument(
        "-c", "--coverage",
        action="store_true",
        help="Generate coverage reports"
    )
    
    args = parser.parse_args()
    
    exit_code = run_refactoring_protection_tests(
        verbose=args.verbose,
        parallel=args.parallel,
        coverage=args.coverage
    )
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()