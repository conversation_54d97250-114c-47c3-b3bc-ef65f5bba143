"""
Test Download Path Generation Pattern

Verify that download paths follow the exact pattern from parallel-processing-fix.md:
data/{iso_date}/dockets/temp/{court_id}_ctx_dl_{report_type}/attempt_{attempt_number}_{uuid}
"""

import re
from datetime import date
from pathlib import Path

from src.pacer.jobs.job_runner_service import PacerJobRunnerService
from src.pacer.jobs.jobs_models import PacerJob
from src.infrastructure.patterns.component_base import AsyncServiceBase


class TestDownloadPathPattern:
    """Test download path generation patterns."""
    
    def test_download_path_pattern_compliance(self):
        """Test that download paths match the specified pattern exactly."""
        
        # Mock configuration
        config = {
            'data_path': './data',
            'use_browser_pool': False
        }
        
        # Create mock logger that supports the expected interface
        import logging
        logger = logging.getLogger(__name__)
        
        # Create job runner service
        job_runner = PacerJobRunnerService(
            config=config,
            logger=logger,
            pacer_orchestrator=None,
            browser_pool_manager=None
        )
        
        # Test case 1: Regular report processing (cand court)
        job1 = PacerJob(
            court_id='cand',
            iso_date='20250615', 
            start_date=date(2025, 6, 10),
            end_date=date(2025, 6, 15),
            config_snapshot=config
        )
        
        path1 = job_runner._generate_unique_download_path(job1)
        self._verify_download_path_pattern(path1, 'cand', '20250615', 'report')
        
        # Test case 2: Docket list processing (ilnd court)
        job2 = PacerJob(
            court_id='ilnd',
            iso_date='20250615',
            start_date=date(2025, 6, 10), 
            end_date=date(2025, 6, 15),
            config_snapshot=config,
            docket_list_input=[{'docket': '1:25-cv-00001'}]
        )
        
        path2 = job_runner._generate_unique_download_path(job2)
        self._verify_download_path_pattern(path2, 'ilnd', '20250615', 'docket_log')
        
        # Test case 3: Different court and date
        job3 = PacerJob(
            court_id='nysd',
            iso_date='20250101',
            start_date=date(2025, 1, 1),
            end_date=date(2025, 1, 5), 
            config_snapshot=config
        )
        
        path3 = job_runner._generate_unique_download_path(job3)
        self._verify_download_path_pattern(path3, 'nysd', '20250101', 'report')
        
        # Verify paths are unique
        paths = [str(path1), str(path2), str(path3)]
        assert len(set(paths)) == 3, "All download paths should be unique"
        
        print("✅ All download path patterns verified successfully!")
        
    def _verify_download_path_pattern(self, path: Path, court_id: str, iso_date: str, report_type: str):
        """
        Verify a download path matches the expected pattern.
        
        Expected pattern: 
        data/{iso_date}/dockets/temp/{court_id}_ctx_dl_{report_type}/attempt_{attempt_number}_{uuid}
        """
        path_str = str(path)
        
        # Define the expected regex pattern
        pattern = rf".*data/{iso_date}/dockets/temp/{court_id}_ctx_dl_{report_type}/attempt_\d+_[a-f0-9]+$"
        
        if not re.match(pattern, path_str):
            raise AssertionError(f"Path does not match expected pattern:\nPath: {path_str}\nPattern: {pattern}")
        
        # Additional specific checks
        path_parts = Path(path_str).parts
        
        # Check that it contains the expected segments
        assert iso_date in path_parts, f"ISO date {iso_date} not found in path: {path_str}"
        assert 'dockets' in path_parts, f"'dockets' not found in path: {path_str}"
        assert 'temp' in path_parts, f"'temp' not found in path: {path_str}"
        
        # Check court-specific directory
        court_dir = path_parts[-2]
        expected_court_dir = f"{court_id}_ctx_dl_{report_type}"
        assert court_dir == expected_court_dir, f"Court dir '{court_dir}' != expected '{expected_court_dir}'"
        
        # Check attempt directory
        attempt_dir = path_parts[-1]
        attempt_pattern = r"^attempt_\d+_[a-f0-9]+$"
        assert re.match(attempt_pattern, attempt_dir), f"Attempt dir '{attempt_dir}' doesn't match pattern '{attempt_pattern}'"
        
        print(f"✅ Path verified: {court_id} -> {path_str}")


if __name__ == "__main__":
    test = TestDownloadPathPattern()
    test.test_download_path_pattern_compliance()