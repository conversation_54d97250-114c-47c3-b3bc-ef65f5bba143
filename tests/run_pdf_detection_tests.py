"""
Test runner script for PDF detection and s3_link assignment test suite.
Runs comprehensive tests and generates detailed reports.
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))

def run_test_suite():
    """Run the complete PDF detection test suite."""
    
    test_files = [
        'tests/unit/services/transformer/test_pdf_detection_service.py',
        'tests/unit/services/transformer/test_s3_link_integration.py',
        'tests/unit/services/transformer/test_di_container_integration.py',
        'tests/unit/services/transformer/test_edge_cases_pdf_detection.py'
    ]
    
    print("🧪 Starting PDF Detection Test Suite")
    print("=" * 60)
    
    start_time = time.time()
    
    # Change to project root directory
    os.chdir(project_root)
    
    # Run tests with detailed output
    cmd = [
        'python', '-m', 'pytest',
        '-v',  # Verbose output
        '--tb=short',  # Short traceback format
        '--durations=10',  # Show 10 slowest tests
        '--cov=src/services/transformer',  # Coverage for transformer services
        '--cov-report=term-missing',  # Show missing lines
        '--cov-report=html:tests/coverage/html',  # HTML coverage report
        '--junit-xml=tests/results/junit.xml',  # JUnit XML for CI
        '--html=tests/results/report.html',  # HTML test report
        '--self-contained-html',  # Self-contained HTML report
        *test_files
    ]
    
    # Create results directories
    os.makedirs('tests/results', exist_ok=True)
    os.makedirs('tests/coverage', exist_ok=True)
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        # Print output
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
        
        # Calculate duration
        duration = time.time() - start_time
        
        print("-" * 60)
        print(f"Test suite completed in {duration:.2f} seconds")
        print(f"Exit code: {result.returncode}")
        
        # Generate summary
        if result.returncode == 0:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed!")
        
        # Show coverage summary if available
        try:
            with open('tests/coverage/.coverage', 'r') as f:
                print("\n📊 Coverage report generated at tests/coverage/html/index.html")
        except FileNotFoundError:
            pass
        
        # Show test report location
        print(f"📋 Detailed test report: tests/results/report.html")
        print(f"📊 Coverage report: tests/coverage/html/index.html")
        
        return result.returncode
        
    except subprocess.TimeoutExpired:
        print("❌ Test suite timed out after 5 minutes")
        return 1
    except Exception as e:
        print(f"❌ Error running test suite: {e}")
        return 1


def run_specific_test_categories():
    """Run specific test categories with targeted focus."""
    
    categories = {
        'unit_tests': 'tests/unit/services/transformer/test_pdf_detection_service.py',
        'integration_tests': 'tests/unit/services/transformer/test_s3_link_integration.py',
        'di_tests': 'tests/unit/services/transformer/test_di_container_integration.py',
        'edge_case_tests': 'tests/unit/services/transformer/test_edge_cases_pdf_detection.py'
    }
    
    print("📂 Running Test Categories Separately")
    print("=" * 60)
    
    results = {}
    
    for category, test_file in categories.items():
        print(f"\n🔍 Running {category}...")
        
        cmd = [
            'python', '-m', 'pytest',
            '-v',
            '--tb=line',
            test_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            results[category] = result.returncode
            
            if result.returncode == 0:
                print(f"✅ {category}: PASSED")
            else:
                print(f"❌ {category}: FAILED")
                if result.stdout:
                    print(f"Output: {result.stdout[-500:]}")  # Last 500 chars
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ {category}: TIMEOUT")
            results[category] = 1
        except Exception as e:
            print(f"💥 {category}: ERROR - {e}")
            results[category] = 1
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Category Results Summary:")
    
    for category, exit_code in results.items():
        status = "PASSED" if exit_code == 0 else "FAILED"
        emoji = "✅" if exit_code == 0 else "❌"
        print(f"{emoji} {category}: {status}")
    
    total_passed = sum(1 for code in results.values() if code == 0)
    total_categories = len(results)
    
    print(f"\n🎯 Overall: {total_passed}/{total_categories} categories passed")
    
    return 0 if total_passed == total_categories else 1


def check_test_environment():
    """Check if test environment is properly set up."""
    
    print("🔧 Checking Test Environment")
    print("-" * 30)
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8+ required")
        return False
    
    # Check required packages
    required_packages = [
        'pytest',
        'pytest-cov',
        'pytest-html',
        'pytest-asyncio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 Install missing packages:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    # Check project structure
    required_paths = [
        'src/services/transformer',
        'tests/unit/services/transformer',
        'tests/mocks'
    ]
    
    for path in required_paths:
        full_path = project_root / path
        if full_path.exists():
            print(f"✅ {path}")
        else:
            print(f"❌ {path} (missing)")
            return False
    
    print("✅ Test environment ready!")
    return True


def main():
    """Main test runner function."""
    
    print("🚀 PDF Detection Test Suite Runner")
    print("=" * 60)
    
    # Check environment first
    if not check_test_environment():
        print("\n❌ Environment check failed. Please fix issues and try again.")
        return 1
    
    print("\n")
    
    # Get command line arguments
    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        mode = 'full'
    
    if mode == 'categories':
        return run_specific_test_categories()
    elif mode == 'full':
        return run_test_suite()
    else:
        print("Usage: python tests/run_pdf_detection_tests.py [full|categories]")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)