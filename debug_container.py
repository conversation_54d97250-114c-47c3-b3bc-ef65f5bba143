#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tests.conftest import TestApplicationContainer
import asyncio
from unittest.mock import Mock, AsyncMock

def test_container_setup():
    """Test the container setup manually."""
    container = TestApplicationContainer()
    
    # Setup configuration
    mock_config_data = {
        'config_name': 'test_config',
        'iso_date': '20240101',
        'DATA_DIR': '/tmp/test',
    }
    container.workflow_specific_config.from_dict(mock_config_data)
    
    print(f'Container type: {type(container)}')
    print(f'Container pacer type: {type(container.pacer)}')
    
    # Check available providers
    print(f'Container pacer providers: {list(container.pacer.providers.keys())}')
    
    # Test access
    try:
        analytics = container.pacer.analytics_service()
        print(f'Analytics service type: {type(analytics)}')
        print('✓ PACER container working correctly!')
        
        # Test fixture-style creation
        print('\n--- Testing fixture-style creation ---')
        container2 = TestApplicationContainer()
        container2.workflow_specific_config.from_dict(mock_config_data)
        analytics2 = container2.pacer.analytics_service()
        print(f'Analytics service 2 type: {type(analytics2)}')
        print('✓ Fixture-style creation also working!')
        
        return True
    except Exception as e:
        print(f'✗ Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_container_setup()
    sys.exit(0 if success else 1)