#!/usr/bin/env python3
"""
Categorize Facebook Ad Summaries based on content rules
"""
import json
import re
from pathlib import Path
from typing import Dict, Any


def load_summary_frequencies() -> Dict[str, int]:
    """Load the fb_summaries_export.json file to get summary frequencies"""
    project_root = Path(__file__).parent
    summaries_file = project_root / "fb_summaries_export.json"

    if not summaries_file.exists():
        raise FileNotFoundError("fb_summaries_export.json not found")

    with open(summaries_file, 'r', encoding='utf-8') as f:
        summaries_data = json.load(f)

    # Convert to dict: {summary: count}
    frequency_dict = {}
    for item in summaries_data:
        summary = item.get('summary')
        count = item.get('count', 0)
        if summary:
            frequency_dict[summary] = count

    return frequency_dict


def categorize_summary(summary: str) -> str:
    """Categorize a summary based on content rules"""
    if not summary or summary in ['NA', 'SKIPPED', None]:
        return 'uncategorized'

    # Convert to lowercase for case-insensitive matching
    summary_lower = summary.lower()

    # Define category rules (order matters - first match wins)
    category_rules = [
        ('Video Game Addiction', ['video game addiction', 'gaming addiction', 'fortnite mental health',
                                  'fornite addiction', 'roblox video game addiction', 'nba 2k addiction']),
        ('PFAS Water Contamination', ['water contamination']),
        ('Nexium Prilosec Cancer Investigation',['Nexium Prilosec Cancer Investigation']),
        ('Toxic Baby Food', ['toxic baby food', 'beech-nut baby food', 'baby food autism']),
        ('Bard PowerPort', ['bard powerport']),
        ('Overdraft Fees', ['overdraft fee']),
        ('Juvenile Detention Abuse',
         ['juvenile detention', 'child abuse', 'juvenile abuse', 'juvenile hall abuse']),
        ('Deceptive Advertising', ['false advertising', 'mislabeling', 'deceptive pricing']),
        ('Ethylene Oxide Toxic Air', ['ethylene oxide', 'sterilization', 'toxic air']),
        ('Pressure Cooker Products Liability', ['pressure cooker']),
        ('Data Privacy Investigation',
         ['privacy investigation', 'data privacy', 'data misuse', 'privacy violation', 'privacy law']),
        ('Data Breach', ['data breach']),
        ('Labor Law', ['laborlaw']),
        ('Junk Fees', ['shipping protection fee']),
        ('Depo Provera Products Liability', ['depo-provera', 'depo provera']),
        ('Galaxy Gas Products Liability', ['nitros oxide', 'nitrous oxide']),
        # Note: assuming "nitros" was a typo for "nitrous"
        ('Junk Fees Litigation', ['hidden fees', 'junk fees', 'deceptive fee', 'deceptive fees','junk fee', 'convenience fee']),
        ('Oxbryta Products Liability', ['oxbryta']),
        ('NEC Baby Formula Products Liability', ['baby formula nec', 'enfamil NEC', 'NEC formula', 'nec baby formula']),
        ('California Wildfire', ['wildfire']),
        ('CAN-SPAM', ['email']),
        ('Antitrust', ['antitrust']),
        ('AFFF Products Liability', ['afff', 'firefoam', 'firefighters toxic foam']),
        ('Roundup Products Liability', ['roundup']),
        ('Securities Litigation', ['securities', 'shareholder', 'stock loss']),
        ('Hair Relaxer Products Liability', ['hair relaxer', 'hair relaxers']),
        ('Uber Sexual Assault', ['uber sexual assault', 'uber lyft harm']),
        ('ERISA', ['retirement plan', 'pension plan']),
        ('PFAS Exposure', ['pfas', 'pfas cancer']),
        ('Talcum Powder Products Liability', ['baby powder', 'talcum powder']),
        ('Paragard IUD Products Liability', ['paragard']),
        ('Suboxone Products Liability', ['suboxone']),
        ('TCPA', ['tcpa']),
        ('Ultra Processed Foods', ['ultra processed foods','ultra-processed foods', 'junk food']),
        ('Hernia Mesh', ['hernia mesh']),
        ('LDS Sex Abuse', ['lds']),
        ('Spinal Cord Stimulator Products Liability', ['spinal cord stimulator', 'spinal stimulator']),
        ('Silicosis Product Liability', ['silicosis']),

    ]

    # Check each category rule with word boundaries
    for category, phrases in category_rules:
        for phrase in phrases:
            # Use word boundaries for exact phrase matching
            pattern = r'\b' + re.escape(phrase) + r'\b'
            if re.search(pattern, summary_lower):
                return category

    return 'uncategorized'


def create_categorized_mapping(summary_frequencies: Dict[str, int]) -> Dict[str, Any]:
    """Create categorized mapping of summaries with frequencies"""
    categorized = {
        'categories': {},
        'summary_count': len(summary_frequencies),
        'categorization_stats': {}
    }

    total_ads = sum(summary_frequencies.values())

    # Process each summary with its frequency
    for summary, frequency in summary_frequencies.items():
        category = categorize_summary(summary)

        if category not in categorized['categories']:
            categorized['categories'][category] = []

        # Include both summary and frequency
        categorized['categories'][category].append({
            'summary': summary,
            'frequency': frequency
        })

    # Sort summaries within each category by frequency (descending)
    for category in categorized['categories']:
        categorized['categories'][category].sort(key=lambda x: x['frequency'],
                                                 reverse=True)

    # Calculate stats
    for category, summary_list in categorized['categories'].items():
        total_frequency = sum(item['frequency'] for item in summary_list)
        categorized['categorization_stats'][category] = {
            'summary_count': len(summary_list),
            'total_ads': total_frequency,
            'percentage_of_summaries': round(
                (len(summary_list) / len(summary_frequencies)) * 100, 2),
            'percentage_of_ads': round((total_frequency / total_ads) * 100, 2)
        }

    # Sort categories by total ads (descending)
    sorted_stats = dict(sorted(
        categorized['categorization_stats'].items(),
        key=lambda x: x[1]['total_ads'],
        reverse=True
    ))
    categorized['categorization_stats'] = sorted_stats

    return categorized


def save_categorized_results(categorized_data: Dict[str, Any]):
    """Save categorized results to JSON file"""
    project_root = Path(__file__).parent
    output_file = project_root / "fb_summary_frequencies_categorized.json"

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(categorized_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Categorized results saved to: {output_file}")
    return output_file


def print_categorization_summary(categorized_data: Dict[str, Any]):
    """Print a summary of the categorization results"""
    total_ads = sum(stats['total_ads'] for stats in
                    categorized_data['categorization_stats'].values())

    print(f"\n📊 Categorization Summary:")
    print(f"  🎯 Total summaries processed: {categorized_data['summary_count']}")
    print(f"  🎯 Total ads processed: {total_ads:,}")
    print(f"  📂 Categories found: {len(categorized_data['categories'])}")

    print(f"\n📈 Category Breakdown (by total ads):")
    for category, stats in categorized_data['categorization_stats'].items():
        summary_count = stats['summary_count']
        total_ads_cat = stats['total_ads']
        percentage_ads = stats['percentage_of_ads']
        percentage_summaries = stats['percentage_of_summaries']
        print(f"  • {category.replace('_', ' ').title()}:")
        print(f"    📋 {summary_count} summaries ({percentage_summaries}%)")
        print(f"    📊 {total_ads_cat:,} ads ({percentage_ads}%)")

    # Show some examples from top categories
    print(f"\n🔍 Category Examples (Top 3 by frequency):")
    for category, summary_list in list(categorized_data['categories'].items())[:5]:
        print(f"\n  📋 {category.replace('_', ' ').title()}:")
        for item in summary_list[:3]:  # Show first 3 examples
            print(f"    • {item['summary']} ({item['frequency']} ads)")
        if len(summary_list) > 3:
            print(f"    ... and {len(summary_list) - 3} more")


def main():
    """Main execution function"""
    try:
        # Load summary frequencies
        print("🔍 Loading fb_summaries_export.json...")
        summary_frequencies = load_summary_frequencies()
        total_ads = sum(summary_frequencies.values())
        print(
            f"  ✅ Loaded {len(summary_frequencies)} summaries with {total_ads:,} total ads")

        # Categorize summaries
        print("\n🏷️  Categorizing summaries...")
        categorized_data = create_categorized_mapping(summary_frequencies)

        # Save results
        print("\n💾 Saving categorized results...")
        output_file = save_categorized_results(categorized_data)

        # Print summary
        print_categorization_summary(categorized_data)

        print(f"\n🎉 Categorization complete!")
        print(f"   📁 Output file: {output_file}")

    except Exception as e:
        print(f"❌ Error during categorization: {e}")


if __name__ == "__main__":
    main()
