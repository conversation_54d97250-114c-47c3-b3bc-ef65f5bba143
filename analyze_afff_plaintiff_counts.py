#!/usr/bin/env python3
"""
Analyze AFFF (MDL 2873) plaintiff counts for the past 30 days.

This script queries DynamoDB to understand how plaintiff counts are calculated
and identifies discrepancies in the Chart.js values.
"""

import asyncio
import os
import sys
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Tuple

from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage

# Load environment variables
load_dotenv()

console = Console()


class AfffAnalyzer:
    """Analyze AFFF plaintiff counts and transfers."""
    
    def __init__(self):
        self.storage = None
        self.repository = None
        self.mdl_num = '2873'  # AFFF MDL number
        
    async def initialize(self):
        """Initialize storage and repository."""
        # Create storage configuration
        class StorageConfig:
            def __init__(self):
                self.use_local = False
                self.local_port = 8000
                self.dynamodb_endpoint = None
                self.aws_region = 'us-west-2'
                self.dynamodb_max_retries = 10
                self.dynamodb_base_delay = 1.0
                self.dynamodb_max_delay = 60.0
        
        config = StorageConfig()
        self.storage = AsyncDynamoDBStorage(config)
        self.repository = PacerRepository(self.storage)
        
    async def get_afff_filings_30_days(self, end_date_str: str) -> List[Dict[str, Any]]:
        """Get all AFFF filings for the past 30 days."""
        console.print(f"[cyan]Fetching AFFF filings for 30 days ending {end_date_str}...[/cyan]")
        
        end_date = datetime.strptime(end_date_str, '%Y%m%d')
        start_date = end_date - timedelta(days=29)  # 30 days including end date
        
        all_filings = []
        current_date = end_date
        
        # Progress indicator
        with console.status("[bold green]Querying DynamoDB...") as status:
            while current_date >= start_date:
                date_str = current_date.strftime('%Y%m%d')
                status.update(f"[bold green]Querying {date_str}...")
                
                # Query by MDL and date
                daily_filings = await self.repository.query_by_mdl_and_date_range(
                    self.mdl_num, date_str, date_str
                )
                
                all_filings.extend(daily_filings)
                current_date -= timedelta(days=1)
        
        console.print(f"[green]Found {len(all_filings)} total AFFF filings[/green]")
        return all_filings
    
    def calculate_plaintiff_count(self, filing: Dict[str, Any]) -> int:
        """Calculate plaintiff count for a filing, defaulting to 1."""
        num_plaintiffs = filing.get('num_plaintiffs')
        if num_plaintiffs is None or str(num_plaintiffs) == '0':
            return 1
        try:
            return int(num_plaintiffs)
        except (ValueError, TypeError):
            return 1
    
    def display_filings_table(self, filings: List[Dict[str, Any]], title: str, show_all: bool = False):
        """Display filings in a detailed table."""
        if show_all:
            table = Table(title=title, show_header=True, header_style="bold magenta")
            
            # Add columns
            table.add_column("Date", style="cyan", width=10)
            table.add_column("Court ID", style="yellow", width=8)
            table.add_column("Docket Num", style="green", width=20)
            table.add_column("Transferor Court", style="yellow", width=8)
            table.add_column("Transferor Docket", style="green", width=20)
            table.add_column("Pending CTO", style="blue", width=11)
            table.add_column("Is Transferred", style="blue", width=14)
            table.add_column("Transferred In", style="blue", width=14)
            table.add_column("Plaintiffs", style="red", width=10)
            
            total_plaintiffs = 0
            
            for filing in filings:
                # Get values with defaults
                filing_date = filing.get('filing_date', 'N/A')
                court_id = filing.get('court_id', 'N/A')
                docket_num = filing.get('docket_num', 'N/A')
                transferor_court = filing.get('transferor_court_id', '-')
                transferor_docket = filing.get('transferor_docket_num', '-')
                
                # Boolean fields
                pending_cto = 'Yes' if filing.get('pending_cto') else 'No'
                is_transferred = 'Yes' if filing.get('is_transferred') else 'No'
                transferred_in = 'Yes' if filing.get('transferred_in') else 'No'
                
                # Plaintiff count
                plaintiff_count = self.calculate_plaintiff_count(filing)
                total_plaintiffs += plaintiff_count
                
                table.add_row(
                    filing_date,
                    court_id,
                    docket_num,
                    transferor_court if transferor_court else '-',
                    transferor_docket if transferor_docket else '-',
                    pending_cto,
                    is_transferred,
                    transferred_in,
                    str(plaintiff_count)
                )
            
            console.print(table)
        else:
            # Just show summary
            total_plaintiffs = sum(self.calculate_plaintiff_count(f) for f in filings)
            console.print(f"[cyan]{title}:[/cyan] {len(filings)} cases, {total_plaintiffs} total plaintiffs")
        
        return total_plaintiffs
    
    async def analyze_transfers(self, filings: List[Dict[str, Any]]) -> Tuple[int, List[Dict], List[Dict]]:
        """Analyze transfer relationships and calculate CALC_2."""
        console.print("\n[bold cyan]Analyzing Transfer Relationships...[/bold cyan]")
        
        # Create lookup maps
        case_map = {}  # (court_id, docket_num) -> filing
        transfer_matches = []
        matched_original_filings = []
        
        # Build case map
        for filing in filings:
            court_id = filing.get('court_id')
            docket_num = filing.get('docket_num')
            if court_id and docket_num:
                key = (court_id, docket_num)
                case_map[key] = filing
        
        # Find transfers and their originals
        for filing in filings:
            if filing.get('is_transferred') or filing.get('transferred_in'):
                transferor_court = filing.get('transferor_court_id')
                transferor_docket = filing.get('transferor_docket_num')
                
                if transferor_court and transferor_docket:
                    original_key = (transferor_court, transferor_docket)
                    original_filing = case_map.get(original_key)
                    
                    if original_filing:
                        transfer_matches.append({
                            'transferred_filing': filing,
                            'original_filing': original_filing,
                            'original_plaintiffs': self.calculate_plaintiff_count(original_filing)
                        })
                        matched_original_filings.append(original_filing)
        
        # Display transfer matches
        if transfer_matches:
            table = Table(title="Transfer Matches Found", show_header=True, header_style="bold red")
            table.add_column("Transferred Case", style="cyan")
            table.add_column("Original Case", style="yellow")
            table.add_column("Original Plaintiffs", style="red")
            
            total_matched_plaintiffs = 0
            
            for match in transfer_matches:
                transferred = match['transferred_filing']
                original = match['original_filing']
                original_plaintiffs = match['original_plaintiffs']
                
                transferred_desc = f"{transferred.get('court_id', 'N/A')} / {transferred.get('docket_num', 'N/A')}"
                original_desc = f"{original.get('court_id', 'N/A')} / {original.get('docket_num', 'N/A')}"
                
                table.add_row(transferred_desc, original_desc, str(original_plaintiffs))
                total_matched_plaintiffs += original_plaintiffs
            
            console.print(table)
            console.print(f"\n[bold red]CALC_2: Total plaintiffs in matched original cases: {total_matched_plaintiffs}[/bold red]\n")
            
            return total_matched_plaintiffs, transfer_matches, matched_original_filings
        else:
            console.print("[yellow]No transfer matches found in the 30-day period[/yellow]")
            return 0, [], []
    
    async def run_analysis(self, date_str: str):
        """Run the complete AFFF plaintiff count analysis."""
        try:
            await self.initialize()
            
            async with self.storage:
                # Header
                console.print(Panel.fit(
                    f"[bold blue]AFFF (MDL 2873) Plaintiff Count Analysis[/bold blue]\n"
                    f"[cyan]30-day period ending: {date_str}[/cyan]",
                    border_style="blue"
                ))
                
                # Get all AFFF filings
                filings = await self.get_afff_filings_30_days(date_str)
                
                if not filings:
                    console.print("[red]No AFFF filings found for the specified period[/red]")
                    return
                
                # CALC_1: Display all filings and calculate total
                console.print("\n[bold green]=== CALC_1: All AFFF Filings ===[/bold green]")
                calc_1_total = self.display_filings_table(filings, "All AFFF Filings (30 days)")
                
                # CALC_2: Analyze transfers
                calc_2_total, transfer_matches, matched_originals = await self.analyze_transfers(filings)
                
                # Final calculation
                console.print("\n[bold magenta]=== FINAL CALCULATION ===[/bold magenta]")
                
                summary_table = Table(show_header=True, header_style="bold cyan")
                summary_table.add_column("Calculation", style="yellow")
                summary_table.add_column("Value", style="green", justify="right")
                summary_table.add_column("Description", style="white")
                
                summary_table.add_row(
                    "CALC_1",
                    str(calc_1_total),
                    "Total plaintiffs in all AFFF filings"
                )
                summary_table.add_row(
                    "CALC_2",
                    str(calc_2_total),
                    "Plaintiffs in original cases that were transferred"
                )
                summary_table.add_row(
                    "FINAL",
                    str(calc_1_total - calc_2_total),
                    "CALC_1 - CALC_2 (deduplicated count)"
                )
                
                console.print(summary_table)
                
                # Additional statistics
                console.print("\n[bold blue]=== Additional Statistics ===[/bold blue]")
                
                stats_table = Table(show_header=True, header_style="bold cyan")
                stats_table.add_column("Metric", style="yellow")
                stats_table.add_column("Count", style="green", justify="right")
                
                # Count different types
                pending_cto_count = sum(1 for f in filings if f.get('pending_cto') and not f.get('transferred_in'))
                transferred_in_count = sum(1 for f in filings if f.get('transferred_in'))
                direct_filings = len(filings) - pending_cto_count - transferred_in_count
                
                stats_table.add_row("Total AFFF cases", str(len(filings)))
                stats_table.add_row("Pending CTO cases", str(pending_cto_count))
                stats_table.add_row("Transferred in cases", str(transferred_in_count))
                stats_table.add_row("Direct filings (estimated)", str(direct_filings))
                stats_table.add_row("Transfer matches found", str(len(transfer_matches)))
                
                console.print(stats_table)
                
                # Show how this compares to report calculations
                console.print("\n[bold yellow]=== Report Calculation Comparison ===[/bold yellow]")
                
                pending_cto_plaintiffs = sum(
                    self.calculate_plaintiff_count(f) 
                    for f in filings 
                    if f.get('pending_cto') and not f.get('transferred_in')
                )
                
                transferred_in_plaintiffs = sum(
                    self.calculate_plaintiff_count(f)
                    for f in filings
                    if f.get('transferred_in')
                )
                
                # Check for duplicates by versus field
                versus_map = {}
                duplicates = []
                for f in filings:
                    if f.get('mdl_num') == '2873':
                        versus = f.get('versus', '')
                        if versus and versus != 'N/A':
                            key = (f.get('mdl_num'), versus)
                            if key in versus_map:
                                duplicates.append((versus_map[key], f))
                            else:
                                versus_map[key] = f
                
                console.print(f"\n[yellow]Found {len(duplicates)} duplicate 'versus' entries[/yellow]")
                
                # Calculate deduplicated total
                unique_filings = list(versus_map.values())
                deduplicated_total = sum(self.calculate_plaintiff_count(f) for f in unique_filings)
                
                report_table = Table(show_header=True, header_style="bold cyan")
                report_table.add_column("Report Field", style="yellow")
                report_table.add_column("Value", style="green", justify="right")
                report_table.add_column("Calculation", style="white")
                
                report_table.add_row(
                    "pacer_plaintiffs_period",
                    str(pending_cto_plaintiffs),
                    "Plaintiffs in pending CTO cases"
                )
                report_table.add_row(
                    "transferred_in_plaintiffs_period", 
                    str(transferred_in_plaintiffs),
                    "Plaintiffs in transferred cases"
                )
                report_table.add_row(
                    "Direct filings (calculated)",
                    str(calc_1_total - pending_cto_plaintiffs - transferred_in_plaintiffs),
                    "Total - Pending CTO - Transferred In"
                )
                report_table.add_row(
                    "Chart.js 30-day value (raw)",
                    str(calc_1_total),
                    "All AFFF plaintiffs (includes duplicates)"
                )
                report_table.add_row(
                    "Chart.js 30-day value (deduped)",
                    str(deduplicated_total),
                    "Deduplicated by mdl_num + versus"
                )
                
                console.print(report_table)
                
        except Exception as e:
            console.print(f"[red]Error during analysis: {e}[/red]")
            import traceback
            traceback.print_exc()


async def main():
    """Main entry point."""
    # Get current date or use provided date
    if len(sys.argv) > 1:
        date_str = sys.argv[1]
    else:
        date_str = datetime.now().strftime('%Y%m%d')
    
    # Validate date format
    try:
        datetime.strptime(date_str, '%Y%m%d')
    except ValueError:
        console.print("[red]Error: Date must be in YYYYMMDD format[/red]")
        sys.exit(1)
    
    analyzer = AfffAnalyzer()
    await analyzer.run_analysis(date_str)


if __name__ == "__main__":
    asyncio.run(main())