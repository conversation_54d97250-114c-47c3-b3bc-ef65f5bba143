#!/usr/bin/env python3
"""
Simple script to run FB Ads scraping using the new async orchestrator.
This demonstrates how to use the modernized FB Ads system.
"""

import asyncio
import aiohttp
import sys
import os
import signal
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.services.orchestration.fb_ads_orchestrator import FbAdsOrchestrator
from src.config_models.loader import load_config
from src.config_models.base import WorkflowConfig

# Global shutdown event for signal handling
shutdown_event = None

def setup_signal_handlers():
    """Set up signal handlers for graceful shutdown."""
    global shutdown_event
    shutdown_event = asyncio.Event()
    
    def signal_handler(sig, frame):
        """Handle shutdown signals"""
        print(f"\n⚠️  Received signal {sig}. Starting graceful shutdown...")
        logging.warning(f"Received signal {sig}. Starting graceful shutdown...")
        shutdown_event.set()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def check_shutdown():
    """Check if shutdown has been requested."""
    global shutdown_event
    if shutdown_event and shutdown_event.is_set():
        print("🛑 Shutdown requested, stopping FB ads processing...")
        return True
    return False


def load_fb_ads_config(date_override=None) -> WorkflowConfig:
    """Load FB Ads configuration from YAML file."""
    # Load the YAML file using the config system
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'fb_ads.yml')
    
    # Check if config file exists
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        sys.exit(1)
    
    try:
        config = load_config(config_path)
        
        # Override date if specified
        if date_override:
            # Parse YYYYMMDD format
            try:
                date_obj = datetime.strptime(date_override, '%Y%m%d')
                # Update the config with new date
                config.date = date_obj.date()
                config.iso_date = date_override
            except ValueError:
                print(f"❌ Invalid date format: {date_override}. Use YYYYMMDD format.")
                sys.exit(1)
        elif not config.date:
            # Set today's date if not specified
            today = datetime.now()
            config.date = today.date()
            config.iso_date = today.strftime('%Y%m%d')
        
        return config
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        sys.exit(1)


async def run_full_scrape(date_override=None):
    """Run full FB Ads scraping workflow."""
    print("🚀 Starting FB Ads full scrape workflow...")
    
    if check_shutdown():
        return 1
    
    config = load_fb_ads_config(date_override)
    
    # Ensure fb_ads is enabled
    config.fb_ads = True
    
    try:
        print(f"📅 Processing date: {config.date}")
        
        # Use FbAdsOrchestrator with shutdown event support
        orchestrator = FbAdsOrchestrator(config, shutdown_event)
        
        async with orchestrator:
            if check_shutdown():
                print("🛑 Shutdown requested before execution")
                return 1
                
            await orchestrator.execute()
            
        print("✅ FB Ads scrape completed successfully!")
        return 0
        
    except Exception as e:
        print(f"💥 Error during scraping: {e}")
        import traceback
        traceback.print_exc()
        return 1


async def run_single_firm(firm_id: str, date_override=None):
    """Run single firm processing."""
    print(f"🎯 Starting single firm processing for: {firm_id}")
    
    if check_shutdown():
        return 1
        
    config = load_fb_ads_config(date_override)
    config.fb_ads = True
    
    try:
        # Use FbAdsOrchestrator with shutdown event support
        orchestrator = FbAdsOrchestrator(config, shutdown_event)
        
        async with orchestrator:
            if check_shutdown():
                print("🛑 Shutdown requested before execution")
                return 1
                
            await orchestrator.execute()
            
        print(f"✅ Single firm processing completed for {firm_id}!")
        return 0
        
    except Exception as e:
        print(f"💥 Error processing firm {firm_id}: {e}")
        return 1


async def run_ignore_list(date_override=None):
    """Run ignore list processing."""
    print("📋 Starting ignore list processing...")
    
    if check_shutdown():
        return 1
        
    config = load_fb_ads_config(date_override)
    config.fb_ads = True
    
    try:
        # Use FbAdsOrchestrator with shutdown event support
        orchestrator = FbAdsOrchestrator(config, shutdown_event)
        
        async with orchestrator:
            if check_shutdown():
                print("🛑 Shutdown requested before execution")
                return 1
                
            await orchestrator.execute()
            
        print("✅ Ignore list processing completed!")
        return 0
        
    except Exception as e:
        print(f"💥 Error processing ignore list: {e}")
        return 1


def main():
    """Main entry point with command line options."""
    import argparse
    
    # Set up signal handlers for graceful shutdown
    setup_signal_handlers()
    
    parser = argparse.ArgumentParser(description='Run FB Ads scraping with graceful shutdown support')
    parser.add_argument('--mode', choices=['full', 'single', 'ignore'], 
                       default='full', help='Processing mode')
    parser.add_argument('--firm-id', help='Firm ID for single firm processing')
    parser.add_argument('--date', help='Date in YYYYMMDD format (default: today)')
    parser.add_argument('--concurrent', action='store_true', 
                       help='Enable concurrent processing (default: True)')
    
    args = parser.parse_args()
    
    if args.mode == 'single' and not args.firm_id:
        print("❌ --firm-id is required for single firm processing")
        return 1
    
    try:
        # Run the appropriate workflow
        if args.mode == 'full':
            result = asyncio.run(run_full_scrape(args.date))
        elif args.mode == 'single':
            result = asyncio.run(run_single_firm(args.firm_id, args.date))
        elif args.mode == 'ignore':
            result = asyncio.run(run_ignore_list(args.date))
        
        if check_shutdown():
            print("🛑 Process was interrupted by user")
            return 130  # Standard exit code for Ctrl+C
            
        return result
        
    except KeyboardInterrupt:
        print("\n🛑 Process interrupted by user")
        return 130
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())