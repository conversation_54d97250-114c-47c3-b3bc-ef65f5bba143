**OBJCTIVE**: When `src/services/html/case_parser.py` parses cases, it is parsing the company names and corporation extension which it should not do. 
Each td item is a full company name and should not be split on commas. Example:

https://cdn.lexgenius.ai/20250819/html/scd_25_11228_v_3M_Company_fka_Minnesota_Mining_and_Manufacturing_Company.html

Here are how the defendants are currently being parsed for this item:

```
"defendant": [
        "3M Company (f/k/a Minnesota Mining and Manufacturing Company)",
        "AGC Chemicals America",
        "Inc.",
        "Allstar Fire Equipment",
        "Amerex Corporation",
        "Archroma U.S.",
        "Inc.",
        "Arkema",
        "Inc.",
        "Buckeye Fire Equipment Company",
        "Carrier Global Corporation",
        "Chemguard",
        "Inc.",
        "Chemicals",
        "Inc.",
        "Chemours Company FC",
        "LLC",
        "Chubb Fire",
        "Ltd",
        "Clariant Corp.",
        "Corteva",
        "Inc.",
        "Deepwater Chemicals",
        "Inc.",
        "Du Pont de Nemours Inc. (f/k/a Dowdupont Inc.)",
        "Dynax Corporation",
        "E.I. Du Pont de Nemours and Company",
        "Fire-Dex",
        "LLC",
        "Globe Manufacturing Company LLC",
        "Honeywell Safety Products USA",
        "Inc.",
        "Kidde plc",
        "Lion Group",
        "Inc.",
        "Mallory Safety and Supply LLC",
        "Mine Respirator Company",
        "LLC (f/k/a Mine Safety Appliances Co.",
        "LLC)",
        "Municipal Emergency Services",
        "Inc.",
        "Nation Ford Chemical Company",
        "National Foam",
        "Inc.",
        "PBI Performance Products",
        "Inc.",
        "Raytheon Technologies Corporation",
        "Southern Mills",
        "Inc.",
        "Stedfast USA",
        "Inc.",
        "The Chemours Company",
        "Tyco Fire Products L.P. as successor-in-interest to The Ansul Company",
        "United Technologies Corporation",
        "UTC Fire & Security Americas Corporation",
        "Inc. (f/k/a GE Interlogix",
        "Inc.)",
        "W.L. Gore & Associates",
        "Inc."
    ],
```


**IMPORTANT**
- Instead of using:
  - Analyst use `code-analyzer`.
  - Coordinator use `task-orchestrator`.
  - Optimizer use `perf-analyzer`.
  - Documenter use `api-docs`.
  - Monitor use `performance-benchmarker`.
  - Specialist use `system-architect`.
  - Architect use `system-architect`.
- If you get an error when spawning an agent, select the most appropriate agent for the task from agents in the error message.
- We have moved to `uv pip`. Do NOT use `conda env`.
