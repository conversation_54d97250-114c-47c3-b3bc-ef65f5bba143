repos:
  # Pre-commit hooks for basic file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        exclude: ^archive/
      - id: end-of-file-fixer
        exclude: ^archive/
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: debug-statements
      - id: requirements-txt-fixer
      - id: mixed-line-ending
        args: ['--fix=lf']

  # Python import sorting
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        name: isort (python)
        args: ["--profile", "black", "--filter-files"]
        exclude: ^archive/

  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        language_version: python3.11
        exclude: ^archive/

  # Modern Python linting with Ruff (faster than flake8)
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.3
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        exclude: ^archive/
      - id: ruff-format
        exclude: ^archive/

  # Type checking with mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.16.1
    hooks:
      - id: mypy
        exclude: ^(archive/|tests/)
        additional_dependencies: 
          - types-requests
          - types-beautifulsoup4
          - types-tqdm
          - boto3-stubs
          - pydantic
        args: [--ignore-missing-imports, --no-strict-optional]

  # Security linting
  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.6
    hooks:
      - id: bandit
        args: ["-c", "pyproject.toml"]
        additional_dependencies: ["bandit[toml]"]
        exclude: ^(archive/|tests/)

  # Dependency security scanning (using local safety)
  - repo: local
    hooks:
      - id: safety
        name: safety
        entry: safety
        language: system
        args: [check, --short-report, --ignore=70612]
        files: requirements.*\.txt$

  # Documentation linting
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        exclude: ^(archive/|tests/|src/scripts/)
        args: [--convention=google, --add-ignore=D100,D101,D102,D103,D104,D105,D107]

  # Secrets detection
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: ^(archive/|\.git/)

  # YAML linting
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.37.1
    hooks:
      - id: yamllint
        args: [-c=.yamllint.yml]

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.10.0.1
    hooks:
      - id: shellcheck
        args: [--severity=warning]

  # Commit message linting
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v4.8.3
    hooks:
      - id: commitizen
        stages: [commit-msg]

# Global exclusions
exclude: |
  (?x)^(
      archive/.*|
      \.git/.*|
      \.venv/.*|
      venv/.*|
      __pycache__/.*|
      \.pytest_cache/.*|
      \.mypy_cache/.*|
      \.coverage.*|
      htmlcov/.*|
      dist/.*|
      build/.*|
      \.eggs/.*|
      data/.*
  )$

# Run configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false