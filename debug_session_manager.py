#!/usr/bin/env python3
"""
Debug script to test FacebookSessionManager session creation and token extraction.
This script helps diagnose session management issues independently.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from services.fb_ads.session_manager import FacebookSessionManager


def setup_logging():
    """Setup debug logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('debug_session_manager.log')
        ]
    )
    return logging.getLogger(__name__)


def load_test_config():
    """Load a minimal test configuration."""
    config = {
        # Basic settings
        'testing': False,  # This is the key setting we're testing
        'use_proxy': True,
        'mobile_proxy': False,
        'render_html': False,
        
        # Proxy credentials from environment
        'oxy_labs_residential_username': os.environ.get('OXY_LABS_RESIDENTIAL_USERNAME'),
        'oxy_labs_residential_password': os.environ.get('OXY_LABS_RESIDENTIAL_PASSWORD'),
        'oxy_labs_mobile_username': os.environ.get('OXY_LABS_MOBILE_USERNAME'),
        'oxy_labs_mobile_password': os.environ.get('OXY_LABS_MOBILE_PASSWORD'),
        
        # Retry settings
        'api_retries': 3,
        'api_backoff_base': 1.7,
        
        # Proxy settings
        'oxylabs_num_proxies': 10,
        'proxy_ban_duration': 600,
        'max_proxy_failures': 3,
        
        # Bandwidth logging
        'disable_bandwidth_periodic_logging': True,
        
        # Error handling
        'verbose': True,
    }
    return config


async def test_session_creation():
    """Test session creation and token extraction."""
    logger = setup_logging()
    logger.info("🚀 Starting FacebookSessionManager debug test")
    
    # Load test configuration
    config = load_test_config()
    
    # Check if proxy credentials are available
    has_residential_creds = bool(config['oxy_labs_residential_username'] and config['oxy_labs_residential_password'])
    has_mobile_creds = bool(config['oxy_labs_mobile_username'] and config['oxy_labs_mobile_password'])
    
    logger.info(f"🔐 Proxy credentials - Residential: {'✅' if has_residential_creds else '❌'}, Mobile: {'✅' if has_mobile_creds else '❌'}")
    
    if not (has_residential_creds or has_mobile_creds):
        logger.error("❌ No proxy credentials found in environment variables")
        logger.error("Required: OXY_LABS_RESIDENTIAL_USERNAME/PASSWORD or OXY_LABS_MOBILE_USERNAME/PASSWORD")
        return False
    
    # Initialize session manager
    logger.info("🔧 Initializing FacebookSessionManager")
    session_manager = FacebookSessionManager(config, logger)
    
    try:
        # Test session creation
        logger.info("🎯 Testing session creation...")
        success = await session_manager.create_new_session()
        
        if success:
            logger.info("✅ Session creation SUCCESS")
            
            # Validate session data
            session_data = session_manager.get_session_data()
            session_id = session_manager.get_current_session_id()
            
            logger.info(f"🆔 Session ID: {'✅' if session_id else '❌'} {session_id[:20] if session_id else 'None'}...")
            
            # Check required tokens
            required_tokens = ['fb_dtsg', 'lsd', 'jazoest']
            token_status = {}
            for token in required_tokens:
                has_token = bool(session_data.get(token))
                token_status[token] = '✅' if has_token else '❌'
                if has_token:
                    logger.info(f"🎫 {token}: ✅ {session_data[token][:20]}...")
                else:
                    logger.error(f"🎫 {token}: ❌ MISSING")
            
            logger.info(f"📊 Token summary: {token_status}")
            
            # Check proxy status
            current_proxy = session_manager.get_current_proxy()
            if current_proxy:
                proxy_info = session_manager._extract_session_id_for_log(current_proxy)
                logger.info(f"🌐 Current proxy: ✅ {proxy_info}")
            else:
                logger.warning("🌐 Current proxy: ❌ None")
            
            # Test get_session method
            session = session_manager.get_session()
            logger.info(f"📡 Session object: {'✅' if session else '❌'}")
            
            # Check if testing mode was accidentally enabled
            testing_mode = session_manager.testing
            logger.info(f"🧪 Testing mode: {'⚠️  ENABLED' if testing_mode else '✅ DISABLED'}")
            
            if testing_mode:
                logger.error("❌ CRITICAL: Testing mode is enabled! This explains why no real ads are being fetched.")
                logger.error("💡 Check your configuration files and environment variables.")
                return False
            
            # Final assessment
            all_tokens_present = all(session_data.get(token) for token in required_tokens)
            if all_tokens_present and session_id and not testing_mode:
                logger.info("🎉 Session manager validation PASSED - should work for API calls")
                return True
            else:
                logger.error("❌ Session manager validation FAILED")
                return False
                
        else:
            logger.error("❌ Session creation FAILED")
            return False
            
    except Exception as e:
        logger.error(f"❌ Exception during session test: {e}", exc_info=True)
        return False
    
    finally:
        # Cleanup
        if session_manager.reqs:
            try:
                session_manager.reqs.close()
                logger.info("🧹 Session cleanup completed")
            except Exception as e:
                logger.warning(f"⚠️  Session cleanup warning: {e}")


async def main():
    """Main test function."""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("FacebookSessionManager Debug Test")
    logger.info("=" * 60)
    
    success = await test_session_creation()
    
    logger.info("=" * 60)
    logger.info(f"Test Result: {'✅ PASSED' if success else '❌ FAILED'}")
    logger.info("=" * 60)
    
    return success


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        sys.exit(1)