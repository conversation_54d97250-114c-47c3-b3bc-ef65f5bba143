#!/usr/bin/env python3
"""
Reset processing from 3:25-cv-11409 onward
"""

import sqlite3
import re
from pathlib import Path

# Database configuration
DB_DIR = Path("sqlite")
DB_FILE = DB_DIR / "pacermon_cache.db"

def parse_docket_for_comparison(docket_str):
    """Parse docket string to extract number for comparison."""
    match = re.match(r'(.*?)([0-9]{5})$', docket_str)
    if match:
        base_pattern = match.group(1)
        number = int(match.group(2))
        return base_pattern, number
    return None, None

def should_process_docket(docket_num, min_docket="3:25-cv-11409"):
    """Check if docket should be processed based on minimum docket number."""
    min_base, min_num = parse_docket_for_comparison(min_docket)
    curr_base, curr_num = parse_docket_for_comparison(docket_num)
    
    if not min_base or not curr_base or min_base != curr_base:
        return False
    
    return curr_num >= min_num

def reset_from_11409():
    """Reset processing from 3:25-cv-11409 onward."""
    conn = sqlite3.connect(DB_FILE)
    
    # Get ALL records from the database
    cursor = conn.execute('SELECT docket_num, results_json IS NOT NULL as has_search, page_fetched FROM pacermon_searches')
    all_records = cursor.fetchall()
    
    records_to_reset = []
    for docket_num, has_search, page_fetched in all_records:
        if should_process_docket(docket_num):
            records_to_reset.append((docket_num, has_search, page_fetched))
    
    print(f"Found {len(records_to_reset)} records from 3:25-cv-11409 onward")
    
    if records_to_reset:
        # Show breakdown
        has_search_no_html = 0
        no_search = 0
        
        for docket, has_search, page_fetched in records_to_reset:
            if has_search and not page_fetched:
                has_search_no_html += 1
            elif not has_search:
                no_search += 1
        
        print(f"  - {has_search_no_html} have search results but need HTML fetching")
        print(f"  - {no_search} need both search and HTML fetching")
        
        # Reset records from 3:25-cv-11409 onward - ONLY reset page_fetched to 0 to force HTML reprocessing
        reset_count = 0
        for docket_num, has_search, page_fetched in records_to_reset:
            # Only reset page_fetched to 0 - KEEP ALL EXISTING HTML DATA
            conn.execute('''
                UPDATE pacermon_searches 
                SET page_fetched = 0,
                    retry_count = 0,
                    last_error = NULL,
                    needs_retry = 0
                WHERE docket_num = ?
            ''', (docket_num,))
            reset_count += 1
        
        conn.commit()
        print(f"✅ Reset {reset_count} records from 3:25-cv-11409 onward for reprocessing")
    else:
        print("✅ No records found from 3:25-cv-11409 onward")
    
    conn.close()

if __name__ == "__main__":
    reset_from_11409()