# Proxy Configuration Environment Variable Expansion Fix

## Problem
The proxy configuration was failing because environment variables like `${OXY_LABS_MOBILE_PASSWORD}` were being passed literally to the proxy connection instead of being expanded to their actual values.

## Root Cause
1. The YAML config file (`config/fb_ads.yml`) correctly uses environment variable placeholders like `${OXY_LABS_MOBILE_PASSWORD}`
2. The `ConfigLoader` properly expands these variables when initially loading the config
3. However, when the Pydantic model is converted to a dictionary using `model_dump()`, it returns the raw unexpanded values
4. The `fb_ads_orchestrator.py` was overriding the container's configuration with this unexpanded data

## Solution Implemented

### 1. Updated `src/services/orchestration/fb_ads_orchestrator.py` (lines 196-210)
- Added re-expansion of environment variables after `model_dump()`
- Added logging to verify proxy credentials are properly expanded
- Uses the existing `ConfigLoader._expand_env_vars()` method to re-expand variables

```python
# CRITICAL: Re-expand environment variables that may have been lost during model_dump()
# The ConfigLoader expands env vars during initial load, but model_dump() returns raw values
from src.config_models.loader import ConfigLoader
config_loader = ConfigLoader()
fb_config_data = config_loader._expand_env_vars(fb_config_data)
```

### 2. Updated `src/services/fb_ads/orchestrator.py` (lines 114-131)
- Deprecated the hardcoded `load_config()` function that was bypassing proper config loading
- Removed direct environment variable access for proxy credentials
- Added comment explaining that proxy credentials should come from YAML config

### 3. Added debug logging in:
- `src/services/fb_ads/session_manager.py` (lines 280-289): To detect unexpanded variables
- `src/services/scraping/proxy/proxy_manager.py` (lines 116-120): To detect unexpanded variables

## Testing
The fix ensures that:
1. Environment variables in the YAML config are properly expanded
2. The expanded values are preserved when passing config to containers
3. Both the legacy session manager and Camoufox proxy manager receive properly expanded credentials

## Verification
To verify the fix works:
1. Ensure environment variables are set (e.g., `OXY_LABS_MOBILE_PASSWORD`)
2. Run the FB ads module
3. Check logs for "✅ Mobile proxy password expanded correctly" message
4. Verify proxy connection succeeds

The fix preserves backward compatibility while ensuring environment variables are properly expanded throughout the configuration flow.