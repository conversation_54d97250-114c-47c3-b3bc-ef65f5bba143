#!/usr/bin/env python3
"""
Debug script to test actual data loading from DynamoDB for the specific case.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from datetime import datetime
import json

async def debug_actual_data():
    """Debug the actual data loading process."""
    
    print("=== DEBUGGING ACTUAL DATA LOADING ===")
    
    # Initialize DynamoDB storage with minimal config
    import logging
    config = {
        'region': 'us-east-1',
        'table_name': 'pacer',
        'aws_access_key_id': None,
        'aws_secret_access_key': None,
        'aws_session_token': None
    }
    logger = logging.getLogger('debug')
    
    storage = AsyncDynamoDBStorage(config, logger)
    
    # Initialize repository
    repo = PacerRepository(storage)
    
    # Test specific case
    docket_num = "3:25-cv-01789"
    filing_date = "20250714"
    
    print(f"Looking for case: {docket_num} filed on {filing_date}")
    
    try:
        # Test 1: Query by added_on_range (daily report method)
        print(f"\n--- Test 1: Query by added_on_range ---")
        items = await repo.query_by_added_on_range(filing_date, filing_date)
        
        matching_items = [item for item in items if item.get('docket_num') == docket_num]
        
        if matching_items:
            item = matching_items[0]
            print(f"Found item via added_on_range:")
            print(f"  title: '{item.get('title', 'NOT FOUND')}'")
            print(f"  law_firm: '{item.get('law_firm', 'NOT FOUND')}'")
            print(f"  versus: '{item.get('versus', 'NOT FOUND')}'")
            print(f"  added_on: '{item.get('added_on', 'NOT FOUND')}'")
            print(f"  All keys: {list(item.keys())}")
        else:
            print("No matching items found via added_on_range")
            
        # Test 2: Query by filing_date (weekly report method)
        print(f"\n--- Test 2: Query by filing_date ---")
        items = await repo.query_by_filing_date(filing_date)
        
        matching_items = [item for item in items if item.get('docket_num') == docket_num]
        
        if matching_items:
            item = matching_items[0]
            print(f"Found item via filing_date:")
            print(f"  title: '{item.get('title', 'NOT FOUND')}'")
            print(f"  law_firm: '{item.get('law_firm', 'NOT FOUND')}'")
            print(f"  versus: '{item.get('versus', 'NOT FOUND')}'")
            print(f"  filing_date: '{item.get('filing_date', 'NOT FOUND')}'")
            print(f"  All keys: {list(item.keys())}")
        else:
            print("No matching items found via filing_date")
            
        # Test 3: Check the exact type and value of the title field
        if matching_items:
            item = matching_items[0]
            title_value = item.get('title')
            print(f"\n--- Test 3: Title field analysis ---")
            print(f"Title value: '{title_value}'")
            print(f"Title type: {type(title_value)}")
            print(f"Title length: {len(title_value) if title_value else 'N/A'}")
            print(f"Title is None: {title_value is None}")
            print(f"Title is empty string: {title_value == ''}")
            print(f"Title is 'nan': {title_value == 'nan'}")
            print(f"Title repr: {repr(title_value)}")
        
    except Exception as e:
        print(f"Error during data loading: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_actual_data())