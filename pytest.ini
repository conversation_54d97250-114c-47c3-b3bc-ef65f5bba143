[pytest]
# pytest configuration for LexGenius test suite

# Test discovery patterns
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Test paths
testpaths = tests

# Minimum Python version
minversion = 3.8

# Output options
addopts = 
    -v
    --strict-markers
    --tb=short
    --disable-warnings
    
# Coverage options (when running with pytest-cov)
# Run with: pytest --cov=src --cov-report=html
    
# Markers
markers =
    asyncio: mark a test as asyncio.
    unit: Unit tests for isolated components
    integration: Integration tests for component interactions
    e2e: End-to-end tests for complete workflows
    slow: Tests that take more than 5 seconds
    requires_aws: Tests requiring AWS credentials
    requires_pacer: Tests requiring PACER credentials
    requires_browser: Tests requiring browser functionality
    regression: Tests for regression bugs
    
# Timeout for tests (requires pytest-timeout)
timeout = 300

# Asyncio configuration
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Environment variables for testing
env = 
    LEXGENIUS_ENV=test
    AWS_DEFAULT_REGION=us-west-2