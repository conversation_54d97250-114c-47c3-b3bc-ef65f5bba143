#!/usr/bin/env python3
"""Debug Facebook Ads configuration loading to verify environment variable expansion."""

import os
import sys
from pathlib import Path
from pprint import pprint

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.config_models.loader import load_config
from src.config_models.utils import convert_datetime_fields_to_strings

def debug_fb_config():
    """Debug the Facebook Ads configuration loading process."""
    
    print("=== Facebook Ads Configuration Debug ===\n")
    
    # Check if environment variables are set
    env_vars = [
        'OXY_LABS_MOBILE_PASSWORD',
        'OXY_LABS_MOBILE_USERNAME', 
        'OXY_LABS_RESIDENTIAL_PASSWORD',
        'OXY_LABS_RESIDENTIAL_USERNAME',
        'OPENAI_API_KEY',
        'DEEPSEEK_API_KEY'
    ]
    
    print("1. Environment Variables Check:")
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            print(f"  ✅ {var}: Set (length: {len(value)})")
        else:
            print(f"  ❌ {var}: Not set")
    
    print("\n2. Loading FB Ads Configuration...")
    
    try:
        # Load the config using the same method as main.py
        config = load_config('fb_ads')
        print("  ✅ Config loaded successfully")
        
        # Convert to dict format
        config_dict = config.model_dump()
        
        # Check proxy credentials in the loaded config
        print("\n3. Proxy Credentials in Loaded Config:")
        proxy_fields = [
            'oxy_labs_mobile_password',
            'oxy_labs_mobile_username',
            'oxy_labs_residential_password', 
            'oxy_labs_residential_username'
        ]
        
        for field in proxy_fields:
            if field in config_dict:
                value = config_dict[field]
                if value:
                    if isinstance(value, str) and value.startswith('${'):
                        print(f"  ❌ {field}: NOT EXPANDED - {value}")
                    else:
                        print(f"  ✅ {field}: Properly set (length: {len(str(value))})")
                else:
                    print(f"  ⚠️  {field}: Empty or None")
            else:
                print(f"  ❌ {field}: Not found in config")
        
        # Test the expansion process that fb_ads_orchestrator uses
        print("\n4. Testing fb_ads_orchestrator expansion process...")
        fb_config_data = convert_datetime_fields_to_strings(config.model_dump())
        
        print("  Before expansion:")
        for field in proxy_fields:
            if field in fb_config_data:
                value = fb_config_data[field]
                if value and isinstance(value, str) and value.startswith('${'):
                    print(f"    {field}: {value}")
        
        # Apply the fix - re-expand environment variables
        from src.config_models.loader import ConfigLoader
        config_loader = ConfigLoader()
        fb_config_data = config_loader._expand_env_vars(fb_config_data)
        
        print("\n  After expansion:")
        for field in proxy_fields:
            if field in fb_config_data:
                value = fb_config_data[field]
                if value:
                    if isinstance(value, str) and value.startswith('${'):
                        print(f"    ❌ {field}: STILL NOT EXPANDED - {value}")
                    else:
                        print(f"    ✅ {field}: Expanded (length: {len(str(value))})")
                else:
                    print(f"    ⚠️  {field}: Empty")
        
        print("\n5. Full Configuration Sample (first 1000 chars):")
        config_str = str(fb_config_data)[:1000]
        print(config_str)
        
    except Exception as e:
        print(f"  ❌ Error loading config: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_fb_config()