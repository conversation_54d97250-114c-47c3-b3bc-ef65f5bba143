#!/bin/bash
# Simple CloudFront cache invalidation script

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Get date parameter or use today
DATE=${1:-$(date +%Y%m%d)}

# Check for required environment variable
if [ -z "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
    echo "Error: CLOUDFRONT_DISTRIBUTION_ID environment variable is not set"
    exit 1
fi

echo "Invalidating CloudFront cache for date: $DATE"
echo "Distribution ID: $CLOUDFRONT_DISTRIBUTION_ID"

# Create invalidation
aws cloudfront create-invalidation \
    --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
    --paths "/$DATE/*" "/index.html" "/assets/*" "/*" \
    --query 'Invalidation.Id' \
    --output text

if [ $? -eq 0 ]; then
    echo "Cache invalidation created successfully!"
    echo ""
    echo "To regenerate the daily report, run:"
    echo "python src/main.py --params config/report.yml --date $DATE"
    echo ""
    echo "To regenerate the weekly report, run:"
    echo "python src/main.py --params config/weekly_report.yml --date $DATE"
else
    echo "Failed to create cache invalidation"
    exit 1
fi