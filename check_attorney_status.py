#!/usr/bin/env python3
import sqlite3
from collections import defaultdict

conn = sqlite3.connect('sqlite/pacermon_cache.db')
cursor = conn.cursor()

# Query for records that were successfully updated (have attorney data now)
cursor.execute("""
    SELECT filing_date, docket_num, versus
    FROM pacermon_searches 
    WHERE attorney IS NOT NULL AND attorney != '' AND attorney != '[]'
    ORDER BY filing_date DESC
""")

all_records = cursor.fetchall()

# Query for records that still have missing attorney data
cursor.execute("""
    SELECT filing_date, docket_num, versus
    FROM pacermon_searches 
    WHERE attorney IS NULL OR attorney = '' OR attorney = '[]'
    ORDER BY filing_date DESC
""")

missing_records = cursor.fetchall()
conn.close()

print("=== DATES THAT WERE SUCCESSFULLY CHANGED ===")
print()

# Group successful records by date
by_date = defaultdict(list)
for filing_date, docket_num, versus in all_records:
    if filing_date:
        by_date[filing_date].append((docket_num, versus))

# Show dates with counts
for date in sorted(by_date.keys(), reverse=True):
    cases = by_date[date]
    print(f"{date}: {len(cases)} records with attorney data")

print()
print("=== DOCKET NUMBERS WHERE ATTORNEY NAMES WERE NOT FOUND ===")
print()

for filing_date, docket_num, versus in missing_records:
    date_str = filing_date if filing_date else 'Unknown'
    versus_short = versus[:60] + "..." if versus and len(versus) > 60 else versus or 'N/A'
    print(f"{date_str} | {docket_num} | {versus_short}")

print(f"\nTotal missing: {len(missing_records)} dockets")