{                                                                                                                                                                                                                                                                   │
│     'extracted_at': '2025-07-01T23:47:26.522032',                                                                                                                                                                                                                   │
│     'source': 'docket_report_log',                                                                                                                                                                                                                                  │
│     '_is_explicitly_requested': <PERSON>alse,                                                                                                                                                                                                                              │
│     '_already_exists': False,                                                                                                                                                                                                                                       │
│     '_exists_locally': False,                                                                                                                                                                                                                                       │
│     '_exists_in_db': <PERSON><PERSON><PERSON>,                                                                                                                                                                                                                                         │
│     'court_name': 'California Northern District',                                                                                                                                                                                                                   │
│     'office': 'San Francisco',                                                                                                                                                                                                                                      │
│     'assigned_to': 'Judge <PERSON>',                                                                                                                                                                                                                       │
│     'case_in_other_court': 'Northern District of California, MDL 3084 CRB',                                                                                                                                                                                         │
│     'jury_demand': 'Plaintiff',                                                                                                                                                                                                                                     │
│     'attorney': [                                                                                                                                                                                                                                                   │
│         {                                                                                                                                                                                                                                                           │
│             'attorney_name': '<PERSON>',                                                                                                                                                                                                                    │
│             'law_firm': 'Nachawati Law Group',                                                                                                                                                                                                                      │
│             'phone': '************',                                                                                                                                                                                                                                │
│             'fax': '',                                                                                                                                                                                                                                              │
│             'email': '<EMAIL>',                                                                                                                                                                                                                          │
│             'address1': '5489 Blair Road',                                                                                                                                                                                                                          │
│             'address2': '',                                                                                                                                                                                                                                         │
│             'city': 'Dallas',                                                                                                                                                                                                                                       │
│             'state': 'TX',                                                                                                                                                                                                                                          │
│             'zip_code': '75231',                                                                                                                                                                                                                                    │
│             'lead_attorney': <PERSON><PERSON><PERSON>,                                                                                                                                                                                                                                 │
│             'pro_hac_vice': <PERSON><PERSON><PERSON>,                                                                                                                                                                                                                                  │
│             'attorney_to_be_noticed': False                                                                                                                                                                                                                         │
│         },                                                                                                                                                                                                                                                          │
│         {                                                                                                                                                                                                                                                           │
│             'attorney_name': '<PERSON> S. Schulte',                                                                                                                                                                                                                    │
│             'law_firm': '<PERSON>chawati Law Group',                                                                                                                                                                                                                      │
│             'phone': '************',                                                                                                                                                                                                                                │
│             'fax': '',                                                                                                                                                                                                                                              │
│             'email': '<EMAIL>',                                                                                                                                                                                                                          │
│             'address1': '5489 Blair Road',                                                                                                                                                                                                                          │
│             'address2': '',                                                                                                                                                                                                                                         │
│             'city': 'Dallas',                                                                                                                                                                                                                                       │
│             'state': 'TX',                                                                                                                                                                                                                                          │
│             'zip_code': '75231',                                                                                                                                                                                                                                    │
│             'lead_attorney': False,                                                                                                                                                                                                                                 │
│             'pro_hac_vice': False,                                                                                                                                                                                                                                  │
│             'attorney_to_be_noticed': False                                                                                                                                                                                                                         │
│         },                                                                                                                                                                                                                                                          │
│         {                                                                                                                                                                                                                                                           │
│             'attorney_name': 'Steve S. Schulte',                                                                                                                                                                                                                    │
│             'law_firm': 'Nachawati Law Group',                                                                                                                                                                                                                      │
│             'phone': '************',                                                                                                                                                                                                                                │
│             'fax': '',                                                                                                                                                                                                                                              │
│             'email': '<EMAIL>',                                                                                                                                                                                                                          │
│             'address1': '5489 Blair Road',                                                                                                                                                                                                                          │
│             'address2': '',                                                                                                                                                                                                                                         │
│             'city': 'Dallas',                                                                                                                                                                                                                                       │
│             'state': 'TX',                                                                                                                                                                                                                                          │
│             'zip_code': '75231',                                                                                                                                                                                                                                    │
│             'lead_attorney': False,                                                                                                                                                                                                                                 │
│             'pro_hac_vice': False,                                                                                                                                                                                                                                  │
│             'attorney_to_be_noticed': False                                                                                                                                                                                                                         │
│         }                                                                                                                                                                                                                                                           │
│     ],                                                                                                                                                                                                                                                              │
│     'date_filed': '07/01/25',                                                                                                                                                                                                                                       │
│     'base_filename': 'cand_25_05522_DOE_NLG_IK_v_UBER_TECHNOLOGIES_et_al',                                                                                                                                                                                          │
│     'original_filename': 'cand_25_05522_DOE_NLG_IK_v_UBER_TECHNOLOGIES_et_al',                                                                                                                                                                                      │
│     's3_html': '/20250701/html/cand_25_05522_DOE_NLG_IK_v_UBER_TECHNOLOGIES_et_al.html',                                                                                                                                                                            │
│     'transferred_from': 'Northern District of California, MDL 3084 CRB',                                                                                                                                                                                            │
│     'transferor_court': 'Northern District of California',                                                                                                                                                                                                          │
│     'transferor_court_docket_num': 'MDL 3084 CRB',                                                                                                                                                                                                                  │
│     'plaintiff': [                                                                                                                                                                                                                                                  │
│         'JANE DOE NLG (IK)'                                                                                                                                                                                                                                         │
│     ],                                                                                                                                                                                                                                                              │
│     'is_removal': True,                                                                                                                                                                                                                                             │
│     '_initial_removal_status': False,                                                                                                                                                                                                                               │
│     '_has_transfer_indicator': True,                                                                                                                                                                                                                                │
│     '_transfer_source': 'Northern District of California, MDL 3084 CRB',                                                                                                                                                                                            │
│     '_initial_classification_completed': True,                                                                                                                                                                                                                      │
│     '_classification_version': '2.0',                                                                                                                                                                                                                               │
│     '_html_indicates_removal': False,                                                                                                                                                                                                                               │
│     '_html_indicates_transfer': False,                                                                                                                                                                                                                              │
│     '_html_transfer_indicators': [                                                                                                                                                                                                                                  │
│         'mdl'                                                                                                                                                                                                                                                       │
│     ],                                                                                                                                                                                                                                                              │
│     '_final_removal_determination': {                                                                                                                                                                                                                               │
│         'initial_removal': False,                                                                                                                                                                                                                                   │
│         'html_removal': False,                                                                                                                                                                                                                                      │
│         'final_status': False                                                                                                                                                                                                                                       │
│     },                                                                                                                                                                                                                                                              │
│     '_requires_transfer_processing': True,                                                                                                                                                                                                                          │
│     '_case_type': 'transfer',                                                                                                                                                                                                                                       │
│     '_html_classification_completed': True,                                                                                                                                                                                                                         │
│     'is_transferred': False,                                                                                                                                                                                                                                        │
│     'pending_cto': False,                                                                                                                                                                                                                                           │
│     'transferor_docket_num': 'MDL 3084 CRB',                                                                                                                                                                                                                        │
│     'transferor_court_id': 'UNK',                                                                                                                                                                                                                                   │
│     '_transfer_processing_successful': False,                                                                                                                                                                                                                       │
│     '_processed_timestamp': '2025-07-02T00:27:07.747151',                                                                                                                                                                                                           │
│     '_processed_by': 'PacerCaseProcessingService',                                                                                                                                                                                                                  │
│     '_processing_version': '2.0',                                                                                                                                                                                                                                   │
│     '_docket_sheet_html': '<html><head><link rel="shortcut icon" href="/favicon.ico"><title>CAND-ECF</title>\n<script type="text/javascript">var default_base_path = "/"; </script><script type="text/javascript">if (top!=self)                                    │
│ {top.location.replace(location.href);}</script><link rel="stylesheet" type="text/css" href="/css/default.css"><script type="text/javascript" src="/lib/core.js"></script><link rel="stylesheet" type="text/css" href="/css/print.css" media="print"><script         │
│ type="text/javascript" src="/cgi-bin/menu.pl?id=-1"></script></head><body bgcolor="CCFFFF" text="000000"><iframe id="_yuiResizeMonitor" style="position: absolute; visibility: visible; width: 10em; height: 10em; top: -160px; left: -160px; border-width:         │
│ 0px;"></iframe>        <div id="topmenu" class="yuimenubar yui-module yui-overlay visible" style="position: static; display: block; z-index: 30; visibility: visible;">\n\t\t\t\t<div class="bd"><img src="/graphics/logo-cmecf-sm.png" class="cmecfLogo"           │
│ id="cmecfLogo" alt="CM/ECF" title="">\n\t\t\t\t<ul class="first-of-type">\n<li class="yuimenubaritem first-of-type" id="yui-gen0" groupindex="0" index="0"><a class="yuimenubaritemlabel" href="/cgi-bin/iquery.pl"><u>Q</u>uery</a></li>\n<li                      │
│ class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen1" groupindex="0" index="1"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Reports">Reports <div class="spritedownarrow"></div></a></li>\n<li              │
│ class="yuimenubaritem yuimenubaritem-hassubmenu" id="yui-gen2" groupindex="0" index="2"><a class="yuimenubaritemlabel yuimenubaritemlabel-hassubmenu" href="/cgi-bin/DisplayMenu.pl?Utilities"><u>U</u>tilities <div                                                │
│ class="spritedownarrow"></div></a></li>\n\t\t\t\t<li class="yuimenubaritem" id="yui-gen3" groupindex="0" index="3">\n\t\t\t\t<a class="yuimenubaritemlabel" onclick="CMECF.MainMenu.showHelpPage(); return false">Help</a></li>\n\t\t\t\t\n<li                      │
│ class="yuimenubaritem" id="yui-gen4" groupindex="0" index="4"><a class="yuimenubaritemlabel" href="/cgi-bin/login.pl?logout">Log Out</a></li></ul><hr class="hrmenuseparator"></div></div><script type="text/javascript">if                                         │
│ (navigator.appVersion.indexOf("MSIE")==-1){window.setTimeout(CMECF.MainMenu.createMenu, 0);}else{CMECF.util.Event.addListener(window, "load", CMECF.MainMenu.createMenu);}</script>  <div id="cmecfMainContent" style="height: 678px;"><input type="hidden"         │
│ id="cmecfMainContentScroll" value="0"><h3 align="center">U.S. District Court<br>\nCalifornia Northern District (San Francisco)<br>\nCIVIL DOCKET FOR CASE #: 3:25-cv-05522-CRB</h3>\n<table width="100%" border="0" cellspacing="5"><tbody><tr>\n<td valign="top"   │
│ width="60%"><br>DOE NLG (IK) v. UBER TECHNOLOGIES et al<br>\nAssigned to: Judge Charles R. Breyer<br>\n<table border="0" cellspacing="0"><tbody><tr><td valign="top">Case&nbsp;in&nbsp;other&nbsp;court:</td><td>&nbsp;Northern District of California, MDL 3084    │
│ CRB</td></tr></tbody></table>\nCause: 28:1332 Diversity-(Citizenship)</td>\n<td valign="top" width="40%"><br>Date Filed: 07/01/2025<br>\nJury Demand: Plaintiff<br>\nNature of Suit: 360 P.I.: Other<br>\nJurisdiction:                                             │
│ Diversity</td>\n</tr></tbody></table>\n<table width="100%" border="0" cellspacing="5">\n\t\t\t\t<tbody><tr>\n\t\t\t\t\t<td><b><u>Plaintiff                               </u></b></td>\n\t\t\t\t</tr>\n\t\t\t\n\t\t\t<tr>\n\t\t\t\t<td valign="top"                 │
│ width="40%">\n\t\t\t\t\t<b>JANE DOE NLG (IK)</b>\n\t\t</td>\n<td valign="top" width="20%" align="right">represented&nbsp;by</td><td valign="top" width="40%"><b>Steve                S.              Schulte                                                        │
│ </b>\n<br>Nachawati Law Group                                         \n<br>5489 Blair Road                                             \n<br>Dallas, TX 75231          \n<br>(*************           \n<br>Email: <EMAIL>\n<br><i>ATTORNEY TO BE       │
│ NOTICED</i></td></tr><tr><td></td></tr>\n<tr><td valign="top"><br>V.<br></td></tr>\n\n\t\t\t\t<tr>\n\t\t\t\t\t<td><b><u>Defendant                               </u></b></td>\n\t\t\t\t</tr>\n\t\t\t\n\t\t\t<tr>\n\t\t\t\t<td valign="top"                          │
│ width="40%">\n\t\t\t\t\t<b>UBER TECHNOLOGIES</b>\n\t\t</td>\n</tr><tr><td></td></tr>\n\n\t\t\t\t<tr>\n\t\t\t\t\t<td><b><u>Defendant                               </u></b></td>\n\t\t\t\t</tr>\n\t\t\t\n\t\t\t<tr>\n\t\t\t\t<td valign="top"                        │
│ width="40%">\n\t\t\t\t\t<b>RASIER, LLC</b>\n\t\t</td>\n</tr><tr><td></td></tr>\n\n\t\t\t\t<tr>\n\t\t\t\t\t<td><b><u>Defendant                               </u></b></td>\n\t\t\t\t</tr>\n\t\t\t\n\t\t\t<tr>\n\t\t\t\t<td valign="top"                              │
│ width="40%">\n\t\t\t\t\t<b>RASIER-CA, LLC</b>\n\t\t</td>\n</tr><tr><td></td></tr>\n</tbody></table>\n<br><table align="center" width="99%" border="1" rules="all" cellpadding="5" cellspacing="0">\n<tbody><tr><td style="font-weight:bold; width=94;               │
│ white-space:nowrap">Date Filed</td>\n<th>#</th><td style="font-weight:bold">Docket Text</td></tr>\n<tr><td width="94" valign="top" nowrap="">07/01/2025</td><td style="white-space:nowrap" valign="top" align="right"><a                                            │
│ href="https://ecf.cand.uscourts.gov/doc1/035025918451" onclick="goDLS(\'/doc1/035025918451\',\'452179\',\'5\',\'\',\'1\',\'1\',\'\',\'\',\'\');return(false);">1</a>&nbsp;</td><td valign="top"><!--SB-->COMPLAINT <i>Jane Doe NLG IK</i> against All Defendants (  │
│ Filing fee $ 405, receipt number ACANDC-20847044.). Filed by JANE DOE NLG (IK). (Attachments: # <a href="https://ecf.cand.uscourts.gov/doc1/035125918452"                                                                                                           │
│ onclick="goDLS(\'/doc1/035125918452\',\'452179\',\'5\',\'\',\'1\',\'1\',\'\',\'\',\'\');return(false);">1</a> Civil Cover Sheet)(Schulte, Steve) (Filed on 7/1/2025) (Entered: 07/01/2025)</td></tr>\n</tbody></table><br>\n<hr><center><table border="1"           │
│ bgcolor="white" width="400"><tbody><tr><th colspan="4"><font size="+1" color="DARKRED">PACER Service Center </font></th></tr><tr><th colspan="4"><font color="DARKBLUE">Transaction Receipt </font></th></tr><tr></tr><tr></tr><tr><td colspan="4"                  │
│ align="CENTER"><font size="-1" color="DARKBLUE">07/01/2025 21:27:03</font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> PACER Login: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> gratefuldave </font></td><th                 │
│ align="LEFT"><font size="-1" color="DARKBLUE"> Client Code: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE">  </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Description: </font></th><td align="LEFT"><font size="-1"       │
│ color="DARKBLUE"> Docket Report </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Search Criteria: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 3:25-cv-05522-CRB    Starting with document: 1  Ending with document: 1              │
│ </font></td></tr><tr><th align="LEFT"><font size="-1" color="DARKBLUE"> Billable Pages: </font></th><td align="LEFT"><font size="-1" color="DARKBLUE"> 1 </font></td><th align="LEFT"><font size="-1" color="DARKBLUE"> Cost: </font></th><td align="LEFT"><font    │
│ size="-1" color="DARKBLUE"> 0.10 </font></td></tr><tr></tr><tr></tr></tbody></table></center></div></body></html>',                                                                                                                                                 │
│     '_docket_sheet_html_captured': True,                                                                                                                                                                                                                            │
│     '_relevance_status': 'relevant',                                                                                                                                                                                                                                │
│     '_relevance_reason': 'Passed all relevance checks',                                                                                                                                                                                                             │
│     '_relevance_checked': True,                                                                                                                                                                                                                                     │
│     '_relevance_engine_version': '2.0',                                                                                                                                                                                                                             │
│     '_filters_applied': [                                                                                                                                                                                                                                           │
│         'relevance_checked'                                                                                                                                                                                                                                         │
│     ],                                                                                                                                                                                                                                                              │
│     '_filters_timestamp': '2025-07-02T00:27:08.234943',                                                                                                                                                                                                             │
│     '_should_skip_processing': True                                                                                                                                                                                                                                 │
│ }