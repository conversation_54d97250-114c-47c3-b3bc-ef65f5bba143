tests/integration/test_law_firms_integration.py::TestLawFirmsIntegration::test_error_handling_workflow FAILED [ 15%]
tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_add_and_query_workflow FAILED [ 16%]
tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_query_by_court_and_date FAILED [ 16%]
tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_search_by_law_firm FAILED [ 16%]
tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_get_docket_statistics FAILED [ 16%]
tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_error_handling_workflow FAILED [ 16%]
tests/unit/infrastructure/test_refactoring_complete.py::TestPerformanceMonitoringService::test_initialization FAILED [ 45%]
tests/unit/infrastructure/test_refactoring_complete.py::TestPerformanceMonitoringService::test_service_registration FAILED [ 45%]
tests/unit/infrastructure/test_refactoring_complete.py::TestPerformanceMonitoringService::test_metric_recording FAILED [ 45%]
tests/unit/infrastructure/test_refactoring_complete.py::TestPerformanceMonitoringService::test_performance_summary FAILED [ 45%]
tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_init_with_pdf_source FAILED [ 49%]
tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_init_with_s3_link FAILED [ 49%]
tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_init_without_source_raises_error FAILED [ 49%]
tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_configure_tesseract_success FAILED [ 50%]
tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_configure_tesseract_not_installed FAILED [ 50%]
tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_download_pdf_success FAILED [ 50%]
tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_download_pdf_invalid_url FAILED [ 50%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLAnalytics::test_get_mdl_count_in_date_range FAILED [ 68%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLAnalytics::test_get_filings_by_court FAILED [ 68%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLAnalytics::test_summarize_filings_by_law_firm_for_mdl FAILED [ 68%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestDataFrameOperations::test_get_pacer_records_df FAILED [ 69%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestDataFrameOperations::test_scan_to_dataframe FAILED [ 69%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestDuplicationAnalysis::test_count_identical_court_docket_combinations FAILED [ 69%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLSummaries::test_get_mdl_summary FAILED [ 69%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLSummaries::test_get_mdl_summary2 FAILED [ 69%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestLawFirmExtraction::test_get_unique_law_firms FAILED [ 69%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestExecuteAction::test_execute_action_get_mdl_count FAILED [ 69%]
tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestExecuteAction::test_execute_action_invalid FAILED [ 69%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_classify_ad_success FAILED [ 76%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_extract_summary FAILED [ 76%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_extract_summary_with_chunking FAILED [ 77%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_extract_attorney_info FAILED [ 77%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_determine_court_id FAILED [ 77%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_format_fb_body FAILED [ 77%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_parse_law_firm_signature FAILED [ 77%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_openai_format FAILED [ 77%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_direct_json FAILED [ 77%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_string_response FAILED [ 78%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_no_number_field FAILED [ 78%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_invalid_json FAILED [ 78%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_no_response FAILED [ 78%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_unexpected_format FAILED [ 78%]
tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_extract_all_information FAILED [ 78%]
tests/unit/test_date_processor_shutdown.py::test_date_processor_accepts_shutdown_event FAILED [ 85%]
tests/unit/test_date_processor_shutdown.py::test_date_processor_without_shutdown_event FAILED [ 85%]
tests/unit/test_date_processor_shutdown.py::test_date_processor_passes_shutdown_to_main_orchestrator FAILED [ 85%]
tests/unit/test_date_processor_shutdown.py::test_date_processor_shutdown_handling FAILED [ 86%]
tests/unit/test_end_to_end_shutdown.py::test_complete_shutdown_flow FAILED [ 89%]
tests/unit/test_end_to_end_shutdown.py::test_data_transformer_shutdown_in_processing_phases FAILED [ 89%]
tests/unit/test_end_to_end_shutdown.py::test_shutdown_event_propagation_chain FAILED [ 89%]
tests/unit/test_infrastructure.py::TestInfrastructure::test_fixtures_available ERROR [ 89%]
tests/unit/test_infrastructure.py::TestInfrastructure::test_sample_data_fixtures ERROR [ 89%]
==================================== ERRORS ====================================
_________ ERROR at setup of TestInfrastructure.test_fixtures_available _________
________ ERROR at setup of TestInfrastructure.test_sample_data_fixtures ________
FAILED tests/integration/test_law_firms_integration.py::TestLawFirmsIntegration::test_error_handling_workflow - Failed: DID NOT RAISE <class 'Exception'>
FAILED tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_add_and_query_workflow - AttributeError: 'PacerDocketsQueryService' object has no attribute 'get_docket'
FAILED tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_query_by_court_and_date - AttributeError: 'PacerDocketsQueryService' object has no attribute 'get_dockets_by_court_and_date'
FAILED tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_search_by_law_firm - AttributeError: 'PacerDocketsQueryService' object has no attribute 'get_dockets_by_law_firm'
FAILED tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_get_docket_statistics - AttributeError: 'PacerDocketsQueryService' object has no attribute 'get_docket_statistics'
FAILED tests/integration/test_pacer_dockets_integration.py::TestPacerDocketsIntegration::test_error_handling_workflow - AssertionError: Regex pattern did not match.
FAILED tests/unit/infrastructure/test_refactoring_complete.py::TestPerformanceMonitoringService::test_initialization - TypeError: PerformanceMonitoringService.__init__() missing 1 required positional argument: 'config'
FAILED tests/unit/infrastructure/test_refactoring_complete.py::TestPerformanceMonitoringService::test_service_registration - TypeError: PerformanceMonitoringService.__init__() missing 1 required positional argument: 'config'
FAILED tests/unit/infrastructure/test_refactoring_complete.py::TestPerformanceMonitoringService::test_metric_recording - TypeError: PerformanceMonitoringService.__init__() missing 1 required positional argument: 'config'
FAILED tests/unit/infrastructure/test_refactoring_complete.py::TestPerformanceMonitoringService::test_performance_summary - TypeError: PerformanceMonitoringService.__init__() missing 1 required positional argument: 'config'
FAILED tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_init_with_pdf_source - NameError: name 'logging' is not defined
FAILED tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_init_with_s3_link - NameError: name 'logging' is not defined
FAILED tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_init_without_source_raises_error - AttributeError: 'ExceptionInfo' object has no attribute 'exception'
FAILED tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_configure_tesseract_success - NameError: name 'logging' is not defined
FAILED tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_configure_tesseract_not_installed - NameError: name 'logging' is not defined
FAILED tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_download_pdf_success - NameError: name 'logging' is not defined
FAILED tests/unit/lib/test_pdf_extractor.py::TestPDFExtractor::test_download_pdf_invalid_url - NameError: name 'logging' is not defined
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLAnalytics::test_get_mdl_count_in_date_range - AssertionError: assert <AsyncMock name='mock.get_mdl_count_in_date_range()' id='13690798032'> == 2
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLAnalytics::test_get_filings_by_court - AssertionError: assert False
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLAnalytics::test_summarize_filings_by_law_firm_for_mdl - ValueError: not enough values to unpack (expected 2, got 0)
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestDataFrameOperations::test_get_pacer_records_df - AssertionError: assert False
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestDataFrameOperations::test_scan_to_dataframe - AssertionError: assert False
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestDuplicationAnalysis::test_count_identical_court_docket_combinations - AssertionError: assert 0 == 1
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLSummaries::test_get_mdl_summary - AssertionError: assert False
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestMDLSummaries::test_get_mdl_summary2 - AssertionError: assert False
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestLawFirmExtraction::test_get_unique_law_firms - AssertionError: assert False
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestExecuteAction::test_execute_action_get_mdl_count - AssertionError: assert <AsyncMock name='mock._execute_action()' id='13060274576'> == 2
FAILED tests/unit/services/pacer/test_analytics_service.py::TestAnalyticsService::TestExecuteAction::test_execute_action_invalid - Failed: DID NOT RAISE <class 'src.infrastructure.protocols.exceptions.PacerServiceError'>
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_classify_ad_success - assert False is True
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_extract_summary - AssertionError: assert None == 'Product liability case involving defective products'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_extract_summary_with_chunking - AssertionError: assert None == 'Part 1'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_extract_attorney_info - assert 0 == 2
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_determine_court_id - AssertionError: assert None == 'TXND'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_format_fb_body - AssertionError: assert '\U0001f4e2' in 'Plain legal content'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_parse_law_firm_signature - AssertionError: assert None == 'Smith & Associates LLP'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_openai_format - AssertionError: assert None == '1'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_direct_json - AssertionError: assert None == '2'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_string_response - AssertionError: assert None == '3'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_no_number_field - assert "No 'number' field in response" in 'PromptManager not available'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_invalid_json - AssertionError: assert 'Failed to parse response' in 'PromptManager not available'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_no_response - AssertionError: assert 'PromptManager not available' == 'No response from AI service'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_get_removal_link_number_unexpected_format - AssertionError: assert 'Unexpected response format' in 'PromptManager not available'
FAILED tests/unit/services/test_deepseek_service.py::TestDeepSeekService::test_extract_all_information - AssertionError: assert 0 >= 3
FAILED tests/unit/test_date_processor_shutdown.py::test_date_processor_accepts_shutdown_event -   File "/Users/<USER>/PycharmProjects/lexgenius/src/services/ai/batch_processor.py", line 127
FAILED tests/unit/test_date_processor_shutdown.py::test_date_processor_without_shutdown_event -   File "/Users/<USER>/PycharmProjects/lexgenius/src/services/ai/batch_processor.py", line 127
FAILED tests/unit/test_date_processor_shutdown.py::test_date_processor_passes_shutdown_to_main_orchestrator -   File "/Users/<USER>/PycharmProjects/lexgenius/src/services/ai/batch_processor.py", line 127
FAILED tests/unit/test_date_processor_shutdown.py::test_date_processor_shutdown_handling -   File "/Users/<USER>/PycharmProjects/lexgenius/src/services/ai/batch_processor.py", line 127
FAILED tests/unit/test_end_to_end_shutdown.py::test_complete_shutdown_flow -   File "/Users/<USER>/PycharmProjects/lexgenius/src/services/ai/batch_processor.py", line 127
FAILED tests/unit/test_end_to_end_shutdown.py::test_data_transformer_shutdown_in_processing_phases - AttributeError: <module 'src.services.transformer.data_transformer' from '/Users/<USER>/PycharmProjects/lexgenius/src/services/transformer/data_transformer.py'> does not have the attribute 'DataProcessingEngine'
FAILED tests/unit/test_end_to_end_shutdown.py::test_shutdown_event_propagation_chain -   File "/Users/<USER>/PycharmProjects/lexgenius/src/services/ai/batch_processor.py", line 127
ERROR tests/unit/test_infrastructure.py::TestInfrastructure::test_fixtures_available
ERROR tests/unit/test_infrastructure.py::TestInfrastructure::test_sample_data_fixtures
