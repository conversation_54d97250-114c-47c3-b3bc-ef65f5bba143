#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add debug logging to the report service to track down the Title issue.
"""

import os

def add_debug_logging():
    """Add debug logging to track the Title field processing."""
    
    file_path = "src/services/reports/data_loader_service.py"
    
    print("=== ADDING DEBUG LOGGING TO TRACK TITLE FIELD ===")
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the location to add debug logging
    debug_code = '''        # DEBUG: Log title field before processing
        if len(df) > 0:
            sample_title = df['title'].iloc[0] if 'title' in df.columns else 'COLUMN NOT FOUND'
            self.logger.info(f"🔍 TITLE DEBUG - Before processing: '{sample_title}' (type: {type(sample_title)})")
        '''
    
    # Add debug logging before the title processing
    insert_point = "        # Handle title: properly handle NaN values before string conversion"
    
    if insert_point in content:
        content = content.replace(insert_point, debug_code + "\n        " + insert_point)
    else:
        print(f"❌ Could not find insertion point: {insert_point}")
        return
    
    # Add debug logging after the title processing
    debug_code_after = '''        # DEBUG: Log title field after processing
        if len(df) > 0:
            sample_title = df['title'].iloc[0] if 'title' in df.columns else 'COLUMN NOT FOUND'
            self.logger.info(f"🔍 TITLE DEBUG - After processing: '{sample_title}' (type: {type(sample_title)})")
        '''
    
    insert_point_after = "        df.loc[df['title'].isin(['', 'nan']), 'title'] = 'Unknown Title'"
    
    if insert_point_after in content:
        content = content.replace(insert_point_after, insert_point_after + "\n" + debug_code_after)
    else:
        print(f"❌ Could not find insertion point: {insert_point_after}")
        return
    
    # Write the modified content back
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"✅ Added debug logging to {file_path}")
    print("Now run your report generation and check the logs for '🔍 TITLE DEBUG' messages")

if __name__ == "__main__":
    add_debug_logging()