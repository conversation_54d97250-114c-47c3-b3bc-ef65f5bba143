# Dependency Injection Implementation Checklist

## Overview
Total files to modify: **~150+ files**

This checklist tracks the complete migration from custom DI to dependency-injector framework.

---

## Phase 1: Infrastructure Setup (10 files)

### 1.1 Add dependency-injector to environment
- [ ] `/environment.yml` - Add dependency-injector package

### 1.2 Remove Custom DI Implementation (9 files)
- [ ] `/src/infrastructure/di/container.py` - DELETE
- [ ] `/src/infrastructure/di/base_container.py` - DELETE
- [ ] `/src/infrastructure/di/container_factory.py` - DELETE
- [ ] `/src/infrastructure/di/service_registry.py` - DELETE
- [ ] `/src/infrastructure/di/exceptions.py` - DELETE
- [ ] `/src/infrastructure/di/modules/__init__.py` - DELETE
- [ ] `/src/infrastructure/di/modules/shared_module.py` - DELETE
- [ ] `/src/infrastructure/di/modules/storage_module.py` - DELETE
- [ ] `/src/infrastructure/di/modules/pacer_module.py` - DELETE
- [ ] `/src/infrastructure/di/modules/fb_ads_module.py` - DELETE
- [ ] `/src/infrastructure/di/modules/transformer_module.py` - DELETE
- [ ] `/src/infrastructure/di/modules/reports_module.py` - DELETE
- [ ] `/src/infrastructure/di/modules/orchestration_module.py` - DELETE
- [ ] `/src/infrastructure/di/modules/ai_module.py` - DELETE

---

## Phase 2: Create New Container Structure (7 files)

### 2.1 Create Container Directory Structure
- [ ] `/src/containers/__init__.py` - CREATE
- [ ] `/src/containers/core.py` - CREATE (move from di_container.py)
- [ ] `/src/containers/storage.py` - CREATE (repositories and storage)
- [ ] `/src/containers/pacer.py` - CREATE (PACER services)
- [ ] `/src/containers/fb_ads.py` - CREATE (Facebook Ads services)
- [ ] `/src/containers/transformer.py` - CREATE (transformation services)
- [ ] `/src/containers/reports.py` - CREATE (reporting services)

---

## Phase 3: Update Main Application Files (5 files)

### 3.1 Entry Points
- [ ] `/src/main.py` - Update to use new DI container
- [ ] `/src/factories/main_factory.py` - Replace custom DI with dependency-injector
- [ ] `/src/factories/service_factory.py` - Update if exists
- [ ] `/src/factories/repository_factory.py` - Update if exists
- [ ] `/example_di_container.py` - DELETE (no longer needed)

---

## Phase 4: Update Service Files (104+ files already have @inject)

### 4.1 AI Services (7 files)
- [ ] `/src/services/ai/ai_orchestrator.py` - Verify @inject paths
- [ ] `/src/services/ai/ai_service_factory.py` - Verify @inject paths
- [ ] `/src/services/ai/batch_processor.py` - Verify @inject paths
- [ ] `/src/services/ai/deepseek_service.py` - Verify @inject paths
- [ ] `/src/services/ai/mistral_service.py` - Verify @inject paths
- [ ] `/src/services/ai/prompt_manager.py` - Verify @inject paths

### 4.2 PACER Services (24 files)
- [ ] `/src/services/pacer/analytics_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/authentication_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/case_classification_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/case_processing_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/case_verification_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/configuration_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/court_processing_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/docket_processing_orchestrator_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/download_orchestration_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/export_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/file_management_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/file_operations_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/html_processing_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/ignore_download_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/interactive_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/navigation_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/pacer_orchestrator_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/query_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/relevance_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/report_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/row_processing_service.py` - Verify @inject paths
- [ ] `/src/services/pacer/service_factory.py` - Verify @inject paths
- [ ] `/src/services/pacer/transfer_service.py` - Verify @inject paths

### 4.3 Facebook Ads Services (20 files)
- [ ] `/src/services/fb_ads/ad_db_service.py` - Add @inject if missing
- [ ] `/src/services/fb_ads/ad_ner_processor.py` - Add @inject if missing
- [ ] `/src/services/fb_ads/ad_processing_service.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/api_client.py` - Add @inject if missing
- [ ] `/src/services/fb_ads/bandwidth_logger.py` - Add @inject if missing
- [ ] `/src/services/fb_ads/categorizer.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/classifier.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/concurrent_workflow_service.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/data_validation_service.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/disk_cache.py` - Add @inject if missing
- [ ] `/src/services/fb_ads/error_handling_service.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/failed_firms_manager.py` - Skip (standalone utility)
- [ ] `/src/services/fb_ads/image_handler.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/image_utils.py` - Skip (utility module)
- [ ] `/src/services/fb_ads/interactive_service.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/local_image_queue.py` - Add @inject if missing
- [ ] `/src/services/fb_ads/logging_setup.py` - Skip (utility module)
- [ ] `/src/services/fb_ads/ner_rule_analyzer.py` - Add @inject if missing
- [ ] `/src/services/fb_ads/orchestrator.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/processing_tracker.py` - Add @inject if missing
- [ ] `/src/services/fb_ads/processor.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/session_manager.py` - Verify @inject paths
- [ ] `/src/services/fb_ads/workflow_service.py` - Verify @inject paths

### 4.4 Transformer Services (30 files)
- [ ] `/src/services/transformer/afff_calculator.py` - Verify @inject paths
- [ ] `/src/services/transformer/cached_pdf_data.py` - Verify @inject paths
- [ ] `/src/services/transformer/component_factory.py` - Verify @inject paths
- [ ] `/src/services/transformer/config.py` - Skip (config file)
- [ ] `/src/services/transformer/court_data_processor.py` - Verify @inject paths
- [ ] `/src/services/transformer/data_processing_engine.py` - Verify @inject paths
- [ ] `/src/services/transformer/data_transformer.py` - Verify @inject paths
- [ ] `/src/services/transformer/docket_data_cleaner.py` - Verify @inject paths
- [ ] `/src/services/transformer/docket_file_manager.py` - Verify @inject paths
- [ ] `/src/services/transformer/docket_html_processor.py` - Verify @inject paths
- [ ] `/src/services/transformer/docket_llm_engine.py` - Verify @inject paths
- [ ] `/src/services/transformer/docket_processor.py` - Verify @inject paths
- [ ] `/src/services/transformer/docket_text_handler.py` - Verify @inject paths
- [ ] `/src/services/transformer/docket_validator.py` - Verify @inject paths
- [ ] `/src/services/transformer/error_handler.py` - Verify @inject paths
- [ ] `/src/services/transformer/file_handler.py` - Verify @inject paths
- [ ] `/src/services/transformer/file_operations.py` - Verify @inject paths
- [ ] `/src/services/transformer/html_integration_service.py` - Verify @inject paths
- [ ] `/src/services/transformer/law_firm_integration.py` - Verify @inject paths
- [ ] `/src/services/transformer/law_firm_processor.py` - Verify @inject paths
- [ ] `/src/services/transformer/litigation_classifier.py` - Verify @inject paths
- [ ] `/src/services/transformer/mdl_data_processor.py` - Verify @inject paths
- [ ] `/src/services/transformer/mdl_description_manager.py` - Verify @inject paths
- [ ] `/src/services/transformer/mdl_lookup_manager.py` - Verify @inject paths
- [ ] `/src/services/transformer/mdl_persistence_manager.py` - Verify @inject paths
- [ ] `/src/services/transformer/mdl_processor.py` - Verify @inject paths
- [ ] `/src/services/transformer/mdl_processor_original.py` - Verify @inject paths
- [ ] `/src/services/transformer/specialized_workflows.py` - Verify @inject paths
- [ ] `/src/services/transformer/transfer_handler.py` - Verify @inject paths
- [ ] `/src/services/transformer/uploader.py` - Verify @inject paths

### 4.5 Report Services (8 files)
- [ ] `/src/services/reports/ad_df_processor_service.py` - Verify @inject paths
- [ ] `/src/services/reports/ad_page_generator_service.py` - Verify @inject paths
- [ ] `/src/services/reports/config_service.py` - Verify @inject paths
- [ ] `/src/services/reports/data_loader_service.py` - Verify @inject paths
- [ ] `/src/services/reports/processing_service.py` - Verify @inject paths
- [ ] `/src/services/reports/publishing_service.py` - Verify @inject paths
- [ ] `/src/services/reports/rendering_service.py` - Verify @inject paths
- [ ] `/src/services/reports/reports_orchestrator_service.py` - Verify @inject paths

### 4.6 Orchestration Services (5 files)
- [ ] `/src/services/orchestration/fb_ads_orchestrator.py` - Verify @inject paths
- [ ] `/src/services/orchestration/fb_ads_orchestrator_cli.py` - Skip (CLI tool)
- [ ] `/src/services/orchestration/main_orchestrator.py` - Verify @inject paths
- [ ] `/src/services/orchestration/processing_orchestrator.py` - Verify @inject paths
- [ ] `/src/services/orchestration/scraping_orchestrator.py` - Verify @inject paths
- [ ] `/src/services/orchestration/upload_orchestrator.py` - Verify @inject paths

### 4.7 Other Services (3 files)
- [ ] `/src/services/uploader/s3_upload_service.py` - Verify @inject paths
- [ ] `/src/services/uploader/upload_service.py` - Verify @inject paths
- [ ] `/src/services/document/pdf_processor_service.py` - Add @inject if missing

---

## Phase 5: Update Repository Files (7 files)

- [ ] `/src/repositories/district_courts_repository.py` - Add @inject if needed
- [ ] `/src/repositories/fb_archive_repository.py` - Add @inject if needed
- [ ] `/src/repositories/fb_image_hash_repository.py` - Add @inject if needed
- [ ] `/src/repositories/law_firms_repository.py` - Add @inject if needed
- [ ] `/src/repositories/pacer_dockets_repository.py` - Add @inject if needed
- [ ] `/src/repositories/pacer_repository.py` - Add @inject if needed

---

## Phase 6: Update Infrastructure Files (10 files)

### 6.1 Storage Services
- [ ] `/src/infrastructure/storage/dynamodb_async.py` - Add @inject if needed
- [ ] `/src/infrastructure/storage/s3_async.py` - Add @inject if needed
- [ ] `/src/infrastructure/storage/dynamodb_legacy_adapter.py` - Add @inject if needed

### 6.2 External Services
- [ ] `/src/infrastructure/external/deepseek_client.py` - Add @inject if needed
- [ ] `/src/infrastructure/external/openai_client.py` - Add @inject if needed
- [ ] `/src/infrastructure/external/llava_client.py` - Add @inject if needed

### 6.3 Other Infrastructure
- [ ] `/src/infrastructure/patterns/component_base.py` - Update if needed
- [ ] `/src/infrastructure/patterns/repository_base.py` - Update if needed
- [ ] `/src/infrastructure/lifecycle/lifecycle_manager.py` - Update if needed
- [ ] `/src/infrastructure/monitoring/performance_monitor.py` - Update if needed

---

## Phase 7: Update Test Files (5+ files)

- [ ] `/tests/conftest.py` - Add DI container fixtures
- [ ] `/src/infrastructure/di/test_container.py` - Move to tests/
- [ ] `/src/infrastructure/di/test_di_container.py` - Move to tests/
- [ ] Update unit tests to use container overrides
- [ ] Update integration tests to use test container

---

## Phase 8: Final Cleanup

- [ ] Remove all references to custom DI container
- [ ] Update imports throughout codebase
- [ ] Wire container in main.py
- [ ] Test all services resolve correctly
- [ ] Update CLAUDE.md documentation
- [ ] Create DI conventions guide

---

## Summary

- **Total Infrastructure Files**: ~30
- **Total Service Files**: ~104 
- **Total Repository Files**: ~7
- **Total Other Files**: ~10
- **Grand Total**: ~150+ files

## Execution Order

1. **Infrastructure Setup** (Phase 1-2): Set up dependency-injector and new container structure
2. **Main Application** (Phase 3): Update entry points to use new DI
3. **Service Updates** (Phase 4-6): Update all services to use proper DI paths
4. **Testing** (Phase 7): Update test infrastructure
5. **Cleanup** (Phase 8): Final cleanup and documentation

---

## Notes

- Files marked "Skip" are utility modules that don't need DI
- Files marked "Verify @inject paths" already have @inject but need path updates
- Files marked "Add @inject if missing" need the decorator added
- All Provide paths need to match the new container structure