The CLAUDE.md files refer to an docs/ARCHITECTURAL_CONSISTENCY_PLAN.md which no longer exists. The plan
showed the desired end @src/ structure and @DESIGN_PATTERNS.md would be used as the target goal.
This is to prevent the coding agent from going off on a tangent.

Review ALL CLAUDE.md files in @src/ and all sub-folders. Review @DESIGN_PATTERNS.md and reconstruct the
docs/ARCHITECTURAL_CONSISTENCY_PLAN.md.


```
  ERROR    Error during scraping execution                                                                                                                                                                                                                       core.py:553
                    ERROR    MainOrchestrator encountered an error during run                                                                                                                                                                                            component_base.py:156
                             ╭─────────────────────────────────────────────────────────────────────────────────────────────────── Traceback (most recent call last) ───────────────────────────────────────────────────────────────────────────────────────────────────╮                      
                             │ /Users/<USER>/PycharmProjects/lexgenius/src/services/orchestration/scraping_orchestrator.py:100 in execute                                                                                                                        │                      
                             │                                                                                                                                                                                                                                         │                      
                             │    97 │   │   async with self.pacer_service:                                                                                                                                                                                            │                      
                             │    98 │   │   │   try:                                                                                                                                                                                                                  │                      
                             │    99 │   │   │   │   # 5. Determine processing mode and route appropriately                                                                                                                                                            │                      
                             │ ❱ 100 │   │   │   │   processed = await self._route_processing_mode(                                                                                                                                                                    │                      
                             │   101 │   │   │   │   │   docket_list_input=docket_list_input,                                                                                                                                                                          │                      
                             │   102 │   │   │   │   │   docket_num_param=docket_num_param,                                                                                                                                                                            │                      
                             │   103 │   │   │   │   │   court_ids_param=court_ids_param,                                                                                                                                                                              │                      
                             │                                                                                                                                                                                                                                         │                      
                             │ /Users/<USER>/PycharmProjects/lexgenius/src/services/orchestration/scraping_orchestrator.py:143 in _route_processing_mode                                                                                                         │                      
                             │                                                                                                                                                                                                                                         │                      
                             │   140 │   │   │   │   "docket_count": len(docket_list_input)                                                                                                                                                                            │                      
                             │   141 │   │   │   })                                                                                                                                                                                                                    │                      
                             │   142 │   │   │   # 6. Route to PacerOrchestratorService.process_courts()                                                                                                                                                               │                      
                             │ ❱ 143 │   │   │   await self.pacer_service.process_courts(                                                                                                                                                                              │                      
                             │   144 │   │   │   │   court_ids=[],  # Not used when docket_list_input is provided                                                                                                                                                      │                      
                             │   145 │   │   │   │   context=None,                                                                                                                                                                                                     │                      
                             │   146 │   │   │   │   iso_date=iso_date_str,                                                                                                                                                                                            │                      
                             │                                                                                                                                                                                                                                         │                      
                             │ /Users/<USER>/PycharmProjects/lexgenius/src/services/pacer/pacer_orchestrator_service.py:67 in process_courts                                                                                                                     │                      
                             │                                                                                                                                                                                                                                         │                      
                             │    64 │   │   """                                                                                                                                                                                                                       │                      
                             │    65 │   │   # Service-level validation                                                                                                                                                                                                │                      
                             │    66 │   │   if not court_ids:                                                                                                                                                                                                         │                      
                             │ ❱  67 │   │   │   raise ValueError("court_ids cannot be empty")                                                                                                                                                                         │                      
                             │    68 │   │                                                                                                                                                                                                                             │                      
                             │    69 │   │   # Service-level logging                                                                                                                                                                                                   │                      
                             │    70 │   │   if self._logger:                                                                                                                                                                                                          │                      
                             ╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯                      
                             ValueError: court_ids cannot be empty                                                                                                                    
```

**IMPORTANT**
- Instead of using:
  - Analyst use `code-analyzer`.
  - Coordinator use `task-orchestrator`.
  - Optimizer use `perf-analyzer`.
  - Documenter use `api-docs`.
  - Monitor use `performance-benchmarker`.
  - Specialist use `system-architect`.
  - Architect use `system-architect`.
- If you get an error when spawning an agent, select the most appropriate agent for the task from agents in the error message.
- We have moved to `uv pip`. Do NOT use `conda env`.
