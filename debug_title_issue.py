#!/usr/bin/env python3
"""
Debug script to investigate the Title field issue in report processing.
"""

import pandas as pd
import json
from datetime import datetime

# Sample data from the user's issue
sample_data = {
    "FilingDate": "20250714",
    "DocketNum": "3:25-cv-01789",
    "Title": "AngioDynamics Inc. and Navilyst Medical Inc. Port Catheter Products Liability Litigation",
    "LawFirm": "Dickerson Oxton LLC",
    "CourtId": "casd",
    "AddedOn": "20250714",
    "Versus": "Weber  v. Angiodynamics, Inc. et al"
}

print("=== DEBUGGING TITLE FIELD ISSUE ===")
print(f"Original DynamoDB data (PascalCase):")
print(f"Title: '{sample_data['Title']}'")
print(f"Type: {type(sample_data['Title'])}")
print(f"Length: {len(sample_data['Title'])}")

# Convert to snake_case (as the repository would do)
snake_case_data = {
    'title': sample_data['Title'],
    'law_firm': sample_data['LawFirm'],
    'filing_date': sample_data['FilingDate'],
    'docket_num': sample_data['DocketNum'],
    'court_id': sample_data['CourtId'],
    'added_on': sample_data['AddedOn'],
    'versus': sample_data['Versus']
}

print(f"\nAfter snake_case conversion:")
print(f"title: '{snake_case_data['title']}'")
print(f"Type: {type(snake_case_data['title'])}")
print(f"Length: {len(snake_case_data['title'])}")

# Create DataFrame (as the service would do)
df = pd.DataFrame([snake_case_data])
print(f"\nDataFrame created:")
print(f"df['title'].iloc[0]: '{df['title'].iloc[0]}'")
print(f"Type: {type(df['title'].iloc[0])}")
print(f"Is null: {pd.isna(df['title'].iloc[0])}")
print(f"Is empty string: {df['title'].iloc[0] == ''}")

# Simulate the cleaning process from data_loader_service.py line 644-645
print(f"\n=== SIMULATING CLEANING PROCESS ===")

# Step 1: Replace NaN values
print("Step 1: Replace NaN values")
df['title'] = df['title'].replace({float('nan'): None, 'nan': None}).fillna('').astype(str).str.strip()
print(f"After NaN replacement: '{df['title'].iloc[0]}'")
print(f"Type: {type(df['title'].iloc[0])}")
print(f"Is empty string: {df['title'].iloc[0] == ''}")
print(f"Is 'nan' string: {df['title'].iloc[0] == 'nan'}")

# Step 2: Check if it would be replaced with 'Unknown Title'
print(f"\nStep 2: Check replacement conditions")
title_value = df['title'].iloc[0]
should_replace = title_value in ['', 'nan']
print(f"Should replace with 'Unknown Title': {should_replace}")
print(f"Title value in ['', 'nan']: {should_replace}")

# Step 3: Apply the replacement
df.loc[df['title'].isin(['', 'nan']), 'title'] = 'Unknown Title'
print(f"\nFinal result: '{df['title'].iloc[0]}'")

print(f"\n=== CONCLUSION ===")
if df['title'].iloc[0] == 'Unknown Title':
    print("❌ ISSUE FOUND: Title was replaced with 'Unknown Title'")
    print("This means the title field was empty, null, or 'nan' before processing")
else:
    print("✅ NO ISSUE: Title was preserved correctly")
    print("The cleaning process worked as expected")