#!/usr/bin/env python3
"""
Script to compare docket numbers from report list with actual downloaded files.
"""

import json
import os
import re
from pathlib import Path
from rich.console import Console
from rich.table import Table

console = Console()

def extract_docket_from_report(report_file):
    """Extract docket numbers from report file and convert to format."""
    docket_set = set()
    
    with open(report_file, 'r') as f:
        data = json.load(f)
    
    # Extract cases from the nested structure
    cases = data.get('cases', [])
    
    for case in cases:
        docket_num = case.get('docket_num', '')
        # Extract pattern like 1:25-cv-05436 -> njd_25_05436 or 3:25-cv-08439 -> njd_25_08439
        match = re.match(r'[13]:(\d{2})-cv-(\d{5})', docket_num)
        if match:
            year_digits = match.group(1)
            case_digits = match.group(2)
            formatted = f"njd_{year_digits}_{case_digits}"
            docket_set.add(formatted)
    
    return docket_set

def extract_docket_from_files(data_dir, start_date, end_date):
    """Extract docket prefixes from actual downloaded files."""
    file_set = set()
    
    # Generate date range
    from datetime import datetime, timedelta
    start = datetime.strptime(start_date, '%Y%m%d')
    end = datetime.strptime(end_date, '%Y%m%d')
    
    current = start
    while current <= end:
        date_str = current.strftime('%Y%m%d')
        dockets_dir = Path(data_dir) / date_str / 'dockets'
        
        if dockets_dir.exists():
            for file_path in dockets_dir.glob('njd*.json'):
                filename = file_path.name
                # Extract pattern njd_25_XXXXX from filename
                match = re.match(r'(njd_\d{2}_\d{5})', filename)
                if match:
                    prefix = match.group(1)
                    file_set.add(prefix)
        
        current += timedelta(days=1)
    
    return file_set

def main():
    # File paths
    report_file = "/Users/<USER>/PycharmProjects/lexgenius/data/20250613/logs/docket_report_list_njd.json"
    data_dir = "/Users/<USER>/PycharmProjects/lexgenius/data"
    
    console.print("[bold blue]Extracting docket numbers from report file...[/bold blue]")
    report_dockets = extract_docket_from_report(report_file)
    console.print(f"Found {len(report_dockets)} dockets in report")
    
    console.print("[bold blue]Extracting docket numbers from downloaded files...[/bold blue]")
    file_dockets = extract_docket_from_files(data_dir, "20250605", "20250612")
    console.print(f"Found {len(file_dockets)} unique docket prefixes in files")
    
    # Find dockets in report but not in files
    missing_dockets = report_dockets - file_dockets
    
    console.print(f"\n[bold red]Dockets in report but not found in downloaded files: {len(missing_dockets)}[/bold red]")
    
    # Show some sample ranges for debugging
    if report_dockets:
        report_numbers = [int(d.split('_')[2]) for d in report_dockets]
        console.print(f"Report docket range: {min(report_numbers):05d} - {max(report_numbers):05d}")
    
    if file_dockets:
        file_numbers = [int(d.split('_')[2]) for d in file_dockets]
        console.print(f"Downloaded file range: {min(file_numbers):05d} - {max(file_numbers):05d}")
        console.print(f"Sample downloaded dockets: {sorted(list(file_dockets))[:5]}")
    
    if missing_dockets:
        # Show first 50 missing dockets instead of all
        table = Table(title=f"Missing Dockets (showing first 50 of {len(missing_dockets)})")
        table.add_column("Docket Number", style="cyan")
        
        for docket in sorted(missing_dockets)[:50]:
            table.add_row(docket)
        
        console.print(table)
        
        if len(missing_dockets) > 50:
            console.print(f"[dim]... and {len(missing_dockets) - 50} more[/dim]")
    else:
        console.print("[bold green]All report dockets found in downloaded files![/bold green]")

if __name__ == "__main__":
    main()