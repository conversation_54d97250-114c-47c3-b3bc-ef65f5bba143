For this item, I gam getting the error following it:

ITEM:

``` 
{
    "court_id": "scd",
    "docket_num": "2:25-cv-09963",
    "versus": "Bridgewaters v. 3M Company, f/k/a Minnesota Mining and Manufacturing Company, et al.",
    "filing_date": "20250807",
    "docket_link": "/cgi-bin/DktRpt.pl?309204",
    "cause": "28:1332 Diversity-Personal Injury",
    "nos": "365 Personal Inj. Prod. Liability",
    "extracted_at": "2025-08-07T23:20:32.107700",
    "source": "docket_report_log",
    "flags": [
        "JURY",
        "MDL",
        "html_only"
    ],
    "court_name": "District of South Carolina",
    "office": "Charleston",
    "assigned_to": "Honorable <PERSON>",
    "lead_case": "2:18-mn-02873-RMG",
    "jury_demand": "Plaintiff",
    "jurisdiction": "Diversity",
    "attorney": [
        {
            "attorney_name": "<PERSON> , III",
            "law_firm": "Frazer PLC",
            "phone": "************",
            "fax": "************",
            "email": "<EMAIL>",
            "address1": "30 Burton Hills Boulevard",
            "address2": "",
            "city": "Nashville",
            "state": "TN",
            "zip_code": "37215",
            "lead_attorney": false,
            "pro_hac_vice": false,
            "attorney_to_be_noticed": false
        }
    ],
    "date_filed": "20250807",
    "base_filename": "scd_25_09963_Earnest_Bridgewaters_v_3M_Company_fka_Minnesota_Mining_and_M",
    "original_filename": "scd_25_09963_Earnest_Bridgewaters_v_3M_Company_fka_Minnesota_Mining_and_M",
    "s3_html": "https://cdn.lexgenius.ai/20250807/html/scd_25_09963_Earnest_Bridgewaters_v_3M_Company_fka_Minnesota_Mining_and_M.html",
    "new_filename": "scd_25_09963_Bridgewaters_v_3M_Company_f_k_a_Minnesota_Mining_and_Manufacturing_Company_et_al",
    "case_in_other_court": null,
    "plaintiff": [
        "Earnest Bridgewaters"
    ],
    "defendant": [
        "3M Company",
        "f/k/a Minnesota Mining and Manufacturing Company",
        "AGC Chemicals Americas Inc.",
        "Archroma U.S.",
        "Inc.",
        "Arkema Inc.",
        "BASF Corporation",
        "Buckeye Fire Equipment Company",
        "Carrier Global Corporation",
        "Chemdesign Products",
        "Inc.",
        "Chemguard",
        "Inc.",
        "Chubb Fire",
        "Ltd.",
        "Clariant Corporation",
        "Corteva",
        "Inc.",
        "Deepwater Chemicals",
        "Inc.",
        "DuPont de Nemours Inc.",
        "f/k/a DowDuPont",
        "Inc.",
        "Dynax Corporation",
        "E.I. DuPont de Nemours and Company",
        "individually and as successor in interest to DuPont Chemical Solutions Enterprise",
        "Nation Ford Chemical Company",
        "National Foam",
        "Inc.",
        "The Chemours Company FC",
        "LLC",
        "individually and as successor in interest to DuPont Chemical Solutions Enterprise",
        "The Chemours Company",
        "individually and as successor in interest to DuPont Chemical Solutions Enterprise",
        "Tyco Fire Products L.P.",
        "United Technologies Corporation",
        "Inc.",
        "UTC Fire & Security Americas Corporation",
        "Inc."
    ],
    "referred_to": null,
    "demand": null,
    "is_removal": false,
    "html_only": true,
    "mdl_num": "2873",
    "attorneys_gpt": [
        {
            "attorney_name": "Thomas R Frazer , III",
            "law_firm": "Frazer PLC",
            "phone": "************",
            "fax": "************",
            "email": "<EMAIL>",
            "address1": "30 Burton Hills Boulevard",
            "address2": "",
            "city": "Nashville",
            "state": "TN",
            "zip_code": "37215",
            "lead_attorney": false,
            "pro_hac_vice": false,
            "attorney_to_be_noticed": false
        }
    ],
    "s3_link": "https://cdn.lexgenius.ai/20250807/html/scd_25_09963_Earnest_Bridgewaters_v_3M_Company_fka_Minnesota_Mining_and_M.html",
    "_saved_timestamp": "2025-08-07T23:46:55.358455",
    "_saved_by": "PacerFileOperationsService",
    "json_path": "/Users/<USER>/PycharmProjects/lexgenius/data/20250807/dockets/scd_25_09963_Earnest_Bridgewaters_v_3M_Company_fka_Minnesota_Mining_and_M.json",
    "plaintiffs_gpt": [
        "Earnest Bridgewaters"
    ],
    "title": "Aqueous Film-Forming Foams Products Liability Litigation",
    "allegations": "The plaintiffs are suing a group of companies for producing and selling aqueous film-forming foam (AFFF) that contained harmful substances, specifically per- and polyfluoroalkyl substances (PFAS) such as perfluorooctanoic acid (PFOA) and perfluorooctane sulfonic acid (PFOS). These substances contaminated the plaintiffs' drinking water, leading to personal injuries and bioaccumulation of PFAS in their bodies. The AFFF products were designed to meet military specifications requiring PFAS for effectiveness. The lawsuit alleges negligence, strict liability, failure to warn, defective design and manufacture, and violations of state consumer protection laws. Additional claims include negligent and fraudulent misrepresentation, fraudulent concealment, and unjust enrichment, focusing on both physical harm and economic losses due to the defective AFFF products. <em>Product Liability</em>.",
    "mdl_cat": "NA",
    "law_firms": [
        "Frazer PLC"
    ],
    "law_firm": "Frazer PLC",
    "attorneys": [],
    "law_firm_processed": true,
    "law_firm_normalized": true,
    "law_firm_processing_timestamp": "2025-08-08T00:30:06.188451",
    "law_firm_count": 1,
    "is_transferred": false,
    "pending_cto": false,
    "added_on": "20250807"
}
```
ERROR:

```
M: FilingDate=20250807, DocketNum=2:25-cv-09963
                    DEBUG    Putting item to DynamoDB                                                                                                                                                                                                                  core.py:24
[08/08/25 00:33:00] DEBUG    Putting item to DynamoDB                                                                                                                                                                                                                  core.py:24
[08/08/25 00:33:01] ERROR    DynamoDB ValidationException - Invalid item format: One or more parameter values were invalid: Type mismatch for key FilingDate expected: S actual: NULL                                                                                  core.py:33
                    WARNING  Retrying src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage.put_item in 16.0 seconds as it raised ClientError: An error occurred (ValidationException) when calling the PutItem operation: One or more parameter      before_sleep.py:65
                             values were invalid: Type mismatch for key FilingDate expected: S actual: NULL.                                                                                                                                                                     
                    DEBUG    Putting item to DynamoDB                                                                                                                                                                                                                  core.py:24
                    ERROR    DynamoDB ValidationException - Invalid item format: One or more parameter values were invalid: Type mismatch for key FilingDate expected: S actual: NULL                                                                                  core.py:33
                    WARNING  Retrying src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage.put_item in 16.0 seconds as it raised ClientError: An error occurred (ValidationException) when calling the PutItem operation: One or more parameter      before_sleep.py:65
                             values were invalid: Type mismatch for key FilingDate expected: S actual: NULL.                                                                                                                                                                     
[08/08/25 00:33:04] DEBUG    Putting item to DynamoDB                                                                                                                                                                                                                  core.py:24
                    ERROR    DynamoDB ValidationException - Invalid item format: One or more parameter values were invalid: Type mismatch for key FilingDate expected: S actual: NULL                                                                                  core.py:33
                    WARNING  Retrying src.infrastructure.storage.dynamodb_async.AsyncDynamoDBStorage.put_item in 16.0 seconds as it raised ClientError: An error occurred (ValidationException) when calling the PutItem operation: One or more parameter      before_sleep.py:65
                             values were invalid: Type mismatch for key FilingDate expected: S actual: NULL.                                                                                                                                                                     
[08/08/25 00:33:10] WARNING  add_or_update_record failed with Timeout after 30.0s, attempt 1/6. Retrying in 1.03s...                                                                                     
```

**IMPORTANT**
- If you get an error when spawning an agent, select the most appropriate agent for the task from agents in the error message.
- We have moved to `uv pip`. Do NOT use `conda env`.
