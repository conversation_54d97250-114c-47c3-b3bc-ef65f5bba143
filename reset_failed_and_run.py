#!/usr/bin/env python3
"""
Reset fetch_failed dockets and prepare for rerun
"""

import sqlite3
from pathlib import Path

# Database configuration
DB_DIR = Path("sqlite")
DB_FILE = DB_DIR / "pacermon_cache.db"

def reset_fetch_failed():
    """Reset fetch_failed dockets for reprocessing."""
    conn = sqlite3.connect(DB_FILE)
    
    # Check ALL status values with counts
    cursor = conn.execute('SELECT status, COUNT(*) FROM pacermon_searches GROUP BY status')
    statuses = cursor.fetchall()
    print("All status values in database:")
    for status, count in statuses:
        print(f"  '{status}': {count}")
    
    # Check specifically for docket 3:25-cv-11580 you mentioned
    cursor = conn.execute('SELECT docket_num, status FROM pacermon_searches WHERE docket_num = ?', ('3:25-cv-11580',))
    result = cursor.fetchone()
    if result:
        print(f"\nDocket 3:25-cv-11580 has status: '{result[1]}'")
    else:
        print(f"\nDocket 3:25-cv-11580 not found in database")
    
    # Count ALL records that need HTML fetching
    cursor = conn.execute('''
        SELECT COUNT(*) FROM pacermon_searches 
        WHERE results_json IS NOT NULL 
        AND (page_fetched = 0 OR page_fetched IS NULL)
    ''')
    total_count = cursor.fetchone()[0]
    
    if total_count > 0:
        print(f"\nFound {total_count} records that need HTML fetching")
        
        # Reset ALL records that have results but no HTML fetched - KEEP STATUS AS SUCCESS
        cursor = conn.execute('''
            UPDATE pacermon_searches 
            SET page_fetched = 0, 
                html_file_path = NULL,
                retry_count = 0,
                last_error = NULL,
                needs_retry = 0
            WHERE results_json IS NOT NULL 
            AND (page_fetched = 0 OR page_fetched IS NULL OR status = 'fetch_failed')
        ''')
        
        reset_count = cursor.rowcount
        conn.commit()
        print(f"✅ Reset {reset_count} records for HTML reprocessing")
    else:
        print("\n✅ No records need HTML fetching")
    
    conn.close()

if __name__ == "__main__":
    reset_fetch_failed()