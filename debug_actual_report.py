#!/usr/bin/env python3
"""
Debug script to run the actual report service and trace where the issue occurs.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from datetime import datetime
from typing import Dict, Any

# Configure logging to see debug messages
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def debug_actual_report():
    """Debug the actual report service."""
    
    print("=== DEBUGGING ACTUAL REPORT SERVICE ===")
    
    try:
        # Set up the configuration
        from src.config_models.reports import ReportsConfig
        
        config = ReportsConfig(
            iso_date="20250714",
            seven_days_ago="20250707",
            download_dir="/Users/<USER>/PycharmProjects/lexgenius/data/20250714",
            email=None,
            enabled=True,
            is_weekly=False
        )
        
        # Initialize the data loader service
        from src.services.reports.data_loader_service import ReportsDataLoaderService
        from src.repositories.pacer_repository import PacerRepository
        from src.repositories.fb_ad_repository import FBAdRepository
        from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
        from src.services.reports.law_firm_handler import LawFirmHandler
        from src.services.reports.ad_df_processor_service import AdDataFrameProcessor
        
        # Initialize storage
        storage_config = {
            'region': 'us-west-2',
            'aws_access_key_id': None,
            'aws_secret_access_key': None,
            'aws_session_token': None
        }
        logger = logging.getLogger('debug')
        storage = AsyncDynamoDBStorage(storage_config, logger)
        
        # Initialize repositories
        pacer_repo = PacerRepository(storage)
        fb_ad_repo = FBAdRepository(storage)
        
        # Initialize other dependencies
        law_firm_handler = LawFirmHandler({})
        ad_processor = AdDataFrameProcessor({})
        
        # Initialize the service
        service_config = {
            'reports_config': config
        }
        
        data_loader = ReportsDataLoaderService(
            config=service_config,
            pacer_repo=pacer_repo,
            fb_ad_repo=fb_ad_repo,
            law_firm_handler=law_firm_handler,
            ad_processor=ad_processor
        )
        
        # Initialize the service
        await data_loader.initialize()
        
        print(f"✅ Service initialized successfully")
        
        # Load PACER data
        print(f"\n--- Loading PACER data ---")
        pacer_df = await data_loader.load_pacer_dataframe(is_weekly=False)
        
        print(f"Loaded {len(pacer_df)} PACER records")
        
        # Check for our specific case
        target_docket = "3:25-cv-01789"
        target_court = "casd"
        
        matching_records = pacer_df[
            (pacer_df['docket_num'] == target_docket) & 
            (pacer_df['court_id'] == target_court)
        ]
        
        if len(matching_records) > 0:
            record = matching_records.iloc[0]
            print(f"\n✅ Found matching record:")
            print(f"   docket_num: {record['docket_num']}")
            print(f"   court_id: {record['court_id']}")
            print(f"   title: '{record['title']}'")
            print(f"   law_firm: '{record['law_firm']}'")
            print(f"   versus: '{record['versus']}'")
            print(f"   filing_date: {record['filing_date']}")
            print(f"   added_on: {record['added_on']}")
            
            if record['title'] == 'Unknown Title':
                print(f"❌ PROBLEM: Title is 'Unknown Title' in processed data")
                print(f"This confirms the issue is in the data loading/processing pipeline")
            else:
                print(f"✅ GOOD: Title is preserved in processed data")
        else:
            print(f"❌ No matching record found in processed data")
            print(f"Available records:")
            print(f"   Total records: {len(pacer_df)}")
            if len(pacer_df) > 0:
                print(f"   Sample docket_nums: {pacer_df['docket_num'].head().tolist()}")
                print(f"   Sample court_ids: {pacer_df['court_id'].head().tolist()}")
                
        # Check for any 'Unknown Title' records
        unknown_title_records = pacer_df[pacer_df['title'] == 'Unknown Title']
        print(f"\n--- Summary ---")
        print(f"Total records: {len(pacer_df)}")
        print(f"Records with 'Unknown Title': {len(unknown_title_records)}")
        
        if len(unknown_title_records) > 0:
            print(f"Sample 'Unknown Title' records:")
            for i, (idx, row) in enumerate(unknown_title_records.head(3).iterrows()):
                print(f"   {i+1}. {row['court_id']} {row['docket_num']} - {row['versus']}")
        
        # Check cache info
        print(f"\n--- Cache Info ---")
        print(f"Has cached data: {data_loader._cached_docket_df is not None}")
        if data_loader._cached_docket_df is not None:
            print(f"Cached data size: {len(data_loader._cached_docket_df)}")
        
        # Clean up
        await data_loader.cleanup()
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_actual_report())