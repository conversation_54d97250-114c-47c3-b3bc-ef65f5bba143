#!/usr/bin/env python3
"""
Analyze Dependency Injection status across all services in src/services/
"""
import ast
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple
import json


class ServiceAnalyzer(ast.NodeVisitor):
    """AST visitor to analyze service classes and their dependencies"""
    
    def __init__(self):
        self.services = []
        self.current_class = None
        self.current_file = None
        
    def visit_ClassDef(self, node):
        """Visit class definitions"""
        if any(base.id == 'AsyncServiceBase' for base in node.bases if isinstance(base, ast.Name)):
            service_info = {
                'name': node.name,
                'file': self.current_file,
                'has_initialize_service': False,
                'uses_get_dependency': False,
                'manual_instantiations': [],
                'injected_dependencies': [],
                'constructor_params': []
            }
            
            self.current_class = service_info
            self.services.append(service_info)
            
            # Visit child nodes
            self.generic_visit(node)
            self.current_class = None
            
    def visit_FunctionDef(self, node):
        """Visit function definitions"""
        if self.current_class:
            if node.name == '_initialize_service':
                self.current_class['has_initialize_service'] = True
            elif node.name == '__init__':
                # Extract constructor parameters
                for arg in node.args.args[1:]:  # Skip 'self'
                    self.current_class['constructor_params'].append(arg.arg)
                    
        self.generic_visit(node)
        
    def visit_AsyncFunctionDef(self, node):
        """Visit async function definitions"""
        self.visit_FunctionDef(node)
        
    def visit_Call(self, node):
        """Visit function calls to detect service instantiations"""
        if self.current_class:
            # Check for get_dependency calls
            if isinstance(node.func, ast.Attribute) and node.func.attr == 'get_dependency':
                if node.args and isinstance(node.args[0], ast.Constant):
                    self.current_class['uses_get_dependency'] = True
                    self.current_class['injected_dependencies'].append(node.args[0].value)
                    
            # Check for manual service instantiation
            elif isinstance(node.func, ast.Name) and node.func.id.endswith('Service'):
                self.current_class['manual_instantiations'].append(node.func.id)
                
        self.generic_visit(node)


def analyze_services_directory(base_path: str) -> Dict[str, any]:
    """Analyze all Python files in the services directory"""
    analyzer = ServiceAnalyzer()
    all_services = []
    
    for root, dirs, files in os.walk(base_path):
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                file_path = os.path.join(root, file)
                analyzer.current_file = file_path
                
                try:
                    with open(file_path, 'r') as f:
                        tree = ast.parse(f.read())
                        analyzer.visit(tree)
                except Exception as e:
                    print(f"Error parsing {file_path}: {e}")
                    
    return analyzer.services


def analyze_di_modules(modules_path: str) -> Dict[str, int]:
    """Count registered services in DI modules"""
    registered_services = {}
    
    for root, dirs, files in os.walk(modules_path):
        for file in files:
            if file.endswith('_module.py'):
                module_name = file.replace('_module.py', '')
                file_path = os.path.join(root, file)
                
                with open(file_path, 'r') as f:
                    content = f.read()
                    # Count registry.register calls
                    count = content.count('registry.register(')
                    registered_services[module_name] = count
                    
    return registered_services


def main():
    """Main analysis function"""
    services_path = '/Users/<USER>/PycharmProjects/lexgenius/worktrees/task-create-system/src/services'
    modules_path = '/Users/<USER>/PycharmProjects/lexgenius/worktrees/task-create-system/src/infrastructure/di/modules'
    
    # Analyze services
    services = analyze_services_directory(services_path)
    
    # Analyze DI modules
    di_registrations = analyze_di_modules(modules_path)
    
    # Calculate statistics
    total_services = len(services)
    services_with_initialize = sum(1 for s in services if s['has_initialize_service'])
    services_using_di = sum(1 for s in services if s['uses_get_dependency'])
    services_with_manual = sum(1 for s in services if s['manual_instantiations'])
    
    # Group by directory
    services_by_dir = {}
    for service in services:
        dir_name = Path(service['file']).parent.name
        if dir_name not in services_by_dir:
            services_by_dir[dir_name] = []
        services_by_dir[dir_name].append(service)
    
    # Print analysis
    print("=" * 80)
    print("DEPENDENCY INJECTION ANALYSIS REPORT")
    print("=" * 80)
    print()
    
    print(f"Total Service Classes (AsyncServiceBase): {total_services}")
    print(f"Services with _initialize_service: {services_with_initialize}")
    print(f"Services using get_dependency: {services_using_di}")
    print(f"Services with manual instantiation: {services_with_manual}")
    print()
    
    print("DI Module Registrations:")
    total_registered = 0
    for module, count in sorted(di_registrations.items()):
        print(f"  {module}: {count} services")
        total_registered += count
    print(f"  Total: {total_registered} registrations")
    print()
    
    print("Services by Directory:")
    for dir_name, dir_services in sorted(services_by_dir.items()):
        di_count = sum(1 for s in dir_services if s['uses_get_dependency'])
        manual_count = sum(1 for s in dir_services if s['manual_instantiations'])
        print(f"\n  {dir_name}/ ({len(dir_services)} services)")
        print(f"    - Using DI: {di_count}")
        print(f"    - Manual instantiation: {manual_count}")
        
        # List services needing migration
        needs_migration = [s for s in dir_services if not s['uses_get_dependency'] and s['manual_instantiations']]
        if needs_migration:
            print("    - Needs migration:")
            for service in needs_migration[:5]:  # Show first 5
                print(f"      • {service['name']} → {', '.join(service['manual_instantiations'])}")
            if len(needs_migration) > 5:
                print(f"      ... and {len(needs_migration) - 5} more")
    
    # Services with complex dependencies
    print("\nServices with Complex Dependencies:")
    complex_services = [s for s in services if len(s['manual_instantiations']) > 2]
    for service in complex_services[:10]:
        print(f"  {service['name']} ({Path(service['file']).parent.name})")
        print(f"    Manual: {', '.join(service['manual_instantiations'][:3])}...")
        
    # Save detailed analysis
    output_file = 'di_analysis_report.json'
    with open(output_file, 'w') as f:
        json.dump({
            'summary': {
                'total_services': total_services,
                'services_with_initialize': services_with_initialize,
                'services_using_di': services_using_di,
                'services_with_manual': services_with_manual,
                'total_di_registrations': total_registered
            },
            'di_registrations': di_registrations,
            'services_by_directory': {
                dir_name: [{'name': s['name'], 'file': s['file'].replace('/Users/<USER>/PycharmProjects/lexgenius/worktrees/task-create-system/', ''), 
                           'uses_di': s['uses_get_dependency'], 
                           'manual_deps': s['manual_instantiations']} 
                          for s in dir_services]
                for dir_name, dir_services in services_by_dir.items()
            }
        }, f, indent=2)
    
    print(f"\nDetailed analysis saved to: {output_file}")


if __name__ == '__main__':
    main()