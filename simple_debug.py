#!/usr/bin/env python3
"""
Super simple debug - just load the page and see what happens.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestConfig:
    def __init__(self):
        self.config = {
            'camoufox': {
                'browser': {
                    'headless': False,
                    'timeout': 60000,
                    'viewport': {'width': 1920, 'height': 1080}
                },
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                    'refresh_before_expiry_seconds': 30
                },
                'anti_bot': {
                    'humanize': True,
                    'mouse_curves': True,
                    'typing_variation': True,
                    'disable_ad_blocker_detection': True,
                    'block_resources_for_performance': False
                },
                'search': {
                    'typing_delay': 120,
                    'suggestion_wait': 8000,
                    'capture_wait': 10
                }
            }
        }

async def simple_debug():
    """Just load Facebook Ad Library and see what we get."""
    logger.info("🔍 Simple debug - just load Facebook and see what happens")
    
    config = TestConfig().config
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )
    
    try:
        # Create session
        logger.info("🚀 Creating browser session")
        success = await session_manager.create_new_session()
        if not success:
            logger.error("❌ Failed to create session")
            return False
        
        logger.info("✅ Browser session created successfully")
        logger.info(f"Current URL after session creation: {session_manager.page.url}")
        
        # Try to navigate to Facebook Ad Library
        logger.info("🌐 Navigating to Facebook Ad Library")
        try:
            await session_manager.page.goto('https://www.facebook.com/ads/library/', 
                                           wait_until='domcontentloaded', timeout=60000)
            logger.info("✅ Navigation completed")
            logger.info(f"Final URL: {session_manager.page.url}")
            
            # Check page title
            title = await session_manager.page.title()
            logger.info(f"Page title: {title}")
            
            # Wait a bit for the page to fully load
            logger.info("⏳ Waiting 5 seconds for page to fully load")
            await asyncio.sleep(5)
            
            # Check for the country dropdown element
            logger.info("🔍 Looking for country dropdown (div#js_o)")
            country_dropdown = await session_manager.page.query_selector('div#js_o')
            if country_dropdown:
                is_visible = await country_dropdown.is_visible()
                logger.info(f"✅ Country dropdown found: visible={is_visible}")
                
                if is_visible:
                    text_content = await country_dropdown.inner_text()
                    logger.info(f"Country dropdown text: '{text_content}'")
                else:
                    logger.warning("⚠️ Country dropdown found but not visible")
            else:
                logger.error("❌ Country dropdown div#js_o not found")
                
                # Let's see what IS on the page
                logger.info("🔍 Checking page content...")
                body_text = await session_manager.page.inner_text('body')
                logger.info(f"Page body contains: {body_text[:500]}...")
            
            # Check if we're logged into Facebook or blocked
            if "log in" in title.lower() or "login" in title.lower():
                logger.error("❌ Facebook is asking us to log in")
                return False
            elif "blocked" in title.lower() or "error" in title.lower():
                logger.error("❌ Facebook blocked or error page")
                return False
            else:
                logger.info("✅ Successfully loaded Facebook Ad Library page")
                return True
                
        except Exception as nav_error:
            logger.error(f"❌ Navigation failed: {nav_error}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        logger.info("🧹 Closing browser")
        await session_manager.cleanup()

async def main():
    logger.info("🚀 Simple Facebook Debug")
    logger.info("Just trying to load Facebook Ad Library and see what happens")
    logger.info("=" * 80)
    
    success = await simple_debug()
    
    if success:
        logger.info("✅ Basic Facebook loading is working")
    else:
        logger.error("❌ Basic Facebook loading failed")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Debug interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Debug failed: {e}")
        sys.exit(1)