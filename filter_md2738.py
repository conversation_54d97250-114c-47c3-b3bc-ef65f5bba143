#!/usr/bin/env python3
"""
Script to filter out cases with case_flags "MD2738" from NJD.json
"""
import json
from pathlib import Path

def filter_md2738_cases(input_file: str, output_file: str = None):
    """
    Load JSON file, remove items with case_flags "MD2738", and save filtered data.
    
    Args:
        input_file: Path to input JSON file
        output_file: Path to output JSON file (defaults to input_file with _filtered suffix)
    """
    input_path = Path(input_file)
    
    if output_file is None:
        output_file = input_path.parent / f"{input_path.stem}_filtered{input_path.suffix}"
    
    # Load JSON data
    with open(input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Filter out items where "flags" list contains "MDL2738"
    def has_mdl2738_flag(item):
        flags = item.get('flags', [])
        return isinstance(flags, list) and 'MDL2738' in flags
    
    if isinstance(data, list):
        filtered_data = [item for item in data if not has_mdl2738_flag(item)]
        removed_count = len(data) - len(filtered_data)
    elif isinstance(data, dict):
        # Handle case where data is a dict with cases in a list
        if 'cases' in data:
            original_count = len(data['cases'])
            data['cases'] = [item for item in data['cases'] if not has_mdl2738_flag(item)]
            removed_count = original_count - len(data['cases'])
            filtered_data = data
        else:
            # Handle dict format differently
            filtered_data = {k: v for k, v in data.items() if not (isinstance(v, dict) and has_mdl2738_flag(v))}
            removed_count = len(data) - len(filtered_data)
    else:
        filtered_data = data
        removed_count = 0
    
    # Save filtered data
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(filtered_data, f, indent=2, ensure_ascii=False)
    
    print(f"Removed {removed_count} items with 'MDL2738' in flags list")
    print(f"Filtered data saved to: {output_file}")
    return filtered_data

if __name__ == "__main__":
    input_file = "data/20250703/logs/docket_report/NJD.json"
    filter_md2738_cases(input_file)