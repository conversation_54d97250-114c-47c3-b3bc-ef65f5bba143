# Facebook Ads Proxy Configuration Fix

## Issue
The error "IH_DOWNLOAD: Cannot download image adarchive/3924301247835404/4095666057416129.jpg: Session not available" indicates that the session manager is not properly initialized when trying to download images from Facebook CDN.

## Root Cause
1. The session is not being initialized before image download attempts
2. Proxy configuration must ALWAYS be used for Facebook CDN downloads

## Fixes Applied

### 1. Image Handler - Always Require Proxy (COMPLETED)
**File**: `/src/services/fb_ads/image_handler.py` (lines 223-250)

Changed the proxy setup logic to ALWAYS require proxy for Facebook CDN downloads:
```python
# ALWAYS use proxy for Facebook CDN downloads
if self.session_manager:
    proxy_settings = self.session_manager.get_current_proxy_settings()
    if proxy_settings:
        proxies = proxy_settings
        # ... use proxy ...
    else:
        self.log_error("CRITICAL - Proxy is REQUIRED for Facebook CDN downloads but failed to get proxy settings")
        return None, s3_exists_result
else:
    self.log_error("CRITICAL - Session manager not available. Proxy is REQUIRED for Facebook CDN downloads")
    return None, s3_exists_result
```

## Additional Fixes Applied

### 2. Session Auto-Initialization (COMPLETED)
**File**: `/src/services/fb_ads/image_handler.py` (lines 216-251)

Added automatic session establishment when session is not available:
```python
session = self.session_manager.get_session()
if not session:
    # Try to establish session if not available
    self.log_warning("Session not available. Attempting to establish session...")
    if hasattr(self.session_manager, 'create_new_session'):
        session_created = await self.session_manager.create_new_session()
        if session_created:
            session = self.session_manager.get_session()
            self.log_info("Successfully established new session")
```

This ensures that even if the session wasn't properly initialized, the image handler will attempt to establish it before failing.

### 3. Ensure Proxy Configuration
Make sure the configuration includes:
```yaml
use_proxy: true
oxylabs_num_proxies: 500  # or appropriate number
oxy_labs_username: <username>
oxy_labs_password: <password>
```

Or via environment variables:
```bash
OXY_LABS_USERNAME=<username>
OXY_LABS_PASSWORD=<password>
```

## Recommendations

### 1. Check Session Manager Creation
Ensure that when creating the session manager for each firm, it's properly initialized:
```python
# In job_orchestration_service.py
session_manager = factory.create(
    config=dependencies_with_progress.get("config", {}),
    logger=dependencies_with_progress.get("logger"),
    firm_id=job.firm_id,
    # ... other params ...
)

# Ensure session is initialized
if not session_manager.get_session():
    await session_manager.establish_session()
```

### 2. Force Session Initialization in Image Handler
As a safety measure, the image handler could check and initialize the session:
```python
session = self.session_manager.get_session()
if not session:
    # Try to establish session
    if hasattr(self.session_manager, 'establish_session'):
        await self.session_manager.establish_session()
        session = self.session_manager.get_session()
    
    if not session:
        self.log_error("Cannot establish session for image download")
        return None, s3_exists_result
```

### 3. Configuration Validation
Add validation to ensure proxy credentials are available when `use_proxy: true`:
```python
if self.config.get('use_proxy', False):
    if not (self.config.get('oxy_labs_username') and self.config.get('oxy_labs_password')):
        raise ValueError("Proxy credentials required when use_proxy is enabled")
```

## Testing
1. Verify proxy credentials are in `.env` or config
2. Check that `use_proxy: true` in config
3. Ensure session manager is properly created for each firm
4. Monitor logs for "Using proxy" messages during image downloads