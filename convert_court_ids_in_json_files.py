#!/usr/bin/env python3
"""
Script to convert all court_id values to lowercase in JSON files.

This script processes:
1. /Users/<USER>/PycharmProjects/lexgenius/data/20250703/logs/removal_cases_all.json
2. /Users/<USER>/PycharmProjects/lexgenius/data/20250703/logs/review_cases_all.json

Usage:
    python convert_court_ids_in_json_files.py [--dry-run]
"""

import json
import shutil
from pathlib import Path
from typing import Dict, List, Any
import argparse
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()

# File paths
DATA_DIR = Path("/Users/<USER>/PycharmProjects/lexgenius/data/20250703")
REMOVAL_CASES_FILE = DATA_DIR / "logs" / "removal_cases_all.json"
REVIEW_CASES_FILE = DATA_DIR / "logs" / "review_cases_all.json"
BACKUP_DIR = DATA_DIR / "backup_court_id_json_conversion"

class CourtIdJsonConverter:
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.console = Console()
        self.changes_made = []
        
    def backup_file(self, file_path: Path) -> None:
        """Create backup of file before modification."""
        if not file_path.exists():
            return
            
        BACKUP_DIR.mkdir(parents=True, exist_ok=True)
        backup_path = BACKUP_DIR / file_path.name
        
        if not self.dry_run:
            shutil.copy2(file_path, backup_path)
        
        console.print(f"[green]✓ Backed up {file_path.name} to {BACKUP_DIR}[/green]")
    
    def process_json_file(self, file_path: Path) -> Dict[str, Any]:
        """Process a single JSON file to convert court_id values to lowercase."""
        if not file_path.exists():
            console.print(f"[red]File not found: {file_path}[/red]")
            return {"file": file_path.name, "status": "not_found", "changes": 0}
        
        console.print(f"\n[bold]Processing: {file_path.name}[/bold]")
        
        # Backup file
        self.backup_file(file_path)
        
        try:
            # Read JSON data
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Process the data
            changes_count = self._convert_court_ids_recursive(data)
            
            if not self.dry_run and changes_count > 0:
                # Write updated JSON
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            
            status = "converted" if changes_count > 0 else "no_changes"
            console.print(f"[green]✓ Processed {file_path.name}: {changes_count} court_id values converted[/green]")
            
            return {
                "file": file_path.name,
                "status": status,
                "changes": changes_count
            }
            
        except Exception as e:
            console.print(f"[red]Error processing {file_path.name}: {e}[/red]")
            return {"file": file_path.name, "status": "error", "changes": 0, "error": str(e)}
    
    def _convert_court_ids_recursive(self, obj: Any, path: str = "") -> int:
        """Recursively convert court_id values to lowercase."""
        changes_count = 0
        
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                if key == "court_id" and isinstance(value, str) and value.isupper():
                    old_value = value
                    new_value = value.lower()
                    obj[key] = new_value
                    changes_count += 1
                    console.print(f"  [cyan]{current_path}[/cyan]: {old_value} → {new_value}")
                    self.changes_made.append(f"{current_path}: {old_value} → {new_value}")
                else:
                    changes_count += self._convert_court_ids_recursive(value, current_path)
        
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                current_path = f"{path}[{i}]"
                changes_count += self._convert_court_ids_recursive(item, current_path)
        
        return changes_count
    
    def run(self) -> None:
        """Run the complete conversion process."""
        console.print(Panel.fit(
            "[bold blue]Court ID JSON Conversion[/bold blue]\n"
            f"Data Directory: {DATA_DIR}\n"
            f"Mode: {'DRY RUN' if self.dry_run else 'LIVE RUN'}",
            title="Starting JSON Court ID Conversion"
        ))
        
        files_to_process = [
            REMOVAL_CASES_FILE,
            REVIEW_CASES_FILE
        ]
        
        results = []
        for file_path in files_to_process:
            result = self.process_json_file(file_path)
            results.append(result)
        
        # Display results
        self._display_results(results)
    
    def _display_results(self, results: List[Dict[str, Any]]) -> None:
        """Display summary of changes made."""
        console.print("\n[bold green]JSON Court ID Conversion Complete![/bold green]")
        
        # Results table
        table = Table(title="Files Processed")
        table.add_column("File", style="cyan")
        table.add_column("Status", style="yellow")
        table.add_column("Changes", style="green")
        table.add_column("Notes", style="blue")
        
        total_changes = 0
        for result in results:
            status_color = {
                "converted": "[green]Converted",
                "no_changes": "[yellow]No Changes",
                "not_found": "[red]Not Found",
                "error": "[red]Error"
            }.get(result["status"], result["status"])
            
            notes = result.get("error", "") if result["status"] == "error" else ""
            table.add_row(
                result["file"],
                status_color,
                str(result["changes"]),
                notes
            )
            total_changes += result["changes"]
        
        console.print(table)
        
        # Summary
        console.print(f"\n[bold]Summary:[/bold]")
        console.print(f"• Files processed: {len(results)}")
        console.print(f"• Total court_id values converted: {total_changes}")
        console.print(f"• Total changes made: {len(self.changes_made)}")
        
        if self.dry_run:
            console.print("\n[yellow]⚠ This was a DRY RUN - no actual changes were made[/yellow]")
        else:
            console.print(f"\n[green]✓ Backup created in: {BACKUP_DIR}[/green]")


def main():
    parser = argparse.ArgumentParser(description="Convert court_id values to lowercase in JSON files")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be changed without making changes")
    args = parser.parse_args()
    
    converter = CourtIdJsonConverter(dry_run=args.dry_run)
    converter.run()


if __name__ == "__main__":
    main()