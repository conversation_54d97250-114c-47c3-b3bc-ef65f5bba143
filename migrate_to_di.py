#!/usr/bin/env python3
"""
Migrate services to use dependency-injector library.
"""
import os
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import shutil


# Mapping of common types to their provider names
TYPE_TO_PROVIDER = {
    "LoggerProtocol": "logger",
    "logging.Logger": "logger",
    "Dict[str, Any]": "config",
    "Optional[Dict[str, Any]]": "config",
    "WorkflowConfig": "config",
    "PacerRepository": "pacer_repository",
    "FBArchiveRepository": "fb_archive_repository",
    "DistrictCourtsRepository": "district_courts_repository",
    "LawFirmsRepository": "law_firms_repository",
    "FBImageHashRepository": "fb_image_hash_repository",
    "PacerDocketsRepository": "pacer_dockets_repository",
    "S3AsyncStorage": "s3_async_storage",
    "AsyncDynamoDBStorage": "async_dynamodb_storage",
    "aiohttp.ClientSession": "http_session",
    "DeepSeekService": "deepseek_service",
    "MistralService": "mistral_service",
    "AIOrchestrator": "ai_orchestrator",
    "PromptManager": "prompt_manager",
    "TransferHandler": "transfer_handler",
    "MDLProcessor": "mdl_processor",
    "PacerFileManagementService": "pacer_file_management_service",
    "PacerNavigationService": "pacer_navigation_service",
    "PacerServiceFactory": "pacer_service_factory",
    "DataUpdaterService": "html_data_updater_service",
    "CaseParserService": "case_parser_service",
    "ProcessingTracker": "processing_tracker",
    "FacebookSessionManager": "facebook_session_manager",
    "FacebookAPIClient": "facebook_api_client",
    "ImageHandler": "image_handler",
    "BandwidthLogger": "bandwidth_logger",
    "LocalImageQueue": "local_image_queue",
    "AdProcessingService": "ad_processing_service",
    "AdDBService": "ad_db_service",
    "DataValidationService": "data_validation_service",
    "ErrorHandlingService": "error_handling_service",
    "InteractiveService": "interactive_service",
    "WorkflowService": "workflow_service",
    "JobOrchestrationService": "job_orchestration_service",
    "JobRunnerService": "job_runner_service",
    "FailedFirmsManager": "failed_firms_manager",
    "PDFExtractor": "pdf_extractor",
    "GPT4": "openai_client",
    "PacerIgnoreDownloadService": "pacer_ignore_download_service",
    "LitigationClassifier": "litigation_classifier",
    "FileHandler": "file_handler",
    "DocketProcessor": "docket_processor",
}


def backup_file(file_path: Path):
    """Create a backup of the file."""
    backup_path = file_path.with_suffix('.py.bak')
    shutil.copy2(file_path, backup_path)
    return backup_path


def infer_provider_name(param_name: str, param_type: str) -> str:
    """Infer provider name from parameter name and type."""
    # First check if type has a direct mapping
    for type_pattern, provider_name in TYPE_TO_PROVIDER.items():
        if type_pattern in param_type:
            return provider_name
    
    # Then try to infer from parameter name
    if 'logger' in param_name.lower():
        return 'logger'
    elif 'config' in param_name.lower():
        return 'config'
    elif 'repository' in param_name or 'repo' in param_name:
        return param_name
    elif 'service' in param_name:
        return param_name
    elif 'client' in param_name:
        return param_name
    elif 'manager' in param_name:
        return param_name
    elif 'handler' in param_name:
        return param_name
    elif 'processor' in param_name:
        return param_name
    elif 'factory' in param_name:
        return param_name
    else:
        # Default to the parameter name itself
        return param_name


def migrate_service(file_path: Path, class_name: str, params: List[Tuple[str, str]]) -> bool:
    """Migrate a single service file to use dependency-injector."""
    try:
        content = file_path.read_text()
        original_content = content
        
        # Check if already migrated
        if '@inject' in content and 'from dependency_injector.wiring import inject' in content:
            print(f"  ⚠️  {file_path.name} already migrated, skipping")
            return False
        
        # Find the import section
        import_section_end = 0
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip() and not line.startswith(('import ', 'from ', '#', '"""', "'''")) and not line.strip() == '':
                import_section_end = i
                break
        
        # Add dependency_injector import if not present
        di_import = "from dependency_injector.wiring import inject, Provide"
        if di_import not in content:
            # Insert after other imports
            lines.insert(import_section_end, di_import)
            lines.insert(import_section_end + 1, "")
            content = '\n'.join(lines)
        
        # Find the __init__ method
        init_pattern = rf'(\s*)def\s+__init__\s*\((.*?)\)(\s*->.*?)?:'
        init_match = re.search(init_pattern, content, re.MULTILINE | re.DOTALL)
        
        if not init_match:
            print(f"  ❌ Could not find __init__ method in {file_path.name}")
            return False
        
        indent = init_match.group(1)
        old_params = init_match.group(2)
        return_type = init_match.group(3) or ""
        
        # Parse parameters and convert to DI format
        new_params = ["self"]
        param_lines = []
        
        # Extract full parameter list
        param_str = old_params.strip()
        if param_str and param_str != 'self':
            # Split by commas not inside brackets
            params_list = []
            current_param = ""
            bracket_count = 0
            for char in param_str:
                if char in '[{(':
                    bracket_count += 1
                elif char in ']})':
                    bracket_count -= 1
                elif char == ',' and bracket_count == 0:
                    if current_param.strip() and current_param.strip() != 'self':
                        params_list.append(current_param.strip())
                    current_param = ""
                    continue
                current_param += char
            
            if current_param.strip() and current_param.strip() != 'self':
                params_list.append(current_param.strip())
            
            # Process each parameter
            for param in params_list:
                param = param.strip()
                if not param or param == 'self':
                    continue
                
                # Parse parameter: name: type = default
                param_match = re.match(r'(\w+):\s*([^=]+?)(?:\s*=\s*(.+))?$', param)
                if param_match:
                    param_name = param_match.group(1)
                    param_type = param_match.group(2).strip()
                    param_default = param_match.group(3)
                    
                    # Skip special parameters
                    if param_name in ['kwargs', 'args']:
                        param_lines.append(f"{param_name}: {param_type}" + (f" = {param_default}" if param_default else ""))
                        continue
                    
                    # Infer provider name
                    provider_name = infer_provider_name(param_name, param_type)
                    
                    # Create DI parameter
                    param_lines.append(f'{param_name}: {param_type} = Provide["{provider_name}"]')
                else:
                    # Handle simple parameters without type annotation
                    param_match = re.match(r'(\w+)(?:\s*=\s*(.+))?$', param)
                    if param_match:
                        param_name = param_match.group(1)
                        param_default = param_match.group(2)
                        
                        if param_name not in ['self', 'kwargs', 'args']:
                            provider_name = infer_provider_name(param_name, "Any")
                            param_lines.append(f'{param_name} = Provide["{provider_name}"]')
        
        # Build new __init__ signature
        if param_lines:
            # Format with proper indentation
            param_indent = " " * (len(indent) + 17)  # Length of "def __init__("
            new_init = f"{indent}@inject\n"
            new_init += f"{indent}def __init__(self"
            if len(param_lines) == 1:
                new_init += f", {param_lines[0]}){return_type}:"
            else:
                new_init += ",\n"
                for i, param_line in enumerate(param_lines):
                    if i < len(param_lines) - 1:
                        new_init += f"{param_indent}{param_line},\n"
                    else:
                        new_init += f"{param_indent}{param_line}){return_type}:"
        else:
            new_init = f"{indent}@inject\n{indent}def __init__(self){return_type}:"
        
        # Replace old __init__ with new one
        old_init_pattern = rf'{indent}def\s+__init__\s*\([^)]*\)(\s*->.*?)?:'
        content = re.sub(old_init_pattern, new_init, content, count=1, flags=re.MULTILINE | re.DOTALL)
        
        # Write the migrated content
        file_path.write_text(content)
        
        print(f"  ✅ Migrated {file_path.name}")
        return True
        
    except Exception as e:
        print(f"  ❌ Error migrating {file_path.name}: {e}")
        return False


def migrate_module(module_path: Path, services: List[Tuple[Path, str, List[Tuple[str, str]]]]) -> int:
    """Migrate all services in a module."""
    migrated_count = 0
    
    for file_path, class_name, params in services:
        if migrate_service(file_path, class_name, params):
            migrated_count += 1
    
    return migrated_count


def main():
    """Main migration function."""
    base_path = Path("/Users/<USER>/PycharmProjects/lexgenius/worktrees/task-create-system")
    
    # First analyze what needs migration
    from analyze_di_migration import analyze_services
    results = analyze_services(base_path)
    
    # Group services by module that need migration
    by_module = {}
    for path, class_name, params in results['needs_migration']:
        module = path.parent.name
        if module not in by_module:
            by_module[module] = []
        by_module[module].append((path, class_name, params))
    
    print("\n=== Starting Dependency Injection Migration ===\n")
    
    total_migrated = 0
    
    # Migrate each module
    for module_name in sorted(by_module.keys()):
        services = by_module[module_name]
        print(f"\n📁 Migrating {module_name}/ ({len(services)} services):")
        
        migrated = migrate_module(base_path / "src" / "services" / module_name, services)
        total_migrated += migrated
        
        print(f"  Module summary: {migrated}/{len(services)} migrated")
    
    print(f"\n=== Migration Complete ===")
    print(f"Total services migrated: {total_migrated}")
    print(f"Already migrated: {len(results['already_migrated'])}")
    print(f"Total services with DI: {total_migrated + len(results['already_migrated'])}")


if __name__ == "__main__":
    main()