#!/bin/bash

# Cleanup S3 versions for July 1-28, 2025
# Usage: ./cleanup_s3_versions_july.sh

BUCKET="lexgenius-dockets"
KEEP_LATEST=1

echo "🗑️  Starting S3 version cleanup for July 1-28, 2025..."
echo "📦 Bucket: $BUCKET"
echo "📋 Keep Latest: $KEEP_LATEST versions per object"
echo ""

# Ensure we're in the correct conda environment
if [[ "$CONDA_DEFAULT_ENV" != "lexgenius" ]]; then
    echo "⚠️  Warning: Not in lexgenius conda environment. Activating..."
    conda activate lexgenius
fi

# Array of dates from July 1 to July 28, 2025
dates=(
    "20250701" "20250702" "20250703" "20250704" "20250705" "20250706" "20250707"
    "20250708" "20250709" "20250710" "20250711" "20250712" "20250713" "20250714"
    "20250715" "20250716" "20250717" "20250718" "20250719" "20250720" "20250721"
    "20250722" "20250723" "20250724" "20250725" "20250726" "20250727" "20250728"
)

total_deleted=0
total_kept=0
total_objects=0

for date in "${dates[@]}"; do
    echo "📅 Processing date: $date"
    
    # Run the cleanup command and capture output
    result=$(python scripts/utils/s3_version_manager.py \
        --bucket "$BUCKET" \
        --action bulk-cleanup \
        --prefix "${date}/" \
        --keep-latest "$KEEP_LATEST" 2>&1)
    
    if [[ $? -eq 0 ]]; then
        echo "✅ $date completed successfully"
        
        # Extract statistics from output (basic parsing)
        deleted=$(echo "$result" | grep -o "Total versions deleted: [0-9]*" | grep -o "[0-9]*" || echo "0")
        kept=$(echo "$result" | grep -o "Total versions kept: [0-9]*" | grep -o "[0-9]*" || echo "0")
        objects=$(echo "$result" | grep -o "Objects processed: [0-9]*" | grep -o "[0-9]*" || echo "0")
        
        total_deleted=$((total_deleted + deleted))
        total_kept=$((total_kept + kept))
        total_objects=$((total_objects + objects))
        
        echo "   📊 Objects: $objects, Deleted: $deleted, Kept: $kept"
    else
        echo "❌ $date failed:"
        echo "$result"
    fi
    
    echo ""
    
    # Small delay to avoid overwhelming S3
    sleep 1
done

echo "🎉 Cleanup Complete!"
echo "📈 Summary:"
echo "   📦 Total Objects Processed: $total_objects"
echo "   🗑️  Total Versions Deleted: $total_deleted"
echo "   💾 Total Versions Kept: $total_kept"
echo ""
echo "💡 To verify results, you can run:"
echo "   aws s3api list-object-versions --bucket $BUCKET --prefix 20250701/ --query 'length(Versions)'"