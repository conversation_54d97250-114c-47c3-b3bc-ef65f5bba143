#!/usr/bin/env python3
"""
Analyze dependency injection structure for reports services troubleshooting.
"""
from src.utils.troubleshooting_logger import create_troubleshooting_logger
import os

def analyze_di_structure():
    """Analyze DI structure and log findings."""
    # Initialize logger for dependency injection analysis
    logger = create_troubleshooting_logger('reports_service.log', 'di_analysis_phase')

    logger.log_phase('DEPENDENCY INJECTION ANALYSIS', 'Examining DI patterns and container configurations')

    # Search for DI-related files
    with logger.log_operation('Searching for DI container and provider files'):
        # Check for containers directory
        containers_dir = 'src/containers'
        if os.path.exists(containers_dir):
            logger.log_finding('ARCHITECTURE', 'Found containers directory', {'path': containers_dir})
            
            # List container files
            container_files = []
            for file in os.listdir(containers_dir):
                if file.endswith('.py') and file != '__init__.py':
                    container_files.append(file)
            
            logger.log_analysis('CONTAINERS', f'Found {len(container_files)} container files', {'files': container_files})
        else:
            logger.log_finding('MISSING', 'No containers directory found', {'expected_path': containers_dir})

        # Check for providers
        providers_found = []
        for root, dirs, files in os.walk('src'):
            for file in files:
                if 'provider' in file.lower() and file.endswith('.py'):
                    providers_found.append(os.path.join(root, file))

        if providers_found:
            logger.log_analysis('PROVIDERS', f'Found {len(providers_found)} provider files', {'files': providers_found})
        else:
            logger.log_finding('MISSING', 'No provider files found in src directory')

    # Analyze reports services DI patterns
    with logger.log_operation('Analyzing reports services DI patterns'):
        reports_dir = 'src/services/reports'
        if os.path.exists(reports_dir):
            services = []
            for file in os.listdir(reports_dir):
                if file.endswith('.py') and file != '__init__.py':
                    services.append(file)
            
            logger.log_analysis('REPORTS_SERVICES', f'Found {len(services)} service files', {'services': services})
            
            # Check each service for DI patterns
            mixed_patterns = []
            for service_file in services:
                service_path = os.path.join(reports_dir, service_file)
                with open(service_path, 'r') as f:
                    content = f.read()
                    
                has_inject_decorator = '@inject' in content
                has_constructor_injection = 'def __init__(self,' in content and ': ' in content
                has_get_dependency = 'get_dependency(' in content
                
                pattern_info = {
                    'file': service_file,
                    'inject_decorator': has_inject_decorator,
                    'constructor_injection': has_constructor_injection,
                    'get_dependency': has_get_dependency
                }
                
                if has_get_dependency and has_constructor_injection:
                    mixed_patterns.append(pattern_info)
                
                logger.log_debug(f'DI pattern analysis for {service_file}', pattern_info)
            
            if mixed_patterns:
                logger.log_finding('INCONSISTENCY', 'Mixed DI patterns detected', {
                    'count': len(mixed_patterns),
                    'services': [p['file'] for p in mixed_patterns],
                    'issue': 'Services use both constructor injection and get_dependency()'
                })

    logger.log_success('DI analysis phase completed', {'next': 'Apply Five Whys analysis'})

if __name__ == '__main__':
    analyze_di_structure()