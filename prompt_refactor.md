### **Preamble: Instructions for You (The Human)**

1.  **Start a new, clean session** with your agentic coder.
2.  **Use the "Master Context Prompt" first.** This is the most important step. It provides the agent with the entire implementation plan, which it MUST be able to reference for all subsequent prompts.
3.  **Send each of the following prompts (1 through 8) one at a time.** Do not combine them.
4.  After the agent completes a prompt, you can give it the next one. The instructions are designed to build upon each other logically, even if the context is reset.

---

### **Master Context Prompt (Send This First)**

**Goal:** Provide the agent with the complete context and implementation plan for all the tasks we are about to do.

**Prompt to send to the agent:**

"Hello. We are going to perform a series of updates to a Python codebase in the `src/pacer/` directory. The goal is to update the existing architecture to perform the exact workflow from a legacy specification without creating any new files or services.

I am going to provide you with the full implementation plan document below. You must use this document as the single source of truth for all subsequent tasks. I will give you instructions one file at a time. Please refer back to this plan for details like method names, logic, data structures, and file paths.

After you have read and understood this plan, please reply with "Ready to begin."

Here is the implementation plan:

**[PASTE THE ENTIRE `Legacy PACER New Architecture Implementation Plan` DOCUMENT FROM YOUR ORIGINAL PROMPT HERE]**"

---

### **Prompt 1: Core Orchestrator**

**Goal:** Update the main orchestrator file. This establishes the high-level workflow.

**Prompt to send to the agent:**

"Great. Let's begin. Our first task is to update the core orchestrator.

**File to Update:** `src/pacer/facades/docket_orchestrator.py`

**Instructions:**
Modify the `DocketOrchestrator` class in this file according to Section 2.1 of the plan.

1.  **ADD a new public method `process_courts`**. This is the new entry point for the legacy workflow. Its logic must implement Steps 2.1 and 2.2 from the plan (loading config, initializing the browser) and then loop through court IDs, calling the existing `process_single_court` method for each.
2.  **ADD a new private method `_run_court_report_generation_workflow`**. This method will contain the high-level logic for the scraping workflow (Steps 2.3 - 2.9 from the plan). It will orchestrate calls to other services like authentication, navigation, and reporting.
3.  **ADD a new private method `_process_court_with_docket_log`**. This method will implement the legacy workflow for processing from a pre-downloaded docket list.
4.  **UPDATE the existing `process_single_court()` method**. Modify it to call `_run_court_report_generation_workflow` to execute the legacy scraping flow.
5.  **UPDATE the existing `process_dockets_for_court()` method**. Modify its logic to ensure the data dictionaries it processes and returns conform to the `legacy_final_data` structure described in Section 3.1 of the plan.

Please provide the complete, updated content for the file `src/pacer/facades/docket_orchestrator.py`."

---

### **Prompt 2: Authentication Components**

**Goal:** Update the authentication handler with the precise selectors and logic from the legacy spec.

**Prompt to send to the agent:**

"Next, we will update the authentication component.

**File to Update:** `src/pacer/components/authentication/ecf_login_handler.py`

**Instructions:**
Modify the methods in this file to match the exact behavior described in Section 2.2 of the plan.

1.  **UPDATE `_perform_main_pacer_login`**: It must navigate to `https://pacer.login.uscourts.gov/csologin/login.jsf`, use selectors `#loginForm\:loginName`, `#loginForm\:password`, and `#loginForm\:fbtnLogin`, and log "Main PACER login successful."
2.  **UPDATE `navigate_to_court_ecf`**: It must navigate to `https://pacer.uscourts.gov/file-case/court-cmecf-lookup`, find and click the anchor `href` containing `ecf.{court_id}.uscourts.gov`, and log the success message with the URL.
3.  **UPDATE `login_to_court_ecf`**: It must click the "Document Filing System" link, handle the client code prompt for 'flnd' by filling "007", and log "Court ECF login successful."

Please provide the complete, updated content for the file `src/pacer/components/authentication/ecf_login_handler.py`."

---

### **Prompt 3: Navigation Facade**

**Goal:** Update the navigation facade with the precise actions from the legacy spec.

**Prompt to send to the agent:**

"Now, let's update the navigation facade.

**File to Update:** `src/pacer/facades/navigation_facade.py`

**Instructions:**
Modify the methods in this file to match the exact behavior described in Section 2.3 of the plan.

1.  **UPDATE `navigate_to_query_page`**: It must find and click the anchor with the exact text "Query" and perform the specified logging before and after the click.
2.  **UPDATE `query_docket_and_get_page`**: It must perform the exact sequence of actions for finding a case: fill `input[name='case_num']`, trigger a blur event, click "Find This Case", then "Run Query", then "Docket Report".

Please provide the complete, updated content for the file `src/pacer/facades/navigation_facade.py`."

---

### **Prompt 4: Report Facade & Data Structure**

**Goal:** Update the reporting facade to handle form filling and, crucially, to generate the initial legacy data structure.

**Prompt to send to the agent:**

"Next is the report facade. This is a critical step for data structure compliance.

**File to Update:** `src/pacer/facades/report_facade.py`

**Instructions:**
Modify the methods in this file to match the behavior in Section 2.4 and the data structure in Section 3.1 of the plan.

1.  **UPDATE `configure_case_filed_report`**: It must perform the exact navigation and form filling sequence: click "Reports", then "Civil Cases", fill date fields, select from dropdowns, and click "Run Report".
2.  **UPDATE `_extract_and_save_report_list`** (and any helper methods it uses, like `_extract_single_row_data`):
    *   It must parse the HTML table to produce a dictionary for each row that EXACTLY matches the `legacy_initial_data` structure shown in Section 3.1 of the plan. All specified fields must be present.
    *   It must save the resulting JSON file to the EXACT legacy path: `data/{iso_date}/logs/docket_report/{court_id}.json`.

Please provide the complete, updated content for the file `src/pacer/facades/report_facade.py`."

---

### **Prompt 5: Download Orchestration & File Paths**

**Goal:** Update the download service to use the specific legacy download and file-naming logic.

**Prompt to send to the agent:**

"Now let's update the download orchestration service.

**File to Update:** `src/pacer/core/download_orchestration/download_orchestration_service.py`

**Instructions:**
Modify the `process_download_workflow` method to match the exact behavior described in Section 2.5 of the plan.

1.  The workflow must check `ignore_download_config.json` to decide if a download should be skipped.
2.  The PDF download logic must uncheck all document checkboxes except the first one before clicking the download button.
3.  The file must first be saved to a temporary directory with the path structure: `data/{iso_date}/dockets/temp/{court_id}_..._{uuid}/`.
4.  The file must then be moved to its final destination with the exact naming convention: `data/{iso_date}/dockets/{court_id}_{year}_{case_num}_{versus}.pdf`.

Please provide the complete, updated content for the file `src/pacer/core/download_orchestration/download_orchestration_service.py`."

---

### **Prompt 6: Data Enrichment and File Operations**

**Goal:** Ensure other components enrich the data correctly and that the central file service uses the correct paths. This is a conceptual prompt that might affect multiple files, but we'll target the main ones.

**Prompt to send to the agent:**

"We need to ensure our data structures and file paths are correct across the application.

**Task 1: Data Enrichment**
Review your work on the case processing components (like those in `src/pacer/components/case_processing/`) and the `DocketOrchestrator`. Ensure that after the full docket sheet is parsed, the data object is enriched to match the `legacy_enriched_data` structure from Section 3.1 of the plan (adding fields like `assigned_to`, `plaintiffs`, etc.). Confirm this logic is in place.

**Task 2: File Paths**
The central file service must use the legacy paths.

**File to Update:** `src/pacer/services/file_operations_service.py`
**Instructions:**
Modify any methods in this file that write files to use the exact legacy path structures defined in Section 3.2 of the plan.
*   Case JSON: `data/{iso_date}/dockets/{base_filename}.json`
*   PDFs: `data/{iso_date}/dockets/{court_id}_{year}_{case_num}_{versus}.pdf`

Please provide the complete, updated content for the file `src/pacer/services/file_operations_service.py`."

---

### **Prompt 7: Service Factory & Dependency Injection**

**Goal:** Update the dependency injection container to correctly wire the components under their legacy interface names.

**Prompt to send to the agent:**

"Now we need to update the service factory to wire our updated components correctly.

**File to Update:** `src/pacer/factories/service_factory.py`

**Instructions:**
Modify the `PacerCoreContainer` and `PacerServiceFactory` classes as described in Section 1.2 of the plan.

1.  In `PacerCoreContainer`, **ADD** a `providers.Singleton` for `pacer_orchestrator_service` that maps to the `DocketOrchestrator` class.
2.  In `PacerCoreContainer`, **UPDATE** the existing `pacer_court_processing_service` provider to ensure its dependencies (`docket_orchestrator`, `navigation_service`, etc.) are wired to the correct facades and services as specified in the plan.
3.  In `PacerServiceFactory`, **ADD** the accessor method `get_pacer_orchestrator_service` to return the new provider instance.

Please provide the complete, updated content for the file `src/pacer/factories/service_factory.py`."

---

### **Prompt 8: Final Entry Point Integration**

**Goal:** Make the final, minimal change to the main entry point to use the new service interface.

**Prompt to send to the agent:**

"This is the final task. We will update the main entry point to use the new service interface we just configured.

**File to Update:** `src/main.py`

**Instructions:**
Make the minimal change described in Section 4.1 of the plan.

1.  Locate the `MainOrchestrator` class and its `run` method.
2.  Change the line that gets the orchestrator instance. It should now call `self.factory.get_pacer_orchestrator_service()`.
3.  The subsequent method call (`pacer_orchestrator.process_courts(...)`) and its arguments must remain exactly the same.

Please provide the complete, updated content for the file `src/main.py`."