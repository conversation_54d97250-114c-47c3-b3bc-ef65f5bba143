# Facebook Ads CamoufoxSessionManager Compatibility Fix

## Issue
The error `'CamoufoxSessionManager' object has no attribute 'get'` occurred because the image handler was trying to use `session.get()` which is a method from `requests.Session`, but `CamoufoxSessionManager` uses Playwright browser automation instead of requests.

## Root Cause
The image handler was written to work with `requests.Session` objects, but when using Camoufox browser automation, a different session manager (`CamoufoxSessionManager`) is used that has different methods.

## Fix Applied

### File: `/src/services/fb_ads/image_handler.py` (lines 289-421)

Added logic to detect and handle both session manager types:

```python
# Check if this is a CamoufoxSessionManager (has download_image method)
if hasattr(session, 'download_image') and callable(getattr(session, 'download_image')):
    # Use Camoufox's download_image method
    image_content = await session.download_image(image_url, timeout=timeout)
    if image_content:
        image_content_buffer = BytesIO(image_content)
        content_length = len(image_content)

elif hasattr(session, 'get'):
    # Standard requests.Session - use the get method
    response = session.get(image_url, timeout=timeout, stream=True, headers=headers, proxies=proxies)
    # ... process response ...

else:
    self.log_error(f"Session object has neither 'get' nor 'download_image' method")
    return None, s3_exists_result
```

## How It Works

1. **Detection**: The code checks if the session object has a `download_image` method (CamoufoxSessionManager) or a `get` method (requests.Session)

2. **CamoufoxSessionManager Path**:
   - Uses the async `download_image` method
   - Returns image content directly as bytes
   - Handles async/sync context appropriately

3. **requests.Session Path**:
   - Uses the traditional `get` method with proxy settings
   - Processes response headers and content
   - Handles blocks and rate limits

4. **Common Processing**:
   - Both paths converge to save the image locally
   - Upload to S3
   - Calculate PHash

## Benefits

- ✅ Supports both traditional requests-based sessions and Camoufox browser automation
- ✅ Maintains backward compatibility with existing code
- ✅ Properly handles async methods in sync contexts
- ✅ Clear error messages when unsupported session types are encountered

## Testing
The system now correctly handles image downloads whether using:
- Traditional FacebookSessionManager (requests-based)
- CamoufoxSessionManager (Playwright-based)
- Any future session manager that implements either interface