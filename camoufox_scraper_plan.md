# Camoufox Web Scraper Implementation Plan

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [Architecture Overview](#architecture-overview)
3. [Anti-Bot Features](#anti-bot-features)
4. [Session Management](#session-management)
5. [Service Implementations](#service-implementations)
6. [Proxy Provider Abstraction](#proxy-provider-abstraction)
7. [Configuration](#configuration)
8. [Testing Strategy](#testing-strategy)
9. [Migration Plan](#migration-plan)
10. [Implementation Timeline](#implementation-timeline)

## Executive Summary

This document outlines the implementation plan for a Camoufox-based web scraper to replace the current Facebook ads scraping system. The new system will provide:

- **Superior Anti-Detection**: Browser-level fingerprinting with Camoufox's advanced features
- **Session Management**: 3-5 minute session lifecycles with automatic rotation
- **Proxy Flexibility**: Easy switching between proxy providers (starting with Oxylabs)
- **JavaScript Support**: Full browser rendering capabilities
- **LexGenius Integration**: Seamless integration with existing dependency injection patterns

## Architecture Overview

### Directory Structure
```
src/services/scraping/
├── __init__.py
├── browser/
│   ├── __init__.py
│   ├── camoufox_service.py          # Main browser management
│   ├── fingerprint_manager.py       # Device fingerprint rotation
│   ├── context_manager.py           # Browser context lifecycle
│   └── humanization_service.py      # Human-like behavior patterns
├── proxy/
│   ├── __init__.py
│   ├── proxy_provider_base.py       # Abstract base for providers
│   ├── oxylabs_provider.py          # Oxylabs implementation
│   ├── proxy_manager.py             # Proxy rotation & health tracking
│   └── proxy_models.py              # Configuration models
├── session/
│   ├── __init__.py
│   ├── session_manager.py           # 3-5 minute session lifecycle
│   ├── cookie_manager.py            # Cookie persistence
│   ├── token_extractor.py           # FB token extraction
│   └── session_models.py            # Session data models
├── navigation/
│   ├── __init__.py
│   ├── page_navigator.py           # Smart navigation with retries
│   ├── element_handler.py          # Safe element interactions
│   ├── wait_strategies.py          # Intelligent waiting
│   └── screenshot_service.py       # Debug screenshots
├── anti_bot/
│   ├── __init__.py
│   ├── behavior_patterns.py        # Human-like patterns
│   ├── timing_randomizer.py        # Realistic delays
│   ├── interaction_humanizer.py    # Mouse/keyboard humanization
│   └── fingerprint_verification.py # Fingerprint testing & verification
└── scrapers/
    ├── __init__.py
    ├── base_scraper.py             # Abstract scraper with lifecycle
    ├── facebook_ads_scraper.py     # FB-specific implementation
    └── scraper_models.py           # Data models
```

### Core Components

1. **Browser Service**: Manages Camoufox browser instances
2. **Session Manager**: Enforces 3-5 minute session limits
3. **Proxy Manager**: Handles proxy rotation and health
4. **Anti-Bot Services**: Implements human-like behaviors
5. **Navigation Services**: Smart page interactions
6. **Scraper Implementation**: Business logic for data extraction

## Anti-Bot Features

### 1. Camoufox Native Features
```python
# Browser configuration with full anti-bot features
browser_config = {
    # Device fingerprinting
    'config': {
        'navigator.userAgent': None,  # Auto-generated
        'navigator.platform': None,   # Auto-generated
        'navigator.language': None,   # Based on geolocation
        'navigator.languages': None,  # Based on geolocation
        'navigator.hardwareConcurrency': None,  # Realistic CPU cores
        'screen.width': None,         # Realistic screen size
        'screen.height': None,        # Realistic screen size
        'window.innerWidth': None,    # Viewport dimensions
        'window.innerHeight': None,   # Viewport dimensions
    },
    
    # Geolocation spoofing (based on proxy location)
    'geoip': True,  # Enable automatic geolocation
    
    # Human-like cursor movements
    'humanize': True,
    
    # Font spoofing
    'fonts': ['windows', 'mac', 'linux'],  # OS-specific fonts
    
    # WebGL spoofing
    'webgl_vendor': None,  # Auto-generated
    'webgl_renderer': None,  # Auto-generated
    
    # WebRTC spoofing
    'webrtc': {
        'mode': 'fake',  # Fake WebRTC data
        'fill_based_on_ip': True  # Match proxy location
    },
    
    # Browser addons to mimic real users
    'addons': [
        'default',  # Common browser extensions
    ],
    
    # Screen properties
    'screen': {
        'max_touch_points': 0,  # Desktop browser
        'color_depth': 24,
        'pixel_depth': 24,
    }
}
```

### 2. Additional Anti-Bot Measures

#### A. Human-like Timing
```python
class TimingRandomizer:
    """Generates realistic human-like delays."""
    
    @staticmethod
    def typing_delay() -> float:
        """Delay between keystrokes (50-150ms)."""
        return random.uniform(0.05, 0.15)
    
    @staticmethod
    def reading_delay(text_length: int) -> float:
        """Delay based on text length (200ms per 10 chars)."""
        base_time = text_length / 10 * 0.2
        return base_time * random.uniform(0.8, 1.2)
    
    @staticmethod
    def action_delay() -> float:
        """Delay between actions (0.5-2s)."""
        return random.uniform(0.5, 2.0)
    
    @staticmethod
    def page_scan_delay() -> float:
        """Time to 'scan' a page (2-5s)."""
        return random.uniform(2.0, 5.0)
```

#### B. Mouse Movement Patterns
```python
class MouseHumanizer:
    """Implements human-like mouse movements."""
    
    async def move_to_element(self, page, element):
        """Move mouse to element with human-like curve."""
        # Get element position
        box = await element.bounding_box()
        if not box:
            return
            
        # Target random point within element
        target_x = box['x'] + random.uniform(5, box['width'] - 5)
        target_y = box['y'] + random.uniform(5, box['height'] - 5)
        
        # Move with bezier curve
        await self._bezier_move(page, target_x, target_y)
        
    async def _bezier_move(self, page, end_x, end_y):
        """Move mouse along bezier curve."""
        # Current position
        current = await page.evaluate("() => ({x: window.mouseX || 0, y: window.mouseY || 0})")
        
        # Generate control points for curve
        cp1_x = current['x'] + (end_x - current['x']) * 0.25 + random.uniform(-50, 50)
        cp1_y = current['y'] + (end_y - current['y']) * 0.25 + random.uniform(-50, 50)
        cp2_x = current['x'] + (end_x - current['x']) * 0.75 + random.uniform(-50, 50)
        cp2_y = current['y'] + (end_y - current['y']) * 0.75 + random.uniform(-50, 50)
        
        # Move along curve with multiple steps
        steps = random.randint(10, 20)
        for i in range(steps):
            t = i / steps
            # Bezier formula
            x = (1-t)**3 * current['x'] + 3*(1-t)**2*t * cp1_x + 3*(1-t)*t**2 * cp2_x + t**3 * end_x
            y = (1-t)**3 * current['y'] + 3*(1-t)**2*t * cp1_y + 3*(1-t)*t**2 * cp2_y + t**3 * end_y
            
            await page.mouse.move(x, y)
            await asyncio.sleep(random.uniform(0.01, 0.03))
```

#### C. Scroll Patterns
```python
class ScrollHumanizer:
    """Implements human-like scrolling."""
    
    async def natural_scroll(self, page, direction='down', distance=None):
        """Scroll page naturally."""
        if distance is None:
            distance = random.randint(300, 700)
            
        # Smooth scroll with variable speed
        steps = random.randint(10, 20)
        step_size = distance / steps
        
        for i in range(steps):
            # Variable speed (slow-fast-slow)
            speed_factor = 1 - abs(i - steps/2) / (steps/2)
            current_step = step_size * (0.5 + speed_factor)
            
            if direction == 'down':
                await page.mouse.wheel(0, current_step)
            else:
                await page.mouse.wheel(0, -current_step)
                
            await asyncio.sleep(random.uniform(0.01, 0.05))
        
        # Sometimes overshoot and correct
        if random.random() < 0.3:
            overshoot = random.uniform(50, 100)
            await page.mouse.wheel(0, overshoot if direction == 'down' else -overshoot)
            await asyncio.sleep(random.uniform(0.1, 0.3))
            await page.mouse.wheel(0, -overshoot if direction == 'down' else overshoot)
```

## Session Management

### 3-5 Minute Session Lifecycle

```python
class SessionLifecycleManager(AsyncServiceBase):
    """Manages browser sessions with strict time limits."""
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        super().__init__(logger, config)
        self.min_session_duration = 3 * 60  # 3 minutes
        self.max_session_duration = 5 * 60  # 5 minutes
        self.session_start_time: Optional[float] = None
        self.session_end_time: Optional[float] = None
        self.current_task: Optional[str] = None
        
    async def start_session(self, task_id: str) -> SessionContext:
        """Start a new session with time tracking."""
        self.session_start_time = time.time()
        # Random session duration between 3-5 minutes
        session_duration = random.uniform(self.min_session_duration, self.max_session_duration)
        self.session_end_time = self.session_start_time + session_duration
        self.current_task = task_id
        
        self.log_info(f"Started session for task {task_id}, duration: {session_duration:.1f}s")
        
        return SessionContext(
            session_id=str(uuid.uuid4()),
            start_time=self.session_start_time,
            max_end_time=self.session_end_time,
            task_id=task_id
        )
    
    async def check_session_expiry(self) -> bool:
        """Check if current session should end."""
        if not self.session_start_time:
            return True
            
        current_time = time.time()
        
        # Force end if exceeding max time
        if current_time >= self.session_end_time:
            self.log_warning(f"Session expired for task {self.current_task}")
            return True
            
        # Check if we're in the final 30 seconds
        time_remaining = self.session_end_time - current_time
        if time_remaining <= 30:
            self.log_warning(f"Session ending soon: {time_remaining:.1f}s remaining")
            
        return False
    
    async def end_session(self, reason: str = "normal"):
        """End current session and cleanup."""
        if self.session_start_time:
            duration = time.time() - self.session_start_time
            self.log_info(f"Ended session for task {self.current_task} after {duration:.1f}s - {reason}")
            
        self.session_start_time = None
        self.session_end_time = None
        self.current_task = None
```

### Session-Aware Scraper

```python
class SessionAwareFacebookScraper(AsyncServiceBase):
    """Facebook scraper with session management."""
    
    def __init__(self, browser_service: CamoufoxService,
                 session_manager: SessionLifecycleManager,
                 proxy_manager: ProxyManager):
        self.browser_service = browser_service
        self.session_manager = session_manager
        self.proxy_manager = proxy_manager
        
    async def scrape_company_ads(self, company_id: str) -> List[Dict]:
        """Scrape ads with session lifecycle management."""
        # Start new session
        session = await self.session_manager.start_session(f"company_{company_id}")
        
        # Get fresh proxy for session
        proxy = await self.proxy_manager.get_next_proxy()
        
        # Create browser with session-specific config
        browser = await self.browser_service.create_browser(
            proxy=proxy,
            session_id=session.session_id
        )
        
        try:
            page = await browser.new_page()
            ads_data = []
            
            # Navigate to ads library
            await self._navigate_to_ads_library(page, company_id)
            
            # Paginate through results
            while True:
                # Check session expiry before each page
                if await self.session_manager.check_session_expiry():
                    self.log_warning("Session expiring, saving progress and ending")
                    break
                
                # Extract ads from current page
                page_ads = await self._extract_page_ads(page)
                ads_data.extend(page_ads)
                
                # Check for next page
                has_next = await self._has_next_page(page)
                if not has_next:
                    self.log_info(f"Completed scraping {len(ads_data)} ads")
                    break
                
                # Navigate to next page with human-like delay
                await self._navigate_next_page(page)
                
            return ads_data
            
        finally:
            # Always end session properly
            await browser.close()
            await self.session_manager.end_session("scrape_complete")
            
            # Mark proxy as successful
            await self.proxy_manager.mark_proxy_success(proxy)
```

## Service Implementations

### 1. CamoufoxService

```python
from camoufox.sync_api import Camoufox
import asyncio
from typing import Dict, Any, Optional, List
import logging

class CamoufoxService(AsyncServiceBase):
    """Manages Camoufox browser instances with anti-bot features."""
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger,
                 fingerprint_manager: FingerprintManager,
                 proxy_manager: ProxyManager):
        super().__init__(logger, config)
        self.fingerprint_manager = fingerprint_manager
        self.proxy_manager = proxy_manager
        self.active_browsers: Dict[str, Any] = {}
        self.max_concurrent_browsers = config.get('max_concurrent_browsers', 3)
        
    async def create_browser(self, proxy: Optional[ProxyConfig] = None,
                           session_id: Optional[str] = None) -> Any:
        """Create a new Camoufox browser instance."""
        # Check browser limit
        if len(self.active_browsers) >= self.max_concurrent_browsers:
            self.log_warning("Browser limit reached, waiting for available slot")
            await self._wait_for_browser_slot()
        
        # Get fingerprint profile
        fingerprint = await self.fingerprint_manager.get_next_fingerprint()
        
        # Build browser config
        browser_config = self._build_browser_config(fingerprint, proxy)
        
        # Launch browser with Camoufox
        browser_id = session_id or str(uuid.uuid4())
        
        try:
            # Use asyncio.to_thread for sync Camoufox API
            browser = await asyncio.to_thread(
                self._launch_camoufox,
                browser_config,
                browser_id
            )
            
            self.active_browsers[browser_id] = browser
            self.log_info(f"Created browser {browser_id} with fingerprint {fingerprint['id']}")
            
            return browser
            
        except Exception as e:
            self.log_error(f"Failed to create browser: {str(e)}")
            raise
    
    def _launch_camoufox(self, config: Dict, browser_id: str) -> Any:
        """Launch Camoufox browser (sync operation)."""
        with Camoufox(
            config=config['fingerprint'],
            humanize=True,  # Enable human-like behavior
            geoip=config.get('geoip', True),  # Auto-detect location
            addons=config.get('addons', ['default']),
            headless=config.get('headless', False)
        ) as browser:
            # Configure proxy if provided
            if config.get('proxy'):
                browser.set_proxy(config['proxy'])
            
            return browser
    
    def _build_browser_config(self, fingerprint: Dict, 
                            proxy: Optional[ProxyConfig]) -> Dict:
        """Build comprehensive browser configuration."""
        config = {
            'fingerprint': fingerprint['config'],
            'headless': self.config.get('headless', False),
            'geoip': True,  # Auto-detect based on proxy
            'humanize': True,  # Human-like interactions
            'addons': ['default'],  # Common extensions
        }
        
        # Add proxy configuration
        if proxy:
            config['proxy'] = {
                'server': f'{proxy.protocol}://{proxy.host}:{proxy.port}',
                'username': proxy.username,
                'password': proxy.password
            }
        
        # Additional anti-bot measures
        config['args'] = [
            '--disable-blink-features=AutomationControlled',
            '--disable-dev-shm-usage',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-gpu' if config['headless'] else '',
        ]
        
        return config
    
    async def close_browser(self, browser_id: str):
        """Close and cleanup browser instance."""
        if browser_id in self.active_browsers:
            browser = self.active_browsers[browser_id]
            try:
                await asyncio.to_thread(browser.close)
                del self.active_browsers[browser_id]
                self.log_info(f"Closed browser {browser_id}")
            except Exception as e:
                self.log_error(f"Error closing browser {browser_id}: {str(e)}")
```

### 2. FingerprintManager

```python
class FingerprintManager(AsyncServiceBase):
    """Manages browser fingerprint rotation."""
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        super().__init__(logger, config)
        self.fingerprint_profiles = self._load_fingerprint_profiles()
        self.current_index = 0
        self.rotation_count = 0
        self.rotation_interval = config.get('rotation_interval', 10)
        
    def _load_fingerprint_profiles(self) -> List[Dict]:
        """Load fingerprint profiles from configuration."""
        profiles = []
        
        # Windows Chrome profiles
        for chrome_version in range(115, 125):
            profiles.append({
                'id': f'win_chrome_{chrome_version}',
                'config': {
                    'navigator.platform': 'Win32',
                    'navigator.userAgent': f'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/537.36',
                    'navigator.hardwareConcurrency': random.choice([4, 8, 16]),
                    'screen.width': random.choice([1920, 2560, 3840]),
                    'screen.height': random.choice([1080, 1440, 2160]),
                    'screen.colorDepth': 24,
                    'navigator.language': 'en-US',
                    'navigator.languages': ['en-US', 'en'],
                }
            })
        
        # macOS Chrome profiles
        for chrome_version in range(115, 125):
            profiles.append({
                'id': f'mac_chrome_{chrome_version}',
                'config': {
                    'navigator.platform': 'MacIntel',
                    'navigator.userAgent': f'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version}.0.0.0 Safari/537.36',
                    'navigator.hardwareConcurrency': random.choice([8, 10, 12]),
                    'screen.width': random.choice([2560, 2880, 3024]),
                    'screen.height': random.choice([1600, 1800, 1964]),
                    'screen.colorDepth': 30,
                    'navigator.language': 'en-US',
                    'navigator.languages': ['en-US', 'en'],
                }
            })
        
        random.shuffle(profiles)
        return profiles
    
    async def get_next_fingerprint(self) -> Dict:
        """Get next fingerprint profile."""
        self.rotation_count += 1
        
        # Rotate to next profile based on interval
        if self.rotation_count >= self.rotation_interval:
            self.current_index = (self.current_index + 1) % len(self.fingerprint_profiles)
            self.rotation_count = 0
            self.log_info(f"Rotated to fingerprint profile {self.current_index}")
        
        profile = self.fingerprint_profiles[self.current_index].copy()
        
        # Add some randomization to each use
        profile['config']['window.innerWidth'] = profile['config']['screen.width'] - random.randint(0, 200)
        profile['config']['window.innerHeight'] = profile['config']['screen.height'] - random.randint(100, 300)
        
        return profile
```

### 3. ProxyManager

```python
class ProxyManager(AsyncServiceBase):
    """Manages proxy rotation and health tracking."""
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger,
                 proxy_provider: ProxyProvider):
        super().__init__(logger, config)
        self.proxy_provider = proxy_provider
        self.proxy_health: Dict[str, ProxyHealth] = {}
        self.rotation_strategy = config.get('rotation_strategy', 'round_robin')
        self.current_index = 0
        
    async def get_next_proxy(self) -> ProxyConfig:
        """Get next healthy proxy."""
        max_attempts = 10
        
        for attempt in range(max_attempts):
            if self.rotation_strategy == 'round_robin':
                proxy = await self._get_round_robin_proxy()
            elif self.rotation_strategy == 'least_failed':
                proxy = await self._get_least_failed_proxy()
            else:  # random
                proxy = await self._get_random_proxy()
            
            # Check proxy health
            if self._is_proxy_healthy(proxy):
                self.log_info(f"Selected proxy: {proxy.get_safe_url()}")
                return proxy
            
            self.log_warning(f"Proxy {proxy.get_safe_url()} unhealthy, trying next")
        
        raise Exception("No healthy proxies available")
    
    def _is_proxy_healthy(self, proxy: ProxyConfig) -> bool:
        """Check if proxy is healthy."""
        proxy_id = proxy.get_id()
        
        if proxy_id not in self.proxy_health:
            # New proxy, assume healthy
            self.proxy_health[proxy_id] = ProxyHealth(
                proxy_id=proxy_id,
                success_count=0,
                failure_count=0,
                last_used=None,
                last_failure=None
            )
            return True
        
        health = self.proxy_health[proxy_id]
        
        # Check failure rate
        total_uses = health.success_count + health.failure_count
        if total_uses > 0:
            failure_rate = health.failure_count / total_uses
            if failure_rate > 0.5:  # More than 50% failure rate
                return False
        
        # Check cooldown after failure
        if health.last_failure:
            cooldown_minutes = self.config.get('cooldown_minutes', 5)
            time_since_failure = time.time() - health.last_failure
            if time_since_failure < cooldown_minutes * 60:
                return False
        
        return True
    
    async def mark_proxy_success(self, proxy: ProxyConfig):
        """Mark proxy as successful."""
        proxy_id = proxy.get_id()
        if proxy_id not in self.proxy_health:
            self.proxy_health[proxy_id] = ProxyHealth(proxy_id=proxy_id)
        
        health = self.proxy_health[proxy_id]
        health.success_count += 1
        health.last_used = time.time()
        
        self.log_debug(f"Proxy {proxy.get_safe_url()} marked successful")
    
    async def mark_proxy_failure(self, proxy: ProxyConfig, reason: str):
        """Mark proxy as failed."""
        proxy_id = proxy.get_id()
        if proxy_id not in self.proxy_health:
            self.proxy_health[proxy_id] = ProxyHealth(proxy_id=proxy_id)
        
        health = self.proxy_health[proxy_id]
        health.failure_count += 1
        health.last_failure = time.time()
        health.last_failure_reason = reason
        
        self.log_warning(f"Proxy {proxy.get_safe_url()} marked failed: {reason}")
```

## Proxy Provider Abstraction

### Base Provider

```python
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class ProxyConfig:
    """Proxy configuration."""
    protocol: str  # http/https/socks5
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    session_id: Optional[str] = None
    
    def get_url(self) -> str:
        """Get full proxy URL with auth."""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"
    
    def get_safe_url(self) -> str:
        """Get proxy URL without password for logging."""
        if self.username:
            return f"{self.protocol}://{self.username}:***@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"
    
    def get_id(self) -> str:
        """Get unique proxy identifier."""
        return f"{self.host}:{self.port}:{self.session_id or 'default'}"

class ProxyProvider(ABC):
    """Abstract base class for proxy providers."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the proxy provider."""
        pass
    
    @abstractmethod
    async def get_proxies(self, count: int) -> List[ProxyConfig]:
        """Get a list of proxy configurations."""
        pass
    
    @abstractmethod
    async def refresh_proxies(self) -> None:
        """Refresh the proxy list."""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """Get the provider name."""
        pass
```

### Oxylabs Provider

```python
class OxylabsProvider(ProxyProvider):
    """Oxylabs proxy provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        self.username = config.get('username') or os.environ.get('OXYLABS_USERNAME')
        self.password = config.get('password') or os.environ.get('OXYLABS_PASSWORD')
        self.proxy_type = config.get('proxy_type', 'residential')  # residential/mobile
        self.country = config.get('country', 'us')
        self.session_time = config.get('session_time', 10)  # minutes
        self.base_url = 'pr.oxylabs.io'
        self.port = 7777
        self._validate_credentials()
    
    def _validate_credentials(self):
        """Validate Oxylabs credentials."""
        if not self.username or not self.password:
            raise ValueError("Oxylabs username and password required")
    
    async def initialize(self) -> None:
        """Initialize Oxylabs provider."""
        # Could verify credentials with API call
        pass
    
    async def get_proxies(self, count: int) -> List[ProxyConfig]:
        """Generate Oxylabs proxy configurations."""
        proxies = []
        
        for i in range(count):
            # Generate unique session ID
            session_id = str(random.randint(**********, **********))
            
            # Build customer string with parameters
            customer_string = f"customer-{self.username}-cc-{self.country}"
            customer_string += f"-sessid-{session_id}"
            customer_string += f"-sesstime-{self.session_time}"
            
            proxy = ProxyConfig(
                protocol='http',
                host=self.base_url,
                port=self.port,
                username=customer_string,
                password=self.password,
                session_id=session_id
            )
            
            proxies.append(proxy)
        
        return proxies
    
    async def refresh_proxies(self) -> None:
        """Refresh proxy sessions."""
        # Oxylabs automatically rotates based on session_id
        pass
    
    def get_provider_name(self) -> str:
        """Get provider name."""
        return f"Oxylabs-{self.proxy_type}"
```

## Dependency Injection Container

```python
class ScrapingContainer(containers.DeclarativeContainer):
    """Container for Camoufox scraping services."""
    
    config = providers.Configuration()
    logger = providers.Dependency()
    
    # Proxy providers
    oxylabs_provider = providers.Singleton(
        OxylabsProvider,
        config=config.oxylabs
    )
    
    proxy_manager = providers.Singleton(
        ProxyManager,
        config=config.proxy,
        logger=logger,
        proxy_provider=oxylabs_provider
    )
    
    # Fingerprint services
    fingerprint_manager = providers.Singleton(
        FingerprintManager,
        config=config.fingerprints,
        logger=logger
    )
    
    fingerprint_verifier = providers.Singleton(
        FingerprintVerificationService,
        config=config.fingerprints.verification,
        logger=logger
    )
    
    # Browser services
    camoufox_service = providers.Singleton(
        CamoufoxService,
        config=config.browser,
        logger=logger,
        fingerprint_manager=fingerprint_manager,
        fingerprint_verifier=fingerprint_verifier,
        proxy_manager=proxy_manager
    )
    
    # Session management
    session_lifecycle_manager = providers.Singleton(
        SessionLifecycleManager,
        config=config.session,
        logger=logger
    )
    
    session_manager = providers.Singleton(
        CamoufoxSessionManager,
        config=config.session,
        logger=logger
    )
    
    # Anti-bot services
    timing_randomizer = providers.Singleton(
        TimingRandomizer,
        config=config.anti_bot,
        logger=logger
    )
    
    mouse_humanizer = providers.Singleton(
        MouseHumanizer,
        config=config.anti_bot.mouse_movement,
        logger=logger
    )
    
    scroll_humanizer = providers.Singleton(
        ScrollHumanizer,
        config=config.anti_bot.scrolling,
        logger=logger
    )
    
    # Navigation
    page_navigator = providers.Singleton(
        PageNavigator,
        config=config.navigation,
        logger=logger,
        timing_randomizer=timing_randomizer,
        mouse_humanizer=mouse_humanizer,
        scroll_humanizer=scroll_humanizer
    )
    
    # Scrapers
    facebook_ads_scraper = providers.Factory(
        SessionAwareFacebookScraper,
        browser_service=camoufox_service,
        session_manager=session_lifecycle_manager,
        proxy_manager=proxy_manager,
        navigator=page_navigator,
        logger=logger
    )
```

## Fingerprint Verification

### FingerprintVerificationService

```python
import json
from typing import Dict, Any, List
from dataclasses import dataclass
from enum import Enum

class FingerprintDomain(Enum):
    """Fingerprint test domains."""
    NAVIGATOR = "navigator"
    SCREEN = "screen"
    WEBGL = "webgl"
    WEBRTC = "webrtc"
    FONTS = "fonts"
    HEADERS = "headers"
    GEOLOCATION = "geolocation"
    AUDIO = "audio"
    CANVAS = "canvas"
    PLUGINS = "plugins"
    TIMEZONE = "timezone"
    LANGUAGE = "language"

@dataclass
class FingerprintTest:
    """Individual fingerprint test result."""
    domain: FingerprintDomain
    property: str
    expected: Any
    actual: Any
    passed: bool
    message: str

@dataclass
class FingerprintReport:
    """Complete fingerprint verification report."""
    browser_id: str
    timestamp: float
    overall_passed: bool
    uniqueness_score: float  # 0-100
    consistency_score: float  # 0-100
    tests: List[FingerprintTest]
    recommendations: List[str]

class FingerprintVerificationService(AsyncServiceBase):
    """Verifies browser fingerprints against detection tests."""
    
    VERIFICATION_URL = "https://camoufox.com/fingerprint/"
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        super().__init__(logger, config)
        self.verification_history: List[FingerprintReport] = []
        self.baseline_fingerprints: Dict[str, Any] = {}
        
    async def verify_browser_fingerprint(self, browser: Any, 
                                       browser_id: str,
                                       expected_config: Dict[str, Any]) -> FingerprintReport:
        """Run comprehensive fingerprint verification."""
        self.log_info(f"Starting fingerprint verification for browser {browser_id}")
        
        page = await browser.new_page()
        try:
            # Navigate to verification page
            await page.goto(self.VERIFICATION_URL, wait_until='networkidle')
            await asyncio.sleep(2)  # Let all tests complete
            
            # Extract fingerprint data
            fingerprint_data = await self._extract_fingerprint_data(page)
            
            # Run verification tests
            tests = []
            tests.extend(await self._verify_navigator(page, fingerprint_data, expected_config))
            tests.extend(await self._verify_screen(page, fingerprint_data, expected_config))
            tests.extend(await self._verify_webgl(page, fingerprint_data, expected_config))
            tests.extend(await self._verify_webrtc(page, fingerprint_data, expected_config))
            tests.extend(await self._verify_fonts(page, fingerprint_data, expected_config))
            tests.extend(await self._verify_canvas(page, fingerprint_data, expected_config))
            tests.extend(await self._verify_timezone(page, fingerprint_data, expected_config))
            
            # Calculate scores
            passed_tests = sum(1 for t in tests if t.passed)
            total_tests = len(tests)
            overall_passed = passed_tests / total_tests >= 0.95  # 95% threshold
            
            uniqueness_score = await self._calculate_uniqueness_score(fingerprint_data)
            consistency_score = await self._calculate_consistency_score(fingerprint_data, expected_config)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(tests, uniqueness_score, consistency_score)
            
            report = FingerprintReport(
                browser_id=browser_id,
                timestamp=time.time(),
                overall_passed=overall_passed,
                uniqueness_score=uniqueness_score,
                consistency_score=consistency_score,
                tests=tests,
                recommendations=recommendations
            )
            
            self.verification_history.append(report)
            self.log_info(f"Fingerprint verification completed: {passed_tests}/{total_tests} passed, uniqueness: {uniqueness_score:.1f}")
            
            return report
            
        finally:
            await page.close()
    
    async def _extract_fingerprint_data(self, page) -> Dict[str, Any]:
        """Extract all fingerprint data from the page."""
        return await page.evaluate("""
            () => {
                const data = {
                    navigator: {},
                    screen: {},
                    webgl: {},
                    canvas: {},
                    audio: {},
                    fonts: [],
                    timezone: {},
                    plugins: []
                };
                
                // Navigator properties
                const navProps = ['userAgent', 'platform', 'language', 'languages', 
                                'hardwareConcurrency', 'deviceMemory', 'vendor', 
                                'appVersion', 'product', 'productSub'];
                navProps.forEach(prop => {
                    if (prop in navigator) {
                        data.navigator[prop] = navigator[prop];
                    }
                });
                
                // Screen properties
                const screenProps = ['width', 'height', 'availWidth', 'availHeight', 
                                   'colorDepth', 'pixelDepth', 'orientation'];
                screenProps.forEach(prop => {
                    if (prop in screen) {
                        data.screen[prop] = prop === 'orientation' ? screen[prop].type : screen[prop];
                    }
                });
                
                // WebGL data
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                    if (debugInfo) {
                        data.webgl.vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                        data.webgl.renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                    }
                }
                
                // Canvas fingerprint
                const canvasTest = document.createElement('canvas');
                const ctx = canvasTest.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Camoufox Test 🦊', 2, 2);
                data.canvas.fingerprint = canvasTest.toDataURL().substring(0, 100);
                
                // Timezone
                data.timezone.offset = new Date().getTimezoneOffset();
                data.timezone.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                
                // Audio context
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const analyser = audioContext.createAnalyser();
                    const gainNode = audioContext.createGain();
                    const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
                    
                    gainNode.gain.value = 0;
                    oscillator.connect(analyser);
                    analyser.connect(scriptProcessor);
                    scriptProcessor.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    oscillator.start(0);
                    
                    data.audio.sampleRate = audioContext.sampleRate;
                    data.audio.channelCount = audioContext.destination.channelCount;
                    
                    oscillator.stop();
                    audioContext.close();
                } catch (e) {
                    data.audio.error = e.message;
                }
                
                return data;
            }
        """)
    
    async def _verify_navigator(self, page, data: Dict, expected: Dict) -> List[FingerprintTest]:
        """Verify navigator properties."""
        tests = []
        nav_data = data.get('navigator', {})
        expected_nav = expected.get('navigator', {})
        
        # User Agent consistency
        if 'userAgent' in expected_nav:
            tests.append(FingerprintTest(
                domain=FingerprintDomain.NAVIGATOR,
                property='userAgent',
                expected=expected_nav['userAgent'],
                actual=nav_data.get('userAgent'),
                passed=nav_data.get('userAgent') == expected_nav['userAgent'],
                message="User agent should match configured value"
            ))
        
        # Platform consistency
        if 'platform' in expected_nav:
            tests.append(FingerprintTest(
                domain=FingerprintDomain.NAVIGATOR,
                property='platform',
                expected=expected_nav['platform'],
                actual=nav_data.get('platform'),
                passed=nav_data.get('platform') == expected_nav['platform'],
                message="Platform should match configured value"
            ))
        
        # Hardware concurrency realism
        hw_concurrency = nav_data.get('hardwareConcurrency', 0)
        tests.append(FingerprintTest(
            domain=FingerprintDomain.NAVIGATOR,
            property='hardwareConcurrency',
            expected="2-16",
            actual=hw_concurrency,
            passed=2 <= hw_concurrency <= 16,
            message="Hardware concurrency should be realistic (2-16 cores)"
        ))
        
        return tests
    
    async def _verify_webgl(self, page, data: Dict, expected: Dict) -> List[FingerprintTest]:
        """Verify WebGL properties."""
        tests = []
        webgl_data = data.get('webgl', {})
        
        # Check if WebGL data is properly spoofed
        vendor = webgl_data.get('vendor', '')
        renderer = webgl_data.get('renderer', '')
        
        # Should not expose real GPU info
        tests.append(FingerprintTest(
            domain=FingerprintDomain.WEBGL,
            property='vendor',
            expected="Masked",
            actual=vendor,
            passed=vendor and 'Intel' not in vendor and 'NVIDIA' not in vendor and 'AMD' not in vendor,
            message="WebGL vendor should be masked, not expose real GPU"
        ))
        
        tests.append(FingerprintTest(
            domain=FingerprintDomain.WEBGL,
            property='renderer',
            expected="Masked",
            actual=renderer,
            passed=renderer and not any(gpu in renderer for gpu in ['GeForce', 'Radeon', 'Intel']),
            message="WebGL renderer should be masked, not expose real GPU model"
        ))
        
        return tests
    
    async def _verify_canvas(self, page, data: Dict, expected: Dict) -> List[FingerprintTest]:
        """Verify canvas fingerprinting protection."""
        tests = []
        canvas_data = data.get('canvas', {})
        
        # Canvas should have some noise/randomization
        fingerprint = canvas_data.get('fingerprint', '')
        tests.append(FingerprintTest(
            domain=FingerprintDomain.CANVAS,
            property='fingerprint',
            expected="Unique per session",
            actual=fingerprint[:20] + "...",
            passed=len(fingerprint) > 0,
            message="Canvas fingerprint should be present and unique"
        ))
        
        return tests
    
    async def _calculate_uniqueness_score(self, data: Dict) -> float:
        """Calculate how unique this fingerprint is."""
        score = 0.0
        factors = []
        
        # Check entropy of various properties
        nav = data.get('navigator', {})
        
        # User agent entropy
        ua = nav.get('userAgent', '')
        if ua:
            # Check for common patterns
            if 'Chrome/' in ua and 'Safari/537.36' in ua:
                factors.append(0.7)  # Common but good
            else:
                factors.append(0.5)  # Less common
        
        # Screen resolution entropy
        screen = data.get('screen', {})
        width = screen.get('width', 0)
        height = screen.get('height', 0)
        
        common_resolutions = [(1920, 1080), (1366, 768), (2560, 1440), (3840, 2160)]
        if (width, height) in common_resolutions:
            factors.append(0.8)  # Good - common resolution
        else:
            factors.append(0.6)  # Unusual resolution
        
        # WebGL entropy
        webgl = data.get('webgl', {})
        if webgl.get('vendor') and webgl.get('renderer'):
            factors.append(0.9)  # Properly masked
        
        # Calculate final score
        if factors:
            score = sum(factors) / len(factors) * 100
        
        return score
    
    async def _calculate_consistency_score(self, data: Dict, expected: Dict) -> float:
        """Calculate how consistent the fingerprint is with configuration."""
        matches = 0
        total = 0
        
        # Check navigator consistency
        for key in ['userAgent', 'platform', 'language']:
            if key in expected.get('navigator', {}):
                total += 1
                if data.get('navigator', {}).get(key) == expected['navigator'][key]:
                    matches += 1
        
        # Check screen consistency
        for key in ['width', 'height']:
            if key in expected.get('screen', {}):
                total += 1
                if data.get('screen', {}).get(key) == expected['screen'][key]:
                    matches += 1
        
        return (matches / total * 100) if total > 0 else 0.0
    
    def _generate_recommendations(self, tests: List[FingerprintTest], 
                                uniqueness: float, consistency: float) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Check failed tests
        failed_domains = set()
        for test in tests:
            if not test.passed:
                failed_domains.add(test.domain)
        
        if FingerprintDomain.NAVIGATOR in failed_domains:
            recommendations.append("Review navigator configuration - ensure user agent and platform match")
        
        if FingerprintDomain.WEBGL in failed_domains:
            recommendations.append("WebGL fingerprinting may be detectable - verify spoofing is active")
        
        if uniqueness < 70:
            recommendations.append("Fingerprint uniqueness is low - consider more diverse configurations")
        
        if consistency < 80:
            recommendations.append("Fingerprint consistency is low - check configuration mapping")
        
        return recommendations
    
    async def run_fingerprint_test_suite(self, browser: Any) -> bool:
        """Run a comprehensive fingerprint test suite."""
        test_urls = [
            "https://browserleaks.com/javascript",
            "https://fingerprint.com/demo/",
            "https://amiunique.org/",
            "https://coveryourtracks.eff.org/"
        ]
        
        all_passed = True
        
        for url in test_urls:
            try:
                page = await browser.new_page()
                await page.goto(url, wait_until='networkidle')
                
                # Take screenshot for manual review
                screenshot_path = f"fingerprint_test_{url.replace('https://', '').replace('/', '_')}.png"
                await page.screenshot(path=screenshot_path)
                
                self.log_info(f"Fingerprint test completed for {url}, screenshot: {screenshot_path}")
                
                await page.close()
                
            except Exception as e:
                self.log_error(f"Fingerprint test failed for {url}: {str(e)}")
                all_passed = False
        
        return all_passed
```

### Integration with Browser Service

```python
class CamoufoxService(AsyncServiceBase):
    """Enhanced with fingerprint verification."""
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger,
                 fingerprint_manager: FingerprintManager,
                 fingerprint_verifier: FingerprintVerificationService,
                 proxy_manager: ProxyManager):
        super().__init__(logger, config)
        self.fingerprint_manager = fingerprint_manager
        self.fingerprint_verifier = fingerprint_verifier
        self.proxy_manager = proxy_manager
        self.verification_enabled = config.get('fingerprint_verification', True)
        self.verify_interval = config.get('verify_interval', 100)  # Verify every N browsers
        self.browsers_created = 0
        
    async def create_browser(self, proxy: Optional[ProxyConfig] = None,
                           session_id: Optional[str] = None,
                           verify_fingerprint: Optional[bool] = None) -> Any:
        """Create browser with optional fingerprint verification."""
        # Create browser as before
        browser = await self._create_browser_internal(proxy, session_id)
        
        # Determine if we should verify
        should_verify = verify_fingerprint if verify_fingerprint is not None else (
            self.verification_enabled and 
            (self.browsers_created % self.verify_interval == 0)
        )
        
        if should_verify:
            try:
                # Get the expected configuration
                fingerprint = await self.fingerprint_manager.get_current_fingerprint()
                
                # Run verification
                report = await self.fingerprint_verifier.verify_browser_fingerprint(
                    browser, 
                    session_id or str(uuid.uuid4()),
                    fingerprint['config']
                )
                
                if not report.overall_passed:
                    self.log_warning(f"Fingerprint verification failed: {report.uniqueness_score:.1f} uniqueness")
                    for rec in report.recommendations:
                        self.log_warning(f"  - {rec}")
                else:
                    self.log_info(f"Fingerprint verification passed: {report.uniqueness_score:.1f} uniqueness")
                    
            except Exception as e:
                self.log_error(f"Fingerprint verification error: {str(e)}")
        
        self.browsers_created += 1
        return browser
```

### Testing Fingerprint Quality

```python
# tests/integration/scraping/test_fingerprint_verification.py
import pytest
from src.services.scraping.anti_bot.fingerprint_verification import FingerprintVerificationService

@pytest.mark.integration
async def test_fingerprint_uniqueness():
    """Test that fingerprints are sufficiently unique."""
    verifier = FingerprintVerificationService(config, logger)
    
    # Create multiple browsers and verify fingerprints
    fingerprints = []
    for i in range(5):
        browser = await create_test_browser()
        report = await verifier.verify_browser_fingerprint(
            browser, f"test_{i}", {}
        )
        fingerprints.append(report)
        await browser.close()
    
    # Check uniqueness scores
    avg_uniqueness = sum(f.uniqueness_score for f in fingerprints) / len(fingerprints)
    assert avg_uniqueness >= 75.0, f"Average uniqueness too low: {avg_uniqueness}"
    
    # Check that fingerprints are different
    nav_fingerprints = set()
    for fp in fingerprints:
        nav_data = json.dumps(fp.tests[0].actual)  # Navigator data
        nav_fingerprints.add(nav_data)
    
    assert len(nav_fingerprints) >= 4, "Fingerprints not diverse enough"

@pytest.mark.integration
async def test_fingerprint_consistency():
    """Test that fingerprints match configuration."""
    config = {
        'navigator': {
            'platform': 'Win32',
            'language': 'en-US'
        }
    }
    
    verifier = FingerprintVerificationService({}, logger)
    browser = await create_test_browser_with_config(config)
    
    report = await verifier.verify_browser_fingerprint(
        browser, "test_consistency", config
    )
    
    assert report.consistency_score >= 90.0
    assert report.overall_passed

@pytest.mark.integration
async def test_detection_sites():
    """Test against real fingerprinting detection sites."""
    verifier = FingerprintVerificationService({}, logger)
    browser = await create_test_browser()
    
    passed = await verifier.run_fingerprint_test_suite(browser)
    assert passed, "Failed fingerprint detection tests"
    
    await browser.close()
```

## Configuration

### YAML Configuration Template

```yaml
# config/scraping.yml
scraping:
  browser:
    max_concurrent_browsers: 3
    headless: false  # Set to true for production
    timeout: 30000
    viewport:
      width: 1920
      height: 1080
    
  session:
    min_duration_minutes: 3
    max_duration_minutes: 5
    warning_threshold_seconds: 30
    
  proxy:
    provider: oxylabs  # Can switch to other providers
    rotation_strategy: round_robin  # round_robin, random, least_failed
    max_failures: 3
    cooldown_minutes: 5
    
  oxylabs:
    username: ${OXYLABS_USERNAME}
    password: ${OXYLABS_PASSWORD}
    proxy_type: residential  # residential or mobile
    country: us
    session_time: 10  # minutes
    pool_size: 500
    
  fingerprints:
    rotation_interval: 10  # Rotate every N requests
    verification:
      enabled: true
      interval: 100  # Verify every N browsers
      test_suite_urls:
        - "https://camoufox.com/fingerprint/"
        - "https://browserleaks.com/javascript"
        - "https://fingerprint.com/demo/"
        - "https://amiunique.org/"
      thresholds:
        uniqueness_min: 70  # Minimum uniqueness score
        consistency_min: 80  # Minimum consistency score
        pass_rate: 0.95  # 95% of tests must pass
    profiles:
      - os: Windows
        browsers: [Chrome]
        version_range: [115, 124]
      - os: macOS
        browsers: [Chrome]
        version_range: [115, 124]
        
  anti_bot:
    humanize: true
    typing_delay_range: [50, 150]  # ms
    action_delay_range: [500, 2000]  # ms
    page_scan_delay_range: [2000, 5000]  # ms
    mouse_movement:
      enable_curves: true
      steps_range: [10, 20]
    scrolling:
      enable_natural: true
      overshoot_probability: 0.3
      
  navigation:
    default_timeout: 30000
    retry_count: 3
    retry_delay: 1000
    wait_strategies:
      - networkidle
      - domcontentloaded
    screenshot_on_error: true
    
  facebook_ads:
    base_url: "https://www.facebook.com/ads/library/"
    results_per_page: 25
    max_pages_per_session: 20
    extract_fields:
      - ad_archive_id
      - page_name
      - start_date
      - end_date
      - ad_creative_body
      - ad_creative_link_caption
      - ad_creative_link_description
      - ad_creative_link_title
      - impressions
      - spend
```

### Environment Variables

```bash
# .env
OXYLABS_USERNAME=your_username
OXYLABS_PASSWORD=your_password

# Optional overrides
SCRAPING_BROWSER_HEADLESS=false
SCRAPING_SESSION_MAX_DURATION_MINUTES=5
SCRAPING_PROXY_POOL_SIZE=1000
```

## Testing Strategy

### 1. Unit Tests

```python
# tests/unit/scraping/test_session_manager.py
import pytest
from src.services.scraping.session.session_manager import SessionLifecycleManager

@pytest.mark.asyncio
async def test_session_duration_limits():
    """Test session enforces 3-5 minute limits."""
    config = {
        'min_duration_minutes': 3,
        'max_duration_minutes': 5
    }
    manager = SessionLifecycleManager(config, logger)
    
    session = await manager.start_session("test_task")
    
    # Check session has valid duration
    duration = session.max_end_time - session.start_time
    assert 180 <= duration <= 300  # 3-5 minutes in seconds
    
    # Fast forward time
    manager.session_end_time = time.time() - 1
    
    # Check expiry detection
    assert await manager.check_session_expiry() is True

@pytest.mark.asyncio
async def test_proxy_rotation():
    """Test proxy rotation strategies."""
    provider = MockProxyProvider()
    manager = ProxyManager({'rotation_strategy': 'round_robin'}, logger, provider)
    
    # Get proxies in sequence
    proxy1 = await manager.get_next_proxy()
    proxy2 = await manager.get_next_proxy()
    proxy3 = await manager.get_next_proxy()
    
    # Should be different proxies
    assert proxy1.get_id() != proxy2.get_id()
    assert proxy2.get_id() != proxy3.get_id()
```

### 2. Integration Tests

```python
# tests/integration/scraping/test_camoufox_integration.py
import pytest
from src.containers.scraping import ScrapingContainer

@pytest.mark.integration
async def test_browser_creation_with_proxy():
    """Test browser creation with proxy configuration."""
    container = ScrapingContainer()
    container.config.from_dict({
        'browser': {'headless': True},
        'oxylabs': {
            'username': 'test_user',
            'password': 'test_pass'
        }
    })
    
    browser_service = container.camoufox_service()
    browser = await browser_service.create_browser()
    
    try:
        # Verify browser works
        page = await browser.new_page()
        await page.goto('https://httpbin.org/ip')
        
        # Check proxy is being used
        content = await page.content()
        assert 'origin' in content
        
    finally:
        await browser.close()

@pytest.mark.integration
async def test_facebook_scraper_session_limits():
    """Test Facebook scraper respects session limits."""
    container = ScrapingContainer()
    container.config.from_dict({
        'session': {
            'min_duration_minutes': 0.1,  # 6 seconds for testing
            'max_duration_minutes': 0.2   # 12 seconds for testing
        }
    })
    
    scraper = container.facebook_ads_scraper()
    
    start_time = time.time()
    ads = await scraper.scrape_company_ads("test_company_id")
    end_time = time.time()
    
    duration = end_time - start_time
    assert 6 <= duration <= 12  # Should respect session limits
```

### 3. Anti-Bot Feature Tests

```python
# tests/unit/scraping/test_anti_bot.py
@pytest.mark.asyncio
async def test_mouse_humanizer_bezier_curve():
    """Test mouse movement follows bezier curve."""
    humanizer = MouseHumanizer()
    mock_page = MockPage()
    
    # Track mouse positions
    positions = []
    mock_page.on_mouse_move = lambda x, y: positions.append((x, y))
    
    # Move from (0, 0) to (100, 100)
    await humanizer._bezier_move(mock_page, 100, 100)
    
    # Should have multiple steps
    assert len(positions) >= 10
    
    # Movement should not be linear
    # Check that points deviate from straight line
    deviations = []
    for x, y in positions:
        # Distance from straight line y = x
        deviation = abs(y - x) / math.sqrt(2)
        deviations.append(deviation)
    
    # Should have some deviation
    assert max(deviations) > 5

@pytest.mark.asyncio
async def test_timing_randomizer():
    """Test timing delays are within expected ranges."""
    randomizer = TimingRandomizer()
    
    # Test typing delay
    for _ in range(100):
        delay = randomizer.typing_delay()
        assert 0.05 <= delay <= 0.15
    
    # Test reading delay
    delay = randomizer.reading_delay(100)  # 100 characters
    assert 1.6 <= delay <= 2.4  # 2s ± 20%
```

## Migration Plan

### Phase 1: Foundation (Week 1-2)
1. Create directory structure
2. Implement base classes and abstractions
3. Set up dependency injection container
4. Create configuration system
5. Write unit tests for core components

### Phase 2: Core Services (Week 3-4)
1. Implement CamoufoxService with lifecycle management
2. Build ProxyManager with Oxylabs provider
3. Create SessionLifecycleManager with 3-5 minute limits
4. Implement anti-bot features (humanizers)
5. Integration tests for services

### Phase 3: Facebook Scraper (Week 5-6)
1. Port Facebook ads logic to new architecture
2. Implement token extraction with Camoufox
3. Add pagination with session awareness
4. Create data models and validation
5. End-to-end tests with mock data

### Phase 4: Integration (Week 7-8)
1. Add feature flag for old/new system
2. Create migration scripts for configuration
3. Update existing containers to include new services
4. Performance testing and optimization
5. Documentation and training

### Phase 5: Rollout (Week 9-10)
1. Deploy to staging with 10% traffic
2. Monitor performance and detection rates
3. Gradually increase traffic to 50%
4. Full production rollout
5. Deprecate old system

### Rollback Plan
- Feature flag to instantly revert to old system
- Keep old system running for 30 days post-migration
- Daily backups of scraping results
- Monitoring dashboard for detection rates

## Implementation Timeline

### Week 1-2: Foundation
- [ ] Project setup and structure
- [ ] Base classes implementation
- [ ] Dependency injection setup
- [ ] Configuration system
- [ ] Basic unit tests

### Week 3-4: Core Services  
- [ ] CamoufoxService
- [ ] ProxyManager and OxylabsProvider
- [ ] SessionLifecycleManager
- [ ] Anti-bot humanizers
- [ ] Integration tests

### Week 5-6: Scraper Implementation
- [ ] Facebook ads scraper
- [ ] Token extraction
- [ ] Pagination logic
- [ ] Data validation
- [ ] E2E tests

### Week 7-8: Integration
- [ ] Feature flags
- [ ] Migration scripts
- [ ] Container updates
- [ ] Performance testing
- [ ] Documentation

### Week 9-10: Production Rollout
- [ ] Staging deployment
- [ ] Traffic migration
- [ ] Monitoring setup
- [ ] Full rollout
- [ ] Old system deprecation

## Success Metrics

1. **Detection Rate**: <5% detection rate (vs current ~30%)
2. **Success Rate**: >95% successful scrapes
3. **Performance**: <10s average page load time
4. **Session Compliance**: 100% sessions within 3-5 minute window
5. **Proxy Health**: <10% proxy failure rate
6. **Data Quality**: >99% data validation pass rate
7. **Fingerprint Uniqueness**: >70% average uniqueness score
8. **Fingerprint Consistency**: >80% configuration match rate
9. **Fingerprint Test Pass Rate**: >95% on detection test sites

## Conclusion

This implementation plan provides a comprehensive blueprint for building a Camoufox-based web scraper with advanced anti-bot features. The architecture emphasizes:

- **Modularity**: Easy to extend and maintain
- **Anti-Detection**: Multiple layers of protection including fingerprint verification
- **Session Management**: Strict 3-5 minute lifecycles with automatic rotation
- **Proxy Flexibility**: Easy provider switching with health monitoring
- **Fingerprint Verification**: Automated testing against detection sites including https://camoufox.com/fingerprint/
- **Integration**: Seamless fit with existing LexGenius infrastructure

The fingerprint verification service ensures that browser instances maintain realistic and diverse fingerprints, with continuous monitoring against known detection services. The phased approach ensures minimal disruption while providing a clear path to a more robust and undetectable scraping solution.