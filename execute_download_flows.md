1. If a case is a REMOVAL:
   - You are to upload the HTML.
   - Save the JSON.
   - Add to process_review_cases.
   - You DO NOT DOWNLOAD IT - that will be a different flow.
   - Continue with next item
2. If the case is NOT REMOVAL and has a COMPLAINT link you are to determine if there are any hyperlinks in the 3rd td of the first tr.
  - If there are NO links in the 3rd td of the first tr AND the word COMPLAINT is in the 3rd td (@examples/docket_sheet_no_hyperlinks_3rd_td_step_1.html)
    - Step 1: Click on the 2nd td in the first tr. This usually has a '1' 99% of the time. 
    - Step 2: It will take you to this page: (@examples/docket_sheet_no_hyperlinks_3rd_td_step2.html) click on View Document and wait for download.
  - If there ARE links in the 3rd td of the first tr AND the word COMPLAINT is in the 3rd td (@examples/docket_sheet_no_hyperlinks_3rd_td_step_1.html)
    - Step 1: Click om the 2nd td in the first tr. This usually has a a '1' 99% of the time.
    - Step 2: 
      - a) If you land on this page (@examples/docket_sheet_with_hyperlinks_3rd_td_step2.html).
        - Deselect all checkboxes but first one.
        - Click on 'Download Selected'
        - You will land on (@examples/docket_sheet_with_hyperlinks_3rd_td_step3a.html) and click on 'Download Documents' and wait for download.
      - b) If the page you landed on does not have checkboxes, but does have hyperlinks: (@examples/docket_sheet_with_hyperlinks_3rd_td_step2b.html)
        - Click on the first hyperlink '1' with the "Document Number:" text next to it.
        - You will land on (@example/docket_sheet_with_hyperlinks_3rd_td_step3b.html)
        - Click on 'View Document' and wait for download.

