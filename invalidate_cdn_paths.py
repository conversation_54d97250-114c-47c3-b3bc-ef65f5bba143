#!/usr/bin/env python3
"""
Invalidate specific CloudFront paths
"""
import boto3
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment
project_root = Path(__file__).parent
env_path = project_root / '.env'
load_dotenv(env_path)

# Get AWS credentials
aws_access_key = (
    os.getenv('AWS_ACCESS_KEY_ID') or 
    os.getenv('LEXGENIUS_AWS_ACCESS_KEY_ID') or
    os.getenv('AWS_ACCESS_KEY')
)
aws_secret_key = (
    os.getenv('AWS_SECRET_ACCESS_KEY') or 
    os.getenv('LEXGENIUS_AWS_SECRET_ACCESS_KEY') or
    os.getenv('AWS_SECRET_KEY')
)
aws_region = (
    os.getenv('AWS_REGION') or 
    os.getenv('LEXGENIUS_AWS_REGION') or
    os.getenv('AWS_DEFAULT_REGION') or
    'us-west-2'
)

# CloudFront distribution ID - check env vars
distribution_id = (
    os.getenv('CLOUDFRONT_DISTRIBUTION_ID') or
    os.getenv('LEXGENIUS_CLOUDFRONT_DISTRIBUTION_ID') or
    'E1Y0B1WHHHA26H'  # Default if not in env
)

# Create CloudFront client
cf_client = boto3.client(
    'cloudfront',
    aws_access_key_id=aws_access_key,
    aws_secret_access_key=aws_secret_key,
    region_name=aws_region
)

# Paths to invalidate
paths = [
    '/20250613/html/cand_25_04921_Schmalzbauer_et_al_v_Monsanto_Company.html',
    '/20250613/html/ilnd_25_06519_Madison_v_Beauty_Bell_Enterprises_LLC_et_al.html',
    '/20250613/html/ilnd_25_06520_Jones_v_Beauty_Bell_Enterprises_LLC_et_al.html',
    '/20250613/html/ilsd_25_01247_Daniels_v_Syngenta_Crop_Protection_LLC_et_al.html'
]

print(f"Creating invalidation for {len(paths)} paths...")

try:
    response = cf_client.create_invalidation(
        DistributionId=distribution_id,
        InvalidationBatch={
            'Paths': {
                'Quantity': len(paths),
                'Items': paths
            },
            'CallerReference': f'force-update-{Path(__file__).stat().st_mtime}'
        }
    )
    
    invalidation_id = response['Invalidation']['Id']
    print(f"✓ Invalidation created: {invalidation_id}")
    print(f"  Status: {response['Invalidation']['Status']}")
    print("\nInvalidated paths:")
    for path in paths:
        print(f"  - {path}")
    
except Exception as e:
    print(f"✗ Failed to create invalidation: {e}")
    sys.exit(1)