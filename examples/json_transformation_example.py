#!/usr/bin/env python3
"""
JSON Transformation Example Script

This script demonstrates how to use the JSONTransformationService to transform
messy JSON objects with specific cleaning and transformation rules.

The transformation includes:
1. Key deletions (removes unwanted fields)
2. Value transformations (moves and transforms field values)
3. Attorney list cleaning (filters <PERSON>reer PLC, deduplicates)
4. HTML re-parsing (fetches and corrects jury_demand, is_mdl, lead_case)
5. Final formatting (converts empty strings to None, orders underscore keys)
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any

from dependency_injector.wiring import Provide, inject
from src.containers.transformer import TransformerContainer
from src.transformer.components.transformation.json_transformer import JSONTransformationService


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def transform_json_with_html(
    json_data: Dict[str, Any],
    html_url: str,
    transformation_service: JSONTransformationService
) -> Dict[str, Any]:
    """
    Transform JSON data with HTML re-parsing.
    
    Args:
        json_data: Input JSON data to transform
        html_url: URL to HTML content for re-parsing fields
        transformation_service: The JSON transformation service
        
    Returns:
        Transformed JSON data
    """
    logger.info("Starting JSON transformation with HTML re-parsing")
    
    # Transform the JSON data
    transformed_data = await transformation_service.transform_json_data(
        raw_data=json_data,
        html_url=html_url
    )
    
    logger.info("Transformation completed successfully")
    return transformed_data


@inject
async def main(
    json_transformation_service: JSONTransformationService = Provide[
        TransformerContainer.json_transformation_service
    ]
):
    """Main function demonstrating JSON transformation."""
    
    # Sample JSON data (abbreviated for demonstration)
    sample_json = {
        "workflow_status": "unknown",
        "total_execution_time": 8.048643,
        "docket_number": "1:25-cv-09787",
        "court_id": "ilnd",
        "case_title": "Muir v. Godrej SON Holdings, Inc. et al",
        "versus": "Debra Muir v. Godrej SON Holdings, Inc.",
        "case_info": {
            "flags": ["JANTZ", "MDL 3060", "html_only"],
            "versus": "Debra Muir v. Godrej SON Holdings, Inc.",
            "date_filed": "08/18/2025",
            "assigned_to": "Honorable Mary M. Rowland",
            "cause": "28:1332 Diversity-Product Liability",
            "nos": "365 Personal Inj. Prod. Liability",
            "jurisdiction": "Diversity",
            "demand": "$75,000",
            "_preprocessing_metadata": {
                "has_javascript": True,
                "has_transaction_receipt": True,
                "scripts_removed": 5,
                "preprocessing_applied": True
            }
        },
        "attorney": [
            {
                "attorney_name": "Rebecca King",
                "law_firm": "Keller Postman",
                "represents": "plaintiff",
                "phone": "************",
                "email": "<EMAIL>",
                "city": "Chicago",
                "state": "IL",
                "lead_attorney": True
            },
            {
                "attorney_name": "Jake Woody",
                "law_firm": "BROWN GREER PLC",
                "represents": "defendant",
                "email": "<EMAIL>",
                "city": "Richmond",
                "state": "VA",
                "lead_attorney": False
            },
            {
                "attorney_name": "Rebecca              King",  # Duplicate with extra spaces
                "law_firm": "Keller Postman",
                "phone": "************",
                "email": "<EMAIL>",
                "city": "Chicago",
                "state": "IL",
                "lead_attorney": True
            }
        ],
        "parsed_defendants": [
            "Godrej SON Holdings, Inc.",
            "Loreal USA, Inc.",
            "Loreal USA Products, Inc.",
            "SoftSheen-Carson LLC",
            "Strength of Nature, LLC",
            "Wella Operations US LLC"
        ],
        "parsed_plaintiffs": [
            {
                "name": "Debra Muir",
                "attorneys": []
            }
        ],
        "html_s3_upload_ignore": {
            "success": True,
            "s3_path": "20250818/html/ilnd_25_09787_Debra_Muir_v_Godrej_SON_Holdings_Inc.html",
            "s3_html": "https://cdn.lexgenius.ai/20250818/html/ilnd_25_09787_Debra_Muir_v_Godrej_SON_Holdings_Inc.html",
            "reason": "ignore_download_triggered",
            "timestamp": "2025-08-18T17:37:01.450250"
        },
        "jury_demand": "",  # Wrong value - should be "Plaintiff" from HTML
        "is_mdl": False,    # Wrong value - should be True from HTML
        "lead_case_id": "", # Should be replaced with "lead_case" from HTML
        "title": "",
        "allegations": "",
        "relevance_reason": "Unknown",
        "attorneys": [],
        "relevance_score": 0.0,
        "should_ignore": True,
        "plaintiff_attorneys": [],
        "defendant_attorneys": [],
        "nature_of_suit": "",
        "case_name": "",
        "_filter_reason": "No artifacts found across past 7 days",
        "_filter_decision": "process",
        "_processing_notes": "Matched ignore_download config."
    }
    
    # HTML URL for re-parsing fields
    html_url = "https://cdn.lexgenius.ai/20250818/html/ilnd_25_09787_Debra_Muir_v_Godrej_SON_Holdings_Inc.html"
    
    try:
        # Transform the JSON
        transformed_json = await transform_json_with_html(
            json_data=sample_json,
            html_url=html_url,
            transformation_service=json_transformation_service
        )
        
        # Display results
        print("\n" + "="*60)
        print("TRANSFORMATION RESULTS")
        print("="*60)
        
        # Show key transformations
        print("\n✅ Key Deletions Applied:")
        deleted_keys = [
            'case_title', 'parsed_defendants', 'case_info', 
            'parsed_attorneys', 'parsed_plaintiffs', 'relevance_reason',
            'attorneys', 'relevance_score', 'should_ignore',
            'plaintiff_attorneys', 'defendant_attorneys', 
            'nature_of_suit', 'lead_case_id', 'case_name'
        ]
        for key in deleted_keys:
            if key not in transformed_json:
                print(f"  - {key}: DELETED")
        
        print("\n✅ Value Transformations:")
        print(f"  - versus: {transformed_json.get('versus', 'NOT SET')}")
        print(f"  - defendant count: {len(transformed_json.get('defendant', []))}")
        print(f"  - plaintiff count: {len(transformed_json.get('plaintiff', []))}")
        plaintiffs = transformed_json.get('plaintiff', [])
        if plaintiffs:
            for p in plaintiffs:
                print(f"    • {p}")
        print(f"  - s3_html: {transformed_json.get('s3_html', 'NOT SET')[:50]}...")
        
        print("\n✅ Case Info Flattened:")
        flattened_fields = ['assigned_to', 'cause', 'nos', 'jurisdiction', 'demand']
        for field in flattened_fields:
            if field in transformed_json:
                print(f"  - {field}: {transformed_json[field]}")
        
        print("\n✅ Attorney List Cleaned:")
        attorneys = transformed_json.get('attorney', [])
        print(f"  - Total attorneys: {len(attorneys)}")
        print(f"  - Brown Greer PLC filtered: YES")
        print(f"  - Duplicates removed: YES")
        for attorney in attorneys:
            print(f"    • {attorney.get('attorney_name')} - {attorney.get('law_firm')}")
        
        print("\n✅ HTML Re-parsed Fields:")
        print(f"  - jury_demand: {transformed_json.get('jury_demand', 'NOT SET')}")
        print(f"  - is_mdl: {transformed_json.get('is_mdl', False)}")
        print(f"  - lead_case: {transformed_json.get('lead_case', 'NOT SET')}")
        
        print("\n✅ Final Formatting:")
        print(f"  - title: {transformed_json.get('title')}")
        print(f"  - allegations: {transformed_json.get('allegations')}")
        
        # Check underscore keys are at the end
        keys = list(transformed_json.keys())
        first_underscore_idx = next((i for i, k in enumerate(keys) if k.startswith('_')), -1)
        if first_underscore_idx > 0:
            print(f"  - Underscore keys moved to end: YES (starting at index {first_underscore_idx})")
        
        # Save transformed JSON to file
        output_path = Path("examples/transformed_output.json")
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(transformed_json, f, indent=2, default=str)
        
        print(f"\n✅ Transformed JSON saved to: {output_path}")
        
    except Exception as e:
        logger.error(f"Error during transformation: {str(e)}")
        raise


if __name__ == "__main__":
    # Initialize DI container
    container = TransformerContainer()
    container.config.from_dict({
        "log_level": "INFO",
        "deepseek_api_key": "dummy_key",  # Replace with actual key
        "openai_api_key": "dummy_key",     # Replace with actual key
    })
    container.logger.override(logger)
    
    # Wire the container
    container.wire(modules=[__name__])
    
    # Run the example
    asyncio.run(main())