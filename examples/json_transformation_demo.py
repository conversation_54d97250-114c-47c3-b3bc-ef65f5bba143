#!/usr/bin/env python3
"""
JSON Transformation Service Demonstration

This script demonstrates how the new JSON transformation service works
with sample data, showing all the transformation rules in action.
"""
import asyncio
import json
import sys
import os
from typing import Dict, Any

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.transformer.components.transformation.json_transformer import JSONTransformationService


def create_sample_data() -> Dict[str, Any]:
    """Create sample data that demonstrates all transformation features."""
    return {
        # Fields that will be transformed
        'case_title': '<PERSON> v. ACME Corporation and XYZ Inc.',
        'parsed_defendants': ['ACME Corporation', 'XYZ Inc.', 'ABC LLC'],
        'parsed_plaintiffs': [
            {'name': '<PERSON>'},
            {'name': '<PERSON>'}
        ],
        'html_s3_upload_ignore': {
            's3_html': 'https://cdn.lexgenius.ai/20250812/html/sample_case.html'
        },
        'case_info': {
            'nature_of_suit': 'Product Liability',
            'jurisdiction': 'Federal',
            'cause_of_action': 'Negligence, Strict Liability',
            'judge': 'Hon. <PERSON>',
            'court_name': 'U.S. District Court for the Northern District of Texas'
        },
        
        # Attorney data that will be cleaned and deduplicated
        'attorney': [
            {
                'attorney_name': '  John   D.   Smith  ',
                'law_firm': '  Smith  &   Associates   LLC  ',
                'email': '<EMAIL>'
            },
            {
                'attorney_name': 'Jane Miller',
                'law_firm': 'Brown Greer PLC',  # Should be filtered out
                'email': '<EMAIL>'
            },
            {
                'attorney_name': 'Robert Wilson',
                'law_firm': 'Wilson & Partners',
                'email': '<EMAIL>'
            },
            {
                'attorney_name': '  John   D.   Smith  ',  # Duplicate (different whitespace)
                'law_firm': '  Smith  &   Associates   LLC  ',
                'email': '<EMAIL>'
            },
            {
                'attorney_name': 'Lisa Chen',
                'law_firm': 'Chen Legal Group',
                'email': '<EMAIL>'
            }
        ],
        
        # Fields that should be deleted
        'relevance_reason': 'Case involves product liability claims',
        'relevance_score': 0.87,
        'should_ignore': False,
        'plaintiff_attorneys': ['Attorney 1', 'Attorney 2'],
        'defendant_attorneys': ['Defense Attorney 1'],
        'lead_case_id': 'MDL-2846-001',
        'case_name': 'Product Liability MDL',
        
        # Fields that should remain
        'court_id': 'TXND',
        'docket_num': '4:23-cv-12345-ABC',
        'date_filed': '2023-03-15',
        'date_terminated': None,
        'mdl_num': '2846',
        'transfer_docket': '2:18-md-02846',
        'summary': 'Product liability case involving defective equipment',
        
        # Fields with empty values (should become None)
        'empty_string_field': '',
        'whitespace_only_field': '   ',
        'null_field': None,
        
        # Metadata fields (should be moved to end)
        '_processed_timestamp': '2025-01-15T10:30:00Z',
        '_source_file': 'sample_case.json',
        '_processing_version': '2.0'
    }


async def demonstrate_transformation():
    """Demonstrate the JSON transformation process."""
    print("🔄 JSON Transformation Service Demonstration")
    print("=" * 60)
    
    # Initialize the transformation service with a simple logger
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    transformation_service = JSONTransformationService(logger=logger, config={})
    
    # Create sample data
    original_data = create_sample_data()
    
    print(f"\n📄 Original Data Summary:")
    print(f"   - Total fields: {len(original_data)}")
    print(f"   - Case title: {original_data.get('case_title')}")
    print(f"   - Defendants: {len(original_data.get('parsed_defendants', []))}")
    print(f"   - Attorneys: {len(original_data.get('attorney', []))}")
    print(f"   - Fields to be deleted: {len([k for k in original_data.keys() if k in transformation_service.fields_to_delete])}")
    
    # Apply transformation
    print(f"\n🔄 Applying JSON transformations...")
    transformed_data = await transformation_service.transform_json_data(original_data)
    
    print(f"\n✅ Transformation Results:")
    print(f"   - Total fields: {len(transformed_data)}")
    print(f"   - versus: {transformed_data.get('versus')}")
    print(f"   - defendant: {transformed_data.get('defendant')}")
    print(f"   - plaintiff: {transformed_data.get('plaintiff')}")
    print(f"   - s3_html: {transformed_data.get('s3_html')}")
    print(f"   - Attorneys after cleaning: {len(transformed_data.get('attorney', []))}")
    
    # Show attorney cleaning results
    print(f"\n👥 Attorney Cleaning Results:")
    attorneys = transformed_data.get('attorney', [])
    for i, attorney in enumerate(attorneys, 1):
        print(f"   {i}. {attorney.get('attorney_name')} at {attorney.get('law_firm')}")
    
    # Show flattened case_info fields
    print(f"\n📊 Flattened Case Info Fields:")
    for field in transformation_service.case_info_flatten_fields:
        if field in transformed_data:
            print(f"   - {field}: {transformed_data[field]}")
    
    # Show deleted fields
    deleted_fields = [k for k in original_data.keys() if k not in transformed_data]
    print(f"\n🗑️  Deleted Fields ({len(deleted_fields)}):")
    for field in sorted(deleted_fields):
        print(f"   - {field}")
    
    # Show final formatting
    print(f"\n🎨 Final Formatting:")
    print(f"   - title: {transformed_data.get('title')}")
    print(f"   - allegations: {transformed_data.get('allegations')}")
    print(f"   - Empty strings converted to None: {transformed_data.get('empty_string_field')}")
    
    # Show key ordering (underscore keys at end)
    keys = list(transformed_data.keys())
    underscore_keys = [k for k in keys if k.startswith('_')]
    print(f"   - Underscore keys moved to end: {underscore_keys}")
    
    # Validate transformation
    print(f"\n✅ Validation:")
    validation_result = transformation_service.validate_transformation(original_data, transformed_data)
    print(f"   - Transformation valid: {validation_result['is_valid']}")
    if validation_result['issues']:
        print(f"   - Issues found: {validation_result['issues']}")
    
    # Show transformation summary
    print(f"\n📈 Transformation Summary:")
    for check, result in validation_result['transformation_summary'].items():
        status = "✅" if result else "❌"
        print(f"   {status} {check.replace('_', ' ').title()}")
    
    return transformed_data


async def save_demo_output(transformed_data: Dict[str, Any]):
    """Save the demonstration output to a file."""
    output_file = '/Users/<USER>/PycharmProjects/lexgenius/examples/transformed_sample.json'
    
    try:
        with open(output_file, 'w') as f:
            json.dump(transformed_data, f, indent=2, default=str)
        print(f"\n💾 Sample transformed data saved to: {output_file}")
    except Exception as e:
        print(f"\n❌ Failed to save output: {e}")


async def main():
    """Main demonstration function."""
    try:
        transformed_data = await demonstrate_transformation()
        await save_demo_output(transformed_data)
        
        print(f"\n🎉 Demonstration completed successfully!")
        print(f"\nThe JSON transformation service is now integrated into the data processing engine")
        print(f"and will automatically apply these transformations to all processed docket data.")
        
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())