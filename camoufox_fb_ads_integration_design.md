# Camoufox Integration Design for Facebook Ads System

## Executive Summary

This design document outlines the integration of Camoufox browser automation into the existing Facebook ads scraping system. The integration will be controlled by a feature flag (`use_camoufox: true`) to enable gradual rollout and A/B testing while maintaining backward compatibility with the current `requests`-based implementation.

## Table of Contents
1. [Current Architecture Analysis](#current-architecture-analysis)
2. [Integration Architecture](#integration-architecture)
3. [Feature Flag Implementation](#feature-flag-implementation)
4. [Service Design](#service-design)
5. [Migration Strategy](#migration-strategy)
6. [Configuration Updates](#configuration-updates)
7. [Testing Strategy](#testing-strategy)
8. [Rollback Plan](#rollback-plan)

## Current Architecture Analysis

### Existing Components
1. **FacebookSessionManager**: Uses `requests` library with custom SSL adapters
2. **FacebookAPIClient**: Makes HTTP requests to Facebook's GraphQL endpoints
3. **FbAdsContainer**: Dependency injection container managing all services
4. **Configuration**: YAML-based configuration with extensive proxy and session settings

### Key Integration Points
- Session management and token extraction
- Proxy configuration (Oxylabs integration)
- API client request/response handling
- Error handling and retry logic
- Bandwidth logging and monitoring

## Integration Architecture

### Design Principles
1. **Minimal Disruption**: Existing code paths remain unchanged when feature flag is disabled
2. **Interface Compatibility**: New components implement same interfaces as existing ones
3. **Gradual Migration**: Can run both implementations side-by-side
4. **Performance Parity**: Camoufox implementation maintains or improves performance

### Architecture Diagram
```
┌─────────────────────────────────────────────────────────────┐
│                    FB Ads System                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐         ┌──────────────────┐         │
│  │   Config YAML   │         │  Feature Flag    │         │
│  │ use_camoufox:   │────────▶│   Controller     │         │
│  │   true/false    │         └────────┬─────────┘         │
│  └─────────────────┘                  │                   │
│                                       │                   │
│                    ┌──────────────────┴────────────────┐  │
│                    │                                    │  │
│         ┌──────────▼─────────┐          ┌─────────────▼──┐│
│         │  Legacy Path       │          │ Camoufox Path   ││
│         │  (requests)        │          │ (browser)       ││
│         ├──────────────────┬─┤          ├─┬──────────────┤│
│         │ Session Manager   │ │          │ │ Camoufox     ││
│         │ (requests-based)  │ │          │ │ Session Mgr  ││
│         ├──────────────────┤ │          │ ├──────────────┤│
│         │ API Client       │ │          │ │ Browser      ││
│         │ (HTTP requests)  │ │          │ │ API Client   ││
│         └──────────────────┘ │          │ └──────────────┘│
│                              │          │                  │
│         ┌──────────────────┐ │          │ ┌──────────────┐│
│         │ Shared Services  │◄┴──────────┴►│ Shared       ││
│         │ - Proxy Manager  │              │ Services     ││
│         │ - Error Handler  │              │              ││
│         │ - Data Storage   │              │              ││
│         └──────────────────┘              └──────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## Feature Flag Implementation

### Configuration Schema Update

```yaml
# config/fb_ads.yml additions
feature_flags:
  # Existing flags...
  use_camoufox: false  # Enable Camoufox browser automation
  camoufox_migration:
    enabled: true
    percentage: 0  # Percentage of traffic to route to Camoufox (0-100)
    whitelist_firms: []  # Specific firm IDs to always use Camoufox
    blacklist_firms: []  # Specific firm IDs to never use Camoufox
    
# Camoufox-specific configuration
camoufox:
  browser:
    headless: true
    max_browsers: 3
    browser_timeout: 30000
    fingerprint_rotation_interval: 10
  session:
    min_duration_minutes: 3
    max_duration_minutes: 5
  verification:
    enabled: true
    interval: 100
    min_uniqueness_score: 70
```

### Feature Flag Controller

```python
# src/services/fb_ads/feature_flag_controller.py
from typing import Dict, Any, Optional
import random
import hashlib

class FeatureFlagController:
    """Controls feature flag logic for Camoufox migration."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.feature_flags = config.get('feature_flags', {})
        self.migration_config = self.feature_flags.get('camoufox_migration', {})
        
    def should_use_camoufox(self, firm_id: Optional[str] = None) -> bool:
        """Determine if Camoufox should be used for this request."""
        # Check if feature is enabled at all
        if not self.feature_flags.get('use_camoufox', False):
            return False
            
        # Check if migration is enabled
        if not self.migration_config.get('enabled', False):
            return False
            
        # Check whitelist/blacklist
        if firm_id:
            if firm_id in self.migration_config.get('whitelist_firms', []):
                return True
            if firm_id in self.migration_config.get('blacklist_firms', []):
                return False
                
        # Check percentage rollout
        percentage = self.migration_config.get('percentage', 0)
        if percentage >= 100:
            return True
        if percentage <= 0:
            return False
            
        # Use consistent hashing for A/B testing
        if firm_id:
            hash_value = int(hashlib.md5(firm_id.encode()).hexdigest(), 16)
            return (hash_value % 100) < percentage
        else:
            return random.random() * 100 < percentage
```

## Service Design

### 1. Abstract Base Classes

```python
# src/services/fb_ads/base/session_manager_base.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class SessionManagerBase(ABC):
    """Abstract base class for session managers."""
    
    @abstractmethod
    async def create_new_session(self) -> bool:
        """Create a new session."""
        pass
        
    @abstractmethod
    async def get_session_data(self) -> Dict[str, Any]:
        """Get current session data including tokens."""
        pass
        
    @abstractmethod
    async def refresh_session(self) -> bool:
        """Refresh the current session."""
        pass
        
    @abstractmethod
    async def cleanup(self):
        """Cleanup resources."""
        pass

# src/services/fb_ads/base/api_client_base.py
class APIClientBase(ABC):
    """Abstract base class for API clients."""
    
    @abstractmethod
    async def search_company_by_name(self, company_name: str) -> Optional[Dict]:
        """Search for company by name."""
        pass
        
    @abstractmethod
    async def fetch_ads_page(self, company_id: str, start_date: str, 
                           end_date: str, cursor: Optional[str] = None) -> Optional[Dict]:
        """Fetch a page of ads."""
        pass
```

### 2. Camoufox Implementation

```python
# src/services/fb_ads/camoufox/camoufox_session_manager.py
from camoufox.sync_api import Camoufox
import asyncio
from typing import Dict, Any, Optional
from ..base.session_manager_base import SessionManagerBase
from src.services.scraping.browser.fingerprint_manager import FingerprintManager
from src.services.scraping.proxy.proxy_manager import ProxyManager

class CamoufoxSessionManager(SessionManagerBase):
    """Camoufox-based session manager for Facebook."""
    
    def __init__(self, config: Dict[str, Any], logger, 
                 fingerprint_manager: FingerprintManager,
                 proxy_manager: ProxyManager):
        self.config = config
        self.logger = logger
        self.fingerprint_manager = fingerprint_manager
        self.proxy_manager = proxy_manager
        self.browser = None
        self.page = None
        self.session_data = {}
        self.session_start_time = None
        self.max_session_duration = config.get('camoufox', {}).get(
            'session', {}).get('max_duration_minutes', 5) * 60
        
    async def create_new_session(self) -> bool:
        """Create a new browser session."""
        try:
            # Get proxy and fingerprint
            proxy = await self.proxy_manager.get_next_proxy()
            fingerprint = await self.fingerprint_manager.get_next_fingerprint()
            
            # Launch Camoufox browser
            browser_config = {
                **fingerprint['config'],
                'proxy': proxy.to_dict() if proxy else None,
                'headless': self.config.get('camoufox', {}).get('browser', {}).get('headless', True)
            }
            
            # Use asyncio.to_thread for sync Camoufox API
            self.browser = await asyncio.to_thread(
                self._launch_browser, browser_config
            )
            
            # Create page and navigate to Facebook
            self.page = await self.browser.new_page()
            await self.page.goto('https://www.facebook.com/ads/library/')
            
            # Extract tokens
            self.session_data = await self._extract_tokens()
            self.session_start_time = asyncio.get_event_loop().time()
            
            self.logger.info("Camoufox session created successfully")
            return bool(self.session_data.get('fb_dtsg'))
            
        except Exception as e:
            self.logger.error(f"Failed to create Camoufox session: {str(e)}")
            return False
    
    def _launch_browser(self, config: Dict) -> Any:
        """Launch Camoufox browser (sync operation)."""
        return Camoufox(
            config=config,
            humanize=True,
            geoip=True
        )
    
    async def _extract_tokens(self) -> Dict[str, Any]:
        """Extract Facebook tokens from page."""
        return await self.page.evaluate("""
            () => {
                const scripts = document.querySelectorAll('script');
                let tokens = {};
                
                for (const script of scripts) {
                    const text = script.textContent || '';
                    
                    // Extract fb_dtsg
                    const dtsgMatch = text.match(/"DTSGInitialData".*?"token":"([^"]+)"/);
                    if (dtsgMatch) tokens.fb_dtsg = dtsgMatch[1];
                    
                    // Extract lsd
                    const lsdMatch = text.match(/"LSD".*?{([^}]+)}/);
                    if (lsdMatch) {
                        const lsdToken = lsdMatch[0].match(/"token":"([^"]+)"/);
                        if (lsdToken) tokens.lsd = lsdToken[1];
                    }
                    
                    // Extract jazoest
                    const jazoestMatch = text.match(/jazoest=(\d+)/);
                    if (jazoestMatch) tokens.jazoest = jazoestMatch[1];
                }
                
                return tokens;
            }
        """)
    
    async def get_session_data(self) -> Dict[str, Any]:
        """Get current session data."""
        # Check if session needs refresh
        if self.session_start_time:
            elapsed = asyncio.get_event_loop().time() - self.session_start_time
            if elapsed > self.max_session_duration:
                await self.refresh_session()
                
        return self.session_data
    
    async def refresh_session(self) -> bool:
        """Refresh the browser session."""
        # Close current browser
        if self.browser:
            await self.cleanup()
            
        # Create new session
        return await self.create_new_session()
    
    async def cleanup(self):
        """Cleanup browser resources."""
        if self.page:
            await self.page.close()
            self.page = None
            
        if self.browser:
            await asyncio.to_thread(self.browser.close)
            self.browser = None
            
        self.session_data = {}
        self.session_start_time = None

# src/services/fb_ads/camoufox/camoufox_api_client.py
class CamoufoxAPIClient(APIClientBase):
    """Camoufox-based API client for Facebook."""
    
    def __init__(self, session_manager: CamoufoxSessionManager, config: Dict[str, Any], logger):
        self.session_manager = session_manager
        self.config = config
        self.logger = logger
        
    async def search_company_by_name(self, company_name: str) -> Optional[Dict]:
        """Search for company using browser automation."""
        try:
            page = self.session_manager.page
            if not page:
                await self.session_manager.create_new_session()
                page = self.session_manager.page
                
            # Navigate to search
            search_url = f"https://www.facebook.com/ads/library/?active_status=all&ad_type=all&country=US&q={company_name}"
            await page.goto(search_url)
            
            # Wait for results
            await page.wait_for_selector('[data-testid="ad-library-search-results"]', timeout=10000)
            
            # Extract company data
            companies = await page.evaluate("""
                () => {
                    const results = [];
                    const items = document.querySelectorAll('[data-testid="ad-library-advertiser-link"]');
                    
                    items.forEach(item => {
                        const name = item.textContent;
                        const link = item.href;
                        const idMatch = link.match(/view_all_page_id=(\d+)/);
                        
                        if (idMatch) {
                            results.push({
                                id: idMatch[1],
                                name: name,
                                link: link
                            });
                        }
                    });
                    
                    return results;
                }
            """)
            
            # Return first match
            if companies:
                return {
                    "data": {
                        "ad_library_main_search_results": {
                            "results": [{
                                "id": companies[0]['id'],
                                "name": companies[0]['name']
                            }]
                        }
                    }
                }
                
            return None
            
        except Exception as e:
            self.logger.error(f"Camoufox company search failed: {str(e)}")
            return None
    
    async def fetch_ads_page(self, company_id: str, start_date: str, 
                           end_date: str, cursor: Optional[str] = None) -> Optional[Dict]:
        """Fetch ads using browser automation."""
        try:
            page = self.session_manager.page
            if not page:
                await self.session_manager.create_new_session()
                page = self.session_manager.page
                
            # Build URL
            url = f"https://www.facebook.com/ads/library/?active_status=all&ad_type=all&country=US&view_all_page_id={company_id}"
            url += f"&start_date={start_date}&end_date={end_date}"
            
            if cursor:
                # Handle pagination through browser interaction
                pass  # Implementation depends on Facebook's UI
            else:
                await page.goto(url)
                
            # Wait for ads to load
            await page.wait_for_selector('[data-testid="ad-library-ad-card"]', timeout=10000)
            
            # Extract ads data
            ads_data = await page.evaluate("""
                () => {
                    const ads = [];
                    const cards = document.querySelectorAll('[data-testid="ad-library-ad-card"]');
                    
                    cards.forEach(card => {
                        // Extract ad details
                        const ad = {
                            id: card.getAttribute('data-ad-id'),
                            text: card.querySelector('[data-testid="ad-creative-text"]')?.textContent,
                            startDate: card.querySelector('[data-testid="ad-start-date"]')?.textContent,
                            // ... more fields
                        };
                        
                        ads.push(ad);
                    });
                    
                    // Check for next page cursor
                    const nextButton = document.querySelector('[data-testid="ad-library-load-more"]');
                    const hasMore = nextButton && !nextButton.disabled;
                    
                    return {
                        ads: ads,
                        hasMore: hasMore,
                        cursor: hasMore ? 'next' : null
                    };
                }
            """)
            
            # Format response to match expected structure
            return {
                "payload": {
                    "results": ads_data['ads'],
                    "forwardCursor": ads_data['cursor'],
                    "totalCount": len(ads_data['ads'])
                }
            }
            
        except Exception as e:
            self.logger.error(f"Camoufox ads fetch failed: {str(e)}")
            return None
```

### 3. Factory Pattern for Service Selection

```python
# src/services/fb_ads/factories/session_manager_factory.py
from typing import Dict, Any
from ..session_manager import FacebookSessionManager  # Legacy
from ..camoufox.camoufox_session_manager import CamoufoxSessionManager
from ..feature_flag_controller import FeatureFlagController

class SessionManagerFactory:
    """Factory for creating appropriate session manager based on feature flags."""
    
    @staticmethod
    def create(config: Dict[str, Any], logger, **dependencies) -> SessionManagerBase:
        """Create session manager based on configuration."""
        flag_controller = FeatureFlagController(config)
        
        if flag_controller.should_use_camoufox():
            logger.info("Using Camoufox session manager")
            return CamoufoxSessionManager(
                config=config,
                logger=logger,
                fingerprint_manager=dependencies.get('fingerprint_manager'),
                proxy_manager=dependencies.get('proxy_manager')
            )
        else:
            logger.info("Using legacy requests-based session manager")
            return FacebookSessionManager(config=config, logger=logger)

# src/services/fb_ads/factories/api_client_factory.py
class APIClientFactory:
    """Factory for creating appropriate API client based on feature flags."""
    
    @staticmethod
    def create(session_manager: SessionManagerBase, config: Dict[str, Any], 
               logger) -> APIClientBase:
        """Create API client based on session manager type."""
        if isinstance(session_manager, CamoufoxSessionManager):
            return CamoufoxAPIClient(
                session_manager=session_manager,
                config=config,
                logger=logger
            )
        else:
            return FacebookAPIClient(
                session_manager=session_manager,
                config=config,
                logger=logger
            )
```

### 4. Updated Container Configuration

```python
# src/containers/fb_ads.py updates
from src.services.fb_ads.factories.session_manager_factory import SessionManagerFactory
from src.services.fb_ads.factories.api_client_factory import APIClientFactory
from src.services.scraping.browser.fingerprint_manager import FingerprintManager
from src.services.scraping.proxy.proxy_manager import ProxyManager
from src.services.scraping.proxy.oxylabs_provider import OxylabsProvider

class FbAdsContainer(containers.DeclarativeContainer):
    """Enhanced container with Camoufox support."""
    
    # ... existing configuration ...
    
    # Camoufox dependencies (only created if needed)
    oxylabs_provider = providers.Singleton(
        OxylabsProvider,
        config=config.get('oxylabs', {})
    )
    
    proxy_manager = providers.Singleton(
        ProxyManager,
        config=config.get('proxy', {}),
        logger=logger,
        proxy_provider=oxylabs_provider
    )
    
    fingerprint_manager = providers.Singleton(
        FingerprintManager,
        config=config.get('camoufox', {}).get('fingerprints', {}),
        logger=logger
    )
    
    # Use factory pattern for session manager
    session_manager = providers.Factory(
        SessionManagerFactory.create,
        config=config,
        logger=logger,
        fingerprint_manager=fingerprint_manager,
        proxy_manager=proxy_manager
    )
    
    # Use factory pattern for API client
    api_client = providers.Factory(
        APIClientFactory.create,
        session_manager=session_manager,
        config=config,
        logger=logger
    )
    
    # ... rest of the container remains the same ...
```

## Migration Strategy

### Phase 1: Foundation (Week 1-2)
1. Implement abstract base classes
2. Create Camoufox implementations
3. Set up factory pattern
4. Update container configuration
5. Add feature flag controller

### Phase 2: Testing (Week 3-4)
1. Unit tests for new components
2. Integration tests with feature flag variations
3. Performance benchmarking
4. A/B testing framework setup

### Phase 3: Gradual Rollout (Week 5-8)
1. Deploy with `use_camoufox: false` (0% traffic)
2. Enable for specific test firms (whitelist)
3. Increase to 5% traffic, monitor metrics
4. Gradually increase: 10% → 25% → 50% → 100%
5. Monitor success rates, performance, and detection rates

### Phase 4: Cleanup (Week 9-10)
1. Remove legacy code paths
2. Simplify factories to direct instantiation
3. Update documentation
4. Archive legacy implementations

## Configuration Updates

### Complete fb_ads.yml Update

```yaml
# Add to config/fb_ads.yml
feature_flags:
  # ... existing flags ...
  use_camoufox: false  # Master switch for Camoufox
  camoufox_migration:
    enabled: true
    percentage: 0  # Start at 0%
    whitelist_firms: []  # Test firms first
    blacklist_firms: []  # Exclude problematic firms
    metrics:
      log_performance: true
      log_success_rates: true
      comparison_mode: true  # Run both and compare

# Camoufox configuration
camoufox:
  browser:
    headless: true
    max_browsers: 3
    timeout: 30000
    viewport:
      width: 1920
      height: 1080
  
  session:
    min_duration_minutes: 3
    max_duration_minutes: 5
    refresh_before_expiry_seconds: 30
    
  fingerprints:
    rotation_interval: 10
    profiles:
      - os: Windows
        browsers: [Chrome]
        version_range: [115, 124]
      - os: macOS  
        browsers: [Chrome]
        version_range: [115, 124]
        
  anti_bot:
    humanize: true
    mouse_curves: true
    typing_variation: true
    scroll_behavior: natural
    
  verification:
    enabled: true
    test_on_startup: true
    test_interval: 100  # Every N browser sessions
    min_uniqueness_score: 70
    test_urls:
      - "https://camoufox.com/fingerprint/"
      
  proxy:
    # Inherits from main proxy configuration
    inherit_oxylabs_config: true
    rotation_per_session: true
```

## Testing Strategy

### 1. Unit Tests

```python
# tests/unit/fb_ads/test_camoufox_session_manager.py
import pytest
from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

@pytest.mark.asyncio
async def test_camoufox_session_creation():
    """Test Camoufox session manager creates sessions."""
    config = {
        'camoufox': {
            'browser': {'headless': True},
            'session': {'max_duration_minutes': 5}
        }
    }
    
    manager = CamoufoxSessionManager(config, logger, fingerprint_manager, proxy_manager)
    
    success = await manager.create_new_session()
    assert success
    
    session_data = await manager.get_session_data()
    assert 'fb_dtsg' in session_data
    
    await manager.cleanup()

# tests/unit/fb_ads/test_feature_flag_controller.py
def test_feature_flag_percentage_rollout():
    """Test percentage-based rollout logic."""
    config = {
        'feature_flags': {
            'use_camoufox': True,
            'camoufox_migration': {
                'enabled': True,
                'percentage': 50
            }
        }
    }
    
    controller = FeatureFlagController(config)
    
    # Test with multiple firm IDs
    results = []
    for i in range(1000):
        firm_id = f"firm_{i}"
        results.append(controller.should_use_camoufox(firm_id))
    
    # Should be roughly 50%
    camoufox_count = sum(results)
    assert 450 <= camoufox_count <= 550
```

### 2. Integration Tests

```python
# tests/integration/fb_ads/test_camoufox_integration.py
@pytest.mark.integration
async def test_camoufox_facebook_integration():
    """Test full integration with Facebook."""
    container = FbAdsContainer()
    container.config.from_dict({
        'feature_flags': {
            'use_camoufox': True,
            'camoufox_migration': {'enabled': True, 'percentage': 100}
        },
        'camoufox': {
            'browser': {'headless': True}
        }
    })
    
    session_manager = container.session_manager()
    api_client = container.api_client()
    
    # Test company search
    result = await api_client.search_company_by_name("Morgan & Morgan")
    assert result is not None
    
    # Cleanup
    await session_manager.cleanup()
```

### 3. A/B Testing Framework

```python
# src/services/fb_ads/metrics/ab_test_metrics.py
class ABTestMetrics:
    """Collects metrics for A/B testing."""
    
    def __init__(self, logger):
        self.logger = logger
        self.metrics = {
            'legacy': {'success': 0, 'failure': 0, 'duration': []},
            'camoufox': {'success': 0, 'failure': 0, 'duration': []}
        }
        
    def record_result(self, implementation: str, success: bool, duration: float):
        """Record result for A/B testing."""
        if implementation in self.metrics:
            if success:
                self.metrics[implementation]['success'] += 1
            else:
                self.metrics[implementation]['failure'] += 1
            self.metrics[implementation]['duration'].append(duration)
            
    def get_summary(self) -> Dict[str, Any]:
        """Get summary statistics."""
        summary = {}
        
        for impl, data in self.metrics.items():
            total = data['success'] + data['failure']
            success_rate = data['success'] / total if total > 0 else 0
            avg_duration = sum(data['duration']) / len(data['duration']) if data['duration'] else 0
            
            summary[impl] = {
                'total': total,
                'success_rate': success_rate,
                'avg_duration': avg_duration
            }
            
        return summary
```

## Rollback Plan

### Immediate Rollback
1. Set `use_camoufox: false` in configuration
2. All traffic immediately routes to legacy implementation
3. No code deployment required

### Partial Rollback
1. Reduce `percentage` value gradually
2. Add problematic firms to `blacklist_firms`
3. Monitor legacy path performance

### Emergency Procedures
1. Feature flag override via environment variable
2. Circuit breaker pattern for automatic fallback
3. Real-time monitoring alerts

### Rollback Checklist
- [ ] Update configuration flag
- [ ] Verify traffic routing to legacy path
- [ ] Check error rates returning to normal
- [ ] Document rollback reason
- [ ] Plan fixes for issues encountered

## Success Metrics

### Performance Metrics
1. **Response Time**: Camoufox ≤ 10s per page (vs current ~5s)
2. **Success Rate**: ≥95% successful scrapes
3. **Detection Rate**: <5% (improvement from current ~30%)
4. **Resource Usage**: <200MB per browser instance

### Quality Metrics
1. **Data Completeness**: 100% parity with legacy system
2. **Token Extraction**: 100% success rate
3. **Session Stability**: <1% unexpected session drops

### Operational Metrics
1. **Rollout Duration**: 8-10 weeks total
2. **Incident Rate**: <2 rollbacks during migration
3. **A/B Test Confidence**: >95% statistical significance

## Conclusion

This design provides a safe, gradual migration path from the current `requests`-based implementation to a Camoufox-based browser automation system. The feature flag approach allows for:

1. **Risk Mitigation**: Instant rollback capability
2. **Gradual Validation**: Test with small traffic percentages
3. **A/B Testing**: Direct comparison of implementations
4. **Zero Downtime**: No service interruption during migration

The implementation maintains backward compatibility while providing superior anti-detection capabilities through Camoufox's advanced browser fingerprinting and automation features.