#!/bin/bash

# <PERSON>ript to stop keychain suppression

echo "🔓 Stopping keychain suppression..."

# Kill the auto-deny script if it's running
if [ -f /tmp/keychain_suppression_pid ]; then
    PID=$(cat /tmp/keychain_suppression_pid)
    kill $PID 2>/dev/null || true
    rm /tmp/keychain_suppression_pid
    echo "✅ Stopped auto-deny script (PID: $PID)"
fi

# Clean up temporary files
rm -f /tmp/auto_deny_keychain.applescript

# Unset environment variables
launchctl unsetenv WEBKIT_DISABLE_TCC_PROMPT
launchctl unsetenv WEBKIT_DISABLE_KEYCHAIN
launchctl unsetenv CHROME_DISABLE_KEYCHAIN
launchctl unsetenv CHROMIUM_NO_KEYCHAIN

echo "🔓 Keychain suppression stopped"