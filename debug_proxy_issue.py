#!/usr/bin/env python3
"""
Debug script to test proxy authentication with Camoufox.
Tests the exact format that should work based on user feedback.
"""

import asyncio
import logging
import urllib.parse

from camoufox.async_api import AsyncCamoufox

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_working_format():
    """Test with the format that the user confirmed works."""
    # Based on user feedback: https:// works
    # Using the exact format from the logs that should work
    
    proxy_config = {
        'server': 'https://pr.oxylabs.io:7777',
        'username': 'customer-lexgeniusmob20250612_7jRlZ-cc-us-sessid-1234567890-sesstime-10',
        'password': '24gbfygq=RBhM2+'  # Raw password, not URL-encoded
    }
    
    logger.info("Testing with confirmed working format:")
    logger.info(f"  Server: {proxy_config['server']}")
    logger.info(f"  Username: {proxy_config['username']}")
    logger.info(f"  Password: [REDACTED]")
    
    browser_args = {
        'headless': True,
        'humanize': True,
        'geoip': True,
        'addons': [],
        'proxy': proxy_config
    }
    
    try:
        browser = AsyncCamoufox(**browser_args)
        await browser.start()
        logger.info("✅ Browser started successfully")
        
        # Try to navigate
        context = await browser.browser.new_context()
        page = await context.new_page()
        
        logger.info("Attempting navigation to Facebook...")
        await page.goto('https://www.facebook.com/ads/library/', wait_until='domcontentloaded', timeout=30000)
        logger.info("✅ Navigation successful!")
        
        # Wait a bit to see if we get the page
        await asyncio.sleep(5)
        
        # Check if we can find the search input
        search_input = await page.query_selector('input[placeholder*="Search"]')
        if search_input:
            logger.info("✅ Found search input - proxy is working correctly!")
        else:
            logger.warning("⚠️ Could not find search input - page might not have loaded correctly")
        
        await context.close()
        await browser.stop()
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed: {str(e)}")
        if "NS_ERROR_PROXY_CONNECTION_REFUSED" in str(e):
            logger.error("   -> Proxy connection refused")
            logger.error("   -> This suggests the proxy credentials or format are incorrect")
        return False

async def test_url_encoded_password():
    """Test with URL-encoded password to see if that's the issue."""
    # Test with URL-encoded password
    password = urllib.parse.quote('24gbfygq=RBhM2+', safe='')
    
    proxy_config = {
        'server': 'https://pr.oxylabs.io:7777',
        'username': 'customer-lexgeniusmob20250612_7jRlZ-cc-us-sessid-1234567890-sesstime-10',
        'password': password  # URL-encoded
    }
    
    logger.info("\nTesting with URL-encoded password:")
    logger.info(f"  Server: {proxy_config['server']}")
    logger.info(f"  Username: {proxy_config['username']}")
    logger.info(f"  Password (encoded): {password}")
    
    browser_args = {
        'headless': True,
        'humanize': True,
        'geoip': True,
        'addons': [],
        'proxy': proxy_config
    }
    
    try:
        browser = AsyncCamoufox(**browser_args)
        await browser.start()
        logger.info("✅ Browser started successfully")
        
        # Try to navigate
        context = await browser.browser.new_context()
        page = await context.new_page()
        
        logger.info("Attempting navigation to Facebook...")
        await page.goto('https://www.facebook.com/ads/library/', wait_until='domcontentloaded', timeout=30000)
        logger.info("✅ Navigation successful with URL-encoded password!")
        
        await context.close()
        await browser.stop()
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed with URL-encoded password: {str(e)}")
        return False

async def main():
    """Run tests."""
    logger.info("🚀 Starting Camoufox proxy debugging")
    logger.info("="*60)
    
    # Test 1: Raw password with https://
    result1 = await test_working_format()
    
    await asyncio.sleep(2)
    
    # Test 2: URL-encoded password with https://
    result2 = await test_url_encoded_password()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 SUMMARY:")
    logger.info(f"  Raw password with https://: {'✅ SUCCESS' if result1 else '❌ FAILED'}")
    logger.info(f"  URL-encoded password with https://: {'✅ SUCCESS' if result2 else '❌ FAILED'}")
    logger.info("="*60)
    
    if result1 and not result2:
        logger.info("\n💡 The issue is URL encoding! Use raw password.")
    elif result2 and not result1:
        logger.info("\n💡 URL encoding is required!")
    elif not result1 and not result2:
        logger.info("\n❌ Both formats failed - check proxy credentials or Camoufox setup")

if __name__ == "__main__":
    asyncio.run(main())