#!/usr/bin/env bash

# Script to run reports for a date range backwards
# From June 26, 2025 to June 14, 2025

# Starting and ending dates in YYYYMMDD format
START_DATE=20250626
END_DATE=20250614

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
CONFIG_DIR="$SCRIPT_DIR/config"
TEMP_DIR="/tmp/lexgenius_batch_$$"

# Create temp directory
mkdir -p "$TEMP_DIR"

echo "Running reports from $START_DATE to $END_DATE (backwards)"
echo "=========================================="

# Convert dates to seconds since epoch for easier arithmetic
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS date command
    current_epoch=$(date -j -f "%Y%m%d" "$START_DATE" "+%s")
    end_epoch=$(date -j -f "%Y%m%d" "$END_DATE" "+%s")
else
    # Linux date command
    current_epoch=$(date -d "$START_DATE" +%s)
    end_epoch=$(date -d "$END_DATE" +%s)
fi

# Iterate backwards day by day
while [ $current_epoch -ge $end_epoch ]; do
    # Convert epoch back to YYYYMMDD and MM/DD/YY
    if [[ "$OSTYPE" == "darwin"* ]]; then
        current_date=$(date -j -f "%s" "$current_epoch" "+%Y%m%d")
        formatted_date=$(date -j -f "%s" "$current_epoch" "+%m/%d/%y")
    else
        current_date=$(date -d "@$current_epoch" "+%Y%m%d")
        formatted_date=$(date -d "@$current_epoch" "+%m/%d/%y")
    fi
    
    echo ""
    echo "Processing date: $current_date ($formatted_date)"
    echo "----------------------------"
    
    # Create temporary config file with the specific date
    temp_config="$TEMP_DIR/report.yml"
    sed "s|date: '.*'|date: '$formatted_date'|g" "$CONFIG_DIR/report.yml" > "$temp_config"
    
    # Run the pipeline with the temporary config
    cd "$SCRIPT_DIR" && python3 src/main.py --params "$temp_config"
    
    # Check if the command was successful
    if [ $? -eq 0 ]; then
        echo "✓ Successfully processed report for $current_date"
    else
        echo "✗ Error processing report for $current_date"
        # Optionally stop on error - uncomment the next line if desired
        # exit 1
    fi
    
    # Clean up temp config
    rm -f "$temp_config"
    
    # Subtract one day (86400 seconds)
    current_epoch=$((current_epoch - 86400))
done

# Clean up temp directory
rmdir "$TEMP_DIR" 2>/dev/null || true

echo ""
echo "=========================================="
echo "Batch report generation completed!"