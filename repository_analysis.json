[{"file_path": "src/repositories/pacer_dockets_repository.py", "class_name": "PacerDocketsRepository", "constructors": [{"name": "__init__", "parameters": [{"name": "storage", "type": "AsyncDynamoDBStorage", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}], "required_parameters": ["storage"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "storage", "type": "AsyncDynamoDBStorage", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}], "errors": []}, {"file_path": "src/repositories/pacer_repository.py", "class_name": "PacerRepository", "constructors": [{"name": "__init__", "parameters": [{"name": "storage", "type": "DynamoDBStorage", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}], "required_parameters": ["storage"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "storage", "type": "DynamoDBStorage", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}], "errors": []}, {"file_path": "src/repositories/law_firms_repository.py", "class_name": "LawFirmsRepository", "constructors": [{"name": "__init__", "parameters": [{"name": "storage", "type": "AsyncDynamoDBStorage", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}], "required_parameters": ["storage"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "storage", "type": "AsyncDynamoDBStorage", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}], "errors": []}, {"file_path": "src/repositories/district_courts_repository.py", "class_name": "DistrictCourtsRepository", "constructors": [{"name": "__init__", "parameters": [{"name": "storage", "type": "AsyncDynamoDBStorage", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}], "required_parameters": ["storage"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "storage", "type": "AsyncDynamoDBStorage", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}], "errors": []}, {"file_path": "src/repositories/fb_archive_repository.py", "class_name": "FBArchiveRepository", "constructors": [{"name": "__init__", "parameters": [{"name": "storage", "type": "AsyncDynamoDBStorage", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}], "required_parameters": ["storage"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "storage", "type": "AsyncDynamoDBStorage", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}], "errors": []}, {"file_path": "src/repositories/fb_image_hash_repository.py", "class_name": "FBImageHashRepository", "constructors": [{"name": "__init__", "parameters": [{"name": "storage", "type": "AsyncDynamoDBStorage", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}], "required_parameters": ["storage"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "storage", "type": "AsyncDynamoDBStorage", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}], "errors": []}]