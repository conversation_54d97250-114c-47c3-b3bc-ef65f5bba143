#!/usr/bin/env python3
import json

# Load the data
with open('/Users/<USER>/PycharmProjects/lexgenius/data/20250625/reports/fb_ads_cache_20250625.json', 'r') as f:
    data = json.load(f)

ads = data.get('data', [])
xyz_ads = [ad for ad in ads if ad.get('summary') == 'XYZ Corporation Data Breach Lawsuit']

print(f"Found {len(xyz_ads)} ads with 'XYZ Corporation Data Breach Lawsuit' summary")
print("-" * 80)

for i, ad in enumerate(xyz_ads[:5], 1):
    print(f"\nAd #{i}:")
    print(f"  Ad ID: {ad.get('ad_archive_id')}")
    print(f"  Page: {ad.get('page_name')}")
    print(f"  Title: {ad.get('title', 'N/A')}")
    print(f"  Body: {ad.get('body', 'N/A')[:200]}...")
    print(f"  Link Description: {ad.get('link_description', 'N/A')}")
    print(f"  Caption: {ad.get('caption', 'N/A')}")
    print(f"  CTA Text: {ad.get('cta_text', 'N/A')}")
    print(f"  LLM: {ad.get('llm', 'N/A')}")
    print(f"  Law Firm: {ad.get('law_firm', 'N/A')}")
    print("-" * 50)