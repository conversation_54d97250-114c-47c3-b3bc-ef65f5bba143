#!/usr/bin/env python3
"""
Debug script to test the report service data loading for the specific case.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from datetime import datetime

def test_title_processing():
    """Test the title processing logic from the report service."""
    
    print("=== TESTING REPORT SERVICE TITLE PROCESSING ===")
    
    # Test different scenarios that might cause "Unknown Title"
    test_cases = [
        # Case 1: Valid title (should be preserved)
        {
            "name": "Valid Title",
            "data": {"title": "AngioDynamics Inc. and Navilyst Medical Inc. Port Catheter Products Liability Litigation"},
            "expected": "AngioDynamics Inc. and Navilyst Medical Inc. Port Catheter Products Liability Litigation"
        },
        # Case 2: Empty string
        {
            "name": "Empty String",
            "data": {"title": ""},
            "expected": "Unknown Title"
        },
        # Case 3: None value
        {
            "name": "None Value",
            "data": {"title": None},
            "expected": "Unknown Title"
        },
        # Case 4: NaN value
        {
            "name": "NaN Value",
            "data": {"title": float('nan')},
            "expected": "Unknown Title"
        },
        # Case 5: String 'nan'
        {
            "name": "String 'nan'",
            "data": {"title": "nan"},
            "expected": "Unknown Title"
        },
        # Case 6: Whitespace only
        {
            "name": "Whitespace Only",
            "data": {"title": "   "},
            "expected": "Unknown Title"
        },
        # Case 7: String 'None'
        {
            "name": "String 'None'",
            "data": {"title": "None"},
            "expected": "None"  # This should NOT be replaced
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- Testing: {test_case['name']} ---")
        
        # Create DataFrame
        df = pd.DataFrame([test_case['data']])
        
        print(f"Input: {repr(test_case['data']['title'])}")
        print(f"Input type: {type(test_case['data']['title'])}")
        
        # Apply the cleaning logic from data_loader_service.py
        df['title'] = df['title'].replace({float('nan'): None, 'nan': None}).fillna('').astype(str).str.strip()
        df.loc[df['title'].isin(['', 'nan']), 'title'] = 'Unknown Title'
        
        result = df['title'].iloc[0]
        print(f"Result: '{result}'")
        print(f"Expected: '{test_case['expected']}'")
        print(f"Match: {'✅' if result == test_case['expected'] else '❌'}")
        
        if result != test_case['expected']:
            print(f"❌ MISMATCH: Expected '{test_case['expected']}' but got '{result}'")

def test_actual_data_processing():
    """Test processing with the actual data structure."""
    
    print(f"\n=== TESTING WITH ACTUAL DATA STRUCTURE ===")
    
    # Simulate the actual data that would come from DynamoDB after field conversion
    actual_data = {
        'title': 'AngioDynamics Inc. and Navilyst Medical Inc. Port Catheter Products Liability Litigation',
        'law_firm': 'Dickerson Oxton LLC',
        'filing_date': '20250714',
        'docket_num': '3:25-cv-01789',
        'court_id': 'casd',
        'added_on': '20250714',
        'versus': 'Weber  v. Angiodynamics, Inc. et al'
    }
    
    # Create DataFrame as the service would
    df = pd.DataFrame([actual_data])
    
    print(f"Original title: '{df['title'].iloc[0]}'")
    print(f"Title type: {type(df['title'].iloc[0])}")
    print(f"Title length: {len(df['title'].iloc[0])}")
    
    # Apply the full cleaning process from _clean_docket_data
    
    # Handle title: properly handle NaN values before string conversion
    df['title'] = df['title'].replace({float('nan'): None, 'nan': None}).fillna('').astype(str).str.strip()
    df.loc[df['title'].isin(['', 'nan']), 'title'] = 'Unknown Title'
    
    print(f"After cleaning: '{df['title'].iloc[0]}'")
    
    # Test the filtering conditions from processing_service.py
    print(f"\n--- Testing Filtering Conditions ---")
    is_unknown_title = df['title'].iloc[0] == 'Unknown Title'
    print(f"Is 'Unknown Title': {is_unknown_title}")
    
    if is_unknown_title:
        print("❌ PROBLEM: Title was converted to 'Unknown Title' during processing")
    else:
        print("✅ GOOD: Title was preserved correctly")

if __name__ == "__main__":
    test_title_processing()
    test_actual_data_processing()