"""
Example FastAPI application with full monitoring integration.
"""

from fastapi import FastAPI, HTTPException
import asyncio
import random
from datetime import datetime

# Import monitoring components
from src.infrastructure.monitoring.app_integration import init_fastapi_monitoring
from src.infrastructure.monitoring.health import <PERSON><PERSON><PERSON><PERSON>, ComponentHealth, HealthStatus
from src.infrastructure.monitoring.metrics import (
    track_pacer_scrape,
    track_fb_ads_processed,
    track_transformer_document,
    fb_ads_queue_size
)
from src.infrastructure.monitoring.tracing import trace_method, create_pacer_span
from src.infrastructure.monitoring.structured_logging import log_operation

# Create FastAPI app
app = FastAPI(title="LexGenius API", version="1.0.0")

# Create health checker
health_checker = HealthChecker("lexgenius-api", "1.0.0")

# Add health checks
async def check_database():
    """Simulate database health check."""
    await asyncio.sleep(0.1)  # Simulate DB query
    return ComponentHealth(
        name="database",
        status=HealthStatus.HEALTHY,
        metadata={"connections": 10, "active_queries": 2}
    )

async def check_redis():
    """Simulate Redis health check."""
    await asyncio.sleep(0.05)  # Simulate Redis ping
    return ComponentHealth(
        name="redis",
        status=HealthStatus.HEALTHY,
        metadata={"memory_usage": "256MB", "connected_clients": 5}
    )

async def check_s3():
    """Simulate S3 health check."""
    await asyncio.sleep(0.2)  # Simulate S3 head bucket
    return ComponentHealth(
        name="s3",
        status=HealthStatus.HEALTHY,
        metadata={"bucket": "lexgenius-documents"}
    )

# Register health checks
health_checker.add_check("database", check_database)
health_checker.add_check("redis", check_redis)
health_checker.add_check("s3", check_s3)

# Initialize monitoring
logger = init_fastapi_monitoring(
    app=app,
    service_name="lexgenius-api",
    version="1.0.0",
    environment="production",
    health_checker=health_checker,
    enable_tracing=True
)

# Example endpoints with monitoring

@app.get("/")
async def root():
    """Root endpoint."""
    logger.info("Root endpoint accessed")
    return {"message": "LexGenius API", "timestamp": datetime.utcnow().isoformat()}


@app.post("/pacer/scrape/{court}")
@trace_method(name="pacer.scrape_court")
async def scrape_pacer_court(court: str, case_numbers: list[str]):
    """Scrape PACER documents for a court."""
    logger.info(f"Starting PACER scrape for court: {court}", extra={
        "court": court,
        "case_count": len(case_numbers)
    })
    
    results = []
    
    for case_number in case_numbers:
        # Create span for each case
        with create_pacer_span("scrape_case", court, case_number) as span:
            try:
                # Simulate scraping
                await asyncio.sleep(random.uniform(0.5, 2.0))
                
                # Simulate success/failure
                success = random.random() > 0.1  # 90% success rate
                
                if success:
                    track_pacer_scrape(court, True)
                    result = {
                        "case_number": case_number,
                        "status": "success",
                        "documents": random.randint(1, 10)
                    }
                else:
                    error_type = random.choice(["timeout", "auth_failed", "not_found"])
                    track_pacer_scrape(court, False, error_type)
                    result = {
                        "case_number": case_number,
                        "status": "failed",
                        "error": error_type
                    }
                
                results.append(result)
                
            except Exception as e:
                span.record_exception(e)
                raise
    
    return {"court": court, "results": results}


@app.post("/fb-ads/process")
@log_operation(logger, "process_fb_ads")
async def process_fb_ads(law_firm: str, ad_ids: list[str]):
    """Process Facebook ads for a law firm."""
    # Update queue size metric
    fb_ads_queue_size.set(len(ad_ids))
    
    results = []
    
    for ad_id in ad_ids:
        # Simulate processing
        await asyncio.sleep(random.uniform(0.1, 0.5))
        
        # Simulate success/failure
        success = random.random() > 0.05  # 95% success rate
        
        track_fb_ads_processed(law_firm, success)
        
        results.append({
            "ad_id": ad_id,
            "status": "processed" if success else "failed"
        })
    
    # Clear queue
    fb_ads_queue_size.set(0)
    
    return {"law_firm": law_firm, "results": results}


@app.post("/transformer/process")
async def process_documents(documents: list[dict]):
    """Process documents through transformer."""
    results = []
    
    for doc in documents:
        doc_type = doc.get("type", "unknown")
        doc_id = doc.get("id")
        
        # Simulate processing
        await asyncio.sleep(random.uniform(0.2, 1.0))
        
        # Simulate success/failure
        success = random.random() > 0.02  # 98% success rate
        
        track_transformer_document(doc_type, success)
        
        results.append({
            "document_id": doc_id,
            "type": doc_type,
            "status": "transformed" if success else "failed"
        })
    
    return {"results": results}


@app.get("/simulate-error")
async def simulate_error():
    """Endpoint to simulate an error for testing."""
    logger.error("Simulating an error condition", extra={
        "error_type": "test_error",
        "simulation": True
    })
    
    raise HTTPException(status_code=500, detail="Simulated error for testing")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)