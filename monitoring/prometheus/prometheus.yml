global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'lexgenius-monitor'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alerts.yml"

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
        labels:
          service: 'prometheus'

  # Node exporter for system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
        labels:
          service: 'node-exporter'

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
        labels:
          service: 'cadvisor'

  # LexGenius application metrics
  - job_name: 'lexgenius-app'
    static_configs:
      - targets: ['host.docker.internal:8000']
        labels:
          service: 'lexgenius-api'
          component: 'main'
    metrics_path: '/metrics'

  # LexGenius PACER service metrics
  - job_name: 'lexgenius-pacer'
    static_configs:
      - targets: ['host.docker.internal:8001']
        labels:
          service: 'lexgenius-pacer'
          component: 'scraper'
    metrics_path: '/metrics'

  # LexGenius FB Ads service metrics
  - job_name: 'lexgenius-fb-ads'
    static_configs:
      - targets: ['host.docker.internal:8002']
        labels:
          service: 'lexgenius-fb-ads'
          component: 'processor'
    metrics_path: '/metrics'

  # LexGenius Transformer service metrics
  - job_name: 'lexgenius-transformer'
    static_configs:
      - targets: ['host.docker.internal:8003']
        labels:
          service: 'lexgenius-transformer'
          component: 'data-processing'
    metrics_path: '/metrics'

  # LexGenius Reports service metrics
  - job_name: 'lexgenius-reports'
    static_configs:
      - targets: ['host.docker.internal:8004']
        labels:
          service: 'lexgenius-reports'
          component: 'generator'
    metrics_path: '/metrics'

  # AWS S3 exporter
  - job_name: 's3-exporter'
    static_configs:
      - targets: ['host.docker.internal:9340']
        labels:
          service: 's3-exporter'
          storage: 'aws-s3'

  # DynamoDB exporter
  - job_name: 'dynamodb-exporter'
    static_configs:
      - targets: ['host.docker.internal:9042']
        labels:
          service: 'dynamodb-exporter'
          storage: 'aws-dynamodb'

  # Custom business metrics exporter
  - job_name: 'business-metrics'
    static_configs:
      - targets: ['host.docker.internal:9341']
        labels:
          service: 'business-exporter'
          type: 'kpi'
    scrape_interval: 60s  # Less frequent for business metrics