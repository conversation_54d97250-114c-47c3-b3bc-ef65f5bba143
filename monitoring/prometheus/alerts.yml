groups:
  - name: lexgenius_system_alerts
    interval: 30s
    rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% (current value: {{ $value }}%)"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% (current value: {{ $value }}%)"

      # Disk space low
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"})) * 100 > 80
        for: 10m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk usage is above 80% (current value: {{ $value }}%)"

  - name: lexgenius_application_alerts
    interval: 30s
    rules:
      # Service down
      - alert: ServiceDown
        expr: up{job=~"lexgenius-.*"} == 0
        for: 2m
        labels:
          severity: critical
          service: application
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "{{ $labels.job }} has been down for more than 2 minutes"

      # High error rate
      - alert: HighErrorRate
        expr: |
          (
            sum(rate(lexgenius_http_requests_total{status=~"5.."}[5m])) by (service)
            /
            sum(rate(lexgenius_http_requests_total[5m])) by (service)
          ) > 0.05
        for: 5m
        labels:
          severity: warning
          service: application
        annotations:
          summary: "High error rate in {{ $labels.service }}"
          description: "Error rate is above 5% (current value: {{ $value }}%)"

      # High response time
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95,
            sum(rate(lexgenius_http_request_duration_seconds_bucket[5m])) by (service, le)
          ) > 2
        for: 5m
        labels:
          severity: warning
          service: application
        annotations:
          summary: "High response time in {{ $labels.service }}"
          description: "95th percentile response time is above 2s (current value: {{ $value }}s)"

  - name: lexgenius_business_alerts
    interval: 60s
    rules:
      # PACER scraping failures
      - alert: PacerScrapingFailures
        expr: |
          (
            sum(rate(lexgenius_pacer_scrape_failures_total[1h]))
            /
            sum(rate(lexgenius_pacer_scrape_attempts_total[1h]))
          ) > 0.1
        for: 15m
        labels:
          severity: warning
          service: pacer
        annotations:
          summary: "High PACER scraping failure rate"
          description: "PACER scraping failure rate is above 10% (current value: {{ $value }}%)"

      # FB Ads processing backlog
      - alert: FbAdsProcessingBacklog
        expr: lexgenius_fb_ads_queue_size > 1000
        for: 30m
        labels:
          severity: warning
          service: fb-ads
        annotations:
          summary: "Large FB Ads processing backlog"
          description: "FB Ads queue has more than 1000 items (current value: {{ $value }})"

      # Transformer processing slow
      - alert: TransformerProcessingSlow
        expr: |
          rate(lexgenius_transformer_documents_processed_total[1h]) < 10
        for: 30m
        labels:
          severity: warning
          service: transformer
        annotations:
          summary: "Transformer processing rate low"
          description: "Processing less than 10 documents per hour (current rate: {{ $value }})"

      # Reports generation failed
      - alert: ReportsGenerationFailed
        expr: increase(lexgenius_reports_generation_failures_total[1h]) > 5
        for: 10m
        labels:
          severity: critical
          service: reports
        annotations:
          summary: "Multiple report generation failures"
          description: "More than 5 report generation failures in the last hour"

  - name: lexgenius_storage_alerts
    interval: 60s
    rules:
      # S3 upload failures
      - alert: S3UploadFailures
        expr: |
          (
            sum(rate(lexgenius_s3_upload_failures_total[10m]))
            /
            sum(rate(lexgenius_s3_upload_attempts_total[10m]))
          ) > 0.05
        for: 15m
        labels:
          severity: warning
          service: storage
        annotations:
          summary: "High S3 upload failure rate"
          description: "S3 upload failure rate is above 5% (current value: {{ $value }}%)"

      # DynamoDB throttling
      - alert: DynamoDBThrottling
        expr: sum(rate(lexgenius_dynamodb_throttled_requests_total[5m])) > 10
        for: 10m
        labels:
          severity: warning
          service: storage
        annotations:
          summary: "DynamoDB throttling detected"
          description: "DynamoDB is throttling requests (current rate: {{ $value }} req/s)"

  - name: lexgenius_security_alerts
    interval: 30s
    rules:
      # Authentication failures
      - alert: HighAuthenticationFailures
        expr: sum(rate(lexgenius_auth_failures_total[5m])) > 10
        for: 5m
        labels:
          severity: critical
          service: security
        annotations:
          summary: "High authentication failure rate"
          description: "More than 10 authentication failures per second"

      # Suspicious activity
      - alert: SuspiciousActivity
        expr: |
          sum(rate(lexgenius_suspicious_requests_total[5m])) by (source_ip) > 100
        for: 5m
        labels:
          severity: critical
          service: security
        annotations:
          summary: "Suspicious activity from {{ $labels.source_ip }}"
          description: "High rate of suspicious requests from IP {{ $labels.source_ip }}"