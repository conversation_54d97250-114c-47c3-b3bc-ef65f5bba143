global:
  resolve_timeout: 5m
  smtp_from: '<EMAIL>'
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-smtp-password'

route:
  group_by: ['alertname', 'service', 'severity']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h
  receiver: 'default'
  
  routes:
    # Critical alerts go to PagerDuty
    - match:
        severity: critical
      receiver: 'pagerduty'
      continue: true
    
    # All alerts also go to Slack
    - match_re:
        severity: warning|critical
      receiver: 'slack'
      continue: true
    
    # Security alerts go to security team
    - match:
        service: security
      receiver: 'security-team'

receivers:
  - name: 'default'
    email_configs:
      - to: '<EMAIL>'
        headers:
          Subject: 'LexGenius Alert: {{ .GroupLabels.alertname }}'

  - name: 'pagerduty'
    pagerduty_configs:
      - service_key: 'your-pagerduty-service-key'
        description: '{{ .GroupLabels.alertname }} - {{ .GroupLabels.service }}'
        
  - name: 'slack'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts'
        title: 'LexGenius Alert'
        text: |
          *Alert:* {{ .GroupLabels.alertname }}
          *Service:* {{ .GroupLabels.service }}
          *Severity:* {{ .GroupLabels.severity }}
          *Summary:* {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}
        send_resolved: true

  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>'
        headers:
          Subject: 'SECURITY ALERT: {{ .GroupLabels.alertname }}'

inhibit_rules:
  # Inhibit less severe alerts if critical alert is firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service']