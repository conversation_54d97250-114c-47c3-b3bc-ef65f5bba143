# LexGenius Monitoring Infrastructure

This directory contains the complete monitoring infrastructure for LexGenius, including metrics collection, distributed tracing, log aggregation, and alerting.

## 🚀 Quick Start

1. **Install Python dependencies:**
   ```bash
   pip install -r monitoring/requirements.txt
   ```

2. **Start the monitoring stack:**
   ```bash
   ./monitoring/scripts/start_monitoring.sh
   ```

3. **Access the dashboards:**
   - Prometheus: http://localhost:9090
   - Grafana: http://localhost:3000 (admin/lexgenius-monitor)
   - Jaeger: http://localhost:16686
   - Alertmanager: http://localhost:9093

## 📊 Components

### Metrics Collection (Prometheus)
- System metrics via Node Exporter
- Container metrics via cAdvisor
- Application metrics via custom instrumentation
- Business KPIs via custom exporter

### Visualization (Grafana)
- System Overview Dashboard
- Business Metrics Dashboard
- Custom dashboards for each service

### Distributed Tracing (Jaeger)
- Request flow visualization
- Performance bottleneck identification
- Cross-service correlation

### Log Aggregation (Loki + Promtail)
- Centralized log collection
- Structured JSON logging
- Correlation ID tracking

### Alerting (Alertmanager)
- System alerts (CPU, memory, disk)
- Application alerts (errors, latency)
- Business alerts (processing failures)
- Multi-channel notifications (email, Slack, PagerDuty)

## 🔧 Integration Guide

### Python Service Integration

```python
from fastapi import FastAPI
from src.infrastructure.monitoring.app_integration import init_fastapi_monitoring
from src.infrastructure.monitoring.health import HealthChecker

# Create app
app = FastAPI()

# Create health checker
health_checker = HealthChecker("my-service", "1.0.0")

# Initialize monitoring
logger = init_fastapi_monitoring(
    app=app,
    service_name="my-service",
    version="1.0.0",
    health_checker=health_checker
)

# Your endpoints will now have automatic:
# - Prometheus metrics at /metrics
# - Health checks at /health
# - Distributed tracing
# - Structured logging with correlation IDs
```

### Custom Metrics

```python
from src.infrastructure.monitoring.metrics import (
    track_pacer_scrape,
    track_fb_ads_processed,
    track_transformer_document
)

# Track business operations
track_pacer_scrape(court="TXND", success=True)
track_fb_ads_processed(law_firm="Smith & Associates", success=True)
track_transformer_document(doc_type="docket", success=True)
```

### Distributed Tracing

```python
from src.infrastructure.monitoring.tracing import trace_method, trace_database_operation

@trace_method(name="process_document")
async def process_document(doc_id: str):
    # Automatically traced
    pass

@trace_database_operation("select", table="cases")
async def get_case(case_id: str):
    # Database operations traced
    pass
```

### Structured Logging

```python
# Logger automatically includes:
# - Correlation IDs
# - Service metadata
# - JSON formatting

logger.info("Processing document", extra={
    "document_id": doc_id,
    "document_type": "docket",
    "court": "TXND"
})
```

## 📁 Directory Structure

```
monitoring/
├── docker/
│   └── docker-compose.yml      # Container orchestration
├── prometheus/
│   ├── prometheus.yml          # Prometheus configuration
│   ├── alerts.yml              # Alert rules
│   └── alertmanager.yml        # Alert routing
├── grafana/
│   ├── provisioning/           # Auto-provisioning
│   └── dashboards/             # Dashboard definitions
├── loki/
│   └── loki-config.yml         # Loki configuration
├── promtail/
│   └── promtail-config.yml     # Log shipper config
├── exporters/
│   └── business_metrics_exporter.py  # Custom KPI exporter
├── examples/
│   └── fastapi_example.py      # Integration example
└── scripts/
    └── start_monitoring.sh     # Startup script
```

## 📈 Available Metrics

### System Metrics
- CPU usage (`node_cpu_seconds_total`)
- Memory usage (`node_memory_*`)
- Disk usage (`node_filesystem_*`)
- Network I/O (`node_network_*`)

### Application Metrics
- HTTP requests (`lexgenius_http_requests_total`)
- Request duration (`lexgenius_http_request_duration_seconds`)
- Active requests (`lexgenius_active_requests`)

### Business Metrics
- PACER documents scraped (`lexgenius_pacer_documents_scraped_total`)
- FB ads processed (`lexgenius_fb_ads_processed_total`)
- Reports generated (`lexgenius_reports_generated_total`)
- Active cases by court (`lexgenius_cases_by_court`)
- Active cases by MDL (`lexgenius_cases_by_mdl`)

### Storage Metrics
- S3 operations (`lexgenius_s3_upload_*`)
- DynamoDB operations (`lexgenius_dynamodb_*`)

## 🚨 Alerts

### Critical Alerts
- Service down
- High error rate (>5%)
- Disk space low (<20%)
- Security incidents

### Warning Alerts
- High CPU usage (>80%)
- High memory usage (>85%)
- High response time (>2s)
- Processing backlogs

## 🔐 Security

- All services run in isolated containers
- Metrics endpoints can be secured with authentication
- Sensitive data is not exposed in metrics
- Correlation IDs enable audit trails

## 🛠️ Maintenance

### Backup Prometheus Data
```bash
docker exec lexgenius-prometheus tar -czf /tmp/prometheus-backup.tar.gz /prometheus
docker cp lexgenius-prometheus:/tmp/prometheus-backup.tar.gz ./backups/
```

### Update Dashboards
1. Edit dashboard JSON in `grafana/dashboards/`
2. Restart Grafana: `docker-compose restart grafana`

### Add New Alerts
1. Edit `prometheus/alerts.yml`
2. Reload Prometheus: `curl -X POST http://localhost:9090/-/reload`

## 📝 Troubleshooting

### Check Service Status
```bash
docker-compose ps
docker-compose logs [service-name]
```

### Verify Metrics Collection
```bash
# Check Prometheus targets
curl http://localhost:9090/api/v1/targets

# Check specific metric
curl http://localhost:9090/api/v1/query?query=up
```

### Debug Tracing
- Open Jaeger UI: http://localhost:16686
- Search by service name or trace ID
- Check span details and timings

### Common Issues

**Prometheus can't scrape targets:**
- Check network connectivity
- Verify target endpoints are accessible
- Check firewall rules

**Grafana dashboards empty:**
- Verify Prometheus datasource
- Check metric names in queries
- Ensure time range is correct

**High memory usage:**
- Adjust retention policies
- Reduce scrape frequency
- Optimize cardinality

## 📚 Resources

- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [OpenTelemetry Python](https://opentelemetry.io/docs/instrumentation/python/)
- [Loki Documentation](https://grafana.com/docs/loki/)