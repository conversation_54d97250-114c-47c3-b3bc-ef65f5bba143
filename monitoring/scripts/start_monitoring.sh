#!/bin/bash

# Start LexGenius Monitoring Stack

set -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
MONITORING_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$MONITORING_DIR")"

echo "🚀 Starting LexGenius Monitoring Stack..."
echo "📁 Project root: $PROJECT_ROOT"
echo "📁 Monitoring directory: $MONITORING_DIR"

# Create necessary directories
echo "📂 Creating directories..."
mkdir -p "$PROJECT_ROOT/logs"
mkdir -p "$MONITORING_DIR/data/prometheus"
mkdir -p "$MONITORING_DIR/data/grafana"
mkdir -p "$MONITORING_DIR/data/loki"
mkdir -p "$MONITORING_DIR/data/jaeger"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Navigate to docker directory
cd "$MONITORING_DIR/docker"

# Pull latest images
echo "🐳 Pulling Docker images..."
docker-compose pull

# Start the stack
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to start..."
sleep 10

# Check service status
echo "🔍 Checking service status..."
docker-compose ps

# Print access URLs
echo ""
echo "✅ Monitoring stack started successfully!"
echo ""
echo "📊 Access URLs:"
echo "  - Prometheus:    http://localhost:9090"
echo "  - Grafana:       http://localhost:3000 (admin/lexgenius-monitor)"
echo "  - Jaeger:        http://localhost:16686"
echo "  - Alertmanager:  http://localhost:9093"
echo ""
echo "📈 Metrics endpoints:"
echo "  - Node Exporter: http://localhost:9100/metrics"
echo "  - cAdvisor:      http://localhost:8080/metrics"
echo ""
echo "🎯 Application endpoints (when running):"
echo "  - LexGenius API: http://localhost:8000/metrics"
echo "  - Business KPIs: http://localhost:9341/metrics"
echo ""
echo "💡 To view logs: docker-compose logs -f [service-name]"
echo "🛑 To stop:      cd $MONITORING_DIR/docker && docker-compose down"