"""
Custom business metrics exporter for LexGenius.

This exporter collects business-specific KPIs from the database
and exposes them as Prometheus metrics.
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import aioboto3
import asyncpg
from prometheus_client import Gauge, Counter, generate_latest, CONTENT_TYPE_LATEST
from prometheus_client.core import CollectorRegistry
from aiohttp import web

logger = logging.getLogger(__name__)

# Create a custom registry
registry = CollectorRegistry()

# Business KPI metrics
total_active_cases = Gauge(
    'lexgenius_business_total_active_cases',
    'Total number of active cases',
    registry=registry
)

cases_by_court_gauge = Gauge(
    'lexgenius_business_cases_by_court',
    'Number of cases by court',
    ['court_id', 'court_name'],
    registry=registry
)

cases_by_mdl_gauge = Gauge(
    'lexgenius_business_cases_by_mdl',
    'Number of cases by MDL',
    ['mdl_number', 'mdl_name'],
    registry=registry
)

active_law_firms_gauge = Gauge(
    'lexgenius_business_active_law_firms',
    'Number of active law firms',
    registry=registry
)

documents_processed_today = Gauge(
    'lexgenius_business_documents_processed_today',
    'Number of documents processed today',
    ['document_type'],
    registry=registry
)

fb_ads_active_campaigns = Gauge(
    'lexgenius_business_fb_ads_active_campaigns',
    'Number of active FB ad campaigns',
    registry=registry
)

reports_generated_today = Gauge(
    'lexgenius_business_reports_generated_today',
    'Number of reports generated today',
    ['report_type'],
    registry=registry
)

storage_usage_bytes = Gauge(
    'lexgenius_business_storage_usage_bytes',
    'Storage usage in bytes',
    ['storage_type', 'bucket'],
    registry=registry
)

api_usage_today = Gauge(
    'lexgenius_business_api_usage_today',
    'API usage count today',
    ['api_provider', 'endpoint'],
    registry=registry
)

revenue_metrics = Gauge(
    'lexgenius_business_revenue_metrics',
    'Revenue-related metrics',
    ['metric_type'],
    registry=registry
)


class BusinessMetricsCollector:
    """Collector for business metrics."""

    def __init__(self):
        self.db_pool: Optional[asyncpg.Pool] = None
        self.dynamodb_client = None
        self.s3_client = None
        self.update_interval = 60  # seconds

    async def init(self):
        """Initialize database connections."""
        # Initialize PostgreSQL connection pool
        self.db_pool = await asyncpg.create_pool(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 5432)),
            user=os.getenv('DB_USER', 'lexgenius'),
            password=os.getenv('DB_PASSWORD'),
            database=os.getenv('DB_NAME', 'lexgenius'),
            min_size=1,
            max_size=5
        )

        # Initialize AWS clients
        session = aioboto3.Session()
        self.dynamodb_client = await session.client('dynamodb').__aenter__()
        self.s3_client = await session.client('s3').__aenter__()

    async def cleanup(self):
        """Cleanup connections."""
        if self.db_pool:
            await self.db_pool.close()
        if self.dynamodb_client:
            await self.dynamodb_client.__aexit__(None, None, None)
        if self.s3_client:
            await self.s3_client.__aexit__(None, None, None)

    async def collect_case_metrics(self):
        """Collect case-related metrics."""
        async with self.db_pool.acquire() as conn:
            # Total active cases
            total_cases = await conn.fetchval("""
                SELECT COUNT(*) FROM cases 
                WHERE status = 'active'
            """)
            total_active_cases.set(total_cases or 0)

            # Cases by court
            court_cases = await conn.fetch("""
                SELECT court_id, court_name, COUNT(*) as case_count
                FROM cases
                WHERE status = 'active'
                GROUP BY court_id, court_name
            """)
            
            for row in court_cases:
                cases_by_court_gauge.labels(
                    court_id=row['court_id'],
                    court_name=row['court_name']
                ).set(row['case_count'])

            # Cases by MDL
            mdl_cases = await conn.fetch("""
                SELECT mdl_number, mdl_name, COUNT(*) as case_count
                FROM cases
                WHERE mdl_number IS NOT NULL AND status = 'active'
                GROUP BY mdl_number, mdl_name
            """)
            
            for row in mdl_cases:
                cases_by_mdl_gauge.labels(
                    mdl_number=row['mdl_number'],
                    mdl_name=row['mdl_name'] or 'Unknown'
                ).set(row['case_count'])

    async def collect_law_firm_metrics(self):
        """Collect law firm metrics."""
        async with self.db_pool.acquire() as conn:
            # Active law firms (those with activity in last 30 days)
            active_firms = await conn.fetchval("""
                SELECT COUNT(DISTINCT law_firm_id)
                FROM law_firm_activity
                WHERE last_activity > NOW() - INTERVAL '30 days'
            """)
            active_law_firms_gauge.set(active_firms or 0)

    async def collect_processing_metrics(self):
        """Collect document processing metrics."""
        async with self.db_pool.acquire() as conn:
            # Documents processed today
            today = datetime.utcnow().date()
            
            doc_counts = await conn.fetch("""
                SELECT document_type, COUNT(*) as count
                FROM processed_documents
                WHERE DATE(processed_at) = $1
                GROUP BY document_type
            """, today)
            
            for row in doc_counts:
                documents_processed_today.labels(
                    document_type=row['document_type']
                ).set(row['count'])

            # Reports generated today
            report_counts = await conn.fetch("""
                SELECT report_type, COUNT(*) as count
                FROM generated_reports
                WHERE DATE(generated_at) = $1
                GROUP BY report_type
            """, today)
            
            for row in report_counts:
                reports_generated_today.labels(
                    report_type=row['report_type']
                ).set(row['count'])

    async def collect_fb_ads_metrics(self):
        """Collect Facebook ads metrics."""
        try:
            # Query DynamoDB for active campaigns
            response = await self.dynamodb_client.scan(
                TableName='fb_ad_campaigns',
                FilterExpression='attribute_exists(active) AND active = :val',
                ExpressionAttributeValues={':val': {'BOOL': True}}
            )
            
            fb_ads_active_campaigns.set(response.get('Count', 0))
        except Exception as e:
            logger.error(f"Error collecting FB ads metrics: {e}")

    async def collect_storage_metrics(self):
        """Collect storage usage metrics."""
        try:
            # S3 bucket usage
            buckets = ['lexgenius-dockets', 'lexgenius-reports', 'lexgenius-fb-ads']
            
            for bucket in buckets:
                try:
                    # Get bucket size
                    paginator = self.s3_client.get_paginator('list_objects_v2')
                    total_size = 0
                    
                    async for page in paginator.paginate(Bucket=bucket):
                        if 'Contents' in page:
                            for obj in page['Contents']:
                                total_size += obj['Size']
                    
                    storage_usage_bytes.labels(
                        storage_type='s3',
                        bucket=bucket
                    ).set(total_size)
                except Exception as e:
                    logger.error(f"Error getting S3 metrics for {bucket}: {e}")

        except Exception as e:
            logger.error(f"Error collecting storage metrics: {e}")

    async def collect_api_usage_metrics(self):
        """Collect API usage metrics."""
        async with self.db_pool.acquire() as conn:
            # API usage today
            today = datetime.utcnow().date()
            
            api_usage = await conn.fetch("""
                SELECT api_provider, endpoint, COUNT(*) as count
                FROM api_usage_logs
                WHERE DATE(timestamp) = $1
                GROUP BY api_provider, endpoint
            """, today)
            
            for row in api_usage:
                api_usage_today.labels(
                    api_provider=row['api_provider'],
                    endpoint=row['endpoint']
                ).set(row['count'])

    async def collect_revenue_metrics(self):
        """Collect revenue-related metrics."""
        async with self.db_pool.acquire() as conn:
            # Monthly recurring revenue
            mrr = await conn.fetchval("""
                SELECT SUM(monthly_amount)
                FROM subscriptions
                WHERE status = 'active'
            """)
            revenue_metrics.labels(metric_type='mrr').set(mrr or 0)

            # Active subscriptions
            active_subs = await conn.fetchval("""
                SELECT COUNT(*)
                FROM subscriptions
                WHERE status = 'active'
            """)
            revenue_metrics.labels(metric_type='active_subscriptions').set(active_subs or 0)

            # Trial conversions this month
            conversions = await conn.fetchval("""
                SELECT COUNT(*)
                FROM subscriptions
                WHERE converted_from_trial = true
                AND DATE_TRUNC('month', converted_at) = DATE_TRUNC('month', NOW())
            """)
            revenue_metrics.labels(metric_type='trial_conversions_month').set(conversions or 0)

    async def collect_all_metrics(self):
        """Collect all business metrics."""
        try:
            await asyncio.gather(
                self.collect_case_metrics(),
                self.collect_law_firm_metrics(),
                self.collect_processing_metrics(),
                self.collect_fb_ads_metrics(),
                self.collect_storage_metrics(),
                self.collect_api_usage_metrics(),
                self.collect_revenue_metrics()
            )
            logger.info("Successfully collected all business metrics")
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")

    async def run_collector(self):
        """Run the metrics collector periodically."""
        while True:
            await self.collect_all_metrics()
            await asyncio.sleep(self.update_interval)


# Web server for metrics endpoint
async def metrics_handler(request):
    """Handle metrics requests."""
    metrics = generate_latest(registry)
    return web.Response(
        body=metrics,
        content_type=CONTENT_TYPE_LATEST
    )


async def health_handler(request):
    """Handle health check requests."""
    return web.Response(text="OK", status=200)


async def create_app():
    """Create the web application."""
    app = web.Application()
    app.router.add_get('/metrics', metrics_handler)
    app.router.add_get('/health', health_handler)
    
    # Initialize collector
    collector = BusinessMetricsCollector()
    
    async def on_startup(app):
        await collector.init()
        # Start collector task
        app['collector_task'] = asyncio.create_task(collector.run_collector())
    
    async def on_cleanup(app):
        if 'collector_task' in app:
            app['collector_task'].cancel()
        await collector.cleanup()
    
    app.on_startup.append(on_startup)
    app.on_cleanup.append(on_cleanup)
    
    return app


def main():
    """Main entry point."""
    logging.basicConfig(level=logging.INFO)
    app = create_app()
    web.run_app(app, host='0.0.0.0', port=9341)


if __name__ == '__main__':
    main()