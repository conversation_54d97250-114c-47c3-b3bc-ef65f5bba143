server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Scrape system logs
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: varlogs
          __path__: /var/log/*log

  # Scrape LexGenius application logs
  - job_name: lexgenius
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            service: service
            correlation_id: correlation_id
            message: message
      - labels:
          level:
          service:
          correlation_id:
      - timestamp:
          source: timestamp
          format: RFC3339
    static_configs:
      - targets:
          - localhost
        labels:
          job: lexgenius
          __path__: /app/logs/*.json

  # Scrape LexGenius text logs
  - job_name: lexgenius-legacy
    pipeline_stages:
      - regex:
          expression: '^\[(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] (?P<level>\w+): (?P<message>.*)'
      - labels:
          level:
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05'
    static_configs:
      - targets:
          - localhost
        labels:
          job: lexgenius-legacy
          __path__: /app/logs/*.log