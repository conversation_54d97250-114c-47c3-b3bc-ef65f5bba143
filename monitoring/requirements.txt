# Monitoring dependencies for LexGenius

# Prometheus metrics
prometheus-client==0.19.0

# OpenTelemetry core
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation==0.42b0

# OpenTelemetry exporters
opentelemetry-exporter-otlp==1.21.0
opentelemetry-exporter-otlp-proto-grpc==1.21.0
opentelemetry-exporter-jaeger==1.21.0
opentelemetry-exporter-jaeger-thrift==1.21.0

# OpenTelemetry instrumentations
opentelemetry-instrumentation-aiohttp-client==0.42b0
opentelemetry-instrumentation-boto3sqs==0.42b0
opentelemetry-instrumentation-botocore==0.42b0
opentelemetry-instrumentation-django==0.42b0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-flask==0.42b0
opentelemetry-instrumentation-psycopg2==0.42b0
opentelemetry-instrumentation-redis==0.42b0
opentelemetry-instrumentation-requests==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0

# OpenTelemetry propagators
opentelemetry-propagator-b3==1.21.0

# For business metrics exporter
aioboto3==12.1.0
asyncpg==0.29.0
aiohttp==3.9.1

# For example applications
fastapi==0.104.1
uvicorn==0.24.0
flask==3.0.0