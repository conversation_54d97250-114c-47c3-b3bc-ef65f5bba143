{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0}, "id": 10, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.0.0", "targets": [{"expr": "sum(increase(lexgenius_pacer_documents_scraped_total[24h]))", "refId": "A"}], "title": "PACER Documents (24h)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0}, "id": 11, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.0.0", "targets": [{"expr": "sum(increase(lexgenius_fb_ads_processed_total[24h]))", "refId": "A"}], "title": "FB Ads Processed (24h)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 0}, "id": 12, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.0.0", "targets": [{"expr": "sum(increase(lexgenius_reports_generated_total[24h]))", "refId": "A"}], "title": "Reports Generated (24h)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0}, "id": 13, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "text": {}, "textMode": "auto"}, "pluginVersion": "10.0.0", "targets": [{"expr": "sum(increase(lexgenius_transformer_documents_processed_total[24h]))", "refId": "A"}], "title": "Documents Transformed (24h)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "10.0.0", "targets": [{"expr": "100 * (1 - (sum(rate(lexgenius_pacer_scrape_failures_total[5m])) / sum(rate(lexgenius_pacer_scrape_attempts_total[5m]))))", "refId": "A"}], "title": "PACER Success Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "10.0.0", "targets": [{"expr": "lexgenius_fb_ads_queue_size", "refId": "A"}], "title": "FB Ads Processing Queue", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 16, "options": {"legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum by (court) (lexgenius_cases_by_court)", "format": "time_series", "instant": true, "refId": "A"}], "title": "Cases by Court", "type": "piechart"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 17, "options": {"legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum by (mdl) (lexgenius_cases_by_mdl)", "format": "time_series", "instant": true, "refId": "A"}], "title": "Cases by MDL", "type": "piechart"}], "refresh": "30s", "schemaVersion": 30, "style": "dark", "tags": ["lex<PERSON>ius", "business"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "LexGenius Business Metrics", "uid": "lexgenius-business", "version": 0}