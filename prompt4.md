```
DEBUG    Loaded relevance configuration                                                                                                                                                                                                                                                                                                        core.py:24
                    DEBUG    Loaded stability configuration                                                                                                                                                                                                                                                                                                        core.py:24
                    DEBUG    Loaded paths configuration                                                                                                                                                                                                                                                                                                            core.py:24
                    DEBUG    Loaded ignore_download configuration                                                                                                                                                                                                                                                                                                  core.py:24
                    DEBUG    Loaded relevant_defendants configuration                                                                                                                                                                                                                                                                                              core.py:24
                    INFO     All PACER configurations loaded successfully                                                                                                                                                                                                                                                                                          core.py:27
                    INFO     Configuration service initialized successfully                                                                                                                                                                                                                                                                                        core.py:27
                    INFO     service initialized successfully                                                                                                                                                                                                                                                                                                      core.py:27
                    INFO     Initializing Browser Service (9th Core Service)                                                                                                                                                                                                                                                                                       core.py:27
[08/07/25 14:31:28] DEBUG    Launching browser with configuration                                                                                                                                                                                                                                                                                                  core.py:24
                    INFO     Browser launched successfully                                                                                                                                                                                                                                                                                                         core.py:27
                    INFO     Browser Service initialized successfully                                                                                                                                                                                                                                                                                              core.py:27
                    INFO     service initialized successfully                                                                                                                                                                                                                                                                                                      core.py:27
                    INFO     Optional service 'classification' is not available, skipping related functionality                                                                                                                                                                                                                                                    core.py:27
                    INFO     Docket Orchestrator initialized successfully                                                                                                                                                                                                                                                                                          core.py:27
                    INFO     service initialized successfully                                                                                                                                                                                                                                                                                                      core.py:27
                    INFO     Docket Orchestrator Facade initialized successfully                                                                                                                                                                                                                                                                                   core.py:27
                    INFO     Docket Processing Orchestrator initialized with ALL core services + optional facades                                                                                                                                                                                                                                                  core.py:27
                    INFO     service initialized successfully                                                                                                                                                                                                                                                                                                      core.py:27
                    INFO     Running PacerOrchestratorService for report scraping                                                                                                                                                                                                                                                                        component_base.py:94
                    INFO     Processing courts for reports: ['cand']                                                                                                                                                                                                                                                                                     component_base.py:94
                    INFO     Processing 1 courts using facade coordination + core services: ['cand']                                                                                                                                                                                                                                                               core.py:27
                    DEBUG    Processing directories set up for 20250807                                                                                                                                                                                                                                                                                            core.py:24
                    INFO     === PROCESSING COURT: cand ===                                                                                                                                                                                                                                                                                                        core.py:27
                    INFO     Using CourtProcessingFacadeService for coordination                                                                                                                                                                                                                                                                                   core.py:27
                    INFO     [cand] Workflow(date_range): Starting court workflow via facade.                                                                                                                                                                                                                                                                      core.py:27
                    INFO     DownloadPathManager stub executing task                                                                                                                                                                                                                                                                                               core.py:27
                    INFO     WorkflowOrchestrator stub executing task                                                                                                                                                                                                                                                                                              core.py:27
                    INFO     Multi-court processing complete: 0 total dockets processed, 0 failed across 1 courts                                                                                                                                                                                                                                                  core.py:27
                    INFO     PacerOrchestratorService multi-court report scraping complete                                                                                                                                                                                                                                                               component_base.py:94
                    INFO     Cleaning up Docket Processing Orchestrator and all facade services                                                                                                                                                                                                                                                                    core.py:27
                    WARNING  service cleanup failed                                                                                                

```
