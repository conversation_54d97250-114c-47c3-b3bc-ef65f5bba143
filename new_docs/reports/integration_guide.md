# Reports Service Integration Guide

## Overview

This guide explains how the Reports services integrate with the broader LexGenius system architecture and how to properly use the reports system in different contexts.

## Integration with Main Architecture

### Entry Points

#### 1. Main Orchestrator Integration
```python
# From src/main.py
from src.services.reports import ReportsOrchestratorService

# Initialize through MainServiceFactory
reports_orchestrator = ReportsOrchestratorService(config_dict, is_weekly=False)
results = await reports_orchestrator.generate_report(output_type='both')
```

#### 2. Standalone Usage
```python
# Direct service usage
from src.services.reports import (
    ReportsOrchestratorService,
    ReportsConfigService,
    ReportsDataLoaderService
)

# Initialize with configuration
config_service = ReportsConfigService(config_dict)
orchestrator = ReportsOrchestratorService(config_dict, is_weekly=True)
```

### Configuration Integration

#### Configuration Sources
1. **Environment Variables**: Runtime configuration overrides
2. **Config Files**: Static configuration in `src/config/reports/`
3. **Command Line**: Parameters passed through main orchestrator
4. **Defaults**: Built-in fallback configurations

#### Configuration Hierarchy
```
Command Line Args > Environment Variables > Config Files > Defaults
```

### Data Source Integration

#### DynamoDB Integration
```python
# Automatic integration through data loader service
data_loader = ReportsDataLoaderService(config_service)
case_data = await data_loader.load_dynamic_data()
```

#### S3 Integration
```python
# Publishing service handles S3 operations
publishing_service = ReportsPublishingService(config_service)
await publishing_service.publish_s3(reports_data)
```

#### Facebook Ads Integration
```python
# Specialized ad processing
ad_processor = AdDfProcessorService(config_service)
ad_data = await ad_processor.process_ad_dataframe(fb_ads_df)
```

## Service Dependencies

### Internal Dependencies
- **Configuration Models**: `src/config_models/reports.py`
- **Utilities**: `src/utils/` (date, file, text processing)
- **Repositories**: Data access patterns for DynamoDB and S3
- **Logging**: Shared logging infrastructure

### External Dependencies
- **AWS Services**: S3, CloudFront, DynamoDB
- **Template Engine**: Jinja2 for HTML generation
- **Data Processing**: Pandas for DataFrame operations
- **Async Framework**: asyncio for concurrent operations

## Usage Patterns

### Daily Reports
```python
# Generate daily reports
orchestrator = ReportsOrchestratorService(config_dict, is_weekly=False)
results = await orchestrator.generate_report(output_type='both')
```

### Weekly Reports
```python
# Generate weekly reports with aggregated data
orchestrator = ReportsOrchestratorService(config_dict, is_weekly=True)
results = await orchestrator.generate_report(output_type='s3')
```

### Custom Report Generation
```python
# Use individual services for custom workflows
config_service = ReportsConfigService(config_dict)
data_loader = ReportsDataLoaderService(config_service)
processing_service = ReportsProcessingService(config_service)

# Load and process specific data
data = await data_loader.load_specific_data_source('cases')
processed = await processing_service.process_case_data(data)
```

## Error Handling Integration

### Error Propagation
```python
try:
    results = await orchestrator.generate_report()
except ReportsConfigurationError as e:
    # Handle configuration issues
    logger.error(f"Configuration error: {e}")
except ReportsDataError as e:
    # Handle data loading/processing issues
    logger.error(f"Data error: {e}")
except ReportsPublishingError as e:
    # Handle publishing issues
    logger.error(f"Publishing error: {e}")
```

### Graceful Degradation
- Services continue operation with partial data when possible
- Fallback templates used when primary templates fail
- Local publishing continues even if S3 publishing fails
- Detailed error reporting for debugging

## Performance Considerations

### Memory Management
- Services implement proper cleanup in finally blocks
- Large DataFrames are processed in chunks when possible
- Caching strategies minimize redundant data loading

### Concurrency
- Services use async/await for I/O operations
- Parallel processing where data dependencies allow
- Rate limiting for external API calls

### Resource Optimization
- Template compilation caching
- Data source connection pooling
- Efficient S3 upload strategies

## Monitoring and Observability

### Logging Integration
```python
# All services use structured logging
logger.info("Starting report generation", extra={
    'report_type': 'daily',
    'output_type': 'both',
    'timestamp': datetime.utcnow()
})
```

### Metrics Collection
- Report generation timing
- Data source response times
- Error rates and types
- Resource utilization metrics

### Health Checks
```python
# Service health validation
async def health_check():
    config_ok = await config_service.validate_dependencies()
    data_ok = await data_loader.test_connections()
    return config_ok and data_ok
```

## Testing Integration

### Unit Testing
```python
# Mock external dependencies for unit tests
@pytest.fixture
def mock_config_service():
    return Mock(spec=ReportsConfigService)

async def test_orchestrator_workflow(mock_config_service):
    orchestrator = ReportsOrchestratorService(mock_config_service)
    # Test workflow logic
```

### Integration Testing
```python
# Test with real dependencies in controlled environment
async def test_end_to_end_report_generation():
    config = load_test_config()
    orchestrator = ReportsOrchestratorService(config)
    results = await orchestrator.generate_report()
    assert results['status'] == 'success'
```

## Deployment Integration

### Container Deployment
```dockerfile
# Reports service can be containerized
FROM python:3.9
COPY src/services/reports/ /app/reports/
RUN pip install -r requirements.txt
CMD ["python", "-m", "src.services.reports"]
```

### Environment Configuration
```bash
# Environment variables for deployment
export REPORTS_S3_BUCKET=lexgenius-reports
export REPORTS_CLOUDFRONT_DISTRIBUTION=E1234567890
export REPORTS_DYNAMODB_TABLE=lexgenius-cases
```

### Scaling Considerations
- Services are stateless and horizontally scalable
- Can be deployed as separate microservices if needed
- Supports batch processing for large datasets

## Best Practices

### Service Usage
1. Always use dependency injection for service initialization
2. Implement proper error handling and logging
3. Use async/await for I/O operations
4. Clean up resources in finally blocks

### Configuration Management
1. Use environment-specific configuration files
2. Validate all configuration at startup
3. Provide sensible defaults for optional settings
4. Document all configuration options

### Data Handling
1. Validate data integrity at service boundaries
2. Use appropriate data types and structures
3. Implement caching for frequently accessed data
4. Handle missing or malformed data gracefully

### Error Handling
1. Use specific exception types for different error categories
2. Provide detailed error messages for debugging
3. Implement retry logic for transient failures
4. Log errors with appropriate context

This integration guide provides the foundation for effectively using the Reports services within the broader LexGenius system architecture.