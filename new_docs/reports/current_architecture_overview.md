# Reports Service Architecture - Current Implementation

## Overview

The Reports service architecture in `src/services/reports/` implements a modern, service-oriented design with 9 specialized services that handle the complete report generation workflow. This architecture follows the same patterns used throughout the LexGenius system.

## Service Inventory

### Core Services (6)

1. **ReportsOrchestratorService** (`reports_orchestrator_service.py`)
   - Main workflow coordinator
   - Manages 4-phase report generation process
   - Handles error recovery and graceful degradation
   - Integrates with CloudFront cache invalidation

2. **ReportsConfigService** (`config_service.py`) 
   - Configuration management and validation
   - Pydantic-based configuration models
   - Environment-specific settings handling
   - Template and section configuration management

3. **ReportsDataLoaderService** (`data_loader_service.py`)
   - Data loading and caching operations
   - Static and dynamic data source management
   - DynamoDB integration for case data
   - Facebook ads data loading

4. **ReportsProcessingService** (`processing_service.py`)
   - Data processing and aggregation
   - Statistical calculations and metrics
   - Data transformation and enrichment
   - Cross-reference data validation

5. **ReportsRenderingService** (`rendering_service.py`)
   - Template rendering and HTML generation
   - Jinja2 template engine integration
   - Dynamic content generation
   - Asset management and optimization

6. **ReportsPublishingService** (`publishing_service.py`)
   - Local and S3 publishing operations
   - CloudFront cache invalidation
   - File management and cleanup
   - Multi-destination publishing support

### Specialized Services (3)

7. **AdPageGeneratorService** (`ad_page_generator_service.py`)
   - Facebook ad page generation
   - Ad content processing and formatting
   - Image and media handling
   - Ad-specific template rendering

8. **AdDfProcessorService** (`ad_df_processor_service.py`)
   - Facebook ads DataFrame processing
   - Data cleaning and normalization
   - Statistical analysis for ad data
   - Integration with main data pipeline

## Architecture Patterns

### Service Communication
```
ReportsOrchestratorService
├── ReportsConfigService (configuration)
├── ReportsDataLoaderService (data acquisition)
├── ReportsProcessingService (data transformation)
├── ReportsRenderingService (content generation)
├── ReportsPublishingService (content delivery)
├── AdPageGeneratorService (specialized content)
└── AdDfProcessorService (specialized processing)
```

### Workflow Phases

#### Phase 1: Configuration & Validation
- Load and validate configuration
- Initialize service dependencies
- Verify template availability
- Check AWS credentials and permissions

#### Phase 2: Data Loading
- Load static configuration data
- Fetch dynamic case data from DynamoDB
- Load Facebook ads data
- Cache frequently accessed data

#### Phase 3: Data Processing
- Process and aggregate loaded data
- Calculate statistics and metrics
- Enrich data with cross-references
- Validate data integrity

#### Phase 4: Rendering & Publishing
- Generate HTML reports from templates
- Process ad pages and specialized content
- Publish to local filesystem
- Upload to S3 and invalidate CloudFront cache

## Key Features

### Error Handling & Recovery
- Graceful degradation on service failures
- Comprehensive error logging and reporting
- Retry mechanisms for transient failures
- Fallback strategies for missing data

### Performance Optimization
- Efficient data caching strategies
- Parallel processing where applicable
- Optimized template rendering
- Batch operations for S3 uploads

### Configuration Management
- Environment-specific configurations
- Template-based section management
- Feature flags for conditional functionality
- Validation of all configuration parameters

### Integration Points
- **DynamoDB**: Case data storage and retrieval
- **S3**: Report storage and content delivery
- **CloudFront**: CDN and cache management
- **Facebook API**: Ad data integration
- **Jinja2**: Template rendering engine

## Service Dependencies

### External Dependencies
- AWS SDK (boto3) for S3 and CloudFront
- DynamoDB for data storage
- Jinja2 for template rendering
- Pandas for data processing
- Asyncio for asynchronous operations

### Internal Dependencies
- Configuration models from `src/config_models/`
- Shared utilities from `src/utils/`
- Repository patterns for data access
- Logging infrastructure

## Deployment Considerations

### Resource Requirements
- Memory: Moderate (data processing and template rendering)
- CPU: Moderate (statistical calculations and HTML generation)
- Network: High (S3 uploads and DynamoDB queries)
- Storage: Low (temporary file generation)

### Scaling Characteristics
- Horizontally scalable (stateless services)
- Can be containerized for cloud deployment
- Supports batch processing for large datasets
- Efficient resource utilization through async patterns

## Future Enhancements

### Planned Improvements
- Enhanced caching strategies
- Real-time report generation capabilities
- Advanced analytics and metrics
- Improved error recovery mechanisms

### Architectural Evolution
- Potential microservice decomposition
- Event-driven architecture integration
- Enhanced monitoring and observability
- Performance optimization initiatives

This architecture provides a robust, maintainable, and scalable foundation for report generation while maintaining consistency with the broader LexGenius system design patterns.