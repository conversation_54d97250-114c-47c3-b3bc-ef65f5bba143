# Reports Service Specifications - Detailed Implementation

## Core Service Specifications

### 1. ReportsOrchestratorService

**File**: `src/services/reports/reports_orchestrator_service.py`

**Purpose**: Main workflow coordinator for the entire report generation process

**Key Methods**:
- `generate_report(output_type='both')`: Main entry point for report generation
- `_validate_configuration()`: Validate all configuration and dependencies
- `_load_data_sources()`: Coordinate data loading from all sources
- `_process_data()`: Process and aggregate all loaded data
- `_render_reports()`: Generate HTML reports from templates
- `_publish_reports()`: Publish reports to all configured destinations

**Configuration**:
- Accepts configuration dictionary and weekly flag
- Initializes all dependent services
- Manages service lifecycle and cleanup

**Error Handling**:
- Comprehensive try-catch blocks for each phase
- Graceful degradation on service failures
- Detailed error logging and reporting

### 2. ReportsConfigService

**File**: `src/services/reports/config_service.py`

**Purpose**: Configuration management and validation using Pydantic models

**Key Methods**:
- `load_config()`: Load and validate configuration from multiple sources
- `get_template_config()`: Retrieve template-specific configuration
- `get_section_config()`: Get section-specific settings
- `validate_dependencies()`: Verify all required dependencies

**Configuration Models**:
- `ReportsConfig`: Main configuration model
- Template configuration validation
- Section-specific configuration management
- Environment variable integration

**Features**:
- Pydantic-based validation
- Environment-specific overrides
- Template and section management
- Comprehensive validation rules

### 3. ReportsDataLoaderService

**File**: `src/services/reports/data_loader_service.py`

**Purpose**: Data loading and caching operations from multiple sources

**Key Methods**:
- `load_all_data()`: Load data from all configured sources
- `load_static_data()`: Load configuration and template data
- `load_dynamic_data()`: Load case data from DynamoDB
- `load_fb_ads_data()`: Load Facebook ads data
- `cache_data()`: Implement caching strategies

**Data Sources**:
- DynamoDB case data
- Facebook ads data
- Static configuration files
- Template and asset files

**Caching Strategy**:
- In-memory caching for frequently accessed data
- Configurable cache expiration
- Cache invalidation on data updates

### 4. ReportsProcessingService

**File**: `src/services/reports/processing_service.py`

**Purpose**: Data processing, aggregation, and statistical calculations

**Key Methods**:
- `process_all_data()`: Main data processing coordinator
- `aggregate_case_data()`: Aggregate case statistics
- `calculate_metrics()`: Calculate key performance metrics
- `enrich_data()`: Add cross-reference data and enrichments
- `validate_data_integrity()`: Ensure data consistency

**Processing Features**:
- Statistical calculations and aggregations
- Data transformation and normalization
- Cross-reference data integration
- Data validation and integrity checks

### 5. ReportsRenderingService

**File**: `src/services/reports/rendering_service.py`

**Purpose**: Template rendering and HTML generation

**Key Methods**:
- `render_all_reports()`: Render all configured report types
- `render_template()`: Render individual templates
- `generate_html()`: Generate final HTML output
- `process_assets()`: Handle static assets and resources

**Template Engine**:
- Jinja2 template integration
- Custom template filters and functions
- Dynamic content generation
- Asset optimization and management

**Output Formats**:
- HTML reports with embedded CSS/JS
- Standalone HTML files
- Template-based content generation

### 6. ReportsPublishingService

**File**: `src/services/reports/publishing_service.py`

**Purpose**: Publishing reports to local and remote destinations

**Key Methods**:
- `publish_all_reports()`: Publish to all configured destinations
- `publish_local()`: Save reports to local filesystem
- `publish_s3()`: Upload reports to S3
- `invalidate_cloudfront()`: Invalidate CloudFront cache
- `cleanup_old_reports()`: Clean up outdated reports

**Publishing Destinations**:
- Local filesystem storage
- S3 bucket uploads
- CloudFront cache invalidation
- Multi-destination support

## Specialized Service Specifications

### 7. AdPageGeneratorService

**File**: `src/services/reports/ad_page_generator_service.py`

**Purpose**: Facebook ad page generation and management

**Key Methods**:
- `generate_ad_pages()`: Generate all ad pages
- `process_ad_content()`: Process individual ad content
- `handle_ad_images()`: Process ad images and media
- `render_ad_templates()`: Render ad-specific templates

**Features**:
- Facebook ad content processing
- Image and media handling
- Ad-specific template rendering
- Integration with main report workflow

### 8. AdDfProcessorService

**File**: `src/services/reports/ad_df_processor_service.py`

**Purpose**: Facebook ads DataFrame processing and analysis

**Key Methods**:
- `process_ad_dataframe()`: Process ads DataFrame
- `clean_ad_data()`: Clean and normalize ad data
- `calculate_ad_metrics()`: Calculate ad-specific metrics
- `integrate_with_pipeline()`: Integrate with main data pipeline

**Processing Features**:
- DataFrame operations and transformations
- Data cleaning and normalization
- Statistical analysis for ad data
- Integration with pandas ecosystem

## Service Integration Patterns

### Dependency Injection
All services use constructor-based dependency injection:
```python
class ReportsOrchestratorService:
    def __init__(self, config_dict: dict, is_weekly: bool = False):
        self.config_service = ReportsConfigService(config_dict)
        self.data_loader = ReportsDataLoaderService(self.config_service)
        # ... other services
```

### Async/Await Patterns
Services use async/await for I/O operations:
```python
async def generate_report(self, output_type: str = 'both') -> dict:
    await self._load_data_sources()
    await self._process_data()
    # ... other async operations
```

### Error Handling
Consistent error handling across all services:
```python
try:
    result = await self._perform_operation()
except SpecificException as e:
    self.log_error(f"Operation failed: {e}")
    # Graceful degradation or retry logic
```

### Configuration Management
All services accept configuration through dependency injection:
```python
class ServiceClass:
    def __init__(self, config_service: ReportsConfigService):
        self.config = config_service
        self.settings = config_service.get_service_config('service_name')
```

## Performance Characteristics

### Memory Usage
- **Low**: Configuration and publishing services
- **Medium**: Data loading and rendering services
- **High**: Data processing services (large DataFrames)

### CPU Usage
- **Low**: Configuration and publishing services
- **Medium**: Data loading and rendering services
- **High**: Data processing and ad processing services

### I/O Patterns
- **Network**: Heavy S3 uploads, DynamoDB queries
- **Disk**: Moderate template and asset reading
- **Memory**: Significant for data caching and processing

## Testing Strategy

### Unit Testing
- Individual service method testing
- Mock external dependencies
- Configuration validation testing
- Error handling verification

### Integration Testing
- Service-to-service communication
- End-to-end workflow testing
- External service integration
- Performance benchmarking

### Contract Testing
- Service interface validation
- Configuration contract testing
- Data format validation
- API compatibility testing

This specification provides a comprehensive overview of the current reports service architecture, enabling effective maintenance, testing, and future development.