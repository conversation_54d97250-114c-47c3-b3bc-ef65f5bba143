# Reports Service Documentation

## Overview

This directory contains comprehensive documentation for the LexGenius Reports service architecture located in `src/services/reports/`. The Reports system implements a modern, service-oriented design with 9 specialized services that handle the complete report generation workflow.

## Documentation Structure

### Core Documentation

1. **[Current Architecture Overview](current_architecture_overview.md)**
   - High-level architecture overview
   - Service inventory and relationships
   - Workflow phases and patterns
   - Key features and capabilities

2. **[Service Specifications](service_specifications.md)**
   - Detailed specifications for each service
   - Method signatures and functionality
   - Configuration requirements
   - Performance characteristics

3. **[Integration Guide](integration_guide.md)**
   - Integration with main LexGenius architecture
   - Usage patterns and examples
   - Error handling strategies
   - Testing and deployment guidance

## Quick Start

### Basic Usage
```python
from src.services.reports import ReportsOrchestratorService

# Initialize with configuration
orchestrator = ReportsOrchestratorService(config_dict, is_weekly=False)

# Generate reports
results = await orchestrator.generate_report(output_type='both')
```

### Service Architecture
```
ReportsOrchestratorService (Main Coordinator)
├── ReportsConfigService (Configuration Management)
├── ReportsDataLoaderService (Data Acquisition)
├── ReportsProcessingService (Data Processing)
├── ReportsRenderingService (Content Generation)
├── ReportsPublishingService (Content Delivery)
├── AdPageGeneratorService (Ad Content)
└── AdDfProcessorService (Ad Data Processing)
```

## Service Categories

### Core Services (6)
- **ReportsOrchestratorService**: Main workflow coordination
- **ReportsConfigService**: Configuration management and validation
- **ReportsDataLoaderService**: Data loading and caching
- **ReportsProcessingService**: Data processing and aggregation
- **ReportsRenderingService**: Template rendering and HTML generation
- **ReportsPublishingService**: Local and S3 publishing

### Specialized Services (3)
- **AdPageGeneratorService**: Facebook ad page generation
- **AdDfProcessorService**: Facebook ads DataFrame processing

## Key Features

### Workflow Management
- **4-Phase Process**: Configuration → Data Loading → Processing → Publishing
- **Error Recovery**: Graceful degradation and comprehensive error handling
- **Async Operations**: Efficient I/O handling with async/await patterns

### Data Integration
- **DynamoDB**: Case data storage and retrieval
- **Facebook Ads**: Ad data processing and integration
- **S3**: Report storage and content delivery
- **CloudFront**: CDN and cache management

### Template System
- **Jinja2 Integration**: Powerful template rendering engine
- **Dynamic Content**: Data-driven content generation
- **Asset Management**: Optimized handling of static resources

### Publishing Pipeline
- **Multi-Destination**: Local filesystem and S3 publishing
- **Cache Invalidation**: Automatic CloudFront cache management
- **Batch Operations**: Efficient bulk upload strategies

## Configuration

### Environment Variables
```bash
REPORTS_S3_BUCKET=lexgenius-reports
REPORTS_CLOUDFRONT_DISTRIBUTION=E1234567890
REPORTS_DYNAMODB_TABLE=lexgenius-cases
```

### Configuration Files
- `src/config/reports/`: Static configuration files
- Template configurations for different report types
- Section-specific settings and parameters

## Development Guidelines

### Service Development
1. Follow dependency injection patterns
2. Implement comprehensive error handling
3. Use async/await for I/O operations
4. Maintain clear service boundaries

### Testing Strategy
1. **Unit Tests**: Individual service methods
2. **Integration Tests**: Service-to-service communication
3. **Contract Tests**: Interface validation
4. **End-to-End Tests**: Complete workflow validation

### Performance Optimization
1. Implement efficient caching strategies
2. Use parallel processing where appropriate
3. Optimize template rendering and compilation
4. Monitor resource utilization

## Troubleshooting

### Common Issues
1. **Configuration Errors**: Validate all required settings
2. **Data Loading Failures**: Check DynamoDB and S3 connectivity
3. **Template Rendering Issues**: Verify template syntax and data availability
4. **Publishing Failures**: Confirm AWS credentials and permissions

### Debugging Tools
1. Comprehensive logging throughout all services
2. Error tracking with detailed stack traces
3. Performance monitoring and metrics collection
4. Health check endpoints for service validation

## Migration Notes

### From Legacy System
The current Reports service architecture represents a complete modernization from the previous monolithic approach:

- **Modular Design**: Clear separation of concerns
- **Testable Components**: Individual service testing capabilities
- **Scalable Architecture**: Horizontal scaling support
- **Maintainable Code**: Clean interfaces and documentation

### Backward Compatibility
- Configuration format compatibility maintained
- Template structure preserved where possible
- Output format consistency ensured
- API interface stability maintained

## Future Roadmap

### Planned Enhancements
- Enhanced caching strategies for improved performance
- Real-time report generation capabilities
- Advanced analytics and metrics collection
- Improved error recovery mechanisms

### Architectural Evolution
- Potential microservice decomposition for cloud deployment
- Event-driven architecture integration
- Enhanced monitoring and observability features
- Performance optimization initiatives

## Contributing

### Development Setup
1. Clone the repository and install dependencies
2. Configure environment variables for development
3. Run tests to verify setup: `pytest tests/services/reports/`
4. Follow the coding standards and documentation guidelines

### Code Review Process
1. Ensure all tests pass
2. Verify documentation updates
3. Check performance impact
4. Validate error handling coverage

For detailed implementation information, refer to the individual documentation files in this directory.