# Click Complaint Link and Removal Processing Flow

This document provides detailed flowcharts for the complaint link clicking process, including special handling for removal cases in the PACER document processing pipeline.

## Overview

The complaint link clicking process has two main paths:
1. **Standard Complaint Link**: For regular cases with complaint documents
2. **Removal Case Processing**: Special handling for removal cases with attachments

## Main Click Complaint Link Flow

```mermaid
graph TD
    A[_click_complaint_link Start] --> B{Navigator<br/>Available?}
    
    B -->|No| B1[Log Error:<br/>Navigator is None]
    B1 --> FAIL[Return False]
    
    B -->|Yes| C{Navigator.page<br/>Exists?}
    
    C -->|No| C1[Log Error:<br/>No page attribute]
    C1 --> FAIL
    
    C -->|Yes| D{Page Closed?}
    
    D -->|Yes| D1[Log Error:<br/>Page is closed]
    D1 --> FAIL
    
    D -->|No| E[Get Page State:<br/>URL & Title]
    
    E --> F[Wait for Document Table<br/>Timeout: 10s]
    
    F --> G{Table Found?}
    
    G -->|No| G1[Log Warning:<br/>No document table]
    G1 --> FAIL
    
    G -->|Yes| H[Get All Table Rows<br/>Skip Header Row]
    
    H --> I{Rows Found?}
    
    I -->|No| I1[Log Warning:<br/>No rows in table]
    I1 --> FAIL
    
    I -->|Yes| J[Standard Complaint<br/>Link Search]
    
    J --> K[For Each Row]
    
    K --> L[Get Docket Text<br/>from 3rd TD]
    
    L --> M{Skip Row?}
    
    M -->|Yes| M1[Skip Reasons:<br/>- Empty text<br/>- Contains 'incomplete'<br/>- Contains 'stricken - complaint'<br/>- Starts with 'civil cover sheet'<br/>- Complaint with e-file<br/>- Amended complaint not first row]
    M1 --> K
    
    M -->|No| N{Contains<br/>'complaint'?}
    
    N -->|No| K
    N -->|Yes| O[Check 2nd TD<br/>for Link]
    
    O --> P{Link Found?}
    
    P -->|No| P1[Log Warning:<br/>No link in 2nd TD]
    P1 --> K
    
    P -->|Yes| Q[Set target_link_locator]
    Q --> R[Break Loop]
    
    R --> S{Target Link<br/>Found?}
    
    S -->|No| S1[Log Warning:<br/>No complaint link]
    S1 --> S2[Save Screenshot]
    S2 --> FAIL
    
    S -->|Yes| T[Wait for Link Visible<br/>Timeout: 10s]
    T --> U[Click Link]
    U --> SUCCESS[Return True]
```

## Removal Case Processing Flow

```mermaid
graph TD
    A[Removal Case Detected] --> B[_trigger_download_action_removal Start]
    
    B --> C{Navigator &<br/>Navigation Service<br/>Available?}
    
    C -->|No| C1[Log Error]
    C1 --> FAIL[Return False]
    
    C -->|Yes| D[Wait Initial Delay<br/>Default: 1000ms]
    
    D --> E[Look for Document Table]
    
    E --> F{Table Found?}
    
    F -->|No| F1[Log Warning:<br/>No document table]
    F1 --> FAIL
    
    F -->|Yes| G[Get First Row<br/>3rd Column Text]
    
    G --> H{Docket Text<br/>Extracted?}
    
    H -->|No| H1[Log Warning:<br/>No docket text]
    H1 --> FAIL
    
    H -->|Yes| I[Call DeepSeek API:<br/>_get_removal_attachment_number]
    
    I --> J{Attachment<br/>Number Valid?}
    
    J -->|NA| J1[No Relevant<br/>Attachment]
    J1 --> FAIL
    
    J -->|Invalid| J2[Invalid Number<br/>Not Digit]
    J2 --> FAIL
    
    J -->|Valid| K[Store Current URL]
    
    K --> L[Click Attachment Link:<br/>_click_removal_attachment_link]
    
    L --> M{Click<br/>Successful?}
    
    M -->|No| M1[Log Error:<br/>Failed to click]
    M1 --> FAIL
    
    M -->|Yes| N[Wait 2000ms]
    
    N --> O[Check URL Change]
    
    O --> P[Attach Download<br/>Event Listener]
    
    P --> Q[Click View Document<br/>Button]
    
    Q --> R{View Document<br/>Clicked?}
    
    R -->|No| R1[Check Download<br/>Directory]
    R1 --> R2{Recent Files<br/>Found?}
    R2 -->|Yes| SUCCESS[Return True]
    R2 -->|No| FAIL
    
    R -->|Yes| S[Wait 1000ms]
    
    S --> T[Wait for Download Event<br/>Timeout: 10s]
    
    T --> U{Download Event<br/>Triggered?}
    
    U -->|Yes| U1[Log Success:<br/>Download confirmed]
    U1 --> SUCCESS
    
    U -->|No| V[Fallback: Check<br/>Download Directory]
    
    V --> W{Recent Files<br/>in Last 30s?}
    
    W -->|Yes| W1[Log Info:<br/>Files found]
    W1 --> SUCCESS
    
    W -->|No| FAIL
```

## Standard Download Trigger Flow

```mermaid
graph TD
    A[_trigger_download_action Start] --> B{Is Removal<br/>Case?}
    
    B -->|Yes| C[Special Removal<br/>Handling]
    C --> C1[Extract Docket Text<br/>from HTML]
    C1 --> C2[Get Attachment Number<br/>via DeepSeek]
    C2 --> C3[Click Attachment Link]
    C3 --> C4[Click View Document]
    C4 --> Z[Continue to<br/>Common Flow]
    
    B -->|No| D[Analyze Page Content]
    
    D --> E[Count Elements:<br/>- Doc links<br/>- Attachments<br/>- Checkboxes<br/>- View/Download buttons]
    
    E --> F[Handle Document<br/>Checkboxes]
    
    F --> G{Checkbox Action<br/>Performed?}
    
    G -->|Yes| H[Try Download Buttons]
    H --> I{Download Selected<br/>Clicked?}
    
    I -->|Yes| J[Check for View<br/>Document Button]
    J --> K{View Document<br/>Clicked?}
    K -->|Yes| SUCCESS[Return True]
    K -->|No| SUCCESS
    
    I -->|No| L[Try Download<br/>Documents Button]
    L --> M{Clicked?}
    M -->|Yes| SUCCESS
    M -->|No| Z
    
    G -->|No| N[Enhanced Fallback:<br/>Direct Link Click]
    
    N --> O[Try Direct Link<br/>Selectors]
    
    O --> P{Direct Link<br/>Found?}
    
    P -->|Yes| Q[Click First<br/>Visible Link]
    Q --> R[Wait 2000ms]
    R --> Z
    
    P -->|No| Z
    
    Z --> AA[Try View Document<br/>Button]
    
    AA --> AB{View Document<br/>Clicked?}
    
    AB -->|Yes| SUCCESS
    AB -->|No| AC[Try VADA Buttons:<br/>View All/Download All]
    
    AC --> AD{VADA Button<br/>Clicked?}
    
    AD -->|Yes| SUCCESS
    AD -->|No| AE[Try Fallback<br/>Buttons]
    
    AE --> AF{Fallback Button<br/>Clicked?}
    
    AF -->|Yes| SUCCESS
    AF -->|No| AG[Try Hyperlink '1']
    
    AG --> AH{Hyperlink '1'<br/>Clicked?}
    
    AH -->|Yes| AI[Check for Multi-Doc<br/>Page]
    AI --> AJ{Multi-Doc<br/>Page?}
    AJ -->|Yes| AK[Handle Checkboxes<br/>Again]
    AK --> AL[Click Download<br/>Selected]
    AL --> SUCCESS
    AJ -->|No| SUCCESS
    
    AH -->|No| AM[Final Page<br/>Analysis]
    AM --> AN[Log Detailed<br/>Error Info]
    AN --> FAIL[Return False]
```

## Removal Attachment Link Click Detail

```mermaid
graph TD
    A[_click_removal_attachment_link Start] --> B{Navigator<br/>Available?}
    
    B -->|No| B1[Log Error]
    B1 --> FAIL[Return False]
    
    B -->|Yes| C[Try Different<br/>Table Selectors]
    
    C --> D["Table Selectors:<br/>- div#cmecfMainContent table<br/>- table[width='99%']<br/>- table[border='1']"]
    
    D --> E[For Each Table]
    
    E --> F[Find All Rows<br/>in Table]
    
    F --> G{Rows Found?}
    
    G -->|No| H[Try Next Table]
    H --> E
    
    G -->|Yes| I[For Each Data Row<br/>First 2 Rows Only]
    
    I --> J[Get 3rd TD<br/>Index 2]
    
    J --> K{TD Exists?}
    
    K -->|No| K1[Skip Row]
    K1 --> I
    
    K -->|Yes| L[Find All Links<br/>Within 3rd TD]
    
    L --> M[For Each Link<br/>in 3rd TD]
    
    M --> N[Get Link Text]
    
    N --> O{Text Matches<br/>Attachment Number?}
    
    O -->|No| O1[Check Next Link]
    O1 --> M
    
    O -->|Yes| P[Log TD Content<br/>for Verification]
    
    P --> Q[Wait for Visible<br/>Timeout: 3s]
    
    Q --> R[Click Link<br/>Timeout: 3s]
    
    R --> S[Wait 500ms]
    S --> SUCCESS[Return True]
    
    M --> T{All Links<br/>Checked?}
    T -->|Yes| I
    
    I --> U{All Rows<br/>Checked?}
    U -->|Yes| H
    
    E --> V{All Tables<br/>Tried?}
    
    V -->|Yes| W[Log Warning:<br/>Link not found in 3rd TD]
    W --> FAIL
```

### Key Improvements in the Fix:

1. **Proper TD Isolation**: The new approach explicitly navigates to the 3rd TD using `.locator("td").nth(2)` before looking for links
2. **Link Enumeration**: Instead of using text selectors that might match across TDs, it enumerates all links within the 3rd TD
3. **Text Matching**: Compares link text directly after ensuring we're only looking within the 3rd TD
4. **Verification Logging**: Logs the parent TD content to confirm the link is found in the correct column
5. **Row Iteration**: Checks the first 2 data rows in case the removal entry isn't in the first row

## Key Decision Points

### 1. **Case Type Detection**
- Standard cases use complaint link in 2nd TD
- Removal cases require attachment handling in 3rd TD
- Removal detection based on `is_removal` flag

### 2. **Link Location Strategy**
- **Standard Cases**: Look for "complaint" text, link in 2nd TD
- **Removal Cases**: Get attachment number via DeepSeek, link in 3rd TD
- **Fallback**: Direct document link clicking with multiple selectors

### 3. **Skip Conditions for Standard Cases**
- Empty docket text
- Contains "incomplete"
- Contains "stricken - complaint"
- Starts with "civil cover sheet"
- Complaint with e-file markers
- Amended complaint (not first row)

### 4. **Download Trigger Strategies**
1. Checkbox workflow → Download Selected
2. Direct document link clicking
3. View Document button
4. VADA buttons (View All/Download All)
5. Fallback buttons
6. Hyperlink '1' → Multi-doc page

## Error Handling

### Critical Validation Points:
1. **Navigator State**: Must have valid navigator with page
2. **Page State**: Page must not be closed
3. **Table Presence**: Document table must be found
4. **Link Visibility**: Links must be visible before clicking
5. **Removal Attachment**: Must get valid attachment number

### Fallback Strategies:
1. **No Checkboxes**: Try direct link clicking
2. **No Direct Links**: Try various button selectors
3. **Download Event Timeout**: Check download directory
4. **Multi-Doc Page**: Re-run checkbox workflow

## DeepSeek Integration for Removal Cases

The system uses DeepSeek API to intelligently identify which attachment to download:
- Analyzes docket text from 3rd column
- Returns attachment number or "NA" if none relevant
- Focuses on complaint-related attachments
- Handles exhibit naming variations

## Page Analysis on Failure

When all strategies fail, the system performs detailed analysis:
- Current URL and page title
- Count of various elements (checkboxes, links, buttons)
- Check for access restrictions
- Check for "no documents" indicators
- Save screenshot for debugging