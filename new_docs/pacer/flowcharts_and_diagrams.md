# PACER Flowcharts and Component Interaction Diagrams

## System Overview Flowchart

```mermaid
graph TD
    A[src/main.py] --> B[MainOrchestrator]
    B --> C[ScrapingOrchestrator]
    C --> D[PacerOrchestratorService]
    
    D --> E{Processing Mode}
    E -->|Sequential| F[Sequential Court Processing]
    E -->|Parallel| G[Parallel Court Processing]
    
    F --> H[Single Browser Context]
    G --> I[Isolated Browser Contexts]
    
    H --> J[Court Processing Workflow]
    I --> J
    
    J --> K[Authentication Service]
    J --> L[Navigation Service]
    J --> M[Case Processing Service]
    J --> N[HTML Processing Service]
    J --> O[Classification Service]
    J --> P[File Management Service]
    
    K --> Q[PACER Login]
    L --> R[Page Navigation]
    M --> S[Case Data Extraction]
    N --> T[HTML Parsing]
    O --> U[Case Classification]
    P --> V[File Operations]
    
    Q --> W[Data Persistence]
    R --> W
    S --> W
    T --> W
    U --> W
    V --> W
    
    W --> X[S3 Storage]
    W --> Y[DynamoDB]
    W --> Z[Local Files]
```

## Service Factory Dependency Injection Flow

```mermaid
graph LR
    A[PacerServiceFactory] --> B[create_html_processing_service]
    A --> C[create_case_classification_service]
    A --> D[create_case_processing_service]
    A --> E[create_transfer_service]
    A --> F[wire_services]
    
    B --> G[PacerHTMLProcessingService]
    C --> H[PacerCaseClassificationService]
    D --> I[PacerCaseProcessingService]
    E --> J[PacerTransferService]
    
    F --> K[Service Wiring]
    K --> L[Isolated Config Copy]
    K --> M[Dependency Injection]
    K --> N[Service Registration]
    
    G --> O[DataUpdaterService]
    G --> P[S3Manager]
    
    H --> Q[Transfer Handler]
    H --> R[Court Lookup]
    
    I --> S[HTML Processing]
    I --> T[Classification Service]
    
    J --> U[PACER Repository]
    J --> V[District Court Repository]
    J --> W[S3 Storage]
```

## Browser Resource Management Flow

```mermaid
graph TD
    A[PacerOrchestratorService] --> B{Parallel Mode?}
    
    B -->|No| C[Sequential Processing]
    B -->|Yes| D[Parallel Processing]
    
    C --> E[Single Browser Context]
    E --> F[Shared Context for All Courts]
    F --> G[Court Processing]
    
    D --> H[Semaphore Control]
    H --> I[Max Workers: 8]
    I --> J[Isolated Browser Contexts]
    
    J --> K[Court 1 - Browser 1]
    J --> L[Court 2 - Browser 2]
    J --> M[Court N - Browser N]
    
    K --> N[Unique Download Path 1]
    L --> O[Unique Download Path 2]
    M --> P[Unique Download Path N]
    
    N --> Q[Context Cleanup]
    O --> Q
    P --> Q
    
    Q --> R[Resource Release]
```

## Case Processing Workflow

```mermaid
graph TD
    A[Case Processing Start] --> B[PacerCaseProcessingService]
    B --> C[HTML Processing Service]
    C --> D[Parse HTML Content]
    D --> E[Extract Case Data]
    E --> F[Case Classification Service]
    F --> G[Determine Case Type]
    G --> H{Case Type}
    
    H -->|Civil| I[Civil Case Processing]
    H -->|Criminal| J[Criminal Case Processing]
    H -->|Transfer| K[Transfer Service]
    H -->|Removal| L[Removal Processing]
    
    I --> M[Relevance Assessment]
    J --> M
    K --> N[Transfer Order Processing]
    L --> O[Removal Detection]
    
    N --> M
    O --> M
    
    M --> P[Case Verification Service]
    P --> Q[Data Validation]
    Q --> R[File Management Service]
    R --> S[Save Case Data]
    S --> T[S3 Upload]
    S --> U[DynamoDB Update]
    S --> V[Local File Save]
    
    T --> W[Processing Complete]
    U --> W
    V --> W
```

## HTML Processing and Data Extraction Flow

```mermaid
graph LR
    A[Raw HTML] --> B[PacerHTMLProcessingService]
    B --> C[DataUpdaterService]
    C --> D[CaseParserService]
    D --> E[HTML Parsing]
    E --> F[Data Extraction]
    
    F --> G[Case Metadata]
    F --> H[Attorney Information]
    F --> I[Docket Entries]
    F --> J[Document Links]
    
    G --> K[Case Processing Service]
    H --> K
    I --> K
    J --> K
    
    K --> L[Classification Service]
    L --> M[Case Type Determination]
    M --> N[Relevance Scoring]
    N --> O[Transfer Detection]
    O --> P[Data Persistence]
```

## File Operations and Directory Structure

```mermaid
graph TD
    A[File Management Service] --> B[Directory Creation]
    B --> C[Local Structure]
    B --> D[S3 Structure]
    
    C --> E[data/YYYYMMDD/dockets/]
    D --> F[{iso_date}/dockets/{filename}]
    
    E --> G[Court-Specific Subdirs]
    G --> H[temp/court_ctx_dl_report/]
    H --> I[attempt_1_UUID/]
    
    F --> J[S3 Object Keys]
    J --> K[Metadata Storage]
    
    I --> L[File Operations]
    K --> L
    
    L --> M[JSON File Save]
    L --> N[PDF Document Save]
    L --> O[HTML Content Save]
    
    M --> P[Local + S3 Sync]
    N --> P
    O --> P
```

## Concurrent Processing Data Isolation

```mermaid
graph TD
    A[Multiple Courts] --> B[Configuration Isolation]
    B --> C[Court A - Config Copy 1]
    B --> D[Court B - Config Copy 2]
    B --> E[Court N - Config Copy N]
    
    C --> F[Service Instance A]
    D --> G[Service Instance B]
    E --> H[Service Instance N]
    
    F --> I[Browser Context A]
    G --> J[Browser Context B]
    H --> K[Browser Context N]
    
    I --> L[Download Path A]
    J --> M[Download Path B]
    K --> N[Download Path N]
    
    L --> O[Data Processing A]
    M --> P[Data Processing B]
    N --> Q[Data Processing N]
    
    O --> R[Isolated Results A]
    P --> S[Isolated Results B]
    Q --> T[Isolated Results N]
    
    R --> U[Aggregated Results]
    S --> U
    T --> U
```

## Service Lifecycle Management

```mermaid
graph TD
    A[Service Creation] --> B[AsyncServiceBase.__init__]
    B --> C[Configuration Validation]
    C --> D[Dependency Injection]
    D --> E[Service Registration]
    
    E --> F[Service.initialize()]
    F --> G[_initialize_service()]
    G --> H[Service Ready]
    
    H --> I[Service Operations]
    I --> J[_execute_action()]
    J --> K[Error Handling]
    K --> L[Logging & Monitoring]
    
    L --> M{Continue?}
    M -->|Yes| I
    M -->|No| N[Service.cleanup()]
    
    N --> O[_cleanup_service()]
    O --> P[Resource Release]
    P --> Q[Service Destroyed]
```

## Authentication and Session Management

```mermaid
graph LR
    A[Authentication Request] --> B[PacerAuthenticationService]
    B --> C[Browser Context]
    C --> D[PACER Login Page]
    D --> E[Credential Input]
    E --> F[Login Submission]
    F --> G[Session Validation]
    
    G --> H{Login Success?}
    H -->|Yes| I[Session Cookie Storage]
    H -->|No| J[Retry Logic]
    
    J --> K{Max Retries?}
    K -->|No| E
    K -->|Yes| L[Authentication Failed]
    
    I --> M[Session Management]
    M --> N[Cookie Validation]
    N --> O[Session Renewal]
    O --> P[Authenticated Operations]
```

## Error Handling and Recovery Flow

```mermaid
graph TD
    A[Service Operation] --> B[Try Block]
    B --> C[_execute_action()]
    C --> D{Success?}
    
    D -->|Yes| E[Log Success]
    D -->|No| F[Exception Caught]
    
    F --> G[Log Exception]
    G --> H[Error Context Collection]
    H --> I[ServiceError Creation]
    I --> J[Error Propagation]
    
    J --> K{Recoverable?}
    K -->|Yes| L[Retry Logic]
    K -->|No| M[Graceful Degradation]
    
    L --> N[Exponential Backoff]
    N --> O[Resource Cleanup]
    O --> B
    
    M --> P[Partial Results]
    P --> Q[Error Reporting]
    
    E --> R[Operation Complete]
    Q --> R
```

## Data Contamination Prevention Patterns

```mermaid
graph TD
    A[Service Factory] --> B[Configuration Isolation]
    B --> C[config.copy() for each service]
    C --> D[Isolated Service Instances]
    
    D --> E[Court A Processing]
    D --> F[Court B Processing]
    D --> G[Court N Processing]
    
    E --> H[Browser Context A]
    F --> I[Browser Context B]
    G --> J[Browser Context N]
    
    H --> K[Download Directory A]
    I --> L[Download Directory B]
    J --> M[Download Directory N]
    
    K --> N[File Operations A]
    L --> O[File Operations B]
    M --> P[File Operations N]
    
    N --> Q[Data Persistence A]
    O --> R[Data Persistence B]
    P --> S[Data Persistence N]
    
    Q --> T[No Cross-Contamination]
    R --> T
    S --> T
```

## Component Interaction Matrix

```mermaid
graph LR
    subgraph "Orchestration Layer"
        A[PacerOrchestratorService]
        B[DocketProcessingOrchestratorService]
        C[DownloadOrchestrationService]
    end
    
    subgraph "Processing Layer"
        D[CaseProcessingService]
        E[HTMLProcessingService]
        F[RowProcessingService]
    end
    
    subgraph "Classification Layer"
        G[CaseClassificationService]
        H[TransferService]
        I[RelevanceService]
    end
    
    subgraph "Infrastructure Layer"
        J[AuthenticationService]
        K[NavigationService]
        L[BrowserService]
    end
    
    subgraph "Data Layer"
        M[FileManagementService]
        N[ExportService]
        O[ReportService]
    end
    
    A --> D
    A --> J
    A --> K
    B --> D
    B --> E
    C --> M
    D --> E
    D --> G
    E --> F
    G --> H
    G --> I
    J --> L
    K --> L
    M --> N
    M --> O
```

These flowcharts provide comprehensive visualization of the PACER service architecture, showing how components interact, data flows through the system, and how concurrent processing maintains data isolation.