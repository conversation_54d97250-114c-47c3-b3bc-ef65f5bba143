# PACER Services Architecture - 2025 Current State

## Overview

The PACER (Public Access to Court Electronic Records) system has evolved into a comprehensive service-oriented architecture with 24 specialized services handling court document processing, case classification, and data management. This document reflects the current state as of July 2025.

## Service Inventory

### Core Services (24 Total)

1. **PacerOrchestratorService** - Main workflow coordination
2. **PacerDocketProcessingOrchestratorService** - Docket-specific orchestration
3. **PacerDownloadOrchestrationService** - Download workflow management
4. **PacerCourtProcessingService** - Court-specific processing
5. **PacerCaseProcessingService** - Individual case processing
6. **PacerRowProcessingService** - Row-level data processing
7. **PacerHTMLProcessingService** - HTML parsing and processing
8. **PacerCaseClassificationService** - Case type classification
9. **PacerCaseVerificationService** - Case validation
10. **PacerTransferService** - Transfer case handling
11. **PacerAuthenticationService** - PACER login management
12. **PacerNavigationService** - Browser navigation
13. **PacerQueryService** - Search query management
14. **PacerFileManagementService** - File operations
15. **PacerIgnoreDownloadService** - Download filtering
16. **PacerInteractiveService** - Interactive processing
17. **PacerAnalyticsService** - Analytics and metrics
18. **PacerConfigurationService** - Configuration management
19. **PacerExportService** - Data export functionality
20. **ReportService** - Report generation
21. **RelevanceService** - Relevance filtering
22. **BrowserService** - Browser automation
23. **PacerNavigator** - Navigation utilities
24. **PacerServiceFactory** - Service creation and DI

## Architecture Layers

### 1. Orchestration Layer
```
PacerOrchestratorService (Main Entry Point)
├── PacerDocketProcessingOrchestratorService
├── PacerDownloadOrchestrationService
└── PacerCourtProcessingService
```

**Responsibilities:**
- Workflow coordination across multiple courts
- Resource management and concurrency control
- Error handling and recovery
- Integration with MainOrchestrator

### 2. Processing Layer
```
Court Processing
├── PacerCaseProcessingService
├── PacerRowProcessingService
├── PacerHTMLProcessingService
└── PacerCaseVerificationService
```

**Responsibilities:**
- Individual case processing workflows
- HTML parsing and data extraction
- Row-level data transformation
- Case validation and verification

### 3. Classification Layer
```
Case Analysis
├── PacerCaseClassificationService
├── PacerTransferService
├── RelevanceService
└── PacerAnalyticsService
```

**Responsibilities:**
- Case type determination (removal, transfer, MDL)
- Transfer case processing
- Relevance filtering
- Analytics and metrics collection

### 4. Infrastructure Layer
```
Core Infrastructure
├── PacerAuthenticationService
├── PacerNavigationService
├── BrowserService
├── PacerNavigator
└── PacerQueryService
```

**Responsibilities:**
- PACER authentication and session management
- Browser automation and navigation
- Search query construction and execution
- Core infrastructure services

### 5. Data Management Layer
```
Data Operations
├── PacerFileManagementService
├── PacerExportService
├── ReportService
└── PacerConfigurationService
```

**Responsibilities:**
- File operations and storage management
- Data export and reporting
- Configuration management
- Report generation

### 6. Control Layer
```
Processing Control
├── PacerIgnoreDownloadService
├── PacerInteractiveService
└── PacerServiceFactory
```

**Responsibilities:**
- Download filtering and control
- Interactive processing modes
- Service creation and dependency injection

## Key Architectural Changes from Legacy

### Service Factory Pattern
The `PacerServiceFactory` implements comprehensive dependency injection:

```python
# Service Creation Methods
- create_html_processing_service()
- create_case_classification_service()
- create_case_processing_service()
- create_transfer_service()
- create_report_service()
- wire_services()  # Full service wiring
```

### Isolated Configuration
Each service receives isolated configuration copies to prevent cross-court contamination:

```python
isolated_config = self.config.copy() if self.config else {}
service = PacerService(config=isolated_config, ...)
```

### Browser Architecture
Dedicated browser services with proper resource management:

```
BrowserService
├── Context Management
├── Resource Cleanup
└── Concurrent Processing Support

PacerNavigator
├── Navigation Utilities
├── Element Interaction
└── Page State Management
```

## Service Dependencies

### Primary Dependencies
- **AsyncServiceBase**: Base class for all services
- **LoggerProtocol**: Standardized logging interface
- **DataUpdaterService**: HTML data processing
- **S3AsyncStorage**: Cloud storage operations
- **DynamoDB**: Data persistence

### Cross-Service Integration
```
PacerOrchestratorService
├── Uses: PacerServiceFactory
├── Creates: Court-specific services
├── Manages: Browser resources
└── Coordinates: Multi-court processing

PacerCaseProcessingService
├── Uses: PacerHTMLProcessingService
├── Uses: PacerCaseClassificationService
├── Integrates: Transfer handling
└── Outputs: Processed case data
```

## File Operations Architecture

### Directory Structure
```
Local Storage: data/YYYYMMDD/dockets/
S3 Storage: {iso_date}/dockets/{filename}
```

### File Management Services
- **PacerFileManagementService**: High-level file operations
- **File Operations**: Local directory management
- **S3 Integration**: Cloud storage synchronization

## Configuration Management

### Feature Flags
```python
{
    'enhanced_html_processing': True,
    'attorney_extraction_enabled': True,
    'transfer_processing_enabled': True
}
```

### Service-Specific Configuration
Each service maintains isolated configuration to prevent interference between court processing instances.

## Error Handling Strategy

### Service-Level Error Handling
- Comprehensive exception handling in each service
- Proper resource cleanup on failures
- Retry logic for transient failures

### Orchestration-Level Recovery
- Failed case reprocessing capabilities
- Resource management and cleanup
- Graceful degradation strategies

## Performance Characteristics

### Concurrency Model
- Semaphore-based browser resource management
- Parallel court processing
- Isolated service instances per court

### Resource Management
- Browser context lifecycle management
- Memory-efficient service caching
- Proper cleanup on completion

## Integration Points

### External Systems
- **PACER Website**: Authentication and data retrieval
- **AWS S3**: Document storage
- **DynamoDB**: Metadata persistence
- **MainOrchestrator**: System-wide coordination

### Internal Systems
- **HTML Services**: Document processing
- **Transformer Services**: Data transformation
- **Repository Layer**: Data access abstraction

## Future Architecture Considerations

### Scalability
- Service-oriented design supports horizontal scaling
- Isolated configurations enable multi-tenant processing
- Resource management supports increased concurrency

### Maintainability
- Clear separation of concerns
- Comprehensive service factory pattern
- Standardized error handling and logging

### Extensibility
- Plugin-based service architecture
- Configurable feature flags
- Modular service composition

## Migration Status

The architecture has fully migrated from the legacy monolithic `src/pacer/` design to the current service-oriented architecture in `src/services/pacer/`. All 24 services are operational and integrated with the main system orchestration.

## Service Health Monitoring

Each service implements health check capabilities through the `AsyncServiceBase` pattern, enabling comprehensive system monitoring and diagnostics.