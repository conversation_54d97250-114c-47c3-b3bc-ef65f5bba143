# Execute Download Workflow After <PERSON>TM<PERSON> Upload to S3

This document provides a comprehensive flowchart of the `execute_download_workflow` process that occurs after <PERSON>TM<PERSON> has been uploaded to S3 in the PACER document processing pipeline.

## Overview

The `execute_download_workflow` method in `PacerDownloadOrchestrationService` manages the complete document download process for PACER cases. This workflow is triggered after the initial docket sheet HTM<PERSON> has been captured and uploaded to S3.

## Main Workflow Flowchart

```mermaid
graph TD
    A[execute_download_workflow Start] --> B{Page Object<br/>Available?}
    
    B -->|No| B1[Log Error:<br/>Browser page required]
    B1 --> B2[Set _processing_error]
    B2 --> Z[Return case_details]
    
    B -->|Yes| C[Store current page<br/>Update navigator]
    
    C --> D{Already Exists &<br/>HTML Only Mode?}
    D -->|Yes| D1[Skip Download<br/>Mark is_downloaded = False]
    D1 --> D2[HTML already uploaded]
    D2 --> Z
    
    D -->|No| E{Should Skip<br/>Download?}
    
    E -->|Yes| E1[Process HTML Only Case]
    E1 --> E2[Apply config overrides]
    E2 --> E3[Set html_only = True]
    E3 --> Z
    
    E -->|No| F{Check Database<br/>for Existing Docket}
    
    F -->|Exists & Downloaded| F1[Skip Duplicate<br/>is_downloaded = True]
    F1 --> F2[Copy existing S3 links]
    F2 --> Z
    
    F -->|Not Downloaded/New| G[STEP 1: Apply Document<br/>Numbering & Capture HTML]
    
    G --> H{HTML Capture<br/>Successful?}
    H -->|No| H1[Set _processing_error]
    H1 --> Z
    
    H -->|Yes| I[Upload HTML to S3<br/>Set s3_html key]
    
    I --> J{HTML Only<br/>Mode?}
    J -->|Yes| J1[Stop Processing<br/>Skip PDF Downloads]
    J1 --> Z
    
    J -->|No| K{Check ignore_download<br/>Configuration}
    
    K -->|Match Found| K1[Apply ignore_download config]
    K1 --> K2[Set MDL, html_only flags]
    K2 --> K3[Save case data]
    K3 --> Z
    
    K -->|No Match| L{Is Explicitly<br/>Requested?}
    
    L -->|Yes| M[Bypass Relevance Check]
    L -->|No| L1{Relevance<br/>Check}
    
    L1 -->|Needs Review| L2[Flag for Review<br/>html_only = True]
    L2 --> Z
    
    L1 -->|Relevant| M
    
    M --> N{Local Artifact<br/>Exists?}
    N -->|Yes| N1[Skip Download<br/>is_downloaded = True]
    N1 --> Z
    
    N -->|No| O{Database<br/>Duplicate Check<br/>2nd Check}
    
    O -->|Exists & Downloaded| O1[Skip Duplicate<br/>Copy S3 links]
    O1 --> Z
    
    O -->|Not Downloaded/New| P{Transfer Case with<br/>_skip_download?}
    
    P -->|Yes| P1[Skip Download<br/>Save without downloads]
    P1 --> Z
    
    P -->|No| Q[STEP 4a: Click<br/>Complaint Link]
    
    Q --> R{Is Removal<br/>Case?}
    
    R -->|Yes| S[Special Removal<br/>Handling]
    R -->|No| T[Standard Complaint<br/>Link Click]
    
    S --> U[STEP 4b: Execute<br/>PDF Download]
    T --> U
    
    U --> V{Download<br/>Successful?}
    
    V -->|Yes| V1[Set is_downloaded = True]
    V1 --> V2[Upload to S3]
    V2 --> V3[Set s3_link]
    V3 --> W
    
    V -->|No| V4[Handle Download<br/>Failure]
    V4 --> W
    
    W[Save Final Case Details<br/>to JSON]
    W --> Z
```

## Detailed Sub-Process: Document Numbering and HTML Capture

```mermaid
graph TD
    DN1[Apply Document Numbering Start] --> DN2[Apply Run Report<br/>Workflow]
    DN2 --> DN3[Wait for Page Settle<br/>1000ms]
    DN3 --> DN4[Wait for Network Idle<br/>10s timeout]
    DN4 --> DN5[Additional Wait 500ms]
    
    DN5 --> DN6{Capture HTML<br/>Content}
    DN6 -->|Navigation Error| DN7[Wait 2s & Retry<br/>Max 3 attempts]
    DN7 --> DN6
    
    DN6 -->|Success| DN8{Validate HTML<br/>Content}
    
    DN8 --> DN9{Is Redirect<br/>Page?}
    DN9 -->|Yes| DN10[Critical Error:<br/>Wrong Page]
    DN10 --> DN_FAIL[Return False]
    
    DN9 -->|No| DN11{Has Docket<br/>Indicators?}
    DN11 -->|No| DN12[Log Warning<br/>Proceed Anyway]
    DN11 -->|Yes| DN13[HTML Validated]
    
    DN12 --> DN13
    DN13 --> DN14[Format Date to<br/>YYYYMMDD]
    
    DN14 --> DN15[Upload HTML to S3]
    DN15 --> DN16[Set s3_html URL<br/>with CDN base]
    DN16 --> DN17[Save case_details<br/>with s3_html]
    DN17 --> DN_SUCCESS[Return True]
```

## Detailed Sub-Process: PDF Download Handling

```mermaid
graph TD
    PD1[handle_pdf_download_async Start] --> PD2{Navigator &<br/>FileManagement<br/>Available?}
    
    PD2 -->|No| PD_FAIL[Return False]
    PD2 -->|Yes| PD3[Set 60s Timeout<br/>Wrapper]
    
    PD3 --> PD4{context_download_path<br/>in Config?}
    PD4 -->|No| PD5[Critical Error:<br/>Browser Isolation Issue]
    PD5 --> PD_FAIL
    
    PD4 -->|Yes| PD6[Create Temp<br/>Download Directory]
    
    PD6 --> PD7{Is Removal<br/>Case?}
    
    PD7 -->|Yes| PD8[Defer Download<br/>Listener Attachment]
    PD7 -->|No| PD9[Attach Download<br/>Event Listener]
    
    PD8 --> PD10[Trigger Removal<br/>Download Action]
    PD9 --> PD11[Trigger Standard<br/>Download Action]
    
    PD10 --> PD12{Download Event<br/>Fired?}
    PD11 --> PD12
    
    PD12 -->|Yes| PD13[Save Downloaded File<br/>to Temp Directory]
    PD12 -->|No| PD14{Check for<br/>Inline PDF}
    
    PD14 -->|Yes| PD15[Generate PDF<br/>from Page]
    PD14 -->|No| PD_FAIL
    
    PD13 --> PD16[Wait for File<br/>Stabilization]
    PD15 --> PD16
    
    PD16 --> PD17[Finalize Download:<br/>Move & Rename]
    PD17 --> PD18[Upload to S3]
    PD18 --> PD19[Set s3_link<br/>in case_details]
    PD19 --> PD20[Cleanup Temp<br/>Directory]
    PD20 --> PD_SUCCESS[Return True]
```

## Key Decision Points

### 1. **HTML Only Mode Checks**
- Already existing cases in html_only mode skip downloads
- Configuration-based html_only flag stops after HTML capture
- ignore_download matches trigger HTML-only processing

### 2. **Duplicate Detection**
- **First Check**: Before HTML capture - checks database for existing downloaded dockets
- **Local File Check**: Looks for existing PDFs/ZIPs in last 7 days
- **Second Check**: After HTML upload - final database check before download

### 3. **Relevance and Request Type**
- Explicitly requested cases (via multiple_courts config) bypass relevance checks
- Cases failing relevance are flagged for review and skip downloads
- Transfer cases with _skip_download flag skip document downloads

### 4. **Download Type Handling**
- **Standard Cases**: Click complaint link in document table
- **Removal Cases**: Special handling for attachment clicking
- **Inline PDFs**: Detection and page.pdf() generation for embedded PDFs

## Error Handling and Recovery

### Critical Validation Points:
1. **Browser Context Isolation**: Validates context_download_path exists
2. **Page State**: Checks page is not closed before operations
3. **HTML Content**: Validates captured HTML is docket sheet, not redirect
4. **File Stabilization**: Ensures downloaded files are complete
5. **Timeout Protection**: 60-second timeout on entire download operation

### Failure Recovery:
- Navigation errors: Retry with delays
- Download failures: Check for inline PDFs
- Missing files: Preserve temp directories for debugging
- Database errors: Continue with warnings

## Data Flow Summary

1. **Input**: case_details dict with docket information
2. **HTML Capture**: Apply document numbering → Capture HTML → Upload to S3
3. **Validation**: Check duplicates, relevance, and configuration rules
4. **Download**: Click links → Handle download events → Save files
5. **Upload**: Finalize files → Upload to S3 → Set s3_link
6. **Output**: Updated case_details with download status and S3 URLs

## Configuration Parameters

Key configuration values used:
- `html_only`: Skip PDF downloads entirely
- `ignore_download`: Pattern-based download skipping
- `context_download_path`: Browser-specific download directory
- `download_save_as_completion_timeout_s`: Download timeout (default 600s)
- `inline_pdf_check_delay_ms`: Wait before inline PDF check (default 3500ms)
- `zip_pdf_downloads`: Whether to zip PDFs before upload
- `cleanup_temp_downloads_on_failure`: Cleanup temp files on failure

## Integration Points

- **PacerNavigator**: Browser automation and page interaction
- **FileManagementService**: File operations and finalization
- **S3 Storage**: HTML and document uploads
- **PACER Repository**: Database duplicate checking
- **Relevance Service**: Case relevance determination
- **Ignore Download Service**: Configuration-based skip rules