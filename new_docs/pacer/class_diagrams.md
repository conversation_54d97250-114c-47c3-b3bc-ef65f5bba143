# PACER System Class Diagrams

## Core Service Architecture

```mermaid
classDiagram
    class AsyncServiceBase {
        <<abstract>>
        +logger: LoggerProtocol
        +config: Dict
        +__init__(logger, config)
        +log_info(message)
        +log_error(message)
        +log_debug(message)
        +get_config_value(key, default)
    }
    
    class PacerOrchestratorService {
        -service_factory: PacerServiceFactory
        -browser_semaphore: Semaphore
        -s3_storage: S3AsyncStorage
        +process_courts_async(courts)
        +process_single_court(court_id)
        -_manage_browser_resources()
        -_cleanup_resources()
    }
    
    class PacerServiceFactory {
        -config: Dict
        -logger: LoggerProtocol
        -cached_services: Dict
        +create_html_processing_service(court_id)
        +create_case_classification_service(court_id)
        +create_case_processing_service(court_id)
        +create_transfer_service(court_id)
        +wire_services(court_id, dependencies)
        -_create_isolated_config(court_id)
    }
    
    class PacerCaseProcessingService {
        -html_processing_service: PacerHTMLProcessingService
        -classification_service: PacerCaseClassificationService
        -file_operations_service: PacerFileOperationsService
        +process_case(case_data)
        +extract_case_details(html_content)
        +coordinate_classification(case_data)
        -_validate_case_data(data)
    }
    
    class PacerHTMLProcessingService {
        -data_updater_service: DataUpdaterService
        -s3_manager: S3Manager
        +parse_html_content(html)
        +extract_case_data(parsed_html)
        +handle_page_variations(page_type)
        -_clean_html_content(html)
    }
    
    class PacerCaseClassificationService {
        -transfer_service: PacerTransferService
        -relevance_service: RelevanceService
        +classify_case(case_data)
        +detect_removal(case_content)
        +assess_relevance(case_data)
        -_apply_classification_rules(data)
    }
    
    AsyncServiceBase <|-- PacerOrchestratorService
    AsyncServiceBase <|-- PacerCaseProcessingService
    AsyncServiceBase <|-- PacerHTMLProcessingService
    AsyncServiceBase <|-- PacerCaseClassificationService
    
    PacerOrchestratorService --> PacerServiceFactory : uses
    PacerServiceFactory --> PacerCaseProcessingService : creates
    PacerServiceFactory --> PacerHTMLProcessingService : creates
    PacerServiceFactory --> PacerCaseClassificationService : creates
    
    PacerCaseProcessingService --> PacerHTMLProcessingService : depends on
    PacerCaseProcessingService --> PacerCaseClassificationService : depends on
```

## Browser Management Architecture

```mermaid
classDiagram
    class BrowserService {
        -browser_instance: Browser
        -context_pool: List[BrowserContext]
        -semaphore: Semaphore
        +create_context()
        +release_context(context)
        +manage_resources()
        -_initialize_browser()
        -_cleanup_browser()
    }
    
    class PacerNavigator {
        -browser_context: BrowserContext
        -current_page: Page
        +navigate_to_url(url)
        +click_element(selector)
        +fill_form_field(selector, value)
        +wait_for_element(selector)
        +extract_page_content()
        -_handle_navigation_errors()
    }
    
    class PacerNavigationService {
        -navigator: PacerNavigator
        -authentication_service: PacerAuthenticationService
        +navigate_to_court(court_id)
        +perform_search(query)
        +handle_pagination()
        -_validate_page_state()
    }
    
    class PacerAuthenticationService {
        -credentials: Dict
        -session_manager: SessionManager
        +authenticate(credentials)
        +validate_session()
        +refresh_session()
        -_handle_login_form()
        -_store_session_cookies()
    }
    
    BrowserService --> PacerNavigator : creates
    PacerNavigationService --> PacerNavigator : uses
    PacerNavigationService --> PacerAuthenticationService : depends on
```

## Data Management Layer

```mermaid
classDiagram
    class PacerFileManagementService {
        -data_dir: Path
        -s3_storage: S3AsyncStorage
        +get_dockets_directory(iso_date)
        +save_report_data(data, filename)
        +save_review_cases(cases)
        +manage_file_lifecycle()
        -_create_directory_structure()
        -_validate_file_paths()
    }
    
    class DataUpdaterService {
        -s3_manager: S3Manager
        -dynamodb_client: DynamoDBClient
        +update_case_data(case_data)
        +save_html_content(html, metadata)
        +update_metadata(case_id, metadata)
        -_validate_data_integrity()
        -_handle_update_conflicts()
    }
    
    class S3AsyncStorage {
        -s3_client: S3Client
        -bucket_name: str
        +upload_file(file_path, s3_key)
        +download_file(s3_key, local_path)
        +list_objects(prefix)
        +delete_object(s3_key)
        -_handle_s3_errors()
    }
    
    class ReportService {
        -file_management_service: PacerFileManagementService
        -template_engine: TemplateEngine
        +generate_report(report_type, data)
        +save_report(report, filename)
        +schedule_report_generation()
        -_format_report_data()
    }
    
    PacerFileManagementService --> S3AsyncStorage : uses
    DataUpdaterService --> S3AsyncStorage : uses
    ReportService --> PacerFileManagementService : depends on
```

## Classification and Analysis Layer

```mermaid
classDiagram
    class PacerCaseClassificationService {
        -classification_rules: Dict
        -ml_models: Dict
        +classify_case_type(case_data)
        +determine_relevance(case_content)
        +extract_case_features(data)
        -_apply_business_rules()
        -_score_relevance()
    }
    
    class PacerTransferService {
        -transfer_patterns: List[Pattern]
        -district_courts_repo: DistrictCourtsRepository
        +detect_transfer_case(case_data)
        +extract_transfer_details(content)
        +process_transfer_order(order_data)
        -_parse_transfer_language()
        -_validate_transfer_data()
    }
    
    class RelevanceService {
        -relevance_criteria: Dict
        -scoring_algorithms: Dict
        +calculate_relevance_score(case_data)
        +apply_relevance_filters(cases)
        +update_relevance_rules(rules)
        -_weighted_scoring()
        -_threshold_filtering()
    }
    
    class PacerAnalyticsService {
        -metrics_collector: MetricsCollector
        -analytics_engine: AnalyticsEngine
        +collect_processing_metrics(metrics)
        +generate_analytics_report()
        +track_performance_indicators()
        -_aggregate_metrics()
        -_calculate_trends()
    }
    
    PacerCaseClassificationService --> PacerTransferService : uses
    PacerCaseClassificationService --> RelevanceService : uses
    PacerAnalyticsService --> PacerCaseClassificationService : monitors
```

## Configuration and Factory Pattern

```mermaid
classDiagram
    class PacerServiceFactory {
        -config: Dict
        -logger: LoggerProtocol
        -service_cache: Dict
        +create_orchestrator_service()
        +create_case_processing_service(court_id)
        +create_html_processing_service(court_id)
        +wire_services(court_id, dependencies)
        -_create_isolated_config(court_id)
        -_cache_service(service_key, service)
    }
    
    class ConfigurationService {
        -config_data: Dict
        -feature_flags: Dict
        +load_configuration(config_path)
        +get_feature_flag(flag_name)
        +validate_configuration()
        +reload_configuration()
        -_merge_configurations()
        -_validate_config_schema()
    }
    
    class FeatureFlags {
        -flags: Dict[str, bool]
        +is_enabled(feature_name)
        +enable_feature(feature_name)
        +disable_feature(feature_name)
        +get_all_flags()
        -_validate_flag_name()
    }
    
    class ServiceRegistry {
        -registered_services: Dict
        -service_dependencies: Dict
        +register_service(name, service_class)
        +get_service(name)
        +resolve_dependencies(service_name)
        -_validate_dependencies()
    }
    
    PacerServiceFactory --> ConfigurationService : uses
    ConfigurationService --> FeatureFlags : manages
    PacerServiceFactory --> ServiceRegistry : uses
```

## Protocol and Interface Layer

```mermaid
classDiagram
    class LoggerProtocol {
        <<interface>>
        +info(message: str)
        +error(message: str)
        +debug(message: str)
        +warning(message: str)
    }
    
    class StorageProtocol {
        <<interface>>
        +save(key: str, data: Any)
        +load(key: str)
        +delete(key: str)
        +exists(key: str)
    }
    
    class RepositoryProtocol {
        <<interface>>
        +find_by_id(id: str)
        +save(entity: Any)
        +delete(id: str)
        +find_all()
    }
    
    class ServiceProtocol {
        <<interface>>
        +initialize()
        +process(data: Any)
        +cleanup()
        +health_check()
    }
    
    class AsyncServiceBase {
        +logger: LoggerProtocol
        +config: Dict
        +initialize()
        +cleanup()
        +health_check()
    }
    
    AsyncServiceBase ..> LoggerProtocol : implements
    AsyncServiceBase ..> ServiceProtocol : implements
    S3AsyncStorage ..> StorageProtocol : implements
    DistrictCourtsRepository ..> RepositoryProtocol : implements
```

## Error Handling and Recovery

```mermaid
classDiagram
    class ErrorHandler {
        -retry_strategies: Dict
        -error_classifiers: List
        +handle_error(error, context)
        +classify_error(error)
        +determine_retry_strategy(error_type)
        +execute_recovery_action(action)
        -_log_error_details()
    }
    
    class RetryStrategy {
        <<abstract>>
        -max_retries: int
        -backoff_factor: float
        +should_retry(attempt, error)
        +calculate_delay(attempt)
        +execute_retry(operation)
    }
    
    class ExponentialBackoffStrategy {
        +should_retry(attempt, error)
        +calculate_delay(attempt)
        -_exponential_delay()
    }
    
    class CircuitBreaker {
        -failure_threshold: int
        -recovery_timeout: int
        -state: CircuitState
        +call(operation)
        +record_success()
        +record_failure()
        -_should_attempt_reset()
    }
    
    RetryStrategy <|-- ExponentialBackoffStrategy
    ErrorHandler --> RetryStrategy : uses
    ErrorHandler --> CircuitBreaker : uses
```

## Workflow Orchestration

```mermaid
classDiagram
    class WorkflowOrchestrator {
        -workflow_steps: List[WorkflowStep]
        -execution_context: ExecutionContext
        +execute_workflow(workflow_id)
        +pause_workflow()
        +resume_workflow()
        +cancel_workflow()
        -_validate_workflow()
        -_handle_step_failure()
    }
    
    class WorkflowStep {
        <<abstract>>
        -step_id: str
        -dependencies: List[str]
        +execute(context)
        +validate_preconditions()
        +handle_failure()
        +rollback()
    }
    
    class AuthenticationStep {
        +execute(context)
        +validate_preconditions()
        -_perform_authentication()
    }
    
    class CaseProcessingStep {
        +execute(context)
        +validate_preconditions()
        -_process_cases()
    }
    
    class DataPersistenceStep {
        +execute(context)
        +validate_preconditions()
        -_save_processed_data()
    }
    
    WorkflowStep <|-- AuthenticationStep
    WorkflowStep <|-- CaseProcessingStep
    WorkflowStep <|-- DataPersistenceStep
    WorkflowOrchestrator --> WorkflowStep : manages
```

## Resource Management

```mermaid
classDiagram
    class ResourceManager {
        -resource_pools: Dict
        -allocation_strategies: Dict
        +acquire_resource(resource_type)
        +release_resource(resource)
        +monitor_resource_usage()
        -_create_resource_pool()
        -_cleanup_expired_resources()
    }
    
    class BrowserResourcePool {
        -available_contexts: Queue
        -active_contexts: Set
        -max_contexts: int
        +acquire_context()
        +release_context(context)
        +cleanup_context(context)
        -_create_new_context()
    }
    
    class MemoryResourceMonitor {
        -memory_threshold: float
        -monitoring_interval: int
        +start_monitoring()
        +check_memory_usage()
        +trigger_cleanup()
        -_calculate_memory_usage()
    }
    
    class ResourceLease {
        -resource: Any
        -lease_time: datetime
        -expiry_time: datetime
        +is_expired()
        +extend_lease()
        +release()
    }
    
    ResourceManager --> BrowserResourcePool : manages
    ResourceManager --> MemoryResourceMonitor : uses
    ResourceManager --> ResourceLease : creates
```

This comprehensive class diagram documentation provides detailed views of all major components in the PACER system, showing their relationships, dependencies, and key methods.