# PACER Browser Architecture

## Overview

The PACER system implements a sophisticated browser management architecture designed for concurrent, reliable, and resource-efficient web scraping operations. This architecture handles multiple browser contexts, resource pooling, and comprehensive error recovery.

## Core Components

### 1. Browser Service Layer

#### BrowserService
**Location**: `src/services/pacer/browser/browser_service.py`

```python
class BrowserService(AsyncServiceBase):
    """
    Core browser management service providing:
    - Browser instance lifecycle management
    - Context creation and cleanup
    - Resource pooling and allocation
    - Error handling and recovery
    """
    
    def __init__(self, config: Dict, logger: LoggerProtocol):
        self.browser_instance = None
        self.context_pool = []
        self.active_contexts = set()
        self.semaphore = Semaphore(config.get('max_concurrent_contexts', 5))
        self.resource_monitor = ResourceMonitor()
    
    async def create_context(self) -> BrowserContext:
        """Create new browser context with proper resource management"""
        
    async def release_context(self, context: BrowserContext):
        """Release browser context and cleanup resources"""
        
    async def manage_resources(self):
        """Monitor and manage browser resource usage"""
```

#### PacerNavigator
**Location**: `src/services/pacer/browser/navigator.py`

```python
class PacerNavigator(AsyncServiceBase):
    """
    Navigation utilities and page interaction management:
    - Page navigation and state management
    - Element interaction and form handling
    - Wait strategies and timeout management
    - Error recovery and retry logic
    """
    
    def __init__(self, browser_context: BrowserContext, logger: LoggerProtocol):
        self.browser_context = browser_context
        self.current_page = None
        self.navigation_history = []
        self.wait_strategies = WaitStrategies()
    
    async def navigate_to_url(self, url: str) -> Page:
        """Navigate to URL with error handling and retries"""
        
    async def click_element(self, selector: str, timeout: int = 30000):
        """Click element with wait and error handling"""
        
    async def extract_page_content(self) -> str:
        """Extract page content with validation"""
```

## Resource Management Architecture

### Browser Context Pool

```mermaid
graph TD
    A[Browser Service] --> B[Context Pool Manager]
    B --> C[Available Contexts Queue]
    B --> D[Active Contexts Set]
    B --> E[Context Factory]
    
    C --> F[Context 1]
    C --> G[Context 2]
    C --> H[Context N]
    
    D --> I[Court A Processing]
    D --> J[Court B Processing]
    D --> K[Court N Processing]
    
    E --> L[Create New Context]
    L --> M[Initialize Settings]
    M --> N[Add to Pool]
```

### Semaphore-Based Concurrency Control

```python
class BrowserResourceManager:
    """
    Manages browser resource allocation using semaphore pattern
    """
    
    def __init__(self, max_concurrent: int = 5):
        self.semaphore = Semaphore(max_concurrent)
        self.active_contexts = {}
        self.resource_metrics = ResourceMetrics()
    
    async def acquire_context(self, court_id: str) -> BrowserContext:
        """
        Acquire browser context with semaphore control
        
        Flow:
        1. Wait for semaphore availability
        2. Create or reuse browser context
        3. Register context for monitoring
        4. Return context for use
        """
        async with self.semaphore:
            context = await self._create_context()
            self.active_contexts[court_id] = context
            self.resource_metrics.record_acquisition(court_id)
            return context
    
    async def release_context(self, court_id: str):
        """
        Release browser context and update metrics
        
        Flow:
        1. Cleanup context resources
        2. Remove from active tracking
        3. Update resource metrics
        4. Release semaphore slot
        """
        if court_id in self.active_contexts:
            context = self.active_contexts[court_id]
            await self._cleanup_context(context)
            del self.active_contexts[court_id]
            self.resource_metrics.record_release(court_id)
```

## Browser Configuration Management

### Configuration Isolation

```python
class BrowserConfigurationManager:
    """
    Manages browser configurations with isolation per court
    """
    
    def create_isolated_browser_config(self, court_id: str, base_config: Dict) -> Dict:
        """
        Create isolated browser configuration for specific court
        
        Isolation Features:
        - Separate user data directories
        - Court-specific headers and settings
        - Isolated cookie storage
        - Custom viewport and user agent
        """
        isolated_config = {
            'user_data_dir': f"browser_data/{court_id}",
            'headless': base_config.get('headless', True),
            'viewport': base_config.get('viewport', {'width': 1920, 'height': 1080}),
            'user_agent': self._generate_court_user_agent(court_id),
            'extra_http_headers': self._get_court_headers(court_id),
            'ignore_https_errors': True,
            'timeout': base_config.get('timeout', 30000)
        }
        return isolated_config
```

### Browser Settings Optimization

```python
class BrowserOptimizationSettings:
    """
    Optimized browser settings for PACER scraping
    """
    
    @staticmethod
    def get_optimized_settings() -> Dict:
        return {
            # Performance optimizations
            'args': [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--window-size=1920,1080',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',  # Speed up loading
                '--disable-javascript',  # Only when JS not needed
            ],
            
            # Memory management
            'ignore_default_args': ['--enable-automation'],
            'slow_mo': 100,  # Slight delay to avoid detection
            
            # Network settings
            'timeout': 30000,
            'navigation_timeout': 60000,
            'page_timeout': 45000,
        }
```

## Navigation Patterns

### Page State Management

```python
class PageStateManager:
    """
    Manages page state and navigation history
    """
    
    def __init__(self, navigator: PacerNavigator):
        self.navigator = navigator
        self.state_history = []
        self.current_state = None
        self.state_validators = {}
    
    async def navigate_with_state_tracking(self, url: str, expected_state: str):
        """
        Navigate to URL and track page state
        
        Flow:
        1. Record current state
        2. Navigate to new URL
        3. Validate expected state
        4. Update state history
        5. Handle state transition errors
        """
        previous_state = self.current_state
        
        try:
            page = await self.navigator.navigate_to_url(url)
            new_state = await self._detect_page_state(page)
            
            if new_state != expected_state:
                await self._handle_unexpected_state(new_state, expected_state)
            
            self._update_state_history(previous_state, new_state, url)
            self.current_state = new_state
            
        except NavigationError as e:
            await self._handle_navigation_error(e, previous_state)
```

### Wait Strategies

```python
class WaitStrategies:
    """
    Comprehensive wait strategies for different page elements and states
    """
    
    async def wait_for_pacer_page_load(self, page: Page, timeout: int = 30000):
        """Wait for PACER page to fully load"""
        await page.wait_for_load_state('networkidle', timeout=timeout)
        await page.wait_for_selector('body', timeout=timeout)
        
        # PACER-specific wait conditions
        await self._wait_for_pacer_elements(page)
    
    async def wait_for_search_results(self, page: Page, timeout: int = 45000):
        """Wait for search results to load"""
        await page.wait_for_selector('table.results', timeout=timeout)
        await page.wait_for_function(
            'document.querySelectorAll("table.results tr").length > 1',
            timeout=timeout
        )
    
    async def wait_for_case_details(self, page: Page, timeout: int = 30000):
        """Wait for case details page to load"""
        await page.wait_for_selector('.case-details', timeout=timeout)
        await page.wait_for_load_state('domcontentloaded', timeout=timeout)
```

## Error Handling and Recovery

### Browser Error Recovery

```python
class BrowserErrorRecovery:
    """
    Comprehensive error recovery for browser operations
    """
    
    def __init__(self, browser_service: BrowserService):
        self.browser_service = browser_service
        self.recovery_strategies = {
            'timeout': self._handle_timeout_error,
            'navigation': self._handle_navigation_error,
            'element_not_found': self._handle_element_error,
            'page_crash': self._handle_page_crash,
            'context_disposed': self._handle_context_disposed
        }
    
    async def handle_browser_error(self, error: Exception, context: Dict) -> bool:
        """
        Handle browser errors with appropriate recovery strategy
        
        Returns:
            bool: True if recovery successful, False if unrecoverable
        """
        error_type = self._classify_error(error)
        recovery_strategy = self.recovery_strategies.get(error_type)
        
        if recovery_strategy:
            return await recovery_strategy(error, context)
        else:
            return await self._handle_unknown_error(error, context)
    
    async def _handle_timeout_error(self, error: TimeoutError, context: Dict) -> bool:
        """Handle timeout errors with retry logic"""
        retry_count = context.get('retry_count', 0)
        max_retries = context.get('max_retries', 3)
        
        if retry_count < max_retries:
            await asyncio.sleep(2 ** retry_count)  # Exponential backoff
            return True  # Indicate retry should be attempted
        return False
    
    async def _handle_page_crash(self, error: Exception, context: Dict) -> bool:
        """Handle page crashes by recreating context"""
        court_id = context.get('court_id')
        if court_id:
            await self.browser_service.recreate_context(court_id)
            return True
        return False
```

### Resource Cleanup Patterns

```python
class ResourceCleanupManager:
    """
    Manages comprehensive resource cleanup for browser operations
    """
    
    async def cleanup_browser_context(self, context: BrowserContext):
        """
        Comprehensive cleanup of browser context
        
        Cleanup Steps:
        1. Close all pages in context
        2. Clear cookies and storage
        3. Cancel pending requests
        4. Close context
        5. Update resource metrics
        """
        try:
            # Close all pages
            for page in context.pages:
                await page.close()
            
            # Clear storage
            await context.clear_cookies()
            await context.clear_permissions()
            
            # Close context
            await context.close()
            
        except Exception as e:
            self.logger.error(f"Error during context cleanup: {e}")
        finally:
            self._update_cleanup_metrics()
    
    async def emergency_cleanup(self):
        """Emergency cleanup for system shutdown"""
        # Force close all browser instances
        # Clean up temporary files
        # Release all semaphore slots
        # Log cleanup status
```

## Performance Monitoring

### Browser Performance Metrics

```python
class BrowserPerformanceMonitor:
    """
    Monitors browser performance and resource usage
    """
    
    def __init__(self):
        self.metrics = {
            'context_creation_time': [],
            'page_load_times': [],
            'memory_usage': [],
            'cpu_usage': [],
            'error_rates': {},
            'throughput': []
        }
    
    async def monitor_context_performance(self, context: BrowserContext):
        """Monitor performance of browser context"""
        start_time = time.time()
        
        # Monitor memory usage
        memory_info = await self._get_memory_info(context)
        self.metrics['memory_usage'].append(memory_info)
        
        # Monitor page load performance
        for page in context.pages:
            load_time = await self._measure_page_load_time(page)
            self.metrics['page_load_times'].append(load_time)
    
    def generate_performance_report(self) -> Dict:
        """Generate comprehensive performance report"""
        return {
            'avg_context_creation_time': np.mean(self.metrics['context_creation_time']),
            'avg_page_load_time': np.mean(self.metrics['page_load_times']),
            'peak_memory_usage': max(self.metrics['memory_usage']),
            'error_rate': self._calculate_error_rate(),
            'throughput_per_hour': self._calculate_throughput()
        }
```

## Integration with PACER Services

### Service Integration Pattern

```python
class PacerBrowserIntegration:
    """
    Integration layer between browser services and PACER processing services
    """
    
    def __init__(self, browser_service: BrowserService, navigator: PacerNavigator):
        self.browser_service = browser_service
        self.navigator = navigator
        self.integration_patterns = {
            'authentication': self._handle_authentication_integration,
            'case_processing': self._handle_case_processing_integration,
            'data_extraction': self._handle_data_extraction_integration
        }
    
    async def integrate_with_service(self, service_type: str, service_instance: Any):
        """
        Integrate browser capabilities with PACER service
        
        Integration includes:
        - Browser context sharing
        - Navigation coordination
        - Error handling coordination
        - Resource management coordination
        """
        integration_handler = self.integration_patterns.get(service_type)
        if integration_handler:
            await integration_handler(service_instance)
```

## Configuration Examples

### Production Browser Configuration

```yaml
browser:
  max_concurrent_contexts: 5
  context_timeout: 300000  # 5 minutes
  page_timeout: 60000      # 1 minute
  navigation_timeout: 45000 # 45 seconds
  
  optimization:
    headless: true
    disable_images: true
    disable_css: false
    disable_javascript: false
    
  resource_limits:
    max_memory_mb: 512
    max_cpu_percent: 80
    
  error_handling:
    max_retries: 3
    retry_delay: 2000
    exponential_backoff: true
    
  monitoring:
    performance_tracking: true
    resource_monitoring: true
    error_reporting: true
```

### Development Browser Configuration

```yaml
browser:
  max_concurrent_contexts: 2
  context_timeout: 120000
  page_timeout: 30000
  navigation_timeout: 20000
  
  optimization:
    headless: false  # For debugging
    disable_images: false
    slow_mo: 500     # Slow down for observation
    
  debugging:
    devtools: true
    console_logging: true
    network_logging: true
    
  resource_limits:
    max_memory_mb: 1024  # Higher for development
    max_cpu_percent: 90
```

This browser architecture provides a robust, scalable, and maintainable foundation for all PACER web scraping operations, with comprehensive resource management, error handling, and performance monitoring capabilities.