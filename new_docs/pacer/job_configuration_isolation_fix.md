# PACER Job Configuration Isolation Fix

## Problem Analysis

The job processing architecture was designed to solve configuration isolation problems, but the current `DocketProcessingJob` implementation has a critical flaw that allows configuration leakage between parallel jobs.

## Root Cause

The `DocketProcessingJob` class in `src/services/pacer/row_processing_service.py` uses a shared configuration reference instead of an isolated snapshot:

```python
@dataclass
class DocketProcessingJob:
    # ...
    config: dict[str, Any]  # ❌ SHARED REFERENCE - NOT ISOLATED!
```

This causes:
- Configuration leakage between jobs
- Cross-court contamination during parallel processing
- Browser config pollution
- Inconsistent manual `self.config.copy()` calls throughout the codebase

## Solution

Follow the FB ads `ProcessFirmJob` pattern which correctly uses `config_snapshot`:

### 1. Update DocketProcessingJob Class

**File:** `src/services/pacer/row_processing_service.py`

**Change:**
```python
@dataclass
class DocketProcessingJob:
    # ...
    relevance_engine: RelevanceService
    config_snapshot: dict[str, Any]  # ✅ ISOLATED CONFIG SNAPSHOT PER JOB
    file_management_service: Any
```

### 2. Update Job Creation

**In `process_report_rows` method:**
```python
# Create enriched config for jobs that includes the iso_date and processor_config
# CRITICAL: Create isolated config snapshot for each job to prevent cross-contamination
job_config_snapshot = self.config.copy() if self.config else {}
job_config_snapshot["iso_date"] = iso_date
if processor_config:
    job_config_snapshot.update(processor_config)

# Create the job
job = DocketProcessingJob(
    # ...
    config_snapshot=job_config_snapshot,  # Use isolated config snapshot
    # ...
)
```

**In `process_single_row_docket` method:**
```python
# Create enriched config for the job
# CRITICAL: Create isolated config snapshot for single docket job
job_config_snapshot = self.config.copy() if self.config else {}
# Add iso_date if not present
if "iso_date" not in job_config_snapshot:
    job_config_snapshot["iso_date"] = DateType.today().strftime("%Y%m%d")
if processor_config:
    job_config_snapshot.update(processor_config)

job = DocketProcessingJob(
    # ...
    config_snapshot=job_config_snapshot,  # Use isolated config snapshot
    # ...
)
```

### 3. Update All References

Replace all instances of `job.config` with `job.config_snapshot` throughout the file:

- `job.config.get("max_job_retries", 2)` → `job.config_snapshot.get("max_job_retries", 2)`
- `job.config.get("job_retry_delay_seconds", 5)` → `job.config_snapshot.get("job_retry_delay_seconds", 5)`
- `job.config.get("iso_date")` → `job.config_snapshot.get("iso_date")`
- `job.config.get("_processing_explicit_dockets", False)` → `job.config_snapshot.get("_processing_explicit_dockets", False)`

### 4. Remove Manual Config Copying

Once the job-level isolation is in place, remove the manual `isolated_config = self.config.copy()` calls in service creation methods like:
- `_get_case_processing_service`
- `_get_download_orchestration_service`
- `_get_ignore_download_service`
- `_get_file_operations_service`
- `_get_case_classification_service`

The services should use the job's `config_snapshot` instead.

## Benefits

1. **True Job Isolation**: Each job gets its own configuration copy
2. **Safe Parallel Processing**: Multiple courts can be processed simultaneously without interference
3. **Browser Config Isolation**: Browser settings, download paths, and timeouts are properly isolated
4. **Consistent Architecture**: Matches the proven FB ads job pattern
5. **Eliminates Manual Copying**: No more error-prone manual `config.copy()` calls

## Testing

After implementing these changes:

1. Test parallel processing of multiple courts
2. Verify browser contexts are properly isolated
3. Confirm download paths don't interfere between jobs
4. Check that configuration changes in one job don't affect others

## Files to Modify

- `src/services/pacer/row_processing_service.py` (primary changes)
- Any other files that create `DocketProcessingJob` instances