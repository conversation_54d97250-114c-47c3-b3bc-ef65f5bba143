# PACER Services Documentation - 2025

## Overview

This directory contains the current documentation for the PACER (Public Access to Court Electronic Records) services architecture as of July 2025. The PACER system has evolved from a monolithic design to a comprehensive service-oriented architecture with 24 specialized services.

## Documentation Structure

### 📋 Architecture Documentation

#### [Current Architecture 2025](./current_architecture_2025.md)
- **Purpose**: Comprehensive overview of the current service-oriented architecture
- **Contents**: 
  - Service inventory (24 services)
  - Architecture layers and responsibilities
  - Service dependencies and integration points
  - Performance characteristics and scalability considerations
- **Audience**: Architects, senior developers, system administrators

#### [Service Specifications 2025](./service_specifications_2025.md)
- **Purpose**: Detailed specifications for each of the 24 PACER services
- **Contents**:
  - Individual service documentation
  - Key methods and responsibilities
  - Dependencies and integration patterns
  - Performance characteristics
- **Audience**: Developers, QA engineers, technical leads

#### [Migration Summary 2025](./migration_summary_2025.md)
- **Purpose**: Documentation of the migration from legacy monolithic to current service architecture
- **Contents**:
  - Legacy vs current architecture comparison
  - Key architectural changes and improvements
  - Migration status and benefits
  - Future evolution plans
- **Audience**: Technical leads, project managers, stakeholders

## Quick Reference

### Service Categories

| Category | Count | Key Services |
|----------|-------|--------------|
| **Orchestration** | 3 | PacerOrchestratorService, PacerDocketProcessingOrchestratorService |
| **Processing** | 4 | PacerCaseProcessingService, PacerHTMLProcessingService |
| **Classification** | 3 | PacerCaseClassificationService, PacerTransferService |
| **Infrastructure** | 5 | PacerAuthenticationService, BrowserService |
| **Data Management** | 4 | PacerFileManagementService, ReportService |
| **Control** | 3 | PacerIgnoreDownloadService, PacerServiceFactory |
| **Browser** | 2 | BrowserService, PacerNavigator |

### Key Architectural Patterns

- **Service Factory Pattern**: Centralized service creation and dependency injection
- **Configuration Isolation**: Per-service configuration to prevent cross-contamination
- **Resource Management**: Comprehensive browser and memory resource management
- **Async Service Base**: Standardized base class for all services

### Entry Points

```
Main System Flow:
src/main.py → MainOrchestrator → ScrapingOrchestrator → PacerOrchestratorService
```

## Architecture Highlights

### 🏗️ Service-Oriented Design
- **24 specialized services** replacing legacy monolithic design
- **Clear separation of concerns** with single-responsibility services
- **Dependency injection** through PacerServiceFactory
- **Isolated configurations** preventing cross-court contamination

### 🔄 Processing Workflow
```
Authentication → Navigation → Court Processing → Case Processing → Classification → Data Persistence
```

### 🌐 Browser Management
- **Semaphore-based concurrency control** for browser resources
- **Context management** with automatic cleanup
- **Parallel processing** across multiple courts
- **Resource pooling** for optimal performance

### 📁 File Operations
- **Local Storage**: `data/YYYYMMDD/dockets/`
- **S3 Storage**: `{iso_date}/dockets/{filename}`
- **Coordinated file management** between local and cloud storage

## Development Guidelines

### Service Development
1. **Inherit from AsyncServiceBase** for standardized functionality
2. **Use PacerServiceFactory** for service creation and wiring
3. **Implement isolated configurations** to prevent cross-service interference
4. **Follow dependency injection patterns** for testability

### Testing Strategy
- **Unit Testing**: Individual service testing with mocked dependencies
- **Integration Testing**: Service interaction and workflow testing
- **Contract Testing**: Interface compatibility between services

### Error Handling
- **Service-level error handling** with proper exception management
- **Resource cleanup** on failures
- **Graceful degradation** strategies
- **Comprehensive logging** for debugging

## Performance Considerations

### Concurrency
- **Browser resource management** with semaphore controls
- **Parallel court processing** capabilities
- **Isolated service instances** per court

### Memory Management
- **Service caching** with proper lifecycle management
- **Configuration isolation** preventing memory leaks
- **Resource pooling** for browser contexts

### Scalability
- **Horizontal scaling** ready architecture
- **Service-oriented design** supports distributed deployment
- **Resource management** supports increased concurrency

## Integration Points

### External Systems
- **PACER Website**: Authentication and data retrieval
- **AWS S3**: Document storage and retrieval
- **DynamoDB**: Metadata persistence and querying
- **MainOrchestrator**: System-wide workflow coordination

### Internal Systems
- **HTML Services**: Document processing and parsing
- **Transformer Services**: Data transformation and normalization
- **Repository Layer**: Data access abstraction and management

## Migration Status

### ✅ Completed
- Service decomposition (24 services operational)
- Dependency injection implementation
- Configuration isolation
- Browser resource management
- Comprehensive error handling
- Testing infrastructure

### 📋 Legacy System
- **Legacy `src/pacer/` directory**: Preserved for reference only
- **Not used in production**: Current system uses `src/services/pacer/`
- **Backward compatibility**: Maintained through service interfaces

## Getting Started

### For Developers
1. Review [Service Specifications](./service_specifications_2025.md) for detailed service documentation
2. Understand the [Current Architecture](./current_architecture_2025.md) for system overview
3. Check [Migration Summary](./migration_summary_2025.md) for historical context

### For Architects
1. Start with [Current Architecture](./current_architecture_2025.md) for system design
2. Review [Migration Summary](./migration_summary_2025.md) for evolution context
3. Reference [Service Specifications](./service_specifications_2025.md) for implementation details

### For Operations
1. Focus on service health monitoring capabilities
2. Understand resource management patterns
3. Review error handling and recovery strategies

## Support and Maintenance

### Monitoring
- Each service implements health check capabilities
- Comprehensive logging through LoggerProtocol
- Performance metrics collection via PacerAnalyticsService

### Troubleshooting
- Service-level error isolation
- Comprehensive logging and debugging capabilities
- Clear error propagation and handling

### Updates and Maintenance
- Service-oriented design enables independent updates
- Feature flags support gradual rollouts
- Minimal cross-service impact for changes

---

**Last Updated**: July 2025  
**Architecture Version**: Service-Oriented (24 services)  
**Migration Status**: Complete  
**Legacy Status**: `src/pacer/` preserved for reference only