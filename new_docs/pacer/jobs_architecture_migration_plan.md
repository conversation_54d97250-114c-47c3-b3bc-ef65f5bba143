# PACER Jobs Architecture Migration Plan

## Executive Summary

This document outlines a comprehensive plan to migrate the PACER services from the current browser context isolation pattern to a jobs-based architecture similar to the FB ads implementation. This migration will simplify the codebase, eliminate complex configuration management, and provide better scalability and maintainability.

## Current Architecture Problems

### 1. Configuration Management Complexity
- Deep copying configurations in 5+ locations
- Manual tracking of isolated configs
- Risk of cross-court contamination
- 14+ hours spent on configuration isolation

### 2. Service Instance Proliferation
```python
# Current pattern - multiple service instances
isolated_docket_orchestrator = PacerDocketProcessingOrchestratorService(...)
isolated_court_service = PacerCourtProcessingService(...)
isolated_config = config.copy()
isolated_config_2 = self.config.copy()
isolated_config_3 = self.config.copy()
```

### 3. State Management Issues
- State spread across multiple services
- No single source of truth for court processing
- Browser contexts managed separately from processing state
- Download paths tracked independently

### 4. Resource Management Complexity
- Manual browser context lifecycle management
- Semaphore controls mixed with service creation
- Complex cleanup procedures

## Proposed Jobs Architecture

### Core Components

#### 1. PacerCourtJob Data Model
```python
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
import uuid
from datetime import datetime

@dataclass
class PacerCourtJob:
    """
    Represents a job to process PACER data for a single court.
    Encapsulates all state and configuration for court processing.
    """
    # Identity
    court_id: str
    court_name: str
    job_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    
    # Configuration (frozen at job creation)
    config_snapshot: Dict[str, Any]
    iso_date: str
    start_date: str
    end_date: str
    
    # Processing state
    status: str = "PENDING"  # PENDING, RUNNING, COMPLETED, ERROR
    error_message: Optional[str] = None
    
    # Browser/Download Management
    browser_context_id: Optional[str] = None
    download_path: Optional[str] = None
    
    # Processing mode
    processing_mode: str = "normal"  # normal, review_cases, reprocess_failed
    docket_numbers: Optional[List[str]] = None  # For specific docket processing
    
    # Results
    cases_found: int = 0
    cases_processed: int = 0
    cases_skipped: int = 0
    processed_cases: Optional[List[Dict[str, Any]]] = None
    
    # Metrics
    metrics: Dict[str, Any] = field(default_factory=lambda: {
        "start_time_utc": None,
        "end_time_utc": None,
        "duration_sec": None,
        "report_generation_time": None,
        "docket_processing_time": None,
        "browser_operations_time": None
    })
    
    # Timestamps
    created_at_utc: datetime = field(default_factory=datetime.utcnow)
    updated_at_utc: datetime = field(init=False)
    
    def __post_init__(self):
        self.updated_at_utc = self.created_at_utc
```

#### 2. PacerJobRunner Service
```python
class PacerJobRunner:
    """
    Stateless service responsible for executing a single PacerCourtJob.
    All state is contained within the job object.
    """
    def __init__(
        self,
        logger: LoggerProtocol,
        # Injected services (stateless)
        auth_service: PacerAuthenticationService,
        file_service: PacerFileManagementService,
        court_service: PacerCourtProcessingService,
        deepseek_service: Optional[DeepSeekService] = None,
    ):
        self.logger = logger
        self.auth_service = auth_service
        self.file_service = file_service
        self.court_service = court_service
        self.deepseek_service = deepseek_service
    
    async def run_job(
        self,
        job: PacerCourtJob,
        browser_service: BrowserService,
    ) -> PacerCourtJob:
        """Execute a single court processing job"""
        job.status = "RUNNING"
        job.start_timer()
        
        try:
            # Phase 1: Setup browser context with job-specific config
            context = await self._setup_browser_context(job, browser_service)
            
            # Phase 2: Generate report if needed
            if job.processing_mode in ["normal", "report_generation"]:
                await self._generate_court_report(job, context)
            
            # Phase 3: Process dockets
            await self._process_court_dockets(job, context)
            
            # Phase 4: Cleanup
            await context.close()
            
            job.status = "COMPLETED"
            job.end_timer()
            
        except Exception as e:
            job.set_error(str(e))
            self.logger.error(f"Job {job.job_id} failed: {e}")
        
        return job
```

#### 3. PacerJobOrchestrator Service
```python
class PacerJobOrchestrator:
    """
    Orchestrates the creation and concurrent execution of PacerCourtJob instances.
    Manages worker pool and job scheduling.
    """
    def __init__(
        self,
        config: Dict[str, Any],
        job_runner: PacerJobRunner,
        logger: LoggerProtocol,
    ):
        self.config = config
        self.job_runner = job_runner
        self.logger = logger
        
        # Worker pool configuration
        self.num_workers = min(
            config.get("num_workers", 10),
            16  # Max browser contexts
        )
        self._worker_semaphore = asyncio.Semaphore(self.num_workers)
    
    async def process_courts_as_jobs(
        self,
        court_ids: List[str],
        iso_date: str,
        start_date: str,
        end_date: str,
        processing_mode: str = "normal",
        docket_list: Optional[List[Dict]] = None,
    ) -> List[PacerCourtJob]:
        """Create and process jobs for multiple courts"""
        
        # Phase 1: Create jobs
        jobs = self._create_jobs(
            court_ids, iso_date, start_date, end_date, 
            processing_mode, docket_list
        )
        
        # Phase 2: Process jobs concurrently
        if self.config.get("run_parallel", False):
            return await self._process_jobs_parallel(jobs)
        else:
            return await self._process_jobs_sequential(jobs)
    
    def _create_jobs(self, ...) -> List[PacerCourtJob]:
        """Create job instances with frozen configuration"""
        jobs = []
        for court_id in court_ids:
            # Snapshot configuration at job creation time
            config_snapshot = {
                "headless": self.config.get("headless", True),
                "timeout_ms": self.config.get("timeout_ms", 60000),
                "download_wait_time": self.config.get("download_wait_time", 5),
                # ... other relevant config
            }
            
            job = PacerCourtJob(
                court_id=court_id,
                court_name=self._get_court_name(court_id),
                config_snapshot=config_snapshot,
                iso_date=iso_date,
                start_date=start_date,
                end_date=end_date,
                processing_mode=processing_mode,
            )
            
            # Set download path
            job.download_path = self._setup_download_path(court_id, iso_date)
            
            jobs.append(job)
        
        return jobs
```

## Migration Plan

### Phase 1: Foundation (Week 1)
1. **Create Job Models**
   - [ ] Create `src/services/pacer/jobs/` directory
   - [ ] Implement `PacerCourtJob` data model
   - [ ] Create job status enums and constants
   - [ ] Add job serialization/deserialization methods

2. **Create Job Runner**
   - [ ] Implement stateless `PacerJobRunner` service
   - [ ] Extract court processing logic from existing services
   - [ ] Ensure all state is read from job object
   - [ ] Add comprehensive error handling

3. **Create Job Orchestrator**
   - [ ] Implement `PacerJobOrchestrator` service
   - [ ] Add job creation logic
   - [ ] Implement parallel/sequential processing
   - [ ] Add worker pool management

### Phase 2: Service Refactoring (Week 2)
1. **Make Services Stateless**
   - [ ] Remove config storage from services
   - [ ] Pass config through method parameters
   - [ ] Remove service instance creation within services
   - [ ] Use dependency injection for all services

2. **Update Browser Management**
   - [ ] Tie browser contexts to jobs, not services
   - [ ] Implement browser pool management
   - [ ] Add automatic cleanup on job completion

3. **Refactor Download Path Management**
   - [ ] Move download path to job object
   - [ ] Remove download path from service state
   - [ ] Ensure all file operations use job's download path

### Phase 3: Integration (Week 3)
1. **Update Main Orchestrator**
   - [ ] Replace `PacerOrchestratorService.process_courts` with job-based approach
   - [ ] Update all calling code to use new interface
   - [ ] Maintain backward compatibility during transition

2. **Update Configuration Management**
   - [ ] Remove all `config.copy()` and `deepcopy` calls
   - [ ] Use job's config_snapshot for all operations
   - [ ] Validate configuration isolation

3. **Testing and Validation**
   - [ ] Create comprehensive unit tests for job components
   - [ ] Test parallel processing with multiple courts
   - [ ] Verify no cross-court contamination
   - [ ] Performance testing and optimization

### Phase 4: Cleanup (Week 4)
1. **Remove Legacy Code**
   - [ ] Remove old browser context isolation code
   - [ ] Clean up redundant service instances
   - [ ] Remove manual config copying

2. **Documentation**
   - [ ] Update architecture documentation
   - [ ] Create job architecture diagrams
   - [ ] Document new patterns and best practices

3. **Monitoring and Metrics**
   - [ ] Add job-level metrics and logging
   - [ ] Implement job status tracking
   - [ ] Create performance dashboards

## Benefits After Migration

### 1. Simplified Configuration Management
```python
# Before: Manual config copying everywhere
isolated_config = config.copy()
service = SomeService(config=isolated_config)

# After: Config frozen in job
job = PacerCourtJob(config_snapshot=config)
# Services are stateless, use job.config_snapshot
```

### 2. Clear State Management
```python
# Before: State scattered across services
self.download_path = ...
self.browser_context = ...
self.config = ...

# After: All state in job object
job.download_path
job.browser_context_id
job.config_snapshot
```

### 3. Better Testing
```python
# Easy to create test jobs
test_job = PacerCourtJob(
    court_id="test_court",
    config_snapshot={"headless": True},
    iso_date="20250117"
)

# Services are stateless, easy to mock
mock_runner = Mock(PacerJobRunner)
```

### 4. Improved Observability
- Job status tracking
- Per-job metrics
- Clear execution flow
- Better error attribution

## Risk Mitigation

### 1. Gradual Migration
- Implement new architecture alongside old
- Use feature flags to switch between implementations
- Migrate one court at a time if needed

### 2. Backward Compatibility
- Keep existing interfaces during transition
- Provide adapters for legacy code
- Gradual deprecation of old patterns

### 3. Testing Strategy
- Comprehensive unit tests for new components
- Integration tests with real courts
- Performance comparison with old implementation
- Parallel run validation

## Success Criteria

1. **No Configuration Contamination**: Zero instances of cross-court data leakage
2. **Performance Improvement**: 10-20% faster due to reduced overhead
3. **Code Reduction**: 30% fewer lines of code in orchestration logic
4. **Improved Testability**: 90%+ unit test coverage on job components
5. **Developer Satisfaction**: Easier to understand and modify

## Timeline

- **Week 1**: Foundation - Job models and basic runner
- **Week 2**: Service refactoring to stateless pattern
- **Week 3**: Integration and testing
- **Week 4**: Cleanup and documentation
- **Week 5**: Monitoring and optimization

Total estimated effort: 4-5 weeks for complete migration

## Conclusion

The jobs architecture will significantly simplify the PACER codebase by:
1. Eliminating complex configuration management
2. Providing clear state encapsulation
3. Enabling better testing and debugging
4. Improving code maintainability

The investment in this migration will pay dividends in reduced complexity, fewer bugs, and easier feature development.