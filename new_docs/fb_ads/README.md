# Facebook Ads Service Documentation

## Overview

The Facebook Ads service is a comprehensive system for scraping, processing, and analyzing Facebook advertising data. It provides automated workflows for collecting legal advertising data, processing images, categorizing content, and managing large-scale data operations.

## Architecture

### Core Components

#### 1. Orchestrator (`orchestrator.py`)
- **Purpose**: Main coordination service for Facebook Ads operations
- **Key Features**:
  - Workflow orchestration and management
  - Configuration loading and validation
  - Service initialization and dependency injection
  - Error handling and recovery mechanisms
  - Progress tracking and reporting

#### 2. API Client (`api_client.py`)
- **Purpose**: Interface with Facebook's Ad Library API
- **Key Features**:
  - GraphQL query execution
  - Rate limiting and throttling
  - Response parsing and validation
  - Error handling and retry logic
  - Session management integration

#### 3. Session Manager (`session_manager.py`)
- **Purpose**: Manages HTTP sessions and browser automation
- **Key Features**:
  - Session lifecycle management
  - SSL/TLS configuration
  - Proxy rotation and management
  - Cookie and header management
  - Connection pooling

#### 4. Workflow Services

##### WorkflowService (`workflow_service.py`)
- **Purpose**: Core workflow execution engine
- **Key Features**:
  - Full scrape workflow management
  - Ignore list processing
  - Date range handling
  - Firm processing coordination
  - Job-based architecture integration

##### ConcurrentWorkflowService (`concurrent_workflow_service.py`)
- **Purpose**: Wrapper for concurrent processing (legacy compatibility)
- **Key Features**:
  - Backward compatibility with concurrent processing flags
  - Delegates to job-based architecture
  - Statistics tracking for legacy systems

#### 5. Processing Services

##### AdProcessor (`processor.py`)
- **Purpose**: Core ad processing logic
- **Key Features**:
  - Individual ad data processing
  - Content extraction and normalization
  - Image processing coordination
  - Data validation and cleanup

##### AdProcessingService (`ad_processing_service.py`)
- **Purpose**: High-level ad processing coordination
- **Key Features**:
  - Batch processing management
  - Error handling and recovery
  - Progress tracking
  - Result aggregation

#### 6. Job System

##### JobOrchestrationService (`jobs/job_orchestration_service.py`)
- **Purpose**: Modern job-based processing architecture
- **Key Features**:
  - Concurrent job execution
  - Job queue management
  - Resource allocation
  - Progress monitoring
  - Error recovery

##### Job Models (`jobs/job_models.py`)
- **Purpose**: Data models for job system
- **Key Features**:
  - Job state management
  - Configuration snapshots
  - Result tracking
  - Dependency management

#### 7. Content Analysis

##### Categorizer (`categorizer.py`)
- **Purpose**: AI-powered content categorization
- **Key Features**:
  - Legal content classification
  - NER (Named Entity Recognition)
  - Rule-based analysis
  - Confidence scoring

##### Classifier (`classifier.py`)
- **Purpose**: Legal ad analysis and classification
- **Key Features**:
  - Legal relevance detection
  - Content type classification
  - Confidence metrics
  - Batch processing support

#### 8. Image Processing

##### ImageHandler (`image_handler.py`)
- **Purpose**: Image download and processing
- **Key Features**:
  - Asynchronous image downloading
  - Hash-based deduplication
  - Format conversion and optimization
  - Error handling and retry logic

##### ImageUtils (`image_utils.py`)
- **Purpose**: Image utility functions
- **Key Features**:
  - Hash calculation
  - Image processing utilities
  - Metadata extraction
  - File management

#### 9. Data Management

##### AdDbService (`ad_db_service.py`)
- **Purpose**: Database operations for ad data
- **Key Features**:
  - CRUD operations
  - Batch processing
  - Data validation
  - Query optimization

##### DataValidationService (`data_validation_service.py`)
- **Purpose**: Data quality assurance
- **Key Features**:
  - Schema validation
  - Data integrity checks
  - Error reporting
  - Cleanup operations

#### 10. Infrastructure Services

##### ErrorHandlingService (`error_handling_service.py`)
- **Purpose**: Centralized error management
- **Key Features**:
  - Error categorization
  - Retry logic
  - Logging and reporting
  - Recovery strategies

##### ProcessingTracker (`processing_tracker.py`)
- **Purpose**: Progress and status tracking
- **Key Features**:
  - Real-time progress updates
  - Performance metrics
  - Status reporting
  - Historical tracking

##### BandwidthLogger (`bandwidth_logger.py`)
- **Purpose**: Network usage monitoring
- **Key Features**:
  - Request/response size tracking
  - Bandwidth utilization metrics
  - Performance analysis
  - Rate limiting support

## Factory Pattern Implementation

### API Client Factory (`factories/api_client_factory.py`)
- Creates appropriate API client instances based on configuration
- Supports multiple client types (standard, Camoufox)
- Handles dependency injection

### Session Manager Factory (`factories/session_manager_factory.py`)
- Creates session manager instances
- Configures session parameters
- Manages session lifecycle

## Camoufox Integration

### CamoufoxApiClient (`camoufox/camoufox_api_client.py`)
- Specialized API client for Camoufox browser automation
- Enhanced fingerprinting resistance
- Advanced proxy support

### CamoufoxSessionManager (`camoufox/camoufox_session_manager.py`)
- Session management for Camoufox integration
- Browser automation coordination
- Enhanced privacy features

## Configuration

The service uses a hierarchical configuration system:

```yaml
fb_ads:
  api:
    base_url: "https://graph.facebook.com"
    version: "v18.0"
    rate_limit: 200
  
  processing:
    batch_size: 50
    max_concurrent_firms: 3
    enable_concurrent_processing: true
  
  image_processing:
    download_images: true
    max_image_size: 10485760
    supported_formats: ["jpg", "png", "webp"]
  
  categorization:
    enable_ner: true
    confidence_threshold: 0.7
    use_hybrid_classifier: true
```

## Usage Examples

### Basic Workflow Execution

```python
from src.services.fb_ads import FacebookAdsOrchestrator

# Initialize orchestrator
orchestrator = FacebookAdsOrchestrator(config)

# Run full scrape workflow
success = await orchestrator.run_full_scrape_workflow()

# Process specific firms
firms = ["firm1", "firm2"]
results = await orchestrator.process_firms(firms)
```

### Job-Based Processing

```python
from src.services.fb_ads.jobs import JobOrchestrationService

# Create job orchestration service
job_service = JobOrchestrationService(config)

# Submit jobs
jobs = await job_service.create_firm_jobs(firms)
results = await job_service.execute_jobs(jobs)
```

## Error Handling

The service implements comprehensive error handling:

1. **Retry Logic**: Automatic retries for transient failures
2. **Circuit Breakers**: Prevent cascading failures
3. **Graceful Degradation**: Continue processing when possible
4. **Error Categorization**: Different handling for different error types
5. **Recovery Strategies**: Automatic recovery from common issues

## Performance Optimization

1. **Concurrent Processing**: Parallel execution of independent tasks
2. **Connection Pooling**: Efficient HTTP connection management
3. **Caching**: Multiple levels of caching for improved performance
4. **Batch Processing**: Efficient handling of large datasets
5. **Resource Management**: Proper cleanup and resource allocation

## Monitoring and Logging

1. **Structured Logging**: Consistent log format across all components
2. **Performance Metrics**: Detailed performance tracking
3. **Progress Reporting**: Real-time progress updates
4. **Error Tracking**: Comprehensive error logging and analysis
5. **Bandwidth Monitoring**: Network usage tracking

## Testing

The service includes comprehensive test coverage:

1. **Unit Tests**: Individual component testing
2. **Integration Tests**: Service interaction testing
3. **End-to-End Tests**: Complete workflow testing
4. **Performance Tests**: Load and stress testing
5. **Mock Services**: Isolated testing capabilities

## Migration Notes

The service has undergone significant architectural improvements:

1. **Job-Based Architecture**: Migration from direct processing to job-based system
2. **Dependency Injection**: Improved service composition
3. **Factory Pattern**: Better service instantiation
4. **Enhanced Error Handling**: More robust error management
5. **Performance Improvements**: Better resource utilization