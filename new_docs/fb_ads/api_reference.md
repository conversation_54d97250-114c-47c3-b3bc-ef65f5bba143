# Facebook Ads Service API Reference

## Core Services

### FacebookAdsOrchestrator

Main orchestration service for Facebook Ads operations.

```python
class FacebookAdsOrchestrator:
    def __init__(self, config: dict, logger: logging.Logger = None)
```

#### Methods

##### `async run_full_scrape_workflow() -> bool`
Executes the complete Facebook Ads scraping workflow.

**Returns:**
- `bool`: True if workflow completed successfully

**Example:**
```python
orchestrator = FacebookAdsOrchestrator(config)
success = await orchestrator.run_full_scrape_workflow()
```

##### `async run_ignore_list_workflow() -> bool`
Processes firms on the ignore list with specific handling.

**Returns:**
- `bool`: True if ignore list processing completed successfully

##### `async process_firms(firms: List[str]) -> Dict[str, Any]`
Processes specific firms.

**Parameters:**
- `firms`: List of firm names to process

**Returns:**
- `Dict[str, Any]`: Processing results for each firm

### FacebookAPIClient

Interface for Facebook Ad Library API interactions.

```python
class FacebookAPIClient:
    def __init__(self, config: dict, session_manager: FacebookSessionManager)
```

#### Methods

##### `async execute_graphql_query(query: str, variables: dict = None) -> dict`
Executes a GraphQL query against Facebook's API.

**Parameters:**
- `query`: GraphQL query string
- `variables`: Query variables (optional)

**Returns:**
- `dict`: API response data

**Raises:**
- `FacebookAPIError`: API request failed
- `RateLimitError`: Rate limit exceeded

##### `async get_ads_for_firm(firm_name: str, date_range: tuple = None) -> List[dict]`
Retrieves ads for a specific firm.

**Parameters:**
- `firm_name`: Name of the law firm
- `date_range`: Tuple of (start_date, end_date) (optional)

**Returns:**
- `List[dict]`: List of ad data

### AdProcessor

Core ad processing and data extraction service.

```python
class AdProcessor:
    def __init__(self, config: dict, logger: logging.Logger)
```

#### Methods

##### `async process_ad(ad_data: dict) -> dict`
Processes individual ad data.

**Parameters:**
- `ad_data`: Raw ad data from Facebook API

**Returns:**
- `dict`: Processed and normalized ad data

##### `async extract_legal_content(ad_text: str) -> dict`
Extracts legal-specific content from ad text.

**Parameters:**
- `ad_text`: Ad text content

**Returns:**
- `dict`: Extracted legal content with confidence scores

### FBAdCategorizer

AI-powered content categorization service.

```python
class FBAdCategorizer:
    def __init__(self, config: dict, logger: logging.Logger)
```

#### Methods

##### `async categorize_ad(ad_data: dict) -> dict`
Categorizes ad content using AI analysis.

**Parameters:**
- `ad_data`: Ad data to categorize

**Returns:**
- `dict`: Categorization results with confidence scores

##### `async extract_entities(text: str) -> List[dict]`
Extracts named entities from text.

**Parameters:**
- `text`: Text to analyze

**Returns:**
- `List[dict]`: List of extracted entities

### LegalAdAnalyzer

Legal content analysis and classification.

```python
class LegalAdAnalyzer:
    def __init__(self, config: dict)
```

#### Methods

##### `async analyze_legal_relevance(ad_data: dict) -> dict`
Analyzes legal relevance of ad content.

**Parameters:**
- `ad_data`: Ad data to analyze

**Returns:**
- `dict`: Legal relevance analysis with confidence score

##### `async classify_legal_type(ad_text: str) -> str`
Classifies the type of legal content.

**Parameters:**
- `ad_text`: Ad text to classify

**Returns:**
- `str`: Legal content type classification

### ImageHandler

Image processing and management service.

```python
class ImageHandler:
    def __init__(self, config: dict, logger: logging.Logger)
```

#### Methods

##### `async download_image(image_url: str, ad_id: str) -> str`
Downloads and processes an image.

**Parameters:**
- `image_url`: URL of the image to download
- `ad_id`: Associated ad ID

**Returns:**
- `str`: Local file path of downloaded image

##### `async process_image_batch(image_urls: List[str], ad_ids: List[str]) -> List[str]`
Processes multiple images in batch.

**Parameters:**
- `image_urls`: List of image URLs
- `ad_ids`: List of associated ad IDs

**Returns:**
- `List[str]`: List of local file paths

### JobOrchestrationService

Job-based processing orchestration.

```python
class JobOrchestrationService:
    def __init__(self, config: dict, logger: logging.Logger)
```

#### Methods

##### `async create_firm_jobs(firms: List[str]) -> List[ProcessFirmJob]`
Creates processing jobs for firms.

**Parameters:**
- `firms`: List of firm names

**Returns:**
- `List[ProcessFirmJob]`: Created job instances

##### `async execute_jobs(jobs: List[ProcessFirmJob]) -> List[JobResult]`
Executes jobs concurrently.

**Parameters:**
- `jobs`: List of jobs to execute

**Returns:**
- `List[JobResult]`: Execution results

## Data Models

### ProcessFirmJob

Represents a firm processing job.

```python
@dataclass
class ProcessFirmJob:
    firm_name: str
    job_id: str
    config_snapshot: dict
    current_process_date: str
    status: JobStatus = JobStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
```

### JobResult

Represents job execution result.

```python
@dataclass
class JobResult:
    job_id: str
    firm_name: str
    status: JobStatus
    ads_processed: int = 0
    images_downloaded: int = 0
    errors: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    metadata: dict = field(default_factory=dict)
```

### AdData

Represents processed ad data.

```python
@dataclass
class AdData:
    ad_id: str
    firm_name: str
    ad_text: str
    image_urls: List[str]
    created_date: datetime
    legal_category: Optional[str] = None
    confidence_score: Optional[float] = None
    entities: List[dict] = field(default_factory=list)
    metadata: dict = field(default_factory=dict)
```

## Configuration Schema

### Main Configuration

```yaml
fb_ads:
  # API Configuration
  api:
    base_url: "https://graph.facebook.com"
    version: "v18.0"
    access_token: "${FB_ACCESS_TOKEN}"
    rate_limit: 200
    timeout: 30
    
  # Processing Configuration
  processing:
    batch_size: 50
    max_concurrent_firms: 3
    enable_concurrent_processing: true
    retry_attempts: 3
    retry_delay: 5
    
  # Image Processing
  image_processing:
    download_images: true
    max_image_size: 10485760  # 10MB
    supported_formats: ["jpg", "png", "webp"]
    storage_path: "data/images"
    
  # Categorization
  categorization:
    enable_ner: true
    confidence_threshold: 0.7
    use_hybrid_classifier: true
    model_path: "models/legal_classifier"
    
  # Job System
  jobs:
    max_concurrent_jobs: 5
    job_timeout: 3600  # 1 hour
    cleanup_completed_jobs: true
    job_retention_days: 7
```

## Error Handling

### Exception Hierarchy

```python
class FacebookAdsError(Exception):
    """Base exception for Facebook Ads service"""
    pass

class FacebookAPIError(FacebookAdsError):
    """Facebook API related errors"""
    pass

class RateLimitError(FacebookAPIError):
    """Rate limit exceeded"""
    pass

class AuthenticationError(FacebookAPIError):
    """Authentication failed"""
    pass

class ProcessingError(FacebookAdsError):
    """Ad processing errors"""
    pass

class ImageProcessingError(ProcessingError):
    """Image processing errors"""
    pass

class CategorizationError(ProcessingError):
    """Categorization errors"""
    pass

class JobExecutionError(FacebookAdsError):
    """Job execution errors"""
    pass
```

### Error Response Format

```python
{
    "error": {
        "type": "RateLimitError",
        "message": "Rate limit exceeded",
        "code": "RATE_LIMIT_EXCEEDED",
        "details": {
            "retry_after": 300,
            "current_limit": 200,
            "reset_time": "2024-01-01T12:00:00Z"
        }
    }
}
```

## Event System

### Event Types

```python
class EventType(Enum):
    JOB_STARTED = "job_started"
    JOB_COMPLETED = "job_completed"
    JOB_FAILED = "job_failed"
    AD_PROCESSED = "ad_processed"
    IMAGE_DOWNLOADED = "image_downloaded"
    RATE_LIMIT_HIT = "rate_limit_hit"
    ERROR_OCCURRED = "error_occurred"
```

### Event Subscription

```python
from src.services.fb_ads.events import EventBus

event_bus = EventBus()

@event_bus.subscribe(EventType.JOB_COMPLETED)
async def handle_job_completion(event_data):
    job_result = event_data['job_result']
    print(f"Job {job_result.job_id} completed successfully")

@event_bus.subscribe(EventType.ERROR_OCCURRED)
async def handle_error(event_data):
    error = event_data['error']
    logger.error(f"Error occurred: {error}")
```

## Metrics and Monitoring

### Available Metrics

```python
class Metrics:
    # Processing Metrics
    ads_processed_total: Counter
    ads_processed_per_second: Gauge
    processing_duration: Histogram
    
    # API Metrics
    api_requests_total: Counter
    api_request_duration: Histogram
    api_errors_total: Counter
    rate_limit_hits: Counter
    
    # Job Metrics
    jobs_created: Counter
    jobs_completed: Counter
    jobs_failed: Counter
    job_execution_time: Histogram
    
    # Image Metrics
    images_downloaded: Counter
    image_download_errors: Counter
    image_processing_time: Histogram
```

### Health Check Endpoints

```python
# Health check response format
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "checks": {
        "facebook_api": {
            "status": "healthy",
            "response_time": 150,
            "last_check": "2024-01-01T11:59:30Z"
        },
        "database": {
            "status": "healthy",
            "connection_pool": {
                "active": 5,
                "idle": 10,
                "max": 20
            }
        },
        "image_storage": {
            "status": "healthy",
            "disk_usage": "45%",
            "available_space": "500GB"
        }
    }
}
```

## Usage Examples

### Basic Workflow

```python
import asyncio
from src.services.fb_ads import FacebookAdsOrchestrator

async def main():
    config = load_config()
    orchestrator = FacebookAdsOrchestrator(config)
    
    # Run full workflow
    success = await orchestrator.run_full_scrape_workflow()
    
    if success:
        print("Workflow completed successfully")
    else:
        print("Workflow failed")

asyncio.run(main())
```

### Custom Processing

```python
from src.services.fb_ads import (
    FacebookAPIClient, 
    AdProcessor, 
    FBAdCategorizer
)

async def process_specific_firm(firm_name: str):
    # Initialize services
    api_client = FacebookAPIClient(config)
    processor = AdProcessor(config)
    categorizer = FBAdCategorizer(config)
    
    # Get ads for firm
    ads = await api_client.get_ads_for_firm(firm_name)
    
    # Process each ad
    results = []
    for ad in ads:
        # Process ad data
        processed_ad = await processor.process_ad(ad)
        
        # Categorize content
        categorization = await categorizer.categorize_ad(processed_ad)
        processed_ad.update(categorization)
        
        results.append(processed_ad)
    
    return results
```

### Job-Based Processing

```python
from src.services.fb_ads.jobs import JobOrchestrationService

async def process_firms_with_jobs(firms: List[str]):
    job_service = JobOrchestrationService(config)
    
    # Create jobs
    jobs = await job_service.create_firm_jobs(firms)
    
    # Execute jobs concurrently
    results = await job_service.execute_jobs(jobs)
    
    # Process results
    for result in results:
        if result.status == JobStatus.COMPLETED:
            print(f"Firm {result.firm_name}: {result.ads_processed} ads processed")
        else:
            print(f"Firm {result.firm_name} failed: {result.errors}")
    
    return results
```