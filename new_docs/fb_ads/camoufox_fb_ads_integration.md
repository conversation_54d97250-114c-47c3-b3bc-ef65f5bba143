**OBJECTIVE**
I am trying to integrate @src/services/scraping/ camoufox into @src/services/fb_ads/                                                                                                  │
The @test_complete_flow_with_graphql.py accurately is able to navigate to the ads page and parse the NDJSON.                                                                          │

When I run @auto_heal_pipeline.py --config fb_ads.yml for Morgan & Morgan & Wisner Baum, I am able to successfully navigate
to the ads page and parse the NDJSON and verified ad page.

You need to debug the code until you are able to run both Morgan & Morgan and Wisner Baum, extract and upload all information to
dynamodb successfully.

**REQUIREMENTS**
The legacy version use the page_id and navigates to the url.
The camoufox version needs to find the following keys from law_firms table.
- page_name
-  id

The prior version of @src/servicesw/fb_ads navigated using the direct url with the page_id embedded. 
Camoufox needs to use the exact page_name from law_firms table.

You need to use the job architecture for parallel ads to prevent manage state and prevent cross contamination

1. Make sure @src/containers.fb_ads.py is properly injected required dependencies.
2. Get law_firm page_id and page_name.
 - Navigate using the EXACT SAME process as @test_complete_flow_with_graphql.py.
 - Once the NDJSON is properly extracted the following steps should happen:
   - Use @src/services/fb_ads/processor.py to iterate through the list and _create_ad_dictionary and for each item:
   - Check if ad image has skip_terms and if so, continue to the next item
   - If summary key already exists and is not NA, null or none, update end_date with the greater of end_date in the ad dictionary or today's date.
   - Check if the ad image for the ad_archive_id/ad_creative_id already exists in S3.
     - If does not exist, download image, calculate PHash and search FBImageHash database and determine if an image_text key exists
       based on PHash only. If it does, add it to the ad_dictionary.\
     - If it does not, download the image and save it to the local_image_queue for procesing when defer_image_processing: true ELSE proces image using llava_vision.
   - Generate ad summary using @src/services/ai/deepseek_service.py.
   - When all images are processed upload to dynamodb FBAdArchive.

Review @src/services/orchestration/fb_ads_orchestrator.py

**NOTES**
- Test with Morgan & Morgan & Wisner Baum
- These both have valid ads that should be parseable.
- They both work headless or with a browser.
- camoufox is able to handle concurrency
