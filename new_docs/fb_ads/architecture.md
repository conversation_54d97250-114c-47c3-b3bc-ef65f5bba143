# Facebook Ads Service Architecture

## System Overview

The Facebook Ads service follows a modular, job-based architecture designed for scalability, reliability, and maintainability. The system processes Facebook advertising data through multiple stages: data collection, processing, analysis, and storage.

## Architectural Patterns

### 1. Job-Based Processing Architecture

The system has migrated from direct processing to a job-based architecture for better scalability and error handling:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Orchestrator  │───▶│ Job Orchestration│───▶│   Job Runners   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Configuration  │    │   Job Queue      │    │  Result Handler │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 2. Factory Pattern Implementation

Services are created through factories for better dependency management:

```python
# API Client Factory
api_client = api_client_factory.create_client(client_type, config)

# Session Manager Factory  
session_manager = session_manager_factory.create_manager(manager_type, config)
```

### 3. Dependency Injection

Services use constructor-based dependency injection:

```python
class WorkflowService:
    def __init__(
        self,
        logger: logging.Logger,
        config: dict,
        ad_db_service: AdDbService,
        error_handling_service: ErrorHandlingService,
        session_manager: SessionManager,
        job_orchestration_service: JobOrchestrationService,
        data_validation_service: DataValidationService,
        ad_processing_service: AdProcessingService,
    ):
        # Initialize with injected dependencies
```

## Core Service Layers

### 1. Orchestration Layer

**FacebookAdsOrchestrator**
- Entry point for all FB Ads operations
- Coordinates workflow execution
- Manages service lifecycle
- Handles configuration and initialization

**WorkflowService**
- Executes specific workflows (full scrape, ignore list)
- Manages workflow state and progress
- Coordinates with job orchestration service

**ConcurrentWorkflowService**
- Legacy wrapper for backward compatibility
- Delegates to job-based architecture
- Maintains existing API contracts

### 2. Job Management Layer

**JobOrchestrationService**
- Creates and manages processing jobs
- Handles concurrent job execution
- Provides job status tracking and results
- Implements resource allocation and throttling

**Job Models**
- `ProcessFirmJob`: Represents firm processing tasks
- Job state management and configuration snapshots
- Result tracking and dependency management

### 3. Data Processing Layer

**AdProcessor**
- Core ad processing logic
- Content extraction and normalization
- Image processing coordination
- Data validation and cleanup

**AdProcessingService**
- High-level processing coordination
- Batch processing management
- Error handling and recovery
- Progress tracking and result aggregation

### 4. Content Analysis Layer

**Categorizer (FBAdCategorizer)**
- AI-powered content categorization
- Named Entity Recognition (NER)
- Rule-based analysis with confidence scoring
- Legal content classification

**Classifier (LegalAdAnalyzer)**
- Legal relevance detection
- Content type classification
- Confidence metrics and batch processing

### 5. Data Access Layer

**AdDbService**
- Database CRUD operations
- Batch processing capabilities
- Data validation and query optimization
- Transaction management

**DataValidationService**
- Schema validation and data integrity checks
- Error reporting and cleanup operations
- Data quality assurance

### 6. Infrastructure Layer

**API Client**
- Facebook Ad Library API interface
- GraphQL query execution
- Rate limiting and throttling
- Response parsing and validation

**Session Manager**
- HTTP session lifecycle management
- SSL/TLS configuration
- Proxy rotation and management
- Connection pooling

**Image Handler**
- Asynchronous image downloading
- Hash-based deduplication
- Format conversion and optimization
- Error handling and retry logic

## Data Flow Architecture

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   FB API    │───▶│ API Client  │───▶│ Processor   │───▶│  Database   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                           │                   │
                           ▼                   ▼
                   ┌─────────────┐    ┌─────────────┐
                   │Session Mgr  │    │Categorizer  │
                   └─────────────┘    └─────────────┘
                           │                   │
                           ▼                   ▼
                   ┌─────────────┐    ┌─────────────┐
                   │Proxy Manager│    │Image Handler│
                   └─────────────┘    └─────────────┘
```

## Error Handling Architecture

### 1. Hierarchical Error Handling

```
┌─────────────────┐
│  Orchestrator   │ ◄── Global error handling
└─────────────────┘
         │
┌─────────────────┐
│ Workflow Service│ ◄── Workflow-level error handling
└─────────────────┘
         │
┌─────────────────┐
│ Job Orchestration│ ◄── Job-level error handling
└─────────────────┘
         │
┌─────────────────┐
│ Individual Jobs │ ◄── Task-level error handling
└─────────────────┘
```

### 2. Error Categories

- **Transient Errors**: Network timeouts, rate limits
- **Permanent Errors**: Invalid credentials, malformed data
- **Resource Errors**: Memory exhaustion, disk space
- **Business Logic Errors**: Invalid firm data, missing configurations

### 3. Recovery Strategies

- **Retry with Backoff**: Exponential backoff for transient errors
- **Circuit Breaker**: Prevent cascading failures
- **Graceful Degradation**: Continue processing when possible
- **Dead Letter Queue**: Handle permanently failed jobs

## Concurrency Architecture

### 1. Job-Level Concurrency

```python
# Concurrent job execution
async def execute_jobs_concurrently(jobs: List[ProcessFirmJob]) -> List[JobResult]:
    semaphore = asyncio.Semaphore(max_concurrent_jobs)
    tasks = [process_job_with_semaphore(job, semaphore) for job in jobs]
    return await asyncio.gather(*tasks, return_exceptions=True)
```

### 2. Resource Management

- **Connection Pooling**: Shared HTTP connections
- **Session Pooling**: Reusable browser sessions
- **Memory Management**: Proper cleanup and garbage collection
- **Rate Limiting**: Respect API limits across concurrent operations

## Configuration Architecture

### 1. Hierarchical Configuration

```yaml
fb_ads:
  global:
    # Global settings
  api:
    # API-specific settings
  processing:
    # Processing configuration
  image_processing:
    # Image handling settings
  categorization:
    # Content analysis settings
```

### 2. Environment-Specific Overrides

- Development, staging, and production configurations
- Feature flags for experimental features
- Runtime configuration updates

## Monitoring and Observability

### 1. Logging Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Structured Logs │───▶│  Log Aggregator │───▶│   Log Storage   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Correlation   │    │   Log Analysis  │    │    Alerting     │
│      IDs        │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. Metrics Collection

- **Performance Metrics**: Processing times, throughput
- **Business Metrics**: Success rates, data quality
- **Infrastructure Metrics**: Resource utilization, error rates
- **Custom Metrics**: Domain-specific measurements

### 3. Health Checks

- Service health endpoints
- Dependency health monitoring
- Automated recovery triggers
- Status dashboards

## Security Architecture

### 1. Authentication & Authorization

- API key management
- Service-to-service authentication
- Role-based access control
- Audit logging

### 2. Data Protection

- Encryption in transit and at rest
- PII data handling
- Data retention policies
- Secure configuration management

## Scalability Considerations

### 1. Horizontal Scaling

- Stateless service design
- Load balancing across instances
- Database connection pooling
- Distributed job processing

### 2. Vertical Scaling

- Resource-aware processing
- Memory optimization
- CPU utilization monitoring
- Adaptive batch sizing

### 3. Auto-scaling

- Metrics-based scaling triggers
- Predictive scaling for known patterns
- Resource reservation and limits
- Cost optimization strategies