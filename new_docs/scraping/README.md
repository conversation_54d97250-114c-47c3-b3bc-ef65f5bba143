# Scraping Service Documentation

## Overview

The Scraping service provides core infrastructure for web scraping operations, including browser automation, proxy management, and fingerprinting resistance. It serves as the foundation for various scraping workflows across the application.

## Architecture

### Core Components

#### 1. Browser Management (`browser/`)

##### FingerprintManager (`browser/fingerprint_manager.py`)
- **Purpose**: Manages browser fingerprinting and detection evasion
- **Key Features**:
  - User agent rotation and management
  - Browser fingerprint randomization
  - Detection evasion techniques
  - Profile management for different scraping scenarios

**Key Methods:**
```python
class FingerprintManager:
    def get_random_user_agent(self, browser_type: str = "chrome") -> str
    def get_browser_fingerprint(self, profile_type: str = "default") -> dict
    def rotate_fingerprint(self) -> dict
    def validate_fingerprint(self, fingerprint: dict) -> bool
```

**Supported Browser Types:**
- Chrome (various versions)
- Firefox (various versions)
- Safari (macOS/iOS)
- Edge (Chromium-based)

**Fingerprint Components:**
- User Agent strings
- Screen resolution and color depth
- Timezone and language settings
- Plugin and extension profiles
- WebGL and Canvas fingerprints

#### 2. Proxy Management (`proxy/`)

##### ProxyManager (`proxy/proxy_manager.py`)
- **Purpose**: Manages proxy rotation and connection handling
- **Key Features**:
  - Proxy pool management
  - Health checking and validation
  - Automatic rotation and failover
  - Geographic distribution support
  - Performance monitoring

**Key Methods:**
```python
class ProxyManager:
    def get_proxy(self, region: str = None) -> dict
    def validate_proxy(self, proxy: dict) -> bool
    def rotate_proxy(self) -> dict
    def mark_proxy_failed(self, proxy: dict) -> None
    def get_proxy_stats(self) -> dict
```

**Proxy Types Supported:**
- HTTP/HTTPS proxies
- SOCKS4/SOCKS5 proxies
- Residential proxies
- Datacenter proxies
- Mobile proxies

**Features:**
- Automatic health checking
- Geographic IP rotation
- Speed and reliability metrics
- Blacklist management
- Connection pooling

## Configuration

### Browser Configuration

```yaml
scraping:
  browser:
    fingerprint_manager:
      enabled: true
      rotation_interval: 300  # seconds
      profile_types:
        - "stealth"
        - "mobile"
        - "desktop"
      user_agents:
        chrome:
          - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
          - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36..."
        firefox:
          - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101..."
      
      fingerprint_options:
        randomize_screen_resolution: true
        randomize_timezone: true
        randomize_language: false
        webgl_noise: true
        canvas_noise: true
```

### Proxy Configuration

```yaml
scraping:
  proxy:
    proxy_manager:
      enabled: true
      rotation_strategy: "round_robin"  # round_robin, random, performance_based
      health_check_interval: 60  # seconds
      max_failures: 3
      timeout: 30  # seconds
      
      proxy_pools:
        residential:
          endpoints:
            - "http://proxy1.example.com:8080"
            - "http://proxy2.example.com:8080"
          auth:
            username: "user"
            password: "pass"
          regions: ["US", "EU", "APAC"]
        
        datacenter:
          endpoints:
            - "http://dc-proxy1.example.com:3128"
          auth:
            type: "ip_whitelist"
          regions: ["US"]
```

## Usage Examples

### Basic Fingerprint Management

```python
from src.services.scraping.browser import FingerprintManager

# Initialize fingerprint manager
fingerprint_manager = FingerprintManager(config)

# Get random user agent
user_agent = fingerprint_manager.get_random_user_agent("chrome")

# Get complete browser fingerprint
fingerprint = fingerprint_manager.get_browser_fingerprint("stealth")

# Use with requests session
import requests
session = requests.Session()
session.headers.update({
    'User-Agent': fingerprint['user_agent'],
    'Accept-Language': fingerprint['language'],
    'Accept-Encoding': fingerprint['encoding']
})
```

### Proxy Management

```python
from src.services.scraping.proxy import ProxyManager

# Initialize proxy manager
proxy_manager = ProxyManager(config)

# Get a proxy for specific region
proxy = proxy_manager.get_proxy(region="US")

# Use with requests
proxies = {
    'http': f"http://{proxy['username']}:{proxy['password']}@{proxy['host']}:{proxy['port']}",
    'https': f"http://{proxy['username']}:{proxy['password']}@{proxy['host']}:{proxy['port']}"
}

response = requests.get(url, proxies=proxies)

# Mark proxy as failed if needed
if response.status_code != 200:
    proxy_manager.mark_proxy_failed(proxy)
```

### Combined Usage

```python
from src.services.scraping import FingerprintManager, ProxyManager

class ScrapingSession:
    def __init__(self, config):
        self.fingerprint_manager = FingerprintManager(config)
        self.proxy_manager = ProxyManager(config)
        self.session = requests.Session()
        
    def setup_session(self, region=None, profile_type="default"):
        # Setup fingerprint
        fingerprint = self.fingerprint_manager.get_browser_fingerprint(profile_type)
        self.session.headers.update(fingerprint['headers'])
        
        # Setup proxy
        proxy = self.proxy_manager.get_proxy(region)
        self.session.proxies.update({
            'http': f"http://{proxy['auth']}@{proxy['endpoint']}",
            'https': f"http://{proxy['auth']}@{proxy['endpoint']}"
        })
        
    def make_request(self, url, **kwargs):
        try:
            response = self.session.get(url, **kwargs)
            return response
        except Exception as e:
            # Handle proxy failures
            self.proxy_manager.mark_proxy_failed(self.current_proxy)
            # Rotate to new proxy
            self.setup_session()
            raise
```

## Integration with Other Services

### Facebook Ads Integration

The scraping service integrates with the Facebook Ads service for:

- **Session Management**: Provides fingerprint and proxy support for FB API sessions
- **Rate Limiting**: Helps distribute requests across different IP addresses
- **Detection Evasion**: Reduces likelihood of being blocked by Facebook

```python
# In FacebookSessionManager
from src.services.scraping import FingerprintManager, ProxyManager

class FacebookSessionManager:
    def __init__(self, config):
        self.fingerprint_manager = FingerprintManager(config.get('scraping', {}))
        self.proxy_manager = ProxyManager(config.get('scraping', {}))
        
    def create_session(self):
        session = requests.Session()
        
        # Apply fingerprint
        fingerprint = self.fingerprint_manager.get_browser_fingerprint("facebook")
        session.headers.update(fingerprint['headers'])
        
        # Apply proxy
        proxy = self.proxy_manager.get_proxy(region="US")
        session.proxies.update(proxy['config'])
        
        return session
```

### PACER Integration

For PACER scraping operations:

- **Court-Specific Fingerprints**: Different profiles for different court systems
- **Geographic Proxies**: Use proxies in appropriate jurisdictions
- **Session Persistence**: Maintain sessions across multiple requests

## Error Handling

### Proxy Failures

```python
class ProxyError(Exception):
    """Raised when proxy operations fail"""
    pass

class ProxyManager:
    def handle_proxy_failure(self, proxy, error):
        """Handle proxy failure and rotation"""
        self.mark_proxy_failed(proxy)
        self.logger.warning(f"Proxy {proxy['host']} failed: {error}")
        
        # Try to get alternative proxy
        try:
            new_proxy = self.get_proxy(exclude=[proxy])
            return new_proxy
        except ProxyError:
            self.logger.error("No alternative proxies available")
            raise
```

### Fingerprint Detection

```python
class FingerprintDetectedError(Exception):
    """Raised when fingerprint is detected as suspicious"""
    pass

class FingerprintManager:
    def handle_detection(self, response):
        """Handle potential fingerprint detection"""
        if self.is_detection_response(response):
            # Rotate fingerprint
            new_fingerprint = self.rotate_fingerprint()
            self.logger.warning("Fingerprint detected, rotating to new profile")
            return new_fingerprint
        return None
```

## Performance Optimization

### Connection Pooling

```python
class OptimizedScrapingSession:
    def __init__(self, config):
        self.session_pool = {}
        self.max_pool_size = config.get('max_sessions', 10)
        
    def get_session(self, fingerprint_type, region):
        key = f"{fingerprint_type}_{region}"
        
        if key not in self.session_pool:
            session = self.create_optimized_session(fingerprint_type, region)
            self.session_pool[key] = session
            
        return self.session_pool[key]
```

### Caching

- **Fingerprint Caching**: Cache generated fingerprints for reuse
- **Proxy Health Caching**: Cache proxy health status
- **DNS Caching**: Cache DNS resolutions for faster connections

## Monitoring and Metrics

### Proxy Metrics

```python
class ProxyMetrics:
    def __init__(self):
        self.success_rates = {}
        self.response_times = {}
        self.failure_counts = {}
        
    def record_request(self, proxy, success, response_time):
        proxy_id = f"{proxy['host']}:{proxy['port']}"
        
        if proxy_id not in self.success_rates:
            self.success_rates[proxy_id] = []
            self.response_times[proxy_id] = []
            
        self.success_rates[proxy_id].append(success)
        if success:
            self.response_times[proxy_id].append(response_time)
```

### Fingerprint Metrics

- **Detection Rates**: Track how often fingerprints are detected
- **Success Rates**: Monitor successful requests per fingerprint type
- **Rotation Frequency**: Track how often fingerprints need rotation

## Security Considerations

### Data Protection

- **No Logging of Sensitive Data**: Avoid logging proxy credentials or personal data
- **Secure Configuration**: Encrypt proxy credentials and API keys
- **Access Control**: Limit access to proxy and fingerprint configurations

### Compliance

- **Respect robots.txt**: Honor website scraping policies
- **Rate Limiting**: Implement respectful request rates
- **Terms of Service**: Ensure compliance with target site terms

## Testing

### Unit Tests

```python
class TestFingerprintManager:
    def test_user_agent_generation(self):
        manager = FingerprintManager(test_config)
        user_agent = manager.get_random_user_agent("chrome")
        assert "Chrome" in user_agent
        
    def test_fingerprint_rotation(self):
        manager = FingerprintManager(test_config)
        fp1 = manager.get_browser_fingerprint()
        fp2 = manager.rotate_fingerprint()
        assert fp1 != fp2
```

### Integration Tests

```python
class TestProxyIntegration:
    def test_proxy_request(self):
        proxy_manager = ProxyManager(test_config)
        proxy = proxy_manager.get_proxy()
        
        # Test actual request through proxy
        response = requests.get("http://httpbin.org/ip", 
                              proxies=proxy['config'], 
                              timeout=30)
        assert response.status_code == 200
```

## Future Enhancements

### Planned Features

1. **Machine Learning Integration**: AI-powered fingerprint generation
2. **Advanced Detection Evasion**: More sophisticated anti-detection techniques
3. **Proxy Quality Scoring**: ML-based proxy performance prediction
4. **Browser Automation**: Integration with Selenium/Playwright
5. **Mobile Fingerprints**: Support for mobile device fingerprinting

### Performance Improvements

1. **Async Proxy Health Checks**: Non-blocking proxy validation
2. **Intelligent Rotation**: Context-aware proxy and fingerprint selection
3. **Predictive Caching**: Pre-cache likely-needed resources
4. **Load Balancing**: Distribute load across proxy pools