# Scraping Service API Reference

## Core Services

### FingerprintManager

Manages browser fingerprinting and detection evasion.

```python
class FingerprintManager:
    def __init__(self, config: dict, logger: logging.Logger = None)
```

#### Methods

##### `get_random_user_agent(browser_type: str = "chrome") -> str`
Returns a random user agent string for the specified browser type.

**Parameters:**
- `browser_type`: Browser type ("chrome", "firefox", "safari", "edge")

**Returns:**
- `str`: Random user agent string

**Example:**
```python
fingerprint_manager = FingerprintManager(config)
user_agent = fingerprint_manager.get_random_user_agent("chrome")
# Returns: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
```

##### `get_browser_fingerprint(profile_type: str = "default") -> dict`
Generates a complete browser fingerprint for the specified profile.

**Parameters:**
- `profile_type`: Fingerprint profile ("default", "stealth", "mobile", "desktop")

**Returns:**
- `dict`: Complete browser fingerprint

**Example:**
```python
fingerprint = fingerprint_manager.get_browser_fingerprint("stealth")
# Returns:
{
    "user_agent": "Mozilla/5.0...",
    "headers": {
        "Accept": "text/html,application/xhtml+xml...",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br"
    },
    "screen_resolution": (1920, 1080),
    "color_depth": 24,
    "timezone": "America/New_York",
    "language": "en-US",
    "webgl_vendor": "Intel Inc.",
    "canvas_fingerprint": "a1b2c3d4..."
}
```

##### `rotate_fingerprint() -> dict`
Generates a new fingerprint with different characteristics.

**Returns:**
- `dict`: New browser fingerprint

##### `validate_fingerprint(fingerprint: dict) -> bool`
Validates if a fingerprint is properly formatted and realistic.

**Parameters:**
- `fingerprint`: Fingerprint dictionary to validate

**Returns:**
- `bool`: True if fingerprint is valid

### ProxyManager

Manages proxy pools, rotation, and health monitoring.

```python
class ProxyManager:
    def __init__(self, config: dict, logger: logging.Logger = None)
```

#### Methods

##### `get_proxy(region: str = None, proxy_type: str = None) -> dict`
Returns an available proxy from the pool.

**Parameters:**
- `region`: Preferred geographic region ("US", "EU", "APAC", etc.)
- `proxy_type`: Proxy type ("residential", "datacenter", "mobile")

**Returns:**
- `dict`: Proxy configuration

**Example:**
```python
proxy_manager = ProxyManager(config)
proxy = proxy_manager.get_proxy(region="US", proxy_type="residential")
# Returns:
{
    "host": "proxy.example.com",
    "port": 8080,
    "protocol": "http",
    "auth": {
        "username": "user123",
        "password": "pass456"
    },
    "region": "US",
    "provider": "ProxyProvider",
    "config": {
        "http": "http://user123:<EMAIL>:8080",
        "https": "http://user123:<EMAIL>:8080"
    }
}
```

##### `validate_proxy(proxy: dict, timeout: int = 30) -> bool`
Tests if a proxy is working and responsive.

**Parameters:**
- `proxy`: Proxy configuration to test
- `timeout`: Request timeout in seconds

**Returns:**
- `bool`: True if proxy is working

##### `mark_proxy_failed(proxy: dict, error: str = None) -> None`
Marks a proxy as failed and removes it from active rotation.

**Parameters:**
- `proxy`: Proxy configuration that failed
- `error`: Optional error description

##### `get_proxy_stats() -> dict`
Returns statistics about proxy pool performance.

**Returns:**
- `dict`: Proxy statistics

**Example:**
```python
stats = proxy_manager.get_proxy_stats()
# Returns:
{
    "total_proxies": 50,
    "active_proxies": 45,
    "failed_proxies": 5,
    "success_rate": 0.92,
    "average_response_time": 250,
    "regional_distribution": {
        "US": 20,
        "EU": 15,
        "APAC": 10
    }
}
```

##### `rotate_proxy() -> dict`
Forces rotation to the next available proxy.

**Returns:**
- `dict`: New proxy configuration

### SessionCoordinator

Coordinates fingerprint and proxy configurations for scraping sessions.

```python
class SessionCoordinator:
    def __init__(self, fingerprint_manager: FingerprintManager, proxy_manager: ProxyManager)
```

#### Methods

##### `create_session(profile_type: str = "default", region: str = None) -> requests.Session`
Creates a configured requests session with fingerprint and proxy.

**Parameters:**
- `profile_type`: Fingerprint profile type
- `region`: Preferred proxy region

**Returns:**
- `requests.Session`: Configured session

**Example:**
```python
coordinator = SessionCoordinator(fingerprint_manager, proxy_manager)
session = coordinator.create_session("stealth", "US")

# Session is ready to use
response = session.get("https://example.com")
```

##### `enhance_session(session: requests.Session, profile_type: str = None, region: str = None) -> requests.Session`
Enhances existing session with scraping capabilities.

**Parameters:**
- `session`: Existing requests session
- `profile_type`: Fingerprint profile type (optional)
- `region`: Preferred proxy region (optional)

**Returns:**
- `requests.Session`: Enhanced session

##### `rotate_session_config(session: requests.Session) -> requests.Session`
Rotates fingerprint and proxy for existing session.

**Parameters:**
- `session`: Session to rotate configuration for

**Returns:**
- `requests.Session`: Session with new configuration

## Data Models

### BrowserFingerprint

Represents a complete browser fingerprint.

```python
@dataclass
class BrowserFingerprint:
    user_agent: str
    headers: dict
    screen_resolution: tuple
    color_depth: int
    timezone: str
    language: str
    plugins: List[str]
    webgl_vendor: str
    webgl_renderer: str
    canvas_fingerprint: str
    audio_fingerprint: Optional[str] = None
    webrtc_ips: List[str] = field(default_factory=list)
    fonts: List[str] = field(default_factory=list)
```

### ProxyConfig

Represents proxy configuration.

```python
@dataclass
class ProxyConfig:
    host: str
    port: int
    protocol: str  # http, https, socks4, socks5
    auth: Optional[ProxyAuth] = None
    region: str = "unknown"
    provider: str = "unknown"
    proxy_type: str = "datacenter"  # datacenter, residential, mobile
    performance_metrics: Optional[ProxyMetrics] = None
    last_validated: Optional[datetime] = None
    failure_count: int = 0
    is_active: bool = True
```

### ProxyAuth

Represents proxy authentication.

```python
@dataclass
class ProxyAuth:
    username: str
    password: str
    auth_type: str = "basic"  # basic, digest, ntlm
```

### ProxyMetrics

Represents proxy performance metrics.

```python
@dataclass
class ProxyMetrics:
    success_rate: float = 0.0
    average_response_time: float = 0.0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    last_success: Optional[datetime] = None
    last_failure: Optional[datetime] = None
```

## Configuration Schema

### Fingerprint Configuration

```yaml
scraping:
  browser:
    fingerprint_manager:
      enabled: true
      rotation_interval: 300  # seconds
      cache_fingerprints: true
      cache_ttl: 3600  # seconds
      
      profiles:
        default:
          user_agents: ["chrome", "firefox"]
          randomize_screen: true
          randomize_timezone: false
          
        stealth:
          user_agents: ["chrome"]
          randomize_screen: true
          randomize_timezone: true
          webgl_noise: true
          canvas_noise: true
          
        mobile:
          user_agents: ["mobile_chrome", "mobile_safari"]
          screen_resolutions: ["375x667", "414x896"]
          
      user_agents:
        chrome:
          - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
          - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
        firefox:
          - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101"
```

### Proxy Configuration

```yaml
scraping:
  proxy:
    proxy_manager:
      enabled: true
      rotation_strategy: "round_robin"  # round_robin, random, performance_based
      health_check_interval: 60  # seconds
      health_check_timeout: 30  # seconds
      max_failures: 3
      retry_failed_after: 300  # seconds
      
      pools:
        residential:
          priority: 1
          max_concurrent: 10
          endpoints:
            - host: "proxy1.example.com"
              port: 8080
              auth:
                username: "${PROXY_USER}"
                password: "${PROXY_PASS}"
              regions: ["US", "CA"]
              
        datacenter:
          priority: 2
          max_concurrent: 5
          endpoints:
            - host: "dc-proxy.example.com"
              port: 3128
              auth_type: "ip_whitelist"
              regions: ["US"]
```

## Error Handling

### Exception Hierarchy

```python
class ScrapingError(Exception):
    """Base exception for scraping service"""
    pass

class FingerprintError(ScrapingError):
    """Fingerprint generation or validation errors"""
    pass

class ProxyError(ScrapingError):
    """Proxy-related errors"""
    pass

class ProxyConnectionError(ProxyError):
    """Proxy connection failures"""
    pass

class ProxyAuthenticationError(ProxyError):
    """Proxy authentication failures"""
    pass

class DetectionError(ScrapingError):
    """Bot detection errors"""
    pass

class ConfigurationError(ScrapingError):
    """Configuration-related errors"""
    pass
```

### Error Response Format

```python
{
    "error": {
        "type": "ProxyConnectionError",
        "message": "Failed to connect to proxy",
        "code": "PROXY_CONNECTION_FAILED",
        "details": {
            "proxy_host": "proxy.example.com",
            "proxy_port": 8080,
            "timeout": 30,
            "retry_count": 3
        },
        "timestamp": "2024-01-01T12:00:00Z"
    }
}
```

## Health Monitoring

### Health Check Methods

```python
class HealthChecker:
    async def check_fingerprint_health() -> dict
    async def check_proxy_health() -> dict
    async def check_overall_health() -> dict
```

### Health Response Format

```python
{
    "status": "healthy",  # healthy, degraded, unhealthy
    "timestamp": "2024-01-01T12:00:00Z",
    "checks": {
        "fingerprint_generation": {
            "status": "healthy",
            "response_time": 50,
            "details": {
                "profiles_available": 4,
                "user_agents_loaded": 150
            }
        },
        "proxy_pools": {
            "status": "degraded",
            "details": {
                "total_proxies": 50,
                "healthy_proxies": 42,
                "failed_proxies": 8,
                "success_rate": 0.84
            }
        }
    }
}
```

## Usage Examples

### Basic Fingerprint Usage

```python
from src.services.scraping.browser import FingerprintManager

# Initialize manager
fingerprint_manager = FingerprintManager(config)

# Get a stealth fingerprint
fingerprint = fingerprint_manager.get_browser_fingerprint("stealth")

# Use with requests
import requests
session = requests.Session()
session.headers.update(fingerprint['headers'])

response = session.get("https://example.com")
```

### Basic Proxy Usage

```python
from src.services.scraping.proxy import ProxyManager

# Initialize manager
proxy_manager = ProxyManager(config)

# Get a US residential proxy
proxy = proxy_manager.get_proxy(region="US", proxy_type="residential")

# Use with requests
proxies = proxy['config']
response = requests.get("https://example.com", proxies=proxies)

# Handle failures
if response.status_code != 200:
    proxy_manager.mark_proxy_failed(proxy, f"HTTP {response.status_code}")
```

### Combined Usage

```python
from src.services.scraping import FingerprintManager, ProxyManager

class ScrapingClient:
    def __init__(self, config):
        self.fingerprint_manager = FingerprintManager(config)
        self.proxy_manager = ProxyManager(config)
        self.session = requests.Session()
        
    def setup_session(self, profile="stealth", region="US"):
        # Apply fingerprint
        fingerprint = self.fingerprint_manager.get_browser_fingerprint(profile)
        self.session.headers.clear()
        self.session.headers.update(fingerprint['headers'])
        
        # Apply proxy
        proxy = self.proxy_manager.get_proxy(region=region)
        self.session.proxies.clear()
        self.session.proxies.update(proxy['config'])
        
        return self.session
        
    def make_request(self, url, **kwargs):
        try:
            response = self.session.get(url, **kwargs)
            return response
        except requests.exceptions.ProxyError:
            # Rotate proxy and retry
            self.setup_session()
            return self.session.get(url, **kwargs)

# Usage
client = ScrapingClient(config)
client.setup_session("stealth", "US")
response = client.make_request("https://example.com")
```

### Advanced Session Management

```python
from src.services.scraping import SessionCoordinator

class AdvancedScraper:
    def __init__(self, config):
        self.coordinator = SessionCoordinator(
            FingerprintManager(config),
            ProxyManager(config)
        )
        self.sessions = {}
        
    def get_session(self, site_type="default"):
        if site_type not in self.sessions:
            if site_type == "facebook":
                session = self.coordinator.create_session("stealth", "US")
            elif site_type == "mobile":
                session = self.coordinator.create_session("mobile", "US")
            else:
                session = self.coordinator.create_session("default")
                
            self.sessions[site_type] = session
            
        return self.sessions[site_type]
        
    def rotate_session(self, site_type):
        if site_type in self.sessions:
            self.sessions[site_type] = self.coordinator.rotate_session_config(
                self.sessions[site_type]
            )
```

### Async Usage

```python
import aiohttp
from src.services.scraping import FingerprintManager, ProxyManager

class AsyncScrapingClient:
    def __init__(self, config):
        self.fingerprint_manager = FingerprintManager(config)
        self.proxy_manager = ProxyManager(config)
        
    async def create_session(self, profile="default", region=None):
        # Get fingerprint and proxy
        fingerprint = self.fingerprint_manager.get_browser_fingerprint(profile)
        proxy = self.proxy_manager.get_proxy(region=region)
        
        # Create aiohttp session
        connector = aiohttp.TCPConnector()
        session = aiohttp.ClientSession(
            headers=fingerprint['headers'],
            connector=connector
        )
        
        # Set proxy
        session._proxy = proxy['config']['http']
        
        return session
        
    async def make_request(self, url, session=None):
        if session is None:
            session = await self.create_session()
            
        try:
            async with session.get(url) as response:
                return await response.text()
        except aiohttp.ClientProxyConnectionError:
            # Handle proxy failure
            await session.close()
            session = await self.create_session()
            async with session.get(url) as response:
                return await response.text()

# Usage
async def main():
    client = AsyncScrapingClient(config)
    session = await client.create_session("stealth", "US")
    content = await client.make_request("https://example.com", session)
    await session.close()
```