# Data Transformer Service Specifications - Updated 2025

This document provides detailed specifications for the **31 components** in the current Data Transformer service architecture.

## Core Orchestration Components (4)

### 1. DataTransformer (Main Orchestrator)

**Location:** `src/services/transformer/data_transformer.py`

**Purpose:** Main async orchestrator that coordinates the entire data transformation pipeline using a 3-phase processing model with concurrent job management.

**Key Responsibilities:**
- **3-Phase Pipeline Orchestration**: Load & Prepare → Enrich → Finalize & Persist
- **Concurrent Job Management**: Semaphore-controlled worker processing
- **Resource Lifecycle**: Async context manager for proper initialization/cleanup
- **Progress Tracking**: Rich console output with progress bars
- **Error Aggregation**: Centralized error handling and reporting

**Core Methods:**
- `async process_all_files(force_reprocess: bool, reprocess_files: List[str]) -> List[TransformationJob]`
- `async _job_phase_1_load_and_prepare(job: TransformationJob) -> bool`
- `async _job_phase_2_enrich(job: TransformationJob) -> None`
- `async _job_phase_3_finalize_and_persist(job: TransformationJob) -> None`

### 2. ComponentFactory

**Location:** `src/services/transformer/component_factory.py`

**Purpose:** Manages component creation and dependency injection for all transformer services.

**Key Responsibilities:**
- Component instantiation with proper dependency injection
- Service lifecycle management
- Configuration propagation to components
- Resource sharing and optimization

### 3. SpecializedWorkflows

**Location:** `src/services/transformer/specialized_workflows.py`

**Purpose:** Handles specialized processing workflows for different case types and scenarios.

**Key Responsibilities:**
- AFFF-specific processing workflows
- Custom litigation type handling
- Specialized data enrichment patterns
- Workflow orchestration for complex cases

### 4. DataProcessingEngine

**Location:** `src/services/transformer/data_processing_engine.py`

**Purpose:** Core data processing engine with concurrent job management and advanced processing capabilities.

**Key Responsibilities:**
- High-performance data processing
- Concurrent job execution
- Resource optimization
- Processing pipeline coordination

## Data Processing Components (8)

### 5. DocketProcessor

**Location:** `src/services/transformer/docket_processor.py`

**Purpose:** Core docket data processing and validation with comprehensive data handling.

**Key Responsibilities:**
- Docket data parsing and validation
- Data structure normalization
- Field extraction and mapping
- Data integrity verification

### 6. DocketDataCleaner

**Location:** `src/services/transformer/docket_data_cleaner.py`

**Purpose:** Data cleaning and normalization for docket information.

**Key Responsibilities:**
- Data sanitization and cleaning
- Format standardization
- Duplicate detection and removal
- Data quality improvement

### 7. DocketValidator

**Location:** `src/services/transformer/docket_validator.py`

**Purpose:** Comprehensive data validation and integrity checks.

**Key Responsibilities:**
- Schema validation
- Business rule enforcement
- Data completeness verification
- Error detection and reporting

### 8. CourtDataProcessor

**Location:** `src/services/transformer/court_data_processor.py`

**Purpose:** Court-specific data processing and enrichment.

**Key Responsibilities:**
- Court information extraction
- Jurisdiction processing
- Court-specific formatting
- Legal metadata enhancement

### 9. DocketTextHandler

**Location:** `src/services/transformer/docket_text_handler.py`

**Purpose:** Advanced text processing and extraction for docket documents.

**Key Responsibilities:**
- Text extraction from various formats
- Natural language processing
- Text normalization and cleaning
- Content analysis and categorization

### 10. DocketLLMEngine

**Location:** `src/services/transformer/docket_llm_engine.py`

**Purpose:** LLM integration for AI-powered docket processing.

**Key Responsibilities:**
- Multi-provider LLM integration (Mistral, DeepSeek, OpenAI)
- AI-powered content analysis
- Intelligent classification
- Context-aware processing

### 11. CachedPdfData

**Location:** `src/services/transformer/cached_pdf_data.py`

**Purpose:** PDF data caching and management for performance optimization.

**Key Responsibilities:**
- PDF content caching
- Cache invalidation strategies
- Performance optimization
- Memory management

### 12. AfffCalculator

**Location:** `src/services/transformer/afff_calculator.py`

**Purpose:** AFFF-specific calculations and processing.

**Key Responsibilities:**
- AFFF case calculations
- Specialized metrics computation
- AFFF-specific data enrichment
- Custom reporting for AFFF cases

## File Management Components (4)

### 13. FileHandler

**Location:** `src/services/transformer/file_handler.py`

**Purpose:** High-level file operations and management.

**Key Responsibilities:**
- File discovery and organization
- File validation and verification
- Atomic file operations
- File lifecycle management

### 14. FileOperations

**Location:** `src/services/transformer/file_operations.py`

**Purpose:** Low-level file operations with robust error handling.

**Key Responsibilities:**
- Basic file I/O operations
- File system interactions
- Error handling and recovery
- Performance optimization

### 15. DocketFileManager

**Location:** `src/services/transformer/docket_file_manager.py`

**Purpose:** Specialized file management for docket-related files.

**Key Responsibilities:**
- Docket file organization
- File naming conventions
- Version management
- File relationship tracking

### 16. Uploader

**Location:** `src/services/transformer/uploader.py`

**Purpose:** S3 upload operations and cloud storage management.

**Key Responsibilities:**
- S3 upload coordination
- Retry mechanisms
- Progress tracking
- Cloud storage optimization

## HTML and Document Processing (3)

### 17. DocketHTMLProcessor

**Location:** `src/services/transformer/docket_html_processor.py`

**Purpose:** Specialized HTML document processing for docket content.

**Key Responsibilities:**
- HTML parsing and extraction
- Docket-specific content identification
- Structured data extraction
- HTML content validation

### 18. TransformerHTMLIntegrationService

**Location:** `src/services/transformer/html_integration_service.py`

**Purpose:** HTML integration service for transformer workflows.

**Key Responsibilities:**
- HTML content integration
- Cross-service HTML coordination
- HTML processing workflows
- Integration with other services

### 19. HTMLIntegrationService

**Location:** `src/services/transformer/html_integration_service.py`

**Purpose:** General HTML integration capabilities.

**Key Responsibilities:**
- General HTML processing
- Content extraction and parsing
- HTML validation and cleaning
- Integration with processing pipeline

## Legal Domain Processing (6)

### 20. LawFirmProcessor

**Location:** `src/services/transformer/law_firm_processor.py`

**Purpose:** Law firm data processing and normalization.

**Key Responsibilities:**
- Law firm identification and extraction
- Name normalization and standardization
- Law firm relationship mapping
- Attorney association processing

### 21. LawFirmIntegration

**Location:** `src/services/transformer/law_firm_integration.py`

**Purpose:** Law firm integration service for comprehensive law firm handling.

**Key Responsibilities:**
- Cross-system law firm integration
- Data synchronization
- Relationship management
- Integration workflows

### 22. LitigationClassifier

**Location:** `src/services/transformer/litigation_classifier.py`

**Purpose:** AI-powered litigation classification and categorization.

**Key Responsibilities:**
- Litigation type identification
- AI-powered classification
- Case categorization
- Relevance scoring

### 23. TransferHandler

**Location:** `src/services/transformer/transfer_handler.py`

**Purpose:** Court transfer processing and tracking.

**Key Responsibilities:**
- Transfer detection and processing
- Court jurisdiction changes
- Transfer history tracking
- Metadata updates for transfers

### 24. MDLProcessor

**Location:** `src/services/transformer/mdl_processor.py`

**Purpose:** Current MDL (Multi-District Litigation) processing.

**Key Responsibilities:**
- MDL identification and classification
- MDL metadata enrichment
- Case association with MDL proceedings
- MDL-specific processing workflows

### 25. MDLDataProcessor

**Location:** `src/services/transformer/mdl_data_processor.py`

**Purpose:** Advanced MDL data processing and enrichment.

**Key Responsibilities:**
- Complex MDL data analysis
- MDL relationship mapping
- Advanced MDL metrics
- Data enrichment for MDL cases

## MDL Management Components (4)

### 26. MDLDescriptionManager

**Location:** `src/services/transformer/mdl_description_manager.py`

**Purpose:** MDL description management and standardization.

**Key Responsibilities:**
- MDL description processing
- Description standardization
- Content management for MDL descriptions
- Description quality assurance

### 27. MDLLookupManager

**Location:** `src/services/transformer/mdl_lookup_manager.py`

**Purpose:** MDL lookup operations and reference management.

**Key Responsibilities:**
- MDL reference lookups
- Cross-reference management
- MDL database queries
- Reference validation

### 28. MDLPersistenceManager

**Location:** `src/services/transformer/mdl_persistence_manager.py`

**Purpose:** MDL data persistence and storage management.

**Key Responsibilities:**
- MDL data storage
- Persistence strategies
- Data consistency management
- Storage optimization

### 29. MDLProcessor (Original)

**Location:** `src/services/transformer/mdl_processor_original.py`

**Purpose:** Legacy MDL processor maintained for compatibility.

**Key Responsibilities:**
- Legacy MDL processing
- Backward compatibility
- Migration support
- Fallback processing

## Infrastructure Components (2)

### 30. ErrorHandler

**Location:** `src/services/transformer/error_handler.py`

**Purpose:** Comprehensive error handling and recovery mechanisms.

**Key Responsibilities:**
- Error detection and classification
- Recovery strategies
- Error reporting and logging
- Graceful degradation

### 31. Config

**Location:** `src/services/transformer/config.py`

**Purpose:** Configuration management for transformer services.

**Key Responsibilities:**
- Configuration loading and validation
- Environment-specific settings
- Configuration propagation
- Dynamic configuration updates

## Service Integration Patterns

### Async Service Base
All services inherit from `AsyncServiceBase` providing:
- Consistent async lifecycle management
- Dependency injection support
- Logging integration
- Configuration management

### Dependency Injection
Services use constructor injection for dependencies:
```python
class ServiceExample(AsyncServiceBase):
    def __init__(self, config: Config, logger: Logger, dependency: SomeService):
        super().__init__(config, logger)
        self.dependency = dependency
```

### Error Handling
Standardized error handling across all services:
- Graceful degradation for non-critical failures
- Detailed error context preservation
- Automatic retry mechanisms
- Centralized error reporting

### Performance Optimization
- Concurrent processing with configurable limits
- Intelligent caching strategies
- Resource pooling and reuse
- Memory management optimization

These service specifications provide the foundation for understanding, maintaining, and extending the comprehensive Data Transformer service architecture.