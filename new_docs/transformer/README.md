# Data Transformer Service Architecture Documentation - 2025 Update

This directory contains comprehensive documentation for the current Data Transformer service architecture in LexGenius, located in `src/services/transformer/`.

## Overview

The Data Transformer module is responsible for the post-processing and enrichment of scraped court docket data. This includes PDF processing, text extraction, AI-powered data extraction and classification (e.g., identifying litigation areas, extracting key case details), and preparing the data for final storage and reporting. The architecture is service-oriented, asynchronous, and designed for concurrency and maintainability.

## Current Architecture (`src/services/transformer/`)

The current architecture is the result of significant evolution and refactoring efforts. The system consists of **31 focused components** organized into logical groups:

### Component Categories

1. **Core Orchestration Components** (4)
   - `DataTransformer` - Main transformation workflow coordination with async context management
   - `ComponentFactory` - Component creation and dependency management
   - `SpecializedWorkflows` - Specialized processing workflows for different case types
   - `DataProcessingEngine` - Core data processing engine with concurrent job management

2. **Data Processing Components** (8)
   - `DocketProcessor` - Core docket data processing and validation
   - `DocketDataCleaner` - Data cleaning and normalization
   - `DocketValidator` - Data validation and integrity checks
   - `CourtDataProcessor` - Court-specific data processing
   - `DocketTextHandler` - Text processing and extraction
   - `DocketLLMEngine` - LLM integration for AI-powered processing
   - `CachedPdfData` - PDF data caching and management
   - `AfffCalculator` - AFFF-specific calculations and processing

3. **File Management Components** (4)
   - `FileHandler` - File operations and management
   - `FileOperations` - Low-level file operations
   - `DocketFileManager` - Docket-specific file management
   - `Uploader` - S3 upload operations and management

4. **HTML and Document Processing** (3)
   - `DocketHTMLProcessor` - HTML document processing
   - `TransformerHTMLIntegrationService` - HTML integration service
   - `HTMLIntegrationService` - General HTML integration

5. **Legal Domain Processing** (6)
   - `LawFirmProcessor` - Law firm data processing and normalization
   - `LawFirmIntegration` - Law firm integration service
   - `LitigationClassifier` - AI-powered litigation classification
   - `TransferHandler` - Court transfer processing
   - `MDLProcessor` - MDL (Multi-District Litigation) processing
   - `MDLDataProcessor` - MDL data processing and enrichment

6. **MDL Management Components** (4)
   - `MDLDescriptionManager` - MDL description management
   - `MDLLookupManager` - MDL lookup operations
   - `MDLPersistenceManager` - MDL data persistence
   - `MDLProcessor` (original) - Legacy MDL processor

7. **Infrastructure Components** (2)
   - `ErrorHandler` - Error handling and recovery
   - `Config` - Configuration management

## Key Features

### Advanced AI Integration
- **Multi-provider LLM support** (Mistral, DeepSeek, OpenAI)
- **Intelligent fallback mechanisms** for AI processing
- **Context-aware classification** using case history

### Robust File Management
- **Atomic file operations** with rollback capability
- **S3 integration** with retry mechanisms
- **Intelligent file discovery** with pattern matching

### Comprehensive Error Handling
- **Graceful degradation** for non-critical failures
- **Detailed error reporting** with context preservation
- **Automatic retry mechanisms** for transient failures

### Performance Optimization
- **Concurrent processing** with configurable limits
- **Intelligent caching** for PDF and AI results
- **Resource pooling** for database connections

## Documentation Structure

| File | Description |
|------|-------------|
| `README.md` | This overview document |
| `architecture_overview.md` | Detailed system architecture and integration patterns |
| `service_specifications.md` | Comprehensive specifications for all 31 components |
| `integration_guide.md` | Integration patterns and usage examples |
| `configuration_guide.md` | Configuration management and setup |
| `api_reference.md` | API documentation for public interfaces |
| `flow_charts.md` | Visual workflow diagrams |
| `class_diagrams.md` | UML class diagrams |

## Quick Start

### Basic Usage
```python
from src.services.transformer import DataTransformer
from src.config_models.transformer import DataTransformerConfig

# Initialize configuration
config = DataTransformerConfig(
    concurrent_jobs=3,
    enable_ai_processing=True,
    pdf_processing_timeout=300
)

# Use async context manager
async with DataTransformer(config, logger) as transformer:
    results = await transformer.process_all_files(
        force_reprocess=False,
        reprocess_files=[]
    )
```

### Component Integration
```python
from src.services.transformer.component_factory import ComponentFactory

# Create component factory
factory = ComponentFactory(config, logger)

# Get specific components
docket_processor = await factory.get_docket_processor()
law_firm_processor = await factory.get_law_firm_processor()
```

## Processing Pipeline

The transformer uses a **3-phase processing model**:

1. **Phase 1: Load & Prepare**
   - Load JSON data and validate file availability
   - Check PDF/MD file availability with fallback patterns
   - Validate data completeness requirements

2. **Phase 2: Enrich**
   - HTML processing for plaintiff/defendant context extraction
   - PDF text extraction and document processing
   - LLM classification for AI-powered litigation identification
   - Additional enrichments (court info, transfers, MDL categorization)

3. **Phase 3: Finalize & Persist**
   - File renaming based on extracted case titles
   - Save final JSON with all enrichments
   - Upload to S3 and update databases

## Configuration

### Core Configuration
```yaml
transformer:
  concurrent_jobs: 3
  enable_ai_processing: true
  pdf_processing_timeout: 300
  max_retries: 3
  cache_enabled: true
```

### AI Configuration
```yaml
ai:
  providers:
    - mistral
    - deepseek
    - openai
  fallback_enabled: true
  timeout: 120
```

### File Management
```yaml
files:
  data_directory: "data"
  backup_enabled: true
  cleanup_on_success: false
```

## Performance Characteristics

### Concurrency
- **Semaphore-controlled** concurrent processing
- **Configurable job limits** (default: 3 concurrent jobs)
- **Resource-aware** processing with automatic throttling

### Caching
- **PDF content caching** for repeated processing
- **AI result caching** to avoid redundant API calls
- **Intelligent cache invalidation** based on file changes

### Error Recovery
- **Automatic retries** for transient failures
- **Graceful degradation** when AI services are unavailable
- **Detailed error context** for debugging and monitoring

## Integration Points

### Main Architecture Integration
- Integrates with `MainOrchestrator` via `ProcessingOrchestrator`
- Uses `MainServiceFactory` for infrastructure dependencies
- Leverages shared repositories and configuration systems

### External Services
- **S3 Integration** for file storage and retrieval
- **DynamoDB** for metadata persistence
- **Multiple LLM Providers** for AI processing
- **PDF Processing Libraries** for document extraction

## Development Guidelines

### Adding New Components
1. Inherit from `AsyncServiceBase`
2. Implement proper async lifecycle methods
3. Use dependency injection for external dependencies
4. Follow established error handling patterns
5. Add comprehensive logging and monitoring

### Testing Strategy
- **Unit tests** for individual components
- **Integration tests** for service interactions
- **End-to-end tests** for complete workflows
- **Performance tests** for concurrency and throughput

### Monitoring and Observability
- **Structured logging** with contextual information
- **Progress tracking** with Rich console output
- **Error aggregation** and reporting
- **Performance metrics** collection

## Future Enhancements

The architecture is designed for extensibility:

1. **Additional AI Providers** - Easy integration of new LLM services
2. **Enhanced Caching** - Redis integration for distributed caching
3. **Real-time Processing** - Event-driven processing capabilities
4. **Advanced Analytics** - Processing metrics and performance monitoring
5. **Microservice Migration** - Potential extraction to independent services

## Troubleshooting

### Common Issues
- **Memory usage** - Monitor concurrent job limits and caching
- **AI timeouts** - Configure appropriate timeout values
- **File access errors** - Verify file permissions and paths
- **S3 connectivity** - Check AWS credentials and network connectivity

### Debug Mode
Enable debug logging for detailed troubleshooting:
```python
config.debug_mode = True
config.log_level = "DEBUG"
```

The service-oriented architecture provides a robust foundation for concurrent data processing while maintaining clean separation of concerns and extensibility for future AI capabilities.