# Data Transformer Architecture Overview - Updated 2025

## System Overview

The Data Transformer architecture in LexGenius processes court docket data for enrichment with AI-powered litigation classification, PDF text extraction, and comprehensive metadata enhancement. The system has evolved into a comprehensive service-oriented architecture with **31 specialized components** organized into logical service groups.

## Integration with Main Architecture

### Entry Point Flow
```
src/main.py → MainOrchestrator → ProcessingOrchestrator → DataTransformer (from src/services/transformer/)
```

The Transformer system (located in `src/services/transformer/`) integrates with the overall LexGenius architecture:
- **MainServiceFactory**: Manages infrastructure dependencies (DynamoDB, S3) used by transformer services
- **ProcessingOrchestrator**: High-level coordination for data processing workflows, invoking the `DataTransformer`
- **`DataTransformer`**: The primary entry point for the transformer domain, orchestrating various sub-services for transformation and enrichment
- **Repository Integration**: Transformer services use `PacerRepository` and other data access layers provided via the factory
- **Configuration Integration**: Uses Pydantic models from `src/config_models/` for its configuration

## Current Architecture (2025)

The current architecture consists of **31 focused components** organized into logical groups:

### Component Categories

1. **Core Orchestration Components** (4)
   - `DataTransformer` - Main transformation workflow coordination with async context management
   - `ComponentFactory` - Component creation and dependency management
   - `SpecializedWorkflows` - Specialized processing workflows for different case types
   - `DataProcessingEngine` - Core data processing engine with concurrent job management

2. **Data Processing Components** (8)
   - `DocketProcessor` - Core docket data processing and validation
   - `DocketDataCleaner` - Data cleaning and normalization
   - `DocketValidator` - Data validation and integrity checks
   - `CourtDataProcessor` - Court-specific data processing
   - `DocketTextHandler` - Text processing and extraction
   - `DocketLLMEngine` - LLM integration for AI-powered processing
   - `CachedPdfData` - PDF data caching and management
   - `AfffCalculator` - AFFF-specific calculations and processing

3. **File Management Components** (4)
   - `FileHandler` - File operations and management
   - `FileOperations` - Low-level file operations
   - `DocketFileManager` - Docket-specific file management
   - `Uploader` - S3 upload operations and management

4. **HTML and Document Processing** (3)
   - `DocketHTMLProcessor` - HTML document processing
   - `TransformerHTMLIntegrationService` - HTML integration service
   - `HTMLIntegrationService` - General HTML integration

5. **Legal Domain Processing** (6)
   - `LawFirmProcessor` - Law firm data processing and normalization
   - `LawFirmIntegration` - Law firm integration service
   - `LitigationClassifier` - AI-powered litigation classification
   - `TransferHandler` - Court transfer processing
   - `MDLProcessor` - MDL (Multi-District Litigation) processing
   - `MDLDataProcessor` - MDL data processing and enrichment

6. **MDL Management Components** (4)
   - `MDLDescriptionManager` - MDL description management
   - `MDLLookupManager` - MDL lookup operations
   - `MDLPersistenceManager` - MDL data persistence
   - `MDLProcessor` (original) - Legacy MDL processor

7. **Infrastructure Components** (2)
   - `ErrorHandler` - Error handling and recovery
   - `Config` - Configuration management

## Core Responsibilities

The Data Transformer system handles:

1. **Court Data Enrichment** - AI-powered analysis of PACER docket data using multiple LLM providers (Mistral, DeepSeek, OpenAI)
2. **Document Processing** - PDF text extraction and litigation classification with advanced caching
3. **HTML Integration** - S3-based HTML content retrieval and case detail extraction via `HTMLCaseParser`
4. **Case Classification** - MDL categorization, litigation type identification, and case relevance scoring
5. **Party Data Processing** - Plaintiff/defendant extraction and law firm normalization
6. **Metadata Enhancement** - Court transfers, case status, and comprehensive metadata augmentation
7. **File Operations** - Document renaming, cleanup, and organization with atomic operations
8. **Specialized Workflows** - AFFF calculations, specialized case type processing

## Architecture Evolution

### Legacy Monolithic Design (Pre-2025)
```
┌─────────────────────────────────────────────────────────┐
│                    Legacy Transformer                   │
│                   (Synchronous)                         │
│                                                         │
│  • Sequential file processing                           │
│  • Tight coupling between components                    │
│  • Limited error handling                               │
│  • Single-threaded operations                           │
│  • Basic AI integration                                 │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### Current Service-Oriented Design (2025)
```
┌─────────────────────────────────────────────────────────┐
│                 DataTransformer                         │
│              (Async Orchestrator)                       │
│                                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ Core Orch.  │  │ Data Proc.  │  │ File Mgmt   │     │
│  │ (4 comps)   │  │ (8 comps)   │  │ (4 comps)   │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
│                                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ HTML/Doc    │  │ Legal Dom.  │  │ MDL Mgmt    │     │
│  │ (3 comps)   │  │ (6 comps)   │  │ (4 comps)   │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
│                                                         │
│  ┌─────────────┐                                       │
│  │ Infra       │                                       │
│  │ (2 comps)   │                                       │
│  └─────────────┘                                       │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## Processing Pipeline

### 3-Phase Processing Model
```mermaid
graph TD
    A[Phase 1: Load & Prepare] --> B[Phase 2: Enrich]
    B --> C[Phase 3: Finalize & Persist]
    
    A --> A1[Load JSON Data]
    A --> A2[Validate Files]
    A --> A3[Check Completeness]
    
    B --> B1[HTML Processing]
    B --> B2[PDF Text Extraction]
    B --> B3[LLM Classification]
    B --> B4[MDL Enrichment]
    B --> B5[Court Data Processing]
    
    C --> C1[File Renaming]
    C --> C2[Save Final JSON]
    C --> C3[Upload to S3]
    C --> C4[Database Updates]
```

### Concurrent Job Processing
- **Semaphore-controlled concurrency** for resource management
- **Async context managers** for proper resource lifecycle
- **Progress tracking** with Rich console output
- **Error aggregation** and centralized reporting

## Service Integration Patterns

### Dependency Injection
All services inherit from `AsyncServiceBase` and use dependency injection:
```python
class DataTransformer(AsyncServiceBase):
    def __init__(self, config: DataTransformerConfig, logger: Logger):
        super().__init__(config, logger)
        self.component_factory = ComponentFactory(config, logger)
```

### Async Context Management
Services implement proper async lifecycle management:
```python
async with DataTransformer(config, logger) as transformer:
    results = await transformer.process_all_files()
```

### Configuration Management
Centralized configuration using Pydantic models:
```python
@dataclass
class DataTransformerConfig(AppConfig, AsyncServiceBase):
    concurrent_jobs: int = 3
    enable_ai_processing: bool = True
    pdf_processing_timeout: int = 300
```

## Key Features

### Advanced AI Integration
- **Multi-provider LLM support** (Mistral, DeepSeek, OpenAI)
- **Intelligent fallback mechanisms** for AI processing
- **Context-aware classification** using case history

### Robust File Management
- **Atomic file operations** with rollback capability
- **S3 integration** with retry mechanisms
- **Intelligent file discovery** with pattern matching

### Comprehensive Error Handling
- **Graceful degradation** for non-critical failures
- **Detailed error reporting** with context preservation
- **Automatic retry mechanisms** for transient failures

### Performance Optimization
- **Concurrent processing** with configurable limits
- **Intelligent caching** for PDF and AI results
- **Resource pooling** for database connections

## Future Enhancements

The architecture is designed for extensibility:

1. **Additional AI Providers** - Easy integration of new LLM services
2. **Enhanced Caching** - Redis integration for distributed caching
3. **Real-time Processing** - Event-driven processing capabilities
4. **Advanced Analytics** - Processing metrics and performance monitoring
5. **Microservice Migration** - Potential extraction to independent services

The service-oriented architecture provides a robust foundation for concurrent data processing while maintaining clean separation of concerns and extensibility for future AI capabilities.