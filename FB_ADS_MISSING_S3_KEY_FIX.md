# Facebook Ads Missing S3 Key Fix

## Issue
The code was not processing images for existing database items that were missing s3_image_key fields. This meant that ads that were previously saved without images (due to failures or other issues) would never have their images downloaded and processed.

## Requirements
If an item exists in the database and does not have an s3_image_key:
1. Download the image from CDN
2. Calculate PHash
3. Upload to S3
4. Add to local image queue for OCR processing
5. Update last_updated with config's iso_date

## Fix Applied

### File: `/src/services/fb_ads/jobs/job_runner_service.py` (lines 927-965)

Added logic to detect and process existing database items missing s3_image_key:

```python
# Check if this is an existing DB item missing s3_image_key
existing_db_data = ad_structured.get("_existing_db_data", {})
existing_s3_key = existing_db_data.get("S3ImageKey") if existing_db_data else None
is_missing_s3_key = ad_structured.get("_existing_in_db") and not existing_s3_key

# Process image if:
# 1. We have a creative_id and source URL (new ads or updates)
# 2. OR this is an existing DB item without an s3_image_key
if ad_creative_id and (source_image_url or is_missing_s3_key):
    # For existing items without s3_key, try to get image URL from existing data
    if is_missing_s3_key and not source_image_url:
        source_image_url = (existing_db_data.get("OriginalImageUrl") or 
                          existing_db_data.get("VideoPreviewImageUrl"))
        if source_image_url:
            logger.info(
                f"Job {job.job_id} (Phase 2): Found missing S3 key for existing ad {ad_archive_id}, "
                f"will download image from: {source_image_url[:100]}..."
            )
    
    # ... process image ...
    
    # If this was an existing item missing s3_key, mark that we need to update it
    if is_missing_s3_key:
        ad_structured["_needs_s3_key_update"] = True
```

## How It Works

1. **Detection**: When checking existing database items (lines 870-902), the code stores the existing data in `_existing_db_data`

2. **Missing S3 Key Check**: The new code checks if:
   - The ad exists in the database (`_existing_in_db` is True)
   - AND the existing data doesn't have an S3ImageKey

3. **Image URL Recovery**: If missing S3 key, it retrieves the image URL from the existing database record (OriginalImageUrl or VideoPreviewImageUrl)

4. **Image Processing**: Downloads the image, calculates PHash, uploads to S3, and adds to local_image_queue (existing flow)

5. **Database Update**: The existing ad_db_service logic already handles updating:
   - Sets `last_updated` to processing_date for ALL items
   - Detects that s3_image_key field changed (None → actual key)
   - Includes the item in batch write with updated last_updated

## Verification

The system now:
- ✅ Detects existing DB items without s3_image_key
- ✅ Downloads images from CDN using stored URLs
- ✅ Calculates PHash and uploads to S3
- ✅ Updates local_image_queue for deferred OCR
- ✅ Updates last_updated to config's iso_date
- ✅ Saves all changes to database

## Complete Fix Summary

Together with the previous fixes, the Facebook ads processing now correctly:
1. Updates `last_updated` using iso_date from config (not current datetime)
2. Updates `end_date` to maximum of all date sources
3. Downloads images from CDN when missing from S3
4. Uses correct S3 key format: `adarchive/{ad_archive_id}/{ad_creative_id}.jpg`
5. Saves PHash to FBImageHash table
6. Processes existing DB items that are missing images
7. Updates local_image_queue for all processed images