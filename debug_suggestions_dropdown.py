#!/usr/bin/env python3
"""
Debug script to investigate the actual selectors used by the suggestions dropdown.
This will help identify the correct selector to use for dropdown detection.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestConfig:
    """Test configuration for debugging."""
    
    def __init__(self):
        self.config = {
            'camoufox': {
                'browser': {
                    'headless': False,  # Visual debugging
                    'timeout': 60000,
                    'viewport': {'width': 1920, 'height': 1080}
                },
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                    'refresh_before_expiry_seconds': 30
                },
                'anti_bot': {
                    'humanize': True,
                    'mouse_curves': True,
                    'typing_variation': True,
                    'disable_ad_blocker_detection': True,
                    'block_resources_for_performance': False
                },
                'search': {
                    'typing_delay': 120,
                    'suggestion_wait': 10000,
                    'capture_wait': 5
                }
            }
        }

async def debug_suggestions_dropdown():
    """Debug the suggestions dropdown to find correct selectors."""
    logger.info("🔍 Debugging suggestions dropdown selectors")
    
    config = TestConfig().config
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )
    
    try:
        # Create session and setup
        success = await session_manager.create_new_session()
        if not success:
            logger.error("❌ Failed to create session")
            return False
        
        # Setup Ad Library
        setup_success = await session_manager._setup_ad_library_search()
        if not setup_success:
            logger.error("❌ Failed to setup Ad Library")
            return False
        
        # Locate search input and type
        search_input_selector = 'input[placeholder="Search by keyword or advertiser"]'
        logger.info(f"🔍 Locating search input: {search_input_selector}")
        
        search_input = await session_manager.page.wait_for_selector(search_input_selector, state='visible')
        await search_input.scroll_into_view_if_needed()
        await search_input.click()
        await search_input.fill('')
        await search_input.type("Morgan & Morgan", delay=120)
        
        logger.info("⌨️ Typed 'Morgan & Morgan'")
        
        # Wait a moment for dropdown to appear
        await asyncio.sleep(3)
        
        # Now debug what elements are available
        logger.info("🔍 Debugging available elements after typing...")
        
        # Check for various possible selectors
        selectors_to_check = [
            'div[role="listbox"]',
            '[role="listbox"]',
            '.listbox',
            '[data-testid*="suggestion"]',
            '[data-testid*="typeahead"]',
            '[data-testid*="dropdown"]',
            'div[aria-expanded="true"]',
            'ul[role="menu"]',
            'div[role="menu"]',
            'div[data-visualcompletion="ignore-dynamic"]',
            'div > div > div[role="heading"]',  # Look for heading elements in nested divs
        ]
        
        found_selectors = []
        for selector in selectors_to_check:
            try:
                elements = await session_manager.page.query_selector_all(selector)
                if elements:
                    logger.info(f"✅ Found {len(elements)} elements with selector: {selector}")
                    found_selectors.append(selector)
                    
                    # For the first few elements, get some sample text
                    if len(elements) <= 5:
                        for i, element in enumerate(elements):
                            try:
                                text = await session_manager.page.evaluate('el => el.textContent', element)
                                logger.info(f"   Element {i+1} text: '{text[:100]}...'")
                            except:
                                pass
                else:
                    logger.debug(f"❌ No elements found with selector: {selector}")
            except Exception as e:
                logger.debug(f"❌ Error checking selector {selector}: {e}")
        
        # Try to find elements that might contain "Morgan" text
        logger.info("🔍 Looking for elements containing 'Morgan' text...")
        try:
            morgan_elements = await session_manager.page.query_selector_all('*:has-text("Morgan")')
            logger.info(f"📋 Found {len(morgan_elements)} elements containing 'Morgan'")
            
            for i, element in enumerate(morgan_elements[:5]):  # Check first 5
                try:
                    tag_name = await session_manager.page.evaluate('el => el.tagName', element)
                    text = await session_manager.page.evaluate('el => el.textContent', element)
                    role = await session_manager.page.evaluate('el => el.getAttribute("role")', element)
                    classes = await session_manager.page.evaluate('el => el.className', element)
                    
                    logger.info(f"   Morgan element {i+1}: <{tag_name}> role='{role}' class='{classes}' text='{text[:50]}...'")
                except Exception as e:
                    logger.debug(f"Error examining Morgan element {i+1}: {e}")
                    
        except Exception as e:
            logger.error(f"Error finding Morgan elements: {e}")
        
        # Check the DOM structure around the search input
        logger.info("🔍 Checking DOM structure around search input...")
        try:
            # Get parent elements and their children
            parent_structure = await session_manager.page.evaluate('''
                () => {
                    const searchInput = document.querySelector('input[placeholder*="Search"]');
                    if (searchInput) {
                        const parent = searchInput.parentElement;
                        const grandParent = parent ? parent.parentElement : null;
                        
                        const result = {
                            searchInputFound: true,
                            parentTagName: parent ? parent.tagName : null,
                            grandParentTagName: grandParent ? grandParent.tagName : null,
                            siblingsCount: parent ? parent.children.length : 0,
                            nextSiblings: []
                        };
                        
                        // Check for next siblings that might be the dropdown
                        if (parent) {
                            let nextSibling = parent.nextElementSibling;
                            let count = 0;
                            while (nextSibling && count < 5) {
                                result.nextSiblings.push({
                                    tagName: nextSibling.tagName,
                                    className: nextSibling.className,
                                    role: nextSibling.getAttribute('role'),
                                    textLength: nextSibling.textContent ? nextSibling.textContent.length : 0
                                });
                                nextSibling = nextSibling.nextElementSibling;
                                count++;
                            }
                        }
                        
                        return result;
                    }
                    return { searchInputFound: false };
                }
            ''')
            
            logger.info(f"📊 DOM structure: {parent_structure}")
            
        except Exception as e:
            logger.error(f"Error checking DOM structure: {e}")
        
        # Wait for user to examine the page manually
        logger.info("⏸️ Pausing for manual inspection. Check the browser window for the dropdown.")
        logger.info("   Press Enter to continue when ready...")
        input()  # Wait for user input
        
        logger.info("✅ Debug session completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Debug session failed: {e}")
        return False
    finally:
        await session_manager.cleanup()

async def main():
    """Run the debug session."""
    logger.info("🚀 Starting Suggestions Dropdown Debug Session")
    
    success = await debug_suggestions_dropdown()
    
    if success:
        logger.info("✅ Debug session completed successfully")
    else:
        logger.error("❌ Debug session failed")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Debug session interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Debug session failed: {e}")
        sys.exit(1)