# Facebook Ads PHash Saving Method Fix

## Issue
The error `'FBImageHashService' object has no attribute 'add_or_update_record'` occurred when trying to save PHash data to the FBImageHash table.

## Root Cause Analysis

1. **Type Mismatch**: The `ImageHandler` expects a `FBImageHashManager` but the error suggests it might be getting a `FBImageHashService`
2. **Method Name Issue**: The code was calling `add_or_update_record` which doesn't exist on either class
3. **Missing Async Method**: `FBImageHashManager` is a legacy wrapper that didn't have async methods exposed

## Architecture Understanding

- `FBImageHashManager`: Legacy wrapper class for backward compatibility
- `FBImageHashService`: Modern service class that extends `AsyncServiceBase`
- `FBImageHashRepository`: Repository that actually has the `add_or_update_record` method

The hierarchy is:
```
FBImageHashManager (legacy wrapper)
  └─> FBImageHashService (service layer)
       └─> FBImageHashRepository (data layer)
            └─> add_or_update_record() method
```

## Fixes Applied

### 1. Fixed Method Name and Field Names in ImageHandler
**File**: `/src/services/fb_ads/image_handler.py` (lines 462-472)

```python
# BEFORE:
hash_record = {
    "PHash": phash_str,
    "AdArchiveID": ad_archive_id,
    "ImageUrl": image_url,
    "S3ImageKey": s3_key,  # Wrong field name
    "CreatedDate": self.config.get("iso_date", ""),  # Wrong field names
    "LastSeen": self.config.get("iso_date", ""),
}
await self.hash_manager.add_or_update_record(hash_record)  # Wrong method

# AFTER:
hash_record = {
    "PHash": phash_str,
    "AdArchiveID": ad_archive_id,
    "ImageUrl": image_url,
    "S3Key": s3_key,  # Correct field name
    "LastUpdated": self.config.get("iso_date", ""),  # Correct field name
}
await self.hash_manager.add_hash_record(hash_record)  # Correct method
```

### 2. Added Async Method to FBImageHashManager
**File**: `/src/services/fb_ads/image_utils.py` (lines 449-452)

```python
async def add_hash_record(self, record: dict[str, Any]) -> bool:
    """Async method to add a hash record."""
    async with self.service.repository.storage:
        return await self.service.add_hash_record(record)
```

## How It Works

1. `ImageHandler` calls `hash_manager.add_hash_record()` (async)
2. `FBImageHashManager.add_hash_record()` forwards to `FBImageHashService.add_hash_record()`
3. `FBImageHashService.add_hash_record()` converts PascalCase to snake_case and calls `repository.add_or_update_record()`
4. `FBImageHashRepository.add_or_update_record()` saves to DynamoDB

## Field Mapping
The service layer handles the conversion between PascalCase (legacy) and snake_case (modern):
- `PHash` → `p_hash`
- `AdArchiveID` → `ad_archive_id`
- `S3Key` → `s3_image_key`
- `LastUpdated` → `created_date` and `last_seen`

## Result
- ✅ PHash records are now saved correctly to FBImageHash table
- ✅ Proper async method chain from ImageHandler to DynamoDB
- ✅ Correct field names and method names used throughout