#!/usr/bin/env python3
"""
Analyze attorney names and their associated law firms to identify issues.
"""
import sqlite3
import json
from collections import defaultdict, Counter
from typing import Dict, List, Set

def analyze_attorney_law_firms(db_path: str):
    """
    Analyze attorney names and their law firm associations.
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get all attorney records
    cursor.execute("SELECT attorney, docket_num FROM pacermon_searches WHERE attorney IS NOT NULL")
    records = cursor.fetchall()
    
    attorney_law_firms = defaultdict(set)  # attorney_name -> set of law_firms
    law_firm_counts = Counter()
    attorney_counts = Counter()
    
    print(f"Processing {len(records)} records...")
    
    for attorney_json, docket_num in records:
        try:
            attorney_data = json.loads(attorney_json)
            
            # Handle both single attorney and list of attorneys
            if isinstance(attorney_data, list):
                for attorney in attorney_data:
                    if isinstance(attorney, dict) and 'attorney_name' in attorney:
                        name = attorney['attorney_name'].strip()
                        law_firm = attorney.get('law_firm', '').strip()
                        if name:
                            attorney_counts[name] += 1
                            if law_firm:
                                attorney_law_firms[name].add(law_firm)
                                law_firm_counts[law_firm] += 1
            elif isinstance(attorney_data, dict) and 'attorney_name' in attorney_data:
                name = attorney_data['attorney_name'].strip()
                law_firm = attorney_data.get('law_firm', '').strip()
                if name:
                    attorney_counts[name] += 1
                    if law_firm:
                        attorney_law_firms[name].add(law_firm)
                        law_firm_counts[law_firm] += 1
                        
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"Error parsing: {attorney_json[:100]}... - {e}")
            continue
    
    conn.close()
    
    # Analysis results
    print("\n" + "="*80)
    print("LAW FIRM DISTRIBUTION ANALYSIS")
    print("="*80)
    
    print(f"\nTop 20 Law Firms by Frequency:")
    for law_firm, count in law_firm_counts.most_common(20):
        print(f"{count:>4} - {law_firm}")
    
    print("\n" + "="*80)
    print("ATTORNEY -> LAW FIRM MAPPING ISSUES")
    print("="*80)
    
    # Show attorneys with multiple law firms (potential data quality issues)
    print("\nAttorneys with multiple law firm associations:")
    for attorney, law_firms in attorney_law_firms.items():
        if len(law_firms) > 1:
            print(f"\n{attorney} ({attorney_counts[attorney]} filings):")
            for firm in sorted(law_firms):
                print(f"  - {firm}")
    
    # Show attorneys with problematic law firm names
    print("\n" + "="*80)
    print("ATTORNEYS WITH PROBLEMATIC LAW FIRM DATA")
    print("="*80)
    
    problematic_patterns = [
        "Counsel Not Admitted",
        "Not Admitted",
        "USDC",
        "Bar"
    ]
    
    for attorney, law_firms in attorney_law_firms.items():
        for firm in law_firms:
            if any(pattern.lower() in firm.lower() for pattern in problematic_patterns):
                print(f"{attorney} ({attorney_counts[attorney]} filings): {firm}")
                break
    
    # Save detailed mapping for correction
    output_data = {}
    for attorney in attorney_counts:
        output_data[attorney] = {
            "filing_count": attorney_counts[attorney],
            "law_firms": list(attorney_law_firms.get(attorney, [])),
            "needs_correction": any(
                any(pattern.lower() in firm.lower() for pattern in problematic_patterns)
                for firm in attorney_law_firms.get(attorney, [])
            )
        }
    
    output_file = "/Users/<USER>/PycharmProjects/lexgenius/attorney_law_firm_analysis.json"
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\n\nDetailed analysis saved to: {output_file}")
    
    # Summary statistics
    total_attorneys = len(attorney_counts)
    attorneys_with_multiple_firms = len([a for a, firms in attorney_law_firms.items() if len(firms) > 1])
    attorneys_needing_correction = len([a for a in output_data if output_data[a]["needs_correction"]])
    
    print(f"\nSUMMARY:")
    print(f"Total unique attorneys: {total_attorneys}")
    print(f"Attorneys with multiple law firms: {attorneys_with_multiple_firms}")
    print(f"Attorneys needing law firm correction: {attorneys_needing_correction}")
    print(f"Unique law firms found: {len(law_firm_counts)}")

if __name__ == "__main__":
    db_path = "/Users/<USER>/PycharmProjects/lexgenius/sqlite/pacermon_cache.db"
    analyze_attorney_law_firms(db_path)