#!/usr/bin/env python3
"""
Simple debug script to test Facebook ads retrieval for a specific page ID.
This script helps diagnose why some pages show zero ads when they should have valid ads.
"""

import asyncio
import json
import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'debug_fb_ads_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# Add project root to path
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import yaml
from src.services.fb_ads.api_client import FacebookAPIClient
from src.services.fb_ads.session_manager import FacebookSessionManager
from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
from src.services.fb_ads.graphql_response_parser import GraphQLResponseParser


async def test_page_ads(page_id: str, use_camoufox: bool = False):
    """Test ads retrieval for a specific page ID."""
    logger.info(f"=" * 80)
    logger.info(f"Testing page ID: {page_id}")
    logger.info(f"Using {'Camoufox' if use_camoufox else 'Standard'} session manager")
    logger.info(f"=" * 80)
    
    # Load config from YAML file
    config_path = "config/workflows/fb_ads.yml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Ensure proxy is enabled
    config['use_proxy'] = True
    config['mobile_proxy'] = True
    
    # Add required fields if missing
    if 'aws_region' not in config:
        config['aws_region'] = os.getenv('AWS_REGION', 'us-west-2')
    if 'iso_date' not in config:
        config['iso_date'] = datetime.now().strftime("%Y%m%d")
    
    # Calculate date range (14 days)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=14)
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    
    logger.info(f"Date range: {start_date_str} to {end_date_str}")
    
    session_manager = None
    api_client = None
    
    try:
        # Create session manager
        if use_camoufox:
            logger.info("Creating Camoufox session manager...")
            session_manager = CamoufoxSessionManager(config, logger)
        else:
            logger.info("Creating standard session manager...")
            session_manager = FacebookSessionManager(config, logger)
        
        # Create session
        success = await session_manager.create_new_session()
        if not success:
            logger.error("Failed to create session")
            return
        
        logger.info("Session created successfully!")
        
        # Create API client with correct parameter order
        logger.info("Creating API client...")
        api_client = FacebookAPIClient(session_manager, config, logger)
        
        # Test 1: Try to fetch ads for the page
        logger.info(f"\n{'='*60}")
        logger.info("TEST 1: Fetching ads for page...")
        logger.info(f"{'='*60}")
        
        # Use the fetch_ads_page method
        result = await api_client.fetch_ads_page(
            company_id=page_id,
            start_date=start_date_str,
            end_date=end_date_str,
            forward_cursor=None
        )
        
        if result:
            logger.info(f"Result type: {type(result)}")
            logger.info(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            # Save raw result
            with open(f'debug_page_{page_id}_result.json', 'w') as f:
                json.dump(result, f, indent=2)
            logger.info(f"Result saved to debug_page_{page_id}_result.json")
            
            # Parse response
            parser = GraphQLResponseParser(config, logger)
            parsed_data = parser.parse_response(result)
            
            if parsed_data:
                logger.info(f"\nParsed data:")
                logger.info(f"  - Number of ads: {len(parsed_data.get('ads', []))}")
                logger.info(f"  - Has more pages: {parsed_data.get('has_next_page', False)}")
                logger.info(f"  - Total count: {parsed_data.get('total_count', 'Unknown')}")
                
                ads = parsed_data.get('ads', [])
                if ads:
                    logger.info(f"\nFirst ad details:")
                    first_ad = ads[0]
                    logger.info(f"  - ID: {first_ad.get('ad_archive_id', 'Unknown')}")
                    logger.info(f"  - Page Name: {first_ad.get('page_name', 'Unknown')}")
                    logger.info(f"  - Start Date: {first_ad.get('start_date', 'Unknown')}")
                    logger.info(f"  - Status: {first_ad.get('is_active', 'Unknown')}")
                else:
                    logger.warning("No ads found in parsed response!")
            else:
                logger.error("Failed to parse response")
        else:
            logger.error("No result returned from fetch_ads_page")
        
        # Test 2: Try searching for the company by name
        logger.info(f"\n{'='*60}")
        logger.info("TEST 2: Searching for company...")
        logger.info(f"{'='*60}")
        
        # Search for "Morgan & Morgan" or use page ID as search term
        search_result = await api_client.search_company_by_name("Morgan & Morgan")
        
        if search_result:
            logger.info(f"Search result type: {type(search_result)}")
            if isinstance(search_result, list):
                logger.info(f"Found {len(search_result)} companies")
                for company in search_result[:5]:  # Show first 5
                    logger.info(f"  - {company.get('name', 'Unknown')} (ID: {company.get('id', 'Unknown')})")
            else:
                logger.info(f"Search result: {search_result}")
        else:
            logger.error("No search results")
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)
    finally:
        # Cleanup
        if session_manager:
            await session_manager.cleanup()
            logger.info("Session manager cleaned up")


async def main():
    """Main function to run the debug script."""
    # Get page ID from command line or use default
    if len(sys.argv) > 1:
        page_id = sys.argv[1]
    else:
        # Default test page - use the correct Morgan & Morgan ID
        page_id = "313942829424"  # Morgan & Morgan (verified via search)
        logger.info(f"No page ID provided, using default Morgan & Morgan: {page_id}")
    
    # First test with standard session manager to show the issue
    logger.info("\n" + "="*80)
    logger.info("TEST 1: Standard Session Manager (Expected to fail)")
    logger.info("="*80)
    try:
        await test_page_ads(page_id, use_camoufox=False)
    except Exception as e:
        logger.error(f"Standard session manager test failed: {e}")
    
    # Then test with Camoufox to show the solution
    logger.info("\n" + "="*80)
    logger.info("TEST 2: Camoufox Session Manager (Should work)")
    logger.info("="*80)
    try:
        await test_page_ads(page_id, use_camoufox=True)
    except Exception as e:
        logger.error(f"Camoufox session manager test failed: {e}")
    
    logger.info("\n" + "="*80)
    logger.info("Debug script completed")
    logger.info("="*80)


if __name__ == "__main__":
    asyncio.run(main())