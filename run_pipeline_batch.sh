#!/usr/bin/env bash

# Signal handling for graceful shutdown
cleanup() {
    echo ""
    echo "⚠️  Batch processing interrupted. Cleaning up..."
    if [ ! -z "$CURRENT_PID" ]; then
        echo "Terminating current process (PID: $CURRENT_PID)..."
        kill -TERM "$CURRENT_PID" 2>/dev/null
        # Wait up to 10 seconds for graceful shutdown
        for i in {1..10}; do
            if ! kill -0 "$CURRENT_PID" 2>/dev/null; then
                echo "Process terminated gracefully"
                break
            fi
            sleep 1
        done
        # Force kill if still running
        if kill -0 "$CURRENT_PID" 2>/dev/null; then
            echo "Force killing process..."
            kill -KILL "$CURRENT_PID" 2>/dev/null
        fi
    fi
    echo "Batch processing cleanup completed"
    exit 130
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM
CURRENT_PID=""

# Function to display usage
usage() {
    echo "Usage: $0 --start-date MM/DD/YY --end-date MM/DD/YY --config <config_name>"
    echo "Example: $0 --start-date 01/01/25 --end-date 01/05/25 --config transform_batch"
    exit 1
}

# Parse arguments
START_DATE=""
END_DATE=""
CONFIG_NAME=""

while [[ "$#" -gt 0 ]]; do
    case $1 in
        --start-date) START_DATE="$2"; shift ;;
        --end-date) END_DATE="$2"; shift ;;
        --config) CONFIG_NAME="$2"; shift ;;
        *) echo "Unknown parameter passed: $1"; usage ;;
    esac
    shift
done

# Validate arguments
if [ -z "$START_DATE" ] || [ -z "$END_DATE" ] || [ -z "$CONFIG_NAME" ]; then
    usage
fi

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$SCRIPT_DIR" # Assuming run_pipeline_batch.sh is in the project root

# Add .yml extension if not present
if [[ "$CONFIG_NAME" != *.yml ]]; then
    CONFIG_FILE="${CONFIG_NAME}.yml"
else
    CONFIG_FILE="$CONFIG_NAME"
fi

CONFIG_PATH="$PROJECT_ROOT/config/$CONFIG_FILE"
MAIN_SCRIPT_PATH="$PROJECT_ROOT/src/main.py"

# Check if config file exists
if [ ! -f "$CONFIG_PATH" ]; then
    echo "Error: Config file not found at $CONFIG_PATH"
    exit 1
fi

# Check if main.py exists
if [ ! -f "$MAIN_SCRIPT_PATH" ]; then
    echo "Error: main.py not found at $MAIN_SCRIPT_PATH"
    exit 1
fi

# Convert dates to seconds since epoch for iteration
# Parse MM/DD/YY format
START_MONTH=$(echo $START_DATE | cut -d'/' -f1)
START_DAY=$(echo $START_DATE | cut -d'/' -f2)
START_YEAR=$(echo $START_DATE | cut -d'/' -f3)
# Assume 20XX for year
START_FULL_YEAR="20$START_YEAR"

END_MONTH=$(echo $END_DATE | cut -d'/' -f1)
END_DAY=$(echo $END_DATE | cut -d'/' -f2)
END_YEAR=$(echo $END_DATE | cut -d'/' -f3)
# Assume 20XX for year
END_FULL_YEAR="20$END_YEAR"

# Convert to seconds since epoch (compatible with macOS and Linux)
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    START_EPOCH=$(date -jf "%Y-%m-%d" "$START_FULL_YEAR-$START_MONTH-$START_DAY" +%s)
    END_EPOCH=$(date -jf "%Y-%m-%d" "$END_FULL_YEAR-$END_MONTH-$END_DAY" +%s)
else
    # Linux
    START_EPOCH=$(date -d "$START_FULL_YEAR-$START_MONTH-$START_DAY" +%s)
    END_EPOCH=$(date -d "$END_FULL_YEAR-$END_MONTH-$END_DAY" +%s)
fi

# Validate date range
if [ $START_EPOCH -gt $END_EPOCH ]; then
    echo "Error: Start date must be before or equal to end date"
    exit 1
fi

# Create temporary directory for modified configs
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

echo "Processing dates from $START_DATE to $END_DATE"
echo "Using config: $CONFIG_FILE"
echo ""

# Iterate through each day
CURRENT_EPOCH=$START_EPOCH
while [ $CURRENT_EPOCH -le $END_EPOCH ]; do
    # Convert epoch back to date
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        CURRENT_DATE=$(date -r $CURRENT_EPOCH "+%m/%d/%y")
    else
        # Linux
        CURRENT_DATE=$(date -d "@$CURRENT_EPOCH" "+%m/%d/%y")
    fi
    
    echo "Processing date: $CURRENT_DATE"
    
    # Create a temporary config file with the updated date
    TEMP_CONFIG="$TEMP_DIR/config_${CURRENT_DATE//\//_}.yml"
    
    # Export the date as an environment variable
    export BATCH_DATE="$CURRENT_DATE"
    
    # Use envsubst if available, otherwise use sed
    if command -v envsubst &> /dev/null; then
        # Use envsubst to replace environment variables
        envsubst < "$CONFIG_PATH" > "$TEMP_CONFIG"
    else
        # Fallback to sed - escape the dollar sign properly
        sed "s/\\\${BATCH_DATE}/$CURRENT_DATE/g" "$CONFIG_PATH" > "$TEMP_CONFIG"
    fi
    
    # Debug: Show the date line from the temp config
    echo "Config date line: $(grep "^date:" "$TEMP_CONFIG")"
    
    # Run the pipeline with the temporary config
    echo "Running: python3 $MAIN_SCRIPT_PATH --params $TEMP_CONFIG"
    
    # Run Python process in background to capture PID
    python3 "$MAIN_SCRIPT_PATH" --params "$TEMP_CONFIG" &
    CURRENT_PID=$!
    
    # Wait for the process to complete
    wait $CURRENT_PID
    EXIT_CODE=$?
    CURRENT_PID=""
    
    # Check the exit code
    if [ $EXIT_CODE -ne 0 ]; then
        if [ $EXIT_CODE -eq 130 ]; then
            echo "Batch processing was interrupted on date: $CURRENT_DATE"
            exit 130
        else
            echo "Error processing date: $CURRENT_DATE (exit code: $EXIT_CODE)"
            # Uncomment the next line to stop on first error
            # exit 1
        fi
    else
        echo "Successfully processed date: $CURRENT_DATE"
    fi
    
    echo ""
    
    # Increment by one day (86400 seconds)
    CURRENT_EPOCH=$((CURRENT_EPOCH + 86400))
done

echo "Batch processing completed."