#!/usr/bin/env python3
"""
Debug the projection expression issue for MDL calculations.
"""

import asyncio
import os
import sys
from datetime import datetime

from dotenv import load_dotenv
from rich.console import Console

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage

# Load environment variables
load_dotenv()

console = Console()


async def test_projections():
    """Test different projection expressions."""
    
    # Initialize storage
    class StorageConfig:
        def __init__(self):
            self.use_local = False
            self.local_port = 8000
            self.dynamodb_endpoint = None
            self.aws_region = 'us-west-2'
            self.dynamodb_max_retries = 10
            self.dynamodb_base_delay = 1.0
            self.dynamodb_max_delay = 60.0
    
    config = StorageConfig()
    storage = AsyncDynamoDBStorage(config)
    repository = PacerRepository(storage)
    
    test_date = '20250620'
    
    async with storage:
        console.print("[bold blue]Testing projection expressions[/bold blue]\n")
        
        # Test 1: With projection
        console.print("[yellow]Test 1: With projection expression[/yellow]")
        records_with_projection = await repository.query_by_filing_date(
            test_date,
            projection_expression='MdlNum, Versus, NumPlaintiffs, CourtId, DocketNum, TransferorCourtId, TransferorDocketNum'
        )
        
        # Filter for MDL 3092
        mdl_3092_proj = [r for r in records_with_projection if r.get('mdl_num') == '3092']
        console.print(f"Found {len(mdl_3092_proj)} MDL 3092 records")
        
        if mdl_3092_proj:
            sample = mdl_3092_proj[0]
            console.print("Sample record fields:", list(sample.keys()))
            console.print(f"Sample versus: {sample.get('versus', 'MISSING')}")
            console.print(f"Sample num_plaintiffs: {sample.get('num_plaintiffs', 'MISSING')}")
        
        # Test 2: Without projection
        console.print("\n[yellow]Test 2: Without projection (all fields)[/yellow]")
        records_all_fields = await repository.query_by_filing_date(test_date)
        
        # Filter for MDL 3092
        mdl_3092_all = [r for r in records_all_fields if r.get('mdl_num') == '3092']
        console.print(f"Found {len(mdl_3092_all)} MDL 3092 records")
        
        if mdl_3092_all:
            sample = mdl_3092_all[0]
            console.print("Sample record fields:", list(sample.keys()))
            console.print(f"Sample versus: {sample.get('versus', 'MISSING')}")
            console.print(f"Sample num_plaintiffs: {sample.get('num_plaintiffs', 'MISSING')}")
        
        # Compare results
        console.print("\n[bold green]Comparison:[/bold green]")
        console.print(f"Records with projection: {len(mdl_3092_proj)}")
        console.print(f"Records without projection: {len(mdl_3092_all)}")
        
        # Check for missing fields
        if mdl_3092_proj and mdl_3092_all:
            proj_fields = set(mdl_3092_proj[0].keys())
            all_fields = set(mdl_3092_all[0].keys())
            missing_in_projection = all_fields - proj_fields
            console.print(f"\nFields missing in projection: {missing_in_projection}")


async def main():
    """Main entry point."""
    await test_projections()


if __name__ == "__main__":
    asyncio.run(main())