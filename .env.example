# LexGenius Environment Configuration
# Copy this file to .env and update with your values

# Environment settings
LEXGENIUS_ENVIRONMENT=development
LEXGENIUS_LOG_LEVEL=INFO
LEXGENIUS_DRY_RUN=false

# Data directory (optional, defaults to ./data)
LEXGENIUS_DATA_DIR=data

# AWS Configuration
LEXGENIUS_AWS_REGION=us-west-2
LEXGENIUS_AWS_ACCESS_KEY_ID=your-access-key-here
LEXGENIUS_AWS_SECRET_ACCESS_KEY=your-secret-key-here
LEXGENIUS_S3_BUCKET_NAME=your-bucket-name

# DynamoDB Configuration
# To use local DynamoDB, set USE_LOCAL_DYNAMODB=true
# This prevents accidental use of local DynamoDB when DYNAMODB_ENDPOINT_URL is set in environment
USE_LOCAL_DYNAMODB=false
# Local DynamoDB endpoint (used by sync scripts and when USE_LOCAL_DYNAMODB=true)
LOCAL_DYNAMODB_ENDPOINT_URL=http://localhost:8000
# AWS DynamoDB endpoint (leave unset to use default AWS endpoint)
# DYNAMODB_ENDPOINT_URL=
DYNAMODB_TABLE_PREFIX=

# Email Configuration
LEXGENIUS_EMAIL_SMTP_HOST=smtp.gmail.com
LEXGENIUS_EMAIL_SMTP_PORT=587
LEXGENIUS_EMAIL_SMTP_USERNAME=<EMAIL>
LEXGENIUS_EMAIL_SMTP_PASSWORD=your-app-password
LEXGENIUS_EMAIL_FROM_EMAIL=<EMAIL>
LEXGENIUS_EMAIL_FROM_NAME=LexGenius Reports

# Feature Flags for Week 3 Migration
# Week 3 Day 1: FB Ads async conversion only (conservative start)
LEXGENIUS_USE_DIRECT_ASYNC_REPOS=true
LEXGENIUS_ENABLE_FB_ADS_ASYNC=true
LEXGENIUS_ENABLE_REPORTS_ASYNC=false
LEXGENIUS_ENABLE_PACER_ASYNC=false
LEXGENIUS_ENABLE_DATA_TRANSFORMER_ASYNC=false

# Safety Net Configuration (critical for production stability)
LEXGENIUS_FALLBACK_TO_COMPATIBILITY=true
LEXGENIUS_AUTO_FALLBACK_ON_ERROR=true
LEXGENIUS_FALLBACK_TIMEOUT_SECONDS=30

# Performance Monitoring (essential for validation)
LEXGENIUS_LOG_PERFORMANCE_METRICS=true
LEXGENIUS_COMPARE_OLD_VS_NEW=false
LEXGENIUS_PERFORMANCE_THRESHOLD_MS=5000

# === Concurrent Processing Configuration ===
# Enable concurrent processing for improved performance (Phase 5)
ENABLE_CONCURRENT_PROCESSING=false
MAX_CONCURRENT_FIRMS=3
CONCURRENT_BATCH_SIZE=5
SESSION_POOL_SIZE=2

# Performance Optimization Settings
DEFAULT_DATE_RANGE_DAYS=14
SESSION_REFRESH_INTERVAL=5
SHUFFLE_FIRM_ORDER=true
ROTATE_PROXY_BETWEEN_FIRMS=true

# Development & Testing Flags
LEXGENIUS_DEBUG_ASYNC_CONVERSION=false
LEXGENIUS_FORCE_COMPATIBILITY_MODE=false
LEXGENIUS_DRY_RUN_ASYNC_MIGRATION=false

# Rollout Control
LEXGENIUS_ASYNC_ROLLOUT_PERCENTAGE=100

# Legacy Feature Flags (for backward compatibility)
LEXGENIUS_FEATURE_USE_ASYNC_STORAGE=false
LEXGENIUS_FEATURE_USE_HYBRID_CLASSIFIER=true
LEXGENIUS_FEATURE_ENABLE_OCR_PROCESSING=true
LEXGENIUS_FEATURE_ENABLE_DEFERRED_PROCESSING=true
LEXGENIUS_FEATURE_ENABLE_CACHE=true

# Migration Timeline Comments:
# Week 3 Day 1: Enable FB_ADS_ASYNC only
# Week 3 Day 2: Also enable REPORTS_ASYNC  
# Week 3 Day 3: Also enable PACER_ASYNC
# Week 3 Day 4: Also enable DATA_TRANSFORMER_ASYNC
# Week 3 Day 5: Full async mode with performance comparison

# API Keys
OPENAI_API_KEY=sk-...
DEEPSEEK_API_KEY=...
MISTRAL_API_KEY=...

# PACER Credentials
PACER_USERNAME=...
PACER_PASSWORD=...

# Facebook Ad Library Credentials (if needed)
FB_APP_ID=...
FB_APP_SECRET=...