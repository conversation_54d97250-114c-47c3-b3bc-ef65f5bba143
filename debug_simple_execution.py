#!/usr/bin/env python3
"""
Simple debug script to check if transfer handler is being called in actual execution.
"""
import json
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_mdl_extraction():
    """Test MDL extraction from lead_case field."""
    
    # Load the test data
    json_path = "/Users/<USER>/PycharmProjects/lexgenius/data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
    with open(json_path, 'r') as f:
        test_data = json.load(f)
    
    print("=" * 80)
    print("Debug Simple Execution")
    print("=" * 80)
    
    print(f"Original data mdl_num: {test_data.get('mdl_num', 'NOT PRESENT')}")
    print(f"Lead case: {test_data.get('lead_case')}")
    print(f"Case in other court: {test_data.get('case_in_other_court')}")
    
    # Test MDL extraction from lead_case
    import re
    lead_case = test_data.get('lead_case', '')
    if lead_case:
        # Pattern for MDL court cases: X:XX-mn-XXXXX or X:XX-md-XXXXX
        lead_case_match = re.search(r'\d+:\d+-m[nd]-(\d+)', lead_case, re.IGNORECASE)
        if lead_case_match:
            mdl_num = lead_case_match.group(1).lstrip('0')
            print(f"✅ MDL extraction test: {mdl_num}")
        else:
            print(f"❌ MDL extraction failed")
    
    # Test case_in_other_court parsing
    case_in_other_court = test_data.get('case_in_other_court')
    if case_in_other_court:
        parts = case_in_other_court.split(',', 1)
        if len(parts) == 2:
            transferor_name = parts[0].strip()
            transferor_docket = parts[1].strip()
            print(f"✅ Transfer parsing test: '{transferor_name}' -> '{transferor_docket}'")
        else:
            print(f"❌ Transfer parsing failed")
    
    # Check what the transformation process would expect
    print("\n" + "-" * 80)
    print("Expected transformation results:")
    print("-" * 80)
    print("1. MDL should be extracted from lead_case")
    print("2. Transfer handler should be called in data_processing_engine")
    print("3. Transfer flags should be set based on parsed data")
    
    # Let's check if there's a logs directory to see what actually happened
    logs_dir = Path("/Users/<USER>/PycharmProjects/lexgenius/logs")
    if logs_dir.exists():
        print(f"\nLogs directory exists: {logs_dir}")
        log_files = list(logs_dir.glob("*.log"))
        if log_files:
            print(f"Log files found: {[f.name for f in log_files]}")
        else:
            print("No log files found")
    else:
        print("No logs directory found")

if __name__ == "__main__":
    test_mdl_extraction()