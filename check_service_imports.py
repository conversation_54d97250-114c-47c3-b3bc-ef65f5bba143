#!/usr/bin/env python3
"""
Script to check all service files for import and syntax errors.
"""

import os
import sys
import importlib.util
from pathlib import Path

# Add src to path
sys.path.insert(0, '/Users/<USER>/PycharmProjects/lexgenius/src')

def check_file_syntax(file_path):
    """Check if a Python file has syntax errors"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        compile(source, file_path, 'exec')
        return True, None
    except SyntaxError as e:
        return False, f"SyntaxError: {e}"
    except IndentationError as e:
        return False, f"IndentationError: {e}"
    except Exception as e:
        return False, f"Error: {e}"

def check_file_imports(file_path):
    """Check if a Python file can be imported"""
    try:
        spec = importlib.util.spec_from_file_location("module", file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return True, None
    except Exception as e:
        return False, f"ImportError: {e}"

def main():
    service_dirs = [
        'services/pacer',
        'services/transformer', 
        'services/ai',
        'services/reports',
        'services/fb_ads',
        'services/orchestration',
        'services/html',
        'services/document',
        'services/uploader'
    ]
    
    base_path = Path('/Users/<USER>/PycharmProjects/lexgenius/src')
    
    all_errors = []
    checked_files = 0
    
    print("Checking service files for syntax and import errors...")
    print("=" * 60)
    
    for service_dir in service_dirs:
        full_path = base_path / service_dir
        if not full_path.exists():
            continue
            
        print(f"\nChecking {service_dir}:")
        print("-" * 40)
        
        for py_file in full_path.glob('*.py'):
            if py_file.name == '__init__.py':
                continue
                
            checked_files += 1
            rel_path = py_file.relative_to(base_path)
            
            # Check syntax first
            syntax_ok, syntax_error = check_file_syntax(py_file)
            if not syntax_ok:
                error_msg = f"{rel_path}: {syntax_error}"
                all_errors.append(error_msg)
                print(f"  ✗ {py_file.name}: {syntax_error}")
                continue
                
            # Check imports
            import_ok, import_error = check_file_imports(py_file)
            if not import_ok:
                error_msg = f"{rel_path}: {import_error}"
                all_errors.append(error_msg)
                print(f"  ✗ {py_file.name}: {import_error}")
                continue
                
            print(f"  ✓ {py_file.name}")
    
    print("\n" + "=" * 60)
    print(f"Summary: Checked {checked_files} files")
    
    if all_errors:
        print(f"Found {len(all_errors)} errors:")
        for error in all_errors:
            print(f"  - {error}")
    else:
        print("All files passed syntax and import checks!")

if __name__ == "__main__":
    main()