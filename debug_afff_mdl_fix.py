#!/usr/bin/env python3
"""
Debug script to find and fix AFFF records missing mdl_num = '2873'

This script will:
1. Find the 4 specific records that are missing MDL numbers
2. Update them with the correct mdl_num = '2873'
3. Normalize the AGC defendant name
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Confirm

# Load environment variables
load_dotenv()

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import services
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)
console = Console()

# Target records to find and fix
TARGET_RECORDS = [
    "Anita Powers v. 3M Company et al.",
    "City Of Salem, Virginia v. AGC CHEMICALS AMERICAS INC.",
    "Judith Mason v. 3M Company et al.",
    "Tony Setzer v. 3M Company et al."
]

class AFFFMDLFixer:
    """Fixes missing MDL numbers for AFFF records"""
    
    def __init__(self):
        self.console = console
        self.config = self._load_config()
        self.dynamodb_storage = None
        self.pacer_repo = None
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from environment."""
        return {
            'aws_region': os.environ.get('AWS_REGION', 'us-west-2'),
            'aws_access_key': os.environ.get('AWS_ACCESS_KEY_ID'),
            'aws_secret_key': os.environ.get('AWS_SECRET_ACCESS_KEY'),
        }
    
    async def initialize_services(self):
        """Initialize DynamoDB services"""
        try:
            # DynamoDB storage
            dynamodb_logger = logging.getLogger('dynamodb_storage')
            self.dynamodb_storage = AsyncDynamoDBStorage(
                config={
                    'aws_region': self.config['aws_region'],
                    'aws_access_key': self.config['aws_access_key'],
                    'aws_secret_key': self.config['aws_secret_key']
                },
                logger=dynamodb_logger
            )
            await self.dynamodb_storage.__aenter__()
            
            # Pacer repository
            self.pacer_repo = PacerRepository(self.dynamodb_storage)
            
            logger.info("✅ Services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            raise
    
    async def cleanup(self):
        """Clean up resources"""
        if self.dynamodb_storage:
            await self.dynamodb_storage.__aexit__(None, None, None)
    
    async def find_records_by_versus(self, target_date: str = "20250716") -> List[Dict[str, Any]]:
        """Find records for the target date and check for our problem records"""
        try:
            logger.info(f"Querying records for date: {target_date}")
            
            # Get all records for the target date
            all_records = await self.pacer_repo.query_by_filing_date(target_date)
            logger.info(f"Found {len(all_records)} total records for {target_date}")
            
            # Find our target records
            found_records = []
            missing_records = []
            
            for target_versus in TARGET_RECORDS:
                found = False
                for record in all_records:
                    record_versus = record.get('versus', '')
                    if record_versus and target_versus.lower() in record_versus.lower():
                        found_records.append({
                            'target': target_versus,
                            'record': record
                        })
                        found = True
                        break
                
                if not found:
                    missing_records.append(target_versus)
            
            # Display results
            if found_records:
                self.console.print(f"\n[green]✅ Found {len(found_records)} target records:[/green]")
                
                table = Table(title="Found Records")
                table.add_column("Target", style="cyan")
                table.add_column("Actual Versus", style="yellow")
                table.add_column("MDL Num", style="red")
                table.add_column("Docket Num", style="green")
                
                for item in found_records:
                    target = item['target']
                    record = item['record']
                    versus = record.get('versus', 'N/A')
                    mdl_num = record.get('mdl_num', 'MISSING')
                    docket_num = record.get('docket_num', 'N/A')
                    
                    table.add_row(target, versus, str(mdl_num), docket_num)
                
                self.console.print(table)
            
            if missing_records:
                self.console.print(f"\n[red]❌ Missing records:[/red]")
                for missing in missing_records:
                    self.console.print(f"  - {missing}")
            
            return found_records
            
        except Exception as e:
            logger.error(f"Error finding records: {e}")
            return []
    
    async def check_mdl_numbers(self, found_records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Check which records need MDL number updates"""
        records_needing_update = []
        
        for item in found_records:
            record = item['record']
            mdl_num = record.get('mdl_num')
            
            if mdl_num != '2873':
                records_needing_update.append({
                    'target': item['target'],
                    'record': record,
                    'current_mdl': mdl_num,
                    'needs_update': True
                })
                logger.info(f"❌ Record needs update: {record.get('versus', 'N/A')} (current MDL: {mdl_num})")
            else:
                logger.info(f"✅ Record already correct: {record.get('versus', 'N/A')} (MDL: {mdl_num})")
        
        return records_needing_update
    
    def normalize_defendant_name(self, versus: str) -> str:
        """Normalize defendant names according to rules"""
        if not versus:
            return versus
        
        # Apply the specific normalization rule
        normalized = versus.replace(
            "AGC CHEMICALS OF AMERICAS INC.", 
            "AGC Chemicals of America Inc."
        ).replace(
            "AGC CHEMICALS AMERICAS INC.", 
            "AGC Chemicals of America Inc."
        )
        
        if normalized != versus:
            logger.info(f"📝 Normalized defendant: '{versus}' -> '{normalized}'")
        
        return normalized
    
    async def update_records(self, records_to_update: List[Dict[str, Any]]) -> bool:
        """Update records with correct MDL numbers and normalized names"""
        if not records_to_update:
            self.console.print("[green]✅ No records need updating[/green]")
            return True
        
        # Show preview of changes
        self.console.print(f"\n[bold yellow]📋 Records to Update:[/bold yellow]")
        
        table = Table(title="Planned Updates")
        table.add_column("Versus", style="cyan")
        table.add_column("Current MDL", style="red")
        table.add_column("New MDL", style="green")
        table.add_column("Normalized", style="yellow")
        
        for item in records_to_update:
            record = item['record']
            current_versus = record.get('versus', 'N/A')
            normalized_versus = self.normalize_defendant_name(current_versus)
            current_mdl = item['current_mdl']
            
            table.add_row(
                current_versus,
                str(current_mdl),
                "2873",
                "Yes" if normalized_versus != current_versus else "No"
            )
        
        self.console.print(table)
        
        # Get user confirmation
        if not Confirm.ask(f"\nProceed with updating {len(records_to_update)} records?"):
            self.console.print("[yellow]Update cancelled by user[/yellow]")
            return False
        
        # Perform updates
        success_count = 0
        fail_count = 0
        
        for item in records_to_update:
            record = item['record']
            filing_date = record.get('filing_date')
            docket_num = record.get('docket_num')
            
            if not filing_date or not docket_num:
                logger.error(f"Missing key fields for record: {record}")
                fail_count += 1
                continue
            
            try:
                # Prepare update data
                update_data = {
                    'mdl_num': '2873'
                }
                
                # Check if versus needs normalization
                current_versus = record.get('versus', '')
                normalized_versus = self.normalize_defendant_name(current_versus)
                if normalized_versus != current_versus:
                    update_data['versus'] = normalized_versus
                
                # Update the record
                success = await self.pacer_repo.update_transfer_info(
                    filing_date=filing_date,
                    docket_num=docket_num,
                    transfer_info=update_data
                )
                
                if success:
                    success_count += 1
                    logger.info(f"✅ Updated: {current_versus}")
                else:
                    fail_count += 1
                    logger.error(f"❌ Failed to update: {current_versus}")
                    
            except Exception as e:
                fail_count += 1
                logger.error(f"❌ Error updating record {docket_num}: {e}")
        
        # Summary
        self.console.print(f"\n[bold]📊 Update Summary:[/bold]")
        self.console.print(f"  ✅ Successful: {success_count}")
        self.console.print(f"  ❌ Failed: {fail_count}")
        
        return fail_count == 0
    
    async def verify_updates(self, target_date: str = "20250716") -> bool:
        """Verify that all records now have correct MDL numbers"""
        try:
            logger.info("🔍 Verifying updates...")
            
            # Re-query the records
            found_records = await self.find_records_by_versus(target_date)
            
            if not found_records:
                logger.warning("No records found for verification")
                return False
            
            # Check if all records now have correct MDL numbers
            all_correct = True
            for item in found_records:
                record = item['record']
                mdl_num = record.get('mdl_num')
                versus = record.get('versus', 'N/A')
                
                if mdl_num != '2873':
                    logger.error(f"❌ Still incorrect: {versus} (MDL: {mdl_num})")
                    all_correct = False
                else:
                    logger.info(f"✅ Verified correct: {versus} (MDL: {mdl_num})")
            
            if all_correct:
                self.console.print("[bold green]🎉 All records now have correct MDL numbers![/bold green]")
            else:
                self.console.print("[bold red]❌ Some records still have incorrect MDL numbers[/bold red]")
            
            return all_correct
            
        except Exception as e:
            logger.error(f"Error verifying updates: {e}")
            return False


async def main():
    """Main entry point"""
    console.print(Panel.fit(
        "[bold cyan]AFFF MDL Number Fixer[/bold cyan]\n"
        "Find and fix records missing mdl_num = '2873'",
        border_style="cyan"
    ))
    
    fixer = AFFFMDLFixer()
    
    try:
        await fixer.initialize_services()
        
        # Step 1: Find the target records
        console.print("\n[bold]Step 1: Finding target records...[/bold]")
        found_records = await fixer.find_records_by_versus()
        
        if not found_records:
            console.print("[red]❌ No target records found[/red]")
            return
        
        # Step 2: Check which ones need MDL updates
        console.print("\n[bold]Step 2: Checking MDL numbers...[/bold]")
        records_needing_update = await fixer.check_mdl_numbers(found_records)
        
        # Step 3: Update records if needed
        if records_needing_update:
            console.print("\n[bold]Step 3: Updating records...[/bold]")
            success = await fixer.update_records(records_needing_update)
            
            if success:
                # Step 4: Verify updates
                console.print("\n[bold]Step 4: Verifying updates...[/bold]")
                await fixer.verify_updates()
            else:
                console.print("[red]❌ Some updates failed[/red]")
        else:
            console.print("[green]✅ All records already have correct MDL numbers[/green]")
    
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        console.print(f"[bold red]❌ Fatal error: {e}[/bold red]")
    
    finally:
        await fixer.cleanup()


if __name__ == "__main__":
    asyncio.run(main())