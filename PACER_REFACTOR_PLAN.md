# PACER Service Refactoring Plan (Revised Granular Approach)

This document outlines a detailed, incremental plan to refactor the `src/pacer` module into a modular architecture using five focused facades. This approach ensures that both facades and their internal components remain small, maintainable, and aligned with the project's goal of simplification.

**Guiding Principles:**
- **Focused Facades:** Each facade will have a single, clear responsibility (e.g., Browser, Processing, Download).
- **Small Components:** Implementation logic will be broken down into small, single-responsibility components, aiming for under 300 lines of code each.
- **Incremental Refactoring:** The plan is broken into phases. Each phase introduces one new facade and its components, followed by DI updates and testing to ensure stability at every step.
- **Safety and Verifiability:** Each step is an atomic, verifiable action.

---

### **Final Directory Structure**

After the refactoring is complete, the `src/pacer/` directory will have the following structure:

```
src/pacer/
├── __init__.py
├── _browser_components/
│   ├── __init__.py
│   ├── authentication_handler.py
│   ├── browser_manager.py
│   └── navigation_handler.py
├── _download_components/
│   ├── __init__.py
│   ├── download_coordinator.py
│   └── download_policy.py
├── _file_components/
│   ├── __init__.py
│   ├── file_manager.py
│   └── file_operations.py
├── _processing_components/
│   ├── __init__.py
│   ├── case_classifier.py
│   ├── case_enricher.py
│   ├── html_processor.py
│   └── transfer_handler.py
├── _verification_components/
│   ├── __init__.py
│   ├── existence_checker.py
│   └── relevance_engine.py
├── jobs/
│   ├── __init__.py
│   ├── job_orchestration_service.py
│   ├── job_runner_service.py
│   └── jobs_models.py
├── analytics_service.py
├── configuration_service.py
├── export_service.py
├── interactive_service.py
├── pacer_browser_service.py
├── pacer_download_service.py
├── pacer_file_service.py
├── pacer_processing_service.py
├── pacer_verification_service.py
├── query_service.py
└── service_factory.py
```

---

### **Phase 0: Environment Setup**

**Goal:** Create a dedicated, isolated environment for the refactoring work.

**Prompt for Coder:**
```
Create a new git worktree named `refactor/pacer-facade` from the main branch. This will be our isolated workspace.

1. `git worktree add -b refactor/pacer-facade ./refactor/pacer-facade main`
2. `cd refactor/pacer-facade`
3. `git mv src/services/pacer src/pacer`
4. Create the new directory structure for all facades and components at once to avoid repetitive `mkdir` commands. Also create the `__init__.py` files.
   - `mkdir -p src/pacer/_browser_components`
   - `mkdir -p src/pacer/_processing_components`
   - `mkdir -p src/pacer/_download_components`
   - `mkdir -p src/pacer/_verification_components`
   - `mkdir -p src/pacer/_file_components`
   - `touch src/pacer/_browser_components/__init__.py`
   - `touch src/pacer/_processing_components/__init__.py`
   - `touch src/pacer/_download_components/__init__.py`
   - `touch src/pacer/_verification_components/__init__.py`
   - `touch src/pacer/_file_components/__init__.py`
```

---

### **Phase 1: Refactor `PacerBrowserService`**

**Goal:** Consolidate browser lifecycle, authentication, and navigation logic.

**Prompt for Coder:**

**Step 1.1: Move and Rename Browser Components**
1.  `git mv src/pacer/browser/browser_service.py src/pacer/_browser_components/browser_manager.py`
    -   In `browser_manager.py`, rename class `BrowserService` to `BrowserManager`.
2.  `git mv src/pacer/authentication_service.py src/pacer/_browser_components/authentication_handler.py`
    -   In `authentication_handler.py`, rename class `PacerAuthenticationService` to `AuthenticationHandler`.
3.  `git mv src/pacer/browser/navigator.py src/pacer/_browser_components/navigation_handler.py`
    -   In `navigation_handler.py`, rename class `PacerNavigator` to `NavigationHandler`.
    -   Merge the content of `src/pacer/navigation_service.py` into `navigation_handler.py`. The `NavigationHandler` class should now contain the methods from both original classes.

**Step 1.2: Implement the `PacerBrowserService` Facade**
1.  Create `src/pacer/pacer_browser_service.py`.
2.  Implement the `PacerBrowserService` class to instantiate and delegate to its components.

**Step 1.3: Update DI Container and Tests**
1.  **DI Container:** Comment out registrations for `PacerAuthenticationService`, `BrowserService`, `PacerNavigator`, `PacerNavigationService`. Register `PacerBrowserService`.
2.  **Tests:** Move and adapt old tests to `tests/pacer/_browser_components/`. Create `test_pacer_browser_service.py` to test facade delegation.
3.  **Verification:** Run the test suite.

---

### **Phase 2: Refactor `PacerFileService`**

**Goal:** Consolidate all direct file system and S3 I/O operations.

**Prompt for Coder:**

**Step 2.1: Move and Rename File Components**
1.  `git mv src/pacer/file_management_service.py src/pacer/_file_components/file_manager.py`
    -   In `file_manager.py`, rename `PacerFileManagementService` to `FileManager`.
2.  `git mv src/pacer/file_operations_service.py src/pacer/_file_components/file_operations.py`
    -   In `file_operations.py`, rename `PacerFileOperationsService` to `FileOperations`.

**Step 2.2: Implement the `PacerFileService` Facade**
1.  Create `src/pacer/pacer_file_service.py`.
2.  Implement the `PacerFileService` class to instantiate and delegate to `FileManager` and `FileOperations`.

**Step 2.3: Update DI Container and Tests**
1.  **DI Container:** Comment out `PacerFileManagementService` and `PacerFileOperationsService`. Register `PacerFileService`.
2.  **Tests:** Move and adapt tests to `tests/pacer/_file_components/`. Create `test_pacer_file_service.py`.
3.  **Verification:** Run the test suite.

---

### **Phase 3: Refactor `PacerVerificationService`**

**Goal:** Consolidate case relevance and existence-checking logic.

**Prompt for Coder:**

**Step 3.1: Move and Rename Verification Components**
1.  `git mv src/pacer/relevance_service.py src/pacer/_verification_components/relevance_engine.py`
    -   In `relevance_engine.py`, rename `RelevanceService` to `RelevanceEngine`.
2.  `git mv src/pacer/case_verification_service.py src/pacer/_verification_components/existence_checker.py`
    -   In `existence_checker.py`, rename `PacerCaseVerificationService` to `ExistenceChecker`.

**Step 3.2: Implement the `PacerVerificationService` Facade**
1.  Create `src/pacer/pacer_verification_service.py`.
2.  Implement the `PacerVerificationService` facade to orchestrate `RelevanceEngine` and `ExistenceChecker`.

**Step 3.3: Update DI Container and Tests**
1.  **DI Container:** Comment out `RelevanceService` and `PacerCaseVerificationService`. Register `PacerVerificationService`.
2.  **Tests:** Move and adapt tests to `tests/pacer/_verification_components/`. Create `test_pacer_verification_service.py`.
3.  **Verification:** Run the test suite.

---

### **Phase 4: Refactor `PacerDownloadService`**

**Goal:** Consolidate the document download workflow.

**Prompt for Coder:**

**Step 4.1: Move and Rename Download Components**
1.  `git mv src/pacer/download_orchestration_service.py src/pacer/_download_components/download_coordinator.py`
    -   In `download_coordinator.py`, rename `PacerDownloadOrchestrationService` to `DownloadCoordinator`.
2.  `git mv src/pacer/ignore_download_service.py src/pacer/_download_components/download_policy.py`
    -   In `download_policy.py`, rename `PacerIgnoreDownloadService` to `DownloadPolicy`.

**Step 4.2: Implement the `PacerDownloadService` Facade**
1.  Create `src/pacer/pacer_download_service.py`.
2.  Implement the `PacerDownloadService` facade to orchestrate `DownloadCoordinator` and `DownloadPolicy`.

**Step 4.3: Update DI Container and Tests**
1.  **DI Container:** Comment out `PacerDownloadOrchestrationService` and `PacerIgnoreDownloadService`. Register `PacerDownloadService`.
2.  **Tests:** Move and adapt tests to `tests/pacer/_download_components/`. Create `test_pacer_download_service.py`.
3.  **Verification:** Run the test suite.

---

### **Phase 5: Refactor `PacerProcessingService`**

**Goal:** Consolidate core business logic for transforming raw data into a processed case.

**Prompt for Coder:**

**Step 5.1: Move and Rename Processing Components**
1.  `git mv src/pacer/html_processing_service.py src/pacer/_processing_components/html_processor.py`
    -   In `html_processor.py`, rename `PacerHTMLProcessingService` to `HtmlProcessor`.
2.  `git mv src/pacer/case_classification_service.py src/pacer/_processing_components/case_classifier.py`
    -   In `case_classifier.py`, rename `PacerCaseClassificationService` to `CaseClassifier`.
3.  `git mv src/pacer/case_processing_service.py src/pacer/_processing_components/case_enricher.py`
    -   In `case_enricher.py`, rename `PacerCaseProcessingService` to `CaseEnricher`.
4.  `git mv src/pacer/transfer_service.py src/pacer/_processing_components/transfer_handler.py`
    -   In `transfer_handler.py`, rename `PacerTransferService` to `TransferHandler`.

**Step 5.2: Implement the `PacerProcessingService` Facade**
1.  Create `src/pacer/pacer_processing_service.py`.
2.  Implement the `PacerProcessingService` facade to orchestrate the four new components.

**Step 5.3: Update DI Container and Tests**
1.  **DI Container:** Comment out the four old services. Register `PacerProcessingService`.
2.  **Tests:** Move and adapt tests to `tests/pacer/_processing_components/`. Create `test_pacer_processing_service.py`.
3.  **Verification:** Run the test suite.

---

### **Phase 6: Final Cleanup**

**Goal:** Remove all obsolete files and finalize the refactoring.

**Prompt for Coder:**

**Step 6.1: Full Integration Test**
Run a new end-to-end integration test that uses all five new facades (injected via the DI container) to process a case from start to finish.

**Step 6.2: Remove Old Files and Directories**
1.  Delete all remaining old service files from `src/pacer/` (e.g., `row_processing_service.py`, `pacer_orchestrator_service.py`, etc.).
2.  `git rm -r src/pacer/browser`

**Step 6.3: Update Package Exports**
Update `src/pacer/__init__.py` to export only the public facades and any remaining standalone services.

```python
# src/pacer/__init__.py
"""PACER Services Module - Facade Architecture"""

from .pacer_browser_service import PacerBrowserService
from .pacer_processing_service import PacerProcessingService
from .pacer_download_service import PacerDownloadService
from .pacer_verification_service import PacerVerificationService
from .pacer_file_service import PacerFileService
# Standalone services
from .analytics_service import PacerAnalyticsService
from .query_service import PacerQueryService
from .export_service import PacerExportService
from .configuration_service import PacerConfigurationService

__all__ = [
    "PacerBrowserService",
    "PacerProcessingService",
    "PacerDownloadService",
    "PacerVerificationService",
    "PacerFileService",
    "PacerAnalyticsService",
    "PacerQueryService",
    "PacerExportService",
    "PacerConfigurationService",
]
```

**Step 6.4: Final Verification**
Run the entire test suite one last time to confirm that nothing was broken during the cleanup phase.
```