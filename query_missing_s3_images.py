#!/usr/bin/env python3
"""
Script to query FB Archive records for last_updated dates 20250725-20250729
and return page_ids that don't have an S3_image_key.
"""
import asyncio
import logging
import sys
import os
from typing import List, Set
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.fb_archive_repository import FBArchiveRepository


async def get_page_ids_without_s3_image_key() -> List[str]:
    """
    Query FB Archive for records with last_updated between 20250725-20250729
    and return page_ids that don't have an S3_image_key.
    
    Returns:
        List of page_id strings
    """
    # Create real config object for DynamoDB storage (not test mock)
    class RealConfig:
        def __init__(self):
            self.use_local = False
            self.aws_region = 'us-west-2'  # Change to correct region where table exists
            self.dynamodb_max_retries = 15
            self.dynamodb_base_delay = 2.0
            self.dynamodb_max_delay = 120.0
    
    config = RealConfig()
    logger = logging.getLogger(__name__)
    
    # Initialize REAL storage and repository (not mock)
    async with AsyncDynamoDBStorage(config, logger=logger) as storage:
        repository = FBArchiveRepository(storage=storage)
        
        # Date range: 20250725 through 20250729
        start_date = "20250725"
        end_date = "20250729"
        
        print(f"Querying FB Archive for last_updated dates {start_date} to {end_date}")
        
        # Query records by last_updated using individual queries (NOT SCAN)
        all_records = []
        current_date = datetime.strptime(start_date, '%Y%m%d')
        end_date_dt = datetime.strptime(end_date, '%Y%m%d')
        
        while current_date <= end_date_dt:
            date_str = current_date.strftime('%Y%m%d')
            print(f"Querying records for last_updated: {date_str}")
            
            try:
                # BYPASS repository retry mechanism - use direct DynamoDB query
                from boto3.dynamodb.conditions import Key
                # Check what table name the repository is actually using
                table_name = repository.table_name
                print(f"🔍 Using table name: {table_name}")
                
                try:
                    # Try LastUpdated-index GSI directly
                    table = await storage.get_table(table_name)
                    response = await table.query(
                        IndexName='LastUpdated-index',
                        KeyConditionExpression=Key('LastUpdated').eq(date_str)
                    )
                    records = response.get('Items', [])
                    print(f"✅ Used LastUpdated-index GSI for {date_str}")
                except Exception as gsi_error:
                    if "ResourceNotFoundException" in str(gsi_error) or "resource not found" in str(gsi_error).lower():
                        print(f"⚠️  LastUpdated-index GSI not found, trying StartDate-index for {date_str}")
                        # Fallback to StartDate-index GSI
                        try:
                            response = await table.query(
                                IndexName='StartDate-index',
                                KeyConditionExpression=Key('StartDate').eq(date_str)
                            )
                            records = response.get('Items', [])
                            print(f"✅ Used StartDate-index GSI fallback for {date_str}")
                        except Exception as fallback_error:
                            print(f"❌ Both GSIs failed for {date_str}: {fallback_error}")
                            print(f"⚠️  WARNING: Using SCAN as last resort for {date_str} - this is inefficient!")
                            # Last resort: use scan with filter (inefficient but works)
                            try:
                                from boto3.dynamodb.conditions import Attr
                                response = await table.scan(
                                    FilterExpression=Attr('LastUpdated').eq(date_str)
                                )
                                records = response.get('Items', [])
                                print(f"🐌 Used SCAN fallback for {date_str} (found {len(records)} records)")
                            except Exception as scan_error:
                                print(f"❌ Even SCAN failed for {date_str}: {scan_error}")
                                records = []
                    else:
                        raise gsi_error
                
                all_records.extend(records)
                print(f"Found {len(records)} records for {date_str}")
            except Exception as e:
                print(f"Error querying records for {date_str}: {e}")
                logging.exception("Failed to query DynamoDB table")
            
            current_date += timedelta(days=1)
        
        print(f"Total records found: {len(all_records)}")
        
        # Filter records that don't have S3_image_key and collect unique page_ids
        page_ids_without_s3_key: Set[str] = set()
        
        print(f"🔍 Sample record keys: {list(all_records[0].keys())[:10] if all_records else 'No records'}")
        
        for record in all_records:
            # Check if S3_image_key is missing, None, or empty (try both snake_case and PascalCase)
            s3_image_key = record.get('s3_image_key') or record.get('S3ImageKey')
            if not s3_image_key or s3_image_key in ['', 'None', None]:
                # Try both snake_case and PascalCase for page_id
                page_id = record.get('page_id') or record.get('PageID')
                if page_id:
                    page_ids_without_s3_key.add(str(page_id))
        
        print(f"Found {len(page_ids_without_s3_key)} unique page_ids without S3_image_key")
        
        # Convert to sorted list for consistent output
        result = sorted(list(page_ids_without_s3_key))
        return result


async def main():
    """Main function to run the query and print results"""
    try:
        page_ids = await get_page_ids_without_s3_image_key()
        
        print(f"\nPage IDs without S3_image_key ({len(page_ids)} total):")
        print(page_ids)
        
        return page_ids
        
    except Exception as e:
        print(f"Error: {e}")
        logging.exception("Unexpected error in main")
        return []


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the script
    result = asyncio.run(main())