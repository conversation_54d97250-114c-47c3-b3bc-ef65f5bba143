#!/usr/bin/env python3
"""
Script to check file sizes and identify splitting candidates
tools/check_component_sizes.py

This script analyzes Python files in src/pacer/components/ recursively to:
- Report files larger than 400 lines
- Analyze for potential splits (multiple classes, many methods)
- Provide actionable recommendations for component refactoring
"""

import os
from pathlib import Path
import argparse


def check_component_sizes(component_dir: Path, line_threshold: int = 400):
    """Check component file sizes and suggest splits."""
    
    print(f"🔍 Analyzing components in: {component_dir}")
    print(f"📏 Line threshold: {line_threshold}")
    print("=" * 60)
    
    large_files = []
    
    for py_file in component_dir.rglob("*.py"):
        if py_file.name == "__init__.py":
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                line_count = len(lines)
                
            if line_count > line_threshold:
                large_files.append((py_file, line_count, lines))
                print(f"⚠️  Large component: {py_file.relative_to(component_dir)}")
                print(f"   Lines: {line_count}")
                print(f"   Action: Review for single responsibility split")
                
                # Analyze for potential splits
                analyze_for_split(py_file, lines)
                print()
                
        except (UnicodeDecodeError, IOError) as e:
            print(f"❌ Error reading {py_file}: {e}")
    
    # Summary
    print("=" * 60)
    print(f"📊 SUMMARY:")
    print(f"   Total large files (>{line_threshold} lines): {len(large_files)}")
    
    if large_files:
        print(f"   Largest file: {max(large_files, key=lambda x: x[1])[0].name} ({max(large_files, key=lambda x: x[1])[1]} lines)")
        avg_lines = sum(f[1] for f in large_files) / len(large_files)
        print(f"   Average size of large files: {avg_lines:.0f} lines")
    else:
        print("   ✅ No files exceed the line threshold!")


def analyze_for_split(file_path: Path, lines: list):
    """Analyze file for splitting opportunities."""
    
    class_count = 0
    method_count = 0
    function_count = 0
    import_count = 0
    comment_count = 0
    blank_count = 0
    
    class_names = []
    method_names = []
    
    for line_num, line in enumerate(lines, 1):
        stripped = line.strip()
        
        # Count different elements
        if stripped.startswith("class "):
            class_count += 1
            class_name = stripped.split("(")[0].replace("class ", "").replace(":", "").strip()
            class_names.append((class_name, line_num))
            
        elif stripped.startswith("def ") or stripped.startswith("async def "):
            method_count += 1
            method_name = stripped.split("(")[0].replace("def ", "").replace("async def ", "").strip()
            method_names.append((method_name, line_num))
            
        elif stripped.startswith("from ") or stripped.startswith("import "):
            import_count += 1
            
        elif stripped.startswith("#") or stripped.startswith('"""') or stripped.startswith("'''"):
            comment_count += 1
            
        elif not stripped:
            blank_count += 1
    
    # Calculate code density
    actual_code_lines = len(lines) - comment_count - blank_count
    
    print(f"   📈 File Analysis:")
    print(f"      Classes: {class_count}")
    print(f"      Methods/Functions: {method_count}")
    print(f"      Imports: {import_count}")
    print(f"      Actual code lines: {actual_code_lines}")
    print(f"      Code density: {(actual_code_lines / len(lines) * 100):.1f}%")
    
    # Splitting recommendations
    recommendations = []
    
    if class_count > 1:
        recommendations.append(f"Multiple classes ({class_count}): Consider splitting into separate files")
        print(f"      🏗️  Classes found: {', '.join([name for name, _ in class_names])}")
        
    if method_count > 15:
        recommendations.append(f"Many methods ({method_count}): Check for multiple responsibilities")
        
    if actual_code_lines > 300:
        recommendations.append(f"High code density ({actual_code_lines} lines): Consider extracting helper functions")
        
    if import_count > 20:
        recommendations.append(f"Many imports ({import_count}): May indicate too many dependencies")
    
    # Advanced analysis
    if class_count == 1 and method_count > 20:
        recommendations.append("Single large class: Consider extracting related methods into mixins or helper classes")
        
    if len([name for name, _ in method_names if name.startswith("_")]) > method_count * 0.3:
        recommendations.append("Many private methods: Consider if some could be extracted to separate utilities")
    
    # Print recommendations
    if recommendations:
        print(f"   💡 Splitting Recommendations:")
        for i, rec in enumerate(recommendations, 1):
            print(f"      {i}. {rec}")
    else:
        print(f"   ✅ File structure looks reasonable")


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(
        description="Check component file sizes and identify splitting candidates"
    )
    parser.add_argument(
        "--component-dir",
        type=Path,
        default=Path("src/pacer/components"),
        help="Directory to analyze (default: src/pacer/components)"
    )
    parser.add_argument(
        "--threshold",
        type=int,
        default=400,
        help="Line count threshold for flagging large files (default: 400)"
    )
    parser.add_argument(
        "--all",
        action="store_true",
        help="Show analysis for all files, not just large ones"
    )
    
    args = parser.parse_args()
    
    if not args.component_dir.exists():
        print(f"❌ Directory not found: {args.component_dir}")
        print("Make sure you're running this from the project root directory.")
        return 1
    
    if args.all:
        # Analyze all files regardless of size
        print(f"🔍 Analyzing ALL Python files in: {args.component_dir}")
        print("=" * 60)
        
        for py_file in args.component_dir.rglob("*.py"):
            if py_file.name == "__init__.py":
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                print(f"📄 {py_file.relative_to(args.component_dir)} ({len(lines)} lines)")
                analyze_for_split(py_file, lines)
                print()
                
            except (UnicodeDecodeError, IOError) as e:
                print(f"❌ Error reading {py_file}: {e}")
    else:
        # Only analyze large files
        check_component_sizes(args.component_dir, args.threshold)
    
    return 0


if __name__ == "__main__":
    exit(main())