# Adding Google Gemini 2.5 Pre to OpenCode Configuration

To add `google/gemini-2.5-pre` to this configuration schema, you need to add a custom provider configuration. Here's how:

```json
{
  "provider": {
    "google": {
      "models": {
        "gemini-2.5-pre": {
          "name": "Gemini 2.5 Pre",
          "attachment": true,
          "reasoning": false,
          "temperature": true,
          "tool_call": true,
          "cost": {
            "input": 0.00125,
            "output": 0.005
          },
          "limit": {
            "context": 2000000,
            "output": 8192
          },
          "id": "gemini-2.5-pre"
        }
      }
    }
  },
  "model": "google/gemini-2.5-pre"
}
```

The key parts:
- **Provider**: `"google"` as the provider name
- **Model ID**: `"gemini-2.5-pre"` as the model identifier
- **Capabilities**: Set `attachment: true` and `tool_call: true` for full functionality
- **Context/Output limits**: Adjust based on Gemini 2.5's actual specifications
- **Cost**: Update with actual pricing (values shown are estimates)

You'll also need the appropriate environment variables (like `GOOGLE_API_KEY`) configured for authentication.