#!/usr/bin/env python
"""
Debug script to check added_on field values in JSON files
"""
import json
import os
import sys
from pathlib import Path

def check_json_files(date_dir):
    """Check all JSON files in the date directory for added_on issues"""
    data_dir = Path(f"data/{date_dir}/dockets")
    
    if not data_dir.exists():
        print(f"Directory not found: {data_dir}")
        return
    
    issues = []
    total_files = 0
    
    for json_file in data_dir.glob("*.json"):
        total_files += 1
        
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            added_on = data.get('added_on')
            filing_date = data.get('filing_date')
            
            # Check if added_on equals filing_date
            if added_on == filing_date and added_on is not None:
                issues.append({
                    'file': json_file.name,
                    'added_on': added_on,
                    'filing_date': filing_date,
                    'issue': 'added_on equals filing_date'
                })
            
            # Check if added_on is missing
            elif not added_on:
                issues.append({
                    'file': json_file.name,
                    'added_on': added_on,
                    'filing_date': filing_date,
                    'issue': 'added_on is missing'
                })
            
            # Check if added_on doesn't match the expected date
            elif added_on != date_dir:
                issues.append({
                    'file': json_file.name,
                    'added_on': added_on,
                    'filing_date': filing_date,
                    'expected': date_dir,
                    'issue': 'added_on does not match directory date'
                })
                
        except Exception as e:
            print(f"Error reading {json_file.name}: {e}")
    
    print(f"\nChecked {total_files} files in {data_dir}")
    print(f"Found {len(issues)} issues:\n")
    
    for issue in issues[:10]:  # Show first 10 issues
        print(f"File: {issue['file']}")
        print(f"  Issue: {issue['issue']}")
        print(f"  added_on: {issue.get('added_on', 'None')}")
        print(f"  filing_date: {issue.get('filing_date', 'None')}")
        if 'expected' in issue:
            print(f"  expected: {issue['expected']}")
        print()
    
    if len(issues) > 10:
        print(f"... and {len(issues) - 10} more issues")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python debug_added_on.py YYYYMMDD")
        sys.exit(1)
    
    date_dir = sys.argv[1]
    check_json_files(date_dir)