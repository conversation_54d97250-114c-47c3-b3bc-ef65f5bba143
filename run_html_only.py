#!/usr/bin/env python3
"""
Run HTML fetching for records that have Google search but missing HTML data
"""

import sqlite3
import asyncio
import re
from pathlib import Path
from rich.console import Console
from rich.progress import Progress

# Add the scraper directory to path
import sys
scraper_dir = Path(__file__).parent / "@scripts/analysis/talc"
sys.path.insert(0, str(scraper_dir))

from scrape_pacermonitor import fetch_and_parse_page, parse_case_data, console
import json

# Database configuration
DB_DIR = Path("sqlite")
DB_FILE = DB_DIR / "pacermon_cache.db"

def parse_docket_for_comparison(docket_str):
    """Parse docket string to extract number for comparison."""
    match = re.match(r'(.*?)([0-9]{5})$', docket_str)
    if match:
        base_pattern = match.group(1)
        number = int(match.group(2))
        return base_pattern, number
    return None, None

def should_process_docket(docket_num, min_docket="3:25-cv-11409"):
    """Check if docket should be processed based on minimum docket number."""
    min_base, min_num = parse_docket_for_comparison(min_docket)
    curr_base, curr_num = parse_docket_for_comparison(docket_num)
    
    if not min_base or not curr_base or min_base != curr_base:
        return False
    
    return curr_num >= min_num

def get_records_needing_html():
    """Get records that have Google search but missing HTML data."""
    conn = sqlite3.connect(DB_FILE)
    
    cursor = conn.execute('''
        SELECT docket_num, search_query, results_json
        FROM pacermon_searches 
        WHERE results_json IS NOT NULL 
        AND (assigned_to IS NULL AND referred_to IS NULL AND versus IS NULL)
        ORDER BY docket_num
    ''')
    
    all_records = cursor.fetchall()
    conn.close()
    
    # Filter for records from 3:25-cv-11409 onward
    filtered_records = []
    for docket_num, search_query, results_json in all_records:
        if should_process_docket(docket_num):
            filtered_records.append((docket_num, search_query, results_json))
    
    return filtered_records

async def process_html_only():
    """Process only HTML fetching for records with Google search results."""
    
    records = get_records_needing_html()
    
    if not records:
        console.print("[green]✅ No records need HTML fetching[/green]")
        return
    
    console.print(f"[yellow]Found {len(records)} records needing HTML fetch from 3:25-cv-11409 onward[/yellow]")
    
    with Progress() as progress:
        task = progress.add_task("[cyan]Fetching HTML pages...", total=len(records))
        
        for docket_num, search_query, results_json in records:
            console.print(f"[cyan]Processing {docket_num}[/cyan]")
            
            # Parse the stored results
            results = json.loads(results_json)
            
            if results and 'items' in results and len(results['items']) > 0:
                # Get the first PacerMonitor URL
                first_item = results['items'][0]
                pacermon_url = first_item.get('link')
                
                if pacermon_url and 'pacermonitor.com' in pacermon_url:
                    console.print(f"[yellow]Fetching HTML for {docket_num}...[/yellow]")
                    case_data_result = await fetch_and_parse_page(pacermon_url, search_query, docket_num)
                    await parse_case_data(case_data_result, docket_num, search_query, results)
                else:
                    console.print(f"[red]No valid PacerMonitor URL for {docket_num}[/red]")
            else:
                console.print(f"[red]No search results for {docket_num}[/red]")
            
            progress.advance(task)
    
    console.print(f"[green]✅ Completed HTML fetching for {len(records)} records[/green]")

if __name__ == "__main__":
    asyncio.run(process_html_only())