#!/usr/bin/env bash

# Script to run reports for a configurable date range
# Usage: ./run_reports_range.sh --start YYYYMMDD --end YYYYMMDD [--config CONFIG_NAME]

# Default config
CONFIG_NAME="report"
START_DATE=""
END_DATE=""

# Parse arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --start) START_DATE="$2"; shift ;;
        --end) END_DATE="$2"; shift ;;
        --config) CONFIG_NAME="$2"; shift ;;
        *) echo "Unknown parameter: $1"; echo "Usage: $0 --start YYYYMMDD --end YYYYMMDD [--config CONFIG_NAME]"; exit 1 ;;
    esac
    shift
done

# Validate required parameters
if [ -z "$START_DATE" ] || [ -z "$END_DATE" ]; then
    echo "Error: Both --start and --end dates are required"
    echo "Usage: $0 --start YYYYMMDD --end YYYYMMDD [--config <PERSON>NFIG_NAME]"
    echo "Example: $0 --start 20250626 --end 20250614"
    echo "Example: $0 --start 20250626 --end 20250614 --config weekly_report"
    exit 1
fi

# Validate date format
if [[ ! "$START_DATE" =~ ^[0-9]{8}$ ]] || [[ ! "$END_DATE" =~ ^[0-9]{8}$ ]]; then
    echo "Error: Dates must be in YYYYMMDD format"
    exit 1
fi

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
CONFIG_DIR="$SCRIPT_DIR/config"
TEMP_DIR="/tmp/lexgenius_batch_$$"

# Create temp directory
mkdir -p "$TEMP_DIR"

echo "Running $CONFIG_NAME from $START_DATE to $END_DATE"
echo "=========================================="

# Convert dates to seconds since epoch for easier arithmetic
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS date command
    current_epoch=$(date -j -f "%Y%m%d" "$START_DATE" "+%s")
    end_epoch=$(date -j -f "%Y%m%d" "$END_DATE" "+%s")
else
    # Linux date command
    current_epoch=$(date -d "$START_DATE" +%s)
    end_epoch=$(date -d "$END_DATE" +%s)
fi

# Determine direction (forward or backward)
if [ $current_epoch -gt $end_epoch ]; then
    echo "Processing dates backwards..."
    step=-86400  # Subtract one day
    comparison="-ge"
else
    echo "Processing dates forwards..."
    step=86400   # Add one day
    comparison="-le"
fi

# Counter for successful runs
success_count=0
total_count=0

# Iterate through dates
while [ $current_epoch $comparison $end_epoch ]; do
    # Convert epoch back to YYYYMMDD and MM/DD/YY
    if [[ "$OSTYPE" == "darwin"* ]]; then
        current_date=$(date -j -f "%s" "$current_epoch" "+%Y%m%d")
        formatted_date=$(date -j -f "%s" "$current_epoch" "+%m/%d/%y")
    else
        current_date=$(date -d "@$current_epoch" "+%Y%m%d")
        formatted_date=$(date -d "@$current_epoch" "+%m/%d/%y")
    fi
    
    echo ""
    echo "[$((total_count + 1))] Processing date: $current_date ($formatted_date)"
    echo "----------------------------"
    
    # Create temporary config file with the specific date
    temp_config="$TEMP_DIR/${CONFIG_NAME}.yml"
    
    # Check if config file exists
    if [ ! -f "$CONFIG_DIR/${CONFIG_NAME}.yml" ]; then
        echo "Error: Config file not found: $CONFIG_DIR/${CONFIG_NAME}.yml"
        exit 1
    fi
    
    # Replace date in the config file
    sed "s|date: '.*'|date: '$formatted_date'|g" "$CONFIG_DIR/${CONFIG_NAME}.yml" > "$temp_config"
    
    # Run the pipeline with the temporary config
    cd "$SCRIPT_DIR" && python3 src/main.py --params "$temp_config"
    
    # Check if the command was successful
    if [ $? -eq 0 ]; then
        echo "✓ Successfully processed $CONFIG_NAME for $current_date"
        ((success_count++))
    else
        echo "✗ Error processing $CONFIG_NAME for $current_date"
        # Optionally stop on error - uncomment the next line if desired
        # exit 1
    fi
    
    # Clean up temp config
    rm -f "$temp_config"
    
    ((total_count++))
    
    # Move to next date
    current_epoch=$((current_epoch + step))
done

# Clean up temp directory
rmdir "$TEMP_DIR" 2>/dev/null || true

echo ""
echo "=========================================="
echo "Batch processing completed!"
echo "Total dates processed: $total_count"
echo "Successful: $success_count"
echo "Failed: $((total_count - success_count))"