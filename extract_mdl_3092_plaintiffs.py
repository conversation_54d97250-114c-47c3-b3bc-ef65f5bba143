#!/usr/bin/env python3
"""
Extract plaintiff names from MDL 3092 cases and find matching plaintiffs.
"""
import asyncio
import json
import logging
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from pathlib import Path

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
import sys
sys.path.append(str(Path(__file__).parent))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.external.deepseek_client import DeepSeekClient
from src.repositories.pacer_repository import PacerRepository

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

console = Console()


class MDL3092PlaintiffExtractor:
    """Extract plaintiff names from MDL 3092 cases."""
    
    def __init__(self):
        # Initialize storage
        self.dynamodb_storage = AsyncDynamoDBStorage({
            'aws_access_key': os.getenv('AWS_ACCESS_KEY'),
            'aws_secret_key': os.getenv('AWS_SECRET_KEY'),
            'region_name': os.getenv('AWS_REGION', 'us-west-2')
        })
        
        self.pacer_repo = PacerRepository(self.dynamodb_storage)
        
        # Initialize DeepSeek client
        self.deepseek_client = DeepSeekClient(
            api_key=os.getenv('DEEPSEEK_API_KEY')
        )
        
        # Data directory
        self.data_dir = Path(os.getenv('DATA_DIR', 'data'))
        
    async def __aenter__(self):
        await self.dynamodb_storage.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.dynamodb_storage.__aexit__(exc_type, exc_val, exc_tb)
    
    async def query_mdl_cases(self, mdl_num: str, start_date: str) -> List[Dict[str, Any]]:
        """Query MDL cases from start date to present."""
        end_date = datetime.now().strftime('%Y%m%d')
        
        console.print(f"[cyan]Querying MDL {mdl_num} cases from {start_date} to {end_date}...[/cyan]")
        
        cases = await self.pacer_repo.query_by_mdl_and_date_range(
            mdl_num=mdl_num,
            start_date=start_date,
            end_date=end_date
        )
        
        console.print(f"[green]Found {len(cases)} cases for MDL {mdl_num}[/green]")
        return cases
    
    def get_base_filename(self, case: Dict[str, Any]) -> str:
        """Extract base filename from case data."""
        # Try different filename fields
        filename = (
            case.get('base_filename') or 
            case.get('original_filename') or 
            case.get('new_filename', '')
        )
        
        # Remove .pdf extension if present
        if filename.endswith('.pdf'):
            filename = filename[:-4]
            
        # If no filename, construct from court_id and docket_num
        if not filename:
            court_id = case.get('court_id', '')
            docket_num = case.get('docket_num', '').replace(':', '_')
            versus = case.get('versus', '').replace(' ', '_').replace('/', '_')[:50]
            filename = f"{court_id}_{docket_num}_{versus}"
            
        return filename
    
    async def check_md_files_exist(self, cases: List[Dict[str, Any]]) -> Tuple[List[Dict], List[Dict]]:
        """Check which cases have MD files locally."""
        found_cases = []
        missing_cases = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            console=console
        ) as progress:
            task = progress.add_task("Checking MD files locally...", total=len(cases))
            
            for case in cases:
                # Use added_on date for directory path
                added_on = case.get('added_on', '')
                if not added_on:
                    logger.warning(f"No added_on date for case {case.get('docket_num')}")
                    missing_cases.append(case)
                    progress.update(task, advance=1)
                    continue
                
                base_filename = self.get_base_filename(case)
                
                # Construct local path
                docket_dir = self.data_dir / added_on / "dockets"
                md_path = docket_dir / f"{base_filename}.md"
                
                # Check if file exists
                if md_path.exists():
                    case['local_md_path'] = str(md_path)
                    found_cases.append(case)
                else:
                    # Try alternative pattern: court_id_YY_NNNNN
                    court_id = case.get('court_id', '')
                    docket_num = case.get('docket_num', '')
                    
                    # Extract year and number from docket if possible
                    # Common patterns: 1:25-sf-65791, 25-65791, etc.
                    alt_filename = None
                    docket_match = re.search(r'(\d{2,4})[-:](?:sf-)?(\d+)', docket_num)
                    if docket_match:
                        year = docket_match.group(1)
                        number = docket_match.group(2)
                        # Use last 2 digits of year
                        if len(year) == 4:
                            year = year[-2:]
                        alt_filename = f"{court_id}_{year}_{number}"
                    
                    # Try alternative path if we could construct it
                    found_alt = False
                    if alt_filename and docket_dir.exists():
                        # Search for files matching this pattern in the directory
                        pattern = f"{alt_filename}*.md"
                        logger.debug(f"Searching for alternative files with pattern: {pattern} in {docket_dir}")
                        
                        try:
                            # Find matching files
                            matching_files = list(docket_dir.glob(pattern))
                            
                            if matching_files:
                                # Use the first matching MD file
                                case['local_md_path'] = str(matching_files[0])
                                case['alt_filename_used'] = True
                                found_cases.append(case)
                                found_alt = True
                                logger.info(f"Found alternative MD file for {docket_num}: {matching_files[0].name}")
                        except Exception as e:
                            logger.warning(f"Error searching for alternative files: {e}")
                    
                    if not found_alt:
                        case['expected_path'] = str(md_path)
                        if alt_filename:
                            case['alt_pattern_tried'] = f"{docket_dir}/{alt_filename}*.md"
                        missing_cases.append(case)
                
                progress.update(task, advance=1)
        
        return found_cases, missing_cases
    
    
    async def extract_text_from_md(self, md_content: str) -> str:
        """Extract relevant text from MD file."""
        # Remove OCR header section
        ocr_pattern = r'# OCR Extracted Text.*?---'
        md_content = re.sub(ocr_pattern, '', md_content, flags=re.DOTALL)
        
        # Find text up to "# Preliminary Statement" or use more text since DeepSeek has 64k context
        prelim_match = re.search(r'# Preliminary Statement', md_content)
        
        if prelim_match:
            text = md_content[:prelim_match.start()]
        else:
            # Use more text - DeepSeek can handle it
            text = md_content[:10000]  # Increased from 2500
        
        return text.strip()
    
    async def extract_plaintiffs_with_deepseek(self, text: str, case_info: Dict[str, Any]) -> List[str]:
        """Use DeepSeek to extract plaintiff names from text."""
        # DeepSeek has 64k context window - use the full text
        prompt = f"""Extract all plaintiff names from the following legal document text. 
Return ONLY a JSON object with a "plaintiffs" key containing an array of plaintiff names.

For example: {{"plaintiffs": ["John Doe", "Jane Smith", "ABC Corporation"]}}

If no plaintiffs are found, return: {{"plaintiffs": []}}

IMPORTANT: Return COMPLETE JSON. Do not truncate the response.

Text:
{text}
"""
        
        try:
            messages = [
                {"role": "system", "content": "You are a legal document analyzer. Extract plaintiff names and return them as a JSON object with a 'plaintiffs' key."},
                {"role": "user", "content": prompt}
            ]
            
            # Use _chat_completion with json_mode=True and higher token limit
            response = await self.deepseek_client._chat_completion(
                messages=messages,
                temperature=0.0,
                max_tokens=8192,  # Increased to handle full responses
                json_mode=True
            )
            
            if response:
                # Response should already be parsed JSON when json_mode=True
                if isinstance(response, dict):
                    plaintiffs = response.get('plaintiffs', [])
                    # Clean up any non-string entries
                    return [str(p).strip() for p in plaintiffs if p and str(p).strip()]
                elif isinstance(response, str):
                    try:
                        parsed = json.loads(response)
                        plaintiffs = parsed.get('plaintiffs', [])
                        return [str(p).strip() for p in plaintiffs if p and str(p).strip()]
                    except json.JSONDecodeError:
                        # Try to extract names from partial response
                        logger.warning(f"Could not parse JSON response for case {case_info.get('docket_num')}, trying fallback extraction")
                        return self._extract_names_fallback(response)
            
            return []
            
        except Exception as e:
            logger.error(f"Error extracting plaintiffs for case {case_info.get('docket_num')}: {e}")
            # Try using existing plaintiff field from case data
            if 'plaintiff' in case_info:
                plaintiffs = case_info['plaintiff']
                if isinstance(plaintiffs, list):
                    return plaintiffs
                elif isinstance(plaintiffs, str):
                    return [plaintiffs]
            return []
    
    def _extract_names_fallback(self, text: str) -> List[str]:
        """Fallback method to extract names from partial/corrupted response."""
        names = []
        
        # Try to find quoted strings that look like names
        import re
        quoted_pattern = r'"([^"]+)"'
        matches = re.findall(quoted_pattern, text)
        
        for match in matches:
            # Basic heuristic: if it looks like a name (has letters, not too long)
            if match and len(match) < 100 and any(c.isalpha() for c in match):
                # Skip common JSON keys
                if match.lower() not in ['plaintiffs', 'plaintiff', 'name', 'names']:
                    names.append(match.strip())
        
        return names[:10]  # Limit to 10 names max
    
    async def process_single_case(self, case: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process a single case to extract plaintiff names."""
        try:
            # Read MD file from local path
            local_path = case.get('local_md_path')
            if not local_path:
                logger.warning(f"No local path for case {case.get('docket_num')}")
                return None
            
            # Read file content
            try:
                with open(local_path, 'r', encoding='utf-8') as f:
                    md_content = f.read()
            except Exception as e:
                logger.warning(f"Could not read MD file for case {case.get('docket_num')}: {e}")
                return None
            
            # Extract relevant text
            text = await self.extract_text_from_md(md_content)
            
            # Extract plaintiffs using DeepSeek
            logger.info(f"Processing case {case.get('docket_num')} with DeepSeek")
            plaintiff_list = await self.extract_plaintiffs_with_deepseek(text, case)
            
            # Create result record
            result = {
                'court_id': case.get('court_id'),
                'docket_num': case.get('docket_num'),
                'versus': case.get('versus'),
                'plaintiff_list': plaintiff_list,
                'law_firm': case.get('law_firm') or case.get('law_firms'),
                'filing_date': case.get('filing_date'),
                'added_on': case.get('added_on')
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing case {case.get('docket_num')}: {e}")
            return None

    async def process_cases(self, cases: List[Dict[str, Any]], max_concurrent: int = 10) -> List[Dict[str, Any]]:
        """Process cases in parallel to extract plaintiff names."""
        console.print(f"[cyan]Processing {len(cases)} cases with {max_concurrent} concurrent workers...[/cyan]")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(case: Dict[str, Any]) -> Optional[Dict[str, Any]]:
            async with semaphore:
                return await self.process_single_case(case)
        
        # Process all cases in parallel
        tasks = [process_with_semaphore(case) for case in cases]
        
        # Use tqdm for progress tracking
        results = []
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            console=console
        ) as progress:
            task = progress.add_task("Processing cases...", total=len(cases))
            
            # Process in batches to update progress
            for future in asyncio.as_completed(tasks):
                result = await future
                if result:
                    results.append(result)
                progress.update(task, advance=1)
        
        return results
    
    def find_matching_plaintiffs(self, results: List[Dict[str, Any]]) -> Dict[str, List[Dict]]:
        """Find cases with matching plaintiff lists."""
        matches = {
            'exact_matches': defaultdict(list),  # Same plaintiffs AND versus
            'plaintiff_matches': defaultdict(list)  # Same plaintiffs, different versus
        }
        
        # Create normalized plaintiff sets for comparison
        for i, case1 in enumerate(results):
            plaintiffs1 = set(p.lower().strip() for p in case1['plaintiff_list'])
            if not plaintiffs1:
                continue
                
            # Create keys for matching
            exact_key = (frozenset(plaintiffs1), case1['versus'].lower().strip())
            plaintiff_key = frozenset(plaintiffs1)
            
            # Check for exact matches (same plaintiffs and versus)
            for j, case2 in enumerate(results[i+1:], i+1):
                plaintiffs2 = set(p.lower().strip() for p in case2['plaintiff_list'])
                if not plaintiffs2:
                    continue
                    
                exact_key2 = (frozenset(plaintiffs2), case2['versus'].lower().strip())
                
                if exact_key == exact_key2:
                    # Group by the key
                    key_str = f"{sorted(plaintiffs1)}|{case1['versus']}"
                    matches['exact_matches'][key_str].extend([case1, case2])
            
            # Group by plaintiff set only
            key_str = str(sorted(plaintiffs1))
            matches['plaintiff_matches'][key_str].append(case1)
        
        # Remove duplicates and single-case groups
        cleaned_matches = {
            'exact_matches': {},
            'plaintiff_matches': {}
        }
        
        # Clean exact matches
        for key, cases in matches['exact_matches'].items():
            # Remove duplicates by docket_num
            unique_cases = {}
            for case in cases:
                docket_key = f"{case['court_id']}_{case['docket_num']}"
                unique_cases[docket_key] = case
            
            if len(unique_cases) > 1:
                cleaned_matches['exact_matches'][key] = list(unique_cases.values())
        
        # Clean plaintiff matches
        for key, cases in matches['plaintiff_matches'].items():
            # Remove duplicates by docket_num
            unique_cases = {}
            for case in cases:
                docket_key = f"{case['court_id']}_{case['docket_num']}"
                unique_cases[docket_key] = case
            
            if len(unique_cases) > 1:
                cleaned_matches['plaintiff_matches'][key] = list(unique_cases.values())
        
        return cleaned_matches
    
    def display_results(self, results: List[Dict[str, Any]], matches: Dict[str, Dict]):
        """Display results in a formatted table."""
        # Display all cases
        console.print("\n[bold cyan]All MDL 3092 Cases with Plaintiff Information:[/bold cyan]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Court ID", style="cyan")
        table.add_column("Docket Number", style="green")
        table.add_column("Versus", style="yellow")
        table.add_column("# Plaintiffs", style="red")
        table.add_column("Law Firm", style="blue")
        table.add_column("Filing Date", style="magenta")
        
        for case in results:
            table.add_row(
                case['court_id'],
                case['docket_num'],
                case['versus'][:50] + "..." if len(case['versus']) > 50 else case['versus'],
                str(len(case['plaintiff_list'])),
                case['law_firm'] or "N/A",
                case['filing_date']
            )
        
        console.print(table)
        
        # Display exact matches
        if matches['exact_matches']:
            console.print("\n[bold green]Cases with Exact Same Plaintiffs AND Versus:[/bold green]")
            for key, cases in matches['exact_matches'].items():
                console.print(f"\n[yellow]Group: {len(cases)} cases[/yellow]")
                for case in cases:
                    console.print(f"  - {case['court_id']} {case['docket_num']} ({case['filing_date']})")
        
        # Display plaintiff-only matches
        if matches['plaintiff_matches']:
            console.print("\n[bold blue]Cases with Same Plaintiff Names (different versus):[/bold blue]")
            for key, cases in matches['plaintiff_matches'].items():
                if len(cases) > 1:  # Only show groups with multiple cases
                    console.print(f"\n[yellow]Group: {len(cases)} cases with same plaintiffs[/yellow]")
                    for case in cases:
                        console.print(f"  - {case['court_id']} {case['docket_num']} vs {case['versus'][:30]}...")
    
    async def run(self, max_concurrent: int = 10):
        """Main execution method."""
        mdl_num = "3092"
        start_date = "20250314"
        
        console.print(Panel.fit("[bold cyan]MDL 3092 Plaintiff Extraction Tool[/bold cyan]"))
        
        # Query cases
        cases = await self.query_mdl_cases(mdl_num, start_date)
        
        if not cases:
            console.print("[red]No cases found for MDL 3092[/red]")
            return
        
        # Check which cases have MD files
        found_cases, missing_cases = await self.check_md_files_exist(cases)
        
        console.print(f"\n[green]Found MD files for {len(found_cases)} cases[/green]")
        console.print(f"[yellow]Missing MD files for {len(missing_cases)} cases[/yellow]")
        
        # Show missing cases and ask for approval
        if missing_cases:
            console.print("\n[bold yellow]Cases missing MD files:[/bold yellow]")
            for case in missing_cases[:10]:  # Show first 10
                alt_info = ""
                if case.get('alt_pattern_tried'):
                    alt_info = f" (also tried: {case['alt_pattern_tried']})"
                console.print(f"  - {case['court_id']} {case['docket_num']} - Expected: {case.get('expected_path', 'N/A')}{alt_info}")
            
            if len(missing_cases) > 10:
                console.print(f"  ... and {len(missing_cases) - 10} more")
            
            proceed = console.input("\n[cyan]Proceed with available cases? (y/n): [/cyan]")
            if proceed.lower() != 'y':
                console.print("[red]Aborted by user[/red]")
                return
        
        # Process found cases
        if not found_cases:
            console.print("[red]No cases with MD files to process[/red]")
            return
            
        results = await self.process_cases(found_cases, max_concurrent=max_concurrent)
        
        # Find matching plaintiffs
        matches = self.find_matching_plaintiffs(results)
        
        # Display results
        self.display_results(results, matches)
        
        # Save to JSON - separate analysis file, NOT modifying existing JSON files
        output_file = f"mdl_3092_plaintiff_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        output_data = {
            'metadata': {
                'mdl_num': mdl_num,
                'start_date': start_date,
                'end_date': datetime.now().strftime('%Y%m%d'),
                'total_cases_queried': len(cases),
                'cases_with_md_files': len(found_cases),
                'cases_processed': len(results),
                'extraction_date': datetime.now().isoformat(),
                'note': 'This is a separate analysis file - no existing JSON files were modified'
            },
            'all_cases': results,
            'exact_matches': {k: v for k, v in matches['exact_matches'].items()},
            'plaintiff_matches': {k: v for k, v in matches['plaintiff_matches'].items() if len(v) > 1}
        }
        
        with open(output_file, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        console.print(f"\n[green]Results saved to {output_file}[/green]")
        console.print(f"[yellow]Note: This is a separate analysis file. No existing JSON files were modified.[/yellow]")
        console.print(f"[cyan]Total cases processed: {len(results)}[/cyan]")
        console.print(f"[cyan]Exact match groups: {len(matches['exact_matches'])}[/cyan]")
        console.print(f"[cyan]Plaintiff match groups: {sum(1 for v in matches['plaintiff_matches'].values() if len(v) > 1)}[/cyan]")


async def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Extract plaintiff names from MDL 3092 cases')
    parser.add_argument('--max-concurrent', type=int, default=10,
                        help='Maximum number of concurrent DeepSeek API calls (default: 10)')
    
    args = parser.parse_args()
    
    async with MDL3092PlaintiffExtractor() as extractor:
        await extractor.run(max_concurrent=args.max_concurrent)


if __name__ == "__main__":
    asyncio.run(main())