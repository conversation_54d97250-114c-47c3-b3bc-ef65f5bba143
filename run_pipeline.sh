#!/usr/bin/env bash

# Signal handling for graceful shutdown
cleanup() {
    echo ""
    echo "⚠️  Pipeline interrupted. Cleaning up..."
    if [ ! -z "$CURRENT_PID" ]; then
        echo "Terminating current process (PID: $CURRENT_PID)..."
        kill -TERM "$CURRENT_PID" 2>/dev/null
        # Wait up to 10 seconds for graceful shutdown
        for i in {1..10}; do
            if ! kill -0 "$CURRENT_PID" 2>/dev/null; then
                echo "Process terminated gracefully"
                break
            fi
            sleep 1
        done
        # Force kill if still running
        if kill -0 "$CURRENT_PID" 2>/dev/null; then
            echo "Force killing process..."
            kill -KILL "$CURRENT_PID" 2>/dev/null
        fi
    fi
    echo "Pipeline cleanup completed"
    exit 130
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Default to an empty string if no --config is provided or if it's empty
CONFIG_NAMES=""
CURRENT_PID=""

# Parse arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --config) CONFIG_NAMES="$2"; shift ;;
        *) echo "Unknown parameter passed: $1"; exit 1 ;;
    esac
    shift
done

if [ -z "$CONFIG_NAMES" ]; then
    echo "Usage: $0 --config <config_name1>[,<config_name2>,...]"
    echo "Example: $0 --config scrape_pacer,transform_data"
    exit 1
fi

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$SCRIPT_DIR" # Assuming run_pipeline.sh is in the project root

# Split the comma-separated config names
IFS=',' read -ra CONFIG_ARRAY <<< "$CONFIG_NAMES"

# Loop through each config name and run main.py
for config_name in "${CONFIG_ARRAY[@]}"; do
    if [[ "$config_name" == *.yml ]]; then
        config_file_path="$PROJECT_ROOT/config/${config_name}"
    else
        config_file_path="$PROJECT_ROOT/config/${config_name}.yml"
    fi
    main_script_path="$PROJECT_ROOT/src/main.py"

    if [ ! -f "$config_file_path" ]; then
        echo "Error: Config file not found at $config_file_path"
        continue # Skip to the next config if this one is not found
    fi

    if [ ! -f "$main_script_path" ]; then
        echo "Error: main2.py not found at $main_script_path"
        exit 1 # Exit if main.py is missing, as it's crucial
    fi

    echo "Running pipeline step: $config_name with config $config_file_path"
    # Assuming main.py accepts the config file path with a --params argument
    # If main.py expects the config file path directly without --params, change the line below to:
    # python3 "$main_script_path" "$config_file_path"
    
    # Run Python process in background to capture PID
    python3 "$main_script_path" --params "$config_file_path" &
    CURRENT_PID=$!
    
    # Wait for the process to complete
    wait $CURRENT_PID
    EXIT_CODE=$?
    CURRENT_PID=""
    
    # Check the exit code of the last command
    if [ $EXIT_CODE -ne 0 ]; then
        if [ $EXIT_CODE -eq 130 ]; then
            echo "Pipeline step $config_name was interrupted"
            exit 130
        else
            echo "Error running pipeline step: $config_name (exit code: $EXIT_CODE)"
            # To stop on the first error, uncomment the next line
            # exit 1
        fi
    fi
done

echo "All pipeline steps completed."