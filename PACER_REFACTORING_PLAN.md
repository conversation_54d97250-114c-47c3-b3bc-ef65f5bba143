# PACER Service Refactoring Plan - Thin Facade Pattern with DI Container

## Executive Summary

This document outlines a comprehensive plan to refactor the PACER service module from its current mixed architecture into a clean, maintainable thin facade pattern with proper Dependency Injection (DI) container integration.

**Current State**: 44 Python files with mixed patterns, some facades already exist but with `_components` directories
**Target State**: Clean facade services with all components in `pacer/components/`, no `_components` directories

## Architecture Goals

### 1. Thin Facade Services (≤500 lines each)
- Public API interface only
- Delegate all implementation to components
- Manage component coordination
- Handle error aggregation

### 2. Focused Components (300-400 lines max)
- Single responsibility
- Testable in isolation
- Reusable across facades
- Clear interfaces

### 3. DI Container Integration
- All facades registered in container
- Components injected, not imported
- Configuration-driven initialization
- Lifecycle management

## Current Structure Analysis

### Existing Facade Services (Already Implemented)
1. **PacerBrowserService** - Delegates to browser components
2. **PacerFileService** - Delegates to file components  
3. **PacerDownloadService** - Delegates to download components
4. **PacerVerificationService** - Delegates to verification components

### Standalone Services (Need Facade Treatment)
- analytics_service.py
- authentication_service.py
- case_classification_service.py
- case_processing_service.py
- configuration_service.py
- court_processing_service.py
- docket_processing_orchestrator_service.py
- export_service.py
- html_processing_service.py
- interactive_service.py
- navigation_service.py
- query_service.py
- report_service.py
- row_processing_service.py
- transfer_service.py

### Jobs Subsystem
- job_orchestration_service.py
- job_runner_service.py
- jobs_models.py

## Proposed Architecture

### Phase 1: Component Migration and Organization

#### Step 1.1: Migrate Existing `_components` to `components/`
```
FROM:
src/pacer/_browser_components/ → src/pacer/components/browser/
src/pacer/_file_components/ → src/pacer/components/file_operations/
src/pacer/_download_components/ → src/pacer/components/download/
src/pacer/_verification_components/ → src/pacer/components/verification/
src/pacer/_processing_components/ → src/pacer/components/processing/
```

#### Step 1.2: Component Size Management Strategy
For components exceeding 400 lines, split by SINGLE RESPONSIBILITY:

**Splitting Guidelines**:
- Only split if component has multiple distinct responsibilities
- Each split should represent a complete, cohesive responsibility
- Avoid over-granular splits (no "helper" or "util" components)
- Aim for 2-3 components max from a single large component

**Example: Large Component Splitting**
```
If HTMLProcessor is 800 lines with mixed responsibilities:

GOOD SPLIT (by responsibility):
components/processing/
├── html_processor.py (350 lines) - Core HTML processing logic
└── html_transformer.py (350 lines) - HTML to data transformation

BAD SPLIT (too granular):
components/processing/
├── html_processor.py (200 lines)
├── html_validator.py (100 lines) 
├── html_utils.py (150 lines)
├── html_helpers.py (100 lines)
└── html_constants.py (50 lines)
```

### Phase 2: Service Consolidation Plan

#### Group A: Core Processing Services
**Target**: `PacerCoreService` facade (≤500 lines)

Consolidates:
- case_processing_service.py
- row_processing_service.py  
- case_classification_service.py

```python
# src/pacer/pacer_core_service.py (400-500 lines)
class PacerCoreService(AsyncServiceBase):
    def __init__(self, 
                 case_processor: CaseProcessor,
                 row_processor: RowProcessor,
                 case_classifier: CaseClassifier):
        # Components injected via DI
        
    async def process_case(self, case_data: Dict) -> ProcessedCase
    async def process_row(self, row_data: Dict) -> ProcessedRow
    async def classify_case(self, case: ProcessedCase) -> Classification
```

Components:
```
components/case_processing/
├── case_processor.py (350 lines)
├── row_processor.py (400 lines)
└── case_classifier.py (300 lines)
```

#### Group B: Data Services
**Target**: `PacerDataService` facade (≤500 lines)

Consolidates:
- analytics_service.py
- report_service.py
- export_service.py

```python
# src/pacer/pacer_data_service.py (450 lines)
class PacerDataService(AsyncServiceBase):
    def __init__(self,
                 analytics_engine: AnalyticsEngine,
                 report_generator: ReportGenerator,
                 export_handler: ExportHandler):
        # Components injected via DI
        
    async def analyze(self, data: Dict) -> AnalyticsResult
    async def generate_report(self, params: Dict) -> Report
    async def export_data(self, format: str, data: Any) -> bytes
```

Components:
```
components/analytics/
├── analytics_engine.py (350 lines)
├── metrics_calculator.py (250 lines)

components/report/
├── report_generator.py (300 lines)
├── report_formatter.py (200 lines)

components/export/
├── export_handler.py (300 lines)
├── format_converters.py (250 lines)
```

#### Group C: Court Operations Services
**Target**: `PacerCourtService` facade (≤450 lines)

Consolidates:
- court_processing_service.py
- docket_processing_orchestrator_service.py
- query_service.py

```python
# src/pacer/pacer_court_service.py (450 lines)
class PacerCourtService(AsyncServiceBase):
    def __init__(self,
                 court_processor: CourtProcessor,
                 docket_orchestrator: DocketOrchestrator,
                 query_engine: QueryEngine):
        # Components injected via DI
```

Components:
```
components/court_processing/
├── court_processor.py (350 lines)
├── docket_orchestrator.py (400 lines)
└── query_engine.py (300 lines)
```

#### Group D: Configuration & Navigation Services  
**Target**: `PacerSystemService` facade (≤400 lines)

Consolidates:
- configuration_service.py
- navigation_service.py
- authentication_service.py

```python
# src/pacer/pacer_system_service.py (400 lines)
class PacerSystemService(AsyncServiceBase):
    def __init__(self,
                 config_manager: ConfigManager,
                 navigator: Navigator,
                 authenticator: Authenticator):
        # Components injected via DI
```

Components:
```
components/configuration/
├── config_manager.py (250 lines)
├── config_validator.py (150 lines)

components/navigation/
├── navigator.py (300 lines)
├── route_handler.py (200 lines)

components/authentication/
├── authenticator.py (300 lines)
├── session_manager.py (250 lines)
```

#### Group E: Processing Services
**Target**: `PacerProcessingService` facade (≤450 lines)

Consolidates:
- html_processing_service.py
- transfer_service.py
- interactive_service.py

```python
# src/pacer/pacer_processing_service.py (450 lines)
class PacerProcessingService(AsyncServiceBase):
    def __init__(self,
                 html_processor: HTMLProcessor,
                 transfer_handler: TransferHandler,
                 interactive_handler: InteractiveHandler):
        # Components injected via DI
```

Components:
```
components/processing/
├── html_processor.py (350 lines)
├── html_parser.py (300 lines)

components/transfer/
├── transfer_handler.py (350 lines)
├── transfer_validator.py (200 lines)

components/interactive/
├── interactive_handler.py (300 lines)
├── user_interface.py (250 lines)
```

#### Group F: Job Management Services
**Target**: `PacerJobService` facade (≤400 lines)

Consolidates:
- jobs/job_orchestration_service.py
- jobs/job_runner_service.py

```python
# src/pacer/pacer_job_service.py (400 lines)
class PacerJobService(AsyncServiceBase):
    def __init__(self,
                 job_orchestrator: JobOrchestrator,
                 job_runner: JobRunner,
                 job_scheduler: JobScheduler):
        # Components injected via DI
```

Components:
```
components/jobs/
├── job_orchestrator.py (350 lines)
├── job_runner.py (300 lines)
├── job_scheduler.py (250 lines)
└── job_models.py (150 lines)
```

### Phase 3: Final Service Structure

After consolidation, the PACER module will have:

**10 Facade Services** (down from 24+ services):
1. PacerBrowserService (existing)
2. PacerFileService (existing)
3. PacerDownloadService (existing)
4. PacerVerificationService (existing)
5. PacerCoreService (new - consolidates 3)
6. PacerDataService (new - consolidates 3)
7. PacerCourtService (new - consolidates 3)
8. PacerSystemService (new - consolidates 3)
9. PacerProcessingService (new - consolidates 3)
10. PacerJobService (new - consolidates 2)

**Component Organization**:
```
src/pacer/
├── facades/                    # All facade services
│   ├── __init__.py
│   ├── pacer_browser_service.py
│   ├── pacer_file_service.py
│   ├── pacer_download_service.py
│   ├── pacer_verification_service.py
│   ├── pacer_core_service.py
│   ├── pacer_data_service.py
│   ├── pacer_court_service.py
│   ├── pacer_system_service.py
│   ├── pacer_processing_service.py
│   └── pacer_job_service.py
│
├── components/                 # All implementation components
│   ├── analytics/
│   ├── authentication/
│   ├── browser/
│   ├── case_processing/
│   ├── classification/
│   ├── configuration/
│   ├── court_processing/
│   ├── download/
│   ├── export/
│   ├── file_operations/
│   ├── interactive/
│   ├── jobs/
│   ├── navigation/
│   ├── processing/
│   ├── query/
│   ├── report/
│   ├── transfer/
│   ├── validation/
│   └── verification/
│
├── interfaces/                 # Protocol definitions
│   ├── __init__.py
│   └── pacer_protocols.py
│
└── __init__.py                # Module exports
```

## DI Container Registration Strategy

### Service Registration Pattern

```python
# src/infrastructure/di/pacer_registry.py

from src.infrastructure.di.service_descriptor import ServiceDescriptor
from src.infrastructure.di.registry import ServiceRegistry

def register_pacer_services(registry: ServiceRegistry):
    """Register all PACER facade services in DI container."""
    
    # Register components first (not exposed publicly)
    _register_components(registry)
    
    # Register facade services (public API)
    registry.register('PacerBrowserService', ServiceDescriptor(
        service_class='src.pacer.facades.pacer_browser_service.PacerBrowserService',
        dependencies={
            'browser_manager': 'BrowserManager',
            'auth_handler': 'AuthenticationHandler',
            'navigation_handler': 'NavigationHandler'
        },
        lifecycle='singleton'
    ))
    
    registry.register('PacerCoreService', ServiceDescriptor(
        service_class='src.pacer.facades.pacer_core_service.PacerCoreService',
        dependencies={
            'case_processor': 'CaseProcessor',
            'row_processor': 'RowProcessor',
            'case_classifier': 'CaseClassifier'
        },
        lifecycle='singleton'
    ))
    
    # ... register other facades

def _register_components(registry: ServiceRegistry):
    """Register internal components (not part of public API)."""
    
    registry.register('CaseProcessor', ServiceDescriptor(
        service_class='src.pacer.components.case_processing.case_processor.CaseProcessor',
        dependencies={
            'logger': 'Logger',
            'config': 'Config'
        },
        lifecycle='transient',
        visibility='internal'  # Not exposed in public API
    ))
    
    # ... register other components
```

### Lifecycle Management

**Singleton Services** (shared instance):
- All facade services
- Configuration managers
- Connection pools

**Transient Services** (new instance per request):
- Processing components
- Temporary handlers
- Request-specific processors

**Scoped Services** (per-session):
- Browser contexts
- Authentication sessions
- Transaction handlers

## Migration Plan

### Week 1: Foundation & Planning
**Day 1-2**: 
- [ ] Set up new directory structure
- [ ] Create interfaces/protocols
- [ ] Design DI container registration

**Day 3-5**:
- [ ] Migrate existing `_components` to `components/`
- [ ] Update imports in existing facades
- [ ] Ensure all tests pass

### Week 2: Service Consolidation
**Day 1-2**: Core Services
- [ ] Create PacerCoreService facade
- [ ] Extract and organize components
- [ ] Update DI registration

**Day 3-4**: Data Services
- [ ] Create PacerDataService facade
- [ ] Extract analytics/report/export components
- [ ] Update DI registration

**Day 5**: Court Operations
- [ ] Create PacerCourtService facade
- [ ] Extract court/docket/query components
- [ ] Update DI registration

### Week 3: Final Consolidation
**Day 1-2**: System Services
- [ ] Create PacerSystemService facade
- [ ] Extract config/nav/auth components
- [ ] Update DI registration

**Day 3-4**: Processing Services
- [ ] Create PacerProcessingService facade
- [ ] Extract HTML/transfer/interactive components
- [ ] Update DI registration

**Day 5**: Job Services
- [ ] Create PacerJobService facade
- [ ] Extract job management components
- [ ] Update DI registration

### Week 4: Testing & Documentation
**Day 1-3**: Testing
- [ ] Update all unit tests
- [ ] Create integration tests for facades
- [ ] Performance testing

**Day 4-5**: Documentation
- [ ] Update API documentation
- [ ] Create component diagrams
- [ ] Migration guide for consumers

## Implementation Guidelines

### 1. Facade Design Principles
```python
class PacerXxxService(AsyncServiceBase):
    """
    Facade service providing unified access to Xxx operations.
    
    This service coordinates between multiple components to provide
    a complete Xxx interface for PACER processing.
    """
    
    def __init__(self, 
                 component_a: ComponentA,
                 component_b: ComponentB,
                 logger: LoggerProtocol = None,
                 config: Dict[str, Any] = None):
        """All dependencies injected, no direct imports."""
        super().__init__(logger, config)
        self._component_a = component_a
        self._component_b = component_b
    
    async def public_method(self, params: Dict) -> Result:
        """Delegate to appropriate component."""
        try:
            # Coordinate between components
            step1 = await self._component_a.process(params)
            step2 = await self._component_b.transform(step1)
            return step2
        except Exception as e:
            raise PacerServiceError(f"Failed in public_method: {e}")
```

### 2. Component Design Principles
```python
class SpecificProcessor:
    """
    Internal component handling specific processing logic.
    
    Not registered in DI container directly - injected into facades.
    """
    
    def __init__(self, logger: LoggerProtocol, config: Dict[str, Any]):
        self._logger = logger
        self._config = config
        # Component-specific initialization
    
    async def process(self, data: Dict) -> ProcessedData:
        """Focused processing logic."""
        # Implementation details
        # Should be 300-400 lines max
```

### 3. Size Management Strategies

**When approaching size limits**:

1. **Single Responsibility Principle**: Each component should have ONE reason to change
2. **Complete Functionality**: Each split should be a complete, usable unit
3. **Avoid Micro-Components**: Don't create files under 150 lines unless necessary
4. **Maintain Cohesion**: Keep related logic together

**Practical Splitting Decision Tree**:
```
Is component > 400 lines?
    ├─ NO → Keep as is
    └─ YES → Does it have multiple distinct responsibilities?
              ├─ NO → Keep as is (single large responsibility is OK)
              └─ YES → Split by responsibility (2-3 components max)
```

**Example: Appropriate vs Over-Granular Splitting**:
```
APPROPRIATE (600-line component with 2 responsibilities):
- case_processor.py (350 lines) - Case processing logic
- case_validator.py (250 lines) - Case validation logic

OVER-GRANULAR (avoid this):
- case_processor.py (150 lines)
- case_utils.py (80 lines)
- case_helpers.py (70 lines)
- case_constants.py (40 lines)
- case_validators.py (100 lines)
- case_transformers.py (90 lines)
```

### 4. Testing Strategy

**Unit Tests**: Test components in isolation
```python
# tests/pacer/components/case_processing/test_case_processor.py
def test_case_processor():
    processor = CaseProcessor(mock_logger, mock_config)
    result = await processor.process(test_data)
    assert result.status == 'processed'
```

**Integration Tests**: Test facades with real components
```python
# tests/pacer/facades/test_pacer_core_service.py
def test_pacer_core_service_integration():
    # Use DI container to create service with real components
    service = container.resolve('PacerCoreService')
    result = await service.process_case(test_case)
    assert result.is_valid
```

**Contract Tests**: Verify facade interfaces
```python
# tests/pacer/contracts/test_facade_contracts.py
def test_facade_implements_protocol():
    assert isinstance(PacerCoreService, PacerCoreProtocol)
```

## Risk Mitigation

### Identified Risks & Mitigations

1. **Risk**: Breaking existing consumers
   - **Mitigation**: Maintain backward compatibility during migration
   - **Strategy**: Keep old service imports working via aliases

2. **Risk**: Component size exceeding limits
   - **Mitigation**: Proactive splitting strategy
   - **Strategy**: Monitor file sizes continuously, split early

3. **Risk**: Circular dependencies
   - **Mitigation**: Strict layering rules
   - **Strategy**: Facades → Components → Utils (no reverse dependencies)

4. **Risk**: Performance degradation
   - **Mitigation**: Performance testing at each phase
   - **Strategy**: Benchmark before/after each consolidation

5. **Risk**: Lost functionality
   - **Mitigation**: Comprehensive test coverage
   - **Strategy**: No code deletion until tests pass

## Success Metrics

### Quantitative Metrics
- **Service Count**: 24+ → 10 facades (58% reduction)
- **File Size**: All files ≤500 lines (facades), ≤400 lines (components)
- **Test Coverage**: Maintain ≥90% coverage
- **Performance**: No degradation (±5% tolerance)
- **Import Depth**: Maximum 3 levels (facade → component → util)

### Qualitative Metrics
- **Code Clarity**: Clear separation of concerns
- **Maintainability**: Easy to locate and modify functionality
- **Testability**: Components testable in isolation
- **Documentation**: Self-documenting structure
- **Developer Experience**: Simplified API surface

## Rollback Plan

If issues arise during migration:

1. **Immediate**: Revert to previous commit
2. **Partial**: Keep working facades, revert problematic ones
3. **Gradual**: Run old and new services in parallel
4. **Full**: Restore from backup branch

## Post-Migration Tasks

1. **Documentation Updates**
   - API reference generation
   - Architecture diagrams
   - Developer guide

2. **Performance Optimization**
   - Profile facade methods
   - Optimize component interactions
   - Cache frequently used results

3. **Monitoring Setup**
   - Service health checks
   - Performance metrics
   - Error tracking

4. **Training**
   - Team walkthrough
   - Code review sessions
   - Best practices documentation

## Appendix A: Current to Target Mapping

| Current Service | Target Facade | Components |
|-----------------|---------------|------------|
| case_processing_service.py | PacerCoreService | case_processor.py |
| row_processing_service.py | PacerCoreService | row_processor.py |
| case_classification_service.py | PacerCoreService | case_classifier.py |
| analytics_service.py | PacerDataService | analytics_engine.py |
| report_service.py | PacerDataService | report_generator.py |
| export_service.py | PacerDataService | export_handler.py |
| court_processing_service.py | PacerCourtService | court_processor.py |
| docket_processing_orchestrator_service.py | PacerCourtService | docket_orchestrator.py |
| query_service.py | PacerCourtService | query_engine.py |
| configuration_service.py | PacerSystemService | config_manager.py |
| navigation_service.py | PacerSystemService | navigator.py |
| authentication_service.py | PacerSystemService | authenticator.py |
| html_processing_service.py | PacerProcessingService | html_processor.py |
| transfer_service.py | PacerProcessingService | transfer_handler.py |
| interactive_service.py | PacerProcessingService | interactive_handler.py |
| job_orchestration_service.py | PacerJobService | job_orchestrator.py |
| job_runner_service.py | PacerJobService | job_runner.py |

## Appendix B: DI Container Configuration Example

```python
# src/infrastructure/di/container_config.py

from src.infrastructure.di.container import DIContainer
from src.infrastructure.di.pacer_registry import register_pacer_services

def configure_container() -> DIContainer:
    """Configure the application DI container."""
    
    container = DIContainer()
    
    # Register infrastructure services
    container.register_singleton('Logger', create_logger)
    container.register_singleton('Config', load_config)
    container.register_singleton('ConnectionPool', create_connection_pool)
    
    # Register PACER services
    register_pacer_services(container.registry)
    
    # Register other domain services
    # ...
    
    return container

# Usage in application
container = configure_container()
pacer_core = container.resolve('PacerCoreService')
```

## Appendix C: File Size Validation Script

```python
#!/usr/bin/env python3
"""Validate that all files meet size constraints."""

import os
from pathlib import Path

def validate_file_sizes(root_dir: Path):
    """Check that all Python files meet size constraints."""
    
    violations = []
    
    for file_path in root_dir.rglob('*.py'):
        with open(file_path, 'r') as f:
            line_count = len(f.readlines())
        
        if 'facades' in str(file_path) and line_count > 500:
            violations.append(f"Facade too large: {file_path} ({line_count} lines)")
        elif 'components' in str(file_path) and line_count > 400:
            violations.append(f"Component too large: {file_path} ({line_count} lines)")
    
    if violations:
        print("Size constraint violations found:")
        for violation in violations:
            print(f"  - {violation}")
        return False
    
    print("All files meet size constraints ✓")
    return True

if __name__ == "__main__":
    validate_file_sizes(Path("src/pacer"))
```

## Next Steps

1. **Review and Approval**: Review this plan with the team
2. **Environment Setup**: Create refactoring branch and worktree
3. **Begin Phase 1**: Start with component migration
4. **Daily Progress**: Track progress against checklist
5. **Continuous Testing**: Run tests after each change

---

**Document Version**: 1.0
**Date**: 2024
**Author**: Development Team
**Status**: Ready for Implementation