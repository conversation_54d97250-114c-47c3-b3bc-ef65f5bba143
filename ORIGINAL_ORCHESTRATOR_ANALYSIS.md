# Original PACER Orchestrator Implementation Analysis

## Key Implementation Patterns from `orchestrator_original.py`

### 1. **Timeout Configurations**

#### Default Values
```python
DEFAULT_TIMEOUT_S = 30  # Main default timeout
DEFAULT_DELAY_S = 1.5   # Default delay between operations
```

#### BrowserService Timeout Setup
```python
self.browser_service = BrowserService(
    headless=config.get('headless', True),
    timeout_ms=config.get('timeout', self.DEFAULT_TIMEOUT_S) * 1000  # Convert to milliseconds
)
```

#### Specific Timeout Values Used Throughout
- **Element visibility waits**: 1000-7000ms (1-7 seconds)
- **Form interactions**: 3000-10000ms (3-10 seconds)
- **Navigation expectations**: `self.browser_service.timeout_ms + 5000-30000ms` (35-60 seconds)
- **Run Report navigation**: `self.browser_service.timeout_ms + 30000ms` (60 seconds)
- **Download completion**: 600 seconds (10 minutes) - `download_save_as_completion_timeout_s`

#### Critical Navigation Timeouts
```python
# Context page expectations with extended timeouts
async with context.expect_page(timeout=main_navigator.timeout + 5000) as new_page_info:
    
# Navigation with extended timeouts  
async with page.expect_navigation(wait_until="domcontentloaded", 
                                 timeout=self.browser_service.timeout_ms + 30000):

# Docket link navigation
await current_page.goto(docket_link_href, wait_until='domcontentloaded',
                       timeout=self.browser_service.timeout_ms + 25000)
```

### 2. **Browser Context Management**

#### Context Creation Pattern
```python
context = await self.browser_service.new_context(
    download_path=str(base_temp_downloads_for_court)
)
main_page = await context.new_page()
main_navigator = PacerNavigator(main_page, processor_config['delay'], 
                               self.browser_service.timeout_ms)
```

#### Parallel Processing Context Management
- Each court gets its own isolated browser context
- Each context has a dedicated download directory: `base_temp_downloads_for_court`
- Multiple contexts run simultaneously via `asyncio.as_completed(active_tasks)`

#### Context Cleanup Pattern
```python
finally:
    if main_page and not main_page.is_closed(): 
        await main_page.close()
    if context:
        court_logger.debug(f"Closing main context.")
        await context.close()
        context = None
```

### 3. **Document Download Completion Logic**

#### Critical Download Wait Pattern from `pacer_document_downloader.py`
```python
# VERY IMPORTANT: This timeout must be long enough for largest expected file
download_completion_timeout_s = self.config.get('download_save_as_completion_timeout_s', 600)  # 10 minutes

# Wait for actual file save completion
await asyncio.wait_for(download_save_operation_complete_event.wait(),
                      timeout=download_completion_timeout_s)
```

#### Download Path Validation
- Uses `context_download_path` for browser contexts
- Each download gets a unique temporary directory per attempt
- Cleanup removes temp directories but preserves final downloads in correct locations

#### File Completion Verification
```python
# Check if downloaded in last 7 days by pattern
if await self.file_manager.check_if_downloaded_last_7_days_by_pattern(
    end_date_obj, court_id, clean_docket_pattern
):
    return ExistenceCheckResult(should_skip=True, reason="skipped_exists_local_7_days")
```

### 4. **Error Handling Patterns**

#### Comprehensive Exception Hierarchy
```python
try:
    # Main operation
except PlaywrightTimeoutError as pte:
    court_logger.error(f"Timeout during operation: {pte}", exc_info=False)
    if navigator and navigator.is_ready:
        await navigator.save_screenshot(f"timeout_at_step_{current_step}")
        
except PlaywrightError as pe:
    err_str = str(pe).lower()
    is_target_closed_error = "target page, context or browser has been closed" in err_str or \
                            "execution context was destroyed" in err_str
    if is_target_closed_error:
        court_logger.error("Critical browser closure detected")
        # Don't retry, mark as fatal
    else:
        # Regular PlaywrightError - can retry
        
except Exception as e:
    court_logger.error(f"Unexpected error: {e}", exc_info=True)
```

#### Critical Error Detection
```python
# Detect fatal browser/context errors that shouldn't be retried
if "net::err_connection_reset" in err_str or \
   "net::err_name_not_resolved" in err_str or \
   "net::err_timed_out" in err_str or \
   "target page, context or browser has been closed" in err_str or \
   "execution context was destroyed" in err_str:
    # Fatal - don't retry
```

#### Screenshot Strategy
- Save screenshots immediately on errors
- Use descriptive names with step context
- Check if page/navigator is still ready before screenshots

### 5. **Browser Service Configuration**

#### Key Browser Arguments (from `browser_service.py`)
```python
chromium_launch_args = [
    "--disable-gpu", "--window-size=1400,2500", "--no-sandbox",
    "--disable-dev-shm-usage", '--disable-pdf-extension', '--disable-extensions',
    '--disable-component-extensions-with-background-pages',
    '--disable-default-apps', '--disable-features=PDFViewer',
    # NUCLEAR KEYCHAIN SUPPRESSION
    '--use-mock-keychain', '--password-store=basic',
    '--disable-component-update', '--disable-background-networking',
    # ... extensive list for macOS compatibility
]
```

#### Context Options
```python
context_options = {
    "user_data_dir": user_data_dir_path,  # Unique temp dir per context
    "headless": self.headless,
    "args": chromium_launch_args,
    "accept_downloads": True,
    "downloads_path": download_path  # If specified
}

context = await self.playwright.chromium.launch_persistent_context(**context_options)
context.set_default_timeout(self.timeout_ms)
```

### 6. **Retry Logic**

#### Court-Level Retries
```python
max_attempts = processor_config.get('max_retries_per_court', self.DEFAULT_RETRIES_PER_COURT)
for attempt in range(max_attempts):
    try:
        # Full court processing
        attempt_success_flag = True
        break
    except Exception:
        if attempt < max_attempts - 1:
            await asyncio.sleep(self.config.get('delay_between_retries_s', 2.0))
```

#### Fatal Error Break Conditions
```python
if not task_overall_success and (
    "Playwright driver/node missing" in last_critical_exception_str or 
    "Fatal browser/context error" in last_critical_exception_str
):
    self.logger.info(f"Not retrying {court_id} due to fatal error.")
    break
```

### 7. **Page State Management**

#### Page Readiness Checks
```python
await page.wait_for_load_state("networkidle", timeout=10000)
await page.wait_for_load_state("domcontentloaded", timeout=main_navigator.timeout + 10000)
```

#### Element State Verification
```python
# Check attachment before interaction
await element.wait_for(state='attached', timeout=5000)
await element.wait_for(state='visible', timeout=7000)

# Verify after interaction
if await element.is_visible(timeout=1000):
    # Element still visible after action
```

## Key Success Factors

1. **Long Download Timeouts**: 10 minutes for download completion
2. **Extended Navigation Timeouts**: Base timeout + 25-30 seconds for complex navigations
3. **Isolated Contexts**: Each court gets independent browser context with unique download paths
4. **Comprehensive Error Detection**: Distinguishes between retryable and fatal errors
5. **Proper Resource Cleanup**: Always closes pages/contexts in finally blocks
6. **State Verification**: Checks element attachment and visibility before interactions
7. **Screenshot Documentation**: Captures state on every error for debugging

## Critical Implementation Notes

- **Download paths are context-specific** - each browser context has its own download directory
- **Timeouts are layered** - element timeouts are shorter, navigation timeouts are longer
- **Error handling is hierarchical** - TimeoutError → PlaywrightError → Exception
- **Resource cleanup is mandatory** - Pages and contexts always closed in finally blocks
- **State checks are defensive** - Always verify element states before interaction