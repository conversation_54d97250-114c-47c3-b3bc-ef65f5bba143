### Day 3: Migrate File, Download, and Verification Components

#### Step 3.1: Migrate File Components
```bash
# Move file components
mv src/pacer/_file_components/file_manager.py \
   src/pacer/components/file_operations/file_manager.py

mv src/pacer/_file_components/file_operations.py \
   src/pacer/components/file_operations/file_operations.py

# Remove old directory
rm -rf src/pacer/_file_components
```

#### Step 3.2: Check Component Sizes and Split if Needed
```python
# Script to check file sizes and identify splitting candidates
# tools/check_component_sizes.py

import os
from pathlib import Path

def check_component_sizes(component_dir: Path):
    """Check component file sizes and suggest splits."""
    
    for py_file in component_dir.rglob("*.py"):
        if py_file.name == "__init__.py":
            continue
            
        with open(py_file, 'r') as f:
            lines = f.readlines()
            line_count = len(lines)
            
        if line_count > 400:
            print(f"⚠️  Large component: {py_file.relative_to(component_dir)}")
            print(f"   Lines: {line_count}")
            print(f"   Action: Review for single responsibility split")
            
            # Analyze for potential splits
            analyze_for_split(py_file, lines)

def analyze_for_split(file_path: Path, lines: list):
    """Analyze file for splitting opportunities."""
    
    class_count = sum(1 for line in lines if line.strip().startswith("class "))
    method_count = sum(1 for line in lines if line.strip().startswith("def ") 
                      or line.strip().startswith("async def "))
    
    if class_count > 1:
        print(f"   → Multiple classes ({class_count}): Consider splitting")
    if method_count > 15:
        print(f"   → Many methods ({method_count}): Check for multiple responsibilities")

if __name__ == "__main__":
    check_component_sizes(Path("src/pacer/components"))
```

#### Step 3.3: Split Large Components (Example)
```python
# If file_operations.py is 600 lines with mixed responsibilities:

# Split into two focused components:

# src/pacer/components/file_operations/case_file_handler.py (350 lines)
class CaseFileHandler(BaseComponent):
    """Handles case-specific file operations."""
    
    async def save_case_data(self, case_data: Dict, path: Path) -> bool:
        """Save case data to file."""
        # Implementation...
    
    async def load_case_data(self, case_id: str) -> Dict:
        """Load case data from file."""
        # Implementation...

# src/pacer/components/file_operations/s3_file_handler.py (250 lines)
class S3FileHandler(BaseComponent):
    """Handles S3 file operations."""
    
    async def upload_to_s3(self, file_path: Path, bucket: str) -> str:
        """Upload file to S3."""
        # Implementation...
    
    async def download_from_s3(self, s3_key: str, local_path: Path) -> bool:
        """Download file from S3."""
        # Implementation...
```

**IMPORTANT**
**DI CONTAINER INJECTION IS MANDATORY. NO LEGACY FALLBACKS. NO MANUAL INSTANTIATION. ONLY EXCEPTIONS**:
1. The Application Entry Point (Composition Root)
2. Data Transfer Objects (DTOs) and Value Objects
3. Objects Created by a Factory Based on Runtime Data In this case, you don't inject the final object. Instead, you inject a Factory for it. The factory itself has its dependencies injected by the container, but its create method may manually instantiate an object using the runtime data.

**DESIGN PATTERNS**: Architecture follows the following @DESIGN_PATTERNS.md

**JOBS ARCHITECTURE**: Jobs architecture, isolated context and processing should be preserved.

**NO FEATURE FLAGS**: This is a direct changeover...

**COMPONENT CREATION8**:
1. ALL code for this functionality should exist in the codebase. BEFORE writing new code, check if it exists
in the codebase and use it.
2. If process_widget.py exists, do not create enhance_process_widget.py. UPDATE enhance original component.  

- If you get an error when spawning an agent, select the most appropriate agent for the task from agents in the error message.
- Instead of using:
  - Analyst use `code-analyzer`.
  - Coordinator use `task-orchestrator`.
  - Optimizer use `perf-analyzer`.
  - Documenter use `api-docs`.
  - Monitor use `performance-benchmarker`.
  - Specialist use `system-architect`.
  - Architect use `system-architect`.
- We have moved to `uv pip`. Do NOT use `conda env`.
