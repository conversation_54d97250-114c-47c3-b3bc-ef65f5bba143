**OBJECTIVE**: The workflow after running `@run_pipeline.sh --config workflows/transform`. I am getting this error.

```
                        TypeError: Float types are not supported. Use Decimal types instead.                                                                                                                                                                                                                           
❌ CRITICAL: DynamoDB put_item FAILED - FilingDate=08/18/2025, DocketNum=1:25-cv-09814
❌ Storage Error: Unexpected error putting item to Pacer: Float types are not supported. Use Decimal types instead.
❌ Error Context: {'table': 'Pacer', 'original_error': TypeError('Float types are not supported. Use Decimal types instead.')}
❌ Table: Pacer
[08/19/25 04:02:44] ERROR    PACER Repository: DynamoDB put_item failed with detailed error                                                                                                                                                                                                            component_base.py:112
                    ERROR    Unexpected error putting item to DynamoDB                             
```

**IMPORTANT**

**DI CONTAINER INJECTION IS MANDATORY. NO LEGACY FALLBACKS. NO MANUAL INSTANTIATION. ONLY EXCEPTiONS**:
1. The Application Entry Point (Composition Root)
2. Data Transfer Objects (DTOs) and Value Objects
3. Objects Created by a Factory Based on Runtime Data In this case, you don't inject the final object. Instead, you inject a Factory for it. The factory itself has its dependencies injected by the container, but its create method may manually instantiate an object using the runtime data.

**COMPONENT CREATION8**:
1. ALL code for this functionality should exist in the codebase. BEFORE writing new code, check if it exists
in the codebase and use it.
2. If process_widget.py exists, do not create enhance_process_widget.py. UPDATE enhance original component.  

- If you get an error when spawning an agent, select the most appropriate agent for the task from agents in the error message.
- Instead of using:
  - Analyst use `code-analyzer`.
  - Coordinator use `task-orchestrator`.
  - Optimizer use `perf-analyzer`.
  - Documenter use `api-docs`.
  - Monitor use `performance-benchmarker`.
  - Specialist use `system-architect`.
  - Architect use `system-architect`.
- We have moved to `uv pip`. Do NOT use `conda env`.
