# Facebook Ad Library API Setup Guide

## Overview

The FB Ads system uses Facebook's Ad Library API to fetch advertising data directly. This requires setting up a Facebook App and obtaining API credentials.

## Step 1: Create a Facebook App

1. Go to [Meta for Developers](https://developers.facebook.com/)
2. Click "Create App" 
3. Select "Business" as the app type
4. Fill in app details:
   - App Name: "LexGenius Ad Library"
   - App Contact Email: Your email
   - Business Manager Account: Select your business account
5. Create the app

## Step 2: Add Ad Library API Product

1. In your app dashboard, click "Add Product"
2. Find "Ad Library API" and click "Set Up"
3. Configure the product settings

## Step 3: Get Your Credentials

### App ID and App Secret
1. Go to app Settings > Basic
2. Copy your **App ID** 
3. Copy your **App Secret** (click "Show")

### Access Token
1. Go to Tools > Graph API Explorer
2. Select your app from dropdown
3. Add permissions: `ads_read`, `business_management`
4. Click "Generate Access Token"
5. Copy the generated token

### Ad Account ID (Optional)
1. Go to Facebook Business Manager
2. Navigate to Business Settings > Accounts > Ad Accounts
3. Copy your Ad Account ID (format: act_XXXXXXXXX)

## Step 4: Update Configuration

Update `config/fb_ads.yml` with your credentials:

```yaml
facebook_ads:
  enabled: True
  app_id: "****************"                    # Your App ID
  app_secret: "abcdef1234567890abcdef1234567890" # Your App Secret
  access_token: "EAAB..."                       # Your Access Token
  ad_account_id: "act_123456789"                # Your Ad Account ID (optional)
```

## Step 5: Test the Setup

Run the FB Ads processing:

```bash
python src/main.py --params config/fb_ads.yml
```

## Troubleshooting

### Common Issues

1. **Invalid Access Token**
   - Tokens expire, regenerate in Graph API Explorer
   - Ensure you have the correct permissions

2. **App Not Approved**
   - Submit app for review if using in production
   - Use test mode for development

3. **Rate Limiting**
   - Facebook has rate limits on API calls
   - Implement delays between requests

### Required Permissions

- `ads_read`: Read access to advertising data
- `business_management`: Access to business accounts

### API Documentation

- [Facebook Ad Library API](https://developers.facebook.com/docs/marketing-api/audiences/ad-library-api)
- [Facebook App Development](https://developers.facebook.com/docs/development/)

## Security Notes

- **Never commit credentials to git**
- Store credentials in environment variables for production
- Use .env files for local development
- Rotate tokens regularly

## Environment Variables Alternative

Instead of putting credentials in YAML, you can use environment variables:

```bash
export FACEBOOK_APP_ID="your_app_id"
export FACEBOOK_APP_SECRET="your_app_secret"
export FACEBOOK_ACCESS_TOKEN="your_access_token"
export FACEBOOK_AD_ACCOUNT_ID="your_ad_account_id"
```

Then reference them in the config:

```yaml
facebook_ads:
  enabled: True
  app_id: "${FACEBOOK_APP_ID}"
  app_secret: "${FACEBOOK_APP_SECRET}"
  access_token: "${FACEBOOK_ACCESS_TOKEN}"
  ad_account_id: "${FACEBOOK_AD_ACCOUNT_ID}"
```