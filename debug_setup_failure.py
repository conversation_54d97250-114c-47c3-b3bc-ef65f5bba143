#!/usr/bin/env python3
"""
Debug script to find exactly where the setup is failing.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestConfig:
    def __init__(self):
        self.config = {
            'camoufox': {
                'browser': {
                    'headless': False,
                    'timeout': 60000,
                    'viewport': {'width': 1920, 'height': 1080}
                },
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                    'refresh_before_expiry_seconds': 30
                },
                'anti_bot': {
                    'humanize': True,
                    'mouse_curves': True,
                    'typing_variation': True,
                    'disable_ad_blocker_detection': True,
                    'block_resources_for_performance': False
                },
                'search': {
                    'typing_delay': 120,
                    'suggestion_wait': 8000,
                    'capture_wait': 10
                }
            }
        }

async def debug_setup_steps():
    """Debug each setup step individually to find where it fails."""
    logger.info("🔍 DEBUG: Testing each setup step individually")
    
    config = TestConfig().config
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )
    
    try:
        # Step 1: Create session
        logger.info("=" * 60)
        logger.info("🚀 STEP 1: Creating browser session")
        logger.info("=" * 60)
        success = await session_manager.create_new_session()
        if not success:
            logger.error("❌ FAILED at Step 1: Browser session creation")
            return False
        logger.info("✅ PASSED: Browser session created")
        
        # Step 2: Check initial page state
        logger.info("=" * 60)
        logger.info("🔍 STEP 2: Checking if on Ad Library page")
        logger.info("=" * 60)
        is_on_page = await session_manager._is_on_ad_library_page()
        logger.info(f"Current URL: {session_manager.page.url}")
        logger.info(f"Is on Ad Library page: {is_on_page}")
        
        # Step 3: Try country dropdown individually
        logger.info("=" * 60)
        logger.info("🇺🇸 STEP 3: Testing country dropdown selection")
        logger.info("=" * 60)
        try:
            await session_manager._select_country_dropdown()
            logger.info("✅ PASSED: Country dropdown selection")
        except Exception as e:
            logger.error(f"❌ FAILED at Step 3: Country dropdown - {e}")
            logger.info("🔍 Let's check what elements are actually visible...")
            
            # Check what's actually on the page
            try:
                country_dropdown = await session_manager.page.query_selector('div#js_o')
                if country_dropdown:
                    is_visible = await country_dropdown.is_visible()
                    logger.info(f"Country dropdown found: visible={is_visible}")
                else:
                    logger.info("❌ Country dropdown div#js_o not found")
                    
                # List all visible elements for debugging
                all_elements = await session_manager.page.query_selector_all('div')
                logger.info(f"Found {len(all_elements)} div elements total")
                
            except Exception as debug_e:
                logger.error(f"❌ Error during debugging: {debug_e}")
            
            return False
        
        # Step 4: Try ad category dropdown individually
        logger.info("=" * 60)
        logger.info("📂 STEP 4: Testing ad category dropdown selection")
        logger.info("=" * 60)
        try:
            await session_manager._select_ad_category_dropdown()
            logger.info("✅ PASSED: Ad category dropdown selection")
        except Exception as e:
            logger.error(f"❌ FAILED at Step 4: Ad category dropdown - {e}")
            return False
        
        # Step 5: Try search input
        logger.info("=" * 60)
        logger.info("🔍 STEP 5: Testing search functionality")
        logger.info("=" * 60)
        try:
            search_success = await session_manager._search_advertiser("Morgan & Morgan")
            if search_success:
                logger.info("✅ PASSED: Search functionality")
            else:
                logger.error("❌ FAILED at Step 5: Search returned False")
                return False
        except Exception as e:
            logger.error(f"❌ FAILED at Step 5: Search - {e}")
            return False
        
        logger.info("🎉 ALL STEPS PASSED! Setup is working correctly.")
        return True
        
    except Exception as e:
        logger.error(f"❌ Unexpected error in debug: {e}")
        return False
    finally:
        logger.info("🧹 Closing browser")
        await session_manager.cleanup()

async def main():
    logger.info("🚀 Debug Setup Failure")
    logger.info("This will test each setup step individually to find the failure point")
    logger.info("=" * 80)
    
    success = await debug_setup_steps()
    
    if success:
        logger.info("✅ All setup steps are working")
    else:
        logger.error("❌ Found the failure point - check logs above")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Debug interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Debug failed: {e}")
        sys.exit(1)