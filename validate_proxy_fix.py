#!/usr/bin/env python3
"""Validate that the proxy configuration fix works correctly."""

import os
import sys
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Run the proxy configuration validation."""
    
    print("=== Proxy Configuration Fix Validation ===\n")
    
    # Check environment variables
    required_vars = [
        'OXY_LABS_MOBILE_PASSWORD',
        'OXY_LABS_MOBILE_USERNAME',
        'OXY_LABS_RESIDENTIAL_PASSWORD',
        'OXY_LABS_RESIDENTIAL_USERNAME'
    ]
    
    print("1. Checking required environment variables...")
    missing_vars = []
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            print(f"  ✅ {var}: Set")
        else:
            print(f"  ❌ {var}: Missing")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\nError: Missing environment variables: {missing_vars}")
        print("Please set these variables in your .env file")
        return False
    
    # Test configuration loading
    print("\n2. Testing configuration loading...")
    try:
        from src.config_models.loader import load_config
        
        config = load_config('fb_ads')
        print("  ✅ Configuration loaded successfully")
        
        # Check proxy config fields
        proxy_fields = {
            'oxy_labs_mobile_username': config.oxy_labs_mobile_username,
            'oxy_labs_mobile_password': config.oxy_labs_mobile_password,
            'oxy_labs_residential_username': config.oxy_labs_residential_username,
            'oxy_labs_residential_password': config.oxy_labs_residential_password,
        }
        
        print("  Proxy configuration fields:")
        for field, value in proxy_fields.items():
            if value:
                if isinstance(value, str) and value.startswith('${'):
                    print(f"    ❌ {field}: NOT EXPANDED - {value}")
                else:
                    print(f"    ✅ {field}: OK (length: {len(str(value))})")
            else:
                print(f"    ⚠️  {field}: Empty")
        
    except Exception as e:
        print(f"  ❌ Configuration loading failed: {e}")
        return False
    
    # Test ProxyManager
    print("\n3. Testing ProxyManager...")
    try:
        from src.services.scraping.proxy.proxy_manager import ProxyManager
        
        # Create config dict
        config_dict = config.model_dump()
        
        # Create ProxyManager
        proxy_manager = ProxyManager(
            config=config_dict,
            logger=logger,
            proxy_provider=None
        )
        
        print("  ✅ ProxyManager created successfully")
        
        # Check if proxies were generated
        if proxy_manager.proxies:
            sample_proxy = proxy_manager.proxies[0]
            print(f"  ✅ Generated {len(proxy_manager.proxies)} proxies")
            print(f"  Sample proxy username: {sample_proxy.username}")
            
            # Check if password is properly expanded
            if sample_proxy.password.startswith('${'):
                print(f"  ❌ Proxy password still unexpanded: {sample_proxy.password}")
                return False
            else:
                print(f"  ✅ Proxy password expanded (length: {len(sample_proxy.password)})")
                
            # Test proxy URL format
            proxy_url = sample_proxy.to_url()
            if '${' in proxy_url:
                print(f"  ❌ Proxy URL contains unexpanded variables: {proxy_url}")
                return False
            else:
                print("  ✅ Proxy URL properly formatted")
                
        else:
            print("  ❌ No proxies generated")
            return False
            
    except Exception as e:
        print(f"  ❌ ProxyManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n🎉 All tests passed! Proxy configuration fix is working correctly.")
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)