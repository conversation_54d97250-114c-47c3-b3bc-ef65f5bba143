# PACER Refactoring - Detailed Phase Implementation Plan

## Overview
This document provides step-by-step implementation details for each phase of the PACER service refactoring to achieve a thin facade pattern with DI container injection.

---

## PHASE 1: Component Migration and Organization
**Duration**: 3-4 days  
**Goal**: Migrate `_components` to `components/` and establish new structure

### Day 1: Setup and Directory Structure

#### Step 1.1: Create New Directory Structure
```bash
# Create the new components directory structure
mkdir -p src/pacer/components/{browser,file_operations,download,verification,processing}
mkdir -p src/pacer/components/{analytics,authentication,case_processing,classification}
mkdir -p src/pacer/components/{configuration,court_processing,export,navigation}
mkdir -p src/pacer/components/{query,report,transfer,validation,interactive,jobs}

# Create facades directory
mkdir -p src/pacer/facades

# Create interfaces directory
mkdir -p src/pacer/interfaces
```

#### Step 1.2: Create Protocol Definitions
```python
# src/pacer/interfaces/pacer_protocols.py

from typing import Protocol, Dict, Any, Optional, List
from abc import abstractmethod

class PacerServiceProtocol(Protocol):
    """Base protocol for all PACER facade services."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the service."""
        ...
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        ...

class PacerBrowserProtocol(Protocol):
    """Protocol for browser operations."""
    
    @abstractmethod
    async def create_context(self, config: Dict[str, Any]) -> Any:
        """Create browser context."""
        ...
    
    @abstractmethod
    async def authenticate(self, username: str, password: str) -> bool:
        """Authenticate with PACER."""
        ...
    
    @abstractmethod
    async def navigate_to_court(self, court_id: str) -> bool:
        """Navigate to specific court."""
        ...

# Add more protocol definitions for each facade...
```

#### Step 1.3: Create Base Component Class
```python
# src/pacer/components/base_component.py

import logging
from typing import Dict, Any, Optional

class BaseComponent:
    """Base class for all PACER components."""
    
    def __init__(self, logger: Optional[logging.Logger] = None, 
                 config: Optional[Dict[str, Any]] = None):
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.config = config or {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize component resources."""
        if not self._initialized:
            await self._setup()
            self._initialized = True
    
    async def _setup(self) -> None:
        """Override in subclasses for specific setup."""
        pass
    
    async def cleanup(self) -> None:
        """Cleanup component resources."""
        if self._initialized:
            await self._teardown()
            self._initialized = False
    
    async def _teardown(self) -> None:
        """Override in subclasses for specific cleanup."""
        pass
```

### Day 2: Migrate Browser Components

#### Step 2.1: Move Browser Components
```bash
# Move existing browser components
mv src/pacer/_browser_components/browser_manager.py \
   src/pacer/components/browser/browser_manager.py

mv src/pacer/_browser_components/authentication_handler.py \
   src/pacer/components/browser/authentication_handler.py

mv src/pacer/_browser_components/navigation_handler.py \
   src/pacer/components/browser/navigation_handler.py

# Create __init__.py
echo "from .browser_manager import BrowserManager
from .authentication_handler import AuthenticationHandler
from .navigation_handler import NavigationHandler

__all__ = ['BrowserManager', 'AuthenticationHandler', 'NavigationHandler']" \
> src/pacer/components/browser/__init__.py
```

#### Step 2.2: Update Browser Component Imports
```python
# src/pacer/components/browser/browser_manager.py

# Old imports
# from src.pacer._browser_components.base import ComponentBase

# New imports
from src.pacer.components.base_component import BaseComponent

class BrowserManager(BaseComponent):
    """Manages browser lifecycle and contexts."""
    
    def __init__(self, logger=None, config=None):
        super().__init__(logger, config)
        # Component implementation...
```

#### Step 2.3: Update Browser Facade
```python
# src/pacer/facades/pacer_browser_service.py

# Old imports
# from src.pacer._browser_components.browser_manager import BrowserManager

# New imports
from src.pacer.components.browser import (
    BrowserManager,
    AuthenticationHandler,
    NavigationHandler
)
from src.pacer.interfaces.pacer_protocols import PacerBrowserProtocol

class PacerBrowserService(AsyncServiceBase, PacerBrowserProtocol):
    """Facade for unified browser operations."""
    
    def __init__(self,
                 browser_manager: BrowserManager,
                 auth_handler: AuthenticationHandler,
                 navigation_handler: NavigationHandler,
                 logger=None,
                 config=None):
        super().__init__(logger, config)
        self._browser_manager = browser_manager
        self._auth_handler = auth_handler
        self._navigation_handler = navigation_handler
```

### Day 3: Migrate File, Download, and Verification Components

#### Step 3.1: Migrate File Components
```bash
# Move file components
mv src/pacer/_file_components/file_manager.py \
   src/pacer/components/file_operations/file_manager.py

mv src/pacer/_file_components/file_operations.py \
   src/pacer/components/file_operations/file_operations.py

# Remove old directory
rm -rf src/pacer/_file_components
```

#### Step 3.2: Check Component Sizes and Split if Needed
```python
# Script to check file sizes and identify splitting candidates
# tools/check_component_sizes.py

import os
from pathlib import Path

def check_component_sizes(component_dir: Path):
    """Check component file sizes and suggest splits."""
    
    for py_file in component_dir.rglob("*.py"):
        if py_file.name == "__init__.py":
            continue
            
        with open(py_file, 'r') as f:
            lines = f.readlines()
            line_count = len(lines)
            
        if line_count > 400:
            print(f"⚠️  Large component: {py_file.relative_to(component_dir)}")
            print(f"   Lines: {line_count}")
            print(f"   Action: Review for single responsibility split")
            
            # Analyze for potential splits
            analyze_for_split(py_file, lines)

def analyze_for_split(file_path: Path, lines: list):
    """Analyze file for splitting opportunities."""
    
    class_count = sum(1 for line in lines if line.strip().startswith("class "))
    method_count = sum(1 for line in lines if line.strip().startswith("def ") 
                      or line.strip().startswith("async def "))
    
    if class_count > 1:
        print(f"   → Multiple classes ({class_count}): Consider splitting")
    if method_count > 15:
        print(f"   → Many methods ({method_count}): Check for multiple responsibilities")

if __name__ == "__main__":
    check_component_sizes(Path("src/pacer/components"))
```

#### Step 3.3: Split Large Components (Example)
```python
# If file_operations.py is 600 lines with mixed responsibilities:

# Split into two focused components:

# src/pacer/components/file_operations/case_file_handler.py (350 lines)
class CaseFileHandler(BaseComponent):
    """Handles case-specific file operations."""
    
    async def save_case_data(self, case_data: Dict, path: Path) -> bool:
        """Save case data to file."""
        # Implementation...
    
    async def load_case_data(self, case_id: str) -> Dict:
        """Load case data from file."""
        # Implementation...

# src/pacer/components/file_operations/s3_file_handler.py (250 lines)
class S3FileHandler(BaseComponent):
    """Handles S3 file operations."""
    
    async def upload_to_s3(self, file_path: Path, bucket: str) -> str:
        """Upload file to S3."""
        # Implementation...
    
    async def download_from_s3(self, s3_key: str, local_path: Path) -> bool:
        """Download file from S3."""
        # Implementation...
```

### Day 4: Remove Old Directories and Update All Imports

#### Step 4.1: Update Import Script
```python
# tools/update_imports.py

import re
from pathlib import Path

def update_imports(src_dir: Path):
    """Update all imports from _components to components."""
    
    replacements = [
        (r'from src\.pacer\._browser_components', 'from src.pacer.components.browser'),
        (r'from src\.pacer\._file_components', 'from src.pacer.components.file_operations'),
        (r'from src\.pacer\._download_components', 'from src.pacer.components.download'),
        (r'from src\.pacer\._verification_components', 'from src.pacer.components.verification'),
        (r'import src\.pacer\._(\w+)_components', r'import src.pacer.components.\1'),
    ]
    
    for py_file in src_dir.rglob("*.py"):
        content = py_file.read_text()
        original = content
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        if content != original:
            py_file.write_text(content)
            print(f"Updated imports in: {py_file}")

if __name__ == "__main__":
    update_imports(Path("src"))
    update_imports(Path("tests"))
```

#### Step 4.2: Verify No Broken Imports
```bash
# Run import check
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

# Try importing all facades
from src.pacer.facades.pacer_browser_service import PacerBrowserService
from src.pacer.facades.pacer_file_service import PacerFileService
from src.pacer.facades.pacer_download_service import PacerDownloadService
from src.pacer.facades.pacer_verification_service import PacerVerificationService

print('✅ All imports successful')
"
```

---

## PHASE 2: Service Consolidation
**Duration**: 5-6 days  
**Goal**: Create consolidated facade services

### Day 5-6: Create PacerCoreService

#### Step 5.1: Analyze Services to Consolidate
```python
# tools/analyze_services.py

from pathlib import Path

def analyze_service_for_consolidation(service_files: list):
    """Analyze services to understand consolidation complexity."""
    
    total_lines = 0
    public_methods = []
    dependencies = set()
    
    for service_file in service_files:
        with open(service_file, 'r') as f:
            lines = f.readlines()
            total_lines += len(lines)
            
            for line in lines:
                # Find public methods
                if line.strip().startswith('async def ') and not line.strip().startswith('async def _'):
                    method_name = line.strip().split('(')[0].replace('async def ', '')
                    public_methods.append(method_name)
                
                # Find imports/dependencies
                if line.strip().startswith('from ') or line.strip().startswith('import '):
                    dependencies.add(line.strip())
    
    print(f"Total lines to consolidate: {total_lines}")
    print(f"Public methods: {len(public_methods)}")
    print(f"Unique dependencies: {len(dependencies)}")
    
    if total_lines > 1200:
        print("⚠️  Will need component extraction")
    
    return public_methods, dependencies

# Analyze core services
services_to_consolidate = [
    "src/pacer/case_processing_service.py",
    "src/pacer/row_processing_service.py",
    "src/pacer/case_classification_service.py"
]

methods, deps = analyze_service_for_consolidation(services_to_consolidate)
```

#### Step 5.2: Extract Components from Services
```python
# src/pacer/components/case_processing/case_processor.py

from typing import Dict, Any, Optional, List
from src.pacer.components.base_component import BaseComponent

class CaseProcessor(BaseComponent):
    """Processes individual case data."""
    
    def __init__(self, logger=None, config=None):
        super().__init__(logger, config)
        self._processing_rules = self._load_processing_rules()
    
    def _load_processing_rules(self) -> Dict:
        """Load case processing rules from configuration."""
        return self.config.get('processing_rules', {})
    
    async def process_case(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single case through the pipeline.
        
        Args:
            case_data: Raw case data
            
        Returns:
            Processed case data
        """
        try:
            # Validate input
            self._validate_case_data(case_data)
            
            # Apply processing rules
            processed = await self._apply_processing_rules(case_data)
            
            # Enrich with metadata
            processed = await self._enrich_metadata(processed)
            
            # Validate output
            self._validate_processed_case(processed)
            
            return processed
            
        except Exception as e:
            self.logger.error(f"Case processing failed: {e}")
            raise
    
    def _validate_case_data(self, case_data: Dict) -> None:
        """Validate incoming case data."""
        required_fields = ['case_id', 'court_id', 'case_number']
        for field in required_fields:
            if field not in case_data:
                raise ValueError(f"Missing required field: {field}")
    
    async def _apply_processing_rules(self, case_data: Dict) -> Dict:
        """Apply configured processing rules."""
        processed = case_data.copy()
        
        for rule_name, rule_config in self._processing_rules.items():
            if self._should_apply_rule(case_data, rule_config):
                processed = await self._execute_rule(processed, rule_name, rule_config)
        
        return processed
    
    def _should_apply_rule(self, case_data: Dict, rule_config: Dict) -> bool:
        """Determine if a rule should be applied."""
        # Rule condition logic
        return True
    
    async def _execute_rule(self, data: Dict, rule_name: str, config: Dict) -> Dict:
        """Execute a specific processing rule."""
        # Rule execution logic
        return data
    
    async def _enrich_metadata(self, case_data: Dict) -> Dict:
        """Add processing metadata."""
        import datetime
        case_data['processed_at'] = datetime.datetime.utcnow().isoformat()
        case_data['processor_version'] = '2.0.0'
        return case_data
    
    def _validate_processed_case(self, case_data: Dict) -> None:
        """Validate processed case data."""
        if not case_data.get('processed_at'):
            raise ValueError("Processing metadata missing")
```

#### Step 5.3: Create the PacerCoreService Facade
```python
# src/pacer/facades/pacer_core_service.py

from typing import Dict, Any, Optional, List
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.interfaces.pacer_protocols import PacerServiceProtocol
from src.pacer.components.case_processing import (
    CaseProcessor,
    RowProcessor,
    CaseClassifier
)

class PacerCoreService(AsyncServiceBase, PacerServiceProtocol):
    """
    Facade service for core PACER processing operations.
    
    Consolidates:
    - Case processing
    - Row processing
    - Case classification
    
    This facade provides a unified interface for all core PACER
    data processing operations, delegating to specialized components.
    """
    
    def __init__(self,
                 case_processor: CaseProcessor,
                 row_processor: RowProcessor,
                 case_classifier: CaseClassifier,
                 logger=None,
                 config=None):
        """
        Initialize PacerCoreService with injected dependencies.
        
        Args:
            case_processor: Component for case processing
            row_processor: Component for row processing
            case_classifier: Component for case classification
            logger: Logger instance
            config: Configuration dictionary
        """
        super().__init__(logger, config)
        
        # Store injected components
        self._case_processor = case_processor
        self._row_processor = row_processor
        self._case_classifier = case_classifier
        
        # Service state
        self._initialized = False
        self._processing_stats = {
            'cases_processed': 0,
            'rows_processed': 0,
            'classifications': 0
        }
    
    async def initialize(self) -> None:
        """Initialize all components."""
        if not self._initialized:
            await self._case_processor.initialize()
            await self._row_processor.initialize()
            await self._case_classifier.initialize()
            self._initialized = True
            self.log_info("PacerCoreService initialized")
    
    async def cleanup(self) -> None:
        """Cleanup all components."""
        if self._initialized:
            await self._case_processor.cleanup()
            await self._row_processor.cleanup()
            await self._case_classifier.cleanup()
            self._initialized = False
            self.log_info("PacerCoreService cleaned up")
    
    # Case Processing Methods
    
    async def process_case(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single case through the pipeline.
        
        Args:
            case_data: Raw case data
            
        Returns:
            Processed and classified case
        """
        try:
            # Process the case
            processed = await self._case_processor.process_case(case_data)
            
            # Classify the processed case
            classification = await self._case_classifier.classify(processed)
            processed['classification'] = classification
            
            # Update stats
            self._processing_stats['cases_processed'] += 1
            
            return processed
            
        except Exception as e:
            self.log_error(f"Failed to process case: {e}")
            raise
    
    async def process_cases_batch(self, cases: List[Dict]) -> List[Dict]:
        """Process multiple cases in batch."""
        results = []
        for case_data in cases:
            try:
                result = await self.process_case(case_data)
                results.append(result)
            except Exception as e:
                self.log_error(f"Batch processing error for case {case_data.get('case_id')}: {e}")
                results.append({'error': str(e), 'case_id': case_data.get('case_id')})
        return results
    
    # Row Processing Methods
    
    async def process_row(self, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a docket row.
        
        Args:
            row_data: Raw row data
            
        Returns:
            Processed row data
        """
        try:
            processed = await self._row_processor.process_row(row_data)
            self._processing_stats['rows_processed'] += 1
            return processed
            
        except Exception as e:
            self.log_error(f"Failed to process row: {e}")
            raise
    
    async def process_rows_batch(self, rows: List[Dict]) -> List[Dict]:
        """Process multiple rows in batch."""
        return await self._row_processor.process_batch(rows)
    
    # Classification Methods
    
    async def classify_case(self, case: Dict[str, Any]) -> str:
        """
        Classify a case.
        
        Args:
            case: Case data to classify
            
        Returns:
            Classification result
        """
        try:
            classification = await self._case_classifier.classify(case)
            self._processing_stats['classifications'] += 1
            return classification
            
        except Exception as e:
            self.log_error(f"Failed to classify case: {e}")
            raise
    
    async def reclassify_cases(self, case_ids: List[str]) -> Dict[str, str]:
        """Reclassify multiple cases."""
        results = {}
        for case_id in case_ids:
            try:
                # Would need to fetch case data first
                classification = await self._case_classifier.classify({'case_id': case_id})
                results[case_id] = classification
            except Exception as e:
                results[case_id] = f"Error: {e}"
        return results
    
    # Statistics and Monitoring
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return self._processing_stats.copy()
    
    async def reset_stats(self) -> None:
        """Reset processing statistics."""
        self._processing_stats = {
            'cases_processed': 0,
            'rows_processed': 0,
            'classifications': 0
        }
    
    # AsyncServiceBase implementation
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute facade actions."""
        if isinstance(data, dict):
            action = data.get('action')
            
            if action == 'process_case':
                return await self.process_case(data.get('case_data', {}))
            elif action == 'process_row':
                return await self.process_row(data.get('row_data', {}))
            elif action == 'classify_case':
                return await self.classify_case(data.get('case', {}))
            elif action == 'get_stats':
                return self.get_processing_stats()
        
        return None
```

### Day 7-8: Create PacerDataService

#### Step 7.1: Extract Analytics Component
```python
# src/pacer/components/analytics/analytics_engine.py

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from src.pacer.components.base_component import BaseComponent

class AnalyticsEngine(BaseComponent):
    """Handles analytics and metrics calculation."""
    
    def __init__(self, logger=None, config=None):
        super().__init__(logger, config)
        self._metrics_cache = {}
        self._cache_ttl = config.get('cache_ttl', 300)  # 5 minutes
    
    async def calculate_metrics(self, data: List[Dict], 
                               metrics: List[str]) -> Dict[str, Any]:
        """
        Calculate specified metrics on data.
        
        Args:
            data: Data to analyze
            metrics: List of metrics to calculate
            
        Returns:
            Dictionary of metric results
        """
        results = {}
        
        for metric in metrics:
            # Check cache first
            cache_key = f"{metric}_{len(data)}"
            if self._is_cached(cache_key):
                results[metric] = self._metrics_cache[cache_key]['value']
                continue
            
            # Calculate metric
            if metric == 'total_count':
                value = len(data)
            elif metric == 'success_rate':
                value = await self._calculate_success_rate(data)
            elif metric == 'average_processing_time':
                value = await self._calculate_avg_time(data)
            elif metric == 'distribution':
                value = await self._calculate_distribution(data)
            else:
                self.logger.warning(f"Unknown metric: {metric}")
                continue
            
            # Cache result
            self._cache_metric(cache_key, value)
            results[metric] = value
        
        return results
    
    async def _calculate_success_rate(self, data: List[Dict]) -> float:
        """Calculate success rate from data."""
        if not data:
            return 0.0
        
        successes = sum(1 for item in data if item.get('status') == 'success')
        return (successes / len(data)) * 100
    
    async def _calculate_avg_time(self, data: List[Dict]) -> float:
        """Calculate average processing time."""
        times = [item.get('processing_time', 0) for item in data]
        return sum(times) / len(times) if times else 0.0
    
    async def _calculate_distribution(self, data: List[Dict]) -> Dict:
        """Calculate data distribution."""
        distribution = {}
        for item in data:
            category = item.get('category', 'unknown')
            distribution[category] = distribution.get(category, 0) + 1
        return distribution
    
    def _is_cached(self, key: str) -> bool:
        """Check if metric is cached and valid."""
        if key not in self._metrics_cache:
            return False
        
        cached = self._metrics_cache[key]
        age = (datetime.now() - cached['timestamp']).total_seconds()
        return age < self._cache_ttl
    
    def _cache_metric(self, key: str, value: Any) -> None:
        """Cache a metric result."""
        self._metrics_cache[key] = {
            'value': value,
            'timestamp': datetime.now()
        }
    
    async def generate_summary(self, data: List[Dict]) -> Dict[str, Any]:
        """Generate comprehensive analytics summary."""
        metrics = [
            'total_count',
            'success_rate',
            'average_processing_time',
            'distribution'
        ]
        
        summary = await self.calculate_metrics(data, metrics)
        summary['generated_at'] = datetime.now().isoformat()
        summary['data_points'] = len(data)
        
        return summary
```

#### Step 7.2: Create PacerDataService Facade
```python
# src/pacer/facades/pacer_data_service.py

from typing import Dict, Any, List, Optional
from io import BytesIO
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.pacer.interfaces.pacer_protocols import PacerServiceProtocol
from src.pacer.components.analytics import AnalyticsEngine
from src.pacer.components.report import ReportGenerator
from src.pacer.components.export import ExportHandler

class PacerDataService(AsyncServiceBase, PacerServiceProtocol):
    """
    Facade service for PACER data operations.
    
    Consolidates:
    - Analytics and metrics
    - Report generation
    - Data export
    
    This facade provides unified access to all data analysis,
    reporting, and export functionality.
    """
    
    def __init__(self,
                 analytics_engine: AnalyticsEngine,
                 report_generator: ReportGenerator,
                 export_handler: ExportHandler,
                 logger=None,
                 config=None):
        """Initialize with injected dependencies."""
        super().__init__(logger, config)
        
        self._analytics = analytics_engine
        self._reporter = report_generator
        self._exporter = export_handler
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize all components."""
        if not self._initialized:
            await self._analytics.initialize()
            await self._reporter.initialize()
            await self._exporter.initialize()
            self._initialized = True
    
    # Analytics Methods
    
    async def analyze_data(self, data: List[Dict], 
                          analysis_type: str = 'summary') -> Dict[str, Any]:
        """
        Analyze PACER data.
        
        Args:
            data: Data to analyze
            analysis_type: Type of analysis ('summary', 'detailed', 'trends')
            
        Returns:
            Analysis results
        """
        if analysis_type == 'summary':
            return await self._analytics.generate_summary(data)
        elif analysis_type == 'detailed':
            metrics = ['total_count', 'success_rate', 'distribution', 
                      'average_processing_time', 'error_analysis']
            return await self._analytics.calculate_metrics(data, metrics)
        elif analysis_type == 'trends':
            return await self._analytics.analyze_trends(data)
        else:
            raise ValueError(f"Unknown analysis type: {analysis_type}")
    
    async def calculate_metrics(self, data: List[Dict], 
                               metrics: List[str]) -> Dict[str, Any]:
        """Calculate specific metrics on data."""
        return await self._analytics.calculate_metrics(data, metrics)
    
    # Report Generation Methods
    
    async def generate_report(self, 
                            report_type: str,
                            data: List[Dict],
                            options: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Generate a report from PACER data.
        
        Args:
            report_type: Type of report ('daily', 'weekly', 'monthly', 'custom')
            data: Data for report
            options: Report options
            
        Returns:
            Generated report
        """
        # First analyze the data
        analytics = await self.analyze_data(data, 'detailed')
        
        # Generate report with analytics
        report = await self._reporter.generate(
            report_type=report_type,
            data=data,
            analytics=analytics,
            options=options or {}
        )
        
        return report
    
    async def generate_court_report(self, court_id: str, 
                                  date_range: Dict) -> Dict[str, Any]:
        """Generate report for specific court."""
        # Would fetch data for court first
        report = await self._reporter.generate_court_report(
            court_id=court_id,
            date_range=date_range
        )
        return report
    
    # Export Methods
    
    async def export_data(self,
                         data: List[Dict],
                         format: str,
                         options: Optional[Dict] = None) -> bytes:
        """
        Export data in specified format.
        
        Args:
            data: Data to export
            format: Export format ('csv', 'json', 'excel', 'pdf')
            options: Export options
            
        Returns:
            Exported data as bytes
        """
        # Validate format
        supported_formats = ['csv', 'json', 'excel', 'pdf']
        if format not in supported_formats:
            raise ValueError(f"Unsupported format: {format}")
        
        # Generate analytics to include
        if options and options.get('include_analytics'):
            analytics = await self.analyze_data(data, 'summary')
        else:
            analytics = None
        
        # Export data
        exported = await self._exporter.export(
            data=data,
            format=format,
            analytics=analytics,
            options=options or {}
        )
        
        return exported
    
    async def export_report(self, report: Dict, format: str) -> bytes:
        """Export a generated report."""
        return await self._exporter.export_report(report, format)
    
    # Combined Operations
    
    async def analyze_and_export(self,
                                data: List[Dict],
                                analysis_type: str,
                                export_format: str) -> bytes:
        """Analyze data and export results."""
        # Analyze
        analysis = await self.analyze_data(data, analysis_type)
        
        # Export analysis
        return await self.export_data(
            [analysis],
            export_format,
            {'is_analysis': True}
        )
    
    async def generate_and_export_report(self,
                                        report_type: str,
                                        data: List[Dict],
                                        export_format: str) -> bytes:
        """Generate report and export it."""
        # Generate report
        report = await self.generate_report(report_type, data)
        
        # Export report
        return await self.export_report(report, export_format)
    
    # AsyncServiceBase implementation
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute facade actions."""
        if isinstance(data, dict):
            action = data.get('action')
            
            if action == 'analyze':
                return await self.analyze_data(
                    data.get('data', []),
                    data.get('analysis_type', 'summary')
                )
            elif action == 'generate_report':
                return await self.generate_report(
                    data.get('report_type'),
                    data.get('data', []),
                    data.get('options')
                )
            elif action == 'export':
                return await self.export_data(
                    data.get('data', []),
                    data.get('format'),
                    data.get('options')
                )
        
        return None
```

### Day 9-10: Continue Service Consolidation

Continue the pattern for remaining services...

---

## PHASE 3: DI Container Integration
**Duration**: 3-4 days  
**Goal**: Set up dependency injection container

### Day 11: Create DI Container Infrastructure

#### Step 11.1: Create Service Registry
```python
# src/infrastructure/di/service_registry.py

from typing import Dict, Any, Optional, Type, Callable
from enum import Enum
import inspect

class ServiceLifecycle(Enum):
    """Service lifecycle types."""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"

class ServiceDescriptor:
    """Describes a service registration."""
    
    def __init__(self,
                 service_class: Type | str,
                 factory: Optional[Callable] = None,
                 dependencies: Optional[Dict[str, str]] = None,
                 lifecycle: ServiceLifecycle = ServiceLifecycle.SINGLETON,
                 config: Optional[Dict] = None):
        self.service_class = service_class
        self.factory = factory
        self.dependencies = dependencies or {}
        self.lifecycle = lifecycle
        self.config = config or {}
        self._instance = None  # For singletons

class ServiceRegistry:
    """Registry for service descriptors."""
    
    def __init__(self):
        self._services: Dict[str, ServiceDescriptor] = {}
        self._scoped_instances: Dict[str, Any] = {}
    
    def register(self, name: str, descriptor: ServiceDescriptor) -> None:
        """Register a service."""
        if name in self._services:
            raise ValueError(f"Service '{name}' is already registered")
        self._services[name] = descriptor
    
    def register_singleton(self, name: str, service_class: Type,
                          dependencies: Optional[Dict] = None) -> None:
        """Convenience method to register singleton."""
        self.register(name, ServiceDescriptor(
            service_class=service_class,
            dependencies=dependencies,
            lifecycle=ServiceLifecycle.SINGLETON
        ))
    
    def register_transient(self, name: str, service_class: Type,
                          dependencies: Optional[Dict] = None) -> None:
        """Convenience method to register transient."""
        self.register(name, ServiceDescriptor(
            service_class=service_class,
            dependencies=dependencies,
            lifecycle=ServiceLifecycle.TRANSIENT
        ))
    
    def get_descriptor(self, name: str) -> ServiceDescriptor:
        """Get service descriptor."""
        if name not in self._services:
            raise ValueError(f"Service '{name}' is not registered")
        return self._services[name]
    
    def is_registered(self, name: str) -> bool:
        """Check if service is registered."""
        return name in self._services
```

#### Step 11.2: Create DI Container
```python
# src/infrastructure/di/container.py

from typing import Any, Dict, Optional, Type
import importlib
import inspect
from .service_registry import ServiceRegistry, ServiceLifecycle, ServiceDescriptor

class DIContainer:
    """Dependency injection container."""
    
    def __init__(self):
        self.registry = ServiceRegistry()
        self._resolving = set()  # Track resolution to detect circular deps
    
    def resolve(self, service_name: str, scope: Optional[str] = None) -> Any:
        """
        Resolve a service and its dependencies.
        
        Args:
            service_name: Name of service to resolve
            scope: Optional scope for scoped services
            
        Returns:
            Service instance
        """
        # Check for circular dependencies
        if service_name in self._resolving:
            raise RuntimeError(f"Circular dependency detected for '{service_name}'")
        
        self._resolving.add(service_name)
        try:
            descriptor = self.registry.get_descriptor(service_name)
            
            # Handle different lifecycles
            if descriptor.lifecycle == ServiceLifecycle.SINGLETON:
                if descriptor._instance is None:
                    descriptor._instance = self._create_instance(descriptor)
                return descriptor._instance
                
            elif descriptor.lifecycle == ServiceLifecycle.TRANSIENT:
                return self._create_instance(descriptor)
                
            elif descriptor.lifecycle == ServiceLifecycle.SCOPED:
                if scope is None:
                    raise ValueError(f"Scope required for scoped service '{service_name}'")
                
                scope_key = f"{scope}:{service_name}"
                if scope_key not in self.registry._scoped_instances:
                    self.registry._scoped_instances[scope_key] = self._create_instance(descriptor)
                return self.registry._scoped_instances[scope_key]
                
        finally:
            self._resolving.remove(service_name)
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """Create a service instance."""
        # Get the class
        if isinstance(descriptor.service_class, str):
            # Import class from string
            module_path, class_name = descriptor.service_class.rsplit('.', 1)
            module = importlib.import_module(module_path)
            service_class = getattr(module, class_name)
        else:
            service_class = descriptor.service_class
        
        # Use factory if provided
        if descriptor.factory:
            return descriptor.factory(self)
        
        # Resolve dependencies
        resolved_deps = {}
        for param_name, service_name in descriptor.dependencies.items():
            resolved_deps[param_name] = self.resolve(service_name)
        
        # Add config if needed
        if descriptor.config:
            resolved_deps['config'] = descriptor.config
        
        # Create instance
        return service_class(**resolved_deps)
    
    def clear_scope(self, scope: str) -> None:
        """Clear all scoped instances for a scope."""
        keys_to_remove = [k for k in self.registry._scoped_instances 
                         if k.startswith(f"{scope}:")]
        for key in keys_to_remove:
            del self.registry._scoped_instances[key]
```

### Day 12: Register PACER Services

#### Step 12.1: Create PACER Service Registration
```python
# src/infrastructure/di/pacer_registration.py

from src.infrastructure.di.service_registry import ServiceRegistry, ServiceDescriptor, ServiceLifecycle
import logging

def register_pacer_components(registry: ServiceRegistry) -> None:
    """Register PACER components (internal - not exposed)."""
    
    # Browser components
    registry.register('BrowserManager', ServiceDescriptor(
        service_class='src.pacer.components.browser.browser_manager.BrowserManager',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.SINGLETON
    ))
    
    registry.register('AuthenticationHandler', ServiceDescriptor(
        service_class='src.pacer.components.browser.authentication_handler.AuthenticationHandler',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.SINGLETON
    ))
    
    registry.register('NavigationHandler', ServiceDescriptor(
        service_class='src.pacer.components.browser.navigation_handler.NavigationHandler',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.SINGLETON
    ))
    
    # Case processing components
    registry.register('CaseProcessor', ServiceDescriptor(
        service_class='src.pacer.components.case_processing.case_processor.CaseProcessor',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.TRANSIENT
    ))
    
    registry.register('RowProcessor', ServiceDescriptor(
        service_class='src.pacer.components.case_processing.row_processor.RowProcessor',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.TRANSIENT
    ))
    
    registry.register('CaseClassifier', ServiceDescriptor(
        service_class='src.pacer.components.classification.case_classifier.CaseClassifier',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.TRANSIENT
    ))
    
    # Analytics components
    registry.register('AnalyticsEngine', ServiceDescriptor(
        service_class='src.pacer.components.analytics.analytics_engine.AnalyticsEngine',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.SINGLETON
    ))
    
    registry.register('ReportGenerator', ServiceDescriptor(
        service_class='src.pacer.components.report.report_generator.ReportGenerator',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.TRANSIENT
    ))
    
    registry.register('ExportHandler', ServiceDescriptor(
        service_class='src.pacer.components.export.export_handler.ExportHandler',
        dependencies={'logger': 'Logger', 'config': 'PacerConfig'},
        lifecycle=ServiceLifecycle.TRANSIENT
    ))
    
    # Add more component registrations...

def register_pacer_facades(registry: ServiceRegistry) -> None:
    """Register PACER facade services (public API)."""
    
    # Browser facade
    registry.register('PacerBrowserService', ServiceDescriptor(
        service_class='src.pacer.facades.pacer_browser_service.PacerBrowserService',
        dependencies={
            'browser_manager': 'BrowserManager',
            'auth_handler': 'AuthenticationHandler',
            'navigation_handler': 'NavigationHandler',
            'logger': 'Logger',
            'config': 'PacerConfig'
        },
        lifecycle=ServiceLifecycle.SINGLETON
    ))
    
    # Core facade
    registry.register('PacerCoreService', ServiceDescriptor(
        service_class='src.pacer.facades.pacer_core_service.PacerCoreService',
        dependencies={
            'case_processor': 'CaseProcessor',
            'row_processor': 'RowProcessor',
            'case_classifier': 'CaseClassifier',
            'logger': 'Logger',
            'config': 'PacerConfig'
        },
        lifecycle=ServiceLifecycle.SINGLETON
    ))
    
    # Data facade
    registry.register('PacerDataService', ServiceDescriptor(
        service_class='src.pacer.facades.pacer_data_service.PacerDataService',
        dependencies={
            'analytics_engine': 'AnalyticsEngine',
            'report_generator': 'ReportGenerator',
            'export_handler': 'ExportHandler',
            'logger': 'Logger',
            'config': 'PacerConfig'
        },
        lifecycle=ServiceLifecycle.SINGLETON
    ))
    
    # Add more facade registrations...

def register_pacer_services(registry: ServiceRegistry) -> None:
    """Register all PACER services."""
    
    # Register infrastructure first
    registry.register_singleton('Logger', logging.Logger)
    
    registry.register('PacerConfig', ServiceDescriptor(
        service_class=dict,
        factory=lambda c: load_pacer_config(),
        lifecycle=ServiceLifecycle.SINGLETON
    ))
    
    # Register components (internal)
    register_pacer_components(registry)
    
    # Register facades (public)
    register_pacer_facades(registry)

def load_pacer_config() -> dict:
    """Load PACER configuration."""
    # Load from file/env
    return {
        'pacer': {
            'timeout': 30,
            'retry_count': 3,
            # etc...
        }
    }
```

#### Step 12.2: Create Container Bootstrap
```python
# src/infrastructure/di/bootstrap.py

from src.infrastructure.di.container import DIContainer
from src.infrastructure.di.pacer_registration import register_pacer_services

_container: Optional[DIContainer] = None

def get_container() -> DIContainer:
    """Get the application DI container (singleton)."""
    global _container
    
    if _container is None:
        _container = create_container()
    
    return _container

def create_container() -> DIContainer:
    """Create and configure the DI container."""
    container = DIContainer()
    
    # Register all services
    register_pacer_services(container.registry)
    
    # Add other domain registrations
    # register_transformer_services(container.registry)
    # register_repository_services(container.registry)
    
    return container

def reset_container() -> None:
    """Reset the container (mainly for testing)."""
    global _container
    _container = None

# Convenience function for service resolution
def resolve(service_name: str) -> Any:
    """Resolve a service from the container."""
    return get_container().resolve(service_name)
```

### Day 13: Update Service Usage

#### Step 13.1: Update Service Factory
```python
# src/pacer/service_factory.py

from src.infrastructure.di.bootstrap import resolve
from typing import Dict, Any, Optional
import logging

class PacerServiceFactory:
    """
    Factory for creating PACER services using DI container.
    
    This factory now delegates to the DI container for service creation,
    ensuring proper dependency injection and lifecycle management.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self._service_cache = {}
    
    def get_browser_service(self) -> 'PacerBrowserService':
        """Get browser service from DI container."""
        if 'browser' not in self._service_cache:
            self._service_cache['browser'] = resolve('PacerBrowserService')
        return self._service_cache['browser']
    
    def get_core_service(self) -> 'PacerCoreService':
        """Get core processing service from DI container."""
        if 'core' not in self._service_cache:
            self._service_cache['core'] = resolve('PacerCoreService')
        return self._service_cache['core']
    
    def get_data_service(self) -> 'PacerDataService':
        """Get data service from DI container."""
        if 'data' not in self._service_cache:
            self._service_cache['data'] = resolve('PacerDataService')
        return self._service_cache['data']
    
    def get_court_service(self) -> 'PacerCourtService':
        """Get court service from DI container."""
        if 'court' not in self._service_cache:
            self._service_cache['court'] = resolve('PacerCourtService')
        return self._service_cache['court']
    
    # Legacy method for backward compatibility
    def create_html_processing_service(self, court_id: str, **kwargs):
        """Legacy method - now gets from DI container."""
        service = resolve('PacerProcessingService')
        # Configure for specific court if needed
        return service
    
    async def cleanup(self):
        """Cleanup all cached services."""
        for service in self._service_cache.values():
            if hasattr(service, 'cleanup'):
                await service.cleanup()
        self._service_cache.clear()
```

---

## PHASE 4: Testing and Validation
**Duration**: 3-4 days  
**Goal**: Ensure everything works correctly

### Day 14-15: Update Tests

#### Step 14.1: Create Component Tests
```python
# tests/pacer/components/case_processing/test_case_processor.py

import pytest
from unittest.mock import Mock, AsyncMock
from src.pacer.components.case_processing.case_processor import CaseProcessor

@pytest.fixture
def case_processor():
    """Create case processor with mocked dependencies."""
    logger = Mock()
    config = {'processing_rules': {}}
    return CaseProcessor(logger=logger, config=config)

@pytest.mark.asyncio
async def test_process_case_success(case_processor):
    """Test successful case processing."""
    # Arrange
    case_data = {
        'case_id': '123',
        'court_id': 'COURT1',
        'case_number': '2024-CV-001'
    }
    
    # Act
    result = await case_processor.process_case(case_data)
    
    # Assert
    assert result['case_id'] == '123'
    assert 'processed_at' in result
    assert result['processor_version'] == '2.0.0'

@pytest.mark.asyncio
async def test_process_case_missing_field(case_processor):
    """Test case processing with missing required field."""
    # Arrange
    case_data = {'case_id': '123'}  # Missing court_id and case_number
    
    # Act & Assert
    with pytest.raises(ValueError, match="Missing required field"):
        await case_processor.process_case(case_data)

@pytest.mark.asyncio
async def test_process_case_with_rules(case_processor):
    """Test case processing with rules applied."""
    # Configure rules
    case_processor._processing_rules = {
        'normalize': {'enabled': True},
        'enrich': {'enabled': True}
    }
    
    case_data = {
        'case_id': '123',
        'court_id': 'COURT1',
        'case_number': '2024-CV-001'
    }
    
    result = await case_processor.process_case(case_data)
    
    assert result is not None
    # Verify rules were applied
```

#### Step 14.2: Create Facade Integration Tests
```python
# tests/pacer/facades/test_pacer_core_service_integration.py

import pytest
from src.infrastructure.di.bootstrap import create_container, reset_container

@pytest.fixture
def container():
    """Create DI container for testing."""
    reset_container()
    container = create_container()
    yield container
    reset_container()

@pytest.mark.asyncio
async def test_pacer_core_service_integration(container):
    """Test PacerCoreService with real components."""
    # Resolve service from container
    core_service = container.resolve('PacerCoreService')
    
    # Initialize
    await core_service.initialize()
    
    try:
        # Test case processing
        case_data = {
            'case_id': 'TEST001',
            'court_id': 'COURT1',
            'case_number': '2024-CV-100',
            'parties': ['Plaintiff v. Defendant']
        }
        
        result = await core_service.process_case(case_data)
        
        assert result['case_id'] == 'TEST001'
        assert 'classification' in result
        assert 'processed_at' in result
        
        # Test batch processing
        cases = [case_data for _ in range(5)]
        results = await core_service.process_cases_batch(cases)
        
        assert len(results) == 5
        assert all('case_id' in r for r in results)
        
        # Check stats
        stats = core_service.get_processing_stats()
        assert stats['cases_processed'] == 6  # 1 single + 5 batch
        
    finally:
        await core_service.cleanup()

@pytest.mark.asyncio
async def test_service_lifecycle(container):
    """Test service lifecycle management."""
    # Resolve same service twice - should be singleton
    service1 = container.resolve('PacerCoreService')
    service2 = container.resolve('PacerCoreService')
    
    assert service1 is service2  # Same instance
    
    # Transient components should be different
    processor1 = container.resolve('CaseProcessor')
    processor2 = container.resolve('CaseProcessor')
    
    assert processor1 is not processor2  # Different instances
```

### Day 16: Performance and Load Testing

#### Step 16.1: Create Performance Tests
```python
# tests/pacer/performance/test_facade_performance.py

import pytest
import asyncio
import time
from src.infrastructure.di.bootstrap import create_container

@pytest.mark.asyncio
async def test_facade_performance_baseline(container):
    """Establish performance baseline for facades."""
    core_service = container.resolve('PacerCoreService')
    await core_service.initialize()
    
    # Create test data
    cases = [
        {
            'case_id': f'PERF{i:04d}',
            'court_id': 'COURT1',
            'case_number': f'2024-CV-{i:04d}'
        }
        for i in range(100)
    ]
    
    # Measure processing time
    start = time.perf_counter()
    
    # Process in parallel
    tasks = [core_service.process_case(case) for case in cases]
    results = await asyncio.gather(*tasks)
    
    elapsed = time.perf_counter() - start
    
    # Performance assertions
    assert len(results) == 100
    assert elapsed < 10.0  # Should process 100 cases in < 10 seconds
    
    # Calculate throughput
    throughput = len(results) / elapsed
    print(f"Throughput: {throughput:.2f} cases/second")
    
    await core_service.cleanup()

@pytest.mark.asyncio
async def test_memory_usage():
    """Test memory usage doesn't grow excessively."""
    import psutil
    import gc
    
    process = psutil.Process()
    container = create_container()
    
    # Get baseline memory
    gc.collect()
    baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # Process many cases
    core_service = container.resolve('PacerCoreService')
    await core_service.initialize()
    
    for batch in range(10):
        cases = [
            {
                'case_id': f'MEM{batch:02d}{i:04d}',
                'court_id': 'COURT1',
                'case_number': f'2024-{batch:02d}-{i:04d}'
            }
            for i in range(100)
        ]
        
        await core_service.process_cases_batch(cases)
        
        # Force garbage collection
        gc.collect()
    
    # Check memory after processing
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_growth = final_memory - baseline_memory
    
    print(f"Memory growth: {memory_growth:.2f} MB")
    
    # Memory shouldn't grow more than 100MB for this test
    assert memory_growth < 100
    
    await core_service.cleanup()
```

---

## Migration Scripts and Automation

### Migration Script
```python
#!/usr/bin/env python3
# tools/migrate_pacer_services.py

import os
import shutil
from pathlib import Path
import subprocess

def run_migration():
    """Run the complete migration."""
    
    print("🚀 Starting PACER Service Migration")
    
    # Phase 1: Component Migration
    print("\n📦 Phase 1: Migrating Components...")
    migrate_components()
    
    # Phase 2: Create Facades
    print("\n🏗️ Phase 2: Creating Facade Services...")
    create_facades()
    
    # Phase 3: Update Imports
    print("\n🔄 Phase 3: Updating Imports...")
    update_imports()
    
    # Phase 4: Run Tests
    print("\n🧪 Phase 4: Running Tests...")
    run_tests()
    
    print("\n✅ Migration Complete!")

def migrate_components():
    """Migrate _components to components."""
    src_dir = Path("src/pacer")
    
    # Map old to new locations
    migrations = {
        "_browser_components": "components/browser",
        "_file_components": "components/file_operations",
        "_download_components": "components/download",
        "_verification_components": "components/verification",
    }
    
    for old_dir, new_dir in migrations.items():
        old_path = src_dir / old_dir
        new_path = src_dir / new_dir
        
        if old_path.exists():
            # Create new directory
            new_path.mkdir(parents=True, exist_ok=True)
            
            # Move files
            for file in old_path.glob("*.py"):
                shutil.move(str(file), str(new_path / file.name))
                print(f"  Moved: {file.name} → {new_dir}")
            
            # Remove old directory
            shutil.rmtree(old_path)
            print(f"  Removed: {old_dir}")

def create_facades():
    """Create facade services."""
    # This would use templates to create the facade files
    pass

def update_imports():
    """Update all imports."""
    subprocess.run(["python", "tools/update_imports.py"], check=True)

def run_tests():
    """Run test suite."""
    result = subprocess.run(["pytest", "tests/pacer", "-v"], capture_output=True)
    if result.returncode != 0:
        print("❌ Tests failed!")
        print(result.stdout.decode())
        raise SystemExit(1)

if __name__ == "__main__":
    run_migration()
```

### Rollback Script
```python
#!/usr/bin/env python3
# tools/rollback_migration.py

import subprocess
from pathlib import Path

def rollback():
    """Rollback to previous state."""
    
    print("🔄 Rolling back migration...")
    
    # Use git to rollback
    subprocess.run(["git", "checkout", "HEAD~1", "src/pacer"], check=True)
    subprocess.run(["git", "checkout", "HEAD~1", "tests/pacer"], check=True)
    
    print("✅ Rollback complete!")
    
    # Run tests to verify
    print("🧪 Verifying rollback...")
    result = subprocess.run(["pytest", "tests/pacer", "-v"], capture_output=True)
    
    if result.returncode == 0:
        print("✅ System restored successfully!")
    else:
        print("⚠️ System restored but tests are failing")

if __name__ == "__main__":
    rollback()
```

---

## Success Validation Checklist

### Phase 1 Validation ✓
- [ ] All `_components` directories removed
- [ ] All components in `components/` directory
- [ ] No files exceed size limits (400 lines for components)
- [ ] All imports updated and working
- [ ] Existing tests still pass

### Phase 2 Validation ✓
- [ ] All facade services created (10 total)
- [ ] Facades under 500 lines each
- [ ] Components properly extracted
- [ ] Single responsibility maintained
- [ ] All functionality preserved

### Phase 3 Validation ✓
- [ ] DI container configured
- [ ] All services registered
- [ ] Dependencies properly injected
- [ ] No circular dependencies
- [ ] Service lifecycles correct

### Phase 4 Validation ✓
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Performance benchmarks met
- [ ] Memory usage acceptable
- [ ] Documentation updated

---

## Notes and Best Practices

1. **Always test after each step** - Don't wait until the end
2. **Use version control** - Commit after each successful phase
3. **Monitor file sizes continuously** - Use the validation script
4. **Keep backwards compatibility** - During migration period
5. **Document decisions** - Why certain components were split or combined

---

**Document Version**: 2.0  
**Status**: Detailed Implementation Ready