#!/usr/bin/env python3
"""
Analyze services that need dependency-injector migration.
"""
import os
import re
from pathlib import Path
from typing import List, Tu<PERSON>, Dict


def has_inject_decorator(file_path: Path) -> bool:
    """Check if a file has @inject decorator."""
    try:
        content = file_path.read_text()
        return '@inject' in content and 'from dependency_injector.wiring import inject' in content
    except Exception:
        return False


def extract_init_params(file_path: Path) -> Tuple[str, List[str]]:
    """Extract __init__ parameters from a service class."""
    try:
        content = file_path.read_text()
        
        # Find class extending AsyncServiceBase
        class_match = re.search(r'class\s+(\w+)\(AsyncServiceBase\):', content)
        if not class_match:
            return None, []
        
        class_name = class_match.group(1)
        
        # Find __init__ method
        init_match = re.search(
            r'def\s+__init__\s*\([^)]+\):', 
            content,
            re.MULTILINE | re.DOTALL
        )
        
        if not init_match:
            return class_name, []
        
        # Extract the full __init__ signature
        start = init_match.start()
        # Find the matching closing parenthesis
        paren_count = 0
        i = content.find('(', start)
        end = i
        while i < len(content) and paren_count >= 0:
            if content[i] == '(':
                paren_count += 1
            elif content[i] == ')':
                paren_count -= 1
                if paren_count == 0:
                    end = i
                    break
            i += 1
        
        init_signature = content[start:end+1]
        
        # Extract parameters
        params = []
        param_pattern = r'(\w+):\s*([^=,\)]+)(?:\s*=\s*[^,\)]+)?'
        for match in re.finditer(param_pattern, init_signature):
            param_name = match.group(1)
            param_type = match.group(2).strip()
            if param_name not in ['self', 'kwargs']:
                params.append((param_name, param_type))
        
        return class_name, params
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return None, []


def analyze_services(base_path: Path) -> Dict[str, List[Tuple[Path, str, List[str]]]]:
    """Analyze all services and categorize by migration status."""
    services_path = base_path / "src" / "services"
    
    results = {
        'already_migrated': [],
        'needs_migration': [],
        'non_service_files': []
    }
    
    for py_file in services_path.rglob("*.py"):
        if '__pycache__' in str(py_file) or '__init__.py' in py_file.name:
            continue
        
        if has_inject_decorator(py_file):
            class_name, params = extract_init_params(py_file)
            if class_name:
                results['already_migrated'].append((py_file, class_name, params))
        else:
            class_name, params = extract_init_params(py_file)
            if class_name:
                results['needs_migration'].append((py_file, class_name, params))
            else:
                results['non_service_files'].append((py_file, None, []))
    
    return results


def print_report(results: Dict[str, List[Tuple[Path, str, List[str]]]]):
    """Print analysis report."""
    print("\n=== Dependency Injection Migration Analysis ===\n")
    
    print(f"✅ Already Migrated: {len(results['already_migrated'])} services")
    for path, class_name, params in sorted(results['already_migrated']):
        rel_path = path.relative_to(path.parts[0])
        print(f"  - {rel_path} ({class_name})")
    
    print(f"\n❌ Needs Migration: {len(results['needs_migration'])} services")
    
    # Group by module
    by_module = {}
    for path, class_name, params in results['needs_migration']:
        module = path.parent.name
        if module not in by_module:
            by_module[module] = []
        by_module[module].append((path, class_name, params))
    
    for module, services in sorted(by_module.items()):
        print(f"\n  {module}/ ({len(services)} services):")
        for path, class_name, params in sorted(services):
            print(f"    - {path.name} ({class_name})")
            if params:
                print(f"      Parameters: {', '.join([f'{p[0]}: {p[1]}' for p in params])}")
    
    print(f"\n📄 Non-service files: {len(results['non_service_files'])}")
    
    # Summary
    total_services = len(results['already_migrated']) + len(results['needs_migration'])
    migrated_pct = (len(results['already_migrated']) / total_services * 100) if total_services > 0 else 0
    
    print(f"\n=== Summary ===")
    print(f"Total Services: {total_services}")
    print(f"Migrated: {len(results['already_migrated'])} ({migrated_pct:.1f}%)")
    print(f"Remaining: {len(results['needs_migration'])} ({100-migrated_pct:.1f}%)")


if __name__ == "__main__":
    base_path = Path("/Users/<USER>/PycharmProjects/lexgenius/worktrees/task-create-system")
    results = analyze_services(base_path)
    print_report(results)