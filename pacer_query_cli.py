#!/usr/bin/env python3
"""
Simple Interactive PACER Query CLI
Query the PACER repository directly without complex configuration
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import pandas as pd
from rich.console import Console
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich import print as rprint
from rich.logging import RichHandler

# Setup logging with rich
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    handlers=[RichHandler(rich_tracebacks=True)]
)
logger = logging.getLogger(__name__)

# Disable verbose logging for boto3/aioboto3
logging.getLogger('boto3').setLevel(logging.WARNING)
logging.getLogger('botocore').setLevel(logging.WARNING)
logging.getLogger('aioboto3').setLevel(logging.WARNING)
logging.getLogger('aiobotocore').setLevel(logging.WARNING)

console = Console()


class PacerQueryCLI:
    def __init__(self):
        self.repository = None
        self.storage = None
        
    async def initialize(self):
        """Initialize storage and repository"""
        try:
            from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
            from src.repositories.pacer_repository import PacerRepository
            from src.config_models.loader import load_storage_config
            
            # Load storage config
            storage_config = load_storage_config()
            
            # Create storage
            self.storage = AsyncDynamoDBStorage(storage_config)
            
            # Create repository
            self.repository = PacerRepository(self.storage)
            
            console.print("[green]✓ Connected to PACER database[/green]")
            
        except Exception as e:
            console.print(f"[red]Error initializing: {e}[/red]")
            raise
    
    async def cleanup(self):
        """Clean up resources"""
        if self.storage:
            await self.storage.close()
    
    def display_results(self, results: List[Dict[str, Any]], limit: int = 10):
        """Display query results in a rich table"""
        if not results:
            console.print("[yellow]No results found[/yellow]")
            return
            
        # Create table
        table = Table(show_header=True, header_style="bold magenta")
        
        # Define columns to show
        columns = [
            ("filing_date", "Filing Date"),
            ("docket_num", "Docket #"),
            ("court_id", "Court"),
            ("versus", "Case"),
            ("law_firm", "Law Firm"),
            ("mdl_num", "MDL"),
        ]
        
        # Add columns
        for _, header in columns:
            table.add_column(header)
        
        # Add rows (limit display)
        for i, record in enumerate(results[:limit]):
            row = []
            for field, _ in columns:
                value = record.get(field, "")
                # Truncate long values
                if isinstance(value, str) and len(value) > 50:
                    value = value[:47] + "..."
                row.append(str(value))
            table.add_row(*row)
        
        console.print(table)
        
        if len(results) > limit:
            console.print(f"\n[dim]Showing {limit} of {len(results)} results[/dim]")
    
    async def query_by_date(self):
        """Query records by filing date"""
        date_str = Prompt.ask("Enter filing date (YYYYMMDD)", 
                              default=datetime.now().strftime("%Y%m%d"))
        
        try:
            # Validate date format
            datetime.strptime(date_str, "%Y%m%d")
            
            with console.status(f"Querying records for {date_str}..."):
                results = await self.repository.query_by_filing_date(date_str)
            
            console.print(f"\n[bold]Found {len(results)} records for {date_str}[/bold]")
            self.display_results(results)
            
            # Offer to export
            if results and Confirm.ask("\nExport results to CSV?"):
                filename = f"pacer_filings_{date_str}.csv"
                df = pd.DataFrame(results)
                df.to_csv(filename, index=False)
                console.print(f"[green]Exported to {filename}[/green]")
                
        except ValueError:
            console.print("[red]Invalid date format[/red]")
    
    async def query_by_docket(self):
        """Query by court and docket number"""
        court_id = Prompt.ask("Enter court ID (e.g., flsd)")
        docket_num = Prompt.ask("Enter docket number")
        
        with console.status(f"Searching for {court_id} {docket_num}..."):
            results = await self.repository.query_by_court_and_docket(court_id, docket_num)
        
        console.print(f"\n[bold]Found {len(results)} records[/bold]")
        self.display_results(results)
    
    async def query_by_mdl(self):
        """Query by MDL number"""
        mdl_num = Prompt.ask("Enter MDL number")
        
        # Ask for date range
        use_range = Confirm.ask("Search specific date range?", default=False)
        
        if use_range:
            start_date = Prompt.ask("Start date (YYYYMMDD)")
            end_date = Prompt.ask("End date (YYYYMMDD)", 
                                 default=datetime.now().strftime("%Y%m%d"))
            
            with console.status(f"Searching MDL {mdl_num} from {start_date} to {end_date}..."):
                results = await self.repository.query_by_mdl_and_date_range(
                    mdl_num, start_date, end_date
                )
        else:
            with console.status(f"Searching all records for MDL {mdl_num}..."):
                results = await self.repository.query_by_mdl_num(mdl_num)
        
        console.print(f"\n[bold]Found {len(results)} records for MDL {mdl_num}[/bold]")
        self.display_results(results)
    
    async def query_by_law_firm(self):
        """Query by law firm"""
        firm = Prompt.ask("Enter law firm name (or partial)")
        
        with console.status(f"Searching for law firm '{firm}'..."):
            results = await self.repository.query_by_law_firm(firm)
        
        console.print(f"\n[bold]Found {len(results)} records[/bold]")
        self.display_results(results)
    
    async def recent_filings(self):
        """Show recent filings"""
        days = int(Prompt.ask("Number of days to look back", default="7"))
        
        with console.status(f"Getting filings from last {days} days..."):
            results = await self.repository.get_recent_filings(days)
        
        console.print(f"\n[bold]Found {len(results)} filings in last {days} days[/bold]")
        self.display_results(results, limit=20)
    
    async def mdl_summary(self):
        """Generate MDL summary for a date"""
        date_str = Prompt.ask("Enter date for MDL summary (YYYYMMDD)", 
                              default=datetime.now().strftime("%Y%m%d"))
        
        try:
            with console.status("Generating MDL summary (30 days)..."):
                df = await self.repository.get_mdl_summary(date_str)
            
            if df.empty:
                console.print("[yellow]No MDL data found[/yellow]")
                return
            
            # Display summary
            table = Table(show_header=True, header_style="bold magenta", 
                         title=f"MDL Summary for 30 days ending {date_str}")
            table.add_column("MDL #", style="cyan")
            table.add_column("Total Cases", justify="right", style="green")
            
            for _, row in df.head(20).iterrows():
                table.add_row(str(row['mdl_num']), f"{int(row['total']):,}")
            
            console.print(table)
            
            if len(df) > 20:
                console.print(f"\n[dim]Showing top 20 of {len(df)} MDLs[/dim]")
            
            # Offer to export
            if Confirm.ask("\nExport full summary to CSV?"):
                filename = f"mdl_summary_{date_str}.csv"
                df.to_csv(filename, index=False)
                console.print(f"[green]Exported to {filename}[/green]")
                
        except Exception as e:
            console.print(f"[red]Error generating summary: {e}[/red]")
    
    async def query_by_added_on(self):
        """Query by AddedOn date (when records were added to system)"""
        date_str = Prompt.ask("Enter AddedOn date (YYYYMMDD)", 
                              default=datetime.now().strftime("%Y%m%d"))
        
        # Ask for range
        use_range = Confirm.ask("Search date range?", default=False)
        
        if use_range:
            end_date = Prompt.ask("End date (YYYYMMDD)", 
                                 default=datetime.now().strftime("%Y%m%d"))
            
            with console.status(f"Querying AddedOn from {date_str} to {end_date}..."):
                results = await self.repository.query_by_added_on_range(date_str, end_date)
        else:
            with console.status(f"Querying records added on {date_str}..."):
                results = await self.repository.query_by_added_on_range(date_str, date_str)
        
        console.print(f"\n[bold]Found {len(results)} records[/bold]")
        self.display_results(results)
    
    async def run_interactive(self):
        """Run interactive query mode"""
        console.print("\n[bold blue]PACER Interactive Query CLI[/bold blue]")
        console.print("=" * 40)
        
        while True:
            console.print("\n[bold]Query Options:[/bold]")
            console.print("1. Query by filing date")
            console.print("2. Query by court & docket number")
            console.print("3. Query by MDL number")
            console.print("4. Query by law firm")
            console.print("5. Recent filings")
            console.print("6. MDL summary (30 days)")
            console.print("7. Query by AddedOn date")
            console.print("q. Quit")
            
            choice = Prompt.ask("\nSelect option", choices=["1","2","3","4","5","6","7","q"])
            
            try:
                if choice == "q":
                    break
                elif choice == "1":
                    await self.query_by_date()
                elif choice == "2":
                    await self.query_by_docket()
                elif choice == "3":
                    await self.query_by_mdl()
                elif choice == "4":
                    await self.query_by_law_firm()
                elif choice == "5":
                    await self.recent_filings()
                elif choice == "6":
                    await self.mdl_summary()
                elif choice == "7":
                    await self.query_by_added_on()
                    
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
                logger.exception("Query error")
            
            if not Confirm.ask("\nContinue querying?", default=True):
                break
        
        console.print("\n[yellow]Goodbye![/yellow]")


async def main():
    """Main entry point"""
    cli = PacerQueryCLI()
    
    try:
        # Initialize
        await cli.initialize()
        
        # Run interactive mode
        await cli.run_interactive()
        
    finally:
        # Cleanup
        await cli.cleanup()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Fatal error: {e}[/red]")
        logger.exception("Fatal error")