#!/bin/bash
# Example environment variables needed for LexGenius

# PACER Credentials (required for scraping)
export PACER_USERNAME='your_pacer_username'
export PACER_PASSWORD='your_pacer_password'

# AWS Credentials (required for S3 and DynamoDB)
export AWS_ACCESS_KEY='your_aws_access_key'
export AWS_SECRET_KEY='your_aws_secret_key'
export AWS_REGION='us-west-2'
export S3_BUCKET_NAME='lexgenius-dockets'

# OpenAI API Key (required for GPT-4 interface)
export OPENAI_API_KEY='sk-your-openai-api-key'

# CloudFront Distribution ID (optional)
export CLOUDFRONT_DISTRIBUTION_ID='your_cloudfront_id'

# Optional: Other API keys
# export DEEPSEEK_API_KEY='your_deepseek_key'
# export MISTRAL_API_KEY='your_mistral_key'

echo "Environment variables set. You can now run: ./run_pipeline.sh --config scrape"