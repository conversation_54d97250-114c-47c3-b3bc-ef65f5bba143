#!/usr/bin/env python3
"""
Debug script to test the categorization logic for a specific summary
"""
import re

def categorize_summary_debug(summary: str) -> str:
    """Debug version of categorize_summary with detailed logging"""
    print(f"🔍 Testing summary: '{summary}'")
    
    if not summary or summary in ['NA', 'SKIPPED', None]:
        print("❌ Summary is empty or NA/SKIPPED")
        return 'uncategorized'

    # Convert to lowercase for case-insensitive matching
    summary_lower = summary.lower()
    print(f"📝 Lowercase summary: '{summary_lower}'")

    # Define category rules (same as in main script)
    category_rules = [
        ('Video Game Addiction', ['video game addiction', 'gaming addiction', 'fortnite mental health',
                                  'fornite addiction', 'roblox video game addiction', 'nba 2k addiction']),
        ('PFAS Water Contamination', ['water contamination']),
        ('Nexium Prilosec Cancer Investigation',['Nexium Prilosec Cancer Investigation']),
        ('Toxic Baby Food', ['toxic baby food', 'beech-nut baby food']),
        ('PFAS Exposure', ['pfas', 'pfas cancer']),
    ]

    # Check each category rule with word boundaries
    for category, phrases in category_rules:
        print(f"\n🏷️  Testing category: '{category}'")
        for phrase in phrases:
            print(f"   🔍 Testing phrase: '{phrase}'")
            # Use word boundaries for exact phrase matching
            pattern = r'\b' + re.escape(phrase) + r'\b'
            print(f"   📋 Pattern: {pattern}")
            match = re.search(pattern, summary_lower)
            print(f"   ✅ Match: {match}")
            if match:
                print(f"   🎯 MATCHED! Returning category: '{category}'")
                return category
            else:
                print(f"   ❌ No match")

    print(f"\n🤷 No matches found, returning 'uncategorized'")
    return 'uncategorized'

def main():
    """Test specific summary"""
    test_summary = "Alabama PFAS Water Contamination Investigation"
    result = categorize_summary_debug(test_summary)
    print(f"\n🎉 Final result: '{result}'")
    
    # Also test a few other variations
    test_cases = [
        "Alabama PFAS Water Contamination Investigation",
        "Water Contamination Settlement",
        "PFAS Exposure Lawsuit",
        "Something about water contamination here"
    ]
    
    print(f"\n" + "="*60)
    print("Testing multiple cases:")
    for test in test_cases:
        result = categorize_summary_debug(test)
        print(f"'{test}' -> '{result}'")
        print("-" * 40)

if __name__ == "__main__":
    main()