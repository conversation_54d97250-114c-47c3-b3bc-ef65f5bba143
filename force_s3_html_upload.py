#!/usr/bin/env python3
"""
Force upload HTML files to S3 with cache busting
"""
import asyncio
import os
import sys
from pathlib import Path
from datetime import datetime
import hashlib
import mimetypes

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.infrastructure.storage.s3_async import S3AsyncStorage
from rich.console import Console
from rich.progress import Progress
from dotenv import load_dotenv

console = Console()

async def force_upload_html_files(date_str: str, specific_files: list = None):
    """Force upload HTML files to S3 with proper headers"""
    
    # Load environment
    env_path = project_root / '.env'
    load_dotenv(env_path)
    
    # Get AWS credentials
    aws_access_key = (
        os.getenv('AWS_ACCESS_KEY_ID') or 
        os.getenv('LEXGENIUS_AWS_ACCESS_KEY_ID') or
        os.getenv('AWS_ACCESS_KEY')
    )
    aws_secret_key = (
        os.getenv('AWS_SECRET_ACCESS_KEY') or 
        os.getenv('LEXGENIUS_AWS_SECRET_ACCESS_KEY') or
        os.getenv('AWS_SECRET_KEY')
    )
    bucket_name = (
        os.getenv('AWS_S3_BUCKET') or 
        os.getenv('LEXGENIUS_AWS_S3_BUCKET') or
        os.getenv('S3_BUCKET_NAME') or
        'lexgeniuswebsite'
    )
    aws_region = (
        os.getenv('AWS_REGION') or 
        os.getenv('LEXGENIUS_AWS_REGION') or
        os.getenv('AWS_DEFAULT_REGION') or
        'us-west-2'
    )
    
    # Local HTML directory
    html_dir = project_root / "data" / date_str / "html"
    
    if not html_dir.exists():
        console.print(f"[red]Directory not found: {html_dir}[/red]")
        return
    
    # Get files to upload
    if specific_files:
        files_to_upload = [html_dir / f for f in specific_files if (html_dir / f).exists()]
    else:
        files_to_upload = list(html_dir.glob("*.html"))
    
    console.print(f"[bold]Found {len(files_to_upload)} HTML files to upload[/bold]")
    
    # Initialize S3 storage
    async with S3AsyncStorage(
        bucket_name=bucket_name,
        aws_access_key=aws_access_key,
        aws_secret_key=aws_secret_key,
        aws_region=aws_region,
        disable_versioning=True  # Permanently disable S3 versioning
    ) as s3_storage:
        
        with Progress() as progress:
            task = progress.add_task("[cyan]Uploading files...", total=len(files_to_upload))
            
            for file_path in files_to_upload:
                try:
                    # Read file content
                    with open(file_path, 'rb') as f:
                        content = f.read()
                    
                    # Calculate MD5 hash
                    md5_hash = hashlib.md5(content).hexdigest()
                    
                    # S3 key
                    s3_key = f"{date_str}/html/{file_path.name}"
                    
                    # Upload with explicit cache headers
                    extra_args = {
                        'ContentType': 'text/html',
                        'CacheControl': 'no-cache, no-store, must-revalidate',
                        'Metadata': {
                            'upload-time': datetime.now().isoformat(),
                            'md5': md5_hash
                        }
                    }
                    
                    # Delete existing object first (force overwrite)
                    try:
                        await s3_storage.delete_object(s3_key)
                        console.print(f"[yellow]Deleted existing: {s3_key}[/yellow]")
                    except:
                        pass  # Ignore if doesn't exist
                    
                    # Upload new version using put_object
                    await s3_storage._client.put_object(
                        Bucket=s3_storage.bucket_name,
                        Key=s3_key,
                        Body=content,
                        **extra_args
                    )
                    
                    console.print(f"[green]✓ Uploaded: {file_path.name} (MD5: {md5_hash[:8]}...)[/green]")
                    
                    # Verify upload
                    try:
                        obj_info = await s3_storage.get_object_metadata(s3_key)
                        console.print(f"  [dim]Verified in S3: Size={obj_info.get('ContentLength', 0)} bytes[/dim]")
                    except Exception as e:
                        console.print(f"  [red]Failed to verify: {e}[/red]")
                    
                except Exception as e:
                    console.print(f"[red]✗ Failed to upload {file_path.name}: {e}[/red]")
                
                progress.update(task, advance=1)
        
        console.print("\n[bold green]Upload complete![/bold green]")
        
        # Display CDN URLs
        console.print("\n[bold]CDN URLs:[/bold]")
        for file_path in files_to_upload[:5]:  # Show first 5
            cdn_url = f"https://cdn.lexgenius.ai/{date_str}/html/{file_path.name}"
            console.print(f"  {cdn_url}")
        
        if len(files_to_upload) > 5:
            console.print(f"  ... and {len(files_to_upload) - 5} more")
        
        console.print("\n[yellow]Note: CloudFront cache was NOT invalidated. Run invalidation separately if needed.[/yellow]")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Force upload HTML files to S3")
    parser.add_argument("date", help="Date in YYYYMMDD format")
    parser.add_argument("--files", nargs="+", help="Specific files to upload (optional)")
    
    args = parser.parse_args()
    
    # Validate date format
    if len(args.date) != 8 or not args.date.isdigit():
        console.print(f"[red]Error: Invalid date format '{args.date}'. Must be YYYYMMDD.[/red]")
        sys.exit(1)
    
    console.print(f"[bold]Force uploading HTML files for date: {args.date}[/bold]\n")
    
    asyncio.run(force_upload_html_files(args.date, args.files))

if __name__ == "__main__":
    main()