
***

### **Definitive Prompt for Meta Ad Library Scraper using Camoufox**

You are updating fb_ads component using the **Camoufox** library. The script must be resilient, handle all dynamic page elements, and emulate human-like behavior to prevent bot detection.

**Objective:** To programmatically navigate to the Meta Ad Library, set the search filters for **Country: "United States"** and **Ad Category: "All Ads,"** and then search for and select the advertiser **"Morgan & Morgan"** from the dynamic suggestions list.

---

### **Detailed Technical Steps & Locators**

**1. Browser Setup & Initial Navigation**

*   **Technology Context:** The script will use Camoufox, which leverages `puppeteer-extra` and the stealth plugin. This handles many low-level anti-bot measures automatically.
*   **Action Sequence:**
    1.  Initialize Camoufox and launch a new browser page.
    2.  **Set Referrer Header:** Before navigating, set the `Referer` HTTP header to mimic a Google search. The script must randomly select one URL from the provided list for each run.
        *   **Code Example:** `await page.setExtraHTTPHeaders({ 'Referer': 'https://www.google.com/' });`
    3.  **Navigate to URL:** Go to the Ad Library homepage.
        *   **Code Example:** `await page.goto('https://www.facebook.com/ads/library/', { waitUntil: 'networkidle2' });`
    4.  **Wait for Page Load:** After navigation, explicitly wait for the primary country dropdown container to be present and interactive. This confirms the initial React application has mounted.
        *   **Selector:** `div#js_o`
        *   **Code Example:** `await page.waitForSelector('div#js_o');`

**2. Select Country: "United States"**

*   **Context:** While "United States" is the default, the script must explicitly interact with the dropdown to ensure a consistent state and mimic human behavior.
*   **Locator Strategy:**
    *   **Dropdown Trigger:** The country dropdown element has a stable ID: `js_o`. Target it with the CSS selector `div#js_o`.
    *   **Popup List:** The dropdown list that appears is a `div` with `role="grid"`.
    *   **Target Option:** The selectable "United States" option is a grid cell. Target it with a selector that combines its role and the text it contains for maximum precision. The correct option is the one within the `Current location` section.
*   **Action Sequence:**
    1.  Click the "Country" dropdown to open the list.
        *   **Code Example:** `await page.click('div#js_o');`
    2.  Wait for the country list popup to become visible.
        *   **Code Example:** `await page.waitForSelector('div[role="grid"]', { visible: true });`
    3.  Click the "United States" option. The selector must be specific enough to avoid ambiguity with the search input in the same popup.
        *   **Selector:** `div[role="gridcell"]:has-text("United States")`
        *   **Code Example:** `await page.click('div[role="gridcell"]:has-text("United States")');`

**3. Select Ad Category: "All Ads"**

*   **Locator Strategy:**
    *   **Dropdown Trigger:** The "Ad category" dropdown also has a stable ID: `js_p`. Use the CSS selector `div#js_p`.
    *   **Target Option:** The "All ads" option is within a `div` with `role="gridcell"`.
*   **Action Sequence:**
    1.  Click the "Ad category" dropdown to open its list.
        *   **Code Example:** `await page.click('div#js_p');`
    2.  Wait for the category selection popup to become visible.
        *   **Code Example:** `await page.waitForSelector('div[role="grid"]', { visible: true });`
    3.  Click the "All ads" option.
        *   **Selector:** `div[role="gridcell"]:has-text("All ads")`
        *   **Code Example:** `await page.click('div[role="gridcell"]:has-text("All ads")');`
    4.  **Wait for State Change:** After selection, the search input becomes enabled. Add a wait to ensure the input is ready for interaction.
        *   **Code Example:** `await page.waitForSelector('input[placeholder*="Search"][disabled]', { hidden: true });`

**4. Enter Advertiser Name & Select from Dynamic Suggestions (Explicit Strategy)**

*   **Context:** This is the most critical step. After typing, a dynamic list appears. The script must reliably find and click the correct item corresponding to "Morgan & Morgan". The key is to locate the clickable container based on the unique text within it.

*   **Locator Strategy Breakdown:**
    *   **Search Input:** `input[placeholder="Search by keyword or advertiser"]`
    *   **Suggestions List Container:** The entire dropdown that appears is a `div[role="listbox"]`.
    *   **Individual Suggestion Row:** Each item in the list is a generic `div` that acts as a container for the image, heading, and follower text. It does not have a stable role like `option`.
    *   **Unique Identifier within the Row:** The most stable element within any given row is the `div[role="heading"]` that contains the advertiser's name.

*   **Recommended Action Sequence (Robust Iteration Method):** This method is preferred because it is highly debuggable and resilient to minor structural changes.
    1.  Define the target advertiser: `const lawFirmName = "Morgan & Morgan";`
    2.  Type the `lawFirmName` into the search input with a human-like delay.
        *   **Code Example:** `await page.type('input[placeholder="Search by keyword or advertiser"]', lawFirmName, { delay: 125 });`
    3.  Wait for the suggestions listbox to appear and contain at least one item.
        *   **Code Example:** `await page.waitForSelector('div[role="listbox"] > div');`
    4.  **Fetch all suggestion rows:** Get all the direct `div` children of the listbox.
        *   **Code Example:** `const suggestionRows = await page.$$('div[role="listbox"] > div');`
    5.  **Iterate and find the correct row:** Loop through the fetched elements to find the one containing the correct heading.
        ```javascript
        let targetRow = null;
        for (const row of suggestionRows) {
            // Find the heading element within the current row
            const headingEl = await row.$('div[role="heading"]');
            if (headingEl) {
                // Extract text and compare
                const headingText = await page.evaluate(el => el.textContent, headingEl);
                if (headingText.trim() === lawFirmName) {
                    targetRow = row;
                    break; // Exit the loop once the correct row is found
                }
            }
        }
        ```
    6.  **Click the target or throw an error:**
        *   If `targetRow` was found, click it. The `click` action will handle scrolling it into view.
        *   If `targetRow` is null after the loop, the script must fail loudly.
        *   **Code Example:**
            ```javascript
            if (targetRow) {
                await targetRow.click();
            } else {
                throw new Error(`Could not find advertiser "${lawFirmName}" in the suggestions list.`);
            }
            ```

**5. Finalize and Verify**

*   **Action Sequence:**
    1.  After the click, the page loads the ads. Wait for a unique element on the results page to confirm the action was successful. A good candidate is the results count summary.
        *   **Selector:** `div:text-matches("[0-9,]+ results")`
        *   **Code Example:** `await page.waitForSelector('div:text-matches("[0-9,]+ results")');`
    2.  The script can now proceed to extract ad data or close the browser.
        *   **Code Example:** `await browser.close();`
