1. Refer<PERSON> should look like a google referral from a search:

- meta ads library
- fb ads library
- fb ad library
- ad library

2. Target URL: `https://www.facebook.com/ads/library/` which takes you to /fb_ads_flow/meta_ad_lib_home_page.html

Of course. This is a great example of a modern, dynamic webpage which can be tricky to scrape.

Here is a prompt for a scraper, followed by an explanation of those "weird" class names.


**Objective:** To filter the ad library for a specific advertiser in the "All ads" category within the "United States".

**Steps:**

1.  **Navigate and Wait:**
    *   Go to the target URL.
    *   Wait for the initial page elements, specifically the country and category dropdowns, to be fully loaded and interactive.

2.  **Select Country: "United States"**
    *   The "United States" country field is pre-selected. To ensure the correct state, the script should confirm this or re-select it.
    *   **Locator Strategy:** Since the class names are unstable, locate the country dropdown by its `role` and the text it contains.
        *   Find the `div` element with `role="combobox"` that contains a child element with the text "United States". A stable identifier is `id="js_o"`.
    *   **Action (if needed):**
        *   Click this `div` to open the country selection list.
        *   Wait for the popup list to appear.
        *   Inside the popup, locate the `div` with `role="gridcell"` that contains the text "United States".
        *   Click this element to confirm the selection.

3.  **Select Ad Category: "All Ads"**
    *   **Locator Strategy:** Locate the "Ad category" dropdown.
        *   Find the `div` with `role="combobox"` that contains a child element with the text "Ad category". A stable identifier is `id="js_p"`.
    *   **Action:**
        *   Click this `div` to open the category selection list.
        *   Wait for the popup list to appear.
        *   Inside the popup, locate the `div` with `role="gridcell"` that contains the text "All ads".
        *   Click this element to select it.

4.  **Enter Advertiser Name:**
    *   **Locator Strategy:** After selecting the category, the search input field becomes active.
        *   Locate the `input` element. A stable way to find it is by its parent `div` with `role="searchbox"`.
    *   **Action:**
        *   Define a variable for the advertiser, for example: `const lawFirmName = "Morgan & Morgan";`
        *   Enter the `lawFirmName` into the search input field using a **human-like typing simulation**. This means typing the string character by character with a small, randomized delay between each key press (e.g., between 70ms and 200ms) to avoid bot detection.

5.  **Finalize:**
    *   After typing, the script should wait for the dropdown of advertiser suggestions to appear and can then proceed to select the correct one or simply press Enter, depending on the desired outcome.

Please ensure the script includes appropriate `waits` (e.g., `waitForSelector`, `waitForNavigation`) between steps to handle the page's dynamic nature and prevent race conditions.

***
