#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to remove 'defendants' and 'attorneys' keys from all JSON files 
in the dockets directory and save the updated JSON files.
"""

import json
import os
from pathlib import Path

def process_json_files():
    """Remove 'defendants' and 'attorneys' keys from all JSON files in the dockets directory."""
    
    dockets_dir = Path("/Users/<USER>/PycharmProjects/lexgenius/data/20250613/dockets")
    
    if not dockets_dir.exists():
        print(f"Directory {dockets_dir} does not exist")
        return
    
    json_files = list(dockets_dir.glob("*.json"))
    
    if not json_files:
        print(f"No JSON files found in {dockets_dir}")
        return
    
    print(f"Found {len(json_files)} JSON files to process")
    
    processed_count = 0
    error_count = 0
    
    for json_file in json_files:
        try:
            # Load JSON data
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Track if we made changes
            changes_made = False
            
            # Remove keys if they exist
            if 'defendants' in data:
                del data['defendants']
                changes_made = True
            
            if 'attorneys' in data:
                del data['attorneys']
                changes_made = True
            
            # Save updated JSON back to file if changes were made
            if changes_made:
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                print(f"Updated: {json_file.name}")
            else:
                print(f"No changes needed: {json_file.name}")
            
            processed_count += 1
            
        except Exception as e:
            print(f"Error processing {json_file.name}: {e}")
            error_count += 1
    
    print(f"\nProcessing complete:")
    print(f"- Successfully processed: {processed_count}")
    print(f"- Errors: {error_count}")

if __name__ == "__main__":
    process_json_files()