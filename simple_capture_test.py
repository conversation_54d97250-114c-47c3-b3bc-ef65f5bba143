#!/usr/bin/env python3
"""
Simple test to verify GraphQL interception is working at all.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestConfig:
    def __init__(self):
        self.config = {
            'camoufox': {
                'browser': {
                    'headless': False,
                    'timeout': 60000,
                    'viewport': {'width': 1920, 'height': 1080}
                },
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                    'refresh_before_expiry_seconds': 30
                },
                'anti_bot': {
                    'humanize': True,
                    'mouse_curves': True,
                    'typing_variation': True,
                    'disable_ad_blocker_detection': True,
                    'block_resources_for_performance': False
                },
                'search': {
                    'typing_delay': 120,
                    'suggestion_wait': 3000,
                    'capture_wait': 5
                }
            }
        }

async def test_simple_capture():
    """Simple test - just navigate to Facebook and capture any responses."""
    logger.info("🔍 Simple capture test - just navigate and capture")
    
    config = TestConfig().config
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )
    
    try:
        # Create session
        logger.info("🚀 Creating browser session")
        success = await session_manager.create_new_session()
        if not success:
            logger.error("❌ Failed to create session")
            return False
        
        # Start GraphQL capture BEFORE any navigation
        logger.info("📡 Setting up GraphQL capture")
        await session_manager._setup_graphql_interception()
        logger.info("✅ GraphQL capture active")
        
        # Navigate to Facebook Ad Library (this should trigger some responses)
        logger.info("🌐 Navigating to Facebook Ad Library")
        await session_manager.page.goto('https://www.facebook.com/ads/library/', 
                                       wait_until='domcontentloaded', timeout=60000)
        
        logger.info("⏳ Waiting 10 seconds for any network responses")
        await asyncio.sleep(10)
        
        # Check what we captured
        captured = session_manager.get_captured_responses()
        logger.info(f"📊 Captured {len(captured)} responses during navigation")
        
        if captured:
            logger.info("✅ SUCCESS: Response capture is working!")
            logger.info("📋 Processing captured responses:")
            await session_manager._process_captured_graphql_responses()
        else:
            logger.warning("⚠️ No responses captured during Facebook navigation")
        
        return len(captured) > 0
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        logger.info("🧹 Closing browser")
        await session_manager.cleanup()

async def main():
    logger.info("🚀 Simple GraphQL Capture Test")
    logger.info("This will just navigate to Facebook and see if we capture any responses")
    logger.info("=" * 80)
    
    success = await test_simple_capture()
    
    if success:
        logger.info("✅ Basic capture is working")
    else:
        logger.error("❌ Basic capture is not working")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        sys.exit(1)