#!/usr/bin/env python3
"""
Simple S3 version cleanup for July 2025
Uses existing project configuration and environment variables
"""

import asyncio
import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.infrastructure.protocols.logger import LoggerProtocol


class SimpleLogger:
    """Simple logger implementation."""
    
    def debug(self, msg: str):
        print(f"[DEBUG] {msg}")
    
    def info(self, msg: str):
        print(f"[INFO] {msg}")
    
    def warning(self, msg: str):
        print(f"[WARNING] {msg}")
    
    def error(self, msg: str):
        print(f"[ERROR] {msg}")


async def cleanup_july_versions():
    """Clean up S3 versions for July 1-28, 2025."""
    
    print("🗑️  Starting S3 version cleanup for July 1-28, 2025...")
    print("📦 Bucket: lexgenius-dockets")
    print("📋 Keep Latest: 1 version per object")
    print("")
    
    # Initialize S3 service using environment variables
    logger = SimpleLogger()
    
    # S3 configuration from environment
    s3_config = {
        "bucket_name": os.getenv("S3_BUCKET_NAME", "lexgenius-dockets"),
        "aws_access_key": os.getenv("AWS_ACCESS_KEY_ID"),
        "aws_secret_key": os.getenv("AWS_SECRET_ACCESS_KEY"),
        "aws_region": os.getenv("AWS_REGION", "us-west-2"),
    }
    
    # Validate configuration
    if not s3_config["aws_access_key"] or not s3_config["aws_secret_key"]:
        print("❌ Error: AWS credentials not found in environment variables")
        print("   Make sure AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are set")
        return False
    
    s3_service = S3AsyncStorage(
        logger=logger,
        config=s3_config,
        disable_versioning=True
    )
    
    try:
        await s3_service.initialize()
        print(f"✅ Connected to S3 bucket: {s3_config['bucket_name']}")
        print("")
        
        # Generate list of dates from July 1 to July 28, 2025
        start_date = datetime(2025, 7, 1)
        end_date = datetime(2025, 7, 28)
        
        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date.strftime("%Y%m%d"))
            current_date += timedelta(days=1)
        
        total_deleted = 0
        total_kept = 0
        total_objects = 0
        errors = 0
        
        # Process each date
        for i, date in enumerate(dates, 1):
            print(f"📅 Processing {date} ({i}/{len(dates)})")
            
            try:
                # Clean up versions for this date
                result = await s3_service.bulk_cleanup_versions(f"{date}/", keep_latest=1)
                
                if "error" in result:
                    print(f"❌ Error for {date}: {result['error']}")
                    errors += 1
                else:
                    print(f"✅ {date} completed:")
                    print(f"   📊 Objects: {result['objects_processed']}")
                    print(f"   🗑️  Deleted: {result['total_versions_deleted']}")
                    print(f"   💾 Kept: {result['total_versions_kept']}")
                    
                    total_objects += result['objects_processed']
                    total_deleted += result['total_versions_deleted']
                    total_kept += result['total_versions_kept']
                    
                    if result['objects_with_errors'] > 0:
                        print(f"   ⚠️  Objects with errors: {result['objects_with_errors']}")
                        errors += result['objects_with_errors']
            
            except Exception as e:
                print(f"❌ Unexpected error processing {date}: {e}")
                errors += 1
            
            print("")
            
            # Small delay to avoid overwhelming S3
            if i < len(dates):  # Don't sleep after the last item
                await asyncio.sleep(0.5)
        
        # Final summary
        print("🎉 Cleanup Complete!")
        print("📈 Summary:")
        print(f"   📅 Dates Processed: {len(dates)} (July 1-28, 2025)")
        print(f"   📦 Total Objects: {total_objects}")
        print(f"   🗑️  Total Versions Deleted: {total_deleted}")
        print(f"   💾 Total Versions Kept: {total_kept}")
        if errors > 0:
            print(f"   ⚠️  Errors Encountered: {errors}")
        
        # Calculate savings
        if total_deleted > 0:
            print("")
            print("💰 Estimated Savings:")
            print(f"   📉 Reduced version count by: {total_deleted}")
            print(f"   💸 Potential storage cost reduction: ~{total_deleted * 0.023:.2f} USD/month")
            print("      (assuming average 1MB per version at $0.023/GB/month)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    finally:
        await s3_service.cleanup()


async def main():
    """Main entry point."""
    success = await cleanup_july_versions()
    return 0 if success else 1


if __name__ == "__main__":
    print("🚀 LexGenius S3 Version Cleanup - July 2025")
    print("=" * 50)
    sys.exit(asyncio.run(main()))