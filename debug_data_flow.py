#!/usr/bin/env python3
"""
Debug script to trace the data flow in report generation.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from datetime import datetime

# Sample data that would come from the repository after field conversion
sample_data = {
    'title': 'AngioDynamics Inc. and Navilyst Medical Inc. Port Catheter Products Liability Litigation',
    'law_firm': 'Dickerson Oxton LLC',
    'filing_date': '20250714',
    'docket_num': '3:25-cv-01789',
    'court_id': 'casd',
    'added_on': '20250714',
    'versus': 'Weber  v. Angiodynamics, Inc. et al',
    'mdl_num': '3125',
    'allegations': 'Multiple plaintiffs are suing AngioDynamics...',
    's3_link': 'https://cdn.lexgenius.ai/20250714/dockets/casd_25_01789_<PERSON>_v_Angiodynamics_Inc_et_al.pdf',
    's3_html': 'https://cdn.lexgenius.ai/20250714/html/casd_25_01789_Weber_v_Angiodynamics_Inc_et_al.html',
    'html_only': False,
    'is_removed': False,
    'pending_cto': False,
    'is_transferred': False,
    'num_plaintiffs': 1,
    'claims': None
}

def simulate_data_loader_processing(data):
    """Simulate the data processing from ReportsDataLoaderService."""
    
    print("=== SIMULATING DATA LOADER PROCESSING ===")
    
    # Create DataFrame
    df = pd.DataFrame([data])
    
    print(f"1. Initial DataFrame:")
    print(f"   title: '{df['title'].iloc[0]}'")
    print(f"   type: {type(df['title'].iloc[0])}")
    
    # Simulate _process_docket_dataframe_sync
    print(f"\n2. Column mapping (already done in this case)")
    
    # Simulate _clean_docket_data
    print(f"\n3. Cleaning data...")
    
    # Handle title: properly handle NaN values before string conversion
    print(f"   Before cleaning: '{df['title'].iloc[0]}'")
    df['title'] = df['title'].replace({float('nan'): None, 'nan': None}).fillna('').astype(str).str.strip()
    print(f"   After NaN handling: '{df['title'].iloc[0]}'")
    
    df.loc[df['title'].isin(['', 'nan']), 'title'] = 'Unknown Title'
    print(f"   After Unknown Title replacement: '{df['title'].iloc[0]}'")
    
    # Simulate _enhance_titles_with_mdl_lookup
    print(f"\n4. Title enhancement with MDL lookup...")
    print(f"   Title before enhancement: '{df['title'].iloc[0]}'")
    print(f"   (Enhancement would happen here)")
    print(f"   Title after enhancement: '{df['title'].iloc[0]}'")
    
    return df

def simulate_processing_service(df):
    """Simulate the processing from ReportsProcessingService."""
    
    print(f"\n=== SIMULATING PROCESSING SERVICE ===")
    
    print(f"1. Before processing service:")
    print(f"   title: '{df['title'].iloc[0]}'")
    
    # Simulate the processing from processing_service.py
    df['title'] = df['title'].astype(str)
    print(f"2. After astype(str): '{df['title'].iloc[0]}'")
    
    # Check if it would be filtered out
    is_unknown_title = df['title'].iloc[0] == 'Unknown Title'
    is_unknown_defendant = df['versus'].iloc[0] == 'Unknown Defendant'
    is_empty_mdl = df['mdl_num'].iloc[0] == ''
    
    print(f"3. Filtering conditions:")
    print(f"   Is Unknown Title: {is_unknown_title}")
    print(f"   Is Unknown Defendant: {is_unknown_defendant}")
    print(f"   Is Empty MDL: {is_empty_mdl}")
    
    would_filter = is_unknown_title and is_unknown_defendant and is_empty_mdl
    print(f"   Would be filtered out: {would_filter}")
    
    return df

def test_empty_scenarios():
    """Test scenarios that might cause Unknown Title."""
    
    print(f"\n=== TESTING EMPTY SCENARIOS ===")
    
    # Test case where title might be empty from the start
    empty_scenarios = [
        {'title': None, 'name': 'None value'},
        {'title': '', 'name': 'Empty string'},
        {'title': 'nan', 'name': 'String nan'},
        {'title': float('nan'), 'name': 'Float nan'},
        {'title': '   ', 'name': 'Whitespace'},
    ]
    
    for scenario in empty_scenarios:
        print(f"\n--- Testing {scenario['name']} ---")
        test_data = sample_data.copy()
        test_data['title'] = scenario['title']
        
        df = pd.DataFrame([test_data])
        print(f"Input: {repr(scenario['title'])}")
        
        # Apply cleaning
        df['title'] = df['title'].replace({float('nan'): None, 'nan': None}).fillna('').astype(str).str.strip()
        df.loc[df['title'].isin(['', 'nan']), 'title'] = 'Unknown Title'
        
        result = df['title'].iloc[0]
        print(f"Result: '{result}'")
        
        if result == 'Unknown Title':
            print(f"   ✅ Correctly converted to 'Unknown Title'")
        else:
            print(f"   ❌ Unexpected result: '{result}'")

def main():
    """Main debug function."""
    
    print("=== DEBUGGING DATA FLOW FOR TITLE FIELD ===")
    
    # Test normal processing with good data
    df = simulate_data_loader_processing(sample_data)
    df = simulate_processing_service(df)
    
    print(f"\nFinal result: '{df['title'].iloc[0]}'")
    
    if df['title'].iloc[0] == 'Unknown Title':
        print("❌ PROBLEM: Good data was converted to 'Unknown Title'")
    else:
        print("✅ GOOD: Title was preserved correctly")
    
    # Test edge cases
    test_empty_scenarios()
    
    print(f"\n=== CONCLUSION ===")
    print("If the actual data has a valid title but the report shows 'Unknown Title',")
    print("the issue is likely in:")
    print("1. The repository field conversion")
    print("2. Some other processing step not simulated here")
    print("3. A caching issue")
    print("4. The data being queried differently than expected")

if __name__ == "__main__":
    main()