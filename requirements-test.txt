# Testing requirements for LexGenius
# Install with: pip install -r requirements-test.txt

# Core testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-timeout>=2.1.0
pytest-xdist>=3.3.1  # For parallel test execution

# Mocking and test utilities
freezegun>=1.2.2  # For mocking time/dates
responses>=0.23.1  # For mocking HTTP requests
moto>=4.2.0  # For mocking AWS services (optional, for more complete AWS mocking)

# Code quality and linting (optional but recommended)
pytest-flake8>=1.1.1
pytest-mypy>=0.10.3
pytest-black>=0.3.12

# Test data generation
faker>=19.0.0
factory-boy>=3.3.0

# Performance testing
pytest-benchmark>=4.0.0

# Rich output for better test visibility
pytest-rich>=0.1.1