extends: default

rules:
  # Line length to match black's default
  line-length:
    max: 88
    level: warning

  # Allow longer lines in comments
  comments:
    min-spaces-from-content: 1

  # Indentation
  indentation:
    spaces: 2
    indent-sequences: true
    check-multi-line-strings: false

  # Brackets
  brackets:
    min-spaces-inside: 0
    max-spaces-inside: 1

  # Braces  
  braces:
    min-spaces-inside: 0
    max-spaces-inside: 1

  # Document start/end
  document-start:
    present: false

  # Truthy values
  truthy:
    allowed-values: ['true', 'false', 'yes', 'no']
    check-keys: false

# File patterns to ignore
ignore: |
  archive/
  .git/
  .venv/
  venv/
  __pycache__/
  *.pyc
  data/