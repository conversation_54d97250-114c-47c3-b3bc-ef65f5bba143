# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LexGenius is a legal intelligence platform that monitors Facebook ads from law firms and court dockets through PACER. It processes this data to generate weekly reports combining ad campaign insights with litigation updates for mass tort cases.

## Architecture

The codebase follows a modern service-oriented architecture with dependency injection:

### Core Service Architecture (`src/services/`)
- **`ai/`** - AI model services (DeepSeek, Mistral, prompt management)
- **`pacer/`** - 24 specialized PACER services with async workflows
- **`fb_ads/`** - Facebook ad processing pipeline services
- **`reports/`** - Report generation and publishing services  
- **`orchestration/`** - Workflow coordination and service orchestration
- **`transformer/`** - 30 active transformation services (16,902 lines) with Phase 3 refactored architecture

### Deprecated Components (Backward Compatibility Only)
- **`src/pacer/`** - Legacy PACER components (deprecated, use `src/services/pacer/`)
- **`src/reports/`** - Legacy report components (deprecated, use `src/services/reports/`)
- **`archive/deprecated-src/`** - Archive of deprecated components moved out of active codebase

### Legacy Utilities (`src/lib/`)
- **`utils/`** - Basic utilities (being phased out in favor of services)
- Legacy modules maintained for backward compatibility

### Service Patterns
- **Dependency Injection**: Dependency injection shall be implemented using a container-facade pattern. The `dependency_injector` framework shall be used for defining containers and wiring dependencies at the composition root. Services shall primarily use constructor injection for receiving their dependencies, explicitly defining them in their `__init__` methods and avoiding direct `dependency_injector` imports or `@inject decorators`.
- **Service Base Classes**: `AsyncServiceBase` with lifecycle management and proper cleanup
- **Repository Pattern**: Async repositories with protocol-based interfaces for data access
- **Component Pattern**: Standardized error handling and logging across services

Key architectural patterns:
- Asynchronous I/O operations throughout with proper resource cleanup
- SQLite-based queue for deferred image processing
- PHash-based image deduplication
- Hybrid rule-based and ML classification for adsw
- Multi-model AI support (GPT-4, DeepSeek, LLaMA)
- Factory-based dependency injection for service composition

## Common Commands

### Environment Setup
```bash
conda env create -f environment.yml
conda activate lexgenius
```

**Important**: Always ensure you're in the `lexgenius` conda environment before development:
- Use `mamba` for faster package installation when needed
- The project uses `dependency-injector` framework for DI (already in environment.yml)
- Python 3.11 is required for optimal performance and type checking

### Main Workflows

Run the main pipeline with configuration:
```bash
python src/main.py --params config/scrape.yml     # Scrape PACER data
python src/main.py --params config/transform.yml   # Process and upload data
python src/main.py --params config/report.yml      # Generate daily report
python src/main.py --params config/weekly_report.yml # Generate weekly report
```

Run full pipeline:
```bash
./run_pipeline.sh --config scraper,transform,report
```

### Facebook Ad Processing
```bash
./run_update_fb_campaigns.sh  # Update campaign classifications
python src/scripts/process_image_queue.py --process  # Process queued images
python classify_all_ads.py    # Classify all ads
```

### Development Tools
```bash
# Maintenance
scripts/maintainence/clean_pycaches.sh    # Clean __pycache__, .pytest_cache, .mypy_cache
scripts/maintainence/find_import_errors.sh # Check for import errors with mypy
./search_json_files.sh <term>             # Search JSON files

# Linting and Type Checking
mypy src/                                 # Type check entire src directory
mypy src/lib                             # Type check legacy utilities specifically

# Testing
python run_tests.py                       # Run all tests with coverage
python run_tests.py --parallel           # Run tests in parallel
python run_tests.py --coverage           # Run with coverage reporting
python run_tests.py unit                 # Run only unit tests
python run_tests.py integration          # Run integration tests
python run_tests.py quick                # Skip slow tests
python run_tests.py fb-ads               # Run FB ads tests specifically
python run_tests.py --file <filepath>    # Run specific test file
python run_tests.py --module <module>    # Run tests for specific module

# Direct pytest commands
pytest tests/ -m unit                    # Run only unit tests
pytest tests/ -m "not slow"              # Skip slow tests
pytest tests/ --cov=src --cov-report=html # Generate HTML coverage report
pytest tests/ --cov=src --cov-report=term # Terminal coverage report

# Specific test categories
pytest tests/ -m requires_aws             # AWS integration tests
pytest tests/ -m integration             # Integration tests
pytest tests/ -m requires_browser         # Browser-dependent tests
pytest tests/ -m regression              # Regression tests
```

## Configuration System

The project uses Pydantic-based type-safe configuration with YAML files:

### Configuration Architecture
- **`src/config_models/`**: Type-safe Pydantic models with validation
  - `scraper.py`: `ScraperConfig` with workflow parameters
  - `features.py`: `FeatureFlags` for gradual service rollouts
  - `base.py`: `WorkflowConfig` base class
  - `loader.py`: YAML loading with validation

### Key Configuration Parameters
- `date`: Target date (MM/DD/YY format)
- `scraper`: Enable PACER scraping
- `post_process`: Enable data transformation
- `upload`: Enable AWS upload
- `report_generator`: Enable report generation
- `fb_ads`: Enable Facebook ad processing
- `headless`: Run browser in headless mode
- `run_parallel`: Enable parallel processing
- `process_single_court`: List of specific court IDs
- `num_workers`: Number of parallel workers

### Service Architecture

All services now use the new refactored architecture by default, which includes:
- Proper browser context lifecycle management and download path coordination
- Enhanced timeout handling for parallel processing
- Prevents "Target closed" errors during document downloads
- Dependency injection with proper resource cleanup

### Environment Variables
- **LEXGENIUS_** prefix for configuration overrides
- **.env** file support for API keys and credentials
- **LEXGENIUS_DATA_DIR**: Override default data directory structure

## Data Flow

1. **Scrape**: Collect data from PACER and Facebook
2. **Process**: Transform, classify, and enrich data
3. **Store**: Save to DynamoDB and S3
4. **Report**: Generate HTML reports and send emails

Deferred processing is used for resource-intensive operations like OCR and image analysis.

## Key Features

- **PHash Deduplication**: Avoids reprocessing duplicate images
- **Campaign Classification**: Combines rules and embeddings to categorize ads
- **Parallel Processing**: Configurable workers for court and file processing
- **Multi-Model Support**: Switches between GPT-4, DeepSeek, and local models
- **Automatic Retries**: Handles failed downloads and processing

## Testing

The project uses pytest with comprehensive testing infrastructure:

### Test Structure
- **Unit Tests**: `pytest tests/ -m unit` - Fast, isolated component tests
- **Integration Tests**: `pytest tests/ -m integration` - Service integration tests  
- **AWS Tests**: `pytest tests/ -m requires_aws` - Tests requiring AWS credentials
- **Slow Tests**: `pytest tests/ -m slow` - Long-running or resource-intensive tests

### Test Execution
```bash
# Run all tests with coverage
python run_tests.py
python run_tests.py --parallel               # Parallel execution

# Specific test categories
pytest tests/ -m unit                        # Fast unit tests only
pytest tests/ -m "not slow"                  # Skip slow tests
pytest tests/ -m requires_aws                # AWS integration tests

# Coverage reporting
pytest tests/ --cov=src --cov-report=html    # HTML coverage report
pytest tests/ --cov=src --cov-report=term    # Terminal coverage report
```

### Legacy Test Files
Individual test files can still be run from root directory:
```bash
python test_classifier_simple.py
python test_hybrid_classifier.py  
python test_dynamodb_upload.py
```

### Test Infrastructure
- **Framework**: pytest with asyncio support
- **Mocking**: pytest-mock, responses, moto for AWS services
- **Fixtures**: Comprehensive fixture library for services and data
- **Parallel Execution**: pytest-xdist for faster test runs
- **Coverage**: pytest-cov with HTML and terminal reporting

## Important Notes

- Data directory is configurable via `LEXGENIUS_DATA_DIR` environment variable
- Default data structure: `data/YYYY/MM/DD/court_id/`
- AWS credentials required for S3 and DynamoDB operations
- PACER credentials required for court document access
- API keys needed for GPT-4 and DeepSeek in `.env` file

## Code Development Guidelines

### Mandatory Dependency Injection Framework

**REQUIREMENT**: All modules in `src/` MUST use dependency injection implemented using a container-facade pattern. The `dependency_injector` framework shall be used for defining containers and wiring dependencies at the composition root. Services shall primarily use constructor injection for receiving their dependencies, explicitly defining them in their `__init__` methods and avoiding direct `dependency_injector` imports or `@inject` decorators.

#### Rationale
- **Testability**: Enables easy mocking and unit testing by injecting dependencies rather than hard-coding them
- **Maintainability**: Reduces coupling between components, making code easier to modify and extend
- **Decoupling**: Separates concerns by removing direct dependencies between classes
- **Configuration Management**: Centralizes service instantiation and configuration
- **Resource Management**: Provides proper lifecycle management and cleanup for async resources

#### Required Implementation Pattern

All services MUST follow this dependency injection pattern using constructor injection without decorators:

```python
# Container definition in composition root only
from dependency_injector import containers, providers

class ServiceContainer(containers.DeclarativeContainer):
    # Configuration
    config = providers.Configuration()
    
    # Dependencies
    storage_service = providers.Singleton(
        StorageService,
        config=config.storage
    )
    
    # Main service with constructor injection
    your_service = providers.Factory(
        YourService,
        storage=storage_service,
        config=config.your_service
    )

# Service implementation - no dependency_injector imports
class YourService(AsyncServiceBase):
    def __init__(self, storage: StorageService, config: Dict[str, Any]):
        super().__init__()
        self.storage = storage
        self.config = config
    
    async def _execute_action(self, data: Any) -> Any:
        return await self.storage.process(data)

# Usage at composition root
container = ServiceContainer()
container.config.from_dict(config_dict)

# Dependencies injected via constructor
service = container.your_service()
```

#### Integration with Existing Patterns

The dependency injection framework MUST be combined with existing architectural patterns using constructor injection:

```python
# Repository with constructor injection - no decorators
class DataRepository(RepositoryBase):
    def __init__(self, db_client: AsyncDynamoDBStorage, config: Dict[str, Any]):
        super().__init__()
        self.db_client = db_client
        self.config = config

# Service with constructor injection extending AsyncServiceBase
class ProcessingService(AsyncServiceBase):
    def __init__(self, repository: DataRepository, ai_service: AIService):
        super().__init__()
        self.repository = repository
        self.ai_service = ai_service
    
    async def _execute_action(self, data: Any) -> Any:
        processed = await self.ai_service.process(data)
        return await self.repository.save(processed)

# Container configuration handles wiring
class ServiceContainer(containers.DeclarativeContainer):
    config = providers.Configuration()
    
    db_client = providers.Singleton(AsyncDynamoDBStorage, config=config.database)
    repository = providers.Singleton(DataRepository, db_client=db_client, config=config.database)
    ai_service = providers.Singleton(AIService, config=config.ai)
    processing_service = providers.Factory(ProcessingService, repository=repository, ai_service=ai_service)
```

### Service Architecture Patterns

- **Use Dependency Injection**: All new code MUST use the `dependency_injection` framework with container-based service creation
- **Async Service Base**: Extend `AsyncServiceBase` from `src/infrastructure/patterns/component_base.py` for new services (116 services across active directories)
- **Repository Pattern**: Use async repositories extending `RepositoryBase` (100% compliance - 7/7 repositories)
- **Component Implementation**: Use `ComponentImplementation` base class for standardized error handling and logging
- **Factory Pattern**: Create services through dependency injection containers rather than direct instantiation

#### Base Class Hierarchy
```python
# For services with lifecycle management - constructor injection only
class MyService(AsyncServiceBase):
    def __init__(self, dependency: SomeDependency):
        super().__init__()
        self.dependency = dependency
    
    async def _execute_action(self, data: Any) -> Any:
        return await self._process_data(data)

# For components needing standardized error handling  
class MyComponent(ComponentImplementation):
    def __init__(self, dependency: SomeDependency):
        super().__init__()
        self.dependency = dependency
    
    async def _execute_action(self, data: Any) -> Any:
        return processed_data

# For data access layers
class MyRepository(RepositoryBase):
    def __init__(self, storage: StorageService):
        super().__init__()
        self.storage = storage
    
    async def find_by_id(self, entity_id: str) -> Optional[Entity]:
        return await self.storage.get(entity_id)

# Container wires dependencies at composition root
class MyContainer(containers.DeclarativeContainer):
    config = providers.Configuration()
    
    dependency = providers.Singleton(SomeDependency, config=config.dependency)
    storage = providers.Singleton(StorageService, config=config.storage)
    
    service = providers.Factory(MyService, dependency=dependency)
    component = providers.Factory(MyComponent, dependency=dependency)
    repository = providers.Factory(MyRepository, storage=storage)
```

### Reuse Existing Code

- **ALWAYS search for existing code before creating new functionality**
- **For Services**: Check `src/services/` for existing service implementations before creating new ones
- **For Storage**: Use existing async storage services (`AsyncDynamoDBStorage`, `S3AsyncStorage`)
- **For AWS Services**: Use the service-based AWS integrations in `src/services/` rather than creating new ones
- **For Common Operations**: Check `src/lib/utils/` for utility functions before writing new ones
- **Legacy Code**: Prefer refactoring legacy `src/lib/` code to `src/services/` patterns when possible

### Modern Development Patterns

- **Type Safety**: Use Pydantic models for configuration and data validation
- **Async/Await**: All I/O operations should be asynchronous with proper resource cleanup
- **Error Handling**: Use the `ComponentImplementation` base class for standardized error handling
- **Testing**: Write tests using pytest with appropriate markers (`@pytest.mark.unit`, `@pytest.mark.integration`)

## Important Operational Guidelines

- **DO NOT RUN PIPELINE COMMANDS WITHOUT USER APPROVAL**

## Critical Path Validation - DO NOT MODIFY

**CRITICAL WARNING: The following code paths are essential for failed download reprocessing and parallel browser operations. DO NOT MODIFY without understanding the full impact:**

### Download Path Validation
- **`src/pacer/pacer_document_downloader.py`**: `context_download_path` validation logic (original)
- **`src/services/pacer/file_management_service.py`**: Download path infrastructure (new services)
- **`src/services/pacer/court_processing_service.py`**: Download path coordination (new services)
  - Ensures downloaded files are placed in correct directories
  - Prevents files from being lost in temp directories during parallel processing
  - Required for browser service download coordination
  - **CRITICAL**: Both original and new services preserve the same download path patterns

### Artifact Verification for Reprocessing
- **`src/pacer/file_manager.py`**: Artifact verification methods
  - `check_if_artifact_exists_by_pattern()`
  - `check_if_artifact_exists_last_7_days_by_pattern()`
  - `check_if_artifact_exists_across_dates_by_pattern()`
  - These methods ONLY check PDF/ZIP files and ignore JSON content
  - Essential for allowing reprocessing of failed downloads

### Case Verification Logic
- **`src/pacer/docket_processor.py`**: `verify_case()` method
  - `_is_explicitly_requested` logic uses artifact-only verification
  - Allows failed downloads to be retried by ignoring JSON skip reasons
  - Critical for `reprocess_failed` and `process_review_cases` functionality

**These paths were specifically designed to handle the complex interaction between:**
- Parallel browser contexts with separate download directories
- Failed download detection and reprocessing
- Artifact verification vs JSON content analysis
- Explicitly requested vs report-scraped case handling

## Database Schemas

The system uses DynamoDB tables with the following schemas:

### FBAdArchive
**Primary Key:** `AdArchiveID` (String), `StartDate` (String)
**GSIs:** StartDate-index, PageID-index, PageID-StartDate-index, PageID-LastUpdated-index, LastUpdated-index, AdArchiveID-index

Key fields: AdArchiveID, StartDate, PageID, PageName, LawFirm, LastUpdated, IsActive, EndDate, PublisherPlatform, LinkUrl, VideoHdUrl, VideoSdUrl, VideoPreviewImageUrl, OriginalImageUrl, ResizedImageUrl, LinkDescription, Body, Caption, CtaText, Title, S3ImageKey, ImageText, Summary, LLM, IsForbidden403, Category, Company, Product, Injuries, LitigationType, LitigationName, MdlName, AdCreativeId

### Pacer
**Primary Key:** `FilingDate` (String, YYYYMMDD), `DocketNum` (String)
**GSIs:** AddedOn-index, CourtId-DocketNum-index, MdlNum-FilingDate-index, TransfereeCourtId-TransfereeDocketNum-index, TransferorCourtId-TransferorDocketNum-index, LawFirm-FilingDate-index, CourtId-FilingDate-index

Key fields: FilingDate, DocketNum, CourtId, DateFiled, AddedDate, AddedDateIso, AddedOn, Versus, Cause, CauseFromReport, Nos, NosFromReport, SourcePage, Jurisdiction, JuryDemand, Office, AssignedTo, ReferredTo, Plaintiff, Plaintiffs, Defendant, Defendants, Attorney, Attorneys, PlaintiffsGpt, AttorneysGpt, LawFirm, LawFirms, BaseFilename, NewFilename, OriginalFilename, IsDownloaded, S3Html, S3Link, IsRemoval, IsTransferred, TransferredIn, PendingCto, TransfereeCourtId, TransfereeDocketNum, TransferorCourtId, TransferorDocketNum, TransferorDocketLawFirm, MdlNum, MdlCat, LeadCase, Title, Summary, Allegations, Flags, ReasonRelevant, HtmlOnly, ProcessingNotes

### LawFirms
**Primary Key:** `ID` (String), `Name` (String)

Fields: ID, Name, PageAlias, Category, ImageURI, AdArchiveLastUpdated, NumAds, LastUpdated

### FBImageHash
**Primary Key:** `PHash` (String), `AdArchiveID` (String)
**GSIs:** AdArchiveID-index

Fields: PHash, AdArchiveID, ImageUrl, S3ImageKey, CreatedDate, LastSeen

### DistrictCourts
**Primary Key:** `CourtId` (String), `MdlNum` (String)
**GSIs:** TransfereeCourtId-TransfereeDocketNum-index

Fields: CourtId, MdlNum, CourtName, District, State, TransfereeCourtId, TransfereeDocketNum, IsActive, LastUpdated

**Schema Notes:**
- Date format: YYYYMMDD for date fields
- Boolean fields properly converted to DynamoDB boolean type
- Field naming: PascalCase (e.g., AdArchiveID, StartDate)
- PHash-based image deduplication via FBImageHash table
- Extensive GSI usage for efficient querying

## Law Firm Processing Fallback Behavior

The active transformer services (`src/services/transformer/`) include robust law firm processing with automatic fallback capabilities:

### Primary Processing: `attorneys_gpt` Field
- **Preferred Source**: The system first attempts to extract law firms from the `attorneys_gpt` field
- **Enhanced Processing**: Includes attorney lookup integration to populate missing law firms
- **Normalization**: Applies law firm name cleaning and standardization

### Automatic Fallback: `attorney` Field
When `attorneys_gpt` is not available or contains no valid law firms, the system automatically falls back to the `attorney` field:

**Fallback Triggers:**
- `attorneys_gpt` is empty (`[]`)
- `attorneys_gpt` is `None` or not present
- `attorneys_gpt` exists but contains no valid law firm data (all null/empty)

**Fallback Processing:**
- **Multiple Formats**: Handles both list of attorney dicts and single attorney dict
- **Same Standards**: Applies identical cleaning, normalization, and deduplication as primary processing
- **Null Filtering**: Filters out `Pro Se`, `N/A`, empty strings, etc.
- **Consistent Output**: Generates same format regardless of source field

**Enhanced Logging**: Fallback behavior includes detailed logging with visual indicators:
- `📋 FALLBACK TRIGGER`: When fallback is initiated
- `✅ FALLBACK PROCESSING`: During fallback processing
- `🏢 EXTRACTED`: When law firms are successfully extracted
- `🎯 FALLBACK SUCCESS`: Final success summary


## Development Guidelines

### Folder and File Structure

- **Do not create any folder that starts with @.** @ is used to denote the file_path for reference. 

### Git Commits
- **DO NOT ADD** 🤖 Generated with [Claude Code](https://claude.ai/code) to any commits
- **NEVER EVER USE** `git reset --hard HEAD`
- **NEVER REMOVE A BRANCH** without asking user.
- **NEVER REMOVE A COMMIT** without asking user.
