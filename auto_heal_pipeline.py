#!/usr/bin/env python3
"""
Auto-healing pipeline runner for LexGenius
Continuously runs the pipeline and attempts to resolve errors automatically.
"""
import asyncio
import logging
import os
import re
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('auto_heal_pipeline.log')
    ]
)
logger = logging.getLogger(__name__)

class PipelineAutoHealer:
    """Auto-healing pipeline runner with error detection and resolution."""

    def __init__(self, max_retries: int = 5, retry_delay: int = 30):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.error_patterns = {
            'dependency_injection': r'TypeError.*got an unexpected keyword argument',
            'missing_argument': r'missing.*required.*argument',
            'import_error': r'ImportError|ModuleNotFoundError',
            'config_error': r'ConfigurationError|Config.*not found',
            'connection_error': r'ConnectionError|ConnectTimeout',
            'permission_error': r'PermissionError|Permission denied'
        }
        self.healing_strategies = {
            'dependency_injection': self._heal_dependency_injection,
            'missing_argument': self._heal_missing_argument,
            'import_error': self._heal_import_error,
            'config_error': self._heal_config_error,
            'connection_error': self._heal_connection_error,
            'permission_error': self._heal_permission_error
        }

    @staticmethod
    def run_pipeline(config: str = "transform.yml") -> Tuple[bool, str, str]:
        """Run the pipeline and capture output."""
        try:
            cmd = ["./run_pipeline.sh", "--config", config]
            logger.info(f"🚀 Running: {' '.join(cmd)}")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                cwd=Path.cwd()
            )

            success = result.returncode == 0
            stdout = result.stdout
            stderr = result.stderr

            if success:
                logger.info("✅ Pipeline completed successfully")
            else:
                logger.error(f"❌ Pipeline failed with exit code: {result.returncode}")

            return success, stdout, stderr

        except subprocess.TimeoutExpired:
            logger.error("⏰ Pipeline timed out")
            return False, "", "Pipeline execution timed out"
        except Exception as e:
            logger.error(f"💥 Unexpected error running pipeline: {e}")
            return False, "", str(e)

    def detect_error_type(self, output: str) -> Optional[str]:
        """Detect the type of error from pipeline output."""
        combined_output = output.lower()

        for error_type, pattern in self.error_patterns.items():
            if re.search(pattern, combined_output, re.IGNORECASE):
                logger.info(f"🔍 Detected error type: {error_type}")
                return error_type

        return None

    def _heal_dependency_injection(self, output: str) -> bool:
        """Attempt to heal dependency injection errors."""
        logger.info("🔧 Attempting to heal dependency injection error...")

        # Extract service name from error
        match = re.search(r"(\w+).__init__\(\) got an unexpected keyword argument '(\w+)'", output)
        if match:
            service_name = match.group(1)
            invalid_param = match.group(2)
            logger.info(f"📝 Found DI error: {service_name} has invalid parameter '{invalid_param}'")

            # This would require Claude Code intervention for actual fixes
            logger.warning("🤖 Dependency injection errors require manual intervention")
            return False

        return False

    def _heal_missing_argument(self, output: str) -> bool:
        """Attempt to heal missing argument errors."""
        logger.info("🔧 Attempting to heal missing argument error...")
        # Implementation would depend on specific missing arguments
        return False

    def _heal_import_error(self, output: str) -> bool:
        """Attempt to heal import errors."""
        logger.info("🔧 Attempting to heal import error...")

        # Try to install missing packages
        match = re.search(r"No module named '(\w+)'", output)
        if match:
            module_name = match.group(1)
            logger.info(f"📦 Attempting to install missing module: {module_name}")

            try:
                subprocess.run([sys.executable, "-m", "pip", "install", module_name],
                             check=True, capture_output=True)
                logger.info(f"✅ Successfully installed {module_name}")
                return True
            except subprocess.CalledProcessError:
                logger.error(f"❌ Failed to install {module_name}")

        return False

    def _heal_config_error(self, output: str) -> bool:
        """Attempt to heal configuration errors."""
        logger.info("🔧 Attempting to heal configuration error...")
        # Implementation would check for missing config files
        return False

    def _heal_connection_error(self, output: str) -> bool:
        """Attempt to heal connection errors."""
        logger.info("🔧 Attempting to heal connection error...")
        logger.info("⏳ Waiting for network connectivity...")
        time.sleep(10)
        return True

    def _heal_permission_error(self, output: str) -> bool:
        """Attempt to heal permission errors."""
        logger.info("🔧 Attempting to heal permission error...")
        # Implementation would fix file permissions
        return False

    def heal_error(self, error_type: str, output: str) -> bool:
        """Apply healing strategy for detected error type."""
        if error_type in self.healing_strategies:
            return self.healing_strategies[error_type](output)
        else:
            logger.warning(f"🤷 No healing strategy available for error type: {error_type}")
            return False

    async def continuous_run(self, config: str = "transform.yml", watch_interval: int = 60):
        """Continuously run the pipeline with auto-healing."""
        logger.info("🎯 Starting continuous pipeline monitoring with auto-healing")
        logger.info(f"📊 Max retries: {self.max_retries}, Retry delay: {self.retry_delay}s")

        run_count = 0
        consecutive_failures = 0

        while True:
            run_count += 1
            retry_count = 0

            logger.info(f"\n{'='*60}")
            logger.info(f"🏃 Pipeline Run #{run_count} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"{'='*60}")

            while retry_count < self.max_retries:
                success, stdout, stderr = self.run_pipeline(config)

                if success:
                    logger.info("🎉 Pipeline completed successfully!")
                    consecutive_failures = 0
                    break
                else:
                    retry_count += 1
                    consecutive_failures += 1

                    logger.error(f"💔 Pipeline failed (attempt {retry_count}/{self.max_retries})")

                    # Detect and attempt to heal errors
                    combined_output = stdout + stderr
                    error_type = self.detect_error_type(combined_output)

                    if error_type:
                        logger.info(f"🩺 Attempting to heal {error_type} error...")
                        healed = self.heal_error(error_type, combined_output)

                        if healed:
                            logger.info("✨ Error healing successful, retrying immediately...")
                            continue
                        else:
                            logger.warning("🚫 Error healing failed")

                    if retry_count < self.max_retries:
                        logger.info(f"⏳ Waiting {self.retry_delay}s before retry...")
                        await asyncio.sleep(self.retry_delay)

            # Check if we should stop due to too many consecutive failures
            if consecutive_failures >= self.max_retries * 2:
                logger.error("🛑 Too many consecutive failures, stopping continuous monitoring")
                break

            # Wait before next run
            logger.info(f"⏰ Next run in {watch_interval}s...")
            await asyncio.sleep(watch_interval)

async def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(description="Auto-healing pipeline runner")
    parser.add_argument("--config", default="transform.yml", help="Pipeline config file")
    parser.add_argument("--max-retries", type=int, default=5, help="Maximum retry attempts")
    parser.add_argument("--retry-delay", type=int, default=30, help="Delay between retries (seconds)")
    parser.add_argument("--watch-interval", type=int, default=300, help="Interval between runs (seconds)")

    args = parser.parse_args()

    healer = PipelineAutoHealer(
        max_retries=args.max_retries,
        retry_delay=args.retry_delay
    )

    try:
        await healer.continuous_run(
            config=args.config,
            watch_interval=args.watch_interval
        )
    except KeyboardInterrupt:
        logger.info("\n🛑 Monitoring stopped by user")
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
