#!/usr/bin/env python3
"""
Complete court ID conversion script.

This script:
1. Renames docket report log files from uppercase to lowercase
2. Renames docket JSON and ZIP files to use lowercase court IDs  
3. Updates JSON content inside the renamed files

Usage:
    python complete_court_id_conversion.py [--dry-run]
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List, Tuple
import argparse
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()

# Directory paths
DATA_DIR = Path("/Users/<USER>/PycharmProjects/lexgenius/data/20250703")
DOCKET_REPORT_DIR = DATA_DIR / "logs" / "docket_report"
DOCKETS_DIR = DATA_DIR / "dockets"
BACKUP_DIR = DATA_DIR / "backup_complete_conversion"

class CompleteConverter:
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.console = Console()
        self.changes_made = []
        
    def backup_files(self, files_to_backup: List[Path]) -> None:
        """Create backup of files before modification."""
        if not files_to_backup:
            return
            
        BACKUP_DIR.mkdir(parents=True, exist_ok=True)
        
        for file_path in files_to_backup:
            if file_path.exists():
                backup_path = BACKUP_DIR / file_path.name
                if not self.dry_run:
                    shutil.copy2(file_path, backup_path)
                    
        console.print(f"[green]✓ Backed up {len(files_to_backup)} files to {BACKUP_DIR}[/green]")
    
    def rename_docket_report_files(self) -> List[Tuple[str, str]]:
        """Rename docket report log files from uppercase to lowercase."""
        if not DOCKET_REPORT_DIR.exists():
            console.print(f"[red]Directory not found: {DOCKET_REPORT_DIR}[/red]")
            return []
            
        json_files = list(DOCKET_REPORT_DIR.glob("*.json"))
        files_to_rename = []
        
        for file_path in json_files:
            if file_path.stem.isupper():
                new_name = file_path.stem.lower() + file_path.suffix
                new_path = file_path.parent / new_name
                
                if new_path.exists():
                    console.print(f"[yellow]⚠ Target file already exists: {new_path}[/yellow]")
                    continue
                    
                files_to_rename.append((file_path, new_path))
        
        if not files_to_rename:
            console.print("[yellow]No uppercase docket report files found to rename[/yellow]")
            return []
        
        # Backup files
        backup_files = [f[0] for f in files_to_rename]
        self.backup_files(backup_files)
            
        renames = []
        console.print(f"[green]Renaming {len(files_to_rename)} docket report files...[/green]")
        for old_path, new_path in files_to_rename:
            if not self.dry_run:
                os.rename(old_path, new_path)
            self.changes_made.append(f"Renamed: {old_path.name} → {new_path.name}")
            renames.append((str(old_path), str(new_path)))
            console.print(f"  {old_path.name} → {new_path.name}")
        
        return renames
    
    def process_docket_files(self) -> List[Dict]:
        """Process docket files: rename files AND update JSON content."""
        if not DOCKETS_DIR.exists():
            console.print(f"[red]Directory not found: {DOCKETS_DIR}[/red]")
            return []
            
        # Get all JSON files with uppercase court IDs
        json_files = list(DOCKETS_DIR.glob("*.json"))
        files_to_process = []
        
        for json_file in json_files:
            # Extract court ID from filename
            filename_parts = json_file.stem.split('_')
            if filename_parts:
                court_id = filename_parts[0]
                if court_id.isupper():
                    files_to_process.append((json_file, court_id))
        
        if not files_to_process:
            console.print("[yellow]No uppercase court ID files found to process[/yellow]")
            return []
        
        processed_files = []
        console.print(f"[green]Processing {len(files_to_process)} files with uppercase court IDs...[/green]")
        
        for i, (json_file, old_court_id) in enumerate(files_to_process, 1):
            console.print(f"  [{i}/{len(files_to_process)}] {json_file.name}")
            result = self._process_single_file(json_file, old_court_id)
            if result:
                processed_files.append(result)
        
        return processed_files
    
    def _process_single_file(self, json_file: Path, old_court_id: str) -> Dict:
        """Process a single file: rename files AND update JSON content."""
        new_court_id = old_court_id.lower()
        
        # Step 1: Backup files
        files_to_backup = [json_file]
        zip_file = json_file.with_suffix('.zip')
        if zip_file.exists():
            files_to_backup.append(zip_file)
        
        self.backup_files(files_to_backup)
        
        # Step 2: Read and update JSON content
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            console.print(f"    [red]Error reading JSON: {e}[/red]")
            return None
        
        # Update JSON content
        json_changes = self._update_json_content(data, old_court_id, new_court_id)
        
        # Step 3: Rename files using two-step process
        files_renamed = []
        
        # Rename ZIP file first (if it exists)
        if zip_file.exists():
            new_zip_name = zip_file.name.replace(old_court_id, new_court_id, 1)
            new_zip_path = zip_file.parent / new_zip_name
            
            console.print(f"    ZIP: {zip_file.name} → {new_zip_name}")
            if new_zip_path != zip_file:
                if not self.dry_run:
                    temp_zip_path = zip_file.parent / f"TEMP_{zip_file.name}"
                    os.rename(zip_file, temp_zip_path)
                    os.rename(temp_zip_path, new_zip_path)
                self.changes_made.append(f"Renamed ZIP: {zip_file.name} → {new_zip_name}")
                files_renamed.append(f"ZIP: {zip_file.name} → {new_zip_name}")
        
        # Rename JSON file
        new_json_name = json_file.name.replace(old_court_id, new_court_id, 1)
        new_json_path = json_file.parent / new_json_name
        
        console.print(f"    JSON: {json_file.name} → {new_json_name}")
        if new_json_path != json_file:
            if not self.dry_run:
                # Write updated JSON content to the new file
                temp_json_path = json_file.parent / f"TEMP_{json_file.name}"
                
                # First write updated content to temp file
                with open(temp_json_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                # Remove original file and rename temp to final name
                os.remove(json_file)
                os.rename(temp_json_path, new_json_path)
            
            self.changes_made.append(f"Renamed JSON: {json_file.name} → {new_json_name}")
            files_renamed.append(f"JSON: {json_file.name} → {new_json_name}")
        else:
            # Same filename, just update content
            if not self.dry_run:
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
        
        return {
            'original_file': json_file.name,
            'old_court_id': old_court_id,
            'new_court_id': new_court_id,
            'files_renamed': files_renamed,
            'json_changes': json_changes
        }
    
    def _update_json_content(self, data: Dict, old_court_id: str, new_court_id: str) -> List[str]:
        """Update JSON content replacing old court_id with new court_id."""
        changes = []
        
        def update_field(field_name: str, value):
            if isinstance(value, str) and old_court_id in value:
                new_value = value.replace(old_court_id, new_court_id)
                data[field_name] = new_value
                changes.append(f"{field_name}: {old_court_id} → {new_court_id}")
                return new_value
            return value
        
        # Update specific fields
        fields_to_update = ['court_id', 'base_filename', 'new_filename', 's3_html', 's3_link']
        
        for field in fields_to_update:
            if field in data:
                update_field(field, data[field])
        
        # Update any other string fields that might contain the court_id
        for key, value in data.items():
            if key not in fields_to_update and isinstance(value, str) and old_court_id in value:
                update_field(key, value)
        
        return changes
    
    def run(self) -> None:
        """Run the complete conversion process."""
        console.print(Panel.fit(
            "[bold blue]Complete Court ID Conversion[/bold blue]\n"
            f"Data Directory: {DATA_DIR}\n"
            f"Mode: {'DRY RUN' if self.dry_run else 'LIVE RUN'}",
            title="Starting Complete Conversion"
        ))
        
        # Step 1: Rename docket report files
        console.print("\n[bold]Step 1: Renaming docket report log files[/bold]")
        docket_report_renames = self.rename_docket_report_files()
        
        # Step 2: Process docket files (rename + update content)
        console.print("\n[bold]Step 2: Processing docket files (rename + update content)[/bold]")
        processed_files = self.process_docket_files()
        
        # Display results
        self._display_results(docket_report_renames, processed_files)
    
    def _display_results(self, docket_report_renames: List[Tuple[str, str]], processed_files: List[Dict]) -> None:
        """Display summary of changes made."""
        console.print("\n[bold green]Complete Conversion Finished![/bold green]")
        
        # Docket report renames
        if docket_report_renames:
            table = Table(title="Docket Report Files Renamed")
            table.add_column("Original", style="red")
            table.add_column("New", style="green")
            
            for old_path, new_path in docket_report_renames:
                table.add_row(Path(old_path).name, Path(new_path).name)
            
            console.print(table)
        
        # Docket files processed
        if processed_files:
            table = Table(title="Docket Files Processed")
            table.add_column("Original File", style="cyan")
            table.add_column("Court ID", style="yellow")
            table.add_column("Files Renamed", style="green")
            table.add_column("JSON Fields Updated", style="blue")
            
            for file_info in processed_files:
                files_str = f"{len(file_info['files_renamed'])} files"
                json_str = f"{len(file_info['json_changes'])} fields"
                table.add_row(
                    file_info['original_file'],
                    f"{file_info['old_court_id']} → {file_info['new_court_id']}",
                    files_str,
                    json_str
                )
            
            console.print(table)
        
        # Summary
        console.print(f"\n[bold]Summary:[/bold]")
        console.print(f"• Docket report files renamed: {len(docket_report_renames)}")
        console.print(f"• Docket file pairs processed: {len(processed_files)}")
        console.print(f"• Total individual changes: {len(self.changes_made)}")
        
        if self.dry_run:
            console.print("\n[yellow]⚠ This was a DRY RUN - no actual changes were made[/yellow]")
        else:
            console.print(f"\n[green]✓ Backup created in: {BACKUP_DIR}[/green]")


def main():
    parser = argparse.ArgumentParser(description="Complete court ID conversion: rename files and update content")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be changed without making changes")
    args = parser.parse_args()
    
    converter = CompleteConverter(dry_run=args.dry_run)
    converter.run()


if __name__ == "__main__":
    main()