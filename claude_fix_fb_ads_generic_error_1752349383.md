# Auto-Healing Fix Request - Fb_Ads Pipeline

/troubleshoot --investigate --fix --seq

Generic error detected in fb_ads pipeline:
- File: Unknown
- Line: Unknown

The fb_ads pipeline has encountered an error that needs immediate investigation.

Full error analysis and fix needed:
1. Review the complete error traceback and output
2. Identify the root cause of the failure
3. Apply appropriate fixes based on error type
4. Test the fix to ensure pipeline runs successfully
5. Verify no regression issues are introduced

Test command: `python src/main.py --params config/fb_ads.yml`

Context: fb_ads pipeline error requiring immediate investigation and fix

## Error Output
```
Running pipeline step: fb_ads.yml with config /Users/<USER>/PycharmProjects/lexgenius/config/fb_ads.yml
Starting main.py with Python 3.11.13 | packaged by conda-forge | (main, Jun  4 2025, 14:52:34) [Clang 18.1.8 ]
Set multiprocessing start method to 'spawn'.
[07/12/25 15:43:02] INFO     Log propagation ENABLED for 'src.pacer' main.py:549
                             to root (console). Check both console              
                             and                                                
                             /Users/<USER>/PycharmProjects/lex            
                             genius/data/20250712/logs/pacer.log                
                    INFO     Configured separate log file for        main.py:555
                             'src.pacer' at:                                    
                             /Users/<USER>/PycharmProjects/lex            
                             genius/data/20250712/logs/pacer.log                
                    INFO     Log propagation ENABLED for             main.py:549
                             'src.services.reports' to root                     
                             (console). Check both console and                  
                             /Users/<USER>/PycharmProjects/lex            
                             genius/data/20250712/logs/reports_servi            
                             ces.log                                            
                    INFO     Configured separate log file for        main.py:555
                             'src.services.reports' at:                         
                             /Users/<USER>/PycharmProjects/lex            
                             genius/data/20250712/logs/reports_servi            
                             ces.log                                            
                    INFO     Log propagation ENABLED for             main.py:549
                             'src.services.transformer' to root                 
                             (console). Check both console and                  
                             /Users/<USER>/PycharmProjects/lex            
                             genius/data/20250712/logs/transformer.l            
                             og                                                 
                    INFO     Configured separate log file for        main.py:555
                             'src.services.transformer' at:                     
                             /Users/<USER>/PycharmProjects/lex            
                             genius/data/20250712/logs/transformer.l            
                             og                                                 
                    INFO     Configured separate log file for        main.py:555
                             'src.logging_config' at:                           
                             /Users/<USER>/PycharmProjects/lex            
                             genius/data/20250712/logs/logging_confi            
                             g_module.log                                       
                    INFO     Initial logging setup complete. Log    main.py:1921
                             directory:                                         
                             /Users/<USER>/PycharmProjects/le             
                             xgenius/data/20250712/logs                         
                    INFO     Base data directory:                   main.py:1924
                             /Users/<USER>/PycharmProjects/le             
                             xgenius/data                                       
                    INFO     No end_date specified, defaulting to   main.py:2013
                             target date: 07/12/25                              
                    INFO     Preparing for single date run:         main.py:2062
                             07/12/25                                           
                    INFO     DEBUG: Merging params into config     loader.py:170
                             data for fb_ads                                    
                    INFO     DEBUG: process_single_court in YAML:  loader.py:171
                             NOT_SET                                            
                    INFO     DEBUG: process_single_court in        loader.py:172
                             params: []                                         
                    INFO     DEBUG: process_single_court after     loader.py:174
                             merge: []                                          
[07/12/25 15:43:02] INFO     Log propagation ENABLED for 'src.pacer' main.py:549
                             to root (console). Check both console              
                             and data/20250712/logs/pacer.log                   
                    INFO     Configured separate log file for        main.py:555
                             'src.pacer' at:                                    
                             data/20250712/logs/pacer.log                       
                    INFO     Log propagation ENABLED for             main.py:549
                             'src.services.reports' to root                     
                             (console). Check both console and                  
                             data/20250712/logs/reports_services.log            
                    INFO     Configured separate log file for        main.py:555
                             'src.services.reports' at:                         
                             data/20250712/logs/reports_services.log            
                    INFO     Log propagation ENABLED for             main.py:549
                             'src.services.transformer' to root                 
                             (console). Check both console and                  
                             data/20250712/logs/transformer.log                 
                    INFO     Configured separate log file for        main.py:555
                             'src.services.transformer' at:                     
                             data/20250712/logs/transformer.log                 
                    INFO     Configured separate log file for        main.py:555
                             'src.logging_config' at:                           
                             data/20250712/logs/logging_config_modul            
                             e.log                                              
                    INFO     Logging re-configured for target date  main.py:2074
                             07/12/25. Log dir: data/20250712/logs              
                    INFO     --- Initializing Main Orchestration    main.py:2088
                             for Single Date Run ---                            
                    INFO     MainServiceFactory initialized   main_factory.py:27
                             with config: Unknown config                        
                    INFO     🚀 Using dependency-injector     main_factory.py:30
                             framework                                          
                    INFO     Entering MainServiceFactory      main_factory.py:89
                             async context.                                     
                    INFO     DI Container created and wired  main_factory.py:116
                             successfully                                       
                    INFO     MainOrchestrator initialized   component_base.py:88
                    INFO     MainOrchestrator run started   component_base.py:88
                    INFO     Scraping phase skipped by      component_base.py:88
                             configuration                                      
                    INFO     Post-processing phase skipped  component_base.py:88
                             by configuration                                   
                    INFO     Upload phase skipped by        component_base.py:88
                             configuration                                      
                    INFO     Facebook Ads processing phase  component_base.py:88
                             initiated                                          
                    INFO     Creating FbAdsOrchestrator via  main_factory.py:257
                             DI Container...                                    
                    INFO     Loaded 26 prompts              component_base.py:88
                    WARNING  S3 storage not properly        component_base.py:95
                             injected - S3 operations will                      
                             be disabled                                        
                    INFO     🔍 VALIDATION: Checking AI     component_base.py:88
                             service requirements...                            
                    INFO       - Available API key sources: component_base.py:88
                             4/4                                                
                    INFO       - AI services validation: ✅ component_base.py:88
                             PASSED                                             
                    INFO     FbAdsOrchestrator initialized  component_base.py:88
                             with DI services                                   
                    INFO     ✅ AI Orchestrator injected    component_base.py:88
                             via DI                                             
                    INFO     ✅ DeepSeek service injected   component_base.py:88
                             via DI                                             
                    INFO     ✅ Prompt Manager injected via component_base.py:88
                             DI                                                 
                    INFO     ✅ FbAdsOrchestrator created    main_factory.py:278
                    INFO     Executing Facebook Ads tasks   component_base.py:88
                    INFO     DEBUG: config type: <class     component_base.py:88
                             'dict'>                                            
                    INFO     DEBUG: config repr: {'date':   component_base.py:88
                             '07/12/25', 'start_date':                          
                             None, 'end_date': '07/12/25',                      
                             'environment': 'development',                      
                             'log_level': 'DEBUG',                              
                             'dry_run': False, 'data_dir':                      
                             PosixPath('data'), 'name':                         
                             'fb_ads', 'enabled': True,                         
                             'parallel': False,                                 
                             'num_workers': 1,                                  
                             'timeout_seconds': 3600,                           
                             'retry_attempts': 3,                               
                             'iso_date': '20250712',                            
                             'DATA_DIR': 'data', 'log_dir':                     
                             'data/20250712/logs',                              
                             'scraper': False,                                  
                             'post_process': False,                             
                             'upload': False,                                   
                             'report_generator': False,                         
                             'fb_ads': True,                                    
                             'process_single_court': [],                        
                             'skip_courts': [],                                 
                             'html_only': False,                                
                             'bucket_name':                                     
                             'lexgenius-dockets',                               
                             'upload_json_to_dynamodb':                         
                             True, 'upload_pdfs_to_s3':                         
                             True, 'force_upload': False,                       
                             'force_upload_all': False,                         
                             'reprocess_files': False,                          
                             'start_from_incomplete':                           
                             False, 'skip_files': [],                           
                             'docket_list_for_orchestrator'                     
                             : None,                                            
                             'process_review_cases': False,                     
                             'reprocess_failed': False,                         
                             'multiple_courts': None,                           
                             'docket_num': None,                                
                             'get_new_docket_report':                           
                             False, 'start_after_court':                        
                             None, 'start_at_court': None,                      
                             'normalize_law_firm_names':                        
                             False, 'reprocess_mdl_num':                        
                             False, 'process_images': True,                     
                             'enable_ocr': True,                                
                             'enable_classification': True,                     
                             'batch_size': 100,                                 
                             'max_workers': 4,                                  
                             'image_timeout_seconds': 30,                       
                             'max_image_size_mb': 10,                           
                             'enable_phash': True,                              
                             'phash_threshold': 10,                             
                             'classification_mode':                             
                             'hybrid', 'embedding_model':                       
                             'sentence-transformers/all-mpn                     
                             et-base-v2',                                       
                             'classification_threshold':                        
                             0.7,                                               
                             'enable_deferred_processing':                      
                             True,                                              
                             'deferred_queue_max_size':                         
                             10000, 'deferred_batch_size':                      
                             50, 'enable_cache': True,                          
                             'cache_ttl_hours': 24,                             
                             'cache_max_size_mb': 500,                          
                             'rate_limit_enabled': True,                        
                             'requests_per_minute': 60,                         
                             'lexgenius_project_root':                          
                             '/Users/<USER>/PycharmPr                     
                             ojects/lexgenius',                                 
                             'lexgenius_data_root':                             
                             '/Users/<USER>/PycharmPr                     
                             ojects/lexgenius/',                                
                             'lexgenius_use_di_container':                      
                             'true', 'aws_region':                              
                             'us-west-2',                                       
                             'aws_access_key_id':                               
                             'AKIAYE5GCR4YIBQTZUH6',                            
                             'aws_secret_access_key':                           
                             'ggy9QxY9s/tUMDwkPgeJl412TIQcb                     
                             SCbfzBLkgOM',                                      
                             's3_bucket_name':                                  
                             'lexgenius-dockets',                               
                             'cloudfront_distribution_id':                      
                             'E1KB03VRP2XA6G',                                  
                             'use_local_dynamodb': False,                       
                             'local_dynamodb_endpoint_url':                     
                             'http://localhost:8000',                           
                             'email_smtp_host':                                 
                             'smtp.gmail.com',                                  
                             'email_smtp_port': '587',                          
                             'email_smtp_username':                             
                             '<EMAIL>',                            
                             'email_smtp_password':                             
                             'your-app-password',                               
                             'email_from_email':                                
                             '<EMAIL>',                           
                             'email_from_name': 'LexGenius                      
                             Reports',                                          
                             'use_direct_async_repos':                          
                             'true', 'enable_fb_ads_async':                     
                             'true',                                            
                             'enable_reports_async':                            
                             'false', 'enable_pacer_async':                     
                             'false',                                           
                             'enable_data_transformer_async                     
                             ': 'false',                                        
                             'fallback_to_compatibility':                       
                             'true',                                            
                             'auto_fallback_on_error':                          
                             'true',                                            
                             'fallback_timeout_seconds':                        
                             '30',                                              
                             'log_performance_metrics':                         
                             'true', 'compare_old_vs_new':                      
                             'false',                                           
                             'performance_threshold_ms':                        
                             '5000', 'pacer_username_prod':                     
                             'gratefuldave',                                    
                             'pacer_password_prod':                             
                             'Sr2ket592521p!',                                  
                             'enable_concurrent_processing'                     
                             : True,                                            
                             'max_concurrent_firms': 1,                         
                             'concurrent_batch_size': 1,                        
                             'session_pool_size': 1,                            
                             'default_date_range_days': 14,                     
                             'session_refresh_interval':                        
                             20, 'shuffle_firm_order':                          
                             True,                                              
                             'rotate_proxy_between_firms':                      
                             True,                                              
                             'debug_async_conversion':                          
                             'false',                                           
                             'force_compatibility_mode':                        
                             'false',                                           
                             'dry_run_async_migration':                         
                             'false',                                           
                             'async_rollout_percentage':                        
                             '100',                                             
                             'feature_use_async_storage':                       
                             'false',                                           
                             'feature_use_hybrid_classifier                     
                             ': 'true',                                         
                             'feature_enable_ocr_processing                     
                             ': 'true',                                         
                             'feature_enable_deferred_proce                     
                             ssing': 'true',                                    
                             'feature_enable_cache':                            
                             'true', 'openai_api_key':                          
                             '${OPENAI_API_KEY}',                               
                             'deepseek_api_key':                                
                             '${DEEPSEEK_API_KEY}',                             
                             'openrouter_api_key':                              
                             'sk-or-v1-411746d5eaf957ac931a                     
                             33eb44cd5db4e1c9b046a0158e7e9d                     
                             c1b95ec576840f',                                   
                             'openrouter_site_url':                             
                             'https://lexgenius.com',                           
                             'openrouter_site_name':                            
                             'LexGenius',                                       
                             'mistral_api_key':                                 
                             '${MISTRAL_API_KEY}',                              
                             'hugging_face_api_key':                            
                             'hf_FbCbFLwKxyCtczbwODnbgqymZx                     
                             RNtywfaL', 'gemini_api_key':                       
                             'AIzaSyBNE2TJcUWLTcLqYCyHsamM5                     
                             0Y1KRD3RqE',                                       
                             'google_generative_ai_api_key'                     
                             :                                                  
                             'AIzaSyBNE2TJcUWLTcLqYCyHsamM5                     
                             0Y1KRD3RqE', 'pacer_username':                     
                             'gratefuldave',                                    
                             'pacer_password':                                  
                             'Sr2ket592521p!',                                  
                             'oxy_labs_residential_username                     
                             ': 'lexgenius20250612_wzykM',                      
                             'oxy_labs_residential_password                     
                             ': '24gbfygq=RBhM2+',                              
                             'oxy_labs_mobile_username':                        
                             'lexgeniusmob20250612_7jRlZ',                      
                             'oxy_labs_mobile_password':                        
                             '24gbfygq=RBhM2+',                                 
                             'step_name': 'facebook_ads',                       
                             'input_source':                                    
                             'data/law_firms',                                  
                             'output_location':                                 
                             'data/fb_ads', 'llm_provider':                     
                             'deepseek', 'headless': True,                      
                             'testing': False,                                  
                             'defer_image_processing':                          
                             True, 'use_local': False,                          
                             'use_proxy': True,                                 
                             'mobile_proxy': False,                             
                             'render_html': False,                              
                             'airplane_mode': False,                            
                             'single_firm_date_range_days':                     
                             30,                                                
                             'ignore_list_date_range_days':                     
                             30,                                                
                             'skip_firms_updated_today':                        
                             True, 'max_ad_pages': 50,                          
                             'api_retries': 3,                                  
                             'api_backoff_base': 1.7,                           
                             'payload_retries': 3,                              
                             'oxylabs_num_proxies': 10000,                      
                             'proxy_ban_duration': 600,                         
                             'max_proxy_failures': 3,                           
                             'rotate_proxy_per_page':                           
                             False,                                             
                             'image_download_timeout': 30,                      
                             'temp_image_dir':                                  
                             './temp_images',                                   
                             's3_ad_archive_prefix':                            
                             'adarchive/fb',                                    
                             's3_cdn_base_url': '',                             
                             'image_queue_dir':                                 
                             './data/image_queue',                              
                             'image_queue': {'directory':                       
                             './data/image_queue',                              
                             'batch_size': 200,                                 
                             'cleanup_days': 30},                               
                             'disable_llava': False,                            
                             'disable_gpt': False,                              
                             'disable_deepseek': False,                         
                             'llava_model_name':                                
                             'llama3.2-vision:11b-instruct-                     
                             q4_K_M', 'llava_timeout': 600,                     
                             'llava_temperature': 0.3,                          
                             'llava_num_gpu_layers': -1,                        
                             'llava_use_semaphore': True,                       
                             'llava_semaphore_count': 6,                        
                             'use_tqdm': True,                                  
                             'run_parallel': True,                              
                             'processing_results_dir':                          
                             './processing_results',                            
                             'law_firm_data_dir':                               
                             './data/law_firms',                                
                             'dynamodb':                                        
                             {'fb_ad_archive_table_name':                       
                             'FBAdArchive',                                     
                             'fb_image_hash_table_name':                        
                             'FBImageHash',                                     
                             'law_firms_table_name':                            
                             'LawFirms'}, 'upload_types':                       
                             [],                                                
                             'disable_bandwidth_periodic_lo                     
                             gging': False, 'verbose':                          
                             False, 'max_retries': 3,                           
                             'fb_ad_categorizer':                               
                             {'batch_size': 100,                                
                             'scan_workers': 8,                                 
                             'update_workers': 8,                               
                             'prompts_dir':                                     
                             'src/config/fb_ad_categorizer_                     
                             prompts',                                          
                             'campaign_config_file':                            
                             'src/config/fb_ad_categorizer/                     
                             campaign_config.json',                             
                             'campaign_skip_terms_file':                        
                             'src/config/fb_ad_categorizer/                     
                             campaign_skip_terms.json',                         
                             'embedding_stop_terms_file':                       
                             'src/config/fb_ad_categorizer/                     
                             embedding_stop_terms.json',                        
                             'company_name_mapping_file':                       
                             'src/config/fb_ad_categorizer/                     
                             company_name_mapping.json'},                       
                             'vector_clusterer':                                
                             {'rules_only': True,                               
                             'embed_fields': ['Title',                          
                             'Body'], 'embedding_model':                        
                             'text-embedding-ada-002'},                         
                             'ignore_firms_file':                               
                             'src/config/fb_ads/ignore_firm                     
                             s.json',                                           
                             'login_required_file':                             
                             'src/config/fb_ads/login_requi                     
                             red.json',                                         
                             'deferred_processing_file':                        
                             'src/config/fb_ads/deferred_pr                     
                             ocessing.json',                                    
                             'feature_flags':                                   
                             {'enable_phash_deduplication':                     
                             True,                                              
                             'enable_rule_based_classificat                     
                             ion': True,                                        
                             'enable_ai_enhancement': True,                     
                             'enable_bandwidth_logging':                        
                             True,                                              
                             'enable_proxy_health_monitorin                     
                             g': True,                                          
                             'enable_session_persistence':                      
                             True}, 'performance':                              
                             {'max_concurrent_firms': 8,                        
                             'max_concurrent_images': 10,                       
                             'request_delay_min': 0.5,                          
                             'request_delay_max': 2.0,                          
                             'memory_limit_mb': 8192},                          
                             'monitoring': {'log_level':                        
                             'INFO', 'enable_metrics':                          
                             True, 'metrics_interval': 300,                     
                             'enable_health_checks': True},                     
                             'development': {'dry_run':                         
                             False, 'sample_firms': [],                         
                             'enable_debug_output': True},                      
                             'cleanup':                                         
                             {'auto_cleanup_temp_files':                        
                             True,                                              
                             'cleanup_interval_hours': 24,                      
                             'max_log_file_size_mb': 100,                       
                             'log_retention_days': 30},                         
                             'reset_chrome': False,                             
                             'reprocess_md': False,                             
                             'force_openrouter_paid':                           
                             False, 'skip_ads': False,                          
                             'skip_invalidate': False,                          
                             'weekly': False,                                   
                             'process_review_cases_legacy':                     
                             False,                                             
                             'fb_ad_archive_table_name':                        
                             'FBAdArchive',                                     
                             'fb_image_hash_table_name':                        
                             'FBImageHash',                                     
                             'law_firms_table_name':                            
                             'LawFirms',                                        
                             'local_dynamodb_port': 8000,                       
                             'dynamodb_endpoint': None,                         
                             '_injected_ai_orchestrator':                       
                             <src.services.ai.ai_orchestrat                     
                             or.AIOrchestrator object at                        
                             0x31f9a73d0>,                                      
                             '_injected_deepseek_service':                      
                             <src.services.ai.deepseek_serv                     
                             ice.DeepSeekService object at                      
                             0x31f9a6550>,                                      
                             '_injected_prompt_manager':                        
                             <src.services.ai.prompt_manage                     
                             r.PromptManager object at                          
                             0x31c1e7690>}                                      
                    INFO     Not a TTY, disabling Rich      component_base.py:88
                             progress bar.                                      
                    INFO     Facebook Ads Orchestrator      component_base.py:88
                             initialized with iso_date:                         
                             20250712                                           
                    INFO     FacebookAdsOrchestrator        component_base.py:88
                             initialized successfully with                      
                             dependency injection.                              
                    INFO     Starting Facebook Ads          component_base.py:88
                             processing for 20250712                            
                    ERROR    Error in Facebook Ads         component_base.py:102
                             processing: 'NoneType' object                      
                             has no attribute                                   
                             'run_full_scrape_workflow'                         
                    INFO     📌 Tracked aiohttp session  resource_tracker.py:127
                             13273855312 created at                             
                             /Users/<USER>/Pycharm                        
                             Projects/lexgenius/src/serv                        
                             ices/orchestration/fb_ads_o                        
                             rchestrator.py:115 in                              
                             execute                                            
                    INFO     ✅ Untracked aiohttp        resource_tracker.py:141
                             session 13273855312 (was                           
                             created at                                         
                             /Users/<USER>/Pycharm                        
                             Projects/lexgenius/src/serv                        
                             ices/orchestration/fb_ads_o                        
                             rchestrator.py:115)                                
                    INFO     Facebook ads processing        component_base.py:88
                             completed                                          
                    INFO     Facebook Ads tasks finished    component_base.py:88
                    INFO     Facebook Ads processing phase  component_base.py:88
                             completed                                          
                    INFO     Report generation phase        component_base.py:88
                             skipped by configuration                           
                    INFO     MainOrchestrator run completed component_base.py:88
                             successfully                                       
                    INFO     Exiting MainServiceFactory      main_factory.py:126
                             async context.                                     
                    INFO     DI Container cleaned up         main_factory.py:135
                             successfully.                                      
                    INFO     MainOrchestrator run completed for     main.py:2095
                             single date.                                       
                    INFO     Main function execution finished.      main.py:2137
                    INFO     === Resource Tracker Report ===        main.py:2163
                    INFO     ✅ No unclosed resources    resource_tracker.py:253
                             detected                                           
                    INFO     🧹 Starting comprehensive   resource_tracker.py:338
                             resource cleanup...                                
                    INFO     ✅ No unclosed resources    resource_tracker.py:253
                             detected                                           
                    INFO     🔍 Searching for orphaned   resource_tracker.py:290
                             aiohttp sessions...                                
                    INFO     ✅ No orphaned aiohttp      resource_tracker.py:317
                             sessions found                                     
                    INFO     ✅ Resource cleanup         resource_tracker.py:351
                             complete                                           
                    DEBUG    Found LLM client for cleanup: Client   main.py:2228
                             ID: 13352338128, Type: DeepSeekClient              
                    WARNING  Found 1 unclosed LLM clients -         main.py:2273
                             attempting to close them                           
                    DEBUG    Closed DeepSeekClient session          main.py:2279
                    INFO     Async resources cleanup completed      main.py:2289
                    INFO     Entering final cleanup phase...        main.py:2346
                    INFO     Gathering all remaining tasks after    main.py:2365
                             main task completion/cancellation...               
                    INFO     No other pending tasks found after     main.py:2438
                             main task.                                         
                    INFO     Explicitly running garbage collection  main.py:2463
                             (first pass)...                                    
[07/12/25 15:43:03] INFO     Explicitly running garbage collection  main.py:2474
                             (second pass)...                                   
                    INFO     Garbage collection finished.           main.py:2478
                    INFO     Closing event loop...                  main.py:2491
                    INFO     Event loop closed.                     main.py:2493
                    INFO     Current thread's event loop policy     main.py:2498
                             reset.                                             
                    INFO     Running synchronous                    main.py:2500
                             cleanup_everything...                              
                    INFO     Starting comprehensive      resource_cleanup.py:282
                             cleanup...                                         
                    DEBUG    Starting comprehensive      resource_cleanup.py:136
                             resource cleanup...                                
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-hpL2GK                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-EIZVRm                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-BFtoVN                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-6yscW5                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-5q1nLI                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-ZEJEbU                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-w9oPGA                              
                    DEBUG    Resource cleanup completed  resource_cleanup.py:145
                    INFO     Comprehensive cleanup       resource_cleanup.py:297
                             completed                                          
                    INFO     cleanup_everything completed.          main.py:2504
                    INFO     safe_run_main finished with exit_code: main.py:2515
                             0                                                  
Main script exit.
                    DEBUG    Starting comprehensive      resource_cleanup.py:136
                             resource cleanup...                                
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-hpL2GK                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-EIZVRm                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-BFtoVN                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-6yscW5                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-5q1nLI                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-ZEJEbU                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-w9oPGA                              
                    DEBUG    Resource cleanup completed  resource_cleanup.py:145
                    DEBUG    Starting comprehensive      resource_cleanup.py:136
                             resource cleanup...                                
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-hpL2GK                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-EIZVRm                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-BFtoVN                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-6yscW5                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-5q1nLI                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-ZEJEbU                              
                    DEBUG    Skipping temp item (mount   resource_cleanup.py:201
                             point or special):                                 
                             /tmp/tmp-mount-w9oPGA                              
                    DEBUG    Resource cleanup completed  resource_cleanup.py:145
All pipeline steps completed.

```
