[{"file_path": "src/services/transformer/docket_file_manager.py", "class_name": "DocketFileManager", "constructors": [{"name": "__init__", "parameters": [{"name": "download_dir", "type": "str", "default": null}, {"name": "file_handler", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["download_dir", "file_handler"], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/docket_validator.py", "class_name": "DocketValidator", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/error_handler.py", "class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructors": [{"name": "__init__", "parameters": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["file_handler"], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "source": "__init__"}, {"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/data_transformer.py", "class_name": "TransformationJob", "constructors": [{"name": "__init__ (implicit or inherited)", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_patterns": ["No explicit __init__ found for class TransformationJob. May use default or inherit."]}], "dependencies": [], "errors": []}, {"file_path": "src/services/transformer/docket_data_cleaner.py", "class_name": "DocketDataCleaner", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/docket_processor.py", "class_name": "DocketProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "llm_client", "type": "Any", "default": "None"}, {"name": "law_firm_processor", "type": "Any", "default": "None"}, {"name": "transfer_handler", "type": "Any", "default": "None"}, {"name": "html_data_updater", "type": "Any", "default": "None"}, {"name": "validator", "type": "Any", "default": "None"}, {"name": "file_handler", "type": "Any", "default": "None"}, {"name": "pdf_processor", "type": "Optional[Any]", "default": "None"}, {"name": "gpt_client", "type": "Optional[Any]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "**kwargs", "type": "Any", "default": null}], "required_parameters": ["config"], "optional_parameters": ["llm_client", "law_firm_processor", "transfer_handler", "html_data_updater", "validator", "file_handler", "pdf_processor", "gpt_client", "logger"], "special_patterns": ["**kwargs present"]}], "dependencies": [{"name": "config", "type": "Dict", "source": "__init__"}, {"name": "gpt_client", "type": "Optional[Any]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/mdl_processor.py", "class_name": "MDLProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "mdl_litigations", "type": "pd.DataFrame", "default": null}, {"name": "mdl_path", "type": "str", "default": null}, {"name": "file_handler", "type": "Any", "default": null}, {"name": "gpt", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "litigation_classifier", "type": "Any", "default": "None"}, {"name": "pdf_cache", "type": "Any", "default": "None"}, {"name": "download_dir", "type": "Optional[str]", "default": "None"}, {"name": "district_court_db", "type": "Any", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["mdl_litigations", "mdl_path", "file_handler", "gpt"], "optional_parameters": ["config", "litigation_classifier", "pdf_cache", "download_dir", "district_court_db", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/uploader.py", "class_name": "Uploader", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "s3_manager", "type": "S3AsyncStorage", "default": null}, {"name": "pacer_db", "type": "PacerRepository", "default": null}, {"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config", "s3_manager", "pacer_db", "file_handler"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict", "source": "__init__"}, {"name": "s3_manager", "type": "S3AsyncStorage", "source": "__init__"}, {"name": "pacer_db", "type": "PacerRepository", "source": "__init__"}, {"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/mdl_description_manager.py", "class_name": "MDLDescriptionManager", "constructors": [{"name": "__init__", "parameters": [{"name": "file_handler", "type": "Any", "default": null}, {"name": "gpt_client", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "download_dir", "type": "Optional[str]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["file_handler", "gpt_client"], "optional_parameters": ["config", "download_dir", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/mdl_processor_original.py", "class_name": "MDLProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "mdl_litigations", "type": "pd.DataFrame", "default": null}, {"name": "mdl_path", "type": "str", "default": null}, {"name": "file_handler", "type": "Any", "default": null}, {"name": "gpt", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "litigation_classifier", "type": "Any", "default": "None"}, {"name": "pdf_cache", "type": "Any", "default": "None"}, {"name": "download_dir", "type": "Optional[str]", "default": "None"}, {"name": "district_court_db", "type": "Any", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["mdl_litigations", "mdl_path", "file_handler", "gpt"], "optional_parameters": ["config", "litigation_classifier", "pdf_cache", "download_dir", "district_court_db", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/specialized_workflows.py", "class_name": "SpecializedWorkflows", "constructors": [{"name": "__init__", "parameters": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "default": null}, {"name": "docket_processor", "type": "Any", "default": null}, {"name": "mdl_processor", "type": "Any", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "config", "type": "Optional[Dict]", "default": "None"}], "required_parameters": ["file_handler", "docket_processor", "mdl_processor"], "optional_parameters": ["logger", "config"], "special_patterns": []}], "dependencies": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}, {"name": "config", "type": "Optional[Dict]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/file_operations.py", "class_name": "FileOperationsManager", "constructors": [{"name": "__init__", "parameters": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["file_handler"], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "source": "__init__"}, {"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/docket_text_handler.py", "class_name": "DocketTextHandler", "constructors": [{"name": "__init__", "parameters": [{"name": "file_handler", "type": "Any", "default": null}, {"name": "pdf_processor", "type": "Optional[Any]", "default": "None"}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["file_handler"], "optional_parameters": ["pdf_processor", "config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/litigation_classifier.py", "class_name": "LitigationRule", "constructors": [{"name": "__init__ (implicit or inherited)", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_patterns": ["No explicit __init__ found for class LitigationRule. May use default or inherit."]}], "dependencies": [], "errors": []}, {"file_path": "src/services/transformer/data_processing_engine.py", "class_name": "DataProcessingEngine", "constructors": [{"name": "__init__", "parameters": [{"name": "docket_processor", "type": "DocketProcessor", "default": null}, {"name": "mdl_processor", "type": "MDLProcessor", "default": null}, {"name": "llm_client", "type": "Any", "default": null}, {"name": "logger", "type": "Any", "default": null}, {"name": "config", "type": "Any", "default": null}, {"name": "html_integration_service", "type": "Any", "default": "None"}], "required_parameters": ["docket_processor", "mdl_processor", "llm_client", "logger", "config"], "optional_parameters": ["html_integration_service"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Any", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/law_firm_integration.py", "class_name": "LawFirmIntegration", "constructors": [{"name": "__init__", "parameters": [{"name": "law_firm_processor", "type": "Optional[Any]", "default": "None"}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["law_firm_processor", "config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/law_firm_processor.py", "class_name": "LawFirmProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "s3_storage", "type": "Optional[Any]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger", "s3_storage"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}, {"name": "s3_storage", "type": "Optional[Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/mdl_persistence_manager.py", "class_name": "MDLPersistenceManager", "constructors": [{"name": "__init__", "parameters": [{"name": "mdl_path", "type": "str", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["mdl_path"], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/docket_llm_engine.py", "class_name": "DocketLLMEngine", "constructors": [{"name": "__init__", "parameters": [{"name": "llm_client", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["llm_client"], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/mdl_lookup_manager.py", "class_name": "MDLLookupManager", "constructors": [{"name": "__init__", "parameters": [{"name": "district_court_db", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["district_court_db"], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/config.py", "class_name": "ScraperConfig", "constructors": [{"name": "__init__ (implicit or inherited)", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_patterns": ["No explicit __init__ found for class ScraperConfig. May use default or inherit."]}], "dependencies": [], "errors": []}, {"file_path": "src/services/transformer/transfer_handler.py", "class_name": "TransferHandler", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "pacer_db", "type": "PacerRepository", "default": null}, {"name": "district_court_db", "type": "DistrictCourtsRepository", "default": null}, {"name": "mdl_processor", "type": "MDLProcessor", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config", "pacer_db", "district_court_db", "mdl_processor"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict", "source": "__init__"}, {"name": "pacer_db", "type": "PacerRepository", "source": "__init__"}, {"name": "district_court_db", "type": "DistrictCourtsRepository", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/file_handler.py", "class_name": "<PERSON><PERSON><PERSON><PERSON>", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "s3_manager", "type": "Optional[Any]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["s3_manager", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/html_integration_service.py", "class_name": "TransformerHTMLIntegrationService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/afff_calculator.py", "class_name": "AfffCalculator", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/mdl_data_processor.py", "class_name": "MDLDataProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/component_factory.py", "class_name": "ComponentFactory", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "openai_client", "type": "Optional[GPTClient]", "default": "None"}, {"name": "deepseek_service", "type": "Optional[DeepSeekService]", "default": "None"}, {"name": "mistral_service", "type": "Optional[MistralService]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["logger", "openai_client", "deepseek_service", "mistral_service"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}, {"name": "openai_client", "type": "Optional[GPTClient]", "source": "__init__"}, {"name": "deepseek_service", "type": "Optional[DeepSeekService]", "source": "__init__"}, {"name": "mistral_service", "type": "Optional[MistralService]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/docket_html_processor.py", "class_name": "DocketHTMLProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "html_data_updater", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "html_processing_service", "type": "Optional[Any]", "default": "None"}], "required_parameters": ["html_data_updater"], "optional_parameters": ["config", "logger", "html_processing_service"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}, {"name": "html_processing_service", "type": "Optional[Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/court_data_processor.py", "class_name": "CourtDataProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/transformer/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/transformer/__init__.py"]}, {"file_path": "src/services/transformer/cached_pdf_data.py", "class_name": "CachedPdfData", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "litigation_classifier", "type": "Optional[LitigationClassifier]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger", "litigation_classifier"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/orchestration/main_orchestrator.py", "class_name": "MainOrchestrator", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "factory", "type": "MainServiceFactory", "default": null}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config", "factory"], "optional_parameters": ["shutdown_event"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "WorkflowConfig", "source": "__init__"}, {"name": "factory", "type": "MainServiceFactory", "source": "__init__"}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/orchestration/processing_orchestrator.py", "class_name": "ProcessingOrchestrator", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["shutdown_event"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "WorkflowConfig", "source": "__init__"}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/orchestration/scraping_orchestrator.py", "class_name": "ScrapingOrchestrator", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "pacer_service", "type": "PacerOrchestratorService", "default": null}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config", "pacer_service"], "optional_parameters": ["shutdown_event"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "WorkflowConfig", "source": "__init__"}, {"name": "pacer_service", "type": "PacerOrchestratorService", "source": "__init__"}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/orchestration/fb_ads_orchestrator_cli.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/orchestration/fb_ads_orchestrator_cli.py"]}, {"file_path": "src/services/orchestration/upload_orchestrator.py", "class_name": "UploadOrchestrator", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "s3_storage_client", "type": "Optional[S3AsyncStorage]", "default": "None"}, {"name": "dynamo_storage_client", "type": "Optional[AsyncDynamoDBStorage]", "default": "None"}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["s3_storage_client", "dynamo_storage_client", "shutdown_event"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "WorkflowConfig", "source": "__init__"}, {"name": "s3_storage_client", "type": "Optional[S3AsyncStorage]", "source": "__init__"}, {"name": "dynamo_storage_client", "type": "Optional[AsyncDynamoDBStorage]", "source": "__init__"}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/orchestration/fb_ads_orchestrator.py", "class_name": "FbAdsOrchestrator", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["shutdown_event"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "WorkflowConfig", "source": "__init__"}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/orchestration/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/orchestration/__init__.py"]}, {"file_path": "src/services/infrastructure/resource_cleanup_service.py", "class_name": "ResourceCleanupService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "Any", "default": "None"}, {"name": "config", "type": "Any", "default": "None"}], "required_parameters": [], "optional_parameters": ["logger", "config"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Any", "source": "__init__"}], "errors": []}, {"file_path": "src/services/infrastructure/performance_monitor_service.py", "class_name": "PerformanceMonitorService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "Any", "default": "None"}, {"name": "config", "type": "Any", "default": "None"}], "required_parameters": [], "optional_parameters": ["logger", "config"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Any", "source": "__init__"}], "errors": []}, {"file_path": "src/services/uploader/upload_service.py", "class_name": "UploadService", "constructors": [{"name": "__init__", "parameters": [{"name": "bucket_name", "type": "str", "default": null}, {"name": "s3_client", "type": "Any", "default": "None"}, {"name": "dynamodb_client", "type": "Any", "default": "None"}, {"name": "region_name", "type": "str", "default": "'us-east-1'"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["bucket_name"], "optional_parameters": ["s3_client", "dynamodb_client", "region_name", "logger", "config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/uploader/s3_upload_service.py", "class_name": "S3UploadService", "constructors": [{"name": "__init__", "parameters": [{"name": "bucket_name", "type": "str", "default": null}, {"name": "cloudfront_distribution_id", "type": "Optional[str]", "default": "None"}, {"name": "s3_client", "type": "Any", "default": "None"}, {"name": "region_name", "type": "str", "default": "'us-east-1'"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["bucket_name"], "optional_parameters": ["cloudfront_distribution_id", "s3_client", "region_name", "logger", "config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/uploader/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/uploader/__init__.py"]}, {"file_path": "src/services/district_courts/query_service.py", "class_name": "DistrictCourtsQueryService", "constructors": [{"name": "__init__", "parameters": [{"name": "repository", "type": "DistrictCourtsRepository", "default": null}], "required_parameters": ["repository"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "repository", "type": "DistrictCourtsRepository", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/ad_df_processor_service.py", "class_name": "AdDataFrameProcessorService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/processing_service.py", "class_name": "ReportsProcessingService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/config_service.py", "class_name": "ReportsConfigService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/rendering_service.py", "class_name": "ReportsRenderingService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/publishing_service.py", "class_name": "ReportsPublishingService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/data_loader_service.py", "class_name": "ReportsDataLoaderService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/reports_orchestrator_service.py", "class_name": "ReportsOrchestratorService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/ad_page_generator_service.py", "class_name": "AdPageGeneratorService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/reports/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/reports/__init__.py"]}, {"file_path": "src/services/monitoring/performance_monitoring_service.py", "class_name": "PerformanceMonitoringService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[MonitoringConfig]", "default": "None"}, {"name": "logger", "type": "Any", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[MonitoringConfig]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_archive/query_service.py", "class_name": "FBArchiveQueryService", "constructors": [{"name": "__init__", "parameters": [{"name": "repository", "type": "FBArchiveRepository", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["logger", "config"], "special_patterns": []}], "dependencies": [{"name": "repository", "type": "FBArchiveRepository", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_archive/delete_service.py", "class_name": "FBArchiveDeleteService", "constructors": [{"name": "__init__", "parameters": [{"name": "repository", "type": "FBArchiveRepository", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["logger", "config"], "special_patterns": []}], "dependencies": [{"name": "repository", "type": "FBArchiveRepository", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_archive/update_service.py", "class_name": "FBArchiveUpdateService", "constructors": [{"name": "__init__", "parameters": [{"name": "repository", "type": "FBArchiveRepository", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}], "required_parameters": ["repository", "config"], "optional_parameters": ["logger"], "special_patterns": []}], "dependencies": [{"name": "repository", "type": "FBArchiveRepository", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_archive/data_conversion_service.py", "class_name": "FBArchiveDataConversionService", "constructors": [{"name": "__init__", "parameters": [{"name": "repository", "type": "FBArchiveRepository", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["logger", "config"], "special_patterns": []}], "dependencies": [{"name": "repository", "type": "FBArchiveRepository", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/ai/deepseek_service.py", "class_name": "DeepSeekService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/ai/ai_orchestrator.py", "class_name": "AIOrchestrator", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/ai/prompt_manager.py", "class_name": "PromptManager", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/ai/mistral_service.py", "class_name": "MistralService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/ai/batch_processor.py", "class_name": "BatchProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/ai/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/ai/__init__.py"]}, {"file_path": "src/services/fb_ads/logging_setup.py", "class_name": "FBAdsLogger", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/ad_db_service.py", "class_name": "AdDBService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/ad_processing_service.py", "class_name": "AdProcessingService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/bandwidth_logger.py", "class_name": "BandwidthLogger", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "logger", "type": "logging.Logger", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/ner_rule_analyzer.py", "class_name": "NerRuleAnalyzer", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "session", "type": "aiohttp.ClientSession", "default": null}, {"name": "ner_model_name", "type": "str", "default": "'en_core_web_trf'"}, {"name": "spacy_pipe_batch_size", "type": "int", "default": "64"}, {"name": "use_local_dynamodb", "type": "bool", "default": "False"}, {"name": "dynamodb_scan_workers", "type": "Optional[int]", "default": "None"}, {"name": "ner_processing_workers", "type": "Optional[int]", "default": "None"}], "required_parameters": ["config", "session"], "optional_parameters": ["ner_model_name", "spacy_pipe_batch_size", "use_local_dynamodb", "dynamodb_scan_workers", "ner_processing_workers"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "session", "type": "aiohttp.ClientSession", "source": "__init__"}, {"name": "dynamodb_scan_workers", "type": "Optional[int]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/workflow_service.py", "class_name": "WorkflowService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/processing_tracker.py", "class_name": "ProcessingTracker", "constructors": [{"name": "__init__", "parameters": [{"name": "file_path", "type": "str", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "logger", "type": "logging.Logger", "default": "None"}], "required_parameters": ["file_path"], "optional_parameters": ["config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/local_image_queue.py", "class_name": "LocalImageQueue", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}, {"name": "data_dir", "type": "str", "default": "'./data/image_queue'"}], "required_parameters": [], "optional_parameters": ["logger", "config", "data_dir"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/disk_cache.py", "class_name": "<PERSON><PERSON><PERSON><PERSON>", "constructors": [{"name": "__init__", "parameters": [{"name": "cache_file", "type": "Union[str, Path]", "default": null}, {"name": "max_memory_mb", "type": "int", "default": "1000"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "logger", "type": "logging.Logger", "default": "None"}], "required_parameters": ["cache_file"], "optional_parameters": ["max_memory_mb", "config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/interactive_service.py", "class_name": "InteractiveService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/session_manager.py", "class_name": "SSLAdapter", "constructors": [{"name": "__init__", "parameters": [{"name": "*args", "type": "Any", "default": null}, {"name": "ciphers", "type": "str", "default": "CIPHERS_SET_1"}, {"name": "**kwargs", "type": "Any", "default": null}], "required_parameters": [], "optional_parameters": ["ciphers"], "special_patterns": ["*args present", "**kwargs present"]}], "dependencies": [], "errors": []}, {"file_path": "src/services/fb_ads/ad_ner_processor.py", "class_name": "AdNerProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "session", "type": "aiohttp.ClientSession", "default": null}, {"name": "text_fields", "type": "List[str]", "default": "['Title', 'Body', 'Summary', 'ImageText']"}, {"name": "ner_model_name", "type": "str", "default": "'en_core_web_lg'"}, {"name": "spacy_pipe_batch_size", "type": "int", "default": "64"}, {"name": "use_local_dynamodb", "type": "bool", "default": "False"}, {"name": "dynamodb_scan_workers", "type": "Optional[int]", "default": "None"}, {"name": "ner_processing_workers", "type": "Optional[int]", "default": "None"}, {"name": "cluster_min_k", "type": "int", "default": "5"}, {"name": "cluster_max_k", "type": "int", "default": "50"}, {"name": "cluster_k_step", "type": "int", "default": "5"}, {"name": "cluster_output_enabled", "type": "bool", "default": "True"}], "required_parameters": ["config", "session"], "optional_parameters": ["text_fields", "ner_model_name", "spacy_pipe_batch_size", "use_local_dynamodb", "dynamodb_scan_workers", "ner_processing_workers", "cluster_min_k", "cluster_max_k", "cluster_k_step", "cluster_output_enabled"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "session", "type": "aiohttp.ClientSession", "source": "__init__"}, {"name": "dynamodb_scan_workers", "type": "Optional[int]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/data_validation_service.py", "class_name": "DataValidationService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "Any", "default": null}, {"name": "processing_tracker", "type": "Any", "default": null}], "required_parameters": ["config", "logger", "processing_tracker"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/failed_firms_manager.py", "class_name": "FailedFirmsManager", "constructors": [{"name": "__init__", "parameters": [{"name": "data_dir", "type": "str", "default": null}, {"name": "logger", "type": "LoggerProtocol", "default": null}], "required_parameters": ["data_dir", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/classifier.py", "class_name": "DecimalEncoder", "constructors": [{"name": "__init__ (implicit or inherited)", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_patterns": ["No explicit __init__ found for class DecimalEncoder. May use default or inherit."]}], "dependencies": [], "errors": []}, {"file_path": "src/services/fb_ads/orchestrator.py", "class_name": "FacebookAdsOrchestrator", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "session", "type": "aiohttp.ClientSession", "default": null}], "required_parameters": ["config", "session"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "session", "type": "aiohttp.ClientSession", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/processor.py", "class_name": "AdProcessor", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "image_handler", "type": "ImageHandler", "default": null}, {"name": "ai_integrator", "type": "AIOrchestrator", "default": null}, {"name": "current_process_date", "type": "str", "default": null}, {"name": "fb_ad_db", "type": "Any", "default": "None"}, {"name": "s3_manager", "type": "Any", "default": "None"}, {"name": "logger", "type": "logging.Logger", "default": "None"}], "required_parameters": ["config", "image_handler", "ai_integrator", "current_process_date"], "optional_parameters": ["fb_ad_db", "s3_manager", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "image_handler", "type": "ImageHandler", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/image_handler.py", "class_name": "ImageHandler", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "s3_manager", "type": "S3AsyncStorage", "default": null}, {"name": "session_manager", "type": "FacebookSessionManager", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "s3_manager", "session_manager", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "s3_manager", "type": "S3AsyncStorage", "source": "__init__"}, {"name": "session_manager", "type": "FacebookSessionManager", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/error_handling_service.py", "class_name": "ErrorHandlingService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}, {"name": "processing_tracker", "type": "Any", "default": null}], "required_parameters": ["config", "logger", "processing_tracker"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/concurrent_workflow_service.py", "class_name": "ConcurrentWorkflowService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/api_client.py", "class_name": "FacebookAPIClient", "constructors": [{"name": "__init__", "parameters": [{"name": "session_manager", "type": "FacebookSessionManager", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["session_manager", "config", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "session_manager", "type": "FacebookSessionManager", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/categorizer.py", "class_name": "DeepSeekServiceUnavailableError", "constructors": [{"name": "__init__ (implicit or inherited)", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_patterns": ["No explicit __init__ found for class DeepSeekServiceUnavailableError. May use default or inherit."]}], "dependencies": [], "errors": []}, {"file_path": "src/services/fb_ads/image_utils.py", "class_name": "FBImageHashService", "constructors": [{"name": "__init__", "parameters": [{"name": "repository", "type": "FBImageHashRepository", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "repository", "type": "FBImageHashRepository", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/fb_ads/__init__.py"]}, {"file_path": "src/services/html/html_service_factory.py", "class_name": "HtmlServiceFactory", "constructors": [{"name": "__init__ (implicit or inherited)", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_patterns": ["No explicit __init__ found for class HtmlServiceFactory. May use default or inherit."]}], "dependencies": [], "errors": []}, {"file_path": "src/services/html/case_parser_service.py", "class_name": "CaseParserService", "constructors": [{"name": "__init__", "parameters": [{"name": "html_content", "type": "Optional[str]", "default": "None"}], "required_parameters": [], "optional_parameters": ["html_content"], "special_patterns": []}], "dependencies": [], "errors": []}, {"file_path": "src/services/html/data_updater_service.py", "class_name": "DataUpdaterService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "s3_manager", "type": "S3AsyncStorage", "default": null}, {"name": "pacer_db", "type": "PacerRepository", "default": null}], "required_parameters": ["config", "s3_manager", "pacer_db"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict", "source": "__init__"}, {"name": "s3_manager", "type": "S3AsyncStorage", "source": "__init__"}, {"name": "pacer_db", "type": "PacerRepository", "source": "__init__"}], "errors": []}, {"file_path": "src/services/html/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/html/__init__.py"]}, {"file_path": "src/services/law_firms/query_service.py", "class_name": "LawFirmsQueryService", "constructors": [{"name": "__init__", "parameters": [{"name": "repository", "type": "LawFirmsRepository", "default": null}], "required_parameters": ["repository"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "repository", "type": "LawFirmsRepository", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer_dockets/query_service.py", "class_name": "PacerDocketsQueryService", "constructors": [{"name": "__init__", "parameters": [{"name": "repository", "type": "PacerDocketsRepository", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["logger", "config"], "special_patterns": []}], "dependencies": [{"name": "repository", "type": "PacerDocketsRepository", "source": "__init__"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/document/pdf_processor_service.py", "class_name": "PdfProcessorService", "constructors": [{"name": "__init__", "parameters": [{"name": "hugging_face_api_key", "type": "Optional[str]", "default": "None"}], "required_parameters": [], "optional_parameters": ["hugging_face_api_key"], "special_patterns": []}], "dependencies": [], "errors": []}, {"file_path": "src/services/document/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/document/__init__.py"]}, {"file_path": "src/services/pacer/download_orchestration_service.py", "class_name": "PacerDownloadOrchestrationService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "file_operations_service", "type": "Optional[PacerFileOperationsService]", "default": "None"}, {"name": "navigator", "type": "Optional[Any]", "default": "None"}, {"name": "navigation_service", "type": "Optional[Any]", "default": "None"}, {"name": "s3_manager", "type": "Optional[S3AsyncStorage]", "default": "None"}, {"name": "stability_config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "iso_date", "type": "Optional[str]", "default": "None"}, {"name": "file_management_service", "type": "Optional[Any]", "default": "None"}, {"name": "relevance_service", "type": "Optional[Any]", "default": "None"}, {"name": "pacer_repository", "type": "Optional[Any]", "default": "None"}, {"name": "deepseek_service", "type": "Optional[DeepSeekService]", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["file_operations_service", "navigator", "navigation_service", "s3_manager", "stability_config", "iso_date", "file_management_service", "relevance_service", "pacer_repository", "deepseek_service"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "file_operations_service", "type": "Optional[PacerFileOperationsService]", "source": "__init__"}, {"name": "navigation_service", "type": "Optional[Any]", "source": "__init__"}, {"name": "s3_manager", "type": "Optional[S3AsyncStorage]", "source": "__init__"}, {"name": "stability_config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}, {"name": "file_management_service", "type": "Optional[Any]", "source": "__init__"}, {"name": "relevance_service", "type": "Optional[Any]", "source": "__init__"}, {"name": "pacer_repository", "type": "Optional[Any]", "source": "__init__"}, {"name": "deepseek_service", "type": "Optional[DeepSeekService]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/case_processing_service.py", "class_name": "PacerCaseProcessingService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "classification_service", "type": "Any", "default": "None"}, {"name": "html_processing_service", "type": "Any", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["classification_service", "html_processing_service"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/analytics_service.py", "class_name": "PacerAnalyticsService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "repository", "type": "PacerRepository", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": ["logger", "repository"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "repository", "type": "PacerRepository", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/export_service.py", "class_name": "PacerExportService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/query_service.py", "class_name": "PacerQueryService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "repository", "type": "PacerRepository", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": ["logger", "repository"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "repository", "type": "PacerRepository", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/navigation_service.py", "class_name": "PacerNavigationService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "html_processing_service", "type": "Any", "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["html_processing_service"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/authentication_service.py", "class_name": "PacerAuthenticationService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}], "required_parameters": ["logger", "config"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/file_operations_service.py", "class_name": "PacerFileOperationsService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["s3_storage"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/court_processing_service.py", "class_name": "PacerCourtProcessingService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "deepseek_service", "type": "Any", "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["deepseek_service"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/interactive_service.py", "class_name": "PacerInteractiveService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "query_service", "type": "PacerQueryService", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": ["logger", "query_service"], "optional_parameters": ["config"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "query_service", "type": "PacerQueryService", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/case_verification_service.py", "class_name": "PacerCaseVerificationService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "default": "None"}, {"name": "file_manager", "type": "Optional[PacerFileManagementService]", "default": "None"}, {"name": "end_date_obj", "type": "Optional[datetime]", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["pacer_repository", "file_manager", "end_date_obj"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "source": "__init__"}, {"name": "file_manager", "type": "Optional[PacerFileManagementService]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/html_processing_service.py", "class_name": "PacerHTMLProcessingService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "Optional[str]", "default": "None"}, {"name": "html_data_updater", "type": "Optional[DataUpdaterService]", "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["court_id", "html_data_updater"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "html_data_updater", "type": "Optional[DataUpdaterService]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/configuration_service.py", "class_name": "PacerConfigurationService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "config_dir", "type": "Path", "default": "DEFAULT_CONFIG_DIR"}], "required_parameters": ["logger", "config"], "optional_parameters": ["config_dir"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "config_dir", "type": "Path", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/report_service.py", "class_name": "ReportService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "navigator", "type": "PacerNavigator", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "from_date_str", "type": "str", "default": null}, {"name": "to_date_str", "type": "str", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "iso_date", "type": "str", "default": "None"}, {"name": "ignore_download_service", "type": "Optional[Any]", "default": "None"}], "required_parameters": ["logger", "navigator", "court_id", "from_date_str", "to_date_str", "config"], "optional_parameters": ["iso_date", "ignore_download_service"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "ignore_download_service", "type": "Optional[Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/ignore_download_service.py", "class_name": "PacerIgnoreDownloadService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}], "required_parameters": ["logger", "config"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/pacer_orchestrator_service.py", "class_name": "PacerOrchestratorService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "service_factory", "type": "Optional[PacerServiceFactory]", "default": "None"}, {"name": "deepseek_service", "type": "Optional[Any]", "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["service_factory", "deepseek_service"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "service_factory", "type": "Optional[PacerServiceFactory]", "source": "__init__"}, {"name": "deepseek_service", "type": "Optional[Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/row_processing_service.py", "class_name": "PacerRowProcessingService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "deepseek_service", "type": "Any", "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["deepseek_service"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/case_classification_service.py", "class_name": "PacerCaseClassificationService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "transfer_handler", "type": "Optional[TransferHandler]", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["transfer_handler"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "transfer_handler", "type": "Optional[TransferHandler]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/file_management_service.py", "class_name": "PacerFileManagementService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}], "required_parameters": ["logger", "config"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/transfer_service.py", "class_name": "PacerTransferService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "default": "None"}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "default": "None"}, {"name": "gpt_interface", "type": "Optional[GPT4]", "default": "None"}, {"name": "court_lookup", "type": "Optional[Dict[str, str]]", "default": "None"}, {"name": "district_court_repository", "type": "Optional[DistrictCourtsRepository]", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["pacer_repository", "s3_storage", "gpt_interface", "court_lookup", "district_court_repository"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "source": "__init__"}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "source": "__init__"}, {"name": "district_court_repository", "type": "Optional[DistrictCourtsRepository]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/service_factory.py", "class_name": "PacerServiceFactory", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}], "required_parameters": ["logger", "config"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/relevance_service.py", "class_name": "RelevanceService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "relevance_config_dict", "type": "Dict[str, Any]", "default": null}, {"name": "relevant_defendants_list", "type": "List[str]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "usa_defendant_regex_str", "type": "Optional[str]", "default": "None"}], "required_parameters": ["logger", "config", "relevance_config_dict", "relevant_defendants_list", "court_id"], "optional_parameters": ["usa_defendant_regex_str"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "relevance_config_dict", "type": "Dict[str, Any]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/docket_processing_orchestrator_service.py", "class_name": "PacerDocketProcessingOrchestratorService", "constructors": [{"name": "__init__", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "iso_date", "type": "str", "default": null}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "default": "None"}, {"name": "file_manager", "type": "Optional[PacerFileManagementService]", "default": "None"}, {"name": "transfer_handler", "type": "Optional[TransferHandler]", "default": "None"}, {"name": "navigator", "type": "Optional[PacerNavigationService]", "default": "None"}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "default": "None"}, {"name": "end_date_obj", "type": "Optional[datetime]", "default": "None"}, {"name": "deepseek_service", "type": "Any", "default": "None"}], "required_parameters": ["logger", "config", "court_id", "iso_date"], "optional_parameters": ["pacer_repository", "file_manager", "transfer_handler", "navigator", "s3_storage", "end_date_obj", "deepseek_service"], "special_patterns": []}], "dependencies": [{"name": "logger", "type": "LoggerProtocol", "source": "__init__"}, {"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "source": "__init__"}, {"name": "file_manager", "type": "Optional[PacerFileManagementService]", "source": "__init__"}, {"name": "transfer_handler", "type": "Optional[TransferHandler]", "source": "__init__"}, {"name": "navigator", "type": "Optional[PacerNavigationService]", "source": "__init__"}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/pacer/__init__.py"]}, {"file_path": "src/services/reports/config/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/reports/config/__init__.py"]}, {"file_path": "src/services/fb_ads/jobs/job_runner_service.py", "class_name": "JobRunnerService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}], "errors": []}, {"file_path": "src/services/fb_ads/jobs/job_models.py", "class_name": "ProcessFirmJob", "constructors": [{"name": "__init__ (implicit or inherited)", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_patterns": ["No explicit __init__ found for class ProcessFirmJob. May use default or inherit."]}], "dependencies": [], "errors": []}, {"file_path": "src/services/fb_ads/jobs/job_orchestration_service.py", "class_name": "JobOrchestrationService", "constructors": [{"name": "__init__", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "job_runner_service", "type": "JobRunnerService", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}, {"name": "failed_firms_manager", "type": "Optional[FailedFirmsManager]", "default": "None"}], "required_parameters": ["config", "job_runner_service", "logger"], "optional_parameters": ["failed_firms_manager"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "job_runner_service", "type": "JobRunnerService", "source": "__init__"}, {"name": "logger", "type": "logging.Logger", "source": "__init__"}, {"name": "failed_firms_manager", "type": "Optional[FailedFirmsManager]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/browser/browser_service.py", "class_name": "BrowserService", "constructors": [{"name": "__init__", "parameters": [{"name": "headless", "type": "bool", "default": "True"}, {"name": "timeout_ms", "type": "int", "default": "60000"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["headless", "timeout_ms", "config", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/browser/navigator.py", "class_name": "PacerNavigator", "constructors": [{"name": "__init__", "parameters": [{"name": "page", "type": "Page", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "screenshot_dir", "type": "str", "default": null}, {"name": "timeout_ms", "type": "Optional[int]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["page", "config", "screenshot_dir"], "optional_parameters": ["timeout_ms", "logger"], "special_patterns": []}], "dependencies": [{"name": "config", "type": "Dict[str, Any]", "source": "__init__"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "source": "__init__"}], "errors": []}, {"file_path": "src/services/pacer/browser/__init__.py", "class_name": null, "constructors": [], "dependencies": [], "errors": ["No class definitions found in src/services/pacer/browser/__init__.py"]}]