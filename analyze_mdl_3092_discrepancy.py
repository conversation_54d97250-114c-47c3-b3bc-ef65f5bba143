#!/usr/bin/env python3
"""
Analyze the discrepancy in MDL 3092 (Suboxone) calculations.
Compare our analysis (12,810) with Chart.js result (12,962).
"""

import asyncio
import os
import sys
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Tuple
import pandas as pd

from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage

# Load environment variables
load_dotenv()

console = Console()


class Mdl3092DiscrepancyAnalyzer:
    """Analyze MDL 3092 calculation discrepancies."""
    
    def __init__(self):
        self.storage = None
        self.repository = None
        self.mdl_num = '3092'  # Suboxone MDL number
        
    async def initialize(self):
        """Initialize storage and repository."""
        # Create storage configuration
        class StorageConfig:
            def __init__(self):
                self.use_local = False
                self.local_port = 8000
                self.dynamodb_endpoint = None
                self.aws_region = 'us-west-2'
                self.dynamodb_max_retries = 10
                self.dynamodb_base_delay = 1.0
                self.dynamodb_max_delay = 60.0
        
        config = StorageConfig()
        self.storage = AsyncDynamoDBStorage(config)
        self.repository = PacerRepository(self.storage)
        
    async def get_all_filings_30_days(self, end_date_str: str) -> List[Dict[str, Any]]:
        """Get all MDL 3092 filings for the past 30 days."""
        console.print(f"[cyan]Fetching MDL {self.mdl_num} filings for 30 days ending {end_date_str}...[/cyan]")
        
        end_date = datetime.strptime(end_date_str, '%Y%m%d')
        start_date = end_date - timedelta(days=29)  # 30 days including end date
        
        all_filings = []
        current_date = end_date
        
        # Progress indicator
        with console.status("[bold green]Querying DynamoDB...") as status:
            while current_date >= start_date:
                date_str = current_date.strftime('%Y%m%d')
                status.update(f"[bold green]Querying {date_str}...")
                
                # Query all filings for the date
                daily_filings = await self.repository.query_by_filing_date(date_str)
                
                # Filter for MDL 3092
                mdl_filings = [f for f in daily_filings if f.get('mdl_num') == self.mdl_num]
                all_filings.extend(mdl_filings)
                
                current_date -= timedelta(days=1)
        
        console.print(f"[green]Found {len(all_filings)} total MDL {self.mdl_num} filings[/green]")
        return all_filings
    
    def calculate_plaintiff_count(self, filing: Dict[str, Any]) -> int:
        """Calculate plaintiff count for a filing, defaulting to 1."""
        num_plaintiffs = filing.get('num_plaintiffs')
        if num_plaintiffs is None or str(num_plaintiffs) == '0':
            return 1
        try:
            return int(num_plaintiffs)
        except (ValueError, TypeError):
            return 1
    
    async def compare_calculations(self, date_str: str):
        """Compare different calculation methods to find discrepancy."""
        try:
            await self.initialize()
            
            async with self.storage:
                # Header
                console.print(Panel.fit(
                    f"[bold blue]MDL {self.mdl_num} (Suboxone) Discrepancy Analysis[/bold blue]\n"
                    f"[cyan]30-day period ending: {date_str}[/cyan]",
                    border_style="blue"
                ))
                
                # Get all filings
                filings = await self.get_all_filings_30_days(date_str)
                
                if not filings:
                    console.print(f"[red]No MDL {self.mdl_num} filings found[/red]")
                    return
                
                # Convert to DataFrame for easier analysis
                df = pd.DataFrame(filings)
                
                # Method 1: Our manual analysis approach
                console.print("\n[bold yellow]Method 1: Manual Analysis Approach[/bold yellow]")
                
                # Total raw plaintiffs
                total_raw = sum(self.calculate_plaintiff_count(f) for f in filings)
                console.print(f"Total plaintiffs (raw): {total_raw}")
                
                # Deduplicate by versus
                unique_versus = {}
                for f in filings:
                    versus = f.get('versus', f.get('docket_num', ''))
                    key = (self.mdl_num, versus)
                    if key not in unique_versus:
                        unique_versus[key] = f
                
                total_after_versus = sum(self.calculate_plaintiff_count(f) for f in unique_versus.values())
                console.print(f"After versus dedup: {total_after_versus}")
                
                # Method 2: Repository's get_mdl_summary approach
                console.print("\n[bold yellow]Method 2: Repository get_mdl_summary Approach[/bold yellow]")
                
                mdl_summary = await self.repository.get_mdl_summary(date_str)
                mdl_3092_row = mdl_summary[mdl_summary['mdl_num'] == self.mdl_num]
                
                if not mdl_3092_row.empty:
                    repo_total = int(mdl_3092_row.iloc[0]['total'])
                    console.print(f"Repository calculation: {repo_total}")
                else:
                    console.print("[red]MDL 3092 not found in repository results[/red]")
                    repo_total = 0
                
                # Method 3: Pandas deduplication (mimicking repository)
                console.print("\n[bold yellow]Method 3: Pandas Deduplication Analysis[/bold yellow]")
                
                # Check for versus field
                if 'versus' in df.columns:
                    df_deduped = df.drop_duplicates(subset=['mdl_num', 'versus'], keep='first')
                    console.print(f"Unique mdl_num + versus combinations: {len(df_deduped)}")
                else:
                    df_deduped = df
                    console.print("[red]No 'versus' field found - no deduplication possible[/red]")
                
                # Calculate with num_plaintiffs
                if 'num_plaintiffs' in df_deduped.columns:
                    df_deduped['num_plaintiffs'] = pd.to_numeric(df_deduped['num_plaintiffs'], errors='coerce').fillna(1).astype(int)
                else:
                    df_deduped['num_plaintiffs'] = 1
                
                pandas_total = df_deduped['num_plaintiffs'].sum()
                console.print(f"Pandas dedup total: {pandas_total}")
                
                # Analyze the difference
                console.print("\n[bold red]Discrepancy Analysis[/bold red]")
                
                # Compare unique versus counts
                console.print(f"\nTotal filings: {len(filings)}")
                console.print(f"Unique versus (manual): {len(unique_versus)}")
                if 'versus' in df.columns:
                    console.print(f"Unique versus (pandas): {len(df_deduped)}")
                
                # Initialize transfer dedup variables
                transfer_dedup_count = 0
                transfer_dedup_plaintiffs = 0
                
                # Look for specific differences
                if total_after_versus != repo_total:
                    console.print(f"\n[bold red]Discrepancy found![/bold red]")
                    console.print(f"Manual calculation: {total_after_versus}")
                    console.print(f"Repository calculation: {repo_total}")
                    console.print(f"Difference: {repo_total - total_after_versus}")
                    
                    # Check for transfer deduplication
                    console.print("\n[cyan]Checking for transfer deduplication...[/cyan]")
                    
                    # Since MDL 3092 now has transfer dedup, check if that's applied
                    case_lookup = {}
                    for f in filings:
                        court_id = f.get('court_id')
                        docket_num = f.get('docket_num')
                        if court_id and docket_num:
                            case_lookup[(court_id, docket_num)] = f
                    
                    for f in filings:
                        transferor_court = f.get('transferor_court_id')
                        transferor_docket = f.get('transferor_docket_num')
                        
                        if transferor_court and transferor_docket:
                            original = case_lookup.get((transferor_court, transferor_docket))
                            if original and original.get('mdl_num') == self.mdl_num:
                                transfer_dedup_count += 1
                                transfer_dedup_plaintiffs += self.calculate_plaintiff_count(original)
                    
                    console.print(f"Transfer matches that would be deduped: {transfer_dedup_count}")
                    console.print(f"Plaintiffs that would be subtracted: {transfer_dedup_plaintiffs}")
                    
                    final_with_transfer = total_after_versus - transfer_dedup_plaintiffs
                    console.print(f"\nFinal with transfer dedup: {final_with_transfer}")
                    
                    if final_with_transfer == repo_total:
                        console.print("[green]✓ Transfer deduplication explains the difference![/green]")
                    else:
                        console.print(f"[red]✗ Still a difference of {repo_total - final_with_transfer}[/red]")
                        
                        # Look for edge cases
                        console.print("\n[yellow]Checking for edge cases...[/yellow]")
                        
                        # Check for empty versus values
                        empty_versus = sum(1 for f in filings if not f.get('versus') or f.get('versus') == 'N/A')
                        console.print(f"Filings with empty/N/A versus: {empty_versus}")
                        
                        # Check for duplicate handling differences
                        if 'versus' in df.columns:
                            # Group by versus to see duplicates
                            versus_groups = df.groupby('versus').size()
                            duplicates = versus_groups[versus_groups > 1]
                            if len(duplicates) > 0:
                                console.print(f"\nFound {len(duplicates)} versus values with duplicates")
                                console.print("Top 5 duplicated versus values:")
                                for versus, count in duplicates.head().items():
                                    console.print(f"  '{versus}': {count} occurrences")
                else:
                    console.print(f"\n[green]✓ No discrepancy found![/green]")
                    console.print(f"Both methods calculate: {total_after_versus}")
                    
                    # Still check for transfers even if no discrepancy
                    console.print("\n[cyan]Checking for transfer deduplication...[/cyan]")
                    
                    case_lookup = {}
                    for f in filings:
                        court_id = f.get('court_id')
                        docket_num = f.get('docket_num')
                        if court_id and docket_num:
                            case_lookup[(court_id, docket_num)] = f
                    
                    for f in filings:
                        transferor_court = f.get('transferor_court_id')
                        transferor_docket = f.get('transferor_docket_num')
                        
                        if transferor_court and transferor_docket:
                            original = case_lookup.get((transferor_court, transferor_docket))
                            if original and original.get('mdl_num') == self.mdl_num:
                                transfer_dedup_count += 1
                                transfer_dedup_plaintiffs += self.calculate_plaintiff_count(original)
                    
                    console.print(f"Transfer matches that would be deduped: {transfer_dedup_count}")
                    console.print(f"Plaintiffs that would be subtracted: {transfer_dedup_plaintiffs}")
                    
                    if transfer_dedup_plaintiffs > 0:
                        console.print(f"\n[yellow]Note: Transfer deduplication is not being applied![/yellow]")
                        console.print(f"Expected total with transfers: {total_after_versus - transfer_dedup_plaintiffs}")
                
                # Summary table
                console.print("\n[bold green]Summary[/bold green]")
                
                summary_table = Table(show_header=True, header_style="bold cyan")
                summary_table.add_column("Method", style="yellow")
                summary_table.add_column("Count", style="green", justify="right")
                summary_table.add_column("Note", style="white")
                
                summary_table.add_row("Raw total", str(total_raw), "All filings, no dedup")
                summary_table.add_row("Manual versus dedup", str(total_after_versus), "Our analysis")
                summary_table.add_row("Repository result", str(repo_total), "Chart.js value")
                summary_table.add_row("With transfer dedup", str(total_after_versus - transfer_dedup_plaintiffs), "Manual + transfers")
                
                console.print(summary_table)
                
        except Exception as e:
            console.print(f"[red]Error during analysis: {e}[/red]")
            import traceback
            traceback.print_exc()


async def main():
    """Main entry point."""
    # Get current date or use provided date
    if len(sys.argv) > 1:
        date_str = sys.argv[1]
    else:
        date_str = datetime.now().strftime('%Y%m%d')
    
    analyzer = Mdl3092DiscrepancyAnalyzer()
    await analyzer.compare_calculations(date_str)


if __name__ == "__main__":
    asyncio.run(main())