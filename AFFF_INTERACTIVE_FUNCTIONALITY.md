# AFFF Case Shell Processor - Interactive Law Firm Functionality

## Overview

The AFFF case shell processor now includes interactive functionality to handle unknown attorneys by prompting the user for law firm information. This eliminates the need to manually update the attorney lookup file after processing.

## How It Works

### 1. Unknown Attorney Detection
When processing AFFF data, if an attorney is not found in the `attorney_law_firm_lookup.json` file, the system will:

- Display the attorney name
- Show any similar names from the lookup for reference
- Prompt the user to enter the law firm name

### 2. Interactive Prompt
```
🔍 Unknown Attorney Found:
  Attorney: <PERSON>
  Similar names in lookup: ['john doe', 'jane smith', 'john wilson']

Enter law firm for '<PERSON>' (or press Enter for 'Unknown'): 
```

### 3. User Options
- **Enter law firm name**: The attorney will be associated with that law firm
- **Press Enter**: The attorney will remain as "Unknown"
- **Ctrl+C**: Processing continues with "Unknown" for that attorney

### 4. Session Updates
When a law firm is provided for an attorney:

1. **Immediate Update**: The `attorney_law_firm_lookup.json` file is updated immediately (no backup created)
2. **Memory Update**: The in-memory lookup is updated for the current session
3. **Batch Application**: All other entries with the same attorney in the current processing batch are updated

### 5. Workflow Example

If processing 10 records and "<PERSON>" appears in 3 of them:

1. First occurrence: User is prompted for law firm → User enters "Smith & Associates"
2. Second occurrence: Automatically uses "Smith & Associates" (no prompt)
3. Third occurrence: Automatically uses "Smith & Associates" (no prompt)

## Implementation Details

### Key Methods Added

#### `_prompt_for_law_firm(attorney_name: str) -> str`
- Displays the unknown attorney with similar names for reference
- Prompts user for law firm name
- Handles keyboard interrupts gracefully
- Returns law firm name or "Unknown"

#### `_update_attorney_lookup_immediate(attorney_name: str, law_firm: str) -> None`
- Updates the attorney lookup file immediately (no backup)
- Updates in-memory lookup structures
- Adds metadata about when the attorney was added

#### `_apply_session_updates_to_processed_data(processed_data: List[Dict[str, Any]]) -> None`
- Applies all session attorney updates to the processed data
- Ensures all entries with the same attorney get the same law firm
- Logs the number of updates applied

### Session Tracking

The processor maintains a `session_attorney_updates` dictionary that tracks attorney → law firm mappings for the current processing session. This ensures:

- Users are only prompted once per attorney per session
- All entries for the same attorney get consistent law firm assignments
- Updates are applied retroactively to all processed entries

### File Updates

The `attorney_law_firm_lookup.json` file is updated immediately when a law firm is provided:

- **No backup file created** (as requested)
- File structure is preserved
- Metadata is updated with current timestamp
- New attorney is added with interactive note

Example update:
```json
{
  "attorneys": {
    "John Smith": {
      "law_firm": "Smith & Associates",
      "notes": "Added interactively on 20250716"
    }
  },
  "metadata": {
    "total_attorneys": 150,
    "last_updated": "2025-07-16 14:30:15"
  }
}
```

## Benefits

1. **Real-time Updates**: No need to manually update attorney files after processing
2. **Efficiency**: Users are prompted only once per attorney per session
3. **Consistency**: All entries for the same attorney get the same law firm
4. **User Control**: Users can choose to leave attorneys as "Unknown"
5. **Immediate Persistence**: Updates are saved immediately to prevent data loss

## Usage

The interactive functionality is automatically enabled when processing AFFF data. No additional flags or configuration required.

```bash
# Standard usage - now includes interactive prompts
python afff_case_shell_processor.py --date 20250716

# The processor will prompt for unknown attorneys during processing
```

## Testing

A test script (`test_interactive_afff.py`) is available to verify the functionality works correctly with mock data and user inputs.