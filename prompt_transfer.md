**Role:** You are an expert software engineer specializing in code refactoring and architecture consolidation. Your task is to analyze legacy code and integrate it into a more modern, structured component, ensuring functionality is preserved and maintainability is improved.

**Task:** Refactor and consolidate the data transfer logic from the standalone script `@transfer_service.py` into the `@src/pacer` component.

**Context:**
Our project currently has data transfer logic in two places:
1.  A legacy script, `@transfer_service.py`, located in the project root. This script likely contains core business logic for handling transfers but is not well-integrated with the rest of the application.
2.  A newer, more structured component located at `@src/pacer/`, which is intended to be the central hub for all pacing and transfer-related operations.

The goal is to eliminate the legacy script by migrating all of its essential functionality into the appropriate modules within `@src/pacer`. This will centralize the logic, reduce redundancy, and make the system easier to maintain.
All features of transfer_service should be executed when @src/pacer is processing a transfer case.

**Files to Analyze:**
*   **Primary Source File:** `@transfer_service.py`
*   **Primary Target Directory:** `@src/pacer/`

---

**Step-by-Step Plan:**

1.  **Analysis Phase: Understand the Source**
    *   Read and fully analyze the contents of `@transfer_service.py`.
    *   Identify all public functions, classes, and key business logic it contains.
    *   Document its dependencies (e.g., external libraries, environment variables, other project modules).
    *   Summarize its primary responsibilities. What specific types of transfers does it handle? What are its main entry points?

2.  **Discovery Phase: Identify the Target**
    *   List the files within the `@src/pacer/` directory.
    *   Based on their names and contents, identify the most appropriate target files for the migration. Likely candidates might be `transfer.py`, `services.py`, `utils.py`, or similar.
    *   If no suitable file exists for a piece of logic, propose the creation of a new file within `@src/pacer/`.

3.  **Comparison and Mapping Phase:**
    *   Compare the functionality in `@transfer_service.py` with the existing functionality in the target files within `@src/pacer/`.
    *   Create a mapping:
        *   **Redundant Logic:** Identify functions/classes in `@transfer_service.py` that are already implemented correctly in `@src/pacer`. These can be discarded.
        *   **Unique Logic:** Identify functions/classes in `@transfer_service.py` that provide functionality *missing* from `@src/pacer`. This is the core logic to be migrated.
        *   **Conflicting Logic:** Identify any areas where both implement the same feature but in different ways. You must decide which implementation to use or how to merge them, prioritizing the architecture of `@src/pacer`.

4.  **Integration and Refactoring Phase:**
    *   Begin migrating the "Unique Logic" from `@transfer_service.py` into the target files you identified in `@src/pacer/`.
    *   **Important:** Do not just copy-paste. You must refactor the code to match the style, patterns, and conventions (e.g., class structures, error handling, logging) of the `@src/pacer` component.
    *   Update or add necessary imports and handle any dependency changes within the `@src/pacer` component.
    *   Ensure that any code that previously called `@transfer_service.py` is updated to call the new logic within `@src/pacer`. You may need to perform a project-wide search for usages.

5.  **Verification and Testing Phase:**
    *   Review any existing unit tests related to `@src/pacer/`.
    *   Update these tests or create new ones to cover the newly migrated functionality.
    *   Ensure all tests pass and that code coverage for the transfer logic is maintained or improved.

6.  **Cleanup Phase:**
    *   Once you have confirmed that all functionality has been migrated and is fully tested, delete the `@transfer_service.py` file.

---

**Acceptance Criteria (Definition of Done):**

*   All essential business logic from `@transfer_service.py` has been successfully migrated into the `@src/pacer` component.
*   The migrated code adheres to the coding standards and architectural patterns of the `@src/pacer` component.
*   The old `@transfer_service.py` file has been removed from the project root.
*   All existing application functionality that depended on the old service continues to work correctly using the new, integrated service.
*   Unit tests have been updated or created to validate the migrated logic, and all tests are passing.
*   There is no redundant or dead code remaining from the refactoring process.

**Instruction:**
Begin with Step 1. Present your analysis of `@transfer_service.py` and your proposed plan for which files within `@src/pacer` will receive the new logic before you begin writing or modifying any code.

### Mandatory Agent and Environment Instructions
- **Agent Mapping:** When you need to spawn a specialist agent, you **MUST** use the following names:
  - Analyst: `code-analyzer`
  - Coordinator: `task-orchestrator`
  - Optimizer: `perf-analyzer`
  - Documenter: `api-docs`
  - Monitor: `performance-benchmarker`
  - Specialist: `system-architect`
  - Architect: `system-architect`
- **Agent Spawning Errors:** If you encounter an error when spawning an agent, read the error message carefully. It will list the available agents. You **MUST** select the most appropriate agent from that list for the task at hand.
- **Environment:** Our development environment now uses `uv pip`. **DO NOT** use or generate commands for `conda env`.
- DO NOT SAVE 20 different documentation of fixes, because there are too  many of them. I only need the latest relevant changes!!!