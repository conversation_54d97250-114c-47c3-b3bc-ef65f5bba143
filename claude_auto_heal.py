#!/usr/bin/env python3
"""
Claude Code Auto-Healing Pipeline Runner
Continuously runs pipelines and passes errors to Claude Code for automatic resolution.
"""

import asyncio
import json
import logging
import os
import re
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from uuid import uuid4

# Rich imports for beautiful console output
try:
    from rich.console import Console
    from rich.live import Live
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
    from rich.text import Text

    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    Console = None
    Panel = None
    Progress = None
    SpinnerColumn = None
    TextColumn = None
    TimeElapsedColumn = None
    Live = None
    Text = None

# Initialize Rich console
console = Console() if RICH_AVAILABLE else None

# Configure logging (only for file output, console will use Rich)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("claude_auto_heal.log")],  # Only log to file
)
logger = logging.getLogger(__name__)


def rich_log(message: str, style: str = "", log_level: str = "info"):
    """Log message with Rich formatting to console and plain text to file"""
    if RICH_AVAILABLE and console:
        if style:
            console.print(f"{message}", style=style)
        else:
            console.print(message)
    else:
        print(message)

    # Also log to file
    getattr(logger, log_level)(message)


class ClaudeAutoHealer:
    """Auto-healing pipeline runner that invokes Claude Code to fix errors."""

    def __init__(
        self,
        config_type: str = "transform",
        max_retries: int = 5,
        retry_delay: int = 30,
        auto_fix: bool = True,
    ):
        self.config_type = config_type
        # Handle reports vs report naming inconsistency and scrape_pacer config
        if config_type == "reports":
            self.config_file = "report.yml"
        elif config_type == "scrape_pacer":
            self.config_file = "scrape_pacer.yml"
        else:
            self.config_file = f"{config_type}.yml"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.auto_fix = auto_fix
        
        # Add task context management
        self.task_id = self._get_or_create_task_id()
        self.context_file = f".claude_context_{self.task_id}.json"
        self._load_context()

        # SuperClaude command templates for different error types
        self.claude_commands = {
            "dependency_injection": self._generate_di_command,
            "missing_argument": self._generate_missing_arg_command,
            "import_error": self._generate_import_command,
            "config_error": self._generate_config_command,
            "type_error": self._generate_type_error_command,
            "attribute_error": self._generate_attr_error_command,
            "file_not_found": self._generate_file_error_command,
            "generic_error": self._generate_generic_error_command,
        }

        # Error detection patterns
        self.error_patterns = {
            "dependency_injection": r"TypeError.*got an unexpected keyword argument|__init__\(\).*unexpected keyword|Dependency is not defined|dependency_injector\.providers\.Dependency.*undefined",
            "missing_argument": r"missing.*required.*argument|takes.*positional.*given",
            "import_error": r"ImportError|ModuleNotFoundError|No module named",
            "config_error": r"ConfigurationError|Config.*not found|KeyError.*config",
            "type_error": r"TypeError(?!.*unexpected keyword)",
            "attribute_error": r"AttributeError.*has no attribute",
            "file_not_found": r"FileNotFoundError|No such file or directory",
        }

    def _get_or_create_task_id(self) -> str:
        """Get or create a unique task ID for this pipeline instance"""
        # Try to read from environment variable first (for process continuity)
        task_id = os.getenv(f"CLAUDE_TASK_ID_{self.config_type.upper()}")
        
        if not task_id:
            # Generate new task ID and store in environment
            task_id = f"{self.config_type}_{str(uuid4())[:8]}"
            os.environ[f"CLAUDE_TASK_ID_{self.config_type.upper()}"] = task_id
        
        return task_id

    def _load_context(self):
        """Load saved context from previous runs"""
        self.context = {
            "task_id": self.task_id,
            "config_type": self.config_type,
            "error_history": [],
            "last_successful_run": None,
            "claude_fixes_applied": []
        }
        
        if os.path.exists(self.context_file):
            try:
                with open(self.context_file, 'r') as f:
                    self.context.update(json.load(f))
            except Exception as e:
                rich_log(f"⚠️ Failed to load context: {e}", "bold yellow")

    def _save_context(self):
        """Save current context to file"""
        try:
            with open(self.context_file, 'w') as f:
                json.dump(self.context, f, indent=2)
        except Exception as e:
            rich_log(f"⚠️ Failed to save context: {e}", "bold yellow")

    def run_pipeline(self) -> tuple[bool, str, str]:
        """Run the specific pipeline configuration."""
        try:
            cmd = ["./run_pipeline.sh", "--config", self.config_file]
            rich_log(
                f"🚀 Running {self.config_type} pipeline: {' '.join(cmd)}", "bold blue"
            )

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600,  # 10 minute timeout
                cwd=Path.cwd(),
            )

            stdout = result.stdout
            stderr = result.stderr
            combined_output = stdout + stderr

            # Check for errors in the output regardless of exit code
            has_error = self.detect_error_type(combined_output) is not None

            # Success only if exit code is 0 AND no errors detected in output
            success = result.returncode == 0 and not has_error

            if success:
                rich_log(
                    f"✅ {self.config_type.title()} pipeline completed successfully",
                    "bold green",
                )
            else:
                if result.returncode != 0:
                    rich_log(
                        f"❌ {self.config_type.title()} pipeline failed with exit code: {result.returncode}",
                        "bold red",
                        "error",
                    )
                if has_error:
                    rich_log(
                        f"🚨 {self.config_type.title()} pipeline failed with detected errors in output",
                        "bold yellow",
                        "error",
                    )

            return success, stdout, stderr

        except subprocess.TimeoutExpired:
            rich_log(
                f"⏰ {self.config_type.title()} pipeline timed out", "bold red", "error"
            )
            return False, "", "Pipeline execution timed out"
        except Exception as e:
            rich_log(
                f"💥 Unexpected error running {self.config_type} pipeline: {e}",
                "bold red",
                "error",
            )
            return False, "", str(e)

    def detect_error_type(self, output: str) -> str | None:
        """Detect ANY error from pipeline output - stop immediately on any error."""
        # Clean Rich/ANSI formatting from output
        ansi_escape = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")
        clean_output = ansi_escape.sub("", output)
        combined_output = clean_output.lower()

        # Check for ANY error indicators - be very broad and include Rich console patterns
        error_indicators = [
            "error:",
            "traceback",
            "exception",
            "failed",
            "failure",
            "dependency is not defined",
            "module not found",
            "import error",
            "type error",
            "attribute error",
            "name error",
            "value error",
            "file not found",
            "permission denied",
            "connection error",
            "timeout",
            "unexpected keyword",
            "missing argument",
            "configuration error",
            "validation error",
            "runtime error",
            # Rich console specific patterns
            "encountered an error",
            "error occurred",
            "traceback (most recent call last)",
            "╭─────────────",
            "╰─────────────",  # Rich error box borders
            "component_base.py:",
            "mainorchestrator encountered an error",
        ]

        # If ANY error indicator is found, classify it
        if any(indicator in combined_output for indicator in error_indicators):
            rich_log(
                f"🚨 DETECTED ERROR in {self.config_type} pipeline - stopping immediately",
                "bold red",
            )
            rich_log(f"🔍 Raw output length: {len(output)} chars", "dim")
            rich_log(f"🔍 Clean output length: {len(clean_output)} chars", "dim")

            # Classify specific error types for better handling
            for error_type, pattern in self.error_patterns.items():
                if re.search(pattern, clean_output, re.IGNORECASE | re.MULTILINE):
                    rich_log(f"🔍 Classified as: {error_type}", "bold cyan")
                    return error_type

            # If we can't classify, return generic error
            rich_log("🔍 Classified as: generic_error", "bold cyan")
            return "generic_error"

        return None

    def extract_error_context(self, output: str, error_type: str) -> dict[str, str]:
        """Extract relevant context from error output."""
        # Clean Rich/ANSI formatting for better parsing
        ansi_escape = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")
        clean_output = ansi_escape.sub("", output)

        context = {
            "error_type": error_type,
            "config_type": self.config_type,
            "full_output": output,
            "clean_output": clean_output,
            "timestamp": datetime.now().isoformat(),
        }

        # Extract specific details based on error type (use clean output for better parsing)
        if error_type == "dependency_injection":
            # Check for unexpected keyword argument
            match = re.search(
                r"(\w+).__init__\(\).*unexpected keyword argument '(\w+)'", clean_output
            )
            if match:
                context["service_name"] = match.group(1)
                context["invalid_param"] = match.group(2)
            # Check for undefined dependency
            elif "Dependency is not defined" in clean_output:
                context["error_detail"] = "undefined_dependency"
                # Extract the factory/container path that failed
                factory_match = re.search(r"(/\S+/src/factories/\S+\.py)", clean_output)
                if factory_match:
                    context["factory_file"] = factory_match.group(1)
                # Extract container path from traceback
                container_match = re.search(
                    r"self\._container\.(\w+)\.(\w+)\(\)", clean_output
                )
                if container_match:
                    context["container_service"] = container_match.group(1)
                    context["service_method"] = container_match.group(2)

        elif error_type == "import_error":
            match = re.search(r"No module named '(\w+)'", output)
            if match:
                context["missing_module"] = match.group(1)

        elif error_type == "file_not_found":
            match = re.search(r"No such file or directory: '([^']+)'", output)
            if match:
                context["missing_file"] = match.group(1)

        # Extract file paths and line numbers
        traceback_match = re.search(r'File "([^"]+)", line (\d+)', output)
        if traceback_match:
            context["error_file"] = traceback_match.group(1)
            context["error_line"] = traceback_match.group(2)

        return context

    def _generate_di_command(self, context: dict[str, str]) -> str:
        """Generate SuperClaude command for dependency injection errors."""
        error_detail = context.get("error_detail", "parameter_mismatch")

        if error_detail == "undefined_dependency":
            # Handle "Dependency is not defined" errors
            container_service = context.get("container_service", "unknown")
            service_method = context.get("service_method", "unknown")
            factory_file = context.get("factory_file", "src/factories/main_factory.py")

            return f"""/troubleshoot --investigate --fix --seq

Undefined dependency error in {self.config_type} pipeline:
- Container service: {container_service}
- Service method: {service_method}
- Factory file: {factory_file}

Error: "Dependency is not defined"

Investigation needed:
1. Check the dependency injection container for missing service definitions
2. Ensure all required services are properly defined in the container
3. Verify that `{container_service}.{service_method}()` is properly configured
4. Check if the service definition exists in the appropriate container module
5. Ensure proper container wiring and initialization

Files to check:
- {factory_file}
- src/containers/{container_service}.py (if exists)
- src/containers/core.py

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline undefined dependency in DI container"""
        else:
            # Handle parameter mismatch errors
            service_name = context.get("service_name", "Unknown")
            invalid_param = context.get("invalid_param", "unknown")
            error_file = context.get("error_file", "src/containers/transformer.py")

            return f"""/troubleshoot --investigate --fix --seq

Dependency injection parameter error in {self.config_type} pipeline:
- Service: {service_name}
- Invalid parameter: {invalid_param}
- File: {error_file}

The service `{service_name}` is receiving an unexpected parameter `{invalid_param}`.

Investigation needed:
1. Check the constructor of `{service_name}` to see what parameters it actually expects
2. Fix the dependency injection container to pass the correct parameters
3. Ensure the parameter names match exactly what the constructor expects

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline DI parameter mismatch"""

    def _generate_missing_arg_command(self, context: dict[str, str]) -> str:
        """Generate SuperClaude command for missing argument errors."""
        error_file = context.get("error_file", "Unknown")
        error_line = context.get("error_line", "Unknown")

        return f"""/troubleshoot --investigate --fix --seq

Missing argument error in {self.config_type} pipeline:
- File: {error_file}
- Line: {error_line}

Investigation needed:
1. Identify which function/method is missing required arguments
2. Check the call site and ensure all required parameters are provided
3. Update the dependency injection if needed
4. Verify parameter types and names match expectations

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline missing required arguments"""

    def _generate_import_command(self, context: dict[str, str]) -> str:
        """Generate SuperClaude command for import errors."""
        missing_module = context.get("missing_module", "unknown")
        error_file = context.get("error_file", "Unknown")

        return f"""/troubleshoot --investigate --fix

Import error in {self.config_type} pipeline:
- Missing module: {missing_module}
- File: {error_file}

Fix actions needed:
1. Install missing module: `pip install {missing_module}` or `mamba install {missing_module}`
2. Check if import path is correct
3. Verify module exists in current environment
4. Check for typos in module name
5. Ensure conda environment is activated: `conda activate lexgenius`

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline module dependency error"""

    def _generate_config_command(self, context: dict[str, str]) -> str:
        """Generate SuperClaude command for configuration errors."""
        return f"""/review --files config/{self.config_file} --quality --evidence --fix

Configuration error in {self.config_type} pipeline:

Investigation needed:
1. Check if config/{self.config_file} exists and is properly formatted
2. Verify all required configuration keys are present
3. Check for typos in configuration key names
4. Ensure the configuration model matches the YAML structure
5. Validate Pydantic model compatibility with YAML

Also run: /troubleshoot --investigate --fix --seq

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline configuration validation error"""

    def _generate_type_error_command(self, context: dict[str, str]) -> str:
        """Generate SuperClaude command for type errors."""
        error_file = context.get("error_file", "Unknown")
        error_line = context.get("error_line", "Unknown")

        return f"""/troubleshoot --investigate --fix --seq

Type error in {self.config_type} pipeline:
- File: {error_file}
- Line: {error_line}

Investigation needed:
1. Check the type mismatch in the error traceback
2. Ensure proper type conversion or validation
3. Update type hints if necessary
4. Fix any incompatible type operations
5. Verify data types match function expectations

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline type mismatch error"""

    def _generate_attr_error_command(self, context: dict[str, str]) -> str:
        """Generate SuperClaude command for attribute errors."""
        error_file = context.get("error_file", "Unknown")
        error_line = context.get("error_line", "Unknown")

        return f"""/analyze --code --forensic --seq

Attribute error in {self.config_type} pipeline:
- File: {error_file}
- Line: {error_line}

Forensic analysis needed:
1. Check if the attribute name is spelled correctly
2. Verify the object has been properly initialized
3. Check if the attribute should be a method call instead
4. Ensure imports are correct and objects are instantiated
5. Verify class inheritance and method availability

Follow up with: /troubleshoot --fix

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline object attribute access error"""

    def _generate_file_error_command(self, context: dict[str, str]) -> str:
        """Generate SuperClaude command for file not found errors."""
        missing_file = context.get("missing_file", "unknown")
        error_file = context.get("error_file", "Unknown")

        return f"""/troubleshoot --investigate --fix

File not found error in {self.config_type} pipeline:
- Missing file: {missing_file}
- Error in: {error_file}

Investigation and fix needed:
1. Check if the file path is correct
2. Create the missing file if needed
3. Verify file permissions and accessibility
4. Check if the file should be in a different location
5. Update the path in the code if necessary
6. Ensure data directory structure exists

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline file system access error"""

    def _generate_generic_error_command(self, context: dict[str, str]) -> str:
        """Generate SuperClaude command for any unclassified error."""
        error_file = context.get("error_file", "Unknown")
        error_line = context.get("error_line", "Unknown")

        return f"""/troubleshoot --investigate --fix --seq

Generic error detected in {self.config_type} pipeline:
- File: {error_file}
- Line: {error_line}

The {self.config_type} pipeline has encountered an error that needs immediate investigation.

Full error analysis and fix needed:
1. Review the complete error traceback and output
2. Identify the root cause of the failure
3. Apply appropriate fixes based on error type
4. Test the fix to ensure pipeline runs successfully
5. Verify no regression issues are introduced

Test command: `python src/main.py --params config/{self.config_file}`

Context: {self.config_type} pipeline error requiring immediate investigation and fix"""

    def stream_claude_output(
        self, claude_cmd: list[str], timeout: int = 1800
    ) -> subprocess.CompletedProcess:
        """Enhanced progress indicator for Claude Code (batch processing)."""
        rich_log(
            "🤖 Claude Code processes internally - showing enhanced progress...",
            "bold blue",
        )

        if not RICH_AVAILABLE or not console:
            # Fallback without Rich
            rich_log("🤖 Claude Code is analyzing and fixing errors...", "bold blue")
            return subprocess.run(
                claude_cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=Path.cwd(),
            )

        try:
            # Start Claude Code process
            start_time = time.time()

            process = subprocess.Popen(
                claude_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=Path.cwd(),
            )

            # Enhanced progress indicator with meaningful messages
            progress_messages = [
                "🔍 Analyzing error context and patterns...",
                "🧠 Understanding codebase structure...",
                "📚 Referencing best practices and patterns...",
                "⚡ Generating targeted fix solutions...",
                "🔧 Preparing code modifications...",
                "✨ Finalizing fixes and validation...",
                "📝 Generating response and recommendations...",
            ]

            message_interval = max(
                3.0, timeout / len(progress_messages)
            )  # Show each message for at least 3s
            message_index = 0
            last_message_time = start_time

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                TextColumn("│"),
                TextColumn("[dim cyan]{task.fields[elapsed]:.1f}s[/dim cyan]"),
                TextColumn("│"),
                TextColumn("[dim]{task.fields[status]}[/dim]"),
                console=console,
                transient=False,
            ) as progress:
                task = progress.add_task(
                    "🤖 Claude Code Working",
                    total=None,
                    elapsed=0.0,
                    status="Starting analysis...",
                )

                # Monitor process with enhanced status updates
                while process.poll() is None:
                    current_time = time.time()
                    elapsed = current_time - start_time

                    # Update elapsed time
                    progress.update(task, elapsed=elapsed)

                    # Update status message periodically
                    if (
                        current_time - last_message_time >= message_interval
                        and message_index < len(progress_messages)
                    ):
                        progress.update(task, status=progress_messages[message_index])
                        message_index += 1
                        last_message_time = current_time

                    # Check for timeout
                    if elapsed > timeout:
                        process.terminate()
                        progress.update(task, status="⏰ Timed out - terminating...")
                        break

                    time.sleep(0.5)  # Update every 500ms

                # Get final results
                stdout, stderr = process.communicate()
                returncode = process.returncode
                final_elapsed = time.time() - start_time

                # Update final status
                if returncode == 0:
                    progress.update(task, status="✅ Completed successfully!")
                else:
                    progress.update(task, status=f"❌ Failed (exit code {returncode})")

                time.sleep(1)  # Show final status briefly

            # Show completion summary
            console.print(
                Panel(
                    f"🏁 **Claude Code Execution Complete**\n\n"
                    f"⏱️  **Duration:** {final_elapsed:.1f} seconds\n"
                    f"🔢 **Exit Code:** {returncode}\n"
                    f"📤 **Output:** {len(stdout)} characters\n"
                    f"🔍 **Status:** {'Success' if returncode == 0 else 'Failed'}",
                    title="📊 Execution Summary",
                    border_style="green" if returncode == 0 else "red",
                    expand=False,
                )
            )

            # Show Claude's output if available
            if stdout and stdout.strip():
                console.print(
                    Panel(
                        stdout.strip(),
                        title="🤖 Claude Code Output",
                        border_style="blue",
                        expand=False,
                    )
                )

            rich_log(
                f"🎬 Claude Code completed in {final_elapsed:.1f}s with exit code: {returncode}",
                "bold green" if returncode == 0 else "bold red",
            )

            # Create result object
            class MockCompletedProcess:
                def __init__(self, returncode, stdout, stderr):
                    self.returncode = returncode
                    self.stdout = stdout
                    self.stderr = stderr

            return MockCompletedProcess(returncode, stdout, stderr)

        except Exception as e:
            rich_log(f"💥 Error in enhanced progress mode: {e}", "bold red", "error")
            rich_log("🔄 Falling back to basic progress indicator", "bold yellow")
            return self._run_claude_with_progress(claude_cmd, timeout)

    def _run_claude_with_progress(
        self, claude_cmd: list[str], timeout: int = 1800
    ) -> subprocess.CompletedProcess:
        """Fallback method using progress spinner."""
        if RICH_AVAILABLE and console:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                TimeElapsedColumn(),
                console=console,
                transient=False,
            ) as progress:
                task = progress.add_task(
                    "🤖 Claude Code is analyzing and fixing errors...", total=None
                )

                result = subprocess.run(
                    claude_cmd,
                    capture_output=True,
                    text=True,
                    timeout=timeout,
                    cwd=Path.cwd(),
                )

                progress.remove_task(task)
                return result
        else:
            # No Rich available
            rich_log("🤖 Claude Code is analyzing and fixing errors...", "bold blue")
            return subprocess.run(
                claude_cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=Path.cwd(),
            )

    async def invoke_claude_fix(self, error_context: dict[str, str]) -> bool:
        """Enhanced version with task context"""
        # Add to error history
        self.context["error_history"].append({
            "timestamp": datetime.now().isoformat(),
            "error_type": error_context["error_type"],
            "details": error_context
        })
        
        error_type = error_context["error_type"]

        if error_type not in self.claude_commands:
            rich_log(
                f"🤷 No Claude command available for error type: {error_type}",
                "bold red",
                "error",
            )
            return False

        # Generate task-aware command
        base_command = self.claude_commands[error_type](error_context)
        
        # Add task context to command
        claude_command = (
            f"/task:update {self.task_id} \"{error_context['error_type']} error detected\"\n"
            f"{base_command}\n"
            f"/task:update {self.task_id} \"Analyzing and fixing error\"\n"
            f"Context: {self.config_type} pipeline error in task {self.task_id}"
        )

        rich_log(
            f"🤖 Invoking Claude Code to fix {error_type} in {self.config_type} pipeline...",
            "bold blue",
        )
        rich_log(f"📝 Claude Command: {claude_command.strip()}", "dim")

        command_file = None
        try:
            # Write the command to a file for Claude to process
            command_file = (
                f"claude_fix_{self.config_type}_{error_type}_{int(time.time())}.md"
            )
            with open(command_file, "w") as f:
                f.write(
                    f"# Auto-Healing Fix Request - {self.config_type.title()} Pipeline\n\n"
                )
                f.write(claude_command)
                f.write(
                    f"\n\n## Error Output\n```\n{error_context['full_output']}\n```\n"
                )

            rich_log(f"📄 Claude fix request written to: {command_file}", "bold green")

            if self.auto_fix:
                # Automatically launch Claude Code to process the fix request
                rich_log(
                    "🤖 Automatically launching Claude Code to analyze and fix the error...",
                    "bold blue",
                )

                try:
                    # Run Claude Code with the fix request file and bypass permissions for automation
                    claude_cmd = [
                        "claude",
                        "--dangerously-skip-permissions",
                        command_file,
                    ]

                    rich_log(f"🚀 Executing: {' '.join(claude_cmd)}", "bold green")

                    # Use real-time streaming to show Claude's output as it happens
                    claude_result = self.stream_claude_output(claude_cmd, timeout=1800)

                    # Display results in a beautiful Rich panel
                    if RICH_AVAILABLE and console:
                        if claude_result.returncode == 0:
                            # Success case - show output in green panel
                            console.print(
                                Panel(
                                    (
                                        claude_result.stdout
                                        if claude_result.stdout.strip()
                                        else "✅ Claude Code completed successfully with no output"
                                    ),
                                    title="🤖 Claude Code - Success",
                                    border_style="green",
                                    expand=False,
                                )
                            )
                            if claude_result.stderr.strip():
                                console.print(
                                    Panel(
                                        claude_result.stderr,
                                        title="⚠️ Claude Code - Warnings",
                                        border_style="yellow",
                                        expand=False,
                                    )
                                )
                        else:
                            # Error case - show output in red panel
                            error_content = []
                            if claude_result.stdout.strip():
                                error_content.append(f"STDOUT:\n{claude_result.stdout}")
                            if claude_result.stderr.strip():
                                error_content.append(f"STDERR:\n{claude_result.stderr}")

                            console.print(
                                Panel(
                                    (
                                        "\n\n".join(error_content)
                                        if error_content
                                        else f"Claude Code failed with exit code {claude_result.returncode}"
                                    ),
                                    title=f"❌ Claude Code - Failed (Exit Code: {claude_result.returncode})",
                                    border_style="red",
                                    expand=False,
                                )
                            )
                    else:
                        # Fallback display without Rich
                        if claude_result.returncode == 0:
                            rich_log(
                                "✅ Claude Code completed successfully!", "bold green"
                            )
                            if claude_result.stdout.strip():
                                rich_log(f"📄 Output: {claude_result.stdout}", "dim")
                        else:
                            rich_log(
                                f"❌ Claude Code failed with exit code: {claude_result.returncode}",
                                "bold red",
                                "error",
                            )
                            if claude_result.stderr.strip():
                                rich_log(
                                    f"🔥 Error output: {claude_result.stderr}",
                                    "bold red",
                                    "error",
                                )

                    # Return success status
                    if claude_result.returncode == 0:
                        rich_log(
                            "🔧 Fixes have been automatically applied", "bold green"
                        )
                        # Save context after each fix attempt
                        self._save_context()
                        return True
                    else:
                        rich_log(
                            "🚫 Claude Code failed to apply fixes", "bold red", "error"
                        )
                        self._save_context()
                        return False

                except subprocess.TimeoutExpired:
                    rich_log(
                        "⏰ Claude Code timed out after 30 minutes", "bold red", "error"
                    )
                    self._save_context()
                    return False
                except FileNotFoundError:
                    rich_log(
                        "❌ Claude Code not found - please ensure 'claude' command is available in PATH",
                        "bold red",
                        "error",
                    )
                    rich_log(
                        "💡 Install with: pip install claude-cli",
                        "bold yellow",
                        "error",
                    )
                    self._save_context()
                    return False
            else:
                # Manual approach - wait for user to manually run Claude and indicate completion
                rich_log(
                    "📋 Manual mode enabled. Please run the following command:",
                    "bold yellow",
                )
                rich_log(f"   claude {command_file}", "bold cyan")
                rich_log(
                    "📝 Then press Enter when Claude has completed the fixes...",
                    "bold yellow",
                )

                # Simple polling mechanism for manual mode
                while True:
                    try:
                        user_input = input().strip().lower()
                        if user_input in [
                            "",
                            "done",
                            "complete",
                            "finished",
                            "y",
                            "yes",
                        ]:
                            rich_log(
                                "✅ User indicated Claude fixes are complete",
                                "bold green",
                            )
                            self._save_context()
                            return True
                        elif user_input in ["skip", "abort", "cancel", "n", "no"]:
                            rich_log(
                                "⏭️  User chose to skip Claude fixes", "bold yellow"
                            )
                            self._save_context()
                            return False
                        else:
                            rich_log(
                                "📝 Press Enter when complete, or type 'skip' to abort...",
                                "dim",
                            )
                    except (EOFError, KeyboardInterrupt):
                        rich_log("⏭️  User cancelled Claude fixes", "bold yellow")
                        self._save_context()
                        return False

        except Exception as e:
            rich_log(f"💥 Error invoking Claude: {e}", "bold red", "error")
            self._save_context()
            return False

        finally:
            # Clean up the command file
            if command_file and os.path.exists(command_file):
                try:
                    os.remove(command_file)
                    rich_log(f"🗑️  Cleaned up command file: {command_file}", "dim")
                except Exception as e:
                    rich_log(
                        f"⚠️  Failed to clean up command file {command_file}: {e}",
                        "bold yellow",
                        "warning",
                    )

    async def continuous_healing(self, watch_interval: int = 300):
        """Enhanced with task context"""
        # Initialize task with SuperClaude
        init_cmd = f"/task:create \"{self.config_type} pipeline monitoring\" --context-persist"
        rich_log(f"📝 Initializing task context: {init_cmd}", "bold blue")
        
        rich_log(
            f"🎯 Starting Claude auto-healing for {self.config_type} pipeline",
            "bold blue",
        )
        rich_log(
            f"📊 Max retries: {self.max_retries}, Retry delay: {self.retry_delay}s",
            "bold blue",
        )

        run_count = 0
        consecutive_failures = 0

        while True:
            run_count += 1
            retry_count = 0

            rich_log(f"\n{'=' * 70}", "bold cyan")
            rich_log(
                f"🏃 {self.config_type.title()} Pipeline Run #{run_count} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "bold cyan",
            )
            rich_log(f"{'=' * 70}", "bold cyan")

            while retry_count < self.max_retries:
                success, stdout, stderr = self.run_pipeline()

                if success:
                    rich_log(
                        f"🎉 {self.config_type.title()} pipeline completed successfully!",
                        "bold green",
                    )
                    rich_log(
                        f"✅ No errors detected - auto-healing complete for {self.config_type}",
                        "bold green",
                    )
                    
                    # On successful run
                    self.context["last_successful_run"] = datetime.now().isoformat()
                    self._save_context()
                    
                    # Complete the task
                    complete_cmd = f"/task:complete {self.task_id}"
                    rich_log(f"📝 Completing task: {complete_cmd}", "bold green")
                    
                    rich_log("🚪 Exiting auto-healing monitoring...", "bold green")
                    return  # Exit the continuous monitoring loop
                else:
                    retry_count += 1
                    consecutive_failures += 1

                    rich_log(
                        f"💔 {self.config_type.title()} pipeline failed (attempt {retry_count}/{self.max_retries})",
                        "bold red",
                        "error",
                    )

                    # Detect and attempt to heal errors with Claude
                    combined_output = stdout + stderr
                    error_type = self.detect_error_type(combined_output)

                    if error_type:
                        rich_log(
                            f"🩺 Invoking Claude to heal {error_type} error...",
                            "bold blue",
                        )
                        error_context = self.extract_error_context(
                            combined_output, error_type
                        )
                        healed = await self.invoke_claude_fix(error_context)

                        if healed:
                            rich_log(
                                "✨ Claude healing attempt completed, retrying pipeline...",
                                "bold green",
                            )
                            # Brief pause for Claude fixes to take effect before retry
                            await asyncio.sleep(5)
                            continue
                        else:
                            rich_log(
                                "🚫 Claude healing failed", "bold yellow", "warning"
                            )
                    else:
                        rich_log(
                            "🤔 Unable to classify error type for Claude healing",
                            "bold yellow",
                            "warning",
                        )

                    if retry_count < self.max_retries:
                        rich_log(
                            f"⏳ Waiting {self.retry_delay}s before retry...",
                            "bold yellow",
                        )
                        await asyncio.sleep(self.retry_delay)

            # Reset consecutive failures if we had any success
            if consecutive_failures > 0 and retry_count < self.max_retries:
                rich_log(
                    f"🔄 Continuing monitoring for {self.config_type} pipeline...",
                    "bold blue",
                )
                consecutive_failures = 0

            # Wait before next run
            rich_log(
                f"⏰ Next {self.config_type} run in {watch_interval}s...", "bold blue"
            )
            await asyncio.sleep(watch_interval)


async def run_all_configs(auto_fix: bool = True):
    """Run auto-healing for all three configurations simultaneously."""
    configs = ["transform", "fb_ads", "reports"]

    rich_log(
        "🚀 Starting Claude auto-healing for all pipeline configurations", "bold blue"
    )

    # Create healers for each config
    healers = [ClaudeAutoHealer(config, auto_fix=auto_fix) for config in configs]

    # Run all healers concurrently
    tasks = [healer.continuous_healing() for healer in healers]

    try:
        await asyncio.gather(*tasks)
    except KeyboardInterrupt:
        rich_log("\n🛑 All pipeline monitoring stopped by user", "bold yellow")


async def main():
    """Main entry point."""
    import argparse

    # Display Rich banner
    if RICH_AVAILABLE and console:
        console.print(
            "╭─────────────────────────────────────────────────────────────────╮",
            style="bold blue",
        )
        console.print(
            "│        🤖 Claude Code Auto-Healing Pipeline Runner 🔧         │",
            style="bold blue",
        )
        console.print(
            "│                                                                 │",
            style="bold blue",
        )
        console.print(
            "│  Automatically detects errors and fixes them with Claude Code  │",
            style="bold cyan",
        )
        console.print(
            "╰─────────────────────────────────────────────────────────────────╯",
            style="bold blue",
        )
        console.print()

    parser = argparse.ArgumentParser(
        description="Claude Code auto-healing pipeline runner"
    )
    parser.add_argument(
        "--config",
        choices=["transform", "fb_ads", "report", "reports", "all", "scrape_pacer"],
        default="transform",
        help="Pipeline configuration to monitor",
    )
    parser.add_argument(
        "--max-retries", type=int, default=5, help="Maximum retry attempts"
    )
    parser.add_argument(
        "--retry-delay", type=int, default=30, help="Delay between retries (seconds)"
    )
    parser.add_argument(
        "--watch-interval",
        type=int,
        default=300,
        help="Interval between runs (seconds)",
    )
    parser.add_argument(
        "--manual",
        action="store_true",
        help="Use manual mode (wait for user input instead of auto-fix)",
    )

    args = parser.parse_args()

    try:
        if args.config == "all":
            await run_all_configs(auto_fix=not args.manual)
        else:
            healer = ClaudeAutoHealer(
                config_type=args.config,
                max_retries=args.max_retries,
                retry_delay=args.retry_delay,
                auto_fix=not args.manual,
            )
            await healer.continuous_healing(watch_interval=args.watch_interval)
            rich_log(
                f"🏁 {args.config.title()} auto-healing completed successfully",
                "bold green",
            )

    except KeyboardInterrupt:
        rich_log(
            f"\n🛑 {args.config.title()} pipeline monitoring stopped by user",
            "bold yellow",
        )
    except Exception as e:
        rich_log(f"💥 Unexpected error: {e}", "bold red", "error")
        sys.exit(1)

    # Clean exit when auto-healing is complete
    rich_log("✨ Auto-healing system exiting cleanly", "bold green")
    sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
