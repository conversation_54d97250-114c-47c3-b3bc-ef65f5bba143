#!/usr/bin/env python3
"""
Debug script to see the actual HTML content of dropdown rows.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Set up DEBUG logging to see HTML content
logging.basicConfig(
    level=logging.DEBUG,  # DEBUG level to see HTML content
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestConfig:
    def __init__(self):
        self.config = {
            'camoufox': {
                'browser': {
                    'headless': False,
                    'timeout': 60000,
                    'viewport': {'width': 1920, 'height': 1080}
                },
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                    'refresh_before_expiry_seconds': 30
                },
                'anti_bot': {
                    'humanize': True,
                    'mouse_curves': True,
                    'typing_variation': True,
                    'disable_ad_blocker_detection': True,
                    'block_resources_for_performance': False
                },
                'search': {
                    'typing_delay': 120,
                    'suggestion_wait': 8000,
                    'capture_wait': 10
                }
            }
        }

async def debug_dropdown_content():
    """Debug the dropdown content to see why Morgan & Morgan isn't found."""
    logger.info("🔍 Debugging dropdown content")
    
    config = TestConfig().config
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )
    
    try:
        # Complete setup
        success = await session_manager.create_new_session()
        if not success:
            return False
        
        setup_success = await session_manager._setup_ad_library_search()
        if not setup_success:
            return False
        
        # Search to get dropdown
        search_success = await session_manager._search_advertiser("Morgan & Morgan")
        if not search_success:
            return False
        
        logger.info("✅ Dropdown should be visible now - examining content")
        
        # Get all dropdown rows 
        dropdown_rows = await session_manager.page.query_selector_all('div[role="listbox"] > div')
        logger.info(f"📊 Found {len(dropdown_rows)} dropdown rows")
        
        for i, row in enumerate(dropdown_rows):
            logger.info(f"=" * 60)
            logger.info(f"🔍 ROW {i+1} ANALYSIS:")
            
            # Get full HTML content
            row_html = await session_manager.page.evaluate('el => el.innerHTML', row)
            logger.info(f"📄 Full HTML: {row_html}")
            
            # Get text content
            row_text = await session_manager.page.evaluate('el => el.textContent', row)
            logger.info(f"📝 Text content: '{row_text.strip()}'")
            
            # Check for heading element
            heading = await row.query_selector('div[role="heading"]')
            if heading:
                heading_text = await session_manager.page.evaluate('el => el.textContent', heading)
                logger.info(f"📋 Heading: '{heading_text.strip()}'")
            else:
                logger.info("❌ No div[role='heading'] found")
                
                # Try alternative selectors
                alternatives = [
                    'span',
                    'div[aria-level]',
                    '[data-text="true"]',
                    'div > div:first-child'
                ]
                
                for selector in alternatives:
                    alt_element = await row.query_selector(selector)
                    if alt_element:
                        alt_text = await session_manager.page.evaluate('el => el.textContent', alt_element)
                        logger.info(f"🔄 Alternative ({selector}): '{alt_text.strip()}'")
            
            # Check if this contains Morgan & Morgan
            if "morgan" in row_text.lower() and "&" in row_text.lower():
                logger.info(f"🎯 THIS ROW CONTAINS MORGAN & MORGAN!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Debug failed: {e}")
        return False
    finally:
        logger.info("🧹 Closing browser")
        await session_manager.cleanup()

async def main():
    logger.info("🚀 Debug Dropdown Content")
    logger.info("This will show the HTML and text content of each dropdown row")
    logger.info("=" * 80)
    
    success = await debug_dropdown_content()
    
    if success:
        logger.info("✅ Debug completed - check logs above for dropdown content")
    else:
        logger.error("❌ Debug failed")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Debug interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Debug failed: {e}")
        sys.exit(1)