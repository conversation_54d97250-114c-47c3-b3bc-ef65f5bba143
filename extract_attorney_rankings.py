#!/usr/bin/env python3
"""
Extract and rank unique attorney names from SQLite database by filing count.
"""
import sqlite3
import json
from collections import Counter
from typing import List, Dict, <PERSON><PERSON>

def extract_attorney_rankings(db_path: str) -> List[Tuple[str, int]]:
    """
    Extract unique attorney names and rank by filing count.
    
    Args:
        db_path: Path to SQLite database
        
    Returns:
        List of (attorney_name, filing_count) tuples, sorted by count descending
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get all attorney records
    cursor.execute("SELECT attorney FROM pacermon_searches WHERE attorney IS NOT NULL")
    attorney_records = cursor.fetchall()
    
    attorney_counter = Counter()
    
    print(f"Processing {len(attorney_records)} attorney records...")
    
    for (attorney_json,) in attorney_records:
        try:
            # Parse JSON attorney data
            attorney_data = json.loads(attorney_json)
            
            # Handle both single attorney and list of attorneys
            if isinstance(attorney_data, list):
                for attorney in attorney_data:
                    if isinstance(attorney, dict) and 'attorney_name' in attorney:
                        name = attorney['attorney_name'].strip()
                        if name:
                            attorney_counter[name] += 1
            elif isinstance(attorney_data, dict) and 'attorney_name' in attorney_data:
                name = attorney_data['attorney_name'].strip()
                if name:
                    attorney_counter[name] += 1
                    
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"Error parsing attorney data: {attorney_json[:100]}... - {e}")
            continue
    
    conn.close()
    
    # Return sorted by count (descending)
    return attorney_counter.most_common()

def main():
    db_path = "/Users/<USER>/PycharmProjects/lexgenius/sqlite/pacermon_cache.db"
    
    print("Extracting attorney rankings from SQLite database...")
    rankings = extract_attorney_rankings(db_path)
    
    print(f"\nFound {len(rankings)} unique attorneys")
    print("\nTop 50 attorneys by filing count:")
    print("=" * 80)
    print(f"{'Rank':<5} {'Filings':<8} {'Attorney Name'}")
    print("=" * 80)
    
    for i, (attorney_name, count) in enumerate(rankings[:50], 1):
        print(f"{i:<5} {count:<8} {attorney_name}")
    
    # Save full results to JSON
    output_file = "/Users/<USER>/PycharmProjects/lexgenius/attorney_rankings.json"
    attorney_data = [
        {
            "rank": i,
            "attorney_name": name,
            "filing_count": count
        }
        for i, (name, count) in enumerate(rankings, 1)
    ]
    
    with open(output_file, 'w') as f:
        json.dump(attorney_data, f, indent=2)
    
    print(f"\nFull rankings saved to: {output_file}")
    print(f"Total unique attorneys: {len(rankings)}")
    
    # Show some statistics
    total_filings = sum(count for _, count in rankings)
    print(f"Total filings: {total_filings}")
    if rankings:
        avg_filings = total_filings / len(rankings)
        print(f"Average filings per attorney: {avg_filings:.2f}")
        print(f"Top attorney: {rankings[0][0]} with {rankings[0][1]} filings")

if __name__ == "__main__":
    main()