# PACER Services Troubleshooting Runbook

## Executive Summary

This runbook provides systematic troubleshooting procedures for the 24 PACER services based on comprehensive Five Whys root cause analysis. It addresses the four critical issue categories identified: browser resource management, configuration cascade failures, download failure complexity, and service orchestration dependencies.

## Quick Reference

### 🚨 Emergency Commands
```bash
# Stop all browser contexts immediately
python scripts/service_health_check.py --json /tmp/health.json
python -c "import sys; sys.path.append('.'); from src.utils.troubleshooting_logger import BrowserContextMonitor; import asyncio; asyncio.run(BrowserContextMonitor(None, {}).force_cleanup_all_contexts())"

# Validate critical configurations
python scripts/config_validator.py --create-fallbacks --verbose

# Check service health
python scripts/service_health_check.py --verbose

# Analyze download failures
python scripts/download_troubleshooter.py --log-dir logs --top 10
```

### 📊 Monitoring Dashboard URLs
- Service Health: `python scripts/service_health_check.py --watch`
- Browser Contexts: Monitor via troubleshooting_logger.py
- Configuration Status: `python scripts/config_validator.py --verbose`

---

## Critical Issue Categories

### 🔴 Category 1: Browser Resource Management

**Root Cause**: Incremental architecture evolution without standardized cleanup patterns

#### Symptoms
- "Target closed" errors in Playwright
- Memory leaks during parallel processing  
- Browser context count exceeding 16 limit
- Temporary directory accumulation

#### Immediate Actions
1. **Check Context Count**
   ```bash
   python -c "
   import sys; sys.path.append('.')
   from src.utils.troubleshooting_logger import create_browser_context_monitor
   import asyncio, logging
   
   async def check():
       monitor = create_browser_context_monitor(logging.getLogger(), {'max_browser_contexts': 16})
       stats = await monitor.get_context_stats()
       print(f'Active contexts: {stats[\"active_contexts\"]}/{stats[\"max_contexts\"]}')
   
   asyncio.run(check())
   "
   ```

2. **Force Cleanup**
   ```bash
   python -c "
   import sys; sys.path.append('.')
   from src.utils.troubleshooting_logger import create_browser_context_monitor
   import asyncio, logging
   
   async def cleanup():
       monitor = create_browser_context_monitor(logging.getLogger(), {})
       await monitor.force_cleanup_all_contexts()
       print('All contexts cleaned')
   
   asyncio.run(cleanup())
   "
   ```

#### Diagnostic Procedures

**Step 1: Context Monitoring**
```bash
# Start continuous monitoring
python -c "
import sys; sys.path.append('.')
from src.utils.troubleshooting_logger import create_browser_context_monitor
import asyncio, logging

async def monitor():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    logger.addHandler(handler)
    
    monitor = create_browser_context_monitor(logger, {
        'max_browser_contexts': 16,
        'cleanup_threshold_minutes': 30,
        'monitoring_interval_seconds': 60
    })
    
    await monitor.start_monitoring()
    
    # Let it run for 5 minutes
    await asyncio.sleep(300)
    await monitor.stop_monitoring()

asyncio.run(monitor())
"
```

**Step 2: Resource Leak Detection**
```bash
# Check for leaked temp directories
find /tmp -name "*playwright*" -type d -user $(whoami) | wc -l
find /tmp -name "*browser*" -type d -user $(whoami) | wc -l

# Check browser processes
ps aux | grep -E "(chromium|chrome|firefox)" | grep -v grep
```

**Step 3: Download Path Validation**
```bash
# Verify download paths are correctly configured
python -c "
import sys; sys.path.append('.')
from pathlib import Path

data_dir = Path('data')
for court_dir in data_dir.glob('*/*/*/[A-Z]*'):
    print(f'Court: {court_dir.name}, Files: {len(list(court_dir.glob(\"*\")))}')
"
```

#### Prevention Measures
1. **Implement Context Tracking**: Add browser context monitoring to all services
2. **Resource Cleanup**: Ensure all services use proper async context managers
3. **Semaphore Limits**: Enforce browser context limits with semaphores
4. **Monitoring Alerts**: Set up alerts when context count exceeds 80% of limit

---

### 🔴 Category 2: Configuration Cascade Failures

**Root Cause**: Consistency-over-resilience design creating brittle failure modes

#### Symptoms
- Services failing to start due to missing config files
- Critical exit on ignore_download.json unavailability
- Path resolution failures
- JSON decode errors causing service shutdown

#### Immediate Actions
1. **Validate All Configs**
   ```bash
   python scripts/config_validator.py --verbose --create-fallbacks
   ```

2. **Check Critical Paths**
   ```bash
   python scripts/config_validator.py --verbose 2>&1 | grep -E "(CRITICAL|ERROR|MISSING)"
   ```

#### Diagnostic Procedures

**Step 1: Configuration Audit**
```bash
# Full configuration validation
python scripts/config_validator.py --verbose --report config_audit.txt

# Check specific critical files
for file in config/ignore_download.json config/paths_config.json config/relevant_defendants.json; do
    echo "=== $file ==="
    if [ -f "$file" ]; then
        echo "✅ EXISTS"
        python -c "import json; json.load(open('$file')); print('✅ VALID JSON')" 2>/dev/null || echo "❌ INVALID JSON"
    else
        echo "❌ MISSING"
    fi
done
```

**Step 2: Dependency Chain Analysis**
```bash
# Check which services depend on which configs
python -c "
configs = {
    'ignore_download.json': ['PacerConfigurationService', 'RelevanceService', 'PacerOrchestratorService'],
    'paths_config.json': ['PacerConfigurationService', 'PacerFileManagementService'],
    'relevant_defendants.json': ['RelevanceService', 'PacerCaseClassificationService']
}

for config, services in configs.items():
    print(f'{config}:')
    for service in services:
        print(f'  - {service}')
"
```

**Step 3: Fallback Mechanism Test**
```bash
# Test fallback loading
python scripts/config_validator.py --create-fallbacks --verbose

# Verify fallbacks work
mv config/ignore_download.json config/ignore_download.json.backup 2>/dev/null || true
python scripts/config_validator.py --verbose | grep -i fallback
mv config/ignore_download.json.backup config/ignore_download.json 2>/dev/null || true
```

#### Prevention Measures
1. **Fallback Configs**: Create default fallback configurations for all critical files
2. **Graceful Degradation**: Implement non-critical configuration loading
3. **Configuration Validation**: Add pre-flight configuration checks
4. **Circuit Breakers**: Implement configuration-specific circuit breakers

---

### 🔴 Category 3: Download Failure Complexity

**Root Cause**: Business requirement conflicts (cost vs completeness) creating dual verification patterns

#### Symptoms
- Failed downloads not being retried properly
- Artifact-only vs JSON verification confusion
- Ignore_download cases being marked as failures
- Complex reprocessing logic errors

#### Immediate Actions
1. **Analyze Recent Failures**
   ```bash
   python scripts/download_troubleshooter.py --log-dir logs --top 20 --verbose
   ```

2. **Check Artifact Status**
   ```bash
   python scripts/download_troubleshooter.py --log-dir logs --csv failures.csv
   # Then inspect failures.csv for artifact_exists vs json_exists patterns
   ```

#### Diagnostic Procedures

**Step 1: Failure Pattern Analysis**
```bash
# Comprehensive failure analysis
python scripts/download_troubleshooter.py --log-dir logs --output failure_analysis.json --verbose

# Category breakdown
python -c "
import json
with open('failure_analysis.json') as f:
    data = json.load(f)
    
categories = data['recovery_plan']['by_category']
for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
    print(f'{category}: {count} failures')
"
```

**Step 2: Verification Pattern Validation**
```bash
# Check artifact vs JSON patterns
python -c "
import sys; sys.path.append('.')
from pathlib import Path

data_dir = Path('data')
cases_checked = 0
artifact_only = 0
json_only = 0
both_exist = 0
neither_exist = 0

for court_dir in data_dir.glob('*/*/*/[A-Z]*'):
    for case_file in court_dir.glob('*.json'):
        if 'docket' in case_file.name.lower():
            cases_checked += 1
            docket_num = case_file.stem
            
            # Check for artifacts
            artifacts = list(court_dir.glob(f'*{docket_num}*.pdf')) + list(court_dir.glob(f'*{docket_num}*.zip'))
            
            if artifacts and case_file.exists():
                both_exist += 1
            elif artifacts:
                artifact_only += 1
            elif case_file.exists():
                json_only += 1
            else:
                neither_exist += 1

print(f'Cases checked: {cases_checked}')
print(f'Both artifact & JSON: {both_exist}')
print(f'Artifact only: {artifact_only}')
print(f'JSON only: {json_only}')
print(f'Neither: {neither_exist}')
"
```

**Step 3: Ignore Download Validation**
```bash
# Check ignore_download effectiveness
python -c "
import json
import sys; sys.path.append('.')

try:
    with open('config/ignore_download.json') as f:
        ignore_config = json.load(f)
    
    total_ignored = 0
    for court, cases in ignore_config.get('courts', {}).items():
        if isinstance(cases, dict):
            total_ignored += len(cases)
        elif isinstance(cases, list):
            total_ignored += len(cases)
    
    total_ignored += len(ignore_config.get('cases', {}))
    print(f'Total cases in ignore_download: {total_ignored}')
    
except Exception as e:
    print(f'Error reading ignore_download config: {e}')
"
```

#### Prevention Measures
1. **Unified Verification**: Implement single verification method that handles both patterns
2. **Clear Separation**: Distinguish between cost-avoidance and failure recovery
3. **Automated Recovery**: Implement automated retry with exponential backoff
4. **Priority Scoring**: Use priority scoring for intelligent retry ordering

---

### 🔴 Category 4: Service Orchestration Dependencies  

**Root Cause**: Performance-over-resilience assumptions mismatched to production realities

#### Symptoms
- Services failing to initialize due to dependency unavailability
- Cascade failures across multiple services
- Deep dependency chains without fallback mechanisms
- Resource initialization errors

#### Immediate Actions
1. **Service Health Check**
   ```bash
   python scripts/service_health_check.py --verbose
   ```

2. **Dependency Validation**
   ```bash
   python scripts/service_health_check.py --json health_status.json --verbose
   ```

#### Diagnostic Procedures

**Step 1: Service Dependency Mapping**
```bash
# Generate dependency map
python -c "
import sys; sys.path.append('.')
from scripts.service_health_check import ServiceHealthMonitor
import asyncio, logging

async def map_dependencies():
    monitor = ServiceHealthMonitor()
    
    print('Service Dependencies:')
    for service, config in monitor.pacer_services.items():
        critical = '🚨' if config.get('critical', False) else '📝'
        print(f'{critical} {service}:')
        for dep in config.get('dependencies', []):
            print(f'    - {dep}')
        print()

asyncio.run(map_dependencies())
"
```

**Step 2: Health Status Analysis**
```bash
# Continuous health monitoring for pattern detection
python scripts/service_health_check.py --watch --interval 30 &
HEALTH_PID=$!

# Let it run for 5 minutes then analyze
sleep 300
kill $HEALTH_PID

echo "Review output above for patterns in service failures"
```

**Step 3: Circuit Breaker Status**
```bash
# Check circuit breaker states
python scripts/service_health_check.py --json current_health.json
python -c "
import json
with open('current_health.json') as f:
    data = json.load(f)

print('Circuit Breaker Status:')
for service, breaker in data.get('circuit_breakers', {}).items():
    if breaker['state'] != 'closed':
        print(f'⚠️ {service}: {breaker[\"state\"]} (failures: {breaker[\"failure_count\"]})')
"
```

#### Prevention Measures
1. **Health Monitoring**: Implement continuous service health monitoring
2. **Circuit Breakers**: Add circuit breakers for external dependencies
3. **Graceful Degradation**: Design services to handle dependency failures
4. **Retry Logic**: Implement intelligent retry with exponential backoff

---

## Common Troubleshooting Scenarios

### Scenario 1: Pipeline Fails to Start

**Symptoms**: Services won't initialize, immediate exit

**Diagnosis Steps**:
1. Check configurations: `python scripts/config_validator.py --verbose`
2. Validate service health: `python scripts/service_health_check.py --verbose`
3. Check logs for initialization errors

**Resolution**:
```bash
# Create missing configs
python scripts/config_validator.py --create-fallbacks

# Restart with health monitoring
python scripts/service_health_check.py --watch &
python src/main.py --params config/scrape.yml
```

### Scenario 2: Browser "Target Closed" Errors

**Symptoms**: Playwright errors, download failures

**Diagnosis Steps**:
1. Check browser context count
2. Monitor resource usage
3. Review download path configuration

**Resolution**:
```bash
# Force cleanup
python -c "
import sys; sys.path.append('.')
from src.utils.troubleshooting_logger import create_browser_context_monitor
import asyncio, logging

asyncio.run(create_browser_context_monitor(logging.getLogger(), {}).force_cleanup_all_contexts())
"

# Restart with monitoring
python src/main.py --params config/scrape.yml --headless
```

### Scenario 3: Download Failures Accumulating

**Symptoms**: Many failed downloads, no retry attempts

**Diagnosis Steps**:
1. Analyze failure patterns: `python scripts/download_troubleshooter.py --log-dir logs`
2. Check ignore_download configuration
3. Validate artifact vs JSON status

**Resolution**:
```bash
# Generate recovery plan
python scripts/download_troubleshooter.py --log-dir logs --output recovery_plan.json

# Execute top priority recoveries
python scripts/download_troubleshooter.py --log-dir logs --top 5 | grep "python src/main.py" | head -3 | bash
```

### Scenario 4: Service Dependencies Failing

**Symptoms**: Import errors, service unavailable

**Diagnosis Steps**:
1. Health check: `python scripts/service_health_check.py --verbose`
2. Check external dependencies
3. Validate service imports

**Resolution**:
```bash
# Check and install missing dependencies
python -c "import playwright, dependency_injector, pydantic, boto3" 2>&1 || echo "Missing dependencies detected"

# Reinstall if needed
mamba install playwright dependency-injector pydantic boto3

# Restart with health monitoring
python scripts/service_health_check.py --watch
```

---

## Production Incident Response

### Severity Levels

**🚨 CRITICAL (P0)**: Service completely down
- Response time: Immediate
- Actions: Emergency commands, immediate escalation

**⚠️ HIGH (P1)**: Major functionality impaired  
- Response time: 15 minutes
- Actions: Diagnostic procedures, targeted fixes

**📝 MEDIUM (P2)**: Minor functionality issues
- Response time: 1 hour
- Actions: Standard troubleshooting, monitoring

**💡 LOW (P3)**: Performance degradation
- Response time: 4 hours
- Actions: Analysis, optimization

### Incident Response Checklist

1. **Immediate Assessment** (0-5 minutes)
   - [ ] Run emergency health check: `python scripts/service_health_check.py --verbose`
   - [ ] Check browser contexts: Count and cleanup if needed
   - [ ] Validate critical configs: `python scripts/config_validator.py --verbose`
   - [ ] Review recent logs for obvious errors

2. **Problem Isolation** (5-15 minutes)
   - [ ] Identify affected services
   - [ ] Determine failure category (browser, config, download, dependency)
   - [ ] Check circuit breaker states
   - [ ] Review monitoring alerts

3. **Initial Response** (15-30 minutes)
   - [ ] Apply appropriate emergency procedures
   - [ ] Implement workarounds if available
   - [ ] Begin detailed diagnostic procedures
   - [ ] Document timeline and actions

4. **Resolution** (30+ minutes)
   - [ ] Execute systematic troubleshooting
   - [ ] Apply permanent fixes
   - [ ] Verify service restoration
   - [ ] Update monitoring and alerts

5. **Post-Incident** (After resolution)
   - [ ] Conduct root cause analysis
   - [ ] Update runbook with lessons learned
   - [ ] Implement prevention measures
   - [ ] Review and improve monitoring

---

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Browser Resource Usage**
   - Active browser context count
   - Memory usage by browser processes
   - Temporary directory accumulation

2. **Configuration Health**
   - Critical config file availability
   - JSON parsing success rate
   - Fallback activation frequency

3. **Download Success Rates**
   - Download failure rate by category
   - Retry success rate
   - Artifact vs JSON completion rates

4. **Service Health**
   - Service initialization success rate
   - Response times
   - Circuit breaker activation

### Alert Thresholds

- **Browser contexts > 13**: Warning
- **Browser contexts > 15**: Critical
- **Configuration errors > 0**: Critical
- **Download failure rate > 20%**: Warning
- **Download failure rate > 50%**: Critical
- **Service health < 90%**: Warning
- **Service health < 70%**: Critical

---

## Tools Quick Reference

### Browser Context Monitor
```bash
# Start monitoring
python -c "from src.utils.troubleshooting_logger import create_browser_context_monitor; ..."

# Get stats
# Force cleanup
```

### Configuration Validator
```bash
# Validate all configs
python scripts/config_validator.py --verbose

# Create fallbacks
python scripts/config_validator.py --create-fallbacks

# Generate report
python scripts/config_validator.py --report config_report.txt
```

### Download Troubleshooter
```bash
# Analyze failures
python scripts/download_troubleshooter.py --log-dir logs --top 20

# Export to CSV
python scripts/download_troubleshooter.py --log-dir logs --csv failures.csv

# Filter by category
python scripts/download_troubleshooter.py --log-dir logs --category network_timeout
```

### Service Health Monitor
```bash
# Single check
python scripts/service_health_check.py --verbose

# Continuous monitoring
python scripts/service_health_check.py --watch --interval 60

# Export health data
python scripts/service_health_check.py --json health_export.json
```

---

## Maintenance Procedures

### Daily Health Checks
1. Run service health check
2. Validate configuration files
3. Review download failure rates
4. Monitor browser context usage

### Weekly Maintenance
1. Analyze download failure trends
2. Update ignore_download configurations
3. Review and cleanup temporary directories
4. Update fallback configurations

### Monthly Reviews
1. Analyze incident patterns
2. Update troubleshooting procedures
3. Review and optimize monitoring thresholds
4. Test disaster recovery procedures

---

## Contact Information

### Escalation Path
1. **L1**: Automated monitoring and basic troubleshooting
2. **L2**: Advanced diagnostic procedures and targeted fixes
3. **L3**: System architecture changes and emergency patches

### Key Personnel
- **Primary On-Call**: [Contact Information]
- **Secondary On-Call**: [Contact Information]
- **System Architect**: [Contact Information]
- **DevOps Lead**: [Contact Information]

---

*This runbook is based on systematic analysis of PACER services using Five Whys methodology and evidence-based troubleshooting. Last updated: [Date]*