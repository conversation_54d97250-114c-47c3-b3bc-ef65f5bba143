#!/usr/bin/env python
"""
Test runner script for LexGenius test suite.

This script provides a convenient way to run different types of tests
with appropriate configurations.
"""
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd):
    """Execute a command and return the result."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=False)
    return result.returncode


def main():
    parser = argparse.ArgumentParser(description="Run LexGenius tests")
    parser.add_argument(
        "type",
        nargs="?",
        default="all",
        choices=["all", "unit", "integration", "e2e", "quick", "workflow", "fb-ads"],
        help="Type of tests to run"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Run with coverage reporting"
    )
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--failfast",
        "-x",
        action="store_true",
        help="Stop on first failure"
    )
    parser.add_argument(
        "--module",
        "-m",
        help="Run tests for a specific module"
    )
    parser.add_argument(
        "--file",
        "-f",
        help="Run tests for a specific file"
    )
    
    args = parser.parse_args()
    
    # Base pytest command
    cmd = ["pytest"]
    
    # Add test type marker or specific path
    if args.type == "unit":
        cmd.extend(["-m", "unit"])
    elif args.type == "integration":
        cmd.extend(["-m", "integration"])
    elif args.type == "e2e":
        cmd.extend(["-m", "e2e"])
    elif args.type == "quick":
        cmd.extend(["-m", "not slow"])
    elif args.type == "workflow":
        cmd.append("tests/unit/fb_ads/test_workflow_service.py")
    elif args.type == "fb-ads":
        cmd.append("tests/unit/fb_ads/")
    
    # Add coverage if requested
    if args.coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=html",
            "--cov-report=term-missing"
        ])
    
    # Add parallel execution
    if args.parallel:
        cmd.extend(["-n", "auto"])
    
    # Add verbose output
    if args.verbose:
        cmd.append("-vv")
    
    # Add failfast
    if args.failfast:
        cmd.append("-x")
    
    # Add specific module if provided
    if args.module:
        cmd.append(f"tests/unit/test_{args.module}.py")
    
    # Add specific file if provided
    if args.file:
        cmd.append(args.file)
    
    # Run the tests
    return run_command(cmd)


if __name__ == "__main__":
    sys.exit(main())