#!/usr/bin/env python3
"""
Litigation Summary Matching & Update System

This script:
1. Scans local DynamoDB FBAdArchive table
2. Matches summaries against established litigation patterns (exact match)
3. For unmatched ads, retrieves image_text from AWS FBImageHash
4. Generates new summaries using DeepSeek AI service
5. Updates both AWS and local DynamoDB with new summaries

Usage:
    python process_litigation_summaries.py [--start_date YYYYMMDD]
"""

import asyncio
import argparse
import sys
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timezone
import json
import os
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv

load_dotenv()

# Rich formatting imports
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint
from rich.markup import escape

# LexGenius imports
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.external.deepseek_client import DeepSeekClient
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.fb_image_hash_repository import FBImageHashRepository
from src.services.ai.deepseek_service import DeepSeekService

# from src.services.ai.prompt_manager import PromptManager  # Skip for now due to import issues
from src.infrastructure.protocols.logger import LoggerProtocol
import logging

console = Console()

# Established litigation patterns - SYNCED with system prompt specifications
ESTABLISHED_LITIGATION_PATTERNS = [
    # Established Litigation (use "Products Liability" per system prompt)
    "Camp Lejeune",
    "Talcum Powder Products Liability",
    "Roundup Products Liability",
    "3M Earplug Products Liability",
    # Wildfire & Environmental
    "Eaton Wildfire",
    "Southern California Edison Wildfire Investigation",
    "PFAS Water Contamination",
    "AFFF Firefighting Foam",
    "Ethylene Oxide Toxic Exposure",
    # Medical Devices & Pharmaceuticals
    "Paragard IUD Products Liability",
    "Ozempic GLP-1 RA Products Liability",
    "Hair Relaxer Products Liability",
    "Depo Provera Products Liability",
    "Suboxone Products Liability",
    "Tepezza Products Liability",
    "NEC Baby Formula Products Liability",
    "Bard PowerPort/AngioDynamics Products Liability",
    "Hernia Mesh Products Liability",
    "Gardasil Products Liability",
    "Zantac Products Liability",
    "Exactech Products Liability",
    "Valsartan Products Liability",
    "Paraquat Products Liability",
    "Oxbryta Products Liability",
    "Makena Products Liability",
    "Spinal Cord Stimulator Products Liability",
    "Bair Hugger Products Liability",
    "Zimmer Biomet Hip Products Liability",
    "Veozah Products Liability",
    "Hologic BioZorb Products Liability",
    "Filshie Clips Products Liability",
    "CooperSurgical IVF Culture Media Products Liability",
    "Cooper Surgical Aneuploid Products Liability",
    "Abiomed Impella CooperSurgical IVF Culture Media Products Liability",
    "Risperdal Products Liability",
    "Prilosec/Nexium Products Liability",
    "Bausch Lomb TASS Investigation",
    "Leqembi Brain Injury Investigation",
    "Kratom Products Liability",
    "Accolade Pacemaker Products Liability",
    "Allergan BIOCELL Products Liability",
    "Hair Dye Cancer Investigation",
    "Fisher Price Swing Products Liability",
    "Pressure Cooker Products Liability",
    "Silicosis Products Liability",
    "Galaxy Gas Products Liability",
    "Dacthal Products Liability",
    "Tabletop Fire Pit Products Liability",
    # Technology & Social Media
    "Social Media Addiction",
    "Video Game Addiction",
    "Roblox Sex Abuse Lawsuit",
    # Data Privacy & Consumer Protection
    "PowerSchool Data Breach Investigation",
    "Data Breach Investigation",
    "Data Privacy Investigation",
    "Online Gambling Addiction Investigation",
    "Etsy Privacy Investigation",
    "Ticket Junk Fee Investigation",
    "Expedia Deceptive Fee Investigation",
    "Experian Consumer Protection Investigation",
    "TCPA Violation Investigation",
    "Barnes Noble Textbook Investigation",
    "Honey Commission Theft Investigation",
    "CrowdStrike Outage Investigation",
    "Cash Sweep Investigation",
    # Sexual Abuse & Institutional
    "Catholic Church Sexual Abuse Investigation",
    "Boy Scouts Sexual Abuse Investigation",
    "Juvenile Detention Abuse Investigation",
    "Uber Sexual Assault Litigation",
    "P Diddy Sex Abuse Investigation",
    "Playboy Sexual Assault Investigation",
    "LDS Sexual Abuse Investigation",
    "Dr Barry Brock Investigation",
    "Dr Derrick Todd Investigation",
    "Dr David Broadbent Investigation",
    "Dr Sanjeev Kumar Investigation",
    # Food & Consumer Products
    "Toxic Baby Products Liability",
    "Ultra Processed Foods Investigation",
    "E Coli Outbreak Investigation",
    "Listeria Outbreak Investigation",
    "In App Dark Pattern Investigation",
    "Chiquita Canyon Landfill Investigation",
    "Bio Lab Explosion Investigation",
    "Asbestos Products Liability",
    "Mohawk Shaw PFAS Investigation",
    "PFAS Fertilizer Investigation",
    # Business & Employment
    "Unpaid Wage Investigation",
    "RealPage Price Fixing Investigation",
    "Multiplan Antitrust Investigation",
    "Syngenta Antitrust Investigation",
    "Insulin Pricing Investigation",
    "Independent Pharmacy Investigation",
    "Antitrust Investigation",
    "Deceptive Marketing Investigation",
    "Overdraft Fee Investigation",
    "Usury Lending Investigation",
    "Atrium Health HER2 Investigation",
]


class LitigationSummaryProcessor:
    """Main processor for litigation summary matching and updating"""

    def __init__(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        test_limit: int = 0,
        workers: int = 50,
        batch_size: int = 200,
        max_retries: int = 3,
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.test_limit = test_limit
        self.workers = workers
        self.batch_size = batch_size
        self.max_retries = max_retries
        self.console = console
        self.litigation_patterns = set(ESTABLISHED_LITIGATION_PATTERNS)

        # Store filter expression for consistent scanning
        self.filter_expression = None

        # Initialize storage and repositories
        self.storage = None
        self.fb_archive_repo = None
        self.fb_image_hash_repo = None
        self.deepseek_service = None

        # Statistics
        self.stats = {
            "total_ads": 0,
            "matched_ads": 0,
            "unmatched_ads": 0,
            "processed_ads": 0,
            "updated_ads": 0,
            "failed_ads": 0,
            "retried_ads": 0,
        }

    async def initialize(self):
        """Initialize all services and repositories"""
        self.console.print("[yellow]Initializing services...[/yellow]")

        # Initialize logger
        logger = logging.getLogger(__name__)

        # Configure for LOCAL DynamoDB
        class LocalConfig:
            def __init__(self):
                self.dynamodb_max_retries = 10
                self.dynamodb_base_delay = 1.0
                self.dynamodb_max_delay = 60.0
                self.dynamodb_endpoint = "http://localhost:8000"
                self.aws_region = "us-west-2"

        storage_config = LocalConfig()
        self.storage = AsyncDynamoDBStorage(storage_config, logger)
        await self.storage.__aenter__()
        self.console.print(
            f"[blue]🔍 Local DynamoDB endpoint: {storage_config.dynamodb_endpoint}[/blue]"
        )

        self.fb_archive_repo = FBArchiveRepository(self.storage, logger)

        # Configure for AWS DynamoDB
        class AWSConfig:
            def __init__(self):
                self.dynamodb_max_retries = 10
                self.dynamodb_base_delay = 1.0
                self.dynamodb_max_delay = 60.0
                self.aws_region = "us-west-2"

        aws_storage_config = AWSConfig()
        aws_storage = AsyncDynamoDBStorage(aws_storage_config, logger)
        await aws_storage.__aenter__()
        self.console.print(
            f"[blue]🔍 AWS DynamoDB region: {aws_storage_config.aws_region} (no endpoint = AWS)[/blue]"
        )

        self.fb_image_hash_repo = FBImageHashRepository(aws_storage, logger)
        self.aws_storage = aws_storage

        # Initialize DeepSeek client
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            raise Exception("DEEPSEEK_API_KEY not found in environment")

        # Create DeepSeek client with OpenRouter API key for fallback
        client_config = {
            "max_retries": 3, 
            "timeout": 60,
            "openrouter_api_key": os.getenv("OPENROUTER_API_KEY")
        }
        self.deepseek_client = DeepSeekClient(
            api_key=api_key, config=client_config, logger=logger
        )

        self.console.print("[green]✓ Services initialized[/green]")

    async def scan_and_analyze(self) -> List[Dict[str, Any]]:
        """Scan FBAdArchive and analyze summary matches"""

        self.console.print(
            f"[cyan]📊 Scanning LOCAL DynamoDB FBAdArchive{' (from ' + self.start_date + ')' if self.start_date else ''}...[/cyan]"
        )

        ads = []
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            scan_task = progress.add_task("Scanning LOCAL DynamoDB...", total=None)

            from boto3.dynamodb.conditions import Attr

            # Base filter: items that do not have 'summary_updated' attribute
            filter_expression = Attr("summary_updated").not_exists()

            if self.start_date:
                filter_expression &= Attr("StartDate").gte(self.start_date)

            if self.end_date:
                filter_expression &= Attr("StartDate").lt(self.end_date)

            self.filter_expression = filter_expression
            ads = await self._scan_with_progress(
                progress, scan_task, filter_expression=self.filter_expression
            )

        self.console.print(
            f"[green]✓ Scanned {len(ads):,} ads from LOCAL DynamoDB[/green]"
        )
        self.stats["total_ads"] = len(ads)

        matched_ads = []
        unmatched_ads = []
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            task = progress.add_task("Analyzing summaries...", total=len(ads))
            for ad in ads:
                summary = ad.get("summary", "").strip()
                if summary and summary in self.litigation_patterns:
                    matched_ads.append(ad)
                    self.stats["matched_ads"] += 1
                else:
                    unmatched_ads.append(ad)
                    self.stats["unmatched_ads"] += 1
                progress.update(task, advance=1)

        self._display_analysis_results()
        return unmatched_ads

    async def _scan_with_progress(self, progress, task, filter_expression=None):
        """Scan DynamoDB using repository's scan_all method"""
        try:
            all_items = await self.fb_archive_repo.scan_all(
                filter_expression=filter_expression
            )
            progress.update(
                task,
                description=f"Scanning LOCAL DynamoDB... ({len(all_items):,} items)",
            )
            return all_items
        except Exception as e:
            self.console.print(f"[red]❌ Error during repository scan: {e}[/red]")
            return []

    def _display_analysis_results(self):
        """Display analysis results in a formatted table"""
        table = Table(title="📈 Analysis Results")
        table.add_column("Metric", style="cyan", no_wrap=True)
        table.add_column("Count", style="magenta")
        table.add_column("Percentage", style="green")

        total = self.stats["total_ads"]
        matched = self.stats["matched_ads"]
        unmatched = self.stats["unmatched_ads"]

        table.add_row("Total Ads", f"{total:,}", "100%")
        table.add_row(
            "Matched",
            f"{matched:,}",
            f"{(matched / total * 100):.1f}%" if total > 0 else "0%",
        )
        table.add_row(
            "Unmatched",
            f"{unmatched:,}",
            f"{(unmatched / total * 100):.1f}%" if total > 0 else "0%",
        )

        self.console.print()
        self.console.print(table)
        self.console.print()

    async def process_unmatched_ads(self, unmatched_ads: List[Dict[str, Any]]):
        """Process unmatched ads in batches to generate new summaries"""
        if not unmatched_ads:
            self.console.print("[green]✓ No unmatched ads to process[/green]")
            return

        if self.test_limit > 0 and len(unmatched_ads) > self.test_limit:
            self.console.print(
                f"[yellow]Testing with first {self.test_limit:,} ads[/yellow]"
            )
            unmatched_ads = unmatched_ads[: self.test_limit]

        total_ads_to_process = len(unmatched_ads)

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            main_task = progress.add_task(
                f"Processing {total_ads_to_process:,} ads...",
                total=total_ads_to_process,
            )

            for i in range(0, total_ads_to_process, self.batch_size):
                batch_ads = unmatched_ads[i : i + self.batch_size]
                batch_num = (i // self.batch_size) + 1
                total_batches = (
                    total_ads_to_process + self.batch_size - 1
                ) // self.batch_size

                self.console.print(
                    f"[cyan]🚀 Processing batch {batch_num}/{total_batches} ({len(batch_ads)} ads)...[/cyan]"
                )

                updates = await self._process_batch_with_workers(
                    batch_ads, progress, main_task
                )

                if updates:
                    await self._update_batch_in_database(updates)

        self.console.print(
            f"[green]✓ Completed processing {total_ads_to_process:,} ads[/green]"
        )

    async def _process_batch_with_workers(
        self, batch_ads: List[Dict[str, Any]], progress, main_task
    ) -> List[Dict[str, Any]]:
        """Process a batch of ads with multiple workers"""
        total_items = len(batch_ads)
        actual_workers = min(self.workers, total_items)
        items_per_worker = max(1, total_items // actual_workers)

        worker_chunks = [
            batch_ads[i : i + items_per_worker]
            for i in range(0, total_items, items_per_worker)
        ]

        tasks = [
            self._process_worker_chunk(chunk, progress, main_task)
            for chunk in worker_chunks
        ]
        results = await asyncio.gather(*tasks)

        return [item for sublist in results for item in sublist]

    async def _process_worker_chunk(
        self, ads: List[Dict[str, Any]], progress, main_task
    ) -> List[Dict[str, Any]]:
        """Process a chunk of ads with a single worker"""
        updates = []
        for ad in ads:
            try:
                update = await self._process_single_ad(ad)
                if update:
                    updates.append(update)
                self.stats["processed_ads"] += 1
            except Exception:
                self.stats["failed_ads"] += 1
            finally:
                progress.update(main_task, advance=1)
        return updates

    async def _process_single_ad(self, ad: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process a single ad to generate new summary"""
        ad_archive_id = ad["ad_archive_id"]
        original_summary = ad.get("summary", "")
        image_text = ""

        try:
            image_records = await self.fb_image_hash_repo.get_images_for_ad(
                ad_archive_id
            )
            if image_records:
                image_text = image_records[0].get("image_text", "")
        except Exception:
            pass

        user_content = f"Title: {ad.get('title', '')}\nBody: {ad.get('body', '')}\nImage Text: {image_text}\nLaw Firm Name: {ad.get('law_firm', 'N/A')}"

        prompt_path = Path("src/config/prompts/fb_ads/ad_summary/system.md")
        system_prompt = (
            prompt_path.read_text()
            if prompt_path.exists()
            else "Generate a 4-7 word summary or 'NA'."
        )

        full_prompt = f"{system_prompt}\n\n{user_content}"
        schema = {
            "type": "object",
            "properties": {
                "summary": {"type": "string", "description": "4-7 word summary or 'NA'"}
            },
            "required": ["summary"],
        }

        response = await self.deepseek_client.extract_structured_data(
            text=full_prompt, schema=schema, temperature=0.0, max_tokens=100
        )

        new_summary = (
            response.get("summary", "NA") if isinstance(response, dict) else "NA"
        )

        log_panel = self._create_summary_log_panel(
            ad_archive_id, original_summary, new_summary
        )
        self.console.print(log_panel)

        if (
            new_summary
            and new_summary.upper() != "NA"
            and new_summary != original_summary
        ):
            return {
                "ad_archive_id": ad["ad_archive_id"],
                "start_date": ad["start_date"],
                "summary": new_summary,
            }
        return None

    def _create_summary_log_panel(
        self, ad_id: str, old_summary: str, new_summary: str
    ) -> Panel:
        """Creates a formatted panel for logging summary processing details."""

        status = ""
        border_style = "dim"

        if new_summary and new_summary.upper() != "NA" and new_summary != old_summary:
            status = "[bold green]UPDATED[/bold green]"
            border_style = "green"
        elif new_summary == old_summary:
            status = "[bold yellow]UNCHANGED[/bold yellow]"
            border_style = "yellow"
        else:
            status = "[bold dim]SKIPPED (NA)[/bold dim]"
            border_style = "dim"

        content = (
            f"[bold]Ad ID[/bold]: {escape(ad_id)}\n"
            f"[bold]Status[/bold]: {status}\n"
            f"  [cyan]Old[/cyan]: {escape(old_summary) or 'N/A'}\n"
            f"  [green]New[/green]: {escape(new_summary) or 'N/A'}"
        )

        return Panel(
            content,
            title="[cyan]Summary Processing[/cyan]",
            border_style=border_style,
            expand=False,
            padding=(1, 2),
        )

    async def _update_batch_in_database(self, updates: List[Dict[str, Any]]):
        """Update a batch of ads in both AWS and local DynamoDB concurrently"""
        if not updates:
            return

        self.console.print(
            f"[cyan]📝 Updating {len(updates)} ads in database...[/cyan]"
        )

        aws_fb_archive_repo = FBArchiveRepository(
            self.aws_storage, logging.getLogger(__name__)
        )

        async def update_item(repo, ad_id, start_date, data):
            # This inner function now returns the result of the update call
            return await repo.update_attributes(ad_id, start_date, data)

        tasks = []
        for update in updates:
            update_data = {
                "summary": update["summary"],
                "summary_updated": datetime.now(timezone.utc).isoformat(),
                "last_updated": datetime.now(timezone.utc).isoformat(),
            }
            # Append both local and AWS update tasks
            tasks.append(
                update_item(
                    self.fb_archive_repo,
                    update["ad_archive_id"],
                    update["start_date"],
                    update_data,
                )
            )
            tasks.append(
                update_item(
                    aws_fb_archive_repo,
                    update["ad_archive_id"],
                    update["start_date"],
                    update_data,
                )
            )

        # Gather results, capturing exceptions
        results = await asyncio.gather(*tasks, return_exceptions=True)

        successful_updates = 0
        failed_items = 0

        # Process results in pairs (local, aws)
        for i in range(0, len(results), 2):
            item_index = i // 2
            ad_id = updates[item_index]["ad_archive_id"]

            local_result = results[i]
            aws_result = results[i + 1]

            is_local_success = (
                not isinstance(local_result, Exception) and local_result is True
            )
            is_aws_success = (
                not isinstance(aws_result, Exception) and aws_result is True
            )

            if is_local_success and is_aws_success:
                successful_updates += 1
            else:
                failed_items += 1
                if not is_local_success:
                    self.console.print(
                        f"[red]❌ Failed to update {ad_id} in LOCAL DB: {local_result}[/red]"
                    )
                if not is_aws_success:
                    self.console.print(
                        f"[red]❌ Failed to update {ad_id} in AWS DB: {aws_result}[/red]"
                    )

        if successful_updates > 0:
            self.stats["updated_ads"] += successful_updates
            self.console.print(
                f"[green]✓ Successfully updated {successful_updates}/{len(updates)} ads in both LOCAL and AWS[/green]"
            )

        if failed_items > 0:
            self.console.print(
                f"[red]❌ Total items with failures: {failed_items}[/red]"
            )

    async def refresh_matched_count(self):
        """Refresh the matched ads count after updates"""
        self.console.print(f"[cyan]🔄 Refreshing matched ads count...[/cyan]")

        ads = await self.fb_archive_repo.scan_all(
            filter_expression=self.filter_expression
        )

        current_matched = sum(
            1 for ad in ads if ad.get("summary", "") in self.litigation_patterns
        )

        old_matched = self.stats["matched_ads"]
        self.stats["matched_ads"] = current_matched

        self.console.print(
            f"[green]✅ Matched count refreshed: {old_matched:,} → {current_matched:,} (+{current_matched - old_matched:,})[/green]"
        )

    def display_final_results(self):
        """Display final processing results"""
        panel = Panel.fit(
            f"""[green]✅ Processing Complete![/green]
            
📊 Final Statistics:
• Total Ads Scanned: {self.stats["total_ads"]:,}
• Matched Ads: {self.stats["matched_ads"]:,}
• Unmatched Ads: {self.stats["unmatched_ads"]:,}
• Processed Ads: {self.stats["processed_ads"]:,}
• Updated Ads: {self.stats["updated_ads"]:,}
• Failed Ads: {self.stats["failed_ads"]:,}
• Retried Ads: {self.stats["retried_ads"]:,}

⚙️ Configuration Used:
• Workers: {self.workers}
• Batch Size: {self.batch_size}
• Max Retries: {self.max_retries}
• Test Limit: {self.test_limit if self.test_limit > 0 else "None"}""",
            title="🎯 Litigation Summary Processing Results",
            border_style="green",
        )
        self.console.print()
        self.console.print(panel)

    async def cleanup(self):
        """Clean up resources"""
        if self.storage:
            await self.storage.__aexit__(None, None, None)
        if self.aws_storage:
            await self.aws_storage.__aexit__(None, None, None)


async def main():
    """Main entry point"""

    parser = argparse.ArgumentParser(description="Process litigation summaries")
    parser.add_argument(
        "--start-date",
        type=str,
        help="Start date filter in YYYYMMDD format (e.g., 20250101)",
    )
    parser.add_argument(
        "--test_limit",
        type=int,
        default=0,
        help="Limit processing to N ads for testing (0 = process all)",
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=50,
        help="Number of parallel workers (default: 50)",
    )
    parser.add_argument(
        "--batch",
        type=int,
        default=200,
        help="Number of ads per processing batch (default: 200)",
    )
    parser.add_argument(
        "--end-date",
        type=str,
        help="End date filter in YYYYMMDD format (exclusive, e.g., 20250201)",
    )
    parser.add_argument(
        "--max_retries",
        type=int,
        default=3,
        help="Maximum retries for failed API calls (default: 3)",
    )

    args = parser.parse_args()

    # Validate start_date format if provided
    if args.start_date:
        try:
            datetime.strptime(args.start_date, "%Y%m%d")
        except ValueError:
            console.print(
                "[red]❌ Invalid start-date format. Use YYYYMMDD (e.g., 20250101)[/red]"
            )
            sys.exit(1)

    if args.end_date:
        try:
            datetime.strptime(args.end_date, "%Y%m%d")
        except ValueError:
            console.print(
                "[red]❌ Invalid end-date format. Use YYYYMMDD (e.g., 20250101)[/red]"
            )
            sys.exit(1)

    # Display header
    console.print()
    console.print(
        Panel.fit(
            "[bold cyan]🏛️  Litigation Summary Matching & Update System[/bold cyan]\n"
            "[dim]Scanning Facebook ads and updating summaries with AI...[/dim]",
            border_style="cyan",
        )
    )
    console.print()

    processor = LitigationSummaryProcessor(
        start_date=args.start_date,
        end_date=args.end_date,
        test_limit=args.test_limit,
        workers=args.workers,
        batch_size=args.batch,
        max_retries=args.max_retries,
    )

    try:
        # Initialize services
        await processor.initialize()

        # Scan and analyze
        unmatched_ads = await processor.scan_and_analyze()

        # Process unmatched ads
        await processor.process_unmatched_ads(unmatched_ads)

        # Refresh matched count to show updated numbers
        await processor.refresh_matched_count()

        # Display final results
        processor.display_final_results()

    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️  Processing interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]❌ Error: {e}[/red]")
        raise
    finally:
        await processor.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
