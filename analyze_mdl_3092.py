#!/usr/bin/env python3
"""
Analyze MDL 3092 (Suboxone) plaintiff counts and transfers.
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any

from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage

# Load environment variables
load_dotenv()

console = Console()


class MdlAnalyzer:
    """Analyze MDL 3092 plaintiff counts and transfers."""
    
    def __init__(self):
        self.storage = None
        self.repository = None
        self.mdl_num = '3092'  # Suboxone MDL number
        
    async def initialize(self):
        """Initialize storage and repository."""
        # Create storage configuration
        class StorageConfig:
            def __init__(self):
                self.use_local = False
                self.local_port = 8000
                self.dynamodb_endpoint = None
                self.aws_region = 'us-west-2'
                self.dynamodb_max_retries = 10
                self.dynamodb_base_delay = 1.0
                self.dynamodb_max_delay = 60.0
        
        config = StorageConfig()
        self.storage = AsyncDynamoDBStorage(config)
        self.repository = PacerRepository(self.storage)
        
    async def get_mdl_filings_30_days(self, end_date_str: str) -> List[Dict[str, Any]]:
        """Get all MDL 3092 filings for the past 30 days."""
        console.print(f"[cyan]Fetching MDL {self.mdl_num} filings for 30 days ending {end_date_str}...[/cyan]")
        
        end_date = datetime.strptime(end_date_str, '%Y%m%d')
        start_date = end_date - timedelta(days=29)  # 30 days including end date
        
        all_filings = []
        current_date = end_date
        
        # Progress indicator
        with console.status("[bold green]Querying DynamoDB...") as status:
            while current_date >= start_date:
                date_str = current_date.strftime('%Y%m%d')
                status.update(f"[bold green]Querying {date_str}...")
                
                # Query by MDL and date
                daily_filings = await self.repository.query_by_mdl_and_date_range(
                    self.mdl_num, date_str, date_str
                )
                
                all_filings.extend(daily_filings)
                current_date -= timedelta(days=1)
        
        console.print(f"[green]Found {len(all_filings)} total MDL {self.mdl_num} filings[/green]")
        return all_filings
    
    def calculate_plaintiff_count(self, filing: Dict[str, Any]) -> int:
        """Calculate plaintiff count for a filing, defaulting to 1."""
        num_plaintiffs = filing.get('num_plaintiffs')
        if num_plaintiffs is None or str(num_plaintiffs) == '0':
            return 1
        try:
            return int(num_plaintiffs)
        except (ValueError, TypeError):
            return 1
    
    async def analyze_mdl(self, date_str: str):
        """Analyze MDL 3092 plaintiff counts."""
        try:
            await self.initialize()
            
            async with self.storage:
                # Header
                console.print(Panel.fit(
                    f"[bold blue]MDL {self.mdl_num} (Suboxone) Analysis[/bold blue]\n"
                    f"[cyan]30-day period ending: {date_str}[/cyan]",
                    border_style="blue"
                ))
                
                # Get all filings
                filings = await self.get_mdl_filings_30_days(date_str)
                
                if not filings:
                    console.print(f"[red]No MDL {self.mdl_num} filings found for the specified period[/red]")
                    return
                
                # Calculate total plaintiffs (raw)
                total_plaintiffs_raw = sum(self.calculate_plaintiff_count(f) for f in filings)
                console.print(f"\n[bold yellow]Total plaintiffs (raw): {total_plaintiffs_raw}[/bold yellow]")
                
                # Check for duplicates by versus
                versus_counts = {}
                for f in filings:
                    versus = f.get('versus', '')
                    if versus and versus != 'N/A':
                        if versus not in versus_counts:
                            versus_counts[versus] = []
                        versus_counts[versus].append(f)
                
                # Show duplicate versus entries
                duplicates = {v: cases for v, cases in versus_counts.items() if len(cases) > 1}
                if duplicates:
                    console.print(f"\n[bold red]Found {len(duplicates)} duplicate 'versus' entries:[/bold red]")
                    
                    dup_table = Table(title="Duplicate Versus Entries", show_header=True, header_style="bold magenta")
                    dup_table.add_column("Versus", style="cyan")
                    dup_table.add_column("Count", style="yellow")
                    dup_table.add_column("Total Plaintiffs", style="red")
                    dup_table.add_column("Filing Dates", style="green")
                    
                    for versus, cases in sorted(duplicates.items(), key=lambda x: len(x[1]), reverse=True)[:10]:
                        total_p = sum(self.calculate_plaintiff_count(c) for c in cases)
                        dates = sorted(set(c.get('filing_date', 'N/A') for c in cases))
                        dup_table.add_row(
                            versus[:50] + "..." if len(versus) > 50 else versus,
                            str(len(cases)),
                            str(total_p),
                            ", ".join(dates[:3]) + ("..." if len(dates) > 3 else "")
                        )
                    
                    console.print(dup_table)
                
                # After deduplication by versus
                unique_versus = {}
                for f in filings:
                    versus = f.get('versus', f.get('docket_num', ''))
                    key = (self.mdl_num, versus)
                    if key not in unique_versus:
                        unique_versus[key] = f
                
                total_after_versus_dedup = sum(self.calculate_plaintiff_count(f) for f in unique_versus.values())
                console.print(f"\n[bold cyan]After versus deduplication: {total_after_versus_dedup} plaintiffs[/bold cyan]")
                
                # Check for transfers
                console.print("\n[bold magenta]Analyzing Transfers...[/bold magenta]")
                
                transferred_cases = [f for f in filings if f.get('is_transferred') or f.get('transferred_in')]
                console.print(f"Found {len(transferred_cases)} transferred cases")
                
                # Create lookup for transfer matching
                case_lookup = {}
                for f in filings:
                    court_id = f.get('court_id')
                    docket_num = f.get('docket_num')
                    if court_id and docket_num:
                        case_lookup[(court_id, docket_num)] = f
                
                # Find transfer matches
                matches_found = 0
                matched_plaintiffs = 0
                
                for f in filings:
                    if f.get('is_transferred') or f.get('transferred_in'):
                        transferor_court = f.get('transferor_court_id')
                        transferor_docket = f.get('transferor_docket_num')
                        
                        if transferor_court and transferor_docket:
                            original = case_lookup.get((transferor_court, transferor_docket))
                            if original and original.get('mdl_num') == self.mdl_num:
                                matches_found += 1
                                matched_plaintiffs += self.calculate_plaintiff_count(original)
                
                console.print(f"[yellow]Transfer matches found: {matches_found}[/yellow]")
                console.print(f"[yellow]Plaintiffs in matched original cases: {matched_plaintiffs}[/yellow]")
                
                # Final calculation
                console.print("\n[bold green]=== SUMMARY ===[/bold green]")
                
                summary_table = Table(show_header=True, header_style="bold cyan")
                summary_table.add_column("Calculation", style="yellow")
                summary_table.add_column("Value", style="green", justify="right")
                
                summary_table.add_row("Total cases", str(len(filings)))
                summary_table.add_row("Total plaintiffs (raw)", str(total_plaintiffs_raw))
                summary_table.add_row("After versus dedup", str(total_after_versus_dedup))
                summary_table.add_row("Transfer matches", str(matches_found))
                summary_table.add_row("Matched plaintiffs to subtract", str(matched_plaintiffs))
                summary_table.add_row(
                    "Final (with transfer dedup)",
                    str(total_after_versus_dedup - matched_plaintiffs)
                )
                
                console.print(summary_table)
                
        except Exception as e:
            console.print(f"[red]Error during analysis: {e}[/red]")
            import traceback
            traceback.print_exc()


async def main():
    """Main entry point."""
    # Get current date or use provided date
    if len(sys.argv) > 1:
        date_str = sys.argv[1]
    else:
        date_str = datetime.now().strftime('%Y%m%d')
    
    analyzer = MdlAnalyzer()
    await analyzer.analyze_mdl(date_str)


if __name__ == "__main__":
    asyncio.run(main())