# Final Migration Checklist - Legacy Code Dependencies (CORRECTED)

## Executive Summary

> [!NOTE]
> This document reflects a snapshot during a major refactoring effort. As of June 2025 and later, the `src/transformer/orchestrator.py` and the rest of the `src/transformer/` directory (like `orchestrator_original.py`) have been **removed**. The transformer functionality is now directly implemented in `src/services/transformer/`. References to the old `src/transformer/` paths in this document are historical.

**NEW Architecture Components (at the time of this document's last major update):**
- `src/pacer/orchestrator.py` (NEW orchestrator)
- `src/transformer/orchestrator.py` (This was the NEW feature-flagged orchestrator, now removed)
- `src/reports/orchestrator.py` (NEW orchestrator)
- Everything in `src/services/*` (This is the current location for transformer logic)

**OLD Architecture (described at the time, parts of which are now removed):**
- Everything else in `src/pacer/*` (except orchestrator.py)
- Everything else in `src/transformer/*` (except orchestrator.py) (This OLD transformer code is now removed)
- Everything else in `src/reports/*` (except orchestrator.py)
- `src/lib/*`, `src/utils/*`, `src/core/*` (Dependencies on these from services are progressively being reduced)

**Total Legacy Dependencies Found: 73** imports from OLD architecture in NEW components

## 🚨 CRITICAL: When All Flags Are Set to Use NEW Services

**The NEW services STILL EXECUTE OLD code!** They are not just imports - the OLD modules are actively instantiated and used:

### Execution Flow:
1. `main.py` → `MainOrchestrator` → `MainServiceFactory`
2. Factory creates NEW orchestrators
3. NEW orchestrators create services that **use OLD implementations**

### Example: PACER Flow with `use_new_pacer_services=true`
```
PacerOrchestratorService (NEW)
  → creates PacerCourtProcessingService (NEW)
    → instantiates & uses PacerNavigator (OLD)
    → instantiates & uses PacerFileManager (OLD)
    → instantiates & uses PacerDocumentDownloader (OLD)
    → instantiates & uses CaseTransferHandler (OLD)
```

The NEW architecture is a **facade pattern** - providing better organization while delegating to OLD implementations.

## Key Findings

### 1. NEW Orchestrators' Dependencies

#### `src/pacer/orchestrator.py` (NEW)
- **Imports from NEW**: ✅ All service imports from `src.services.pacer.*`
- **Imports from OLD**: ✅ NONE - Fully isolated!

#### `src/transformer/orchestrator.py` (NEW)
- **Imports from NEW**: ✅ Service imports from `src.services.transformer.*`
- **Imports from OLD**: ❌ Still imports:
  - `src.transformer.DataTransformer` (OLD module)
  - Mixes OLD and NEW components

#### `src/reports/orchestrator.py` (NEW)
- **Imports from NEW**: ⚠️ Limited - mostly uses local modules
- **Imports from OLD**: ❌ Imports OLD modules from same directory:
  - `src.reports.component_factory`
  - `src.reports.data_loader`
  - `src.reports.processor`
  - `src.reports.renderer`
  - `src.reports.publisher`

### 2. Critical Discovery: Circular Dependencies

**OLD transformer code imports NEW services!**
The OLD `transformer/orchestrator_original.py` imports from `src.services.transformer/*`:
- This creates circular dependencies
- Makes it impossible to cleanly separate architectures
- Some service components are used by BOTH OLD and NEW

### 3. Service Layer Dependencies on OLD Code

The NEW services in `src/services/*` import 73 times from OLD architecture:

#### Most Common OLD Dependencies:
1. **Core HTML Processing** (9 imports from `src.core.html`) ✅ **MIGRATED TO NEW**
2. **PACER OLD modules** (40 imports total):
   - `navigator` (7 imports)
   - `browser_service` (5 imports)
   - `case_relevance_engine` (5 imports)
   - `pacer_document_downloader` (5 imports)
   - `file_manager` (4 imports)
3. **Utilities** (18 imports from `src.utils.*`)
4. **Other OLD code** (6 imports)

## What Actually Executes When All Flags = True

### PACER Workflow (`use_new_pacer_services = true`)
**Executed Path:**
```
MainOrchestrator → ScrapingOrchestrator → PacerOrchestratorService (NEW)
  → PacerCourtProcessingService (NEW) which EXECUTES:
    - PacerNavigator (OLD) - for navigation
    - PacerFileManager (OLD) - for file operations
    - PacerDocumentDownloader (OLD) - for downloads
    - CaseRelevanceEngine (OLD) - for relevance checks
```
**Result:** OLD code is actively executing, not just imported

### Transformer Workflow (`use_new_transformer_services = true`)
**Executed Path:**
```
MainOrchestrator → ProcessingOrchestrator → DataTransformer
  → TransformerOrchestratorService (NEW)
    → RefactoredDataTransformer (NEW) which EXECUTES:
      - HTMLCaseParser (OLD from src.core.html)
      - Various utils from src.utils.*
```
**Result:** Mix of NEW orchestration with OLD core components

### Reports Workflow (always NEW, no flag)
**Executed Path:**
```
MainOrchestrator → ReportsOrchestratorService (NEW)
  → Uses OLD modules from src.reports/:
    - component_factory (OLD)
    - data_loader (OLD)
    - processor (OLD)
    - renderer (OLD)
```
**Result:** NEW orchestrator but OLD implementation modules

## Complete List of OLD Dependencies in NEW Code

### In NEW Orchestrators:

**`src/transformer/orchestrator.py`** imports:
- `src.transformer.DataTransformer` (OLD)

**`src/reports/orchestrator.py`** imports:
- `src.reports.component_factory` (OLD)
- `src.reports.data_loader` (OLD)
- `src.reports.processor` (OLD)
- `src.reports.renderer` (OLD)
- `src.reports.publisher` (OLD)

### In NEW Services (`src/services/*`):

**From `src.core.html` (9 imports):**
- `HTMLCaseParser` → Used by: 
  - `pacer/case_processing_service`
  - `pacer/docket_processing_orchestrator_service`
  - `pacer/html_processing_service`
  - `transformer/file_operations_service`
- `HTMLDataUpdater` → Used by:
  - `pacer/case_processing_service`
  - `pacer/docket_processing_orchestrator_service`
  - `pacer/html_processing_service`
  - `transformer/html_integration_service`
  - `transformer/file_operations_service`

**From OLD `src.pacer.*` modules (40 imports):**
- `navigator.PacerNavigator` → 7 services
- `browser_service.BrowserService` → 5 services
- `case_relevance_engine.CaseRelevanceEngine` → 5 services
- `pacer_document_downloader.PacerDocumentDownloader` → 5 services
- `file_manager.PacerFileManager` → 4 services
- `case_transfer_handler.CaseTransferHandler` → 4 services
- `pacer_utils` functions → 4 services
- `authenticator.PacerAuthenticator` → 3 services
- `docket_processor.DocketProcessor` → 2 services
- `report_handler.ReportHandler` → 1 service

**From `src.utils.*` (18 imports):**
- `docket_utils` functions → 4 services
- `dynamodb_utils.DynamoDBUtils` → 3 services
- `law_firm` functions → 3 services
- `text_processing` functions → 2 services
- `performance_monitor` → 2 services
- Various other utilities → 4 services

**From `src.lib` (1 import):**
- `try_remove` → `transformer_orchestrator_service`

**From `src.fb_ads` (1 import):**
- `VectorClusterer` → `reports/rendering_service`

**From OLD `src.reports` (2 imports):**
- `AdDataFrameProcessor` → `reports/processing_service`
- `AdPageGenerator` → `reports/processing_service`

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                     Infrastructure Layer                         │
│  (src/infrastructure/*, src/repositories/*)                    │
│  Used by: BOTH OLD and NEW ✓                                   │
└────────────────────┬────────────────────┬──────────────────────┘
                     │                    │
        ┌────────────▼──────────┐   ┌────▼────────────────┐
        │    OLD Architecture   │   │   NEW Architecture   │
        │                       │   │                      │
        │  - src/pacer/*       │◄──┤  - src/services/*   │
        │    (except orchest.)  │   │  - pacer/orchest.   │
        │  - src/transformer/* ├───►  - transform/orchest.│
        │    (except orchest.)  │   │  - reports/orchest. │
        │  - src/reports/*     │   │                      │
        │    (except orchest.)  │◄──┘                     │
        │  - src/lib/*         │                          │
        │  - src/utils/*       │                          │
        │  - src/core/*        │                          │
        └───────────────────────┘                         │
                     ▲                                    │
                     └────────────────────────────────────┘
                          73 OLD imports in NEW!
```

## Migration Strategy

### Phase 1: Break Circular Dependencies (URGENT)
1. Stop OLD transformer from importing NEW services
2. Either:
   - Move shared components to infrastructure layer
   - Duplicate components temporarily
   - Complete transformer migration immediately

### Phase 2: Isolate NEW Orchestrators
1. **Transformer**: Remove `DataTransformer` import, use only services
2. **Reports**: Move to service-based architecture instead of local modules

### Phase 3: Eliminate OLD Dependencies in Services
1. Move utilities to service layer
2. Create NEW implementations of:
   - HTML processing
   - PACER navigation
   - Browser management
   - Document downloading
3. Replace all 73 OLD imports

### Phase 4: Complete FB Ads Migration
1. The only domain still importing from `src.lib`
2. Migrate `VectorClusterer` and other components

## Success Metrics

When complete, the following command should return 0 results:
```bash
# Check NEW orchestrators for OLD imports
grep -E "from src\.(pacer|transformer|reports|lib|utils|core)" src/pacer/orchestrator.py src/transformer/orchestrator.py src/reports/orchestrator.py | grep -v "orchestrator\.py"

# Check services for OLD imports  
grep -r "from src\." src/services/ --include="*.py" | grep -v "from src.services" | grep -v "from src.repositories" | grep -v "from src.infrastructure" | grep -v "from src.config_models" | grep -v "from src.models" | grep -v "__pycache__"
```

## Dependencies Breakdown: Used vs Unused

### Actually EXECUTED When All Flags = True (HIGH PRIORITY)

These OLD modules are actively instantiated and their methods called:

**PACER Domain (Executed):**
- `PacerNavigator` - Core navigation functionality
- `PacerFileManager` - File operations
- `PacerDocumentDownloader` - Document downloads
- `CaseRelevanceEngine` - Business logic
- `PacerAuthenticator` - Authentication
- `BrowserService` - Browser automation
- `CaseTransferHandler` - Transfer logic

**Transformer Domain (Executed):**
- `HTMLCaseParser` - Parsing HTML
- `HTMLDataUpdater` - Updating HTML data
- Utils functions - Various utilities

**Reports Domain (Executed):**
- All OLD report modules (since orchestrator uses them directly)

### Imported But NOT Executed When All Flags = True

These are imported by services but not called in the NEW execution path:

**Conditional Imports:**
- `DocketProcessor` - Only used when `use_new_docket_services = false`
- Some util functions that are imported but not called

## Migration Progress

### ✅ Completed Migrations

**HTML Services (src.core.html → src.services.html)**
- **Status**: 100% complete
- **Files Migrated**:
  - `HTMLCaseParser` → `CaseParserService`
  - `HTMLDataUpdater` → `DataUpdaterService`
- **Services Updated**: 10 service files now use NEW HTML services
- **Approach**: Direct migration without compatibility layer
- **Result**: True isolation achieved - NEW services no longer depend on src.core.html

### 🟡 Remaining Migrations

The following OLD modules are still actively used by NEW services and need migration:

**High Priority (Most Used):**
1. `PacerNavigator` (7 imports) - Core navigation
2. `BrowserService` (5 imports) - Browser automation
3. `CaseRelevanceEngine` (5 imports) - Business logic
4. `PacerDocumentDownloader` (5 imports) - Downloads
5. `PacerFileManager` (4 imports) - File operations

**Medium Priority:**
- PACER authentication, database, and utility modules
- Transformer utilities and processing logic
- Report generation components

## Conclusion

When all flags are set to use NEW services:
- **You ARE executing OLD code** - it's not just imported (except for HTML services)
- The NEW architecture is a **wrapper/facade** over OLD implementations
- Approximately **80% of the 73 OLD imports are actively executed** (down from 90% after HTML migration)
- HTML services have achieved true isolation

**This is a pragmatic refactoring approach** that:
1. Provides better structure and organization
2. Enables gradual migration (as demonstrated by HTML services)
3. Maintains stability by reusing proven code
4. Can achieve true isolation with continued migration effort