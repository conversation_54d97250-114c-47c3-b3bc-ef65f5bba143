#!/usr/bin/env python3
"""
Final verification that all 4 AFFF records have correct MDL numbers and normalized names
"""

import asyncio
import logging
import os
import sys

from dotenv import load_dotenv
from rich.console import Console
from rich.table import Table

# Load environment variables
load_dotenv()

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import services
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)
console = Console()

# Updated target records (with normalized AGC name)
TARGET_RECORDS = [
    "Anita Powers v. 3M Company et al.",
    "City Of Salem, Virginia v. AGC Chemicals of America Inc.",  # Updated with normalized name
    "Judith Mason v. 3M Company et al.",
    "Tony <PERSON> v. 3M Company et al."
]

class AFFFVerifier:
    """Verifies AFFF records have correct MDL numbers and normalized names"""
    
    def __init__(self):
        self.console = console
        self.config = {
            'aws_region': os.environ.get('AWS_REGION', 'us-west-2'),
            'aws_access_key': os.environ.get('AWS_ACCESS_KEY_ID'),
            'aws_secret_key': os.environ.get('AWS_SECRET_ACCESS_KEY'),
        }
        self.dynamodb_storage = None
        self.pacer_repo = None
        
    async def initialize_services(self):
        """Initialize DynamoDB services"""
        try:
            # DynamoDB storage
            dynamodb_logger = logging.getLogger('dynamodb_storage')
            self.dynamodb_storage = AsyncDynamoDBStorage(
                config=self.config,
                logger=dynamodb_logger
            )
            await self.dynamodb_storage.__aenter__()
            
            # Pacer repository
            self.pacer_repo = PacerRepository(self.dynamodb_storage)
            
            logger.info("✅ Services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            raise
    
    async def cleanup(self):
        """Clean up resources"""
        if self.dynamodb_storage:
            await self.dynamodb_storage.__aexit__(None, None, None)
    
    async def verify_all_records(self, target_date: str = "20250716"):
        """Verify all 4 target records have correct MDL numbers and normalized names"""
        try:
            # Get all records for the target date
            all_records = await self.pacer_repo.query_by_filing_date(target_date)
            logger.info(f"Found {len(all_records)} total records for {target_date}")
            
            # Find our target records
            found_records = []
            missing_records = []
            
            for target_versus in TARGET_RECORDS:
                found = False
                for record in all_records:
                    record_versus = record.get('versus', '')
                    if record_versus and target_versus.lower() in record_versus.lower():
                        found_records.append({
                            'target': target_versus,
                            'record': record
                        })
                        found = True
                        break
                
                if not found:
                    missing_records.append(target_versus)
            
            # Display results
            console.print(f"\n[bold green]🎉 Final Verification Results[/bold green]")
            
            if found_records:
                console.print(f"\n[green]✅ Found {len(found_records)} target records:[/green]")
                
                table = Table(title="AFFF Records Verification")
                table.add_column("Target", style="cyan")
                table.add_column("Actual Versus", style="yellow")
                table.add_column("MDL Num", style="green")
                table.add_column("Docket Num", style="blue")
                table.add_column("Status", style="bold")
                
                all_correct = True
                for item in found_records:
                    target = item['target']
                    record = item['record']
                    versus = record.get('versus', 'N/A')
                    mdl_num = record.get('mdl_num', 'MISSING')
                    docket_num = record.get('docket_num', 'N/A')
                    
                    # Check status
                    if mdl_num == '2873':
                        status = "✅ CORRECT"
                    else:
                        status = "❌ INCORRECT"
                        all_correct = False
                    
                    table.add_row(target, versus, str(mdl_num), docket_num, status)
                
                console.print(table)
                
                # Summary
                if all_correct:
                    console.print("\n[bold green]🎉 SUCCESS: All records have correct MDL numbers![/bold green]")
                    console.print("✅ All 4 AFFF records now have mdl_num = '2873'")
                    console.print("✅ AGC defendant name has been normalized")
                else:
                    console.print("\n[bold red]❌ Some records still have incorrect MDL numbers[/bold red]")
            
            if missing_records:
                console.print(f"\n[red]❌ Missing records:[/red]")
                for missing in missing_records:
                    console.print(f"  - {missing}")
                    
            return len(found_records) == 4 and len(missing_records) == 0
            
        except Exception as e:
            logger.error(f"Error verifying records: {e}")
            return False


async def main():
    """Main entry point"""
    console.print("[bold cyan]🔍 Final AFFF Records Verification[/bold cyan]")
    console.print("Checking that all 4 records have correct MDL numbers and normalized names")
    
    verifier = AFFFVerifier()
    
    try:
        await verifier.initialize_services()
        success = await verifier.verify_all_records()
        
        if success:
            console.print("\n[bold green]🎊 All AFFF records are now correctly configured![/bold green]")
        else:
            console.print("\n[red]❌ Some issues remain with AFFF records[/red]")
    
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        console.print(f"[bold red]❌ Fatal error: {e}[/bold red]")
    
    finally:
        await verifier.cleanup()


if __name__ == "__main__":
    asyncio.run(main())