import os
import google.auth
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import backoff
import json
import boto3
from typing import Optional, List, Dict, Any
import base64
import urllib.parse
import re
import quopri

class GmailSearch:
    # Add config to the constructor - updated
    def __init__(self, config: Dict, credentials_file: str = 'credentials.json', token_file: str = 'token.json',
                 scopes: Optional[List[str]] = None, use_aws_secrets: bool = False, secret_name: Optional[str] = None):
        self.config = config # Store config
        self.credentials_file = credentials_file
        self.token_file = token_file
        self.SCOPES = scopes if scopes else ['https://www.googleapis.com/auth/gmail.readonly']
        self.use_aws_secrets = use_aws_secrets
        self.secret_name = secret_name
        self.service = self.authenticate_gmail()

    def _load_credentials_from_aws(self) -> Optional[Credentials]:
        """Loads credentials and token from AWS Secrets Manager."""
        # Use config["project_root"] to construct the path to credentials.json
        credentials_path = os.path.join(self.config["project_root"], self.credentials_file)

        client = boto3.client('secretsmanager')
        try:
            response = client.get_secret_value(SecretId=self.secret_name)
            secret_string = response['SecretString']
            secret = json.loads(secret_string)
            credentials_data = json.loads(secret['credentials'])
            token_data = json.loads(secret['token'])

            # Check for BOTH 'installed' and 'web' keys
            if 'installed' in credentials_data:
                credentials_data = credentials_data['installed']
            elif 'web' in credentials_data:
                credentials_data = credentials_data['web']
            else:
                print("Error: Neither 'installed' nor 'web' key found in credentials.")
                return None

            # Build credentials
            creds = Credentials(
                token=token_data.get('token'),
                refresh_token=token_data.get('refresh_token'),
                token_uri=credentials_data['token_uri'],
                client_id=credentials_data['client_id'],
                client_secret=credentials_data['client_secret'],
                scopes=self.SCOPES
            )

            return creds

        except Exception as e:
            print(f"Error loading credentials from AWS Secrets Manager: {e}")
            return None

    def _save_credentials_to_aws(self, creds: Credentials):
        """Saves (or updates) the token in AWS Secrets Manager."""
        client = boto3.client('secretsmanager')

        # Use config["project_root"] to construct the path to credentials.json
        credentials_path = os.path.join(self.config["project_root"], self.credentials_file)

        # Load credentials data
        with open(credentials_path, 'r') as f:
            credentials_data = json.load(f)

        # Prepare secret data
        secret = {
            'credentials': json.dumps(credentials_data),
            'token': creds.to_json()  # Use to_json() for string
        }
        try:
            client.put_secret_value(SecretId=self.secret_name, SecretString=json.dumps(secret))
            print(f"Credentials saved/updated in AWS Secrets Manager: {self.secret_name}")
        except Exception as e:
            print(f"Error saving credentials to AWS Secrets Manager: {e}")

    def authenticate_gmail(self):
        creds = None

        # Use config["project_root"] to construct the path to token.json
        token_path = os.path.join(self.config["project_root"], self.token_file)
        # Use config["project_root"] to construct the path to credentials.json
        credentials_path = os.path.join(self.config["project_root"], self.credentials_file)


        # Load credentials
        if self.use_aws_secrets:
            creds = self._load_credentials_from_aws()
        elif os.path.exists(token_path): # Use token_path
            creds = Credentials.from_authorized_user_file(token_path, self.SCOPES) # Use token_path

        # Refresh or create credentials
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    if self.use_aws_secrets:
                        self._save_credentials_to_aws(creds)
                except Exception as e:
                    print(f"Failed to refresh token: {e}")
                    creds = None  # Set to None if refresh fails

            if self.use_aws_secrets and not creds:
                print("Cannot authenticate with AWS. Initial local authentication needed.")
                return None

            if not creds:
                # Prevent initial auth in AWS (must run locally first)
                if self.use_aws_secrets and "AWS_EXECUTION_ENV" in os.environ:
                    print("Initial authentication MUST be done locally.")
                    return None

                # Use credentials_path
                flow = InstalledAppFlow.from_client_secrets_file(credentials_path, self.SCOPES)
                creds = flow.run_local_server(port=0)

                # Save credentials
                if self.use_aws_secrets:
                    self._save_credentials_to_aws(creds)
                else:
                    # Use token_path
                    with open(token_path, 'w') as token_file_:
                        token_file_.write(creds.to_json())

        return build('gmail', 'v1', credentials=creds)

    @backoff.on_exception(backoff.expo, HttpError, max_tries=5)
    def _execute_api_request(self, request):
        """Executes a Google API request with backoff."""
        return request.execute()

    def search_messages(self, query):
        request = self.service.users().messages().list(userId='me', q=query)
        result = self._execute_api_request(request)
        messages = result.get('messages', [])
        while 'nextPageToken' in result:
            page_token = result['nextPageToken']
            request = self.service.users().messages().list(userId='me', q=query, pageToken=page_token)
            result = self._execute_api_request(request)
            messages.extend(result.get('messages', []))
        return messages

    def search_alias_messages(self, alias_prefix):
        """Searches for messages sent to a specific alias prefix."""
        query = f'to:{alias_prefix}*'
        return self.search_messages(query)

    def get_message_details(self, message_id):
        """Retrieves full details for a single message."""
        request = self.service.users().messages().get(userId='me', id=message_id, format='full')
        return self._execute_api_request(request)

    def get_message_details_batch(self, message_ids):
        """Retrieves message details in batch (more efficient)."""
        batch = self.service.new_batch_http_request()
        results = {}

        def message_callback(request_id, response, exception):
            if exception:
                print(f"Error (batch {request_id}): {exception}")
            else:
                results[request_id] = response

        for msg_id in message_ids:
            batch.add(self.service.users().messages().get(userId='me', id=msg_id, format='full'),
                      callback=message_callback)

        self._execute_api_request(batch)
        return results

    def get_last_n_messages(self, n=10):
        """Retrieves details for the last N messages."""
        result = self.service.users().messages().list(userId='me', maxResults=n).execute()
        if 'messages' not in result:
            return {}
        message_ids = [m['id'] for m in result['messages']]
        return self.get_message_details_batch(message_ids)  # Batch details

    def decode_google_oauth_error(self, url_string):
        """
        Decodes a Google OAuth error URL, handling nested URL and Base64 encoding.

        Args:
            url_string: The full Google OAuth error URL.

        Returns:
            A dictionary containing the decoded error information, or None if
            decoding fails.
        """
        try:
            # 1. Parse the URL to extract the query parameters
            parsed_url = urllib.parse.urlparse(url_string)
            query_params = urllib.parse.parse_qs(parsed_url.query)

            # 2. Get the 'authError' parameter
            auth_error_encoded = query_params.get('authError')
            if not auth_error_encoded:
                return "No authError parameter"  # Or raise exception
            auth_error_encoded = auth_error_encoded[0]

            # 3. Remove spaces:
            auth_error_encoded = auth_error_encoded.replace(" ", "")

            # 4. Fix Base64 Padding:
            missing_padding = len(auth_error_encoded) % 4
            if missing_padding:
                auth_error_encoded += '=' * (4 - missing_padding)

            # 5. Decode the Base64 string
            auth_error_decoded_bytes = base64.b64decode(auth_error_encoded)
            auth_error_decoded_str = auth_error_decoded_bytes.decode('utf-8')

            # 6. URL-decode the *decoded* string
            final_decoded_string = urllib.parse.unquote(auth_error_decoded_str)

            print(final_decoded_string)

            # 7. Extract error parts (Corrected Regex):
            match = re.match(r"(?P<error_code>[^ ]+)(?:\s+(?P<error_message>.+))?", final_decoded_string)

            if not match:
                return {'error_code': None, 'error_message': final_decoded_string,
                        'redirect_uri': None}  # return all unparsed data

            error_code = match.group("error_code")
            error_message = match.group("error_message")

            # Extract redirect_uri if it exists within error_message
            redirect_uri_match = re.search(r"redirect_uri=(?P<redirect_uri>[^ ]+)", final_decoded_string)
            redirect_uri = redirect_uri_match.group("redirect_uri") if redirect_uri_match else None

            return {
                'error_code': error_code,
                'error_message': error_message,
                'redirect_uri': redirect_uri
            }

        except Exception as e:
            print(f"Decoding failed: {e}")  # Better error logging
            return f"Decoding failed: {e}"  # or raise the exception

    def get_email_body(self, message_details: Dict) -> str:
        """Gets the full email body from message details, handling multiple parts."""

        parts = message_details.get('payload', {}).get('parts', [])
        decoded_parts = []

        if parts:  # Multipart message
            for part in parts:
                mime_type = part.get('mimeType')
                if mime_type == 'text/plain' or mime_type == 'text/html':
                    decoded_parts.append(self.decode_email_part(part))
                elif mime_type.startswith('multipart'):
                    # Handle nested multipart
                    nested_parts = part.get('parts', [])
                    for nested_part in nested_parts:
                        if nested_part.get('mimeType') in ['text/plain', 'text/html']:
                            decoded_parts.append(self.decode_email_part(nested_part))

        else:  # Single-part message
            mime_type = message_details.get('payload', {}).get('mimeType')
            if mime_type in ['text/plain', 'text/html']:
                decoded_parts.append(self.decode_email_part(message_details.get('payload', {})))

        return "\n".join(decoded_parts)


    def decode_email_part(self, part: Dict) -> str: # KEPT THIS ONE - ROBUST VERSION
        """Decodes a single part of an email message robustly.

        Handles base64 and quoted-printable encodings, and falls back to
        various encodings with error replacement if UTF-8 fails.
        """
        data = part.get('body', {}).get('../src/data', '')
        if not data:
            return ""

        decoded_data = None

        # 1. Base64 Decoding (First Attempt)
        try:
            decoded_data_bytes = base64.urlsafe_b64decode(data)
        except ValueError:  # Not base64 encoded
            decoded_data_bytes = data.encode('latin-1') # Treat as raw bytes if not base64, use latin-1 to minimize initial decode errors
        else:
            print("Base64 decoded successfully.")


        # 2. Encoding Loop with Error Replacement
        decoded_string = None
        encodings_to_try = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1'] # Added iso-8859-1, a broader latin set

        for encoding in encodings_to_try:
            try:
                if decoded_data_bytes: # Ensure we have bytes to decode
                    decoded_string = decoded_data_bytes.decode(encoding, errors='replace') # Use 'replace'
                    print(f"Email part decoded successfully with {encoding} (errors replaced if any).")
                    return decoded_string # Return immediately on success
                else:
                    return "<No Data to Decode>" # Should not happen, but for safety
            except UnicodeDecodeError:
                print(f"Decoding with {encoding} failed, trying next encoding.")
                continue # Try next encoding

        if decoded_string: # Should be unlikely to reach here, but as a fallback
            return decoded_string
        else:
            print("All decoding attempts failed for email part. Returning raw (possibly undecoded) data.")
            return "<Decoding Error - Raw Data Returned>" # Indicate decoding failure

    def get_last_n_messages_debug(self, n=10): # Modified debug function for RAW format
        """Retrieves details for the last N messages using RAW format and debugs decoding."""
        result = self.service.users().messages().list(userId='me', maxResults=n).execute()
        if 'messages' not in result:
            return {}
        message_ids = [m['id'] for m in result['messages']]
        # No batching for raw format in this debug version for simplicity
        for msg_id in message_ids:
            print(f"\n--- Processing Message ID: {msg_id} (RAW format) ---")
            try:
                request = self.service.users().messages().get(userId='me', id=msg_id, format='raw') # Request RAW format
                raw_message = self._execute_api_request(request)
                raw_data_b64 = raw_message.get('raw') # Get the 'raw' field (base64 encoded)

                if raw_data_b64:
                    try:
                        raw_data_bytes = base64.urlsafe_b64decode(raw_data_b64) # Decode base64url
                        decoded_body = self.decode_raw_email_bytes(raw_data_bytes) # New function to decode raw bytes
                        print(f"Message ID {msg_id}: RAW body decoded successfully with utf-8 (errors replaced if any).") # Indicate utf-8 success

                        print(f"\n--- Message ID: {msg_id} - Decoded Body (truncated) ---") # Header for body output
                        truncated_body = decoded_body[:500] + "...\n[Body truncated for display]" if len(decoded_body) > 500 else decoded_body # Truncate for console
                        print(truncated_body)
                        print("-" * 40) # Separator

                    except UnicodeDecodeError as e:
                        print(f"*** ERROR DECODING Message ID {msg_id} (RAW): {e} ***")
                        error_position = 24 # From the error message
                        start_pos = max(0, error_position - 10) # Context around error
                        end_pos = min(len(raw_data_bytes), error_position + 20)
                        problematic_bytes_context = raw_data_bytes[start_pos:end_pos]
                        print("  Bytes around error position (hex): ", problematic_bytes_context.hex()) # HEX output!
                else:
                    print(f"  Error: No 'raw' data found for message ID {msg_id}")

            except HttpError as e:
                print(f"  HTTP Error fetching message {msg_id} (RAW format): {e}")


    def decode_raw_email_bytes(self, raw_bytes: bytes) -> str:
        """Decodes raw email bytes, trying various encodings."""
        decoded_string = None
        encodings_to_try = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings_to_try:
            try:
                decoded_string = raw_bytes.decode(encoding, errors='replace')
                print(f"  RAW email decoded successfully with {encoding} (errors replaced if any).") # Indent this line
                return decoded_string
            except UnicodeDecodeError:
                print(f"  RAW decode with {encoding} failed, trying next.") # Indent this line
                continue

        if decoded_string:
            return decoded_string
        else:
            print("All RAW decoding attempts failed. Returning undecoded string.")
            return "<RAW Decoding Error>"


if __name__ == '__main__':
    my_scopes = ['https://www.googleapis.com/auth/gmail.readonly']

    # --- Configuration ---
    use_aws = True  # Set to True for AWS, False for local
    secret_name = 'GmailCredentialsSecret'  # AWS secret name

    # --- Initial Local Authentication (Run ONCE locally) ---
    if not use_aws and not os.path.exists('token.json'):
        print("Running initial local authentication...")
        gmail_search = GmailSearch(scopes=my_scopes, use_aws_secrets=True, secret_name=secret_name)
        print("Initial local authentication and AWS secret upload complete.")
        exit()  # Stop after initial setup

    # --- Main Execution (Local or AWS) ---
    gmail_search = GmailSearch(scopes=my_scopes, use_aws_secrets=use_aws, secret_name=secret_name)

    if gmail_search.service is None:
        print("Authentication failed. Exiting.")
        exit()

    # --- Debugging Last 10 Emails (using new RAW debug function) ---
    print("\nRetrieving and Debugging Last 10 Emails (RAW Format):")
    gmail_search.get_last_n_messages_debug(10) # Use RAW debug version

    # --- Example of decoding error URL (commented out for debugging) ---
    # print("\n----------Testing Oauth Error Decoding-----------")
    # test_url = "https://accounts.google.com/signin/oauth/error/v2?authError=ChVyZWRpcmVjdF91cmlfbWlzbWF0Y2gSsAEKWW91IGNhbid0IHNpZ24gaW4gdG8gdGhpcyBhcHAgYmVjYXVzZSBpdCBkb2Vzbid0IGNvbXBseSB3aXRoIEdvb2dsZSdzIE9BdXRoIDIuMCBwb2xpY3kuCgpJZiB5b3UncmUgdGhlIGFwcCBkZXZlbG9wZXIsIHJlZ2lzdGVyIHRoZSByZWRpcmVjdCBVUkkgaW4gdGhlIEdvb2dsZSBDbG91ZCBDb25zb2xlLgogIBptaHR0cHM6Ly9kZXZlbG9wZXJzLmdvb2dsZS5jb20vaWRlbnRpdHkvcHJvdG9jb2xzL29hdXRoMi93ZWItc2VydmVyI2F1dGhvcml6YXRpb24tZXJyb3JzLXJlZGlyZWN0LXVyaS1taXNtYXRjaCCQAyonCgxyZWRpcmVjdF91cmkSF2h0dHA6Ly9sb2NhbGhvc3Q6NjE0OTcvMqQCCAESsAEKWW91IGNhbid0IHNpZ24gaW4gdG8gdGhpcyBhcHAgYmVjYXVzZSBpdCBkb2Vzbid0IGNvbXBseSB3aXRoIEdvb2dsZSdzIE9BdXRoIDIuMCBwb2xpY3kuCgpJZiB5b3UncmUgdGhlIGFwcCBkZXZlbG9wZXIsIHJlZ2lzdGVyIHRoZSByZWRpcmVjd
