#!/usr/bin/env python3
"""
Emergency Cleanup Script for Test Law Firm Data
Removes all "Test Law Firm" entries from production database that were created on 20250701
"""

import asyncio
import os
import sys
import logging
from typing import List, Dict, Any, Set
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
import boto3
from botocore.exceptions import ClientError

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.law_firms_repository import LawFirmsRepository

# Try to import LocalImageQueue, handle if not available
try:
    from src.services.fb_ads.local_image_queue import LocalImageQueue
except ImportError:
    LocalImageQueue = None

class TestFirmCleanup:
    """Emergency cleanup service for Test Law Firm data pollution"""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.console = Console()
        
        # Cleanup targets (must be set before setup_logging)
        self.target_last_updated = '20250701'
        self.target_law_firm = 'Test Law Firm'
        
        self.setup_logging()
        
        # Initialize storage and repositories
        self.storage = None
        self.fb_archive_repo = None
        self.law_firms_repo = None
        self.s3_client = None
        self.image_queue = None
        
        # Data to be cleaned
        self.test_ads: List[Dict[str, Any]] = []
        self.test_page_ids: Set[str] = set()
        self.s3_keys_to_delete: List[str] = []
        self.image_queue_items: List[Dict[str, Any]] = []
        
    def setup_logging(self):
        """Setup logging for cleanup operations"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'emergency_cleanup_{self.target_last_updated}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    async def initialize_services(self):
        """Initialize all required services"""
        self.console.print("[bold blue]🔧 Initializing cleanup services...[/bold blue]")
        
        try:
            # Initialize DynamoDB storage
            config = {
                'aws_region': 'us-west-2',
                'project_root': os.path.dirname(__file__)
            }
            
            self.storage = AsyncDynamoDBStorage(config, self.logger)
            await self.storage.__aenter__()
            
            # Initialize repositories
            self.fb_archive_repo = FBArchiveRepository(self.storage, self.logger)
            self.law_firms_repo = LawFirmsRepository(self.storage, self.logger)
            
            # Initialize S3 client
            self.s3_client = boto3.client('s3', region_name='us-west-2')
            
            # Initialize image queue (skip if not available)
            try:
                queue_dir = './data/image_queue'
                if LocalImageQueue and os.path.exists(f'{queue_dir}/image_queue.db'):
                    # LocalImageQueue constructor: (logger, config, data_dir)
                    self.image_queue = LocalImageQueue(
                        logger=self.logger,
                        config={},
                        data_dir=queue_dir
                    )
                else:
                    self.console.print("[yellow]ℹ️  Image queue database not found, will skip image queue cleanup[/yellow]")
            except Exception as e:
                self.console.print(f"[yellow]⚠️  Could not initialize image queue: {e}[/yellow]")
                self.image_queue = None
            
            self.console.print("[green]✅ All services initialized successfully[/green]")
            
        except Exception as e:
            self.console.print(f"[red]❌ Failed to initialize services: {e}[/red]")
            raise
            
    async def find_test_ads(self):
        """Find all Test Law Firm ads from the target date"""
        self.console.print(f"[yellow]🔍 Searching for Test Law Firm ads from {self.target_last_updated}...[/yellow]")
        
        try:
            # Query FBAdArchive by LastUpdated index, then filter by LawFirm
            from boto3.dynamodb.conditions import Attr
            
            # Query by LastUpdated index (much more efficient than scan)
            all_ads_from_date = await self.fb_archive_repo.query_by_last_updated(self.target_last_updated)
            
            # Filter results for Test Law Firm
            self.test_ads = [ad for ad in all_ads_from_date if ad.get('law_firm') == self.target_law_firm]
            
            # Extract unique page IDs and S3 keys
            for ad in self.test_ads:
                if ad.get('page_id'):
                    self.test_page_ids.add(ad['page_id'])
                if ad.get('s3_image_key'):
                    self.s3_keys_to_delete.append(ad['s3_image_key'])
                    
            self.console.print(f"[green]📊 Found {len(self.test_ads)} test ads with {len(self.test_page_ids)} unique page IDs[/green]")
            
            if self.test_ads:
                # Display sample data
                table = Table(title="Sample Test Law Firm Data Found")
                table.add_column("Ad Archive ID", style="cyan")
                table.add_column("Page ID", style="magenta")
                table.add_column("Creative ID", style="green")
                table.add_column("S3 Key", style="yellow")
                
                for ad in self.test_ads[:5]:  # Show first 5
                    table.add_row(
                        str(ad.get('ad_archive_id', 'N/A')),
                        str(ad.get('page_id', 'N/A')),
                        str(ad.get('ad_creative_id', 'N/A')),
                        str(ad.get('s3_image_key', 'N/A'))[:50] + '...' if ad.get('s3_image_key') else 'N/A'
                    )
                    
                self.console.print(table)
                
        except Exception as e:
            self.console.print(f"[red]❌ Error finding test ads: {e}[/red]")
            raise
            
    async def check_law_firms_table(self):
        """Check if any of the page IDs exist in LawFirms table"""
        self.console.print("[yellow]🔍 Checking LawFirms table for test page IDs...[/yellow]")
        
        found_firms = []
        for page_id in self.test_page_ids:
            try:
                firms = await self.law_firms_repo.get_by_id(page_id)
                if firms:
                    found_firms.extend(firms)
            except Exception as e:
                self.logger.warning(f"Error checking page ID {page_id}: {e}")
                
        if found_firms:
            self.console.print(f"[red]⚠️  Found {len(found_firms)} law firm entries with test page IDs[/red]")
            for firm in found_firms:
                self.console.print(f"  - ID: {firm.get('id')}, Name: {firm.get('name')}")
        else:
            self.console.print("[green]✅ No law firm entries found with test page IDs[/green]")
            
        return found_firms
        
    async def find_image_queue_items(self):
        """Find image queue items for test law firms"""
        if not self.image_queue:
            self.console.print("[yellow]⚠️  Image queue database not found, skipping...[/yellow]")
            return
            
        self.console.print("[yellow]🔍 Searching image queue for test law firm items...[/yellow]")
        
        try:
            # Query the SQLite database directly for items with test law firm names
            queue_items = []
            with self.image_queue._get_connection() as conn:
                cursor = conn.execute("""
                    SELECT image_hash, ad_archive_id, start_date, s3_path, 
                           creative_id, law_firm_name, scrape_date, status
                    FROM image_queue 
                    WHERE law_firm_name = ?
                """, (self.target_law_firm,))
                
                queue_items = [dict(row) for row in cursor]
                    
            self.image_queue_items = queue_items
            self.console.print(f"[green]📊 Found {len(queue_items)} image queue items for test firms[/green]")
            
        except Exception as e:
            self.console.print(f"[red]❌ Error searching image queue: {e}[/red]")
            self.logger.error(f"Image queue search error: {e}")
            
    async def check_s3_objects(self):
        """Check which S3 objects exist for deletion"""
        if not self.s3_keys_to_delete:
            self.console.print("[yellow]ℹ️  No S3 keys to check[/yellow]")
            return
            
        self.console.print(f"[yellow]🔍 Checking {len(self.s3_keys_to_delete)} S3 objects...[/yellow]")
        
        bucket_name = 'lexgenius-dockets'
        existing_keys = []
        
        # For faster execution, just assume all keys exist and skip individual checking
        # The delete operation will handle non-existent keys gracefully
        self.console.print("[yellow]⚡ Skipping individual S3 object verification for faster execution[/yellow]")
        existing_keys = self.s3_keys_to_delete.copy()
                
        self.s3_keys_to_delete = existing_keys
        self.console.print(f"[green]📊 Will attempt to delete {len(existing_keys)} S3 objects[/green]")
        
    async def display_cleanup_summary(self):
        """Display summary of what will be cleaned up"""
        self.console.print("\n[bold red]🧹 CLEANUP SUMMARY[/bold red]")
        
        summary_table = Table(title="Items to be Deleted")
        summary_table.add_column("Resource", style="cyan")
        summary_table.add_column("Count", style="bold magenta")
        summary_table.add_column("Details", style="yellow")
        
        summary_table.add_row("FBAdArchive Records", str(len(self.test_ads)), f"LastUpdated={self.target_last_updated}, LawFirm={self.target_law_firm}")
        summary_table.add_row("Unique Page IDs", str(len(self.test_page_ids)), "Test law firm page identifiers")
        summary_table.add_row("S3 Objects", str(len(self.s3_keys_to_delete)), "Ad images in lexgenius-dockets bucket")
        summary_table.add_row("Image Queue Items", str(len(self.image_queue_items)), "Pending image processing tasks")
        
        self.console.print(summary_table)
        
        if self.dry_run:
            self.console.print("\n[bold yellow]📋 DRY RUN MODE - No actual deletions will be performed[/bold yellow]")
        else:
            self.console.print("\n[bold red]⚠️  LIVE MODE - Actual deletions will be performed![/bold red]")
            
    async def execute_cleanup(self):
        """Execute the actual cleanup operations"""
        if self.dry_run:
            self.console.print("[yellow]📋 DRY RUN - Skipping actual deletions[/yellow]")
            return
            
        self.console.print("[bold red]🚨 EXECUTING CLEANUP OPERATIONS[/bold red]")
        
        # 1. Delete from FBAdArchive
        await self._delete_fb_archive_records()
        
        # 2. Clean image queue
        await self._clean_image_queue()
        
        # 3. Delete S3 objects
        await self._delete_s3_objects()
        
        self.console.print("[bold green]✅ Cleanup completed successfully![/bold green]")
        
    async def _delete_fb_archive_records(self):
        """Delete FBAdArchive records"""
        if not self.test_ads:
            return
            
        self.console.print(f"[red]🗑️  Deleting {len(self.test_ads)} FBAdArchive records...[/red]")
        
        # Prepare keys for batch deletion
        keys_to_delete = []
        for ad in self.test_ads:
            if ad.get('ad_archive_id') and ad.get('start_date'):
                keys_to_delete.append({
                    'AdArchiveID': ad['ad_archive_id'],
                    'StartDate': ad['start_date']
                })
                
        if keys_to_delete:
            deleted_count = await self.fb_archive_repo.batch_delete_records(keys_to_delete)
            self.console.print(f"[green]✅ Deleted {deleted_count} FBAdArchive records[/green]")
        else:
            self.console.print("[yellow]⚠️  No valid keys found for FBAdArchive deletion[/yellow]")
            
    async def _clean_image_queue(self):
        """Clean image queue items"""
        if not self.image_queue_items or not self.image_queue:
            return
            
        self.console.print(f"[red]🗑️  Cleaning {len(self.image_queue_items)} image queue items...[/red]")
        
        try:
            # Delete items directly from SQLite database using image_hash as the primary key
            with self.image_queue._get_connection() as conn:
                for item in self.image_queue_items:
                    conn.execute("DELETE FROM image_queue WHERE image_hash = ?", (item['image_hash'],))
                    
            self.console.print(f"[green]✅ Cleaned {len(self.image_queue_items)} image queue items[/green]")
        except Exception as e:
            self.console.print(f"[red]❌ Error cleaning image queue: {e}[/red]")
            
    async def _delete_s3_objects(self):
        """Delete S3 objects"""
        if not self.s3_keys_to_delete:
            return
            
        self.console.print(f"[red]🗑️  Deleting {len(self.s3_keys_to_delete)} S3 objects...[/red]")
        
        bucket_name = 'lexgenius-dockets'
        deleted_count = 0
        
        # Delete in batches of 1000 (S3 limit)
        for i in range(0, len(self.s3_keys_to_delete), 1000):
            batch = self.s3_keys_to_delete[i:i + 1000]
            delete_objects = [{'Key': key} for key in batch]
            
            try:
                response = self.s3_client.delete_objects(
                    Bucket=bucket_name,
                    Delete={'Objects': delete_objects}
                )
                deleted_count += len(response.get('Deleted', []))
            except Exception as e:
                self.console.print(f"[red]❌ Error deleting S3 batch: {e}[/red]")
                
        self.console.print(f"[green]✅ Deleted {deleted_count} S3 objects[/green]")
        
    async def cleanup(self):
        """Cleanup resources"""
        if self.storage:
            await self.storage.__aexit__(None, None, None)
            
async def main():
    """Main cleanup function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Emergency cleanup of Test Law Firm data')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Preview deletions without executing (default: True)')
    parser.add_argument('--execute', action='store_true',
                       help='Actually execute deletions (overrides --dry-run)')
    parser.add_argument('--force', action='store_true',
                       help='Skip confirmation prompt (use with --execute)')
    
    args = parser.parse_args()
    
    # If --execute is specified, turn off dry run
    dry_run = args.dry_run and not args.execute
    
    cleanup_service = TestFirmCleanup(dry_run=dry_run)
    
    try:
        await cleanup_service.initialize_services()
        await cleanup_service.find_test_ads()
        
        if not cleanup_service.test_ads:
            cleanup_service.console.print("[green]✅ No Test Law Firm data found to clean up![/green]")
            return
            
        await cleanup_service.check_law_firms_table()
        await cleanup_service.find_image_queue_items()
        
        # Skip S3 checking in dry-run mode for faster results
        if not dry_run:
            await cleanup_service.check_s3_objects()
        else:
            cleanup_service.console.print("[yellow]ℹ️  Skipping S3 object verification in dry-run mode[/yellow]")
            
        await cleanup_service.display_cleanup_summary()
        
        if not dry_run and not args.force:
            # Confirm before executing
            try:
                response = input("\nAre you sure you want to execute these deletions? (type 'YES' to confirm): ")
                if response.strip().upper() != 'YES':
                    cleanup_service.console.print(f"[yellow]❌ Cleanup cancelled by user (received: '{response}')[/yellow]")
                    return
            except EOFError:
                cleanup_service.console.print("[red]❌ Cannot read confirmation input. Use --force to skip confirmation.[/red]")
                return
                
        await cleanup_service.execute_cleanup()
        
    except Exception as e:
        cleanup_service.console.print(f"[red]💥 Cleanup failed: {e}[/red]")
        raise
    finally:
        await cleanup_service.cleanup()

if __name__ == "__main__":
    asyncio.run(main())