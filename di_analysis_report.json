{"summary": {"total_services": 97, "services_with_initialize": 15, "services_using_di": 8, "services_with_manual": 18, "total_di_registrations": 133}, "di_registrations": {"pacer": 28, "reports": 19, "ai": 15, "storage": 18, "orchestration": 11, "shared": 7, "transformer": 18, "fb_ads": 17}, "services_by_directory": {"uploader": [{"name": "UploadService", "file": "src/services/uploader/upload_service.py", "uses_di": false, "manual_deps": []}], "transformer": [{"name": "MDLDescriptionManager", "file": "src/services/transformer/mdl_description_manager.py", "uses_di": false, "manual_deps": []}, {"name": "LitigationClassifier", "file": "src/services/transformer/litigation_classifier.py", "uses_di": false, "manual_deps": []}, {"name": "MDLProcessor", "file": "src/services/transformer/mdl_processor.py", "uses_di": false, "manual_deps": []}, {"name": "DocketHTMLProcessor", "file": "src/services/transformer/docket_html_processor.py", "uses_di": false, "manual_deps": []}, {"name": "CachedPdfData", "file": "src/services/transformer/cached_pdf_data.py", "uses_di": false, "manual_deps": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "file": "src/services/transformer/file_handler.py", "uses_di": false, "manual_deps": []}, {"name": "DataTransformerConfig", "file": "src/services/transformer/config.py", "uses_di": false, "manual_deps": []}, {"name": "DataProcessingEngine", "file": "src/services/transformer/data_processing_engine.py", "uses_di": false, "manual_deps": []}, {"name": "DocketProcessor", "file": "src/services/transformer/docket_processor.py", "uses_di": false, "manual_deps": []}, {"name": "TransformerHTMLIntegrationService", "file": "src/services/transformer/html_integration_service.py", "uses_di": false, "manual_deps": ["DataUpdaterService", "PacerHTMLProcessingService"]}, {"name": "DocketValidator", "file": "src/services/transformer/docket_validator.py", "uses_di": false, "manual_deps": []}, {"name": "TransferHandler", "file": "src/services/transformer/transfer_handler.py", "uses_di": false, "manual_deps": []}, {"name": "Uploader", "file": "src/services/transformer/uploader.py", "uses_di": false, "manual_deps": []}, {"name": "ComponentFactory", "file": "src/services/transformer/component_factory.py", "uses_di": false, "manual_deps": ["MistralService"]}, {"name": "AfffCalculator", "file": "src/services/transformer/afff_calculator.py", "uses_di": false, "manual_deps": []}, {"name": "LawFirmIntegration", "file": "src/services/transformer/law_firm_integration.py", "uses_di": false, "manual_deps": []}, {"name": "DataTransformer", "file": "src/services/transformer/data_transformer.py", "uses_di": false, "manual_deps": ["DataUpdaterService", "TransformerHTMLIntegrationService"]}, {"name": "LawFirmProcessor", "file": "src/services/transformer/law_firm_processor.py", "uses_di": false, "manual_deps": []}, {"name": "DocketTextHandler", "file": "src/services/transformer/docket_text_handler.py", "uses_di": false, "manual_deps": []}, {"name": "MDLDataProcessor", "file": "src/services/transformer/mdl_data_processor.py", "uses_di": false, "manual_deps": []}, {"name": "SpecializedWorkflows", "file": "src/services/transformer/specialized_workflows.py", "uses_di": false, "manual_deps": []}, {"name": "DocketFileManager", "file": "src/services/transformer/docket_file_manager.py", "uses_di": false, "manual_deps": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "src/services/transformer/error_handler.py", "uses_di": false, "manual_deps": []}, {"name": "CourtDataProcessor", "file": "src/services/transformer/court_data_processor.py", "uses_di": false, "manual_deps": []}, {"name": "MDLProcessor", "file": "src/services/transformer/mdl_processor_original.py", "uses_di": false, "manual_deps": []}, {"name": "MDLLookupManager", "file": "src/services/transformer/mdl_lookup_manager.py", "uses_di": false, "manual_deps": []}, {"name": "FileOperationsManager", "file": "src/services/transformer/file_operations.py", "uses_di": false, "manual_deps": []}, {"name": "DocketDataCleaner", "file": "src/services/transformer/docket_data_cleaner.py", "uses_di": false, "manual_deps": []}, {"name": "MDLPersistenceManager", "file": "src/services/transformer/mdl_persistence_manager.py", "uses_di": false, "manual_deps": []}, {"name": "DocketLLMEngine", "file": "src/services/transformer/docket_llm_engine.py", "uses_di": false, "manual_deps": []}], "ai": [{"name": "BatchProcessor", "file": "src/services/ai/batch_processor.py", "uses_di": true, "manual_deps": []}, {"name": "AIServiceFactory", "file": "src/services/ai/ai_service_factory.py", "uses_di": false, "manual_deps": []}, {"name": "MistralService", "file": "src/services/ai/mistral_service.py", "uses_di": true, "manual_deps": []}, {"name": "AIOrchestrator", "file": "src/services/ai/ai_orchestrator.py", "uses_di": false, "manual_deps": []}, {"name": "DeepSeekService", "file": "src/services/ai/deepseek_service.py", "uses_di": true, "manual_deps": []}], "orchestration": [{"name": "MainOrchestrator", "file": "src/services/orchestration/main_orchestrator.py", "uses_di": false, "manual_deps": []}, {"name": "ProcessingOrchestrator", "file": "src/services/orchestration/processing_orchestrator.py", "uses_di": false, "manual_deps": ["DeepSeekService"]}, {"name": "FbAdsOrchestrator", "file": "src/services/orchestration/fb_ads_orchestrator.py", "uses_di": false, "manual_deps": []}, {"name": "ScrapingOrchestrator", "file": "src/services/orchestration/scraping_orchestrator.py", "uses_di": false, "manual_deps": []}, {"name": "UploadOrchestrator", "file": "src/services/orchestration/upload_orchestrator.py", "uses_di": false, "manual_deps": []}], "monitoring": [{"name": "PerformanceMonitoringService", "file": "src/services/monitoring/performance_monitoring_service.py", "uses_di": false, "manual_deps": []}], "infrastructure": [{"name": "ResourceCleanupService", "file": "src/services/infrastructure/resource_cleanup_service.py", "uses_di": false, "manual_deps": []}, {"name": "PerformanceMonitorService", "file": "src/services/infrastructure/performance_monitor_service.py", "uses_di": false, "manual_deps": []}], "fb_ads": [{"name": "BandwidthLogger", "file": "src/services/fb_ads/bandwidth_logger.py", "uses_di": false, "manual_deps": []}, {"name": "FBImageHashService", "file": "src/services/fb_ads/image_utils.py", "uses_di": false, "manual_deps": []}, {"name": "LegalAdAnalyzer", "file": "src/services/fb_ads/classifier.py", "uses_di": false, "manual_deps": []}, {"name": "InteractiveService", "file": "src/services/fb_ads/interactive_service.py", "uses_di": false, "manual_deps": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "file": "src/services/fb_ads/disk_cache.py", "uses_di": false, "manual_deps": []}, {"name": "ProcessingTracker", "file": "src/services/fb_ads/processing_tracker.py", "uses_di": false, "manual_deps": []}, {"name": "FacebookSessionManager", "file": "src/services/fb_ads/session_manager.py", "uses_di": false, "manual_deps": []}, {"name": "DataValidationService", "file": "src/services/fb_ads/data_validation_service.py", "uses_di": false, "manual_deps": []}, {"name": "AdNerProcessor", "file": "src/services/fb_ads/ad_ner_processor.py", "uses_di": false, "manual_deps": []}, {"name": "AdProcessor", "file": "src/services/fb_ads/processor.py", "uses_di": false, "manual_deps": []}, {"name": "AdDBService", "file": "src/services/fb_ads/ad_db_service.py", "uses_di": false, "manual_deps": []}, {"name": "ErrorHandlingService", "file": "src/services/fb_ads/error_handling_service.py", "uses_di": false, "manual_deps": []}, {"name": "WorkflowService", "file": "src/services/fb_ads/workflow_service.py", "uses_di": false, "manual_deps": []}, {"name": "LocalImageQueue", "file": "src/services/fb_ads/local_image_queue.py", "uses_di": false, "manual_deps": []}, {"name": "ImageHandler", "file": "src/services/fb_ads/image_handler.py", "uses_di": false, "manual_deps": []}, {"name": "FacebookAdsOrchestrator", "file": "src/services/fb_ads/orchestrator.py", "uses_di": false, "manual_deps": ["AdProcessingService", "DataValidationService", "ErrorHandlingService", "InteractiveService", "ConcurrentWorkflowService", "WorkflowService", "JobRunnerService", "JobOrchestrationService", "AdDBService"]}, {"name": "NerRuleAnalyzer", "file": "src/services/fb_ads/ner_rule_analyzer.py", "uses_di": false, "manual_deps": []}, {"name": "FacebookAPIClient", "file": "src/services/fb_ads/api_client.py", "uses_di": false, "manual_deps": []}, {"name": "AdProcessingService", "file": "src/services/fb_ads/ad_processing_service.py", "uses_di": false, "manual_deps": []}, {"name": "FBAdCategorizer", "file": "src/services/fb_ads/categorizer.py", "uses_di": false, "manual_deps": []}, {"name": "FBAdsLogger", "file": "src/services/fb_ads/logging_setup.py", "uses_di": false, "manual_deps": []}], "pacer": [{"name": "PacerAuthenticationService", "file": "src/services/pacer/authentication_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerCourtProcessingService", "file": "src/services/pacer/court_processing_service.py", "uses_di": false, "manual_deps": ["PacerAuthenticationService", "PacerNavigationService", "PacerFileManagementService", "PacerIgnoreDownloadService", "PacerDocketProcessingOrchestratorService", "ReportService", "PacerRowProcessingService", "RelevanceService", "RelevanceService", "PacerRowProcessingService"]}, {"name": "PacerHTMLProcessingService", "file": "src/services/pacer/html_processing_service.py", "uses_di": false, "manual_deps": ["CaseParserService"]}, {"name": "PacerInteractiveService", "file": "src/services/pacer/interactive_service.py", "uses_di": false, "manual_deps": ["PacerExportService"]}, {"name": "PacerNavigationService", "file": "src/services/pacer/navigation_service.py", "uses_di": false, "manual_deps": ["PacerIgnoreDownloadService", "PacerFileManagementService", "CaseParserService"]}, {"name": "RelevanceService", "file": "src/services/pacer/relevance_service.py", "uses_di": false, "manual_deps": ["PacerIgnoreDownloadService"]}, {"name": "PacerDocketProcessingOrchestratorService", "file": "src/services/pacer/docket_processing_orchestrator_service.py", "uses_di": false, "manual_deps": ["PacerNavigationService", "PacerConfigurationService", "PacerFileOperationsService", "PacerCaseClassificationService", "PacerHTMLProcessingService", "PacerCaseProcessingService", "RelevanceService", "PacerCaseVerificationService", "PacerDownloadOrchestrationService", "PacerIgnoreDownloadService"]}, {"name": "PacerConfigurationService", "file": "src/services/pacer/configuration_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerRowProcessingService", "file": "src/services/pacer/row_processing_service.py", "uses_di": false, "manual_deps": ["PacerCaseProcessingService", "PacerFileOperationsService", "PacerConfigurationService", "RelevanceService", "PacerDownloadOrchestrationService", "PacerIgnoreDownloadService", "PacerFileOperationsService", "PacerCaseClassificationService", "RelevanceService"]}, {"name": "PacerTransferService", "file": "src/services/pacer/transfer_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerCaseProcessingService", "file": "src/services/pacer/case_processing_service.py", "uses_di": false, "manual_deps": ["CaseParserService", "CaseParserService"]}, {"name": "ReportService", "file": "src/services/pacer/report_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerExportService", "file": "src/services/pacer/export_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerFileManagementService", "file": "src/services/pacer/file_management_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerIgnoreDownloadService", "file": "src/services/pacer/ignore_download_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerQueryService", "file": "src/services/pacer/query_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerCaseClassificationService", "file": "src/services/pacer/case_classification_service.py", "uses_di": false, "manual_deps": ["CaseParserService"]}, {"name": "PacerDownloadOrchestrationService", "file": "src/services/pacer/download_orchestration_service.py", "uses_di": false, "manual_deps": ["PacerIgnoreDownloadService"]}, {"name": "PacerServiceFactory", "file": "src/services/pacer/service_factory.py", "uses_di": false, "manual_deps": ["PacerHTMLProcessingService", "PacerCaseClassificationService", "PacerCaseProcessingService", "DataUpdaterService", "PacerIgnoreDownloadService", "ReportService"]}, {"name": "PacerCaseVerificationService", "file": "src/services/pacer/case_verification_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerOrchestratorService", "file": "src/services/pacer/pacer_orchestrator_service.py", "uses_di": false, "manual_deps": ["PacerConfigurationService", "PacerAuthenticationService", "PacerCourtProcessingService", "PacerFileManagementService", "PacerIgnoreDownloadService", "DeepSeekService", "BrowserService", "BrowserService", "BrowserService", "BrowserService", "BrowserService", "RelevanceService", "PacerCaseVerificationService", "BrowserService", "BrowserService", "BrowserService", "BrowserService", "BrowserService"]}, {"name": "PacerAnalyticsService", "file": "src/services/pacer/analytics_service.py", "uses_di": false, "manual_deps": []}], "browser": [{"name": "BrowserService", "file": "src/services/pacer/browser/browser_service.py", "uses_di": false, "manual_deps": []}, {"name": "PacerNavigator", "file": "src/services/pacer/browser/navigator.py", "uses_di": false, "manual_deps": []}], "reports": [{"name": "ReportsOrchestratorService", "file": "src/services/reports/reports_orchestrator_service.py", "uses_di": false, "manual_deps": ["ReportsConfigService", "AdDataFrameProcessorService", "ReportsDataLoaderService", "ReportsProcessingService", "ReportsRenderingService", "S3UploadService", "ReportsPublishingService", "AdPageGeneratorService"]}, {"name": "ReportsRenderingService", "file": "src/services/reports/rendering_service.py", "uses_di": false, "manual_deps": []}, {"name": "ReportsPublishingService", "file": "src/services/reports/publishing_service.py", "uses_di": true, "manual_deps": []}, {"name": "AdDataFrameProcessorService", "file": "src/services/reports/ad_df_processor_service.py", "uses_di": true, "manual_deps": []}, {"name": "ReportsConfigService", "file": "src/services/reports/config_service.py", "uses_di": false, "manual_deps": []}, {"name": "ReportsProcessingService", "file": "src/services/reports/processing_service.py", "uses_di": true, "manual_deps": []}, {"name": "AdPageGeneratorService", "file": "src/services/reports/ad_page_generator_service.py", "uses_di": true, "manual_deps": []}, {"name": "ReportsDataLoaderService", "file": "src/services/reports/data_loader_service.py", "uses_di": true, "manual_deps": []}]}}