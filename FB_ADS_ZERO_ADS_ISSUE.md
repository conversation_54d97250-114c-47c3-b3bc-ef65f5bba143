# Facebook Ads Zero Ads Issue Investigation

## Problem
Some page IDs show zero ads when they should have valid ads. The script is working on some items but not others.

## Investigation Results

### Test Page: Morgan & Morgan
- Initial Page ID tested: `344397646746` (incorrect)
- Correct Page ID: `313942829424` (found via search)

### Findings

1. **Session Creation Works**
   - Successfully creates session with Facebook
   - Obtains valid authentication tokens:
     - `fb_dtsg`: Successfully extracted
     - `lsd`: Successfully extracted  
     - `jazoest`: Successfully generated
   - Uses proxy correctly (mobile proxy via Oxylabs)

2. **GraphQL Search API Works**
   - `/api/graphql/` endpoint works fine
   - Successfully searches for companies
   - Returns proper results with page IDs

3. **Ads Fetch API Fails**
   - `/ads/library/async/search_ads/` endpoint fails
   - Returns `null` payload with authentication error
   - Error: "ServerRedirect to login detected - session authentication failed"

### Root Cause Analysis

The issue appears to be incomplete session authentication:

1. **Missing Cookies**: After session creation, we only have:
   - `datr` cookie ✓
   - Missing: `c_user`, `xs`, `sb`, `fr` cookies that indicate a logged-in session

2. **Authentication Level**: 
   - The current session is "anonymous" (not logged in)
   - GraphQL search works for anonymous users
   - But ads fetch requires authenticated session

3. **API Requirements**:
   - Facebook's ads library has different authentication requirements for different endpoints
   - Search functionality is public
   - Detailed ads data requires authentication

### Why Some Pages Work

The script might work for some pages if:
1. They have cached data from previous authenticated sessions
2. They're using a different API endpoint that doesn't require authentication
3. The session manager sometimes gets a more complete cookie set

### Solution Options

1. **Use Camoufox Browser Automation**
   - The codebase has `CamoufoxSessionManager` which uses real browser automation
   - This can handle JavaScript challenges and get proper authenticated sessions
   - Already configured in the codebase but needs setup

2. **Login-Based Session**
   - Implement actual Facebook login to get authenticated session
   - Would provide `c_user`, `xs`, and other auth cookies
   - More complex but more reliable

3. **Use Different API Approach**
   - Some ads might be accessible through GraphQL queries
   - Investigate alternative endpoints that work with anonymous sessions

### Immediate Workaround

For debugging and testing, the best approach is:
1. Enable Camoufox session manager (already in codebase)
2. This uses Playwright with anti-detection measures
3. Can handle JavaScript challenges and get proper sessions

### Code Reference

The issue is in the session creation process:
- `src/services/fb_ads/session_manager.py`: Only gets basic cookies
- `src/services/fb_ads/camoufox/camoufox_session_manager.py`: Full browser automation

To enable Camoufox in the config:
```yaml
feature_flags:
  use_camoufox: true
```