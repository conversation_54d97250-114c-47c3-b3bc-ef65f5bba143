#!/usr/bin/env python3
"""
Quick test to see all network responses when clicking Morgan & Morgan.
"""

import asyncio
import sys
import os
import logging

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestConfig:
    def __init__(self):
        self.config = {
            'camoufox': {
                'browser': {
                    'headless': False,
                    'timeout': 60000,
                    'viewport': {'width': 1920, 'height': 1080}
                },
                'session': {
                    'min_duration_minutes': 3,
                    'max_duration_minutes': 5,
                    'refresh_before_expiry_seconds': 30
                },
                'anti_bot': {
                    'humanize': True,
                    'mouse_curves': True,
                    'typing_variation': True,
                    'disable_ad_blocker_detection': True,
                    'block_resources_for_performance': False
                },
                'search': {
                    'typing_delay': 120,
                    'suggestion_wait': 8000,
                    'capture_wait': 10
                }
            }
        }

async def test_network_responses():
    """Test to see all network responses during Morgan & Morgan selection."""
    logger.info("🔍 Starting network response analysis")
    
    config = TestConfig().config
    session_manager = CamoufoxSessionManager(
        config=config,
        logger=logger,
        fingerprint_manager=None,
        proxy_manager=None
    )
    
    try:
        # Step 1: Create session
        logger.info("🚀 Step 1: Creating browser session")
        success = await session_manager.create_new_session()
        if not success:
            logger.error("❌ Failed to create session")
            return False
        
        # Step 2: Setup Ad Library  
        logger.info("🚀 Step 2: Setting up Ad Library")
        setup_success = await session_manager._setup_ad_library_search()
        if not setup_success:
            logger.error("❌ Failed to setup Ad Library")
            return False
            
        # Step 3: Search for advertiser
        logger.info("🚀 Step 3: Searching for Morgan & Morgan")
        search_success = await session_manager._search_advertiser("Morgan & Morgan")
        if not search_success:
            logger.error("❌ Failed to search for advertiser")
            return False
        
        # Step 4: Setup GraphQL capture BEFORE clicking
        logger.info("🚀 Step 4: Setting up GraphQL interception")
        await session_manager._setup_graphql_interception()
        logger.info("📡 GraphQL interception active - will show ALL network responses")
        
        # Step 5: Click Morgan & Morgan (this should trigger responses)
        logger.info("🚀 Step 5: Clicking Morgan & Morgan - WATCH FOR RESPONSES")
        logger.info("=" * 80)
        
        selection_success = await session_manager._select_advertiser_from_suggestions("Morgan & Morgan")
        
        logger.info("=" * 80)
        logger.info("🚀 Step 6: Waiting for additional responses")
        await asyncio.sleep(10)  # Extra wait to catch any delayed responses
        
        # Step 7: Check captured responses
        captured = session_manager.get_captured_responses()
        logger.info(f"📊 FINAL RESULT: Captured {len(captured)} GraphQL responses")
        
        if captured:
            logger.info("✅ SUCCESS: GraphQL capture is working!")
            for i, response in enumerate(captured):
                logger.info(f"Response {i+1}: {len(response)} characters")
        else:
            logger.warning("⚠️ No GraphQL responses captured - check the network logs above")
        
        return selection_success
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        logger.info("🧹 Closing browser")
        await session_manager.cleanup()

async def main():
    logger.info("🚀 Quick GraphQL Network Test")
    logger.info("This will show ALL network responses to debug GraphQL capture")
    logger.info("=" * 80)
    
    success = await test_network_responses()
    
    if success:
        logger.info("✅ Test completed successfully")
    else:
        logger.error("❌ Test failed")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        sys.exit(1)