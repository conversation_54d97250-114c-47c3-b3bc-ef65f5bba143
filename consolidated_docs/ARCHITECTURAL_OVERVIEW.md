# LexGenius Architectural Overview

## System Architecture

LexGenius is built on a modern service-oriented architecture with the following key principles:

- **Asynchronous Processing** - All I/O operations use async/await patterns
- **Dependency Injection** - Container-based DI for testability and maintainability
- **Service Isolation** - Each service has a single responsibility
- **Resource Management** - Proper lifecycle management and cleanup
- **Parallel Processing** - Configurable concurrent execution

## Core Components

### 1. Data Collection Layer

#### PACER Integration
- **Browser Automation**: Playwright-based automation for court document access
- **Session Management**: Handles authentication and maintains sessions
- **Download Orchestration**: Manages parallel document downloads
- **Context Isolation**: Each court gets isolated browser context

#### Facebook Ads Collection
- **API Integration**: GraphQL and REST API clients
- **Session Management**: Camoufox browser for anti-detection
- **Image Processing**: Download and deduplication pipeline
- **Rate Limiting**: Respects API limits and implements retries

### 2. Processing Layer

#### Document Processing
- **PDF Extraction**: Text extraction from court documents
- **HTML Parsing**: Structured data extraction from dockets
- **OCR Pipeline**: Mistral-based OCR for scanned documents
- **Entity Recognition**: NER for attorneys, law firms, parties

#### Data Transformation
- **Law Firm Normalization**: Standardizes firm names across sources
- **Case Classification**: Determines litigation type and relevance
- **MDL Association**: Links cases to Multi-District Litigation
- **Data Validation**: Ensures data quality and consistency

#### Ad Processing
- **Categorization**: Hybrid rule-based and ML classification
- **Entity Extraction**: Identifies companies, products, injuries
- **Image Analysis**: PHash-based deduplication
- **Campaign Tracking**: Links ads to law firm campaigns

### 3. Storage Layer

#### DynamoDB Tables
- **FBAdArchive**: Facebook ad data with temporal indexing
- **Pacer**: Court case data with multiple GSIs
- **LawFirms**: Law firm registry and metadata
- **FBImageHash**: Image deduplication index
- **DistrictCourts**: Court metadata and MDL mappings

#### S3 Storage
- **Documents**: PDF/ZIP court documents
- **HTML Files**: Cached docket pages
- **Images**: Ad creatives and screenshots
- **Reports**: Generated HTML reports

#### Local Storage
- **SQLite Queue**: Deferred image processing
- **File Cache**: Temporary download storage
- **Logs**: Processing and error logs

### 4. Reporting Layer

#### Report Generation
- **Data Aggregation**: Combines PACER and FB ads data
- **Chart Generation**: Plotly-based visualizations
- **HTML Rendering**: Jinja2 templates for reports
- **Section Management**: Configurable report sections

#### Distribution
- **Email Service**: SES integration for delivery
- **S3 Publishing**: Web-accessible report storage
- **Scheduling**: Daily and weekly report cycles

## Service Architecture Patterns

### Base Classes

```python
# Async service with lifecycle management
class AsyncServiceBase:
    async def initialize()
    async def cleanup()
    async def _execute_action()

# Standardized component implementation  
class ComponentImplementation:
    def __init__(logger, error_handler)
    async def _execute_action()

# Repository pattern for data access
class RepositoryBase:
    async def find_by_id()
    async def save()
    async def delete()
```

### Dependency Injection

```python
# Container definition
class ServiceContainer(containers.DeclarativeContainer):
    config = providers.Configuration()
    
    # Singleton services
    storage = providers.Singleton(
        StorageService,
        config=config.storage
    )
    
    # Factory services
    processor = providers.Factory(
        ProcessorService,
        storage=storage,
        config=config.processor
    )
```

### Error Handling

- **Structured Logging**: Consistent log formatting
- **Error Recovery**: Automatic retry with exponential backoff
- **Circuit Breakers**: Prevent cascading failures
- **Graceful Degradation**: Partial results on failures

## Data Flow Architecture

### 1. Scraping Pipeline
```
PACER Website → Browser Automation → HTML/PDF Download → 
Local Storage → S3 Upload → Processing Queue
```

### 2. Processing Pipeline
```
Queue → Document Parser → Entity Extraction → 
Classification → Normalization → Validation → DynamoDB
```

### 3. Ad Pipeline
```
FB API → GraphQL Parser → Image Download → 
Deduplication → Classification → Storage → Analytics
```

### 4. Report Pipeline
```
DynamoDB Query → Data Aggregation → Chart Generation → 
HTML Rendering → Email/S3 Distribution
```

## Scalability Considerations

### Horizontal Scaling
- **Worker Pools**: Configurable parallel workers
- **Queue-Based**: Decoupled processing stages
- **Stateless Services**: No shared state between instances
- **Database Sharding**: Partition by date/court

### Performance Optimization
- **Batch Processing**: Efficient bulk operations
- **Caching**: Multi-level cache strategy
- **Connection Pooling**: Reuse expensive connections
- **Async I/O**: Non-blocking operations throughout

### Resource Management
- **Memory Limits**: Bounded queues and streaming
- **Timeout Controls**: Prevent hanging operations
- **Cleanup Handlers**: Proper resource disposal
- **Health Checks**: Service availability monitoring

## Security Architecture

### Authentication
- **API Keys**: Environment-based configuration
- **Session Management**: Secure credential storage
- **Token Rotation**: Automatic refresh mechanisms
- **Access Control**: Service-level permissions

### Data Protection
- **Encryption**: At-rest and in-transit
- **PII Handling**: Careful personal data management
- **Audit Logging**: Comprehensive access logs
- **Compliance**: Legal data handling requirements

## Monitoring and Observability

### Logging
- **Structured Logs**: JSON format with context
- **Log Levels**: Configurable verbosity
- **Correlation IDs**: Request tracing
- **Error Aggregation**: Pattern detection

### Metrics
- **Performance Metrics**: Response times, throughput
- **Business Metrics**: Cases processed, ads classified
- **Resource Metrics**: Memory, CPU, connections
- **Error Rates**: Failure tracking by service

### Alerting
- **Threshold Alerts**: Performance degradation
- **Error Alerts**: Critical failures
- **Business Alerts**: Data quality issues
- **Capacity Alerts**: Resource exhaustion

## Deployment Architecture

### Environment Configuration
- **Config Files**: YAML-based configuration
- **Environment Variables**: Secrets and overrides
- **Feature Flags**: Gradual rollouts
- **Service Discovery**: Dynamic endpoint resolution

### Deployment Strategy
- **Blue-Green**: Zero-downtime deployments
- **Canary Releases**: Gradual rollout
- **Rollback**: Quick reversion capability
- **Health Checks**: Deployment validation

## Technology Stack

### Core Technologies
- **Python 3.11**: Primary language
- **AsyncIO**: Asynchronous execution
- **Pydantic**: Data validation
- **dependency_injector**: DI framework

### Data Technologies
- **DynamoDB**: Primary database
- **S3**: Object storage
- **SQLite**: Local queuing
- **Redis**: Caching (optional)

### Processing Technologies
- **Playwright**: Browser automation
- **BeautifulSoup**: HTML parsing
- **PyPDF**: PDF processing
- **Pillow**: Image processing

### AI/ML Technologies
- **OpenAI API**: GPT-4 integration
- **DeepSeek**: Alternative LLM
- **Mistral**: Local model support
- **scikit-learn**: Classification