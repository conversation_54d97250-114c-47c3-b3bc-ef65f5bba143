# LexGenius Project Index

## Project Overview

LexGenius is a legal intelligence platform that monitors Facebook ads from law firms and court dockets through PACER. It processes this data to generate weekly reports combining ad campaign insights with litigation updates for mass tort cases.

## Core Architecture

### Service Architecture (`src/services/`)

The project follows a modern service-oriented architecture with dependency injection:

#### AI Services (`ai/`)
- **ai_orchestrator.py** - Coordinates AI model services for processing tasks
- **ai_service_factory.py** - Factory for creating AI service instances
- **batch_processor.py** - Batch processing of AI tasks
- **deepseek_service.py** - DeepSeek model integration
- **mistral_service.py** - Mistral model integration  
- **prompt_manager.py** - Manages prompts for different AI models

#### PACER Services (`pacer/`) - 24 specialized services
- **authentication_service.py** - PACER login and session management
- **browser/** - Browser automation and navigation
  - **browser_service.py** - Browser lifecycle management
  - **navigator.py** - Page navigation utilities
- **case_processing_service.py** - Individual case processing
- **court_processing_service.py** - Court-level processing with isolation
- **docket_processing_orchestrator_service.py** - Orchestrates docket workflows
- **download_orchestration_service.py** - Manages document downloads
- **file_operations_service.py** - File management and organization
- **html_processing_service.py** - HTML parsing and extraction
- **pacer_orchestrator_service.py** - Main PACER workflow orchestration
- **relevance_service.py** - Case relevance determination
- **report_service.py** - PACER report generation

#### Facebook Ads Services (`fb_ads/`)
- **ad_processing_service.py** - Main ad processing pipeline
- **api_client.py** - Facebook API integration
- **camoufox/** - Camoufox browser integration
  - **camoufox_api_client.py** - Camoufox-specific API client
  - **camoufox_session_manager.py** - Session management
- **categorizer.py** - Ad categorization logic
- **classifier.py** - Hybrid rule-based and ML classification
- **concurrent_workflow_service.py** - Parallel ad processing
- **graphql_response_parser.py** - GraphQL response parsing
- **image_handler.py** - Image processing and deduplication
- **orchestrator.py** - FB ads workflow orchestration
- **session_manager.py** - Browser session management

#### Transformer Services (`transformer/`) - 30 active services
- **data_processing_engine.py** - Core data transformation engine
- **docket_processor.py** - Docket data processing
- **law_firm_processor.py** - Law firm extraction and normalization
- **litigation_classifier.py** - Case type classification
- **mdl_processor.py** - MDL (Multi-District Litigation) processing
- **uploader.py** - Data upload orchestration

#### Report Services (`reports/`)
- **ad_df_processor_service.py** - Ad data frame processing
- **ad_page_generator_service.py** - Ad page HTML generation
- **data_loader_service.py** - Report data loading
- **processing_service.py** - Report processing logic
- **publishing_service.py** - Report distribution
- **rendering_service.py** - HTML report rendering
- **reports_orchestrator_service.py** - Report workflow orchestration

#### Infrastructure Services
- **monitoring/performance_monitoring_service.py** - Performance tracking
- **infrastructure/resource_cleanup_service.py** - Resource management
- **uploader/s3_upload_service.py** - S3 upload service

### Configuration System (`src/config_models/`)
- **base.py** - Base configuration classes
- **scraper.py** - PACER scraping configuration
- **fb_ads.py** - Facebook ads configuration
- **reports.py** - Report generation configuration
- **features.py** - Feature flags for gradual rollouts
- **loader.py** - YAML configuration loading

### Legacy Components
- **`src/pacer/`** - Deprecated PACER components (backward compatibility)
- **`src/reports/`** - Deprecated report components
- **`src/lib/utils/`** - Basic utilities (being phased out)

## Data Flow

1. **Collection Phase**
   - PACER scraping via browser automation
   - Facebook ads API data collection
   - Image downloading and processing

2. **Processing Phase**  
   - Document parsing and extraction
   - Law firm identification and normalization
   - Case classification and MDL association
   - Ad categorization and deduplication

3. **Storage Phase**
   - DynamoDB for structured data
   - S3 for documents and images
   - SQLite for deferred processing queue

4. **Reporting Phase**
   - Daily and weekly report generation
   - HTML rendering with charts and tables
   - Email distribution

## Key Features

### Technical Capabilities
- **Async Architecture** - Full async/await pattern with proper resource cleanup
- **Dependency Injection** - Container-based DI using `dependency_injector`
- **Parallel Processing** - Configurable workers for courts and files
- **Browser Automation** - Playwright/Camoufox for PACER and FB scraping
- **Multi-Model AI** - GPT-4, DeepSeek, Mistral, local models
- **PHash Deduplication** - Image deduplication using perceptual hashing

### Business Features
- **Campaign Classification** - Hybrid rules + ML for ad categorization
- **Law Firm Tracking** - Comprehensive law firm identification
- **MDL Association** - Multi-District Litigation case linking
- **Automatic Retries** - Failed download and processing recovery
- **Flexible Reporting** - Customizable report sections and formats

## Testing Infrastructure

### Test Organization
- **Unit Tests** (`tests/unit/`) - Component-level testing
- **Integration Tests** (`tests/integration/`) - Service integration
- **Performance Tests** (`tests/performance/`) - Baseline tracking
- **Regression Tests** (`tests/regression/`) - Behavior validation

### Test Markers
- `@pytest.mark.unit` - Fast, isolated tests
- `@pytest.mark.integration` - Service integration tests
- `@pytest.mark.requires_aws` - AWS-dependent tests
- `@pytest.mark.slow` - Long-running tests
- `@pytest.mark.requires_browser` - Browser automation tests

## Command Reference

### Main Workflows
```bash
# Run full pipeline
python src/main.py --params config/scrape.yml
python src/main.py --params config/transform.yml
python src/main.py --params config/report.yml

# Run with specific date
python src/main.py --params config/scrape.yml --date 07/15/24
```

### Facebook Ads
```bash
# Update campaign classifications
./run_update_fb_campaigns.sh

# Process queued images
python src/scripts/process_image_queue.py --process

# Classify all ads
python classify_all_ads.py
```

### Testing
```bash
# Run all tests
python run_tests.py

# Run specific test categories
pytest tests/ -m unit
pytest tests/ -m "not slow"
pytest tests/ -m requires_aws

# Coverage reporting
pytest tests/ --cov=src --cov-report=html
```

### Maintenance
```bash
# Clean cache files
scripts/maintainence/clean_pycaches.sh

# Find import errors
scripts/maintainence/find_import_errors.sh

# Search JSON files
./search_json_files.sh <term>
```

## Environment Setup

### Prerequisites
- Python 3.11
- Conda/Mamba package manager
- AWS credentials
- PACER credentials
- Facebook API access

### Installation
```bash
# Create environment
conda env create -f environment.yml
conda activate lexgenius

# Install additional packages with mamba
mamba install <package>
```

### Configuration
- Copy `.env.example` to `.env`
- Add API keys and credentials
- Configure `LEXGENIUS_DATA_DIR` if needed

## Documentation Index

### Architecture Documentation
- [Service Architecture Overview](architecture/service_architecture_overview.md)
- [Dependency Injection Guide](di_migration_guide.md)
- [Configuration System](configuration/CONFIGURATION_GUIDE.md)

### Service Documentation
- [PACER Services](pacer/README.md)
- [Facebook Ads Services](facebook-ads/FB_ADS_ARCHITECTURE.md)
- [Transformer Services](transformer/README.md)
- [Reports Services](reports/service_architecture_guide.md)

### Operations Guides
- [Weekly Reports](operations/weekly_reports.md)
- [Troubleshooting](TROUBLESHOOTING_RUNBOOK.md)
- [Development Guide](development/service_development_guide.md)

### API References
- [Service Interfaces](service_interfaces.md)
- [Repository Interfaces](repository_interfaces.md)
- [Chart Data API](chart-data-api-spec.md)

## Quick Links

- [CLAUDE.md](../CLAUDE.md) - AI assistant instructions
- [Main Entry Point](../src/main.py)
- [Test Runner](../run_tests.py)
- [Environment Config](../environment.yml)