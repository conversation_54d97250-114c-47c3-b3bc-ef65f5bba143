# Service Dependency Diagram

## Service Hierarchy and Dependencies

```mermaid
graph TB
    %% Main Entry Point
    Main[main.py] --> MO[Main Orchestrator]
    
    %% Orchestration Layer
    MO --> SO[Scraping Orchestrator]
    MO --> PO[Processing Orchestrator]  
    MO --> RO[Reports Orchestrator]
    MO --> FO[FB Ads Orchestrator]
    
    %% PACER Services
    SO --> PAS[PACER Authentication Service]
    SO --> CPS[Court Processing Service]
    CPS --> BS[Browser Service]
    CPS --> DPS[Docket Processing Service]
    CPS --> DOS[Download Orchestration Service]
    CPS --> FMS[File Management Service]
    CPS --> RVS[Relevance Service]
    
    %% Processing Services (Transformer)
    PO --> DPE[Data Processing Engine]
    DPE --> DP[Docket Processor]
    DPE --> LFP[Law Firm Processor]
    DPE --> LC[Litigation Classifier]
    DPE --> MP[MDL Processor]
    DPE --> UP[Uploader Service]
    
    %% Facebook Ads Services
    FO --> APS[Ad Processing Service]
    APS --> AC[API Client]
    APS --> SM[Session Manager]
    APS --> CAT[Categorizer]
    APS --> CLS[Classifier]
    APS --> IH[Image Handler]
    
    %% Report Services
    RO --> DLS[Data Loader Service]
    RO --> ADP[Ad DF Processor]
    RO --> APG[Ad Page Generator]
    RO --> RPS[Report Processing Service]
    RO --> PUB[Publishing Service]
    RO --> REN[Rendering Service]
    
    %% Infrastructure Services
    BS --> NAV[Navigator]
    UP --> S3U[S3 Upload Service]
    UP --> DDB[DynamoDB Service]
    
    %% AI Services
    DP --> AIS[AI Service Factory]
    AIS --> DS[DeepSeek Service]
    AIS --> MS[Mistral Service]
    AIS --> PM[Prompt Manager]
    
    %% Storage Layer
    S3U --> S3[(S3 Storage)]
    DDB --> DY[(DynamoDB)]
    IH --> SQL[(SQLite Queue)]
```

## Service Communication Patterns

### 1. Orchestrator Pattern
```
Main Orchestrator
    ├── Scraping Orchestrator (PACER data collection)
    ├── Processing Orchestrator (data transformation)
    ├── FB Ads Orchestrator (ad data collection)
    └── Reports Orchestrator (report generation)
```

### 2. Factory Pattern
```
AI Service Factory
    ├── DeepSeek Service
    ├── Mistral Service
    └── GPT Service (via prompt manager)

HTML Service Factory
    ├── Case Parser Service
    └── Data Updater Service
```

### 3. Repository Pattern
```
Storage Services
    ├── S3 Async Storage (documents, images)
    ├── DynamoDB Storage (structured data)
    └── SQLite Storage (local queue)
```

## Dependency Injection Container Structure

```python
ServiceContainer
    ├── Configuration
    │   ├── Scraper Config
    │   ├── FB Ads Config
    │   ├── Reports Config
    │   └── Feature Flags
    │
    ├── Infrastructure Services
    │   ├── S3 Storage (Singleton)
    │   ├── DynamoDB Storage (Singleton)
    │   ├── Logger Factory
    │   └── Error Handler
    │
    ├── PACER Services
    │   ├── Authentication (Singleton)
    │   ├── Browser Service (Factory)
    │   ├── Court Processor (Factory)
    │   └── File Operations (Factory)
    │
    ├── Processing Services
    │   ├── Data Engine (Factory)
    │   ├── Law Firm Processor (Factory)
    │   ├── MDL Processor (Factory)
    │   └── Uploader (Factory)
    │
    ├── FB Ads Services
    │   ├── API Client (Factory)
    │   ├── Session Manager (Factory)
    │   ├── Classifier (Singleton)
    │   └── Image Handler (Factory)
    │
    └── Report Services
        ├── Data Loader (Factory)
        ├── Renderer (Factory)
        └── Publisher (Factory)
```

## Service Lifecycle

### Initialization Flow
1. **Container Creation**: DI container initialized with config
2. **Singleton Services**: Storage, auth, classifiers created
3. **Factory Services**: Created on-demand per request
4. **Resource Allocation**: Browsers, connections established

### Processing Flow
1. **Request Routing**: Orchestrator receives request
2. **Service Creation**: Factory creates service instances
3. **Dependency Injection**: Dependencies injected via constructor
4. **Execution**: Service performs its operation
5. **Cleanup**: Resources released, connections closed

### Cleanup Flow
1. **Service Cleanup**: Each service's cleanup() method called
2. **Browser Closure**: All browser contexts closed
3. **Connection Release**: Database connections returned
4. **Resource Disposal**: Temporary files cleaned up

## Critical Service Dependencies

### Must Be Initialized First
1. **Configuration Services** - All config loaded
2. **Storage Services** - S3, DynamoDB clients
3. **Authentication Services** - PACER, API credentials

### Can Be Lazy-Loaded
1. **Browser Services** - Created per court/operation
2. **AI Services** - Created when needed
3. **Processing Services** - On-demand creation

### Shared Services (Singletons)
1. **Logger** - Thread-safe logging
2. **Error Handler** - Global error handling
3. **Performance Monitor** - Metrics collection
4. **Feature Flags** - Runtime configuration

## Service Communication Examples

### PACER Scraping Flow
```
Scraping Orchestrator
    → Court Processing Service (per court)
        → Browser Service (isolated context)
        → Authentication Service (shared)
        → Download Service
        → File Operations Service
        → S3 Upload Service
```

### Data Processing Flow
```
Processing Orchestrator
    → Data Processing Engine
        → Docket Processor
            → AI Service Factory → DeepSeek/Mistral
            → Law Firm Processor
            → MDL Processor
        → Uploader Service
            → DynamoDB Service
            → S3 Service
```

### Report Generation Flow
```
Reports Orchestrator
    → Data Loader Service
        → DynamoDB Query Service
    → Ad DF Processor Service
    → Report Processing Service
        → Rendering Service
        → Publishing Service
            → S3 Upload
            → Email Service
```