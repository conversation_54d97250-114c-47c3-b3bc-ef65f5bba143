# LexGenius Developer Quick Reference

## Essential Commands

### Daily Operations
```bash
# Activate environment
conda activate lexgenius

# Run scraper for today
python src/main.py --params config/scrape.yml

# Run for specific date
python src/main.py --params config/scrape.yml --date 07/15/24

# Process data
python src/main.py --params config/transform.yml

# Generate reports
python src/main.py --params config/report.yml
```

### Facebook Ads
```bash
# Update campaigns
./run_update_fb_campaigns.sh

# Process image queue
python src/scripts/process_image_queue.py --process

# Classify ads
python classify_all_ads.py
```

### Testing
```bash
# Quick test run
python run_tests.py --parallel

# Specific tests
pytest tests/ -m unit
pytest tests/ -m "not slow"
pytest tests/unit/services/pacer/ -v

# With coverage
pytest tests/ --cov=src --cov-report=term
```

## Configuration Files

### Main Configs
- `config/scrape.yml` - PACER scraping settings
- `config/transform.yml` - Data processing settings
- `config/report.yml` - Report generation settings
- `config/weekly_report.yml` - Weekly report settings

### Environment Variables (.env)
```bash
# API Keys
OPENAI_API_KEY=sk-...
DEEPSEEK_API_KEY=...
MISTRAL_API_KEY=...

# AWS
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
AWS_DEFAULT_REGION=us-east-1

# PACER
PACER_USERNAME=...
PACER_PASSWORD=...

# Paths
LEXGENIUS_DATA_DIR=/path/to/data
```

## Code Patterns

### Creating a New Service
```python
# 1. Define the service with DI
from src.infrastructure.patterns.component_base import AsyncServiceBase

class MyNewService(AsyncServiceBase):
    def __init__(self, storage, config, logger):
        super().__init__()
        self.storage = storage
        self.config = config
        self.logger = logger
    
    async def _execute_action(self, data):
        # Service logic here
        return result

# 2. Add to container
class ServiceContainer(containers.DeclarativeContainer):
    my_service = providers.Factory(
        MyNewService,
        storage=storage_service,
        config=config.my_service,
        logger=logger
    )
```

### Using Async Patterns
```python
# Always use async context managers
async with aiofiles.open(file_path, 'r') as f:
    content = await f.read()

# Proper cleanup
try:
    browser = await self.browser_service.get_browser()
    # Use browser
finally:
    await self.browser_service.cleanup()

# Batch operations
tasks = [process_item(item) for item in items]
results = await asyncio.gather(*tasks)
```

### Error Handling
```python
from src.lib.utils.shared_utils import create_error_result

try:
    result = await self.process_data(data)
except Exception as e:
    self.logger.error(f"Processing failed: {e}")
    return create_error_result(str(e))
```

## Database Schemas

### DynamoDB Primary Keys
- **FBAdArchive**: (AdArchiveID, StartDate)
- **Pacer**: (FilingDate, DocketNum)
- **LawFirms**: (ID, Name)
- **FBImageHash**: (PHash, AdArchiveID)
- **DistrictCourts**: (CourtId, MdlNum)

### Common Queries
```python
# Query by date range
response = await dynamodb.query(
    TableName='Pacer',
    KeyConditionExpression='FilingDate = :date',
    ExpressionAttributeValues={':date': '20240715'}
)

# Query with GSI
response = await dynamodb.query(
    TableName='Pacer',
    IndexName='CourtId-FilingDate-index',
    KeyConditionExpression='CourtId = :court AND FilingDate = :date'
)
```

## Debugging Tips

### Logging
```python
# Use structured logging
self.logger.info("Processing case", extra={
    "court_id": court_id,
    "docket_num": docket_num,
    "operation": "download"
})

# Log levels
self.logger.debug("Detailed info")
self.logger.info("Normal operation")
self.logger.warning("Potential issue")
self.logger.error("Error occurred", exc_info=True)
```

### Common Issues

1. **Import Errors**
```bash
# Check for import issues
scripts/maintainence/find_import_errors.sh
```

2. **Browser Issues**
```python
# Always use context isolation
context = await browser.new_context(
    downloads_path=court_specific_path
)
```

3. **Async Warnings**
```python
# Wrong
result = some_async_function()  # Missing await

# Right  
result = await some_async_function()
```

## Performance Tips

### Parallel Processing
```python
# Use semaphores to limit concurrency
sem = asyncio.Semaphore(10)

async def process_with_limit(item):
    async with sem:
        return await process_item(item)
```

### Batch Operations
```python
# Batch DynamoDB writes
with dynamodb.batch_writer() as batch:
    for item in items:
        batch.put_item(Item=item)
```

### Caching
```python
# Use service-level caching
@lru_cache(maxsize=1000)
def expensive_operation(param):
    return result
```

## Testing Guidelines

### Test Structure
```python
import pytest
from unittest.mock import Mock, AsyncMock

@pytest.mark.unit
class TestMyService:
    @pytest.fixture
    def service(self):
        return MyService(
            storage=Mock(),
            config={},
            logger=Mock()
        )
    
    @pytest.mark.asyncio
    async def test_execute_action(self, service):
        result = await service.execute_action(data)
        assert result.success
```

### Mocking AWS Services
```python
import boto3
from moto import mock_dynamodb, mock_s3

@mock_dynamodb
@mock_s3
async def test_with_aws():
    # Set up mock tables/buckets
    dynamodb = boto3.resource('dynamodb')
    table = dynamodb.create_table(...)
```

## Git Workflow

### Branching
```bash
# Feature branch
git checkout -b feature/new-service

# Refactor branch  
git checkout -b refactor/cleanup-pacer

# Fix branch
git checkout -b fix/download-timeout
```

### Commit Messages
```
feat: Add new law firm processor service
fix: Resolve timeout in parallel downloads  
refactor: Extract MDL processing to separate service
docs: Update service architecture documentation
test: Add unit tests for classification service
```

## Service Locations

### Core Services
- PACER: `src/services/pacer/`
- FB Ads: `src/services/fb_ads/`
- Transformer: `src/services/transformer/`
- Reports: `src/services/reports/`
- AI: `src/services/ai/`

### Configuration
- Models: `src/config_models/`
- YAML: `config/`
- Prompts: `src/config/prompts/`

### Tests
- Unit: `tests/unit/`
- Integration: `tests/integration/`
- Fixtures: `tests/fixtures/`