#!/bin/bash
# Claude Code Auto-Healing Pipeline Runner

echo "🤖 Claude Code Auto-Healing Pipeline Runner"
echo "============================================="

# Default values
CONFIG="transform"
MAX_RETRIES=5
RETRY_DELAY=30
WATCH_INTERVAL=300

# Help function
show_help() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --config <type>         Pipeline config: transform, fb_ads, report, reports, or all (default: transform)"
    echo "  --max-retries <num>     Maximum retry attempts (default: 5)"
    echo "  --retry-delay <sec>     Delay between retries (default: 30)"
    echo "  --watch-interval <sec>  Interval between runs (default: 300)"
    echo "  --help                  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --config transform                    # Monitor transform pipeline"
    echo "  $0 --config fb_ads                       # Monitor fb_ads pipeline"
    echo "  $0 --config reports                      # Monitor reports pipeline"
    echo "  $0 --config all                          # Monitor all pipelines simultaneously"
    echo "  $0 --config transform --watch-interval 60  # Quick monitoring"
    echo ""
    echo "SuperClaude Commands:"
    echo "  Transform: ./run_claude_heal.sh --config transform"
    echo "  FB Ads:    ./run_claude_heal.sh --config fb_ads"
    echo "  Reports:   ./run_claude_heal.sh --config reports"
    echo "  All:       ./run_claude_heal.sh --config all"
}

# Parse arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --config) CONFIG="$2"; shift ;;
        --max-retries) MAX_RETRIES="$2"; shift ;;
        --retry-delay) RETRY_DELAY="$2"; shift ;;
        --watch-interval) WATCH_INTERVAL="$2"; shift ;;
        --help|-h) show_help; exit 0 ;;
        *) echo "❌ Unknown parameter: $1"; echo "Use --help for usage info"; exit 1 ;;
    esac
    shift
done

# Validate config type
case $CONFIG in
    transform|fb_ads|report|reports|all) ;;
    *) echo "❌ Invalid config type: $CONFIG"; echo "Must be: transform, fb_ads, report, reports, or all"; exit 1 ;;
esac

echo "📋 Configuration:"
echo "   Pipeline config: $CONFIG"
echo "   Max retries: $MAX_RETRIES"
echo "   Retry delay: ${RETRY_DELAY}s"
echo "   Watch interval: ${WATCH_INTERVAL}s"
echo ""

if [ "$CONFIG" = "all" ]; then
    echo "🚀 Starting Claude auto-healing for ALL pipeline configurations..."
else
    echo "🚀 Starting Claude auto-healing for $CONFIG pipeline..."
fi

echo "💡 Claude will automatically detect errors and generate fix commands"
echo "🔧 Errors will be passed to Claude Code for automatic resolution"
echo "⏹️  Press Ctrl+C to stop monitoring"
echo ""

# Run the Claude auto-healing pipeline
python3 claude_auto_heal.py \
    --config "$CONFIG" \
    --max-retries "$MAX_RETRIES" \
    --retry-delay "$RETRY_DELAY" \
    --watch-interval "$WATCH_INTERVAL"
