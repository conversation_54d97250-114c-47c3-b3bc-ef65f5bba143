#!/usr/bin/env python3
"""
Test script to verify immediate shutdown behavior on Ctrl-C
"""

import asyncio
import signal
import sys
import time

def signal_handler(sig, frame):
    """Immediately terminate on signal"""
    print(f"\nReceived signal {sig}. Terminating immediately.")
    sys.exit(130)  # Exit code 130 for SIGINT

async def long_running_task():
    """Simulate a long-running task"""
    print("Starting long-running task...")
    count = 0
    while True:
        count += 1
        print(f"Working... iteration {count}")
        await asyncio.sleep(1)

async def main():
    # Register signal handlers for immediate termination
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("Test script running. Press Ctrl-C to test immediate shutdown.")
    print("The script should terminate immediately without any cleanup messages.")
    
    try:
        await long_running_task()
    except KeyboardInterrupt:
        # This should not be reached due to signal handler
        print("This message should NOT appear - KeyboardInterrupt caught")
        sys.exit(130)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # This should not be reached due to signal handler
        print("This message should NOT appear - KeyboardInterrupt in main")
        sys.exit(130)