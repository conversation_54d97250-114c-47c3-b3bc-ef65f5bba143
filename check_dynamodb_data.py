#!/usr/bin/env python3
"""
Scrip<PERSON> to check the actual data in DynamoDB for the specific case.
"""

import boto3
import json
from boto3.dynamodb.conditions import Key

def check_dynamodb_data():
    """Check the actual data in DynamoDB."""
    
    print("=== CHECKING DYNAMODB DATA ===")
    
    # Initialize DynamoDB client
    dynamodb = boto3.resource('dynamodb', region_name='us-west-2')
    table = dynamodb.Table('Pacer')
    
    # Query for the specific case
    filing_date = "20250714"
    docket_num = "3:25-cv-01789"
    
    try:
        # Query by filing date
        response = table.query(
            KeyConditionExpression=Key('FilingDate').eq(filing_date) & Key('DocketNum').eq(docket_num)
        )
        
        if response['Items']:
            item = response['Items'][0]
            print(f"✅ Found item in DynamoDB")
            print(f"Title field: '{item.get('Title', 'NOT FOUND')}'")
            print(f"Title type: {type(item.get('Title', 'NOT FOUND'))}")
            print(f"Title length: {len(item.get('Title', '')) if item.get('Title') else 'N/A'}")
            print(f"LawFirm: '{item.get('LawFirm', 'NOT FOUND')}'")
            print(f"AddedOn: '{item.get('AddedOn', 'NOT FOUND')}'")
            print(f"All keys: {list(item.keys())}")
            
            # Check if Title is empty or None
            title_value = item.get('Title')
            if not title_value or title_value == '' or title_value == 'nan':
                print(f"❌ PROBLEM: Title field is empty/null in DynamoDB: '{title_value}'")
            else:
                print(f"✅ Title field looks good in DynamoDB")
                
        else:
            print(f"❌ No items found for FilingDate={filing_date}, DocketNum={docket_num}")
            
    except Exception as e:
        print(f"❌ Error querying DynamoDB: {e}")
        
    # Also try querying by AddedOn index
    try:
        print(f"\n--- Querying by AddedOn index ---")
        response = table.query(
            IndexName='AddedOn-index',
            KeyConditionExpression=Key('AddedOn').eq(filing_date)
        )
        
        matching_items = [item for item in response['Items'] if item.get('DocketNum') == docket_num]
        
        if matching_items:
            item = matching_items[0]
            print(f"✅ Found item via AddedOn index")
            print(f"Title field: '{item.get('Title', 'NOT FOUND')}'")
            print(f"Title type: {type(item.get('Title', 'NOT FOUND'))}")
        else:
            print(f"❌ No matching items found via AddedOn index")
            
    except Exception as e:
        print(f"❌ Error querying AddedOn index: {e}")

if __name__ == "__main__":
    check_dynamodb_data()