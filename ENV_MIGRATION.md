# Environment Migration Guide: Conda to UV/pip

## Executive Summary

After analyzing your actual codebase usage, I've found that you're using a much smaller subset of the 474 conda packages listed in your environment. The migration to UV/pip is very straightforward as most of your actively used packages are pip-compatible.

## Analysis Results

### Current Environment
- **Python Version**: 3.11.13 (fully compatible with UV)
- **Total Conda Packages Listed**: 474
- **Actually Used in Code**: ~25-30 packages
- **Packages Requiring Special Handling**: 0 (none!)

### Actually Used Packages by Service

#### PACER Service Dependencies
- pandas (data processing)
- playwright (browser automation)
- beautifulsoup4, lxml (HTML parsing)
- requests, aiohttp (HTTP clients)
- boto3, aioboto3 (AWS S3)
- rich (console output)

#### Facebook Ads Service Dependencies
- pandas, numpy (data analysis)
- scikit-learn (clustering)
- PIL/Pillow, imagehash (image processing)
- tqdm, rich (progress bars)
- aiohttp, boto3 (networking/storage)

#### AI Services Dependencies
- openai (OpenAI API)
- mistralai (Mistral API)
- litellm (LLM routing)

#### Core Dependencies
- pydantic (data validation)
- python-dotenv (environment)
- pytest (testing)
- mypy, ruff (linting)

### Package Categories

#### 1. ✅ Directly Pip-Compatible (90% of used packages)
All your core dependencies work perfectly with pip:
- pandas, numpy
- playwright, beautifulsoup4, lxml
- boto3, aioboto3, aiohttp
- openai, mistralai, litellm
- scikit-learn, Pillow, imagehash
- pytest, mypy, ruff
- pydantic, python-dotenv

#### 2. ✅ No Special Packages Needed!

Since you're not using the deprecated NER components, you can skip:
- **PyTorch** (was only used for NER hardware detection)
- **spaCy** (was only used for NER processing)

This means **100% of your actively used packages are directly pip-compatible** with no special handling required!

## Migration Strategy

### Phase 1: Preparation (Day 1)
1. **Backup Current Environment**
   ```bash
   conda env export > environment_backup_$(date +%Y%m%d).yml
   conda list --export > conda_packages_backup.txt
   ```

2. **Install UV**
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   # Or on macOS with Homebrew:
   brew install uv
   ```

3. **Create Requirements Files**
   ```bash
   # Create from current environment
   pip freeze > requirements_current.txt
   ```

### Phase 2: Environment Setup (30 minutes)

1. **Create New UV Environment**
   ```bash
   # Create Python 3.11.13 environment
   uv venv --python 3.11.13
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

2. **Create Minimal Requirements**
   Create `requirements.txt` with only what you actually use:
   ```txt
   # Core dependencies
   pandas==2.3.0
   numpy==1.26.4
   
   # Web scraping & automation
   playwright==1.52.0
   beautifulsoup4==4.13.4
   lxml==5.4.0
   requests==2.32.4
   aiohttp==3.12.8
   
   # AWS
   boto3==1.37.1
   aioboto3==14.1.0
   
   # AI/LLM APIs
   openai==1.90.0
   mistralai==1.7.0
   litellm==1.73.0
   
   # Machine Learning (for FB Ads clustering)
   scikit-learn==1.7.0
   
   # Image processing (for FB Ads)
   pillow==11.2.1
   imagehash==4.3.2
   
   # Data validation & config
   pydantic==2.9.2
   pydantic-settings==2.9.1
   python-dotenv==1.1.0
   
   # CLI & progress
   rich==14.0.0
   tqdm==4.67.1
   
   # Development tools
   pytest==8.4.0
   pytest-asyncio==1.0.0
   pytest-cov==6.1.1
   mypy==1.16.0
   ruff==0.12.0
   
   # Other utilities
   pyyaml==6.0.2
   jinja2==3.1.6
   aiofiles==24.1.0
   tenacity==9.0.0
   portalocker==2.10.1
   ```

3. **Install Dependencies**
   ```bash
   # Install all at once
   uv pip install -r requirements.txt
   
   # Or install in groups for better error tracking
   uv pip install pandas numpy
   uv pip install playwright beautifulsoup4 lxml requests aiohttp
   uv pip install boto3 aioboto3
   uv pip install openai mistralai litellm
   uv pip install scikit-learn
   uv pip install -r requirements.txt  # Rest
   ```

### Phase 3: Post-Install Setup (5 minutes)

1. **Install Playwright Browsers**
   ```bash
   playwright install chromium
   ```

That's it! No other special setup needed.

### Phase 4: Testing & Validation (30 minutes)

1. **Quick Import Test**
   ```python
   # test_imports.py
   print("Testing core imports...")
   import pandas
   import playwright
   import boto3
   import openai
   print("✓ Core imports successful!")
   
   print("✓ All imports ready to go!")
   ```

2. **Run Core Tests**
   ```bash
   # Test PACER functionality
   pytest tests/test_pacer/ -v -k "not integration"
   
   # Test full suite
   pytest tests/ -v
   ```

3. **Verify Key Services**
   ```bash
   # Test PACER scraping
   python -m src.main pacer --test
   
   # Test AWS connectivity
   aws s3 ls s3://your-bucket/ --profile lexgenius
   ```

### Phase 5: Cleanup & Documentation (15 minutes)

1. **Update Project Files**
   
   Update `pyproject.toml` dependencies section with the minimal requirements
   
2. **Create `.python-version`**
   ```bash
   echo "3.11.13" > .python-version
   ```

3. **Update CLAUDE.local.md**
   ```markdown
   Environment
   - Use `uv` for package management
   - Python 3.11.13
   - Activate: `source .venv/bin/activate`
   - Install packages: `uv pip install <package>`
   ```

## Benefits of UV over Conda

1. **Speed**: UV is 10-100x faster than conda for package resolution
2. **Size**: Smaller environment footprint (~500MB vs ~2GB)
3. **Compatibility**: Better integration with standard Python tooling
4. **Reproducibility**: Lockfiles ensure exact reproductions
5. **Simplicity**: No channel conflicts or solver issues

## Rollback Plan

If issues arise:
1. Deactivate UV environment: `deactivate`
2. Reactivate conda: `conda activate lexgenius`
3. All original files remain untouched

## Post-Migration Checklist

- [ ] All tests pass
- [ ] Development workflow documented
- [ ] Team members trained on UV
- [ ] CI/CD pipelines updated
- [ ] Production deployment tested
- [ ] Backup of conda environment retained

## Common Issues & Solutions

### Issue 1: Binary Dependencies
**Problem**: Some packages need compiled binaries
**Solution**: UV automatically handles most cases; use `--no-binary` flag if needed

### Issue 2: GPU Support for PyTorch
**Problem**: Need CUDA-enabled PyTorch
**Solution**: 
```bash
uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### Issue 3: Missing System Libraries
**Problem**: ImportError for system libraries
**Solution**: Install via system package manager (see Phase 3)

## Maintenance Commands

```bash
# Update all packages
uv pip install --upgrade -r requirements.txt

# Add new package
uv pip install package_name
uv pip freeze > requirements.txt

# Check outdated packages
uv pip list --outdated

# Clean cache
uv cache clean
```

## Quick Migration Summary

Based on actual code analysis:
- **You're only using ~30-40 packages** out of 474 in conda
- **All actively used packages are pip-compatible**
- **Zero special cases** - everything works out of the box
- **No system library dependencies needed**

## One-Line Migration

For the impatient, here's the entire migration:
```bash
# Install UV
curl -LsSf https://astral.sh/uv/install.sh | sh

# Create environment and install everything
uv venv --python 3.11.13 && source .venv/bin/activate
uv pip install pandas numpy playwright beautifulsoup4 lxml requests aiohttp \
  boto3 aioboto3 openai mistralai litellm scikit-learn \
  pillow imagehash pydantic python-dotenv rich tqdm pytest mypy ruff

# Post-install
playwright install chromium
```

**Total migration time: 30-60 minutes** (mostly download time)

## Why This Migration is So Easy

1. **You're not using conda-specific packages** - No BLAS, LAPACK, or other system libraries in your actual code
2. **Modern pip handles binary dependencies** - PyTorch, numpy, scipy all work fine
3. **UV is incredibly fast** - 10-100x faster than conda for installation
4. **Smaller footprint** - Your env will shrink from ~2GB to ~500MB

## Next Steps

1. After migration, you can delete hundreds of unused packages
2. Consider using `uv pip compile` for reproducible builds
3. Update your CI/CD to use UV for faster builds