# Removal Case Processing Fix - Summary

## Problem Description

The PACER download system was incorrectly handling removal cases:

1. **Wrong Link Clicked**: It was clicking the document number link in the 2nd column (`<td>1</td>`) instead of analyzing the docket text in the 3rd column
2. **No AI Analysis**: It wasn't using the DeepSeek AI to analyze the docket text and identify the correct attachment
3. **Incorrect Review Handling**: When no relevant attachment was found, it was still attempting downloads instead of adding cases to the review list

## Example Case Analysis

From `examples/docket_sheet_removal.html`:

```html
<tr>
  <td>06/27/2025</td>  <!-- Column 1: Date -->
  <td><a href="...">1</a></td>  <!-- Column 2: Document number (WRONG LINK) -->
  <td>NOTICE OF REMOVAL... (Attachments: # <a href="...">1</a> Exhibit A-Complaint)</td>  <!-- Column 3: Docket text with attachments (CORRECT) -->
</tr>
```

## Solution Implemented

### 1. Created New Prompt Structure

**Files Created:**
- `src/config/prompts/pacer/get_removal_link/system.md`
- `src/config/prompts/pacer/get_removal_link/user.md`

**Purpose:** Refactored the old `state_court.md` prompt into a proper system/user prompt structure for DeepSeek AI analysis.

### 2. Updated Download Orchestration Service

**File:** `src/services/pacer/download_orchestration_service.py`

**Key Changes:**
- Added DeepSeek client dependency
- Added `_detect_removal_case_from_html()` - detects removal cases from HTML content
- Added `_extract_removal_docket_text()` - extracts 3rd column text using regex parsing
- Added `_get_removal_attachment_number()` - calls DeepSeek AI to analyze docket text
- Added `_click_removal_attachment_link()` - clicks the correct attachment link in the docket text
- Added `_handle_removal_case_no_attachment()` - handles cases where DeepSeek returns "NA"
- Updated `_trigger_download_action()` - added specialized removal case handling

**New Workflow for Removal Cases:**
1. Detect if case is a removal from HTML content
2. Extract the 3rd column docket text containing "NOTICE OF REMOVAL"
3. Send docket text to DeepSeek AI for analysis
4. If DeepSeek returns a number: click the corresponding attachment link in the docket text
5. If DeepSeek returns "NA": add case to review list (no download)
6. After clicking attachment, look for "View Document" button and click if present

### 3. Updated Navigation Service

**File:** `src/services/pacer/navigation_service.py`

**Key Changes:**
- Modified `handle_document_checkboxes_async()` to NOT click the generic "1" link for removal cases
- Added logging to indicate that removal cases should be handled by specialized logic
- Prevents the bug where the wrong link was being clicked

### 4. Proper Error Handling

**New Behavior:**
- When DeepSeek returns "NA" (no relevant attachment): Case is added to review list, no download attempted
- When DeepSeek returns a number: Specific attachment link is clicked
- When attachment click fails: Falls back to normal download logic
- All removal case processing is logged with clear indicators

## Expected Behavior

### For the Example Case:
1. **HTML Analysis**: Detects "NOTICE OF REMOVAL" in docket text ✅
2. **Text Extraction**: Extracts: `"NOTICE OF REMOVAL from SUPREME COURT... (Attachments: # 1 Exhibit A-Complaint)"` ✅
3. **AI Analysis**: DeepSeek analyzes text and returns `{"number": "1"}` for "Exhibit A-Complaint" ✅
4. **Link Click**: Clicks the attachment link `<a href="...">1</a>` within the docket text (not the document number) ✅
5. **View Document**: Clicks "View Document" button if present ✅
6. **Download**: Proceeds with normal PDF download workflow ✅

### For Cases with No Relevant Attachment:
1. **AI Analysis**: DeepSeek returns `{"number": "NA"}` ✅
2. **Review List**: Case is added to `process_review_cases` ✅
3. **No Download**: No document download is attempted ✅

## Files Modified

1. `src/services/pacer/download_orchestration_service.py` - Main logic updates
2. `src/services/pacer/navigation_service.py` - Prevent wrong link clicks
3. `src/config/prompts/pacer/get_removal_link/system.md` - New AI prompt (system)
4. `src/config/prompts/pacer/get_removal_link/user.md` - New AI prompt (user)

## Testing Recommendations

1. **Test with Example Case**: Use `examples/docket_sheet_removal.html` to verify correct attachment identification
2. **Test AI Responses**: Verify DeepSeek correctly identifies "1" for "Exhibit A-Complaint"
3. **Test NA Cases**: Create test cases where no relevant attachment exists
4. **Test Link Clicking**: Verify correct attachment links are clicked, not document numbers
5. **Test Review List**: Verify cases with "NA" responses are added to review list

## Cost Savings

This fix prevents incorrect downloads that were costing money by:
- Only downloading relevant state court documents identified by AI
- Adding irrelevant cases to review list instead of downloading
- Clicking the correct, cheaper attachment links instead of expensive document numbers
