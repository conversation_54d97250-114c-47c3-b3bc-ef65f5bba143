# /src/infrastructure/patterns/component_base.py

"""
Base implementation template for standardized component patterns.

This module provides the ComponentImplementation base class that follows
the template defined in the backup rules for consistent error handling,
logging, and configuration management.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any

from src.infrastructure.protocols.exceptions import Configuration<PERSON><PERSON>r, ServiceError
from src.infrastructure.protocols.logger import LoggerProtocol


class ComponentImplementation(ABC):
    """
    Base implementation class for any component following the ComponentImplementation template.

    This provides standardized error handling, logging, and configuration management
    patterns across all service components.
    """

    def __init__(self, logger: LoggerProtocol | logging.Logger, config: dict[str, Any] | None = None):
        """Initialize the component with logger and config."""
        self.logger = logger
        self.config = config or {}
        self._validate_config()

    def _validate_config(self) -> None:
        """Validate component configuration. Override in subclasses."""
        # Base implementation does nothing - override in subclasses for specific validation
        return

    @abstractmethod
    async def _execute_action(self, data: Any) -> Any:
        """The actual implementation. Must be implemented by subclasses."""
        pass

    async def perform_action(self, data: Any) -> Any:
        """
        Perform the core action with standardized error handling.

        Args:
           data: Any data input.

        Returns:
           Any: Result of action

        Raises:
           ServiceError: For component-specific errors
        """
        try:
            self.logger.debug(
                "performing action",
                extra={"component": self.__class__.__name__, "data": data},
            )
            result = await self._execute_action(data)
            self.logger.info(
                "action complete",
                extra={"component": self.__class__.__name__, "result": result},
            )
            return result
        except Exception as e:
            self.logger.exception(
                "action failed",
                extra={
                    "component": self.__class__.__name__,
                    "error": str(e),
                    "data": data,
                },
            )
            raise ServiceError(
                f"{self.__class__.__name__} failed: {str(e)}",
                {"original_error": e, "data": data},
            )

    def log_debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        """Log debug message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.debug(message, extra=context)

    def log_info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        """Log info message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.info(message, extra=context)

    def log_warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        """Log warning message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.warning(message, extra=context)

    def log_error(
        self,
        message: str,
        extra: dict[str, Any] | None = None,
        exc_info: bool = False,
    ) -> None:
        """Log error message with component context."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.error(message, extra=context, exc_info=exc_info)

    def log_exception(
        self,
        message: str,
        extra: dict[str, Any] | None = None,
    ) -> None:
        """Log exception message with component context and stack trace."""
        context = {"component": self.__class__.__name__}
        if extra:
            context.update(extra)
        self.logger.exception(message, extra=context)


class AsyncServiceBase(ComponentImplementation):
    """
    Base class for async services with additional service-specific patterns.

    Extends ComponentImplementation with service lifecycle management
    and dependency injection patterns.
    """

    def __init__(self, logger: LoggerProtocol | logging.Logger, config: dict[str, Any] | None = None):
        super().__init__(logger, config)
        self._initialized = False
        self._dependencies: dict[str, Any] = {}

    async def initialize(self) -> None:
        """Initialize the service. Override in subclasses."""
        if self._initialized:
            return

        try:
            await self._initialize_service()
            self._initialized = True
            self.log_info("service initialized successfully")
        except Exception as e:
            self.log_error("service initialization failed", {"error": str(e)})
            raise ServiceError(
                f"Failed to initialize {self.__class__.__name__}: {str(e)}"
            )

    async def cleanup(self) -> None:
        """Cleanup service resources. Override in subclasses."""
        try:
            await self._cleanup_service()
            self._initialized = False
            self.log_info("service cleanup completed")
        except Exception as e:
            self.log_warning("service cleanup failed", {"error": str(e)})

    def set_dependency(self, name: str, dependency: Any) -> None:
        """Set a service dependency."""
        self._dependencies[name] = dependency
        self.log_debug(
            f"dependency '{name}' injected",
            {"dependency_type": type(dependency).__name__},
        )

    def get_dependency(self, name: str) -> Any:
        """Get a service dependency."""
        if name not in self._dependencies:
            raise ConfigurationError(
                f"Required dependency '{name}' not found in {self.__class__.__name__}"
            )
        return self._dependencies[name]

    async def _initialize_service(self) -> None:
        """Override in subclasses for service-specific initialization."""
        pass

    async def _cleanup_service(self) -> None:
        """Override in subclasses for service-specific cleanup."""
        pass
