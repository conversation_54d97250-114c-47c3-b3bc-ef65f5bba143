"""Async S3 storage implementation."""

import asyncio
import mimetypes
import os
import time

import aioboto3
from botocore.config import Config
from botocore.exceptions import ClientError

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import Configu<PERSON><PERSON><PERSON>r, ServiceError
from src.infrastructure.protocols.logger import LoggerProtocol

# Note: Avoiding circular import by implementing try_remove locally

S3_ACCESS_DENIED_MARKER = "S3_ACCESS_DENIED"


def try_remove(file_path: str, logger, context: str = ""):
    """
    Local implementation to avoid circular import.
    Try to remove a file, logging any errors but not raising exceptions.
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"Removed file: {file_path} ({context})")
    except OSError as e:
        logger.warning(f"Could not remove file {file_path} ({context}): {e}")
    except Exception as e:
        logger.error(f"Unexpected error removing file {file_path} ({context}): {e}")


class S3AsyncStorage(AsyncServiceBase):
    """Async S3 storage implementation with connection pooling and retry logic."""

    def __init__(
        self,
        logger: LoggerProtocol,
        config: dict[str, any] | None = None,
        bucket_name: str | None = None,
        aws_access_key_id: str | None = None,
        aws_secret_access_key: str | None = None,
        aws_region: str | None = None,
        max_pool_connections: int = 100,  # Increased for parallel firm processing
        s3_cache_service: any = None,  # S3ExistenceCacheService dependency
        disable_versioning: bool = True,  # PERMANENTLY disable S3 versioning - always overwrite objects
    ):
        """
        Initialize async S3 storage.

        Args:
            logger: Logger instance
            config: Configuration dictionary containing S3 settings
            bucket_name: S3 bucket name (overrides config)
            aws_access_key_id: AWS access key ID (overrides config)
            aws_secret_access_key: AWS secret access key (overrides config)
            aws_region: AWS region (overrides config)
            max_pool_connections: Maximum connection pool size
        """
        # Build effective config from parameters and config dict
        effective_config = config.copy() if config else {}

        # Allow parameter overrides
        if bucket_name is not None:
            effective_config["bucket_name"] = bucket_name
        if aws_access_key_id is not None:
            effective_config["aws_access_key_id"] = aws_access_key_id
        if aws_secret_access_key is not None:
            effective_config["aws_secret_access_key"] = aws_secret_access_key
        if aws_region is not None:
            effective_config["aws_region"] = aws_region
        effective_config["max_pool_connections"] = max_pool_connections

        super().__init__(logger, effective_config)

        # Set instance attributes from validated config
        self.bucket_name = self.config["bucket_name"]
        self.aws_access_key_id = self.config["aws_access_key_id"]
        self.aws_secret_access_key = self.config["aws_secret_access_key"]
        self.aws_region = self.config["aws_region"]
        self.max_pool_connections = self.config["max_pool_connections"]

        self._session = None
        self._client = None
        self.folder_cache = set()
        
        # S3 cache service integration
        self.s3_cache_service = s3_cache_service
        self.use_cache_for_existence = s3_cache_service is not None
        
        # Versioning controls
        self.disable_versioning = disable_versioning  # Control versioning behavior

    def _validate_config(self) -> None:
        """Validate S3 configuration."""
        # Check bucket_name is required
        if "bucket_name" not in self.config or not self.config["bucket_name"]:
            raise ConfigurationError("Missing required S3 configuration: bucket_name")

        # For AWS credentials, try config first, then environment variables
        if "aws_access_key_id" not in self.config or not self.config["aws_access_key_id"]:
            env_access_key = (
                os.getenv("AWS_ACCESS_KEY_ID")
                or os.getenv("LEXGENIUS_AWS_ACCESS_KEY")
                or os.getenv("ACCESS_KEY_ID")
            )
            if env_access_key:
                self.config["aws_access_key_id"] = env_access_key
            else:
                raise ConfigurationError(
                    "Missing required S3 configuration: aws_access_key_id (not found in config or environment)"
                )

        if "aws_secret_access_key" not in self.config or not self.config["aws_secret_access_key"]:
            env_secret_key = (
                os.getenv("AWS_SECRET_ACCESS_KEY")
                or os.getenv("LEXGENIUS_AWS_SECRET_KEY")
                or os.getenv("SECRET_ACCESS_KEY")
            )
            if env_secret_key:
                self.config["aws_secret_access_key"] = env_secret_key
            else:
                raise ConfigurationError(
                    "Missing required S3 configuration: aws_secret_access_key (not found in config or environment)"
                )

        # Set region default if not provided
        if "aws_region" not in self.config or not self.config["aws_region"]:
            self.config["aws_region"] = (
                os.getenv("AWS_REGION")
                or os.getenv("LEXGENIUS_AWS_REGION")
                or os.getenv("REGION_NAME")
                or "us-west-2"  # Default to us-west-2 to match project default
            )

        # Set default max_pool_connections if not provided
        if "max_pool_connections" not in self.config:
            self.config["max_pool_connections"] = 50

    async def _execute_action(self, data: any) -> any:
        """Required implementation for ComponentImplementation - not used in S3AsyncStorage."""
        raise NotImplementedError(
            "S3AsyncStorage does not use the _execute_action pattern. Use specific methods like upload_file, download_file, etc."
        )

    def __del__(self):
        """Destructor to ensure resources are cleaned up."""
        # Check if attributes exist before accessing them to handle partial initialization
        if (
            hasattr(self, "_initialized")
            and not self._initialized
            and (getattr(self, "_client", None) or getattr(self, "_session", None))
        ):
            try:
                self.log_warning(
                    "S3AsyncStorage not properly closed. Resources may leak."
                )
            except AttributeError:
                # If logger isn't available, we can't log - just fail silently
                pass

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()

    async def _initialize_service(self) -> None:
        """Initialize the S3 client - implementation for AsyncServiceBase."""
        try:
            # Configure boto3 client with retry and signature settings for RequestTimeTooSkewed errors
            botocore_config = Config(
                max_pool_connections=self.max_pool_connections,
                region_name=self.aws_region,
                signature_version="s3v4",
                retries={"max_attempts": 5, "mode": "adaptive"},
                s3={"addressing_style": "virtual"},
            )

            self.log_info(
                f"Initializing async S3 client for bucket '{self.bucket_name}' in region '{self.aws_region}'"
            )

            # Create session and client
            self._session = aioboto3.Session(
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                region_name=self.aws_region,
            )

            self._client = await self._session.client(
                "s3", config=botocore_config
            ).__aenter__()

            self.log_info("S3 async client initialized successfully")
            
            # FORCE DISABLE BUCKET VERSIONING IMMEDIATELY
            if self.disable_versioning:
                await self._disable_bucket_versioning()

        except Exception as e:
            self.log_error(f"Failed to initialize S3 client: {e}")
            raise ServiceError(
                f"S3 initialization failed: {str(e)}", {"original_error": e}
            )

    async def _cleanup_service(self) -> None:
        """Close the S3 client and clean up resources - implementation for AsyncServiceBase."""
        if self._client:
            try:
                await self._client.__aexit__(None, None, None)
                self.log_debug("S3 client closed successfully")
            except Exception as e:
                self.log_warning(f"Error closing S3 client: {e}")
            finally:
                self._client = None

        # Clear session reference
        # Note: aioboto3.Session doesn't require explicit cleanup
        if self._session:
            self._session = None
            self.log_debug("S3 session reference cleared")

        # Clear cache
        self.folder_cache.clear()

    async def _disable_bucket_versioning(self) -> bool:
        """
        FORCE DISABLE bucket versioning completely.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.log_info(f"🚫 DISABLING S3 VERSIONING for bucket: {self.bucket_name}")
            
            # Suspend versioning on the bucket
            await self._client.put_bucket_versioning(
                Bucket=self.bucket_name,
                VersioningConfiguration={'Status': 'Suspended'}
            )
            
            self.log_info(f"✅ S3 VERSIONING DISABLED for bucket: {self.bucket_name}")
            return True
            
        except Exception as e:
            self.log_error(f"❌ FAILED to disable S3 versioning for bucket {self.bucket_name}: {e}")
            self.log_warning("⚠️  S3 versioning may still be active - check bucket permissions")
            return False

    async def close(self):
        """Backward compatibility wrapper for cleanup."""
        await self.cleanup()

    async def head_object(self, object_key: str) -> dict[str, any]:
        """
        Get object metadata using HEAD request.

        Args:
            object_key: S3 object key

        Returns:
            Object metadata dictionary

        Raises:
            ClientError: If object doesn't exist or access denied
        """
        if not object_key:
            raise ValueError("object_key cannot be empty")

        # Ensure client is initialized
        if not self._initialized:
            await self.initialize()

        response = await self._client.head_object(
            Bucket=self.bucket_name, Key=object_key
        )
        self.log_debug(f"Retrieved metadata for S3 object: {object_key}")
        return response

    async def file_exists(self, object_key: str) -> bool:
        """
        Check if an object exists in S3.

        Args:
            object_key: S3 object key

        Returns:
            True if object exists, False otherwise
        """
        if not object_key:
            return False

        # Use cache service if available
        if self.use_cache_for_existence and self.s3_cache_service:
            try:
                return await self.s3_cache_service.check_exists(object_key)
            except Exception as e:
                self.log_warning(f"Cache service error for {object_key}, falling back to direct S3: {e}")
                # Fall through to direct S3 check

        # Direct S3 check (fallback or when cache not available)
        try:
            await self.head_object(object_key)
            self.log_debug(f"S3 object exists: {object_key}")
            return True
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code")
            if error_code in ("404", "NoSuchKey"):
                self.log_debug(f"S3 object not found: {object_key}")
                return False
            elif error_code in ("403", "AccessDenied"):
                self.log_warning(f"Access denied for S3 object: {object_key}")
                return False
            else:
                self.log_error(f"Error checking S3 object {object_key}: {e}")
                return False
        except Exception as e:
            self.log_error(f"Unexpected error checking S3 object {object_key}: {e}")
            return False

    async def check_s3_existence_async(self, bucket_name: str, object_key: str) -> bool:
        """Backward compatibility alias for file_exists."""
        # Note: bucket_name parameter is ignored since it's set at init
        return await self.file_exists(object_key)

    async def batch_check_exists(self, object_keys: list[str]) -> dict[str, bool]:
        """
        Check existence of multiple S3 objects efficiently.
        
        Args:
            object_keys: List of S3 object keys to check
            
        Returns:
            Dict mapping object_key to existence status
        """
        if not object_keys:
            return {}

        # Use cache service if available for batch operations
        if self.use_cache_for_existence and self.s3_cache_service:
            try:
                return await self.s3_cache_service.batch_check_exists(object_keys)
            except Exception as e:
                self.log_warning(f"Cache service batch error, falling back to individual checks: {e}")
                # Fall through to individual checks

        # Fallback: individual existence checks
        self.log_debug(f"Performing individual existence checks for {len(object_keys)} objects")
        results = {}
        
        # Use semaphore to limit concurrent operations
        semaphore = asyncio.Semaphore(10)
        
        async def check_single(key: str) -> tuple[str, bool]:
            async with semaphore:
                exists = await self.file_exists(key)
                return key, exists
        
        # Execute batch checks
        tasks = [check_single(key) for key in object_keys]
        batch_results = await asyncio.gather(*tasks)
        
        for key, exists in batch_results:
            results[key] = exists
        
        return results

    async def upload_file(
        self,
        file_path: str,
        object_key: str,
        content_type: str | None = None,
        force_upload: bool = False,
    ) -> tuple[str, str]:
        """
        Upload a file to S3.

        Args:
            file_path: Local file path
            object_key: S3 object key
            content_type: MIME type (optional)
            force_upload: Force upload even if exists

        Returns:
            Tuple of (file_path, status)
        """
        object_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{object_key}"

        exists = await self.file_exists(object_key)

        if not force_upload and exists:
            self.log_info(f"File already exists: {object_url}")
            return file_path, "already exists"

        if not content_type:
            content_type = (
                mimetypes.guess_type(file_path)[0] or "application/octet-stream"
            )

        extra_args = {"ContentType": content_type, "ContentDisposition": "inline"}

        try:
            # Read file and upload
            with open(file_path, "rb") as f:
                file_data = f.read()

            await self._client.put_object(
                Bucket=self.bucket_name, Key=object_key, Body=file_data, **extra_args
            )

            status = "reuploaded" if exists else "uploaded"
            self.log_info(f"File {status}: {object_url}")
            return file_path, status

        except Exception as e:
            self.log_error(f"Error uploading {file_path}: {e}")
            return file_path, f"error: {str(e)}"

    async def upload_file_async(
        self,
        file_path: str,
        bucket_name: str,
        object_key: str,
        force_upload: bool = False,
    ) -> bool:
        """Backward compatibility alias for upload_file.

        Returns True on success to match legacy interface.
        """
        # Note: bucket_name parameter is ignored since it's set at init
        _, status = await self.upload_file(
            file_path, object_key, force_upload=force_upload
        )
        return status in ("uploaded", "reuploaded", "already exists")

    def download_file_as_string(self, object_key: str) -> str | None:
        """Download file content as string - synchronous wrapper for backward compatibility."""
        loop = asyncio.get_event_loop()
        content = loop.run_until_complete(self.download_content(object_key))
        if content and isinstance(content, bytes):
            return content.decode("utf-8")
        return content

    async def upload_html_string_async(
        self, html_content: str, object_key: str
    ) -> bool:
        """Upload HTML content as string - backward compatibility method."""
        return await self.upload_content(
            html_content, object_key, content_type="text/html"
        )

    def upload_html_string(
        self,
        html_string: str,
        object_name: str,
        content_type: str = "text/html",
        overwrite: bool = True,  # ALWAYS overwrite - no versioning
    ) -> bool:
        """Sync wrapper for upload_content - for backward compatibility."""
        import asyncio

        try:
            # Run the async method in a new event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    self.upload_content(
                        html_string, object_name, content_type, overwrite
                    )
                )
                return result
            finally:
                loop.close()
        except Exception as e:
            self.log_error(f"Error in sync upload_html_string: {e}")
            return False

    async def delete_object(
        self, object_key: str, delete_all_versions: bool = True
    ) -> bool:
        """
        Delete an object from S3.

        Args:
            object_key: S3 object key to delete
            delete_all_versions: If True, delete all versions of the object (for versioned buckets)

        Returns:
            True if successful (or object doesn't exist), False otherwise
        """
        try:
            # Ensure client is initialized
            if not self._initialized:
                await self.initialize()

            if delete_all_versions:
                # First, list all versions of the object
                try:
                    versions_response = await self._client.list_object_versions(
                        Bucket=self.bucket_name, Prefix=object_key
                    )

                    # Delete all versions
                    versions = versions_response.get("Versions", [])
                    delete_markers = versions_response.get("DeleteMarkers", [])

                    if versions:
                        self.log_info(
                            f"Found {len(versions)} versions of {object_key} to delete"
                        )
                        for version in versions:
                            if version["Key"] == object_key:
                                await self._client.delete_object(
                                    Bucket=self.bucket_name,
                                    Key=object_key,
                                    VersionId=version["VersionId"],
                                )
                                self.log_debug(
                                    f"Deleted version {version['VersionId']} of {object_key}"
                                )

                    if delete_markers:
                        self.log_info(
                            f"Found {len(delete_markers)} delete markers for {object_key} to remove"
                        )
                        for marker in delete_markers:
                            if marker["Key"] == object_key:
                                await self._client.delete_object(
                                    Bucket=self.bucket_name,
                                    Key=object_key,
                                    VersionId=marker["VersionId"],
                                )
                                self.log_debug(
                                    f"Removed delete marker {marker['VersionId']} of {object_key}"
                                )

                    if not versions and not delete_markers:
                        self.log_info(
                            f"No versions found for {object_key} - bucket may not be versioned"
                        )
                        # Fall back to simple delete
                        await self._client.delete_object(
                            Bucket=self.bucket_name, Key=object_key
                        )

                except Exception as e:
                    self.log_warning(
                        f"Error listing versions for {object_key}: {e}. Falling back to simple delete."
                    )
                    # Fall back to simple delete
                    await self._client.delete_object(
                        Bucket=self.bucket_name, Key=object_key
                    )
            else:
                # Simple delete without version handling
                await self._client.delete_object(
                    Bucket=self.bucket_name, Key=object_key
                )

            self.log_info(f"Object deleted successfully: {object_key}")
            return True

        except self._client.exceptions.NoSuchKey:
            self.log_info(f"Object does not exist (already deleted): {object_key}")
            return True

        except Exception as e:
            self.log_error(f"Error deleting object {object_key}: {e}")
            return False

    async def upload_content(
        self,
        content: str | bytes,
        object_key: str,
        content_type: str = "text/plain",
        overwrite: bool = True,  # ALWAYS overwrite - no versioning
        cache_control: str | None = None,
        content_encoding: str | None = None,
        metadata: dict[str, str] | None = None,
    ) -> bool:
        """
        Upload content directly to S3.

        Args:
            content: Content to upload (string or bytes)
            object_key: S3 object key
            content_type: MIME type
            overwrite: Whether to overwrite existing
            cache_control: Cache control header
            content_encoding: Content encoding (e.g., 'gzip')
            metadata: User-defined metadata

        Returns:
            True if successful, False otherwise
        """
        if not overwrite and await self.file_exists(object_key):
            self.log_info(f"Object already exists and overwrite=False: {object_key}")
            return False

        # Ensure client is initialized
        if not self._initialized:
            await self.initialize()

        try:
            # Convert string to bytes if needed
            if isinstance(content, str):
                content = content.encode("utf-8")

            # Prepare put_object arguments
            put_args = {
                "Bucket": self.bucket_name,
                "Key": object_key,
                "Body": content,
                "ContentType": content_type,
                "ContentDisposition": "inline",
            }

            # Add optional parameters if provided
            if cache_control:
                put_args["CacheControl"] = cache_control
            if content_encoding:
                put_args["ContentEncoding"] = content_encoding
            if metadata:
                put_args["Metadata"] = metadata

            # S3 VERSIONING DISABLED AT BUCKET LEVEL - NO VERSIONS SHOULD BE CREATED
            response = await self._client.put_object(**put_args)

            # Log the response for debugging (SUPPRESS VERSION ID LOGGING)
            etag = response.get("ETag", "No ETag")
            
            # DO NOT LOG VERSION IDS - they should not exist with versioning disabled
            self.log_info(
                f"Content uploaded successfully to {object_key} (ETag: {etag}, Size: {len(content)} bytes)"
            )
            return True

        except Exception as e:
            self.log_error(f"Error uploading content to {object_key}: {e}")
            return False

    async def upload_file_content(
        self,
        content: str | bytes,
        key: str,
        content_type: str = "text/plain",
        overwrite: bool = True,  # ALWAYS overwrite - no versioning
        cache_control: str | None = None,
        content_encoding: str | None = None,
        metadata: dict[str, str] | None = None,
    ) -> bool:
        """
        Backward compatibility alias for upload_content with 'key' parameter.

        Args:
            content: Content to upload (string or bytes)
            key: S3 object key (alias for object_key parameter)
            content_type: MIME type
            overwrite: Whether to overwrite existing
            cache_control: Cache control header
            content_encoding: Content encoding (e.g., 'gzip')
            metadata: User-defined metadata

        Returns:
            True if successful, False otherwise
        """
        return await self.upload_content(
            content=content,
            object_key=key,
            content_type=content_type,
            overwrite=overwrite,
            cache_control=cache_control,
            content_encoding=content_encoding,
            metadata=metadata,
        )

    async def download_file(self, object_key: str, local_path: str) -> bool:
        """
        Download a file from S3.

        Args:
            object_key: S3 object key
            local_path: Local file path

        Returns:
            True if successful, False otherwise
        """
        if not await self.file_exists(object_key):
            self.log_warning(f"S3 object does not exist: {object_key}")
            return False

        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # Download file
            response = await self._client.get_object(
                Bucket=self.bucket_name, Key=object_key
            )

            # Write to local file
            content = await response["Body"].read()
            with open(local_path, "wb") as f:
                f.write(content)

            self.log_info(f"Downloaded {object_key} to {local_path}")
            return True

        except Exception as e:
            self.log_error(f"Error downloading {object_key}: {e}")
            if os.path.exists(local_path):
                try_remove(local_path, self.logger, "partial download")
            return False

    async def download_content(self, object_key: str) -> bytes | str | None:
        """
        Download content from S3.

        Args:
            object_key: S3 object key

        Returns:
            Content as bytes, S3_ACCESS_DENIED_MARKER for 403, or None for errors
        """
        # Ensure client is initialized
        if not self._initialized:
            await self.initialize()

        try:
            response = await self._client.get_object(
                Bucket=self.bucket_name, Key=object_key
            )

            content = await response["Body"].read()
            self.log_debug(f"Downloaded content from {object_key}")
            return content

        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code")
            if error_code == "NoSuchKey":
                self.log_error(f"S3 object not found: {object_key}")
                return None
            elif error_code == "AccessDenied":
                self.log_warning(f"Access denied for S3 object: {object_key}")
                return S3_ACCESS_DENIED_MARKER
            else:
                self.log_error(f"S3 error downloading {object_key}: {e}")
                return None
        except Exception as e:
            self.log_error(f"Unexpected error downloading {object_key}: {e}")
            return None

    async def list_objects(self, prefix: str = "") -> list[str]:
        """
        List objects in S3 with given prefix.

        Args:
            prefix: Object key prefix

        Returns:
            List of object keys
        """
        try:
            # Ensure client is initialized
            if not self._initialized:
                await self.initialize()

            objects = []
            paginator = self._client.get_paginator("list_objects_v2")

            # Log the search
            self.log_debug(f"Listing S3 objects with prefix: '{prefix}'")

            async for page in paginator.paginate(
                Bucket=self.bucket_name, Prefix=prefix
            ):
                for obj in page.get("Contents", []):
                    objects.append(obj["Key"])

            self.log_debug(f"Listed {len(objects)} objects with prefix '{prefix}'")
            return objects

        except Exception as e:
            self.log_error(f"Error listing objects with prefix '{prefix}': {e}")
            return []

    # Alias for backward compatibility
    list_files = list_objects

    # Async version for new services
    async def list_existing_files_async(self, prefix: str = "") -> list[str]:
        """
        Async wrapper for list_objects for compatibility with new services.

        Args:
            prefix: Object key prefix

        Returns:
            List of object keys
        """
        return await self.list_objects(prefix)

    def list_existing_files(self, prefix: str = "") -> list[str]:
        """
        Sync wrapper for list_objects for compatibility with HTMLDataUpdater.

        Args:
            prefix: Object key prefix

        Returns:
            List of object keys
        """
        import asyncio
        import concurrent.futures

        try:
            # Check if we're in an async context
            try:
                asyncio.get_running_loop()
                # We're in an async context, use ThreadPoolExecutor to run the async method
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.list_objects(prefix))
                    return future.result()
            except RuntimeError:
                # No running loop, we can safely use asyncio.run
                return asyncio.run(self.list_objects(prefix))
        except Exception as e:
            self.log_error(f"Error in list_existing_files sync wrapper: {e}")
            return []

    async def batch_upload_files(
        self, file_paths: list[str], object_keys: list[str], force_upload: bool = False
    ) -> dict[str, str]:
        """
        Upload multiple files concurrently.

        Args:
            file_paths: List of local file paths
            object_keys: List of S3 object keys
            force_upload: Force upload even if exists

        Returns:
            Dict mapping object keys to status
        """
        results = {}

        # Create upload tasks
        tasks = []
        for file_path, object_key in zip(file_paths, object_keys, strict=False):
            task = self.upload_file(file_path, object_key, force_upload=force_upload)
            tasks.append(task)

        # Execute uploads concurrently
        upload_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for (_file_path, object_key), result in zip(
            zip(file_paths, object_keys, strict=False), upload_results, strict=False
        ):
            if isinstance(result, Exception):
                results[object_key] = f"error: {str(result)}"
            else:
                _, status = result
                results[object_key] = status

        return results

    async def copy_object(self, source_key: str, dest_key: str) -> bool:
        """
        Copy an object within S3.

        Args:
            source_key: Source object key
            dest_key: Destination object key

        Returns:
            True if successful, False otherwise
        """
        try:
            copy_source = {"Bucket": self.bucket_name, "Key": source_key}
            await self._client.copy_object(
                CopySource=copy_source, Bucket=self.bucket_name, Key=dest_key
            )
            self.log_info(f"Copied {source_key} to {dest_key}")
            return True

        except Exception as e:
            self.log_error(f"Error copying {source_key} to {dest_key}: {e}")
            return False

    async def create_folder(self, folder_key: str) -> str | None:
        """
        Create a folder in S3 (by creating an empty object).

        Args:
            folder_key: Folder key (should end with /)

        Returns:
            Folder URL if successful, None otherwise
        """
        if not folder_key.endswith("/"):
            folder_key += "/"

        if folder_key in self.folder_cache:
            return f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{folder_key}"

        if await self.file_exists(folder_key):
            self.folder_cache.add(folder_key)
            return f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{folder_key}"

        try:
            await self._client.put_object(
                Bucket=self.bucket_name, Key=folder_key, Body=b""
            )
            self.folder_cache.add(folder_key)
            object_url = f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{folder_key}"
            self.log_info(f"Created folder: {object_url}")
            return object_url

        except Exception as e:
            self.log_error(f"Error creating folder {folder_key}: {e}")
            return None

    async def upload_with_overwrite(
        self, 
        content: str | bytes, 
        object_key: str, 
        content_type: str = "text/plain"
    ) -> bool:
        """
        Upload content with guaranteed overwrite - no versioning.
        
        Args:
            content: Content to upload (string or bytes)
            object_key: S3 object key
            content_type: MIME type
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure client is initialized
            if not self._initialized:
                await self.initialize()

            # Convert string to bytes if needed
            if isinstance(content, str):
                content = content.encode("utf-8")

            # Prepare put_object arguments with overwrite optimization
            put_args = {
                "Bucket": self.bucket_name,
                "Key": object_key,
                "Body": content,
                "ContentType": content_type,
                "ContentDisposition": "inline",
                # Force overwrite by ensuring same metadata
                "Metadata": {"upload-timestamp": str(int(time.time()))},
                # Consistent server-side encryption for overwrites
                "ServerSideEncryption": "AES256",
            }

            response = await self._client.put_object(**put_args)

            # Log the response for debugging (SUPPRESS VERSION ID)
            etag = response.get("ETag", "No ETag")

            self.log_info(
                f"Force uploaded (overwrite) to {object_key} (ETag: {etag}, Size: {len(content)} bytes)"
            )
            return True

        except Exception as e:
            self.log_error(f"Error force uploading to {object_key}: {e}")
            return False

    async def cleanup_old_versions(self, object_key: str, keep_latest: int = 1) -> dict:
        """
        Delete all versions except the most recent N versions.
        
        Args:
            object_key: S3 object key
            keep_latest: Number of latest versions to keep (default: 1)
            
        Returns:
            Dict with cleanup stats
        """
        try:
            if not self._initialized:
                await self.initialize()

            # List all versions
            versions_response = await self._client.list_object_versions(
                Bucket=self.bucket_name, Prefix=object_key
            )

            versions = versions_response.get("Versions", [])
            delete_markers = versions_response.get("DeleteMarkers", [])
            
            # Filter to exact key match and sort by LastModified
            exact_versions = [v for v in versions if v["Key"] == object_key]
            exact_versions.sort(key=lambda x: x["LastModified"], reverse=True)
            
            # Keep the latest N versions
            versions_to_delete = exact_versions[keep_latest:]
            
            deleted_count = 0
            kept_count = len(exact_versions) - len(versions_to_delete)
            
            # Delete old versions
            for version in versions_to_delete:
                await self._client.delete_object(
                    Bucket=self.bucket_name,
                    Key=object_key,
                    VersionId=version["VersionId"]
                )
                deleted_count += 1
                self.log_info(f"Deleted version {version['VersionId']} of {object_key}")
                
            # Delete all delete markers
            markers_deleted = 0
            for marker in delete_markers:
                if marker["Key"] == object_key:
                    await self._client.delete_object(
                        Bucket=self.bucket_name,
                        Key=object_key,
                        VersionId=marker["VersionId"]
                    )
                    markers_deleted += 1
                    self.log_info(f"Removed delete marker {marker['VersionId']} of {object_key}")
            
            result = {
                "object_key": object_key,
                "versions_deleted": deleted_count,
                "versions_kept": kept_count,
                "delete_markers_removed": markers_deleted,
                "total_versions_before": len(exact_versions) + len([m for m in delete_markers if m["Key"] == object_key])
            }
            
            self.log_info(f"Cleanup completed for {object_key}: {result}")
            return result
            
        except Exception as e:
            self.log_error(f"Error cleaning up versions for {object_key}: {e}")
            return {"error": str(e)}

    async def bulk_cleanup_versions(self, prefix: str = "", keep_latest: int = 1) -> dict:
        """
        Clean up old versions for all objects with given prefix.
        
        Args:
            prefix: Object key prefix to filter objects
            keep_latest: Number of latest versions to keep per object
            
        Returns:
            Dict with bulk cleanup stats
        """
        try:
            objects = await self.list_objects(prefix)
            results = []
            
            self.log_info(f"Starting bulk cleanup for {len(objects)} objects with prefix '{prefix}'")
            
            # Use semaphore to limit concurrent operations
            semaphore = asyncio.Semaphore(5)  # Limit to 5 concurrent cleanups
            
            async def cleanup_single(obj_key: str) -> dict:
                async with semaphore:
                    return await self.cleanup_old_versions(obj_key, keep_latest)
            
            # Execute cleanup tasks
            cleanup_tasks = [cleanup_single(obj_key) for obj_key in objects]
            results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            # Process results and handle exceptions
            processed_results = []
            for obj_key, result in zip(objects, results):
                if isinstance(result, Exception):
                    processed_results.append({"object_key": obj_key, "error": str(result)})
                    self.log_error(f"Error processing {obj_key}: {result}")
                else:
                    processed_results.append(result)
            
            # Calculate totals
            total_deleted = sum(r.get("versions_deleted", 0) for r in processed_results if "error" not in r)
            total_kept = sum(r.get("versions_kept", 0) for r in processed_results if "error" not in r)
            errors_count = sum(1 for r in processed_results if "error" in r)
            
            bulk_result = {
                "prefix": prefix,
                "objects_processed": len(objects),
                "objects_with_errors": errors_count,
                "total_versions_deleted": total_deleted,
                "total_versions_kept": total_kept,
                "results": processed_results
            }
            
            self.log_info(f"Bulk cleanup completed: {bulk_result}")
            return bulk_result
            
        except Exception as e:
            self.log_error(f"Error in bulk cleanup with prefix '{prefix}': {e}")
            return {"error": str(e)}
