# /src/pacer/components/orchestration/job_scheduler.py

"""
Job scheduling component for PACER orchestration.

This component handles job scheduling, queue management, and execution coordination
that was previously embedded in the massive orchestrator.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
import uuid

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import ServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.jobs.jobs_models import PacerJob


class JobStatus(Enum):
    """Job execution status."""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class JobPriority(Enum):
    """Job priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class ScheduledJob:
    """Represents a scheduled job in the system."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    job_type: str = ""
    priority: JobPriority = JobPriority.NORMAL
    status: JobStatus = JobStatus.PENDING
    
    # Execution details
    handler: Optional[Callable] = None
    args: tuple = field(default_factory=tuple)
    kwargs: Dict[str, Any] = field(default_factory=dict)
    
    # Scheduling details
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Retry configuration
    max_retries: int = 3
    retry_count: int = 0
    retry_delay: float = 5.0
    
    # Results and errors
    result: Any = None
    error: Optional[str] = None
    
    # Dependencies
    dependencies: List[str] = field(default_factory=list)
    dependent_jobs: List[str] = field(default_factory=list)


class JobScheduler(AsyncServiceBase):
    """
    Manages job scheduling and execution for PACER operations.
    
    Responsible for:
    - Job queue management
    - Job scheduling and execution
    - Dependency resolution
    - Retry logic and error handling
    - Job monitoring and reporting
    """
    
    def __init__(
        self, 
        logger: LoggerProtocol | logging.Logger, 
        config: Dict[str, Any] | None = None
    ):
        super().__init__(logger, config)
        
        # Job storage
        self._jobs: Dict[str, ScheduledJob] = {}
        self._job_queue: asyncio.Queue = asyncio.Queue()
        self._running_jobs: Dict[str, asyncio.Task] = {}
        
        # Configuration
        self._max_concurrent_jobs = 5
        self._job_timeout = 300  # 5 minutes default
        self._cleanup_interval = 300  # 5 minutes
        
        # Worker management
        self._workers: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        
        # Statistics
        self._stats = {
            "jobs_scheduled": 0,
            "jobs_completed": 0,
            "jobs_failed": 0,
            "jobs_cancelled": 0,
        }
        
    async def _initialize_service(self) -> None:
        """Initialize job scheduler."""
        self.log_info("initializing job scheduler")
        
        # Load configuration
        self._load_configuration()
        
        # Start worker tasks
        await self._start_workers()
        
        # Start cleanup task
        self._start_cleanup_task()
        
        self.log_info("job scheduler initialized successfully", {
            "max_concurrent_jobs": self._max_concurrent_jobs,
            "workers": len(self._workers)
        })
        
    async def _execute_action(self, data: Any) -> Any:
        """Execute job scheduling actions."""
        if not isinstance(data, dict):
            raise ServiceError("Job scheduler action data must be a dictionary")
            
        action = data.get("action")
        match action:
            case "schedule_job":
                return await self.schedule_job(
                    data.get("name"),
                    data.get("handler"),
                    data.get("args", ()),
                    data.get("kwargs", {}),
                    data.get("priority", JobPriority.NORMAL),
                    data.get("scheduled_at"),
                    data.get("dependencies", [])
                )
            case "cancel_job":
                return await self.cancel_job(data.get("job_id"))
            case "get_job_status":
                return self.get_job_status(data.get("job_id"))
            case "get_queue_status":
                return self.get_queue_status()
            case "retry_job":
                return await self.retry_job(data.get("job_id"))
            case _:
                raise ServiceError(f"Unknown job scheduler action: {action}")
    
    def _load_configuration(self) -> None:
        """Load job scheduler configuration."""
        if self.config:
            self._max_concurrent_jobs = self.config.get("max_concurrent_jobs", 5)
            self._job_timeout = self.config.get("job_timeout_seconds", 300)
            self._cleanup_interval = self.config.get("cleanup_interval_seconds", 300)
        
        self.log_debug("job scheduler configuration loaded", {
            "max_concurrent_jobs": self._max_concurrent_jobs,
            "job_timeout": self._job_timeout,
            "cleanup_interval": self._cleanup_interval
        })
    
    async def _start_workers(self) -> None:
        """Start job worker tasks."""
        for i in range(self._max_concurrent_jobs):
            worker = asyncio.create_task(self._worker_loop(f"worker-{i}"))
            self._workers.append(worker)
        
        self.log_debug("job workers started", {"worker_count": len(self._workers)})
    
    def _start_cleanup_task(self) -> None:
        """Start periodic cleanup task."""
        cleanup_task = asyncio.create_task(self._cleanup_loop())
        self._workers.append(cleanup_task)  # Track with workers for shutdown
        
        self.log_debug("cleanup task started")
    
    async def schedule_job(
        self,
        name: str,
        handler: Callable,
        args: tuple = (),
        kwargs: Dict[str, Any] = None,
        priority: JobPriority = JobPriority.NORMAL,
        scheduled_at: Optional[datetime] = None,
        dependencies: List[str] = None,
        max_retries: int = 3,
        retry_delay: float = 5.0
    ) -> str:
        """
        Schedule a new job for execution.
        
        Args:
            name: Human-readable job name
            handler: Callable to execute
            args: Arguments for the handler
            kwargs: Keyword arguments for the handler
            priority: Job priority level
            scheduled_at: When to run the job (None for immediate)
            dependencies: List of job IDs this job depends on
            max_retries: Maximum retry attempts
            retry_delay: Delay between retries in seconds
            
        Returns:
            Job ID for tracking
        """
        # Create job
        job = ScheduledJob(
            name=name,
            job_type=handler.__name__ if hasattr(handler, '__name__') else str(handler),
            priority=priority,
            handler=handler,
            args=args,
            kwargs=kwargs or {},
            scheduled_at=scheduled_at or datetime.utcnow(),
            dependencies=dependencies or [],
            max_retries=max_retries,
            retry_delay=retry_delay
        )
        
        # Store job
        self._jobs[job.id] = job
        self._stats["jobs_scheduled"] += 1
        
        # Check if job can be queued immediately
        if await self._can_execute_job(job):
            await self._queue_job(job)
        
        self.log_info("job scheduled", {
            "job_id": job.id,
            "name": name,
            "priority": priority.name,
            "scheduled_at": job.scheduled_at.isoformat(),
            "dependencies": len(job.dependencies)
        })
        
        return job.id
    
    async def _can_execute_job(self, job: ScheduledJob) -> bool:
        """Check if a job can be executed now."""
        # Check if scheduled time has passed
        if job.scheduled_at and job.scheduled_at > datetime.utcnow():
            return False
        
        # Check dependencies
        for dep_id in job.dependencies:
            if dep_id not in self._jobs:
                self.log_warning("job dependency not found", {"job_id": job.id, "dependency": dep_id})
                continue
                
            dep_job = self._jobs[dep_id]
            if dep_job.status != JobStatus.COMPLETED:
                return False
        
        return True
    
    async def _queue_job(self, job: ScheduledJob) -> None:
        """Add job to execution queue."""
        job.status = JobStatus.PENDING
        await self._job_queue.put(job)
        
        self.log_debug("job queued for execution", {"job_id": job.id, "name": job.name})
    
    async def _worker_loop(self, worker_name: str) -> None:
        """Main worker loop for job execution."""
        self.log_debug("worker started", {"worker": worker_name})
        
        while True:  # No graceful shutdown - run indefinitely
            try:
                # Get next job from queue
                job = await self._job_queue.get()  # No timeout, just wait for job
                
                # Execute job
                await self._execute_job(job, worker_name)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error("worker error", {"worker": worker_name, "error": str(e)})
        
        self.log_debug("worker stopped", {"worker": worker_name})
    
    async def _execute_job(self, job: ScheduledJob, worker_name: str) -> None:
        """Execute a single job."""
        job.status = JobStatus.RUNNING
        job.started_at = datetime.utcnow()
        
        self.log_info("job execution started", {
            "job_id": job.id,
            "name": job.name,
            "worker": worker_name
        })
        
        try:
            # Execute job with timeout
            result = await asyncio.wait_for(
                self._run_job_handler(job),
                timeout=self._job_timeout
            )
            
            # Job completed successfully
            job.status = JobStatus.COMPLETED
            job.result = result
            job.completed_at = datetime.utcnow()
            self._stats["jobs_completed"] += 1
            
            self.log_info("job execution completed", {
                "job_id": job.id,
                "name": job.name,
                "duration": (job.completed_at - job.started_at).total_seconds()
            })
            
            # Check for dependent jobs
            await self._check_dependent_jobs(job)
            
        except asyncio.TimeoutError:
            await self._handle_job_timeout(job)
        except Exception as e:
            await self._handle_job_error(job, e)
    
    async def _run_job_handler(self, job: ScheduledJob) -> Any:
        """Run the job handler function."""
        if asyncio.iscoroutinefunction(job.handler):
            return await job.handler(*job.args, **job.kwargs)
        else:
            # Run synchronous function in thread pool
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None, lambda: job.handler(*job.args, **job.kwargs)
            )
    
    async def _handle_job_timeout(self, job: ScheduledJob) -> None:
        """Handle job timeout."""
        error_msg = f"Job timed out after {self._job_timeout} seconds"
        job.error = error_msg
        
        if job.retry_count < job.max_retries:
            await self._schedule_job_retry(job)
        else:
            job.status = JobStatus.FAILED
            job.completed_at = datetime.utcnow()
            self._stats["jobs_failed"] += 1
            
            self.log_error("job failed (timeout, max retries exceeded)", {
                "job_id": job.id,
                "name": job.name,
                "retry_count": job.retry_count,
                "max_retries": job.max_retries
            })
    
    async def _handle_job_error(self, job: ScheduledJob, error: Exception) -> None:
        """Handle job execution error."""
        job.error = str(error)
        
        if job.retry_count < job.max_retries:
            await self._schedule_job_retry(job)
        else:
            job.status = JobStatus.FAILED
            job.completed_at = datetime.utcnow()
            self._stats["jobs_failed"] += 1
            
            self.log_error("job failed (max retries exceeded)", {
                "job_id": job.id,
                "name": job.name,
                "error": str(error),
                "retry_count": job.retry_count,
                "max_retries": job.max_retries
            })
    
    async def _schedule_job_retry(self, job: ScheduledJob) -> None:
        """Schedule a job for retry."""
        job.retry_count += 1
        job.status = JobStatus.RETRYING
        
        # Calculate retry delay (exponential backoff)
        delay = job.retry_delay * (2 ** (job.retry_count - 1))
        retry_time = datetime.utcnow() + timedelta(seconds=delay)
        job.scheduled_at = retry_time
        
        self.log_info("job scheduled for retry", {
            "job_id": job.id,
            "name": job.name,
            "retry_count": job.retry_count,
            "retry_at": retry_time.isoformat(),
            "delay": delay
        })
        
        # Schedule retry check
        asyncio.create_task(self._schedule_retry_check(job, delay))
    
    async def _schedule_retry_check(self, job: ScheduledJob, delay: float) -> None:
        """Schedule a retry check after delay."""
        await asyncio.sleep(delay)
        
        # Check if job can be retried
        if job.status == JobStatus.RETRYING and await self._can_execute_job(job):
            await self._queue_job(job)
    
    async def _check_dependent_jobs(self, completed_job: ScheduledJob) -> None:
        """Check and queue dependent jobs that can now run."""
        for job_id, job in self._jobs.items():
            if (completed_job.id in job.dependencies and 
                job.status == JobStatus.PENDING and 
                await self._can_execute_job(job)):
                await self._queue_job(job)
    
    async def cancel_job(self, job_id: str) -> Dict[str, Any]:
        """Cancel a job."""
        if job_id not in self._jobs:
            raise ServiceError(f"Job {job_id} not found")
        
        job = self._jobs[job_id]
        
        match job.status:
            case JobStatus.RUNNING:
                # Can't cancel running job easily - mark as cancelled
                job.status = JobStatus.CANCELLED
                job.completed_at = datetime.utcnow()
                self._stats["jobs_cancelled"] += 1
            case JobStatus.PENDING:
                job.status = JobStatus.CANCELLED
                job.completed_at = datetime.utcnow()
                self._stats["jobs_cancelled"] += 1
        
        self.log_info("job cancelled", {"job_id": job_id, "name": job.name})
        
        return {
            "job_id": job_id,
            "status": job.status.value,
            "cancelled": True
        }
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get status of a specific job."""
        if job_id not in self._jobs:
            raise ServiceError(f"Job {job_id} not found")
        
        job = self._jobs[job_id]
        
        return {
            "id": job.id,
            "name": job.name,
            "job_type": job.job_type,
            "status": job.status.value,
            "priority": job.priority.name,
            "scheduled_at": job.scheduled_at.isoformat() if job.scheduled_at else None,
            "started_at": job.started_at.isoformat() if job.started_at else None,
            "completed_at": job.completed_at.isoformat() if job.completed_at else None,
            "retry_count": job.retry_count,
            "max_retries": job.max_retries,
            "error": job.error,
            "dependencies": job.dependencies,
            "dependent_jobs": job.dependent_jobs
        }
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get overall queue status."""
        # Count jobs by status
        status_counts = {}
        for status in JobStatus:
            status_counts[status.value] = sum(
                1 for job in self._jobs.values() if job.status == status
            )
        
        return {
            "total_jobs": len(self._jobs),
            "queue_size": self._job_queue.qsize(),
            "running_jobs": len(self._running_jobs),
            "workers": len(self._workers),
            "status_counts": status_counts,
            "statistics": self._stats.copy(),
            "configuration": {
                "max_concurrent_jobs": self._max_concurrent_jobs,
                "job_timeout": self._job_timeout,
                "cleanup_interval": self._cleanup_interval
            }
        }
    
    async def retry_job(self, job_id: str) -> Dict[str, Any]:
        """Manually retry a failed job."""
        if job_id not in self._jobs:
            raise ServiceError(f"Job {job_id} not found")
        
        job = self._jobs[job_id]
        
        if job.status not in [JobStatus.FAILED, JobStatus.CANCELLED]:
            raise ServiceError(f"Job {job_id} cannot be retried (status: {job.status.value})")
        
        # Reset job for retry
        job.status = JobStatus.PENDING
        job.retry_count = 0
        job.error = None
        job.result = None
        job.started_at = None
        job.completed_at = None
        job.scheduled_at = datetime.utcnow()
        
        # Queue if dependencies are met
        if await self._can_execute_job(job):
            await self._queue_job(job)
        
        self.log_info("job manually retried", {"job_id": job_id, "name": job.name})
        
        return {
            "job_id": job_id,
            "status": job.status.value,
            "retried": True
        }
    
    async def _cleanup_loop(self) -> None:
        """Periodic cleanup of completed jobs."""
        while True:  # No graceful shutdown - run indefinitely
            try:
                await asyncio.sleep(self._cleanup_interval)
                
                # Clean up old completed jobs (older than 1 hour)
                cutoff_time = datetime.utcnow() - timedelta(hours=1)
                jobs_to_remove = []
                
                for job_id, job in self._jobs.items():
                    if (job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED] and
                        job.completed_at and job.completed_at < cutoff_time):
                        jobs_to_remove.append(job_id)
                
                for job_id in jobs_to_remove:
                    del self._jobs[job_id]
                
                if jobs_to_remove:
                    self.log_debug("cleaned up old jobs", {"removed_count": len(jobs_to_remove)})
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.log_error("cleanup loop error", {"error": str(e)})
    
    async def _cleanup_service(self) -> None:
        """Cleanup job scheduler resources."""
        self.log_info("shutting down job scheduler")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Cancel all workers
        for worker in self._workers:
            worker.cancel()
        
        # Wait for workers to finish
        if self._workers:
            await asyncio.gather(*self._workers, return_exceptions=True)
        
        # Cancel running jobs
        for job_id in list(self._running_jobs.keys()):
            await self.cancel_job(job_id)
        
        self.log_info("job scheduler shutdown completed")