"""
PACER Service Coordinator Component

Handles coordination between different PACER services and manages
service dependencies, configuration, and lifecycle operations.
This component provides service initialization, dependency injection,
and cross-service communication.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.storage.s3_async import S3AsyncStorage


class PacerServiceCoordinator(AsyncServiceBase):
    """
    Service coordination and dependency management for PACER operations.

    This component handles:
    - Service initialization and lifecycle management
    - Configuration loading and distribution
    - S3 storage coordination
    - File service coordination
    - Service dependency injection
    - Cross-service communication
    """

    def __init__(
        self,
        logger: LoggerProtocol,
        config: Dict[str, Any],
        config_service=None,
        service_factory=None,
        deepseek_service=None,
        browser_service_factory=None
    ):
        super().__init__(logger, config)

        # Core service dependencies
        self.config_service = config_service
        self.service_factory = service_factory
        self.deepseek_service = deepseek_service
        self.browser_service_factory = browser_service_factory

        # Debug logging for browser service factory
        if browser_service_factory:
            self.log_info(f"✅ PacerServiceCoordinator initialized with browser_service_factory: {type(browser_service_factory).__name__}")
        else:
            self.log_warning("⚠️ PacerServiceCoordinator initialized WITHOUT browser_service_factory - browser service will not be available")

        # Initialized services
        self.s3_storage: Optional[S3AsyncStorage] = None
        self.file_service = None
        self.auth_service = None
        self.browser_service = None
        self.relevance_engine = None

        # Service registry
        self._service_registry: Dict[str, Any] = {}
        self._service_lock = asyncio.Lock()

        # Configuration cache
        self._loaded_configuration: Optional[Dict[str, Any]] = None
        self._config_lock = asyncio.Lock()

        self.log_info("PacerServiceCoordinator initialized")

    async def _execute_action(self, data: Any) -> Any:
        """Execute service coordination actions."""
        action_type = data.get("action_type")
        match action_type:
            case "initialize_services":
                return await self.initialize_all_services()
            case "get_service":
                return await self.get_service(data.get("service_name"))
            case "load_configuration":
                return await self.get_loaded_configuration()
            case _:
                raise PacerServiceError(f"Unknown service coordinator action: {action_type}")

    async def initialize_all_services(self) -> Dict[str, Any]:
        """Initialize all core services and dependencies."""
        try:
            self.log_info("Initializing all PACER services")

            initialization_results = {}

            # Initialize S3 storage
            s3_result = await self._initialize_s3_storage()
            initialization_results["s3_storage"] = s3_result

            # Initialize file service
            file_result = await self._initialize_file_service()
            initialization_results["file_service"] = file_result

            # Initialize authentication service
            auth_result = await self._initialize_auth_service()
            initialization_results["auth_service"] = auth_result

            # Initialize browser service
            browser_result = await self._initialize_browser_service()
            initialization_results["browser_service"] = browser_result

            # Initialize relevance engine
            relevance_result = await self._initialize_relevance_engine()
            initialization_results["relevance_engine"] = relevance_result

            # Initialize DeepSeek service if available
            deepseek_result = await self._ensure_deepseek_service_initialized()
            initialization_results["deepseek_service"] = deepseek_result

            # Load configuration
            config_result = await self._load_configuration()
            initialization_results["configuration"] = config_result

            self.log_info(
                "PACER services initialization completed",
                extra={"initialization_results": initialization_results}
            )

            return initialization_results

        except Exception as e:
            self.log_error(
                "Failed to initialize PACER services",
                extra={"error": str(e)},
                exc_info=True
            )
            raise PacerServiceError(f"Service initialization failed: {str(e)}")

    async def _initialize_s3_storage(self) -> Dict[str, Any]:
        """Initialize S3 storage service."""
        try:
            if self.s3_storage is not None:
                return {"status": "already_initialized", "available": True}

            # Check if S3 configuration is available
            s3_config = self.config.get("s3", {})
            if not s3_config or not s3_config.get("enabled", False):
                self.log_info("S3 storage disabled in configuration")
                return {"status": "disabled", "available": False}

            # Initialize S3 storage
            self.s3_storage = S3AsyncStorage(
                region_name=s3_config.get("region", "us-east-1"),
                bucket_name=s3_config.get("bucket"),
                logger=self.logger,
                config=s3_config
            )

            # Test S3 connectivity
            await self.s3_storage._initialize()

            # Register S3 storage in service registry
            await self._register_service("s3_storage", self.s3_storage)

            self.log_info("S3 storage initialized successfully")
            return {"status": "initialized", "available": True, "bucket": s3_config.get("bucket")}

        except Exception as e:
            self.log_warning(f"S3 storage initialization failed: {e}")
            self.s3_storage = None
            return {"status": "failed", "available": False, "error": str(e)}

    async def _initialize_file_service(self) -> Dict[str, Any]:
        """Initialize file service."""
        try:
            if self.file_service is not None:
                return {"status": "already_initialized", "available": True}

            # Use service factory if available
            if self.service_factory:
                self.file_service = await self.service_factory.create_file_service()
            else:
                # Fallback: Create file service directly
                from src.pacer.pacer_file_service import PacerFileService
                self.file_service = PacerFileService(self.logger, self.config)

            # Set S3 storage if available
            if self.s3_storage and hasattr(self.file_service, 'set_s3_storage'):
                self.file_service.set_s3_storage(self.s3_storage)

            # Register file service
            await self._register_service("file_service", self.file_service)

            self.log_info("File service initialized successfully")
            return {"status": "initialized", "available": True}

        except Exception as e:
            self.log_error(f"File service initialization failed: {e}")
            return {"status": "failed", "available": False, "error": str(e)}

    async def _initialize_auth_service(self) -> Dict[str, Any]:
        """Initialize authentication service."""
        try:
            if self.auth_service is not None:
                return {"status": "already_initialized", "available": True}

            # Create authentication service with deep copy of config
            import copy
            auth_config = copy.deepcopy(self.config) if self.config else {}

            from src.pacer.authentication_service import PacerAuthenticationService
            self.auth_service = PacerAuthenticationService(self.logger, auth_config)

            # Register auth service
            await self._register_service("auth_service", self.auth_service)

            self.log_info("Authentication service initialized successfully")
            return {"status": "initialized", "available": True}

        except Exception as e:
            self.log_error(f"Authentication service initialization failed: {e}")
            return {"status": "failed", "available": False, "error": str(e)}

    async def _initialize_browser_service(self) -> Dict[str, Any]:
        """Initialize browser service from factory."""
        try:
            if self.browser_service is not None:
                return {"status": "already_initialized", "available": True}

            if not self.browser_service_factory:
                self.log_info("Browser service factory not available")
                return {"status": "no_factory", "available": False}

            # Create browser service from factory
            self.browser_service = self.browser_service_factory()
            if self.browser_service is None:
                raise RuntimeError("Browser service factory returned None")

            # Register browser service in service registry
            await self._register_service("browser_service", self.browser_service)

            self.log_info("Browser service initialized successfully")
            return {"status": "initialized", "available": True}

        except Exception as e:
            self.log_error(f"Failed to initialize browser service: {e}")
            return {"status": "failed", "available": False, "error": str(e)}

    async def _initialize_relevance_engine(self) -> Dict[str, Any]:
        """Initialize the case relevance engine."""
        try:
            # Note: Relevance engine is now handled at the service level via PacerRelevanceService
            # This is a placeholder for backward compatibility
            self.relevance_engine = None

            self.log_info("Relevance engine initialization skipped - using PacerRelevanceService per court")
            return {"status": "delegated_to_services", "available": False}

        except Exception as e:
            self.log_warning(f"Relevance engine initialization issue: {e}")
            return {"status": "failed", "available": False, "error": str(e)}

    async def _ensure_deepseek_service_initialized(self) -> Dict[str, Any]:
        """Ensure DeepSeek service is fully initialized."""
        try:
            if not self.deepseek_service:
                return {"status": "not_available", "available": False}

            # Check if already initialized
            if getattr(self.deepseek_service, "_initialized", False):
                return {"status": "already_initialized", "available": True}

            # Initialize the service
            await self.deepseek_service.initialize()

            # Initialize client session if available
            if hasattr(self.deepseek_service, 'client') and self.deepseek_service.client:
                await self.deepseek_service.client._ensure_session()

            # Register DeepSeek service
            await self._register_service("deepseek_service", self.deepseek_service)

            self.log_info("DeepSeek service async initialization completed")
            return {"status": "initialized", "available": True}

        except Exception as e:
            self.log_warning(f"Failed to initialize DeepSeek service: {e}")
            self.deepseek_service = None
            return {"status": "failed", "available": False, "error": str(e)}

    async def load_docket_report_log(
        self, iso_date: str, court_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Load cases from existing docket report log file.

        Args:
            iso_date: Date string in YYYYMMDD format
            court_id: Court identifier (e.g., 'njd')

        Returns:
            List of case dictionaries or None if file not found
        """
        try:
            # Construct docket report file path
            data_path = Path(self.config.get("data_path", "./data"))
            log_file_path = (
                data_path / iso_date / "logs" / "docket_report" / f"{court_id}.json"
            )

            if not log_file_path.exists():
                self.log_debug(f"Docket report log not found: {log_file_path}")
                return None

            # Load and parse the file
            import json
            with open(log_file_path, 'r', encoding='utf-8') as f:
                cases = json.load(f)

            if not isinstance(cases, list):
                self.log_warning(f"Invalid docket report log format in {log_file_path}")
                return None

            self.log_info(
                f"Loaded {len(cases)} cases from docket report log",
                extra={"court_id": court_id, "iso_date": iso_date, "log_file": str(log_file_path)}
            )

            return cases

        except Exception as e:
            self.log_error(
                f"Failed to load docket report log for {court_id}",
                extra={"court_id": court_id, "iso_date": iso_date, "error": str(e)},
                exc_info=True
            )
            return None

    async def get_loaded_configuration(self) -> Dict[str, Any]:
        """
        Get loaded configuration data for dependency injection into other services.

        Returns:
            Dictionary containing loaded configuration including defendants list
        """
        async with self._config_lock:
            if self._loaded_configuration is None:
                # Try to load configuration if not already loaded
                await self._load_configuration()

            return self._loaded_configuration or {}

    async def _load_configuration(self) -> Dict[str, Any]:
        """Load and cache configuration data."""
        if self._loaded_configuration is not None:
            return {"status": "already_loaded", "config_loaded": True}

        try:
            if not self.config_service:
                self.log_warning("Configuration service not available")
                return {"status": "no_config_service", "config_loaded": False}

            # Load configuration components
            relevance_config = self.config_service.get_relevance_config() or {}
            relevant_defendants = self.config_service.get_relevant_defendants()
            stability_config = self.config_service.get_stability_config() or {}
            paths_config = self.config_service.get_paths_config() or {}
            ignore_download_config = self.config_service.get_ignore_download_config() or []

            self._loaded_configuration = {
                "relevance_config": relevance_config,
                "relevant_defendants_lower": relevant_defendants,
                "usa_defendant_regex": relevance_config.get("usa_defendant_regex", "(?!)"),
                "stability_config": stability_config,
                "paths_config": paths_config,
                "ignore_download_config": ignore_download_config,
            }

            self.log_info(
                f"Configuration loaded successfully with {len(relevant_defendants)} defendants",
                extra={"defendant_count": len(relevant_defendants)}
            )

            return {"status": "loaded", "config_loaded": True, "defendant_count": len(relevant_defendants)}

        except Exception as e:
            self.log_error(f"Failed to load configuration: {e}")
            return {"status": "failed", "config_loaded": False, "error": str(e)}

    async def get_service(self, service_name: str) -> Optional[Any]:
        """
        Get a registered service by name.

        Args:
            service_name: Name of the service to retrieve

        Returns:
            Service instance or None if not found
        """
        async with self._service_lock:
            service = self._service_registry.get(service_name)
            if service_name == "browser_service":
                if service:
                    self.log_info(f"✅ Successfully retrieved browser_service: {type(service).__name__}")
                else:
                    self.log_error(f"❌ browser_service not found in registry. Available services: {list(self._service_registry.keys())}")
            return service

    async def _register_service(self, service_name: str, service_instance: Any) -> None:
        """Register a service in the service registry."""
        async with self._service_lock:
            self._service_registry[service_name] = service_instance
            self.log_debug(f"Registered service: {service_name}")

    async def get_s3_storage(self) -> Optional[S3AsyncStorage]:
        """Get the initialized S3 storage instance."""
        return self.s3_storage

    async def ensure_s3_initialized(self) -> None:
        """Ensure S3 storage is initialized and ready for use."""
        if self.s3_storage and not self.s3_storage._client:
            await self.s3_storage._initialize()

    async def inject_dependencies_to_court_service(self, court_service) -> None:
        """
        Inject dependencies into a court processing service.

        Args:
            court_service: Court processing service to inject dependencies into
        """
        try:
            # Set S3 storage if available
            if self.s3_storage and hasattr(court_service, 'set_s3_storage'):
                court_service.set_s3_storage(self.s3_storage)

            # Set loaded configuration
            loaded_config = await self.get_loaded_configuration()
            if loaded_config and hasattr(court_service, 'set_loaded_configuration'):
                court_service.set_loaded_configuration(loaded_config)

            # Set AI service dependencies
            if self.deepseek_service and hasattr(court_service, 'set_transfer_dependencies'):
                court_service.set_transfer_dependencies(
                    gpt_interface=self.deepseek_service,
                    court_lookup=None  # Will be set later if needed
                )

            self.log_info("Dependencies injected into court service successfully")

        except Exception as e:
            self.log_error(f"Failed to inject dependencies into court service: {e}")

    async def _cleanup_service(self) -> None:
        """Clean up service coordinator when shutting down."""
        self.log_info("Cleaning up service coordinator resources")

        # Cleanup services in reverse order of initialization
        services_to_cleanup = ["deepseek_service", "file_service", "browser_service", "auth_service", "s3_storage"]

        for service_name in services_to_cleanup:
            try:
                service = await self.get_service(service_name)
                if service and hasattr(service, 'cleanup'):
                    await service.cleanup()
                    self.log_debug(f"Cleaned up service: {service_name}")
            except Exception as e:
                self.log_warning(f"Error cleaning up service {service_name}: {e}")

        # Clear service registry
        async with self._service_lock:
            self._service_registry.clear()

        # Clear configuration cache
        async with self._config_lock:
            self._loaded_configuration = None

        self.log_info("Service coordinator cleanup completed")

