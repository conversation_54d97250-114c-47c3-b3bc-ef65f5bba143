"""
PACER Browser Manager Component

Manages browser contexts, resource tracking, and browser-related operations
for PACER court processing workflows. This component handles browser lifecycle,
context creation, resource limits, and diagnostics.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from playwright.async_api import <PERSON><PERSON><PERSON><PERSON><PERSON>x<PERSON>, Page

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.jobs.jobs_models import PacerJob


class PacerBrowserManager(AsyncServiceBase):
    """
    Browser context and resource management for PACER operations.
    
    This component handles:
    - Browser context creation and configuration
    - Resource tracking and limits
    - Download path configuration
    - Browser diagnostics and monitoring
    - Context isolation for parallel processing
    """

    def __init__(
        self,
        logger: LoggerProtocol,
        config: Dict[str, Any],
        browser_service=None
    ):
        super().__init__(logger, config)
        
        # Browser service dependency (can be set later via set_browser_service)
        self.browser_service = browser_service
        
        # Browser context factory for creating contexts
        self._browser_context_factory = None
        
        # Resource management
        max_concurrent_browsers = min(config.get("num_workers", 10), 16)  # Cap at 16
        self._max_concurrent_browsers = max_concurrent_browsers
        self._browser_semaphore = asyncio.Semaphore(max_concurrent_browsers)
        
        # Resource tracking
        self._active_browser_contexts: List[BrowserContext] = []
        self._browser_context_lock = asyncio.Lock()
        
        # Context configuration tracking
        self._context_configurations: Dict[str, Dict[str, Any]] = {}
        
        self.log_info(
            f"Browser manager initialized with max {max_concurrent_browsers} concurrent contexts",
            extra={
                "max_concurrent": max_concurrent_browsers,
                "requested_workers": config.get("num_workers", "not_set")
            }
        )

    def set_browser_service(self, browser_service) -> None:
        """
        Set the browser service after initialization.
        
        This allows for post-construction dependency injection when the browser service
        is not available at construction time (e.g., when created by a factory).
        
        Args:
            browser_service: The browser service instance to use for context creation
        """
        if browser_service is None:
            self.log_error("❌ CRITICAL: Browser service injection failed - received None")
            raise PacerServiceError("Cannot inject None as browser service")
            
        self.browser_service = browser_service
        # Create browser context factory
        from src.containers.browser_container import BrowserContainer
        self._browser_context_factory = BrowserContainer.BrowserContextFactory(browser_service)
        
        self.log_info("✅ Browser service injected successfully", {
            "browser_service_type": type(browser_service).__name__
        })

    def _get_required_dependencies(self) -> list[str]:
        """Return list of required dependencies for this component."""
        return ["browser_service"]
    
    async def _initialize_component(self) -> None:
        """Initialize component-specific resources."""
        self.log_info("Initializing PacerBrowserManager")
        if self.browser_service and not self._browser_context_factory:
            from src.containers.browser_container import BrowserContainer
            self._browser_context_factory = BrowserContainer.BrowserContextFactory(self.browser_service)
            
    async def _process_data(self, data: Any) -> Any:
        """Process data using the browser management capabilities."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Execute browser management actions."""
        action_type = data.get("action_type")
        match action_type:
            case "create_context":
                return await self.create_browser_context_with_download_path(**data.get("params", {}))
            case "get_diagnostics":
                return await self.get_browser_diagnostics()
            case _:
                raise PacerServiceError(f"Unknown browser manager action: {action_type}")

    async def create_browser_context_with_download_path(
        self,
        court_id: str,
        iso_date: str,
        enable_downloads: bool = True,
        **context_options
    ) -> BrowserContext:
        """
        Create a new browser context with properly configured download path.
        
        Args:
            court_id: Court identifier for path configuration
            iso_date: Date string in YYYYMMDD format
            enable_downloads: Whether to enable downloads for this context
            **context_options: Additional context configuration options
            
        Returns:
            Configured BrowserContext instance
        """
        async with self._browser_semaphore:
            try:
                self.log_info(
                    f"Creating browser context for court {court_id}",
                    extra={
                        "court_id": court_id,
                        "iso_date": iso_date,
                        "enable_downloads": enable_downloads
                    }
                )
                
                # Validate browser context factory availability
                if not self._browser_context_factory:
                    if not self.browser_service:
                        self.log_error("❌ CRITICAL: Browser service not available for context creation")
                        raise PacerServiceError(
                            "Browser service not available. Ensure proper DI injection via set_browser_service() "
                            "or container wiring before calling create_browser_context_with_download_path()"
                        )
                    # Create factory if not set
                    from src.containers.browser_container import BrowserContainer
                    self._browser_context_factory = BrowserContainer.BrowserContextFactory(self.browser_service)
                
                # Configure download path
                download_path = None
                if enable_downloads:
                    download_path = self._get_download_path(court_id, iso_date)
                    download_path.mkdir(parents=True, exist_ok=True)
                    self.log_debug(f"Download path configured: {download_path}")
                
                # Prepare context options for the factory
                context_config = {
                    "accept_downloads": enable_downloads,
                    **context_options
                }
                
                if download_path:
                    context_config["downloads_path"] = str(download_path)
                
                # Create the browser context using factory
                context = await self._browser_context_factory.create_context(**context_config)
                
                # Track the context
                await self._track_browser_context(context)
                
                # Store context configuration
                context_id = id(context)
                self._context_configurations[str(context_id)] = {
                    "court_id": court_id,
                    "iso_date": iso_date,
                    "download_path": str(download_path) if download_path else None,
                    "enable_downloads": enable_downloads,
                    "created_at": asyncio.get_event_loop().time()
                }
                
                self.log_info(
                    f"Browser context created successfully",
                    extra={
                        "court_id": court_id,
                        "context_id": context_id,
                        "download_path": str(download_path) if download_path else None
                    }
                )
                
                return context
                
            except Exception as e:
                self.log_error(
                    f"Failed to create browser context for court {court_id}",
                    extra={
                        "court_id": court_id,
                        "iso_date": iso_date,
                        "error": str(e)
                    },
                    exc_info=True
                )
                raise PacerServiceError(f"Browser context creation failed: {str(e)}")

    async def configure_context_for_job(
        self, context: BrowserContext, job: PacerJob
    ) -> None:
        """
        Configure an existing browser context for a specific job.
        
        Args:
            context: Browser context to configure
            job: PacerJob instance with configuration data
        """
        try:
            court_id = job.court_id
            iso_date = job.iso_date
            
            self.log_info(
                f"Configuring context for job",
                extra={
                    "court_id": court_id,
                    "iso_date": iso_date,
                    "job_id": getattr(job, 'id', 'unknown')
                }
            )
            
            # Configure download path if needed
            if hasattr(job, 'enable_downloads') and job.enable_downloads:
                download_path = self._get_download_path(court_id, iso_date)
                download_path.mkdir(parents=True, exist_ok=True)
                
                # Set download path for the context
                # Note: Playwright contexts don't have a direct way to change download path
                # after creation, so we store it for reference
                context_id = id(context)
                if str(context_id) not in self._context_configurations:
                    self._context_configurations[str(context_id)] = {}
                
                self._context_configurations[str(context_id)].update({
                    "court_id": court_id,
                    "iso_date": iso_date,
                    "download_path": str(download_path),
                    "job_configured": True,
                    "job_id": getattr(job, 'id', 'unknown')
                })
                
                self.log_debug(f"Context configured with download path: {download_path}")
            
            # Apply any job-specific context settings
            if hasattr(job, 'context_settings') and job.context_settings:
                self.log_debug(f"Applying job-specific context settings: {job.context_settings}")
                # Additional context configuration can be added here
            
        except Exception as e:
            self.log_error(
                f"Failed to configure context for job",
                extra={
                    "court_id": getattr(job, 'court_id', 'unknown'),
                    "job_id": getattr(job, 'id', 'unknown'),
                    "error": str(e)
                },
                exc_info=True
            )
            raise

    async def create_isolated_context(
        self, court_id: str, iso_date: str, **context_options
    ) -> BrowserContext:
        """
        Create an isolated browser context for parallel processing.
        
        This creates a completely independent context that doesn't share
        state with other contexts, enabling safe parallel processing.
        
        Args:
            court_id: Court identifier
            iso_date: Date string in YYYYMMDD format
            **context_options: Additional context options
            
        Returns:
            Isolated BrowserContext instance
        """
        self.log_info(
            f"Creating isolated context for parallel processing",
            extra={"court_id": court_id, "iso_date": iso_date}
        )
        
        # Add isolation-specific options
        isolation_options = {
            "ignore_https_errors": True,
            "bypass_csp": True,
            **context_options
        }
        
        return await self.create_browser_context_with_download_path(
            court_id, iso_date, **isolation_options
        )

    async def close_context(self, context: BrowserContext) -> None:
        """
        Properly close a browser context and clean up resources.
        
        Args:
            context: Browser context to close
        """
        try:
            context_id = str(id(context))
            
            # Get context info for logging
            context_info = self._context_configurations.get(context_id, {})
            court_id = context_info.get("court_id", "unknown")
            
            self.log_info(
                f"Closing browser context",
                extra={"court_id": court_id, "context_id": context_id}
            )
            
            # Untrack the context
            await self._untrack_browser_context(context)
            
            # Close the context
            await context.close()
            
            # Clean up configuration
            if context_id in self._context_configurations:
                del self._context_configurations[context_id]
            
            self.log_debug(f"Browser context closed successfully for court {court_id}")
            
        except Exception as e:
            self.log_warning(
                f"Error closing browser context: {e}",
                extra={"error": str(e)},
                exc_info=True
            )

    async def _track_browser_context(self, context: BrowserContext) -> None:
        """Track a newly created browser context."""
        async with self._browser_context_lock:
            self._active_browser_contexts.append(context)
            active_count = len(self._active_browser_contexts)

            # Enhanced logging with warnings
            match active_count:
                case count if count >= 12:  # 75% of 16
                    self.log_warning(
                        f"HIGH BROWSER COUNT - {count} active contexts",
                        extra={
                            "active_count": count,
                            "limit": self._max_concurrent_browsers,
                            "hard_cap": 16
                        }
                    )
                case count if count >= self._max_concurrent_browsers:
                    self.log_error(
                        f"AT BROWSER LIMIT - {count} active contexts",
                        extra={
                            "active_count": count,
                            "limit": self._max_concurrent_browsers
                        }
                    )
                case _:
                    self.log_info(
                        f"Added browser context. Total active: {active_count}",
                        extra={
                            "active_count": active_count,
                            "limit": self._max_concurrent_browsers
                        }
                    )

    async def _untrack_browser_context(self, context: BrowserContext) -> None:
        """Remove a browser context from tracking."""
        async with self._browser_context_lock:
            if context in self._active_browser_contexts:
                self._active_browser_contexts.remove(context)
                active_count = len(self._active_browser_contexts)

                if active_count == 0:
                    self.log_info("All browsers closed. Total active: 0")
                else:
                    self.log_info(
                        f"Removed browser context. Total active: {active_count}",
                        extra={
                            "active_count": active_count,
                            "limit": self._max_concurrent_browsers
                        }
                    )
            else:
                self.log_warning("Attempted to untrack unknown browser context")

    def _get_download_path(self, court_id: str, iso_date: str) -> Path:
        """
        Get the configured download path for a court and date.
        
        Args:
            court_id: Court identifier
            iso_date: Date string in YYYYMMDD format
            
        Returns:
            Path object for the download directory
        """
        data_path = Path(self.config.get("data_path", "./data"))
        return data_path / iso_date / "pacer" / court_id

    async def get_browser_diagnostics(self) -> Dict[str, Any]:
        """
        Get current browser diagnostics and resource usage information.
        
        Returns:
            Dictionary containing browser diagnostics
        """
        async with self._browser_context_lock:
            active_count = len(self._active_browser_contexts)
            
            # Get context details
            context_details = []
            for context in self._active_browser_contexts:
                context_id = str(id(context))
                config = self._context_configurations.get(context_id, {})
                context_details.append({
                    "context_id": context_id,
                    "court_id": config.get("court_id", "unknown"),
                    "iso_date": config.get("iso_date", "unknown"),
                    "download_path": config.get("download_path"),
                    "created_at": config.get("created_at"),
                    "age_seconds": (asyncio.get_event_loop().time() - config.get("created_at", 0))
                        if config.get("created_at") else None
                })
            
            return {
                "active_contexts": active_count,
                "max_concurrent": self._max_concurrent_browsers,
                "semaphore_value": self._browser_semaphore._value,
                "context_details": context_details,
                "resource_usage": {
                    "utilization_percent": (active_count / self._max_concurrent_browsers * 100)
                        if self._max_concurrent_browsers > 0 else 0,
                    "available_slots": max(0, self._max_concurrent_browsers - active_count)
                }
            }

    async def log_browser_diagnostics(self, label: str = "Browser Diagnostics") -> None:
        """
        Log current browser diagnostics for monitoring.
        
        Args:
            label: Label for the diagnostic log entry
        """
        try:
            diagnostics = await self.get_browser_diagnostics()
            
            self.log_info(
                f"{label}",
                extra={
                    "diagnostics": diagnostics,
                    "active_contexts": diagnostics["active_contexts"],
                    "utilization_percent": diagnostics["resource_usage"]["utilization_percent"]
                }
            )
            
            # Log individual context details at debug level
            for context_detail in diagnostics["context_details"]:
                self.log_debug(
                    f"Active context: {context_detail['court_id']} "
                    f"(age: {context_detail['age_seconds']:.1f}s)",
                    extra=context_detail
                )
                
        except Exception as e:
            self.log_debug(f"Error collecting browser diagnostics: {e}")

    async def cleanup_stale_contexts(self, max_age_seconds: int = 3600) -> int:
        """
        Clean up stale browser contexts that have been active too long.
        
        Args:
            max_age_seconds: Maximum age in seconds before a context is considered stale
            
        Returns:
            Number of contexts cleaned up
        """
        current_time = asyncio.get_event_loop().time()
        contexts_to_close = []
        
        async with self._browser_context_lock:
            for context in self._active_browser_contexts[:]:  # Create a copy to iterate
                context_id = str(id(context))
                config = self._context_configurations.get(context_id, {})
                created_at = config.get("created_at", 0)
                
                if current_time - created_at > max_age_seconds:
                    contexts_to_close.append((context, config.get("court_id", "unknown")))
        
        # Close stale contexts
        cleaned_count = 0
        for context, court_id in contexts_to_close:
            try:
                self.log_warning(
                    f"Closing stale browser context for court {court_id}",
                    extra={
                        "court_id": court_id,
                        "age_seconds": current_time - config.get("created_at", 0)
                    }
                )
                await self.close_context(context)
                cleaned_count += 1
            except Exception as e:
                self.log_error(f"Failed to close stale context for court {court_id}: {e}")
        
        if cleaned_count > 0:
            self.log_info(f"Cleaned up {cleaned_count} stale browser contexts")
        
        return cleaned_count

    async def get_context_info(self, context: BrowserContext) -> Dict[str, Any]:
        """
        Get information about a specific browser context.
        
        Args:
            context: Browser context to get info for
            
        Returns:
            Dictionary containing context information
        """
        context_id = str(id(context))
        config = self._context_configurations.get(context_id, {})
        
        return {
            "context_id": context_id,
            "court_id": config.get("court_id", "unknown"),
            "iso_date": config.get("iso_date", "unknown"),
            "download_path": config.get("download_path"),
            "enable_downloads": config.get("enable_downloads", False),
            "created_at": config.get("created_at"),
            "age_seconds": (asyncio.get_event_loop().time() - config.get("created_at", 0))
                if config.get("created_at") else None,
            "is_tracked": context in self._active_browser_contexts
        }

    async def _cleanup_component(self) -> None:
        """Clean up all browser contexts when shutting down."""
        self.log_info("Cleaning up browser manager resources")
        
        # Close all active contexts
        contexts_to_close = list(self._active_browser_contexts)
        for context in contexts_to_close:
            try:
                await self.close_context(context)
            except Exception as e:
                self.log_warning(f"Error closing context during cleanup: {e}")
        
        # Clear tracking data
        self._context_configurations.clear()
        
        self.log_info(f"Browser manager cleanup completed. Closed {len(contexts_to_close)} contexts")