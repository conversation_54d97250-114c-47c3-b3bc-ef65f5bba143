"""
PACER Orchestrator Facade

Provides backward compatibility with the original PacerOrchestratorService
while using the new component-based architecture underneath.
This facade maintains the same public interface as the original service.
"""

import asyncio
import logging
from datetime import date as DateType
from pathlib import Path
from typing import Any, Dict, List, Optional

from playwright.async_api import <PERSON><PERSON>er<PERSON>ontext

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.pacer.jobs.jobs_models import PacerJob

# Import the new components
from .workflow_orchestrator import PacerWorkflowOrchestrator
from .browser_manager import PacerBrowserManager
from .report_workflow_engine import Pacer<PERSON>eportWorkflowEngine
from .docket_processor import PacerDocketProcessor
from .service_coordinator import PacerServiceCoordinator


class PacerOrchestratorFacade(AsyncServiceBase):
    """
    Facade that maintains backward compatibility with the original PacerOrchestratorService.

    This class delegates to the new component-based architecture while preserving
    the exact same public interface as the original orchestrator service.
    """

    def __init__(
        self,
        logger: LoggerProtocol,
        config: Dict[str, Any],
        config_service=None,
        service_factory=None,
        deepseek_service=None,
        court_processing_service=None,
        # DI-injected components from orchestration container
        workflow_orchestrator=None,
        browser_manager=None,
        report_workflow_engine=None,
        docket_processor=None,
        service_coordinator=None,
    ):
        super().__init__(logger, config)

        # CRITICAL DEBUG: Log orchestrator facade initialization
        self.log_error(f"🔍 DEBUG: PacerOrchestratorFacade.__init__ called!")
        self.log_error(f"🔍 DEBUG: service_coordinator provided: {service_coordinator is not None}")
        self.log_error(f"🔍 DEBUG: service_coordinator type: {type(service_coordinator).__name__ if service_coordinator else 'None'}")

        # Use injected components if provided, otherwise create them (backward compatibility)
        if service_coordinator:
            self.log_error(f"✅ Using injected service_coordinator: {type(service_coordinator).__name__}")
            self.service_coordinator = service_coordinator
        else:
            self.log_error("⚠️ No service_coordinator injected, creating fallback (this should not happen with proper DI)")
            # CRITICAL FIX: Include browser_service_factory when creating fallback service coordinator
            # We need to get the browser service factory from somewhere - for now, set to None
            # This fallback should ideally not be used when proper DI is in place
            self.service_coordinator = PacerServiceCoordinator(
                logger, config, config_service, service_factory, deepseek_service,
                browser_service_factory=None  # TODO: This should be injected properly via DI
            )

        # Use injected browser manager or create new one
        if browser_manager:
            self.browser_manager = browser_manager
        else:
            self.browser_manager = PacerBrowserManager(
                logger, config, browser_service=None  # Will be injected later
            )

        # Use injected report engine or create new one
        if report_workflow_engine:
            self.report_engine = report_workflow_engine
        else:
            self.report_engine = PacerReportWorkflowEngine(
                logger, config,
                navigation_service=None,      # Will be injected later
                file_service=None,           # Will be injected later
                browser_manager=self.browser_manager,  # Inject browser manager
                court_processing_service=court_processing_service
            )

        # Use injected docket processor or create new one
        if docket_processor:
            self.docket_processor = docket_processor
        else:
            self.docket_processor = PacerDocketProcessor(
                logger, config,
                court_processing_service=court_processing_service,
                file_service=None,        # Will be injected later
                verification_service=None # Will be injected later
            )

        # Use injected workflow orchestrator or create new one
        if workflow_orchestrator:
            self.workflow_orchestrator = workflow_orchestrator
        else:
            self.workflow_orchestrator = PacerWorkflowOrchestrator(
                logger, config,
                browser_manager=self.browser_manager,
                report_engine=self.report_engine,
                docket_processor=self.docket_processor,
                service_coordinator=self.service_coordinator
        )

        # Store original parameters for compatibility
        self.get_new_docket_report = config.get("get_new_docket_report", False)
        self.config_service = config_service
        self.court_service = court_processing_service
        self.deepseek_service = deepseek_service

        self.log_info("PacerOrchestratorFacade initialized with component-based architecture")

    async def _execute_action(self, data: Any) -> Any:
        """Execute orchestrator actions through the component architecture."""
        # Delegate to the workflow orchestrator
        return await self.workflow_orchestrator.perform_action(data)

    async def _initialize_service(self) -> None:
        """Initialize all components."""
        try:
            # Initialize service coordinator first
            await self.service_coordinator.initialize()

            # Initialize other components
            await self.browser_manager.initialize()
            await self.report_engine.initialize()
            await self.docket_processor.initialize()
            await self.workflow_orchestrator.initialize()

            # Inject dependencies after initialization
            await self._inject_component_dependencies()

            self.log_info("PacerOrchestratorFacade initialization completed")

        except Exception as e:
            self.log_error(f"Failed to initialize PacerOrchestratorFacade: {e}")
            raise

    async def _inject_component_dependencies(self) -> None:
        """Inject dependencies between components."""
        try:
            # Get services from coordinator
            file_service = await self.service_coordinator.get_service("file_service")
            auth_service = await self.service_coordinator.get_service("auth_service")

            # DEBUG: Add extensive logging for browser service retrieval
            self.log_error(f"🔍 DEBUG: About to call service_coordinator.get_service('browser_service')")
            self.log_error(f"🔍 DEBUG: service_coordinator type: {type(self.service_coordinator).__name__}")
            self.log_error(f"🔍 DEBUG: service_coordinator has browser_service_factory: {hasattr(self.service_coordinator, 'browser_service_factory')}")
            if hasattr(self.service_coordinator, 'browser_service_factory'):
                self.log_error(f"🔍 DEBUG: browser_service_factory value: {self.service_coordinator.browser_service_factory}")

            browser_service = await self.service_coordinator.get_service("browser_service")

            self.log_error(f"🔍 DEBUG: browser_service result: {browser_service}")
            self.log_error(f"🔍 DEBUG: browser_service type: {type(browser_service).__name__ if browser_service else 'None'}")

            # Inject file service into components that need it
            if file_service:
                self.report_engine.file_service = file_service
                self.docket_processor.file_service = file_service

            # Inject auth service where needed
            if auth_service:
                self.report_engine.auth_service = auth_service

            # CRITICAL FIX: Inject browser service into browser manager
            if browser_service:
                if hasattr(self.browser_manager, 'set_browser_service'):
                    self.browser_manager.set_browser_service(browser_service)
                    self.log_info("✅ Browser service injected into browser manager")
                else:
                    self.log_warning("⚠️ Browser manager doesn't have set_browser_service method")
            else:
                self.log_error("❌ No browser service available for injection")

            self.log_debug("Component dependencies injected successfully")

        except Exception as e:
            self.log_warning(f"Failed to inject some component dependencies: {e}", exc_info=True)

    # ===============================================================
    # PUBLIC API - BACKWARD COMPATIBILITY METHODS
    # ===============================================================

    async def process_courts(
        self,
        court_ids: List[str],
        context: BrowserContext = None,
        iso_date: str = None,
        start_date: DateType = None,
        end_date: DateType = None,
        docket_list_input: List[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Main orchestration method - processes multiple courts.

        Maintains backward compatibility with the original method signature.
        """
        return await self.workflow_orchestrator.process_courts(
            court_ids, context, iso_date, start_date, end_date, docket_list_input
        )

    async def process_single_docket(
        self, court_id: str, docket_num: str, context: BrowserContext, iso_date: str
    ) -> Dict[str, Any]:
        """
        Process a single docket number for a specific court.

        Maintains backward compatibility with the original method signature.
        """
        return await self.docket_processor.process_single_docket(
            court_id, docket_num, context, iso_date
        )

    async def process_single_court_job(
        self, job: PacerJob, context: BrowserContext
    ) -> Dict[str, Any]:
        """
        Process a single court job with proper context configuration.

        Maintains backward compatibility with the original method signature.
        """
        return await self.workflow_orchestrator.process_single_court_job(job, context)

    async def process_review_cases(
        self,
        court_id: str,
        context: BrowserContext,
        iso_date: str,
        cases: List[Dict[str, Any]],
        workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process cases that need review or re-processing.

        Maintains backward compatibility with the original method signature.
        """
        return await self.docket_processor.process_review_cases(
            court_id, context, iso_date, cases, workflow_config
        )

    def get_download_path(self, court_id: str, iso_date: str, workflow_type: str) -> Path:
        """
        Get the download path for a specific court and workflow.

        Args:
            court_id: Court identifier
            iso_date: Date string in YYYYMMDD format
            workflow_type: Type of workflow (report, docket_log, etc.)

        Returns:
            Path object for the download directory
        """
        import uuid

        # Create court-specific download path with attempt tracking
        base_path = Path(self.config.get("data_dir", "data"))
        attempt_id = str(uuid.uuid4())[:8]

        download_path = base_path / iso_date / "dockets" / "temp" / f"{court_id}_ctx_dl_{workflow_type}" / f"attempt_1_{attempt_id}"
        download_path.mkdir(parents=True, exist_ok=True)

        return download_path

    async def create_browser_context_with_download_path(
        self,
        court_id: str,
        iso_date: str,
        enable_downloads: bool = True,
        **context_options
    ) -> BrowserContext:
        """
        Create a new browser context with properly configured download path.

        Maintains backward compatibility with the original method signature.
        """
        return await self.browser_manager.create_browser_context_with_download_path(
            court_id, iso_date, enable_downloads, **context_options
        )

    async def load_docket_report_log(
        self, iso_date: str, court_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Load cases from existing docket report log file.

        Maintains backward compatibility with the original method signature.
        """
        return await self.service_coordinator.load_docket_report_log(iso_date, court_id)

    def get_loaded_configuration(self) -> Dict[str, Any]:
        """
        Get loaded configuration data.

        Maintains backward compatibility with the original method signature.
        """
        # Convert async method to sync for backward compatibility
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an async context, we need to handle this differently
            return self.service_coordinator._loaded_configuration or {}
        else:
            return loop.run_until_complete(self.service_coordinator.get_loaded_configuration())

    def get_s3_storage(self):
        """
        Get the initialized S3 storage instance.

        Maintains backward compatibility with the original method signature.
        """
        # Convert async method to sync for backward compatibility
        loop = asyncio.get_event_loop()
        if loop.is_running():
            return self.service_coordinator.s3_storage
        else:
            return loop.run_until_complete(self.service_coordinator.get_s3_storage())

    async def ensure_s3_initialized(self) -> None:
        """
        Ensure S3 storage is initialized and ready for use.

        Maintains backward compatibility with the original method signature.
        """
        return await self.service_coordinator.ensure_s3_initialized()

    @staticmethod
    def filter_failed_downloads(cases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter cases that have failed downloads.

        This is a simple implementation for backward compatibility.
        """
        return [case for case in cases if case.get("download_failed", False)]

    @staticmethod
    def filter_review_cases(cases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter cases that need review.

        This is a simple implementation for backward compatibility.
        """
        return [case for case in cases if case.get("needs_review", False)]

    @staticmethod
    def get_ignore_download_stats() -> Dict[str, Any]:
        """
        Get ignore download statistics.

        This is a placeholder implementation for backward compatibility.
        """
        return {
            "total_ignored": 0,
            "ignored_by_pattern": 0,
            "ignored_by_size": 0,
            "ignored_by_type": 0
        }

    async def _log_browser_diagnostics(self, label: str) -> None:
        """
        Log browser diagnostics.

        Maintains backward compatibility with the original method signature.
        """
        await self.browser_manager.log_browser_diagnostics(label)

    @staticmethod
    def _extract_clean_docket_pattern(docket_num: str) -> str:
        """
        Extract clean docket pattern.

        Simple implementation for backward compatibility.
        """
        if not docket_num:
            return ""

        # Remove common prefixes and suffixes, normalize format
        clean_pattern = docket_num.strip().upper()

        # Remove common court prefixes
        for prefix in ["CV-", "CR-", "CIVIL-", "CRIM-"]:
            if clean_pattern.startswith(prefix):
                clean_pattern = clean_pattern[len(prefix):]

        return clean_pattern

    # Context manager support for backward compatibility
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()

    async def _cleanup_service(self) -> None:
        """Clean up all components."""
        self.log_info("Cleaning up PacerOrchestratorFacade")

        # Clean up components in reverse order
        cleanup_tasks = [
            self.workflow_orchestrator.cleanup(),
            self.docket_processor.cleanup(),
            self.report_engine.cleanup(),
            self.browser_manager.cleanup(),
            self.service_coordinator.cleanup(),
        ]

        # Execute cleanup tasks
        for task in cleanup_tasks:
            try:
                await task
            except Exception as e:
                self.log_warning(f"Error during component cleanup: {e}")

        self.log_info("PacerOrchestratorFacade cleanup completed")


# Backward compatibility alias
PacerOrchestratorService = PacerOrchestratorFacade
