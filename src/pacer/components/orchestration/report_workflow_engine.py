"""
PACER Report Workflow Engine Component

Handles the court report generation workflows for PACER operations.
This component manages the process of generating docket reports from courts,
including navigation, form filling, and report extraction.
"""

import asyncio
import logging
from datetime import date as DateType
from pathlib import Path
from typing import Any, Dict, List, Optional

from playwright.async_api import <PERSON><PERSON><PERSON><PERSON>ontex<PERSON>, Page

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.infrastructure.protocols.logger import LoggerProtocol


class PacerReportWorkflowEngine(AsyncServiceBase):
    """
    Report generation workflow engine for PACER operations.
    
    This component handles:
    - Court report generation workflows
    - Navigation to report forms
    - Form filling and submission
    - Report extraction and processing
    - Multi-court report coordination
    """

    def __init__(
        self,
        logger: LoggerProtocol,
        config: Dict[str, Any],
        navigation_service=None,
        file_service=None,
        browser_manager=None,
        court_processing_service=None
    ):
        super().__init__(logger, config)
        
        # Service dependencies
        self.navigation_service = navigation_service
        self.file_service = file_service
        self.browser_manager = browser_manager
        self.court_processing_service = court_processing_service
        
        # Report generation configuration
        self.report_timeout = config.get("report_timeout", 300)  # 5 minutes default
        self.retry_attempts = config.get("report_retry_attempts", 3)
        self.delay_between_courts = config.get("delay_between_courts", 1.0)
        
        # Report tracking
        self._active_reports: Dict[str, Dict[str, Any]] = {}
        self._report_lock = asyncio.Lock()
        
        self.log_info("PacerReportWorkflowEngine initialized")

    def _get_required_dependencies(self) -> list[str]:
        """Return list of required dependencies for this component."""
        return ["navigation_service", "browser_manager"]

    async def _initialize_component(self) -> None:
        """Initialize component-specific resources."""
        self.log_info("Initializing PacerReportWorkflowEngine")
        
    async def _process_data(self, data: Any) -> Any:
        """Process data using the report workflow engine capabilities."""
        return await self._execute_action(data)

    async def _execute_action(self, data: Any) -> Any:
        """Execute report workflow actions."""
        action_type = data.get("action_type")
        match action_type:
            case "generate_reports":
                return await self.generate_reports_for_courts(**data.get("params", {}))
            case "run_court_workflow":
                return await self.run_court_report_generation_workflow(**data.get("params", {}))
            case _:
                raise PacerServiceError(f"Unknown report workflow action: {action_type}")

    async def generate_reports_for_courts(
        self,
        court_ids: List[str],
        context: BrowserContext,
        iso_date: str,
        start_date: DateType,
        end_date: DateType,
        workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate reports for multiple courts.
        
        Args:
            court_ids: List of court identifiers
            context: Browser context to use
            iso_date: Date string in YYYYMMDD format
            start_date: Start date for report generation
            end_date: End date for report generation
            workflow_config: Workflow configuration
            
        Returns:
            Dictionary containing results for all courts
        """
        self.log_info(
            f"Starting report generation for {len(court_ids)} courts",
            extra={
                "court_ids": court_ids,
                "iso_date": iso_date,
                "date_range": f"{start_date} to {end_date}"
            }
        )
        
        results = {}
        
        for i, court_id in enumerate(court_ids):
            try:
                self.log_info(
                    f"Processing court {i+1}/{len(court_ids)}: {court_id}",
                    extra={"court_id": court_id, "progress": f"{i+1}/{len(court_ids)}"}
                )
                
                # Generate report for this court
                court_result = await self.run_court_report_generation_workflow(
                    court_id, context, iso_date, start_date, end_date, workflow_config
                )
                
                results[court_id] = court_result
                
                # Add delay between courts to avoid overwhelming the server
                if i < len(court_ids) - 1:  # Don't delay after the last court
                    await asyncio.sleep(self.delay_between_courts)
                
            except Exception as e:
                self.log_error(
                    f"Failed to generate report for court {court_id}",
                    extra={
                        "court_id": court_id,
                        "error": str(e),
                        "progress": f"{i+1}/{len(court_ids)}"
                    },
                    exc_info=True
                )
                results[court_id] = {
                    "status": "failed",
                    "error": str(e),
                    "court_id": court_id
                }
        
        self.log_info(
            f"Report generation completed for {len(court_ids)} courts",
            extra={
                "total_courts": len(court_ids),
                "successful": sum(1 for r in results.values() if r.get("status") != "failed"),
                "failed": sum(1 for r in results.values() if r.get("status") == "failed")
            }
        )
        
        return results

    async def run_court_report_generation_workflow(
        self,
        court_id: str,
        context: BrowserContext,
        iso_date: str,
        start_date: DateType,
        end_date: DateType,
        workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Run the complete report generation workflow for a single court.
        
        Args:
            court_id: Court identifier
            context: Browser context to use
            iso_date: Date string in YYYYMMDD format
            start_date: Start date for report generation
            end_date: End date for report generation
            workflow_config: Workflow configuration
            
        Returns:
            Dictionary containing workflow results
        """
        report_id = f"{court_id}_{iso_date}_{asyncio.get_event_loop().time()}"
        
        async with self._report_lock:
            self._active_reports[report_id] = {
                "court_id": court_id,
                "iso_date": iso_date,
                "status": "starting",
                "start_time": asyncio.get_event_loop().time()
            }
        
        try:
            log_prefix = f"[{court_id}] ReportGeneration:"
            self.log_info(f"{log_prefix} Starting report generation workflow")
            
            # Validate dependencies
            self._validate_report_dependencies()
            
            # Phase 1: Navigate to court and authenticate
            self.log_info(f"{log_prefix} Phase 1: Navigating to court")
            await self._navigate_to_court(context, court_id, workflow_config)
            
            async with self._report_lock:
                if report_id in self._active_reports:
                    self._active_reports[report_id]["status"] = "authenticated"
            
            # Phase 2: Access report form
            self.log_info(f"{log_prefix} Phase 2: Accessing report form")
            report_page = await self._access_report_form(context, court_id, workflow_config)
            
            async with self._report_lock:
                if report_id in self._active_reports:
                    self._active_reports[report_id]["status"] = "form_accessed"
            
            # Phase 3: Fill and submit report form
            self.log_info(f"{log_prefix} Phase 3: Filling and submitting report form")
            report_data = await self._fill_and_submit_report_form(
                report_page, court_id, start_date, end_date, workflow_config
            )
            
            async with self._report_lock:
                if report_id in self._active_reports:
                    self._active_reports[report_id]["status"] = "form_submitted"
            
            # Phase 4: Extract and process report results
            self.log_info(f"{log_prefix} Phase 4: Extracting report results")
            extracted_data = await self._extract_report_results(
                report_page, court_id, iso_date, workflow_config
            )
            
            # Phase 5: Save and post-process results
            self.log_info(f"{log_prefix} Phase 5: Saving results")
            saved_results = await self._save_report_results(
                court_id, iso_date, extracted_data, workflow_config
            )
            
            # Compile final results
            final_results = {
                "status": "completed",
                "court_id": court_id,
                "iso_date": iso_date,
                "report_data": report_data,
                "extracted_data": extracted_data,
                "saved_results": saved_results,
                "total_cases": len(extracted_data.get("cases", [])),
                "report_generation_time": asyncio.get_event_loop().time() - self._active_reports[report_id]["start_time"]
            }
            
            async with self._report_lock:
                if report_id in self._active_reports:
                    self._active_reports[report_id]["status"] = "completed"
                    self._active_reports[report_id]["results"] = final_results
            
            self.log_info(
                f"{log_prefix} Report generation completed successfully",
                extra={
                    "total_cases": final_results["total_cases"],
                    "generation_time": final_results["report_generation_time"]
                }
            )
            
            return final_results
            
        except Exception as e:
            async with self._report_lock:
                if report_id in self._active_reports:
                    self._active_reports[report_id]["status"] = "failed"
                    self._active_reports[report_id]["error"] = str(e)
            
            self.log_error(
                f"{log_prefix} Report generation failed",
                extra={
                    "court_id": court_id,
                    "iso_date": iso_date,
                    "error": str(e)
                },
                exc_info=True
            )
            
            return {
                "status": "failed",
                "court_id": court_id,
                "iso_date": iso_date,
                "error": str(e)
            }

    async def _navigate_to_court(
        self, context: BrowserContext, court_id: str, workflow_config: Dict[str, Any]
    ) -> None:
        """
        Navigate to the specified court and authenticate.
        
        Args:
            context: Browser context to use for navigation (must not be None)
            court_id: Court identifier to navigate to
            workflow_config: Workflow configuration containing authentication settings
            
        Raises:
            PacerServiceError: If navigation service is not available or context is None
        """
        # Validate required dependencies
        if not self.navigation_service:
            raise PacerServiceError("Navigation service not available")
        
        # Validate browser context before use - this is a CRITICAL validation
        if context is None:
            error_msg = (
                f"Browser context is None when navigating to court {court_id}. "
                "This indicates a browser context creation failure in the calling component."
            )
            self.log_error(error_msg, {
                "court_id": court_id,
                "workflow_config": workflow_config,
                "navigation_service": type(self.navigation_service).__name__ if self.navigation_service else None,
                "browser_manager": type(self.browser_manager).__name__ if self.browser_manager else None,
                "context_validation": "FAILED",
                "component": "PacerReportWorkflowEngine._navigate_to_court",
                "error_type": "NULL_BROWSER_CONTEXT"
            })
            raise PacerServiceError(error_msg)
        
        # Additional context validation - check if context is still valid
        try:
            # Try to access context properties to ensure it's not closed/invalid
            await context.pages()  # This will throw if context is closed
            
            self.log_debug(f"Browser context validated successfully for court {court_id}", {
                "court_id": court_id,
                "context_type": type(context).__name__,
                "context_validation": "SUCCESS",
                "context_id": id(context)
            })
            
        except Exception as context_error:
            error_msg = (
                f"Browser context is invalid or closed when navigating to court {court_id}. "
                f"Context validation failed: {str(context_error)}"
            )
            self.log_error(error_msg, {
                "court_id": court_id,
                "context_type": type(context).__name__,
                "context_id": id(context),
                "context_validation": "FAILED",
                "validation_error": str(context_error),
                "component": "PacerReportWorkflowEngine._navigate_to_court",
                "error_type": "INVALID_BROWSER_CONTEXT"
            })
            raise PacerServiceError(error_msg)
        
        # Create a new page for navigation
        page = await context.new_page()
        
        try:
            # Navigate to court homepage
            await self.navigation_service.navigate_to_court(page, court_id)
            
            # Authenticate if required
            if workflow_config.get("require_authentication", True):
                await self.navigation_service.authenticate(page, court_id)
            
            self.log_debug(f"Successfully navigated to court {court_id}", {
                "court_id": court_id,
                "authentication_required": workflow_config.get("require_authentication", True),
                "navigation_status": "SUCCESS"
            })
            
        except Exception as e:
            # Ensure page is closed even on navigation failure
            try:
                await page.close()
            except Exception as close_error:
                self.log_warning(f"Failed to close page after navigation error: {close_error}")
            
            raise PacerServiceError(f"Navigation to court {court_id} failed: {str(e)}")

    async def _access_report_form(
        self, context: BrowserContext, court_id: str, workflow_config: Dict[str, Any]
    ) -> Page:
        """Access the docket report form page."""
        page = await context.new_page()
        
        try:
            # Navigate to the reports section
            await self.navigation_service.navigate_to_reports(page, court_id)
            
            # Find and click on docket report link
            await self.navigation_service.access_docket_report_form(page, court_id)
            
            # Wait for form to load
            await page.wait_for_load_state("networkidle")
            
            return page
            
        except Exception as e:
            await page.close()
            raise PacerServiceError(f"Failed to access report form for court {court_id}: {str(e)}")

    async def _fill_and_submit_report_form(
        self,
        page: Page,
        court_id: str,
        start_date: DateType,
        end_date: DateType,
        workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Fill and submit the docket report form."""
        try:
            # Fill date fields
            await self._fill_date_fields(page, start_date, end_date)
            
            # Configure report options
            await self._configure_report_options(page, workflow_config)
            
            # Submit the form
            submit_result = await self._submit_report_form(page, court_id)
            
            return {
                "form_filled": True,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "submit_result": submit_result
            }
            
        except Exception as e:
            raise PacerServiceError(f"Failed to fill and submit report form: {str(e)}")

    async def _fill_date_fields(self, page: Page, start_date: DateType, end_date: DateType) -> None:
        """Fill the date fields in the report form."""
        # Fill start date field
        start_date_str = start_date.strftime("%m/%d/%Y")
        await page.fill('input[name*="date_from"], input[name*="start"], input[id*="date_from"]', start_date_str)
        
        # Fill end date field
        end_date_str = end_date.strftime("%m/%d/%Y")
        await page.fill('input[name*="date_to"], input[name*="end"], input[id*="date_to"]', end_date_str)
        
        self.log_debug(f"Filled date fields: {start_date_str} to {end_date_str}")

    async def _configure_report_options(self, page: Page, workflow_config: Dict[str, Any]) -> None:
        """Configure report-specific options."""
        # Select case type if specified
        case_type = workflow_config.get("case_type", "all")
        if case_type != "all":
            await page.select_option('select[name*="case_type"]', case_type)
        
        # Set sorting options
        sort_option = workflow_config.get("sort_by", "date_filed")
        try:
            await page.select_option('select[name*="sort"]', sort_option)
        except Exception:
            self.log_debug(f"Could not set sort option to {sort_option}")
        
        # Configure output format
        output_format = workflow_config.get("output_format", "html")
        if output_format == "html":
            await page.check('input[type="radio"][value*="html"], input[type="radio"][value*="screen"]')

    async def _submit_report_form(self, page: Page, court_id: str) -> Dict[str, Any]:
        """Submit the report form and wait for results."""
        try:
            # Click submit button
            submit_button = page.locator('input[type="submit"], button[type="submit"]').first
            await submit_button.click()
            
            # Wait for results to load
            await page.wait_for_load_state("networkidle", timeout=self.report_timeout * 1000)
            
            return {
                "submitted": True,
                "court_id": court_id,
                "timestamp": asyncio.get_event_loop().time()
            }
            
        except Exception as e:
            raise PacerServiceError(f"Report form submission failed: {str(e)}")

    async def _extract_report_results(
        self, page: Page, court_id: str, iso_date: str, workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract data from the generated report."""
        try:
            # Check if we got results or an error page
            page_title = await page.title()
            page_content = await page.content()
            
            if "error" in page_title.lower() or "no records found" in page_content.lower():
                return {
                    "status": "no_results",
                    "court_id": court_id,
                    "cases": [],
                    "message": "No records found for the specified date range"
                }
            
            # Extract case data from the report
            cases = await self._extract_case_data(page, court_id)
            
            # Extract additional metadata
            metadata = await self._extract_report_metadata(page)
            
            return {
                "status": "success",
                "court_id": court_id,
                "iso_date": iso_date,
                "cases": cases,
                "metadata": metadata,
                "total_cases": len(cases)
            }
            
        except Exception as e:
            raise PacerServiceError(f"Failed to extract report results: {str(e)}")

    async def _extract_case_data(self, page: Page, court_id: str) -> List[Dict[str, Any]]:
        """Extract individual case data from the report."""
        cases = []
        
        try:
            # Look for case rows in the report table
            case_rows = await page.locator('tr[class*="case"], tr[onclick*="case"]').all()
            
            for i, row in enumerate(case_rows):
                try:
                    # Extract case number
                    case_number_cell = row.locator('td').first
                    case_number = await case_number_cell.inner_text()
                    
                    # Extract other case details
                    cells = await row.locator('td').all()
                    case_data = {
                        "case_number": case_number.strip(),
                        "court_id": court_id,
                        "row_index": i
                    }
                    
                    # Extract additional fields based on table structure
                    if len(cells) > 1:
                        case_data["title"] = await cells[1].inner_text()
                    if len(cells) > 2:
                        case_data["date_filed"] = await cells[2].inner_text()
                    if len(cells) > 3:
                        case_data["nature_of_suit"] = await cells[3].inner_text()
                    
                    cases.append(case_data)
                    
                except Exception as e:
                    self.log_warning(f"Failed to extract data for case row {i}: {e}")
                    continue
            
            self.log_info(f"Extracted {len(cases)} cases from report for court {court_id}")
            return cases
            
        except Exception as e:
            self.log_warning(f"Failed to extract case data: {e}")
            return []

    async def _extract_report_metadata(self, page: Page) -> Dict[str, Any]:
        """Extract metadata from the report page."""
        metadata = {}
        
        try:
            # Extract report generation timestamp
            timestamp_element = await page.get_by_text("Generated").first.inner_text()
            metadata["generated_timestamp"] = timestamp_element
        except Exception:
            pass
        
        try:
            # Extract total records count - try multiple possible text patterns
            count_locator = page.get_by_text("records found").or_(page.get_by_text("Total:"))
            count_text = await count_locator.first.inner_text()
            metadata["total_records_text"] = count_text
        except Exception:
            pass
        
        return metadata

    async def _save_report_results(
        self, court_id: str, iso_date: str, extracted_data: Dict[str, Any], workflow_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Save the extracted report results."""
        if not self.file_service:
            self.log_warning("File service not available - skipping save")
            return {"saved": False, "reason": "no_file_service"}
        
        try:
            # Prepare save path
            save_path = Path(self.config.get("data_path", "./data")) / iso_date / "reports" / f"{court_id}.json"
            
            # Save the data
            await self.file_service.save_report_data(save_path, extracted_data)
            
            return {
                "saved": True,
                "save_path": str(save_path),
                "cases_saved": len(extracted_data.get("cases", []))
            }
            
        except Exception as e:
            self.log_error(f"Failed to save report results: {e}")
            return {"saved": False, "error": str(e)}

    def _validate_report_dependencies(self) -> None:
        """Validate that required dependencies are available."""
        if not self.navigation_service:
            raise PacerServiceError("Navigation service not available")
        
        if not self.browser_manager:
            self.log_warning("Browser manager not available - context operations may be limited", {
                "component": "PacerReportWorkflowEngine._validate_report_dependencies",
                "warning_type": "MISSING_BROWSER_MANAGER"
            })

    async def _create_browser_context_if_needed(
        self, court_id: str, iso_date: str
    ) -> BrowserContext:
        """
        Create a browser context using the injected browser manager.
        
        This method should only be used if no context is provided from the calling component.
        In normal operation, contexts should be created by the JobRunnerService and passed down.
        
        Args:
            court_id: Court identifier for path configuration
            iso_date: Date string in YYYYMMDD format
            
        Returns:
            BrowserContext instance
            
        Raises:
            PacerServiceError: If browser manager is not available
        """
        if not self.browser_manager:
            raise PacerServiceError(
                "Browser manager not available for context creation. "
                "Ensure browser_manager is injected via DI container."
            )
        
        try:
            self.log_info(f"Creating browser context for court {court_id} in workflow engine", {
                "court_id": court_id,
                "iso_date": iso_date,
                "component": "PacerReportWorkflowEngine._create_browser_context_if_needed"
            })
            
            context = await self.browser_manager.create_browser_context_with_download_path(
                court_id=court_id,
                iso_date=iso_date,
                enable_downloads=True
            )
            
            if context is None:
                raise PacerServiceError(
                    f"Browser manager returned None context for court {court_id}. "
                    "This indicates a browser service or context creation failure."
                )
            
            self.log_debug(f"Successfully created browser context in workflow engine", {
                "court_id": court_id,
                "context_id": id(context),
                "context_type": type(context).__name__
            })
            
            return context
            
        except Exception as e:
            error_msg = f"Failed to create browser context for court {court_id}: {str(e)}"
            self.log_error(error_msg, {
                "court_id": court_id,
                "iso_date": iso_date,
                "error": str(e),
                "component": "PacerReportWorkflowEngine._create_browser_context_if_needed"
            })
            raise PacerServiceError(error_msg)

    async def get_active_reports(self) -> Dict[str, Dict[str, Any]]:
        """Get information about currently active reports."""
        async with self._report_lock:
            return dict(self._active_reports)

    async def cleanup_completed_reports(self, max_age_seconds: int = 3600) -> None:
        """Clean up completed report tracking data."""
        current_time = asyncio.get_event_loop().time()
        
        async with self._report_lock:
            reports_to_remove = []
            for report_id, report_data in self._active_reports.items():
                if (report_data.get("status") in ["completed", "failed"] and
                    current_time - report_data.get("start_time", 0) > max_age_seconds):
                    reports_to_remove.append(report_id)
            
            for report_id in reports_to_remove:
                del self._active_reports[report_id]
            
            if reports_to_remove:
                self.log_info(f"Cleaned up {len(reports_to_remove)} completed reports")