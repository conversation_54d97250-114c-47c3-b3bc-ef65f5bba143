# /src/pacer/pacer_browser_service.py
"""
PACER Browser Service Facade

Delegates to specialized components: <PERSON><PERSON>er<PERSON>anager, AuthenticationHandler, NavigationHandler.
Implements the Facade pattern for simplified browser operations.
"""

import logging
from pathlib import Path
from typing import Any, Dict, Optional

from playwright.async_api import Browser<PERSON>ontex<PERSON>, Page

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import PacerServiceError
from src.pacer.components.browser.authentication_handler import AuthenticationHandler
from src.pacer.components.browser.browser_manager import BrowserManager
from src.pacer.components.browser.navigation_handler import Navigation<PERSON>andler


class PacerBrowserService(AsyncServiceBase):
    """Facade for unified browser operations via component delegation."""

    def __init__(
        self,
        browser_manager: BrowserManager,
        auth_handler: AuthenticationHandler,
        navigation_handler: NavigationHandler = None,
        logger: Optional[logging.Logger] = None,
        config: Optional[Dict[str, Any]] = None,
    ):
        """Initialize facade with injected component dependencies."""
        logger_instance = (
            logger if logger else logging.getLogger(f"{__name__}.PacerBrowserService")
        )
        super().__init__(logger_instance, config or {})

        # CRITICAL FIX: Validate required dependencies
        if browser_manager is None:
            raise ValueError("BrowserManager cannot be None - DI injection failed")
        if auth_handler is None:
            raise ValueError("AuthenticationHandler cannot be None - DI injection failed")
            
        self._browser_manager = browser_manager
        self._auth_handler = auth_handler
        self._navigation_handler = navigation_handler  # Can be None, will be created at runtime

        self._current_context: Optional[BrowserContext] = None
        self._current_page: Optional[Page] = None
        self._is_authenticated = False
        self._current_court_id: Optional[str] = None

        self.log_info("PacerBrowserService facade initialized with DI validation")

    async def _execute_action(self, data: Any) -> Any:
        """Execute facade actions by routing to appropriate components."""
        if isinstance(data, dict):
            action = data.get("action")
            action_data = data.get("data", {})

            match action:
                case "browser_connect" | "connect":
                    return await self.browser_connect()
                case "create_context" | "new_context":
                    context = await self.create_context(
                        action_data.get("download_path"),
                        action_data.get("storage_state"),
                    )
                    return {
                        "status": "created",
                        "context_available": context is not None,
                    }
                case "cleanup_all":
                    await self.cleanup_all()
                    return {"status": "cleaned_up"}
                case "authenticate_session" | "login":
                    success = await self.authenticate_session(
                        action_data.get("court_id", "")
                    )
                    return {
                        "status": "authenticated" if success else "failed",
                        "success": success,
                    }
                case "navigate_to_court" | "nav_to_court":
                    success = await self.navigate_to_court(
                        action_data.get("court_id", "")
                    )
                    return {
                        "status": "navigated" if success else "failed",
                        "success": success,
                    }
                case "query_dockets":
                    return await self.query_dockets()
                case "get_status":
                    return self.get_service_status()
                case _:
                    raise PacerServiceError(
                        "Invalid action data provided to PacerBrowserService"
                    )

        raise PacerServiceError("Invalid action data provided to PacerBrowserService")

    async def browser_connect(self) -> bool:
        """Initialize browser connection via BrowserManager."""
        try:
            self.log_info("Connecting to browser")
            # Validation now done in constructor, this should never be None
            if self._browser_manager is None:
                raise RuntimeError("Browser manager is None - DI validation failed")
                
            await self._browser_manager.connect()
            status = self._browser_manager.get_connection_status()
            connected = status.get("playwright_connected", False)
            self.log_info(f"Browser connection status: {connected}, full status: {status}")
            return connected
        except Exception as e:
            self.log_error(f"Error connecting to browser: {e}", exc_info=True)
            return False

    async def create_context(
        self, download_path: Optional[str] = None, storage_state: Optional[Dict] = None
    ) -> Optional[BrowserContext]:
        """Create new browser context via BrowserManager."""
        try:
            self.log_info(f"Creating context with download_path={download_path}")
            
            # Validation is now done in constructor, but double-check for safety
            if self._browser_manager is None:
                raise RuntimeError("Browser manager is None - DI injection validation failed")
                
            if not await self.browser_connect():
                self.log_error("Browser connect failed - cannot create context")
                return None

            self.log_info("Browser connected, creating new context...")
            context = await self._browser_manager.new_context(
                download_path, storage_state
            )
            
            if context:
                self.log_info(f"Context created successfully: {type(context).__name__}")
                self._current_context = context
                self._current_page = await context.new_page()

                # CRITICAL FIX: Create NavigationHandler only when we have a valid page
                if self._navigation_handler is None and self._current_page is not None:
                    self._navigation_handler = NavigationHandler(
                        page=self._current_page,
                        config=self.config,
                        screenshot_dir=str(Path.cwd() / "screenshots"),
                        logger=self.logger,
                    )
                    self.log_info(f"NavigationHandler created successfully with page: {self._current_page.url}")
                return context
            else:
                self.log_error("Context creation returned None from browser_manager.new_context()")
                return None
        except Exception as e:
            self.log_error(f"Error creating context: {e}", exc_info=True)
            return None

    async def authenticate_session(self, court_id: str = "") -> bool:
        """Perform authentication sequence via AuthenticationHandler."""
        try:
            if not self._navigation_handler:
                return False

            court_logger = (
                logging.getLogger(f"{__name__}.{court_id}") if court_id else self.logger
            )

            if court_id:
                success = await self._auth_handler.perform_ecf_login_sequence(
                    navigator=self._navigation_handler,
                    court_id=court_id,
                    court_logger=court_logger,
                )
                if success:
                    self._is_authenticated = True
                    self._current_court_id = court_id
            else:
                success = await self._auth_handler._perform_main_pacer_login(
                    navigator=self._navigation_handler, court_logger=court_logger
                )
                if success:
                    self._is_authenticated = True
            return success
        except Exception as e:
            self.log_error(f"Authentication error: {e}")
            return False

    async def navigate_to_court(self, court_id: str) -> bool:
        """Navigate to court's ECF portal via AuthenticationHandler."""
        try:
            if not self._navigation_handler:
                return False

            court_logger = logging.getLogger(f"{__name__}.{court_id}")
            success = await self._auth_handler.navigate_to_court_ecf(
                navigator=self._navigation_handler,
                court_id=court_id,
                court_logger=court_logger,
            )
            if success:
                self._current_court_id = court_id
            return success
        except Exception as e:
            self.log_error(f"Navigation error: {e}")
            return False

    async def query_dockets(self) -> Dict[str, Any]:
        """Navigate to query page via NavigationHandler."""
        try:
            if not self._navigation_handler or not self._current_court_id:
                return {"success": False, "error": "Handler or court unavailable"}

            court_logger = logging.getLogger(f"{__name__}.{self._current_court_id}")
            success = await self._navigation_handler.navigate_to_query_page_advanced(
                court_id=self._current_court_id, court_logger=court_logger
            )
            return {
                "success": success,
                "court_id": self._current_court_id,
                "current_url": self._current_page.url if self._current_page else None,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def cleanup_all(self):
        """Clean up all resources across all components."""
        try:
            self._is_authenticated = False
            self._current_court_id = None

            if self._current_page and not self._current_page.is_closed():
                await self._current_page.close()
            self._current_page = None

            if self._current_context:
                await self._current_context.close()
            self._current_context = None
            self._navigation_handler = None

            await self._browser_manager.close()
        except Exception as e:
            self.log_error(f"Cleanup error: {e}")

    def get_service_status(self) -> Dict[str, Any]:
        """Get service status information."""
        try:
            browser_status = self._browser_manager.get_connection_status()
            return {
                "facade_status": {
                    "authenticated": self._is_authenticated,
                    "current_court": self._current_court_id,
                    "context_available": self._current_context is not None,
                    "page_available": self._current_page is not None
                    and not self._current_page.is_closed(),
                    "navigation_ready": self._navigation_handler is not None,
                },
                "browser_manager": browser_status,
                "current_url": self._current_page.url
                if (self._current_page and not self._current_page.is_closed())
                else None,
            }
        except Exception as e:
            return {"error": str(e)}

    async def initialize(self):
        """Initialize browser service."""
        self.log_info("service initialized successfully")
        return True
    
    async def cleanup(self):
        """Cleanup browser service."""
        self.log_info("service cleanup completed")
        await self.cleanup_all()

    async def __aenter__(self):
        """Async context manager entry."""
        await self.browser_connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup_all()

    @property
    def is_authenticated(self) -> bool:
        """Check if currently authenticated."""
        return self._is_authenticated

    @property
    def current_court_id(self) -> Optional[str]:
        """Get current court identifier."""
        return self._current_court_id

    @property
    def current_page(self) -> Optional[Page]:
        """Get current page instance."""
        return self._current_page

    @property
    def current_context(self) -> Optional[BrowserContext]:
        """Get current browser context."""
        return self._current_context

    @property
    def navigation_handler(self) -> Optional[NavigationHandler]:
        """Get navigation handler instance."""
        return self._navigation_handler
