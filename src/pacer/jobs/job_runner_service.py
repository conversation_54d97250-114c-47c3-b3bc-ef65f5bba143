import asyncio
import logging
from typing import Any, Dict, TYPE_CHECKING

from playwright.async_api import <PERSON>rowser<PERSON>ontext

from src.pacer.jobs.jobs_models import PacerJob
from src.pacer.pacer_browser_service import PacerBrowserService

if TYPE_CHECKING:
    from src.pacer.facades.pacer_orchestrator_facade import PacerOrchestratorService

class PacerJobRunnerService:
    """
    Service responsible for executing a single PacerJob.
    It encapsulates the logic for processing a single court within an isolated browser context.
    
    Follows dependency injection pattern - dependencies are injected via constructor.
    """

    def __init__(
        self, 
        config: Dict[str, Any], 
        logger: logging.Logger, 
        pacer_orchestrator: 'PacerOrchestratorService' = None,
        browser_service_factory: Any = None
    ):
        """
        Initializes the PacerJobRunnerService using dependency injection.

        Args:
            config: Application configuration dictionary.
            logger: Logger instance.
            pacer_orchestrator: An instance of the main PacerOrchestratorService (optional to break circular dependency).
            browser_service_factory: Factory for creating browser services (injected via DI).
        """
        self.config = config
        self.logger = logger
        self.pacer_orchestrator = pacer_orchestrator
        self.browser_service_factory = browser_service_factory
    
    def set_orchestrator(self, orchestrator: 'PacerOrchestratorService'):
        """Set the orchestrator after initialization to break circular dependency."""
        self.pacer_orchestrator = orchestrator

    async def run_job(self, job: PacerJob) -> PacerJob:
        """
        Executes a PacerJob, processing a single court.

        Args:
            job: The PacerJob instance to run.

        Returns:
            The job instance, updated with its final status, metrics, and results.
        """
        self.logger.info(f"JobRunner: Starting job {job.job_id} for court {job.court_id}")
        job.start_timer()

        browser_service = None
        context = None
        try:
            # Create a new PacerBrowserService using the injected factory for proper DI
            if self.browser_service_factory:
                # Check if browser_service_factory is already a service instance (not a factory)
                # This happens when dependency-injector resolves the factory into an instance
                if hasattr(self.browser_service_factory, 'create_context'):
                    # It's already a PacerBrowserService instance, use it directly
                    self.logger.info(f"JobRunner: Using injected browser service instance for job {job.job_id}: {type(self.browser_service_factory).__name__}")
                    browser_service = self.browser_service_factory
                else:
                    # It's a factory, call it to get a service instance
                    self.logger.info(f"JobRunner: Creating browser service for job {job.job_id} using factory: {type(self.browser_service_factory).__name__}")
                    browser_service = self.browser_service_factory()
                self.logger.info(f"JobRunner: Browser service ready: {type(browser_service).__name__}")
            else:
                # Fallback to manual creation if factory not available
                # NOTE: PacerBrowserService now requires browser_manager and auth_handler
                # This fallback won't work with the new constructor signature
                error_msg = (
                    f"Browser service factory is required for job {job.job_id}. "
                    "PacerBrowserService now requires injected dependencies "
                    "(browser_manager, auth_handler) that cannot be created manually."
                )
                self.logger.error(error_msg)
                raise RuntimeError(error_msg)
            
            # Validate browser service before proceeding
            if browser_service is None:
                error_msg = f"Failed to create browser service for job {job.job_id}. Factory: {type(self.browser_service_factory).__name__ if self.browser_service_factory else 'None'}"
                self.logger.error(error_msg)
                raise Exception(error_msg)
            
            self.logger.debug(f"JobRunner: Successfully created browser service for job {job.job_id}: {type(browser_service).__name__}")
            
            # The orchestrator is now responsible for creating the context with the correct download path
            if not self.pacer_orchestrator:
                raise RuntimeError("PacerOrchestratorService not set. Call set_orchestrator() first.")
            
            # Create browser context with download path for this specific job
            # Each job gets its own isolated browser context
            download_path = self.pacer_orchestrator.get_download_path(
                job.court_id, 
                job.iso_date, 
                "docket_log" if job.docket_list_input else "report"
            )
            
            # Note: PacerBrowserService.create_context expects download_path (not downloads_path)
            # It doesn't handle accept_downloads - that's a Playwright option we can't pass directly
            
            self.logger.info(f"JobRunner: Creating browser context for job {job.job_id} with download_path: {download_path}")
            
            # Create isolated context for this job
            # Pass download_path directly as a parameter, not as **kwargs
            context = await browser_service.create_context(download_path=str(download_path))
            
            self.logger.info(f"JobRunner: Context creation result for job {job.job_id}: {type(context).__name__ if context else 'None'}")
            
            # Validate context creation
            if context is None:
                self.logger.error(f"JobRunner: Context is None after create_context call for job {job.job_id}")
                raise Exception(f"Failed to create browser context for job {job.job_id}. Browser service: {type(browser_service).__name__}, Options: {context_options}")
            
            self.logger.debug(f"JobRunner: Successfully created browser context for job {job.job_id}")

            # The core processing logic is now encapsulated in a new method in the orchestrator
            result = await self.pacer_orchestrator.process_single_court_job(job, context)

            job.results = result
            job.metrics.update(result.get('metrics', {}))
            job.update_status("COMPLETED")
            self.logger.info(f"JobRunner: Successfully completed job {job.job_id} for court {job.court_id}")

        except Exception as e:
            self.logger.error(f"JobRunner: Unhandled exception in job {job.job_id} for court {job.court_id}: {e}", exc_info=True)
            job.set_error(f"Unhandled job execution error: {str(e)}")
        finally:
            if context:
                await context.close()
            if browser_service:
                await browser_service.close()
            job.end_timer()
            self.logger.info(f"JobRunner: Finished job {job.job_id} for court {job.court_id}. Status: {job.status}, Duration: {job.metrics.get('duration_sec', 0):.2f}s")

        return job
