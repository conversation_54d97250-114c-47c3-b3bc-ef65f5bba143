# Hybrid Legal Ad Classifier Configuration
# Usage: python src/scripts/hybrid_classifier.py --config src/config/fb_ad_categorizer/hybrid_classifier_config.yml

# Data Source
input:
  source: dynamodb                    # 'csv' or 'dynamodb'
  dynamodb_table_name: "FBAdArchive"
  local_dynamodb: true
  dynamodb_endpoint_url: "http://localhost:8000"
  start_date: null                    # YYYYMMDD format for filtering
  limit: null                         # null for no limit, or integer to limit records
  csv_file: null                      # Path to CSV file if using CSV input

# Campaign Configuration
campaigns:
  config_file: "src/config/fb_ad_categorizer/campaign_config.json"
  skip_terms_file: "src/config/fb_ad_categorizer/campaign_skip_terms.json"

# Processing Options
processing:
  batch_size: 200                    # Recommended: 256 for M4 Mac with 128GB RAM
  stages:                            # Classification stages to run
    rules_only: false                # Run only rules classification and save
    embeddings_only: false           # Run only rules + embeddings and save  
    full_pipeline: true              # Run complete pipeline with LLM
  text_fields:                        # Fields to process for rules/embeddings
    - "Title"
    - "Body" 
    - "Summary"
    - "LinkDescription"
    - "PageName"
  deduplication_fields:               # Fields used for deduplication
    - "Title"
    - "Body"
    - "Summary"

# AI Models
models:
  # Embedding Model
  embedder:
    model_name: "all-roberta-large-v1"  # Options: all-MiniLM-L6-v2 (fast), all-mpnet-base-v2 (balanced), all-roberta-large-v1 (best accuracy)
    cache_file: "embedding_roberta-large-v1.pkl"
    similarity_threshold: 0.65  # Minimum similarity score to accept embedding match (0.0-1.0)
  
  # NER Model  
  ner:
    model_name: "en_core_web_trf"      # spaCy NER model
    cache_file: "ner_cache_results.pkl"  # NER results cache file
  
  # LLM Configuration - DISABLED for overnight rules+embeddings run
  llm:
    enabled: false                     # DISABLED - no LLM processing
    backend: "ollama"                  # Options: ollama, deepseek_api, mlx, llama_cpp, transformers
    model: "deepseek-r1:8b-0528-qwen3-q8_0"                 # Model name/path or deepseek-chat for API
    api_base_url: "https://api.deepseek.com"               # API endpoint (for deepseek_api backend)
    succinct_names: true               # Make campaign names more concise
    enhanced_llm: false                # DISABLED - no enhanced post-processing
    timeout: 120                       # Timeout in seconds per request (2 minutes)
    max_retries: 3                     # Number of retries for failed requests
    retry_delay: 5                     # Delay between retries in seconds

    cache_file: "llm_response_cache.pkl"
# Output
output:
  csv_file: "classified_ads_results.csv"
  deduplicated_csv_file: "classified_ads_results_dedup.csv"
  
# Rule Improvement
rules:
  improve_rules: true
  output_suggestions: "rule_suggestions.json"            # Path to save rule suggestions, or null

# Logging
logging:
  level: "INFO"                       # DEBUG, INFO, WARNING, ERROR, CRITICAL

# AWS Settings (if using remote DynamoDB)
aws:
  region: "us-west-2"

#   export OLLAMA_NUM_PARALLEL=8          # Increase from 4 to 8
#   export OLLAMA_NUM_GPU_LAYERS=99        # Use all GPU layers
#   export OLLAMA_MAX_LOADED_MODELS=3      # Increase from 2 to 3
#   export OLLAMA_FLASH_ATTENTION=1        # Enable flash attention

# export OLLAMA_NUM_PARALLEL=8 OLLAMA_NUM_GPU_LAYERS=99 OLLAMA_MAX_LOADED_MODELS=3 OLLAMA_FLASH_ATTENTION=1
#  ollama serve > ollama.log 2>&1 &