# Facebook Ads Processing Configuration
# This file configures the main processing pipeline including deferred image processing

# Main Processing Settings
processing:
  # Enable deferred image processing for faster scraping
  defer_image_processing: true
  
  # Local queue settings
  image_queue:
    enabled: true
    directory: "./data/image_queue"
    cleanup_after_days: 7
    
  # Processing modes
  modes:
    fast_scrape:
      defer_images: true
      skip_llava: true
      parallel_firms: 5
      session_refresh_interval: 25
      
    full_process:
      defer_images: false
      skip_llava: false
      parallel_firms: 3
      session_refresh_interval: 10
      
    image_only:
      process_queue_only: true
      skip_scraping: true

# Vision Model Configuration
vision_models:
  # Default model for image text extraction
  default: "llama3.2-vision:11b-instruct-q4_K_M"
  
  # Ollama server settings
  ollama:
    base_url: "http://localhost:11434"
    keep_alive: -1  # Keep models loaded
    num_parallel: 4  # Adjust based on GPU
    
  # Available models
  models:
    - name: "llama3.2-vision:11b"
      type: "full"
      vram_required: 16
      accuracy: "highest"
      speed: "slow"
      use_for: ["legal_documents", "complex_layouts", "handwriting"]
      
    - name: "llama3.2-vision:11b-instruct-q4_K_M"
      type: "quantized"
      vram_required: 8
      accuracy: "high"
      speed: "fast"
      use_for: ["production", "standard_ads", "bulk_processing"]
      
    - name: "llava:7b"
      type: "legacy"
      vram_required: 6
      accuracy: "medium"
      speed: "fast"
      use_for: ["simple_text", "low_resource"]

# Performance Tuning
performance:
  # GPU-based settings (auto-detected or manual)
  gpu_profiles:
    "8GB":
      vision_concurrency: 4
      ollama_parallel: 2
      batch_size: 20
      model: "llama3.2-vision:11b-instruct-q4_K_M"
      
    "16GB":
      vision_concurrency: 6
      ollama_parallel: 3
      batch_size: 25
      model: "llama3.2-vision:11b-instruct-q4_K_M"
      
    "24GB":
      vision_concurrency: 8
      ollama_parallel: 4
      batch_size: 30
      model: "llama3.2-vision:11b"
      
    "40GB+":
      vision_concurrency: 12
      ollama_parallel: 6
      batch_size: 40
      model: "llama3.2-vision:11b"

  # Queue processing settings
  queue_processing:
    max_concurrent_downloads: 10
    max_concurrent_vision: 6
    max_concurrent_db_writes: 16
    s3_download_timeout: 30
    vision_request_timeout: 1200
    
  # Memory management
  memory:
    max_items_in_memory: 1000
    garbage_collection_interval: 100
    low_memory_threshold: 0.8

# Summary Update Configuration
summary_updates:
  enabled: true
  
  # When to update summaries
  update_conditions:
    - classification_not_in: ["NA", "SKIPPED", "ERROR"]
    - confidence_between: [0.7, 0.95]
    - summary_not_contains: ["[Image Text]", "image text:", "Image content:"]
    
  # Update strategy
  strategy:
    mode: "selective"  # or "all"
    batch_size: 50
    use_ai_model: "gpt-4"
    fallback_model: "deepseek"
    
  # Scheduling
  schedule:
    auto_update: false
    update_time: "02:00"
    update_days: ["Sunday", "Wednesday"]

# Queue Management
queue_management:
  # Monitoring
  monitoring:
    check_interval_minutes: 5
    alert_thresholds:
      pending_items: 10000
      failed_items: 100
      processing_time_hours: 24
      
  # Cleanup
  cleanup:
    remove_processed_after_days: 7
    archive_failed_after_days: 30
    export_before_cleanup: true
    export_path: "./exports/queue_archives"
    
  # Recovery
  recovery:
    auto_retry_failed: true
    retry_interval_hours: 6
    max_retry_attempts: 3
    
# Integration Settings
integration:
  # S3 settings
  s3:
    image_prefix: "adarchive/fb"
    parallel_uploads: 5
    use_acceleration: false
    
  # DynamoDB settings
  dynamodb:
    batch_write_size: 25
    read_capacity: 10
    write_capacity: 10
    use_on_demand: true
    
  # Local development
  local_dev:
    use_local_dynamo: true
    local_dynamo_port: 8000
    use_local_s3: false
    mock_vision_api: false

# Logging Configuration
logging:
  # Component-specific log levels
  components:
    processor: INFO
    image_handler: INFO
    local_queue: DEBUG
    vision_extractor: INFO
    queue_processor: DEBUG
    
  # Performance logging
  performance:
    log_processing_times: true
    log_memory_usage: true
    log_gpu_stats: true
    stats_interval_seconds: 60
    
  # Error tracking
  errors:
    capture_full_stack: true
    log_to_file: true
    error_log_path: "./logs/fb_ads_errors.log"

# Command Shortcuts
shortcuts:
  # Fast daily scrape
  daily_scrape:
    command: "python orchestrator.py --run --date {date}"
    config_overrides:
      defer_image_processing: true
      session_refresh_interval: 25
      
  # Process image queue
  process_images:
    command: "python src/scripts/process_image_queue.py --process --date {date}"
    config_overrides:
      vision_concurrency: 8
      batch_size: 30
      
  # Update summaries
  update_summaries:
    command: "python src/scripts/update_summaries_with_images.py --date {date}"
    config_overrides:
      batch_size: 50
      dry_run_first: true