# /src/services/transformer/data_transformer.py
# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""Refactored DataTransformer module - Phase 3 async-first architecture.

This is the refactored version of transformer.py with functionality broken down
into focused modules for better maintainability and testability.
"""
# Removed dependency_injector imports - using container-based injection
import asyncio
import logging
import os
import time
from dataclasses import dataclass, field  # Ensure 'Any' and 'field' are imported
from typing import Dict, Optional, List, Set, Union, \
    Any  # Ensure 'Any' and 'field' are imported

# Third-Party Imports
import aiofiles  # For async file operations
# Utilities
from rich.console import Console
from tqdm.asyncio import tqdm as tqdm_async

# Direct async imports - no compatibility layer needed
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import TransformerServiceError
# Local Imports - New modular architecture
from .component_factory import ComponentFactory
from ._file_components.file_operations_manager import FileOperationsManager
from .data_processing_engine import DataProcessingEngine
from .specialized_workflows import SpecializedWorkflows
from .error_handler import <PERSON>rror<PERSON>and<PERSON>
from .jobs.job_orchestration_service import JobOrchestrationService
from .jobs.job_runner_service import JobRunnerService

# Existing components
# Infrastructure imports

# Feature flags
from src.infrastructure.monitoring.performance_monitor import \
    monitor_async_performance, get_performance_monitor

# PROJECT_ROOT will be provided via config injection


from src.services.transformer.jobs.job_models import TransformationJob


class Reporter:
    """Simple reporter for processing results."""

    def __init__(self):
        self.console = Console()

    def generate_summary(self, process_summary: List[Dict],
                         upload_results: Dict[str, List], initial_file_count: int):
        """Generate and display processing summary."""
        success_count = sum(
            1 for result in process_summary if result.get('status') == 'success')
        error_count = len(process_summary) - success_count

        self.console.print(f"\n[bold green]Processing Summary[/bold green]")
        self.console.print(f"Total files processed: {len(process_summary)}")
        self.console.print(f"Successful: {success_count}")
        self.console.print(f"Errors: {error_count}")

        if upload_results:
            uploaded_count = sum(len(files) for files in upload_results.values())
            self.console.print(f"Files uploaded: {uploaded_count}")


class DataTransformer(AsyncServiceBase):
    """
    Refactored DataTransformer with modular async-first architecture.

    This version breaks down the monolithic transformer.py into focused modules:
    - ComponentFactory: Component initialization and management
    - DataProcessingEngine: Core data processing and enrichment
    - FileOperationsManager: File operations and management
    - SpecializedWorkflows: Specialized processing modes
    - ErrorHandler: Centralized error handling
    """

    def __init__(self,
                 config: Dict,
                 logger: logging.Logger,
                 openai_client: Optional[Any],
                 deepseek_service: Optional[Any],
                 mistral_service: Optional[Any],
                 shutdown_event: Optional[asyncio.Event],
                 s3_manager: Optional[Any] = None,
                 pacer_repository: Optional[Any] = None,
                 district_courts_repository: Optional[Any] = None,
                 html_integration_service: Optional[Any] = None,
                 file_handler: Optional[Any] = None,
                 docket_processor: Optional[Any] = None,
                 mdl_processor: Optional[Any] = None,
                 law_firm_processing_service: Optional[Any] = None,
                 case_classification_service: Optional[Any] = None,
                 data_cleaning_service: Optional[Any] = None,
                 data_upload_service: Optional[Any] = None):
        """Initialize DataTransformer with modular architecture."""
        # Initialize AsyncServiceBase
        logger_instance = logger if logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config)

        self.shutdown_event = shutdown_event
        self.start_time = time.time()
        # Use module-specific logger to ensure transformer logs go to transformer.log
        self.logger = logging.getLogger(__name__)
        self._setup_transformer_logging()
        self.docket_summary: Optional[List[Dict]] = None
        self._initialized = False

        # Core configuration
        project_root = config.get('directories', {}).get('base_dir')
        if not project_root:
            raise TransformerServiceError(
                "Project root directory not provided in config. Required: config.directories.base_dir")
        self.mdl_path = config.get('files', {}).get('mdl_lookup',
                                                    os.path.join(project_root, 'src',
                                                                 'config', 'mdl',
                                                                 'mdl_lookup.json'))

        # Initialize component factory with injected clients
        self.component_factory = ComponentFactory(config, logger,
                                                  openai_client=openai_client,
                                                  deepseek_service=deepseek_service,
                                                  mistral_service=mistral_service)

        # Store injected core components
        self.file_handler = file_handler
        self.docket_processor = docket_processor
        self.mdl_processor = mdl_processor
        self.deepseek_service = deepseek_service
        self.law_firm_processing_service = law_firm_processing_service
        self.case_classification_service = case_classification_service
        self.data_cleaning_service = data_cleaning_service
        self.data_upload_service = data_upload_service
        self.llm_client = None  # Will be set from component factory

        # Initialize modular components (will be set in async_init)
        self.data_processing_engine = None
        self.file_operations_manager = None
        self.specialized_workflows = None
        self.error_handler = None
        self.job_runner_service = None
        self.job_orchestration_service = None

        # Store config for lazy initialization in _initialize_service
        self._s3_config = {
            'bucket_name': config.get('s3_bucket_name') or config.get(
                'bucket_name') or os.environ.get(
                'S3_BUCKET_NAME'),
            'aws_access_key': config.get('aws_access_key') or os.environ.get(
                'AWS_ACCESS_KEY_ID', ''),
            'aws_secret_key': config.get('aws_secret_key') or os.environ.get(
                'AWS_SECRET_ACCESS_KEY', ''),
            'aws_region': config.get('aws_region')
        }

        # Use injected services or initialize lazily
        self.s3_manager = s3_manager
        self._async_storage = None
        self._pacer_repo = pacer_repository
        self._district_courts_repo = district_courts_repository
        self.pacer_db = pacer_repository
        self.district_court_db = district_courts_repository
        self.html_integration_service = html_integration_service

    def _setup_transformer_logging(self):
        """Setup transformer-specific logging to ensure logs go to transformer.log."""
        # Get the transformer log file path
        iso_date = self.config.get('iso_date', 'default')
        log_dir = self.config.get('directories', {}).get(
            'log_dir',
            os.path.join(self.config.get('DATA_DIR', os.path.join(os.getcwd(), 'data')),
                         f"{iso_date}/logs")
        )

        # Ensure log directory exists
        os.makedirs(log_dir, exist_ok=True)
        transformer_log_file = os.path.join(log_dir, 'transformer.log')

        # Clear existing handlers to avoid duplicates
        self.logger.handlers.clear()

        # Standard formatter including module and function information
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # Create file handler
        file_handler = logging.FileHandler(transformer_log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # Create console handler for immediate feedback with rich formatting
        from rich.logging import RichHandler
        console_handler = RichHandler(rich_tracebacks=True, show_path=False)
        console_handler.setLevel(logging.INFO)
        # Rich handler doesn't need the formatter - it handles formatting internally

        # Add handlers to logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        self.logger.setLevel(logging.DEBUG)
        self.logger.propagate = False

        # Test logging immediately
        self.log_info("=== Transformer logging initialized ===")
        self.log_info(f"Log file: {transformer_log_file}")
        self.log_debug("Debug logging is working")

        # Log initialization using AsyncServiceBase methods
        self.log_info(
            "DataTransformer initialized with ComponentImplementation pattern")

    async def _initialize_modular_components(self):
        """Initialize modular components using injected dependencies."""
        self.log_debug("Initializing modular components...")

        # The core components (docket_processor, mdl_processor, file_handler) are now injected
        # We can create the modular components that depend on them

        # Initialize LLM client from component factory if not already set
        if self.llm_client is None and self.component_factory:
            try:
                self.llm_client = self.component_factory.get_llm_client('deepseek')
                self.log_info("LLM client initialized from component factory")
            except Exception as e:
                self.log_warning(
                    f"Could not initialize LLM client from component factory: {e}")

        # Initialize DataProcessingEngine using injected core components
        if self.docket_processor and self.mdl_processor:
            self.data_processing_engine = DataProcessingEngine(
                docket_processing_service=self.docket_processor,
                mdl_processing_service=self.mdl_processor,
                file_processing_service=self.file_handler,
                law_firm_processing_service=self.law_firm_processing_service,
                case_classification_service=self.case_classification_service,
                data_cleaning_service=self.data_cleaning_service,
                data_upload_service=self.data_upload_service,
                llm_client=self.llm_client,
                logger=self.logger,
                config=self.config,
                html_integration_service=self.html_integration_service,
                deepseek_service=self.deepseek_service
            )
            self.log_debug(
                "DataProcessingEngine initialized with injected dependencies")
        else:
            self.log_warning(
                f"Cannot initialize DataProcessingEngine - docket_processor: {self.docket_processor is not None}, mdl_processor: {self.mdl_processor is not None}")

        # Initialize FileOperationsManager using injected file_handler
        if self.file_handler:
            self.file_operations_manager = FileOperationsManager(
                file_handler=self.file_handler,
                config=self.config,
                logger=self.logger
            )
            self.log_debug(
                "FileOperationsManager initialized with injected file_handler")
        else:
            self.log_warning(
                "Cannot initialize FileOperationsManager - file_handler not injected")

        # Initialize SpecializedWorkflows if available
        # Note: This component may not exist yet or may need different initialization
        if hasattr(self, 'specialized_workflows_factory'):
            try:
                # Placeholder for when SpecializedWorkflows is implemented
                self.specialized_workflows = None
                self.log_debug("SpecializedWorkflows placeholder set")
            except Exception as e:
                self.log_warning(f"Could not initialize SpecializedWorkflows: {e}")
        else:
            self.specialized_workflows = None
            self.log_debug("SpecializedWorkflows not available")

        # Initialize ErrorHandler if available
        try:
            from .error_handler import ErrorHandler
            if self.file_handler:
                self.error_handler = ErrorHandler(
                    file_handler=self.file_handler,
                    config=self.config,
                    logger=self.logger
                )
                self.log_debug("ErrorHandler initialized")
            else:
                self.error_handler = None
                self.log_debug(
                    "ErrorHandler not initialized - file_handler not available")
        except ImportError:
            self.error_handler = None
            self.log_debug("ErrorHandler not available")

        # Initialize JobRunnerService
        self.job_runner_service = JobRunnerService(
            file_handler=self.file_handler,
            docket_processor=self.docket_processor,
            file_operations_manager=self.file_operations_manager,
            data_processing_engine=self.data_processing_engine,
            error_handler=self.error_handler,
            shutdown_event=self.shutdown_event,
            logger=self.logger,
        )
        self.log_debug("JobRunnerService initialized")

        # Initialize JobOrchestrationService
        self.job_orchestration_service = JobOrchestrationService(
            job_runner_service=self.job_runner_service,
            file_operations_manager=self.file_operations_manager,
            shutdown_event=self.shutdown_event,
            logger=self.logger,
        )
        self.log_debug("JobOrchestrationService initialized")

        self.log_info("Modular components initialization completed")

    async def _execute_action(self, data: Any) -> Any:
        """Execute DataTransformer actions."""
        if isinstance(data, dict):
            action = data.get('action')
            action_data = data.get('data', {})

            match action:
                case 'start':
                    upload = action_data.get('upload', False)
                    reprocess_files = action_data.get('reprocess_files', False)
                    start_from_incomplete = action_data.get('start_from_incomplete', False)
                    skip_files = action_data.get('skip_files', set())
                    force_reprocess = action_data.get('force_reprocess', False)
                    num_workers = action_data.get('num_workers', 4)
                    run_law_firm_normalization_only = action_data.get(
                        'run_law_firm_normalization_only', False)
                    run_mdl_title_update_only = action_data.get('run_mdl_title_update_only',
                                                                False)
                    post_process = action_data.get('post_process', False)
                    return await self.start(
                        upload, reprocess_files, start_from_incomplete, skip_files,
                        force_reprocess, num_workers, run_law_firm_normalization_only,
                        run_mdl_title_update_only, post_process
                    )
                case 'process_federal_filings':
                    reprocess_files = action_data.get('reprocess_files', False)
                    start_from_incomplete = action_data.get('start_from_incomplete', False)
                    skip_files = action_data.get('skip_files', set())
                    force_reprocess = action_data.get('force_reprocess', False)
                    num_workers = action_data.get('num_workers', 4)
                    return await self.process_federal_filings_async(
                        reprocess_files, start_from_incomplete, skip_files, force_reprocess,
                        num_workers
                    )
                case 'initialize_async':
                    await self.async_init()
                    return {'status': 'initialized'}
                case 'get_initialization_status':
                    return {'initialized': self._initialized}
                case 'get_processing_summary':
                    return self.get_processing_summary()
                case 'log_docket_summary':
                    self.log_docket_summary()
                    return {'status': 'logged'}
                case 'cleanup_resources':
                    await self.cleanup_resources()
                    return {'status': 'cleaned'}
                case 'get_component_status':
                    return self.get_component_status()
                case 'validate_configuration':
                    return self.validate_configuration()
                case 'run_law_firm_normalization':
                    num_workers = action_data.get('num_workers', 4)
                    return await self.specialized_workflows.run_law_firm_normalization_only(
                        num_workers)
                case 'run_mdl_title_update':
                    num_workers = action_data.get('num_workers', 4)
                    return await self.specialized_workflows.run_mdl_title_update_only(
                        num_workers)
                case 'process_transformation_job':
                    job_data = action_data.get('job_data', {})
                    job = TransformationJob(**job_data)
                    return await self.job_runner_service.run_job_async(job)
                case 'get_modular_components_summary':
                    return self.get_modular_components_summary()
                case 'validate_async_storage':
                    return await self.validate_async_storage()
                case 'get_processing_statistics':
                    return self.get_processing_statistics()
                case _:
                    raise TransformerServiceError(f"Invalid action '{action}' provided to DataTransformer")
        raise TransformerServiceError("Invalid action data provided to DataTransformer")

    async def async_init(self):
        """Initialize async resources."""
        if self._initialized:
            self.log_debug("DataTransformer already initialized, skipping")
            return

        self.log_info("Starting DataTransformer async initialization...")
        start_time = time.time()

        # Initialize S3 manager context
        self.log_debug("Initializing S3 manager...")
        s3_start = time.time()
        await self.s3_manager.__aenter__()
        self.log_debug(f"S3 manager initialized in {time.time() - s3_start:.2f}s")

        # Initialize HTML integration service
        if hasattr(self.html_integration_service, 'initialize'):
            await self.html_integration_service.initialize(self.s3_manager,
                                                           self.pacer_db)

        # Load MDL data if needed
        if self.mdl_processor and hasattr(self.mdl_processor, 'utils'):
            if not hasattr(self.mdl_processor.utils, 'mdl_litigations') or self.mdl_processor.utils.mdl_litigations is None:
                mdl_litigations_df = self.component_factory.load_mdl_data(self.mdl_path)
                self.mdl_processor.utils.mdl_litigations = mdl_litigations_df
        
        # Also ensure MDL data is available in utils for DataProcessingEngine
        if self.mdl_processor and hasattr(self.mdl_processor, 'utils'):
            if not hasattr(self.mdl_processor.utils, 'mdl_litigations') or self.mdl_processor.utils.mdl_litigations is None:
                try:
                    self.log_info(f"Loading MDL litigation data for utils from {self.mdl_path}")
                    mdl_litigations_df = self.component_factory.load_mdl_data(self.mdl_path)
                    self.mdl_processor.utils.mdl_litigations = mdl_litigations_df
                    self.log_info(f"Successfully loaded {len(mdl_litigations_df)} MDL litigation entries into utils")
                except Exception as e:
                    self.log_warning(f"Failed to load MDL data into utils: {e}")

        # Initialize modular components
        await self._initialize_modular_components()

        self._initialized = True
        total_time = time.time() - start_time
        self.log_info(
            f"DataTransformer async initialization completed in {total_time:.2f}s")

    async def __aenter__(self):
        """Async context manager entry."""
        await self.async_init()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        # Cleanup resources if needed
        if self._async_storage and hasattr(self._async_storage, 'close'):
            await self._async_storage.close()

        # Close S3 manager
        if self.s3_manager:
            try:
                await self.s3_manager.__aexit__(None, None, None)
                self.log_debug("S3 manager closed successfully")
            except Exception as e:
                self.log_warning(f"Error closing S3 manager: {e}")

        # Close LLM client session if it has a close method
        if self.llm_client and hasattr(self.llm_client, 'close_session'):
            try:
                await self.llm_client.close_session()
                self.log_debug("LLM client session closed successfully")
            except Exception as e:
                self.log_warning(f"Error closing LLM client session: {e}")

    async def start(self,
                    upload: bool = False,
                    reprocess_files: Union[bool, List[str], Set[str]] = False,
                    start_from_incomplete: bool = False,
                    skip_files: Set[str] = None,
                    force_reprocess: bool = False,
                    num_workers: int = 4,
                    run_law_firm_normalization_only: bool = False,
                    run_mdl_title_update_only: bool = False,
                    post_process: bool = False
                    ) -> List[Dict]:
        """
        Start the data transformation process with modular architecture.
        """
        await self.async_init()

        if self._check_shutdown():
            self.log_info("Shutdown requested before starting any workflow.")
            return []

        if run_law_firm_normalization_only:
            return await self.specialized_workflows.run_law_firm_normalization_only(num_workers)

        if run_mdl_title_update_only:
            return await self.specialized_workflows.run_mdl_title_update_only(num_workers)

        return await self.job_orchestration_service.run_jobs_async(
            reprocess_files=reprocess_files,
            start_from_incomplete=start_from_incomplete,
            skip_files=skip_files,
            force_reprocess=force_reprocess,
            num_workers=num_workers,
            transformer=self,
        )

    async def process_federal_filings_async(self,
                                            reprocess_files: Union[
                                                bool, List[str], Set[str]] = False,
                                            start_from_incomplete: bool = False,
                                            skip_files: Set[str] = None,
                                            force_reprocess: bool = False,
                                            num_workers: int = 4) -> List[Dict]:
        """
        Process federal filings with async concurrency control.

        Args:
            reprocess_files: Files to reprocess
            start_from_incomplete: Whether to start from incomplete files
            skip_files: Set of files to skip
            force_reprocess: Whether to force reprocessing
            num_workers: Number of concurrent workers

        Returns:
            List of processing results
        """
        results = await self.job_orchestration_service.run_jobs_async(
            reprocess_files=reprocess_files,
            start_from_incomplete=start_from_incomplete,
            skip_files=skip_files,
            force_reprocess=force_reprocess,
            num_workers=num_workers,
            transformer=self,
        )
        self.docket_summary = results
        return results

    @staticmethod
    def _format_elapsed_time(seconds: float) -> str:
        """Format elapsed time in human-readable format."""
        match seconds:
            case s if s < 60:
                return f"{s:.1f}s"
            case s if s < 3600:
                minutes = int(s // 60)
                secs = int(s % 60)
                return f"{minutes}m {secs}s"
            case s:
                hours = int(s // 3600)
                minutes = int((s % 3600) // 60)
                return f"{hours}h {minutes}m"

    def log_docket_summary(self):
        """Log processing summary."""
        if not self.docket_summary:
            self.log_info("No docket summary available")
            return

        success_count = sum(
            1 for item in self.docket_summary if item.get('status') == 'success')
        total_count = len(self.docket_summary)
        elapsed_time = time.time() - self.start_time

        self.log_info(f"Processing Summary:")
        self.log_info(f"  Total files: {total_count}")
        self.log_info(f"  Successful: {success_count}")
        self.log_info(f"  Failed: {total_count - success_count}")
        self.log_info(f"  Elapsed time: {self._format_elapsed_time(elapsed_time)}")

    def _check_shutdown(self) -> bool:
        """No graceful shutdown - always return False."""
        return False

    # === ComponentImplementation Helper Methods ===

    def get_processing_summary(self) -> Dict[str, Any]:
        """Get comprehensive processing summary."""
        if not self.docket_summary:
            return {'status': 'no_data', 'message': 'No processing summary available'}

        success_count = sum(
            1 for item in self.docket_summary if item.get('status') == 'success')
        total_count = len(self.docket_summary)
        elapsed_time = time.time() - self.start_time

        return {
            'total_files': total_count,
            'successful': success_count,
            'failed': total_count - success_count,
            'elapsed_time': self._format_elapsed_time(elapsed_time),
            'success_rate': (
                    success_count / total_count * 100) if total_count > 0 else 0,
            'status': 'complete'
        }

    async def cleanup_resources(self):
        """Clean up resources and close connections."""
        try:
            await self.__aexit__(None, None, None)
            self.log_info("Resources cleaned up successfully")
        except Exception as e:
            self.log_error(f"Error during resource cleanup: {e}")

    def get_component_status(self) -> Dict[str, Any]:
        """Get status of all components."""
        return {
            'initialized': self._initialized,
            'core_components': {
                'file_handler': self.file_handler is not None,
                'docket_processor': self.docket_processor is not None,
                'mdl_processor': self.mdl_processor is not None,
                'llm_client': self.llm_client is not None,
                's3_manager': self.s3_manager is not None
            },
            'modular_components': {
                'data_processing_engine': self.data_processing_engine is not None,
                'file_operations_manager': self.file_operations_manager is not None,
                'specialized_workflows': self.specialized_workflows is not None,
                'error_handler': self.error_handler is not None,
                'html_integration_service': hasattr(self,
                                                    'html_integration_service') and self.html_integration_service is not None
            },
            'async_storage': {
                'async_storage': self._async_storage is not None,
                'pacer_repo': self._pacer_repo is not None,
                'district_courts_repo': self._district_courts_repo is not None
            }
        }

    def validate_configuration(self) -> Dict[str, Any]:
        """Validate transformer configuration."""
        validation_results = {
            'config_valid': isinstance(self.config, dict),
            'mdl_path_exists': os.path.exists(
                self.mdl_path) if self.mdl_path else False,
            'required_keys': {},
            'status': 'valid'
        }

        # Check required configuration keys
        required_keys = ['directories', 'aws_region']
        for key in required_keys:
            validation_results['required_keys'][key] = key in self.config

        # Determine overall validity
        if not all([
            validation_results['config_valid'],
            validation_results['mdl_path_exists'],
            all(validation_results['required_keys'].values())
        ]):
            validation_results['status'] = 'invalid'

        return validation_results

    def get_modular_components_summary(self) -> Dict[str, Any]:
        """Get summary of modular components."""
        components = {}

        if self.data_processing_engine:
            components['data_processing_engine'] = {
                'type': type(self.data_processing_engine).__name__,
                'available': True
            }

        if self.file_operations_manager:
            components['file_operations_manager'] = {
                'type': type(self.file_operations_manager).__name__,
                'available': True
            }

        if self.specialized_workflows:
            components['specialized_workflows'] = {
                'type': type(self.specialized_workflows).__name__,
                'available': True
            }

        if self.error_handler:
            components['error_handler'] = {
                'type': type(self.error_handler).__name__,
                'available': True
            }

        if hasattr(self, 'html_integration_service') and self.html_integration_service:
            components['html_integration_service'] = {
                'type': type(self.html_integration_service).__name__,
                'available': True
            }

        return {
            'component_count': len(components),
            'components': components,
            'all_initialized': self._initialized
        }

    async def validate_async_storage(self) -> Dict[str, Any]:
        """Validate async storage connections."""
        validation_results = {
            'async_storage_available': self._async_storage is not None,
            'pacer_repo_available': self._pacer_repo is not None,
            'district_courts_repo_available': self._district_courts_repo is not None,
            's3_manager_available': self.s3_manager is not None,
            'status': 'valid'
        }

        # Test basic connectivity if available
        if self._async_storage:
            try:
                # Basic connectivity test (if the storage has a health check method)
                validation_results['storage_connectivity'] = 'available'
            except Exception as e:
                validation_results['storage_connectivity'] = f'error: {e}'
                validation_results['status'] = 'invalid'

        return validation_results

    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get detailed processing statistics."""
        if not self.docket_summary:
            return {'status': 'no_data', 'message': 'No processing data available'}

        stats = {
            'total_jobs': len(self.docket_summary),
            'status_breakdown': {},
            'processing_times': [],
            'average_processing_time': 0,
            'total_processing_time': 0
        }

        # Analyze job statuses
        for job in self.docket_summary:
            status = job.get('status', 'unknown')
            stats['status_breakdown'][status] = stats['status_breakdown'].get(status,
                                                                              0) + 1

            # Collect processing times
            processing_time = job.get('processing_time', 0)
            if processing_time > 0:
                stats['processing_times'].append(processing_time)

        # Calculate averages
        if stats['processing_times']:
            stats['average_processing_time'] = sum(stats['processing_times']) / len(
                stats['processing_times'])
            stats['total_processing_time'] = sum(stats['processing_times'])
            stats['min_processing_time'] = min(stats['processing_times'])
            stats['max_processing_time'] = max(stats['processing_times'])

        return stats
