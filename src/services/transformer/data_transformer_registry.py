# /src/services/transformer/data_transformer_registry.py
"""
Migrated version of DataTransformer using action registry pattern.

This is the main orchestrator for the data transformation pipeline,
managing all components and workflows through the action registry.
"""
import asyncio
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from src.services.transformer.action_registry import ActionHandler, action
from src.services.transformer.constants import JobStatus
from src.infrastructure.protocols.exceptions import TransformerServiceError
from src.services.transformer.jobs.job_models import TransformationJob
from src.services.transformer.exceptions import (
    ConfigurationException, DependencyException, ProcessingException,
    WorkflowException, ResourceException, wrap_processing_error,
    raise_if_missing_config
)

# Import all necessary components (preserving original imports)
from src.services.transformer.component_factory import ComponentFactory
from src.services.transformer.data_processing_engine import DataProcessingEngine
from src.services.transformer.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.services.transformer.file_processing_service import FileProcessingService
from src.services.transformer._file_components.file_operations_manager import FileOperationsManager
from src.services.transformer.jobs.job_orchestration_service import JobOrchestrationService
from src.services.transformer.jobs.job_runner_service import JobRunnerService
from src.services.transformer.mdl_processing_service import MDLProcessingService
from src.services.transformer.specialized_workflows import SpecializedWorkflows
from src.services.transformer.docket_processing_service import DocketProcessingService
from src.services.transformer.html_integration_service import HTMLIntegrationService

# Import storage and repository components
from src.services.storage.async_dynamodb_storage import AsyncDynamoDBStorage
from src.services.storage.s3_manager import S3Manager
from src.repositories.dynamodb.pacer_repository_async import PacerRepositoryAsync
from src.repositories.dynamodb.district_courts_repository_async import DistrictCourtsRepositoryAsync


class DataTransformerRegistry(ActionHandler):
    """
    Main orchestrator for data transformation pipeline using action registry pattern.
    
    This service coordinates all transformation components and manages the
    overall workflow through registered actions.
    """
    
    def __init__(self,
                 config: Dict[str, Any],
                 transformer_logger: Optional[logging.Logger] = None,
                 llm_clients: Optional[Dict[str, Any]] = None):
        """
        Initialize DataTransformer with action registry.
        
        Args:
            config: Configuration dictionary
            transformer_logger: Optional logger instance
            llm_clients: Optional pre-initialized LLM clients
        """
        # Initialize ActionHandler first
        logger_instance = transformer_logger if transformer_logger else logging.getLogger(__name__)
        super().__init__(logger_instance, config)
        
        # Store initialization parameters
        self._transformer_logger = transformer_logger
        self._llm_clients = llm_clients or {}
        
        # Initialize instance variables (from original)
        self.shutdown_event = asyncio.Event()
        self.start_time = time.time()
        
        self.logger = self._setup_transformer_logging(transformer_logger)
        
        self.docket_summary = {'total': 0, 'relevant': 0}
        self._initialized = False
        
        # Configuration paths
        if "mdl_lookup" in config:
            self.mdl_path = Path(config["mdl_lookup"])
        else:
            self.mdl_path = Path("config/data/mdl/mdl_lookup.json")
        
        # Component placeholders
        self.component_factory: Optional[ComponentFactory] = None
        self.file_handler: Optional[FileHandler] = None
        self.docket_processor: Optional[DocketProcessor] = None
        self.mdl_processor: Optional[MdlProcessor] = None
        self.llm_client: Optional[Any] = None
        
        # Modular components
        self.data_processing_engine: Optional[DataProcessingEngine] = None
        self.file_operations_manager: Optional[FileOperationsManager] = None
        self.specialized_workflows: Optional[SpecializedWorkflows] = None
        self.error_handler: Optional[ErrorHandler] = None
        self.job_runner_service: Optional[JobRunnerService] = None
        self.job_orchestration_service: Optional[JobOrchestrationService] = None
        
        # Storage configuration
        self._s3_config = {
            'bucket_name': config.get('bucket_name', 'lexgenius-json'),
            'region': config.get('aws_region', 'us-west-2')
        }
        
        # Storage components
        self.s3_manager: Optional[S3Manager] = None
        self._async_storage: Optional[AsyncDynamoDBStorage] = None
        self._pacer_repo: Optional[PacerRepositoryAsync] = None
        self._district_courts_repo: Optional[DistrictCourtsRepositoryAsync] = None
        self.pacer_db: Optional[Any] = None  # Legacy reference
        self.district_court_db: Optional[Any] = None  # Legacy reference
        self.html_integration_service: Optional[HTMLIntegrationService] = None
    
    # Action handlers with @action decorator
    
    @action('start')
    async def handle_start(self, data: Dict[str, Any]) -> Any:
        """Start the transformation process action handler."""
        return await self.start(
            upload=data.get('upload', False),
            reprocess_files=data.get('reprocess_files', False),
            start_from_incomplete=data.get('start_from_incomplete', False),
            skip_files=data.get('skip_files', set()),
            force_reprocess=data.get('force_reprocess', False),
            num_workers=data.get('num_workers', 4),
            run_law_firm_normalization_only=data.get('run_law_firm_normalization_only', False),
            run_mdl_title_update_only=data.get('run_mdl_title_update_only', False),
            post_process=data.get('post_process', False)
        )
    
    @action('process_federal_filings')
    async def handle_process_federal_filings(self, data: Dict[str, Any]) -> Any:
        """Process federal filings action handler."""
        return await self.process_federal_filings_async(
            reprocess_files=data.get('reprocess_files', False),
            start_from_incomplete=data.get('start_from_incomplete', False),
            skip_files=data.get('skip_files', set()),
            force_reprocess=data.get('force_reprocess', False),
            num_workers=data.get('num_workers', 4)
        )
    
    @action('initialize_async')
    async def handle_initialize_async(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize async components action handler."""
        await self.async_init()
        return {'status': 'initialized'}
    
    @action('get_initialization_status')
    async def handle_get_initialization_status(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get initialization status action handler."""
        return {'initialized': self._initialized}
    
    @action('get_processing_summary')
    async def handle_get_processing_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get processing summary action handler."""
        return self.get_processing_summary()
    
    @action('log_docket_summary')
    async def handle_log_docket_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Log docket summary action handler."""
        self.log_docket_summary()
        return {'status': 'logged'}
    
    @action('cleanup_resources')
    async def handle_cleanup_resources(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Cleanup resources action handler."""
        await self.cleanup_resources()
        return {'status': 'cleaned'}
    
    @action('get_component_status')
    async def handle_get_component_status(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get component status action handler."""
        return self.get_component_status()
    
    @action('validate_configuration')
    async def handle_validate_configuration(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate configuration action handler."""
        return self.validate_configuration()
    
    @action('run_law_firm_normalization')
    async def handle_run_law_firm_normalization(self, data: Dict[str, Any]) -> Any:
        """Run law firm normalization action handler."""
        num_workers = data.get('num_workers', 4)
        return await self.specialized_workflows.run_law_firm_normalization_only(num_workers)
    
    @action('run_mdl_title_update')
    async def handle_run_mdl_title_update(self, data: Dict[str, Any]) -> Any:
        """Run MDL title update action handler."""
        num_workers = data.get('num_workers', 4)
        return await self.specialized_workflows.run_mdl_title_update_only(num_workers)
    
    @action('process_transformation_job')
    async def handle_process_transformation_job(self, data: Dict[str, Any]) -> Any:
        """Process transformation job action handler."""
        job_data = data.get('job_data', {})
        job = TransformationJob(**job_data)
        return await self.job_runner_service.run_job_async(job)
    
    @action('get_modular_components_summary')
    async def handle_get_modular_components_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get modular components summary action handler."""
        return self.get_modular_components_summary()
    
    @action('validate_async_storage')
    async def handle_validate_async_storage(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate async storage action handler."""
        return await self.validate_async_storage()
    
    @action('get_processing_statistics')
    async def handle_get_processing_statistics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get processing statistics action handler."""
        return self.get_processing_statistics()
    
    @action('shutdown')
    async def handle_shutdown(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Shutdown the transformer action handler."""
        self.shutdown_event.set()
        await self.cleanup_resources()
        return {'status': 'shutdown_complete'}
    
    # Original methods remain unchanged
    
    def _setup_transformer_logging(self, transformer_logger: Optional[logging.Logger] = None) -> logging.Logger:
        """Set up logging for the transformer."""
        if transformer_logger:
            return transformer_logger
        
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        
        # Add handler
        if not logger.handlers:
            logger.addHandler(console_handler)
        
        return logger
    
    async def _initialize_modular_components(self) -> None:
        """Initialize all modular components."""
        try:
            # Initialize error handler first
            self.error_handler = ErrorHandler(
                config=self.config,
                logger=self.logger
            )
            
            # Initialize required services for DataProcessingEngine
            # Initialize law firm processing service
            from src.services.transformer.law_firm_processing_service import LawFirmProcessingService
            from src.services.transformer._law_firm_components.processor import LawFirmProcessor
            from src.services.transformer._law_firm_components.integration import LawFirmIntegration
            
            # Create law firm processor
            law_firm_processor = LawFirmProcessor(
                config=self.config,
                logger=self.logger,
                s3_async_storage=self.s3_manager
            )
            
            # Create law firm integration
            law_firm_integration = LawFirmIntegration(
                law_firm_processor=law_firm_processor,
                config=self.config,
                logger=self.logger
            )
            
            # Create law firm processing service
            self.law_firm_processing_service = LawFirmProcessingService(
                processor=law_firm_processor,
                integration=law_firm_integration,
                config=self.config,
                logger=self.logger
            )
            
            # Initialize data cleaning service
            from src.services.transformer.data_cleaning_service import DataCleaningService
            self.data_cleaning_service = DataCleaningService(
                config=self.config,
                logger=self.logger
            )
            
            # Initialize case classification service
            from src.services.transformer.case_classification_service import CaseClassificationService
            self.case_classification_service = CaseClassificationService(
                config=self.config,
                logger=self.logger
            )
            
            # Initialize data upload service
            from src.services.transformer.data_upload_service import DataUploadService
            self.data_upload_service = DataUploadService(
                config=self.config,
                logger=self.logger,
                s3_manager=self.s3_manager,
                pacer_db=self.pacer_db
            )
            
            # Initialize data processing engine with correct parameters
            self.data_processing_engine = DataProcessingEngine(
                docket_processing_service=self.docket_processor,
                mdl_processing_service=self.mdl_processor,
                file_processing_service=self.file_handler,
                law_firm_processing_service=self.law_firm_processing_service,
                case_classification_service=self.case_classification_service,
                data_cleaning_service=self.data_cleaning_service,
                data_upload_service=self.data_upload_service,
                llm_client=None,  # Will be set later if needed
                logger=self.logger,
                config=self.config,
                html_integration_service=None,  # Will be set later
                deepseek_service=None  # Will be set later if needed
            )
            
            # Initialize HTML integration service
            self.html_integration_service = HTMLIntegrationService(
                config=self.config,
                logger=self.logger,
                s3_manager=self.s3_manager,
                error_handler=self.error_handler
            )
            
            # Update data processing engine with HTML integration service
            self.data_processing_engine.html_integration_service = self.html_integration_service
            
            # Initialize file operations manager
            self.file_operations_manager = FileOperationsManager(
                config=self.config,
                logger=self.logger,
                data_processing_engine=self.data_processing_engine,
                file_handler=self.file_handler,
                html_integration_service=self.html_integration_service,
                error_handler=self.error_handler
            )
            
            # Initialize job services
            self.job_runner_service = JobRunnerService(
                config=self.config,
                logger=self.logger,
                file_operations_manager=self.file_operations_manager,
                error_handler=self.error_handler
            )
            
            self.job_orchestration_service = JobOrchestrationService(
                config=self.config,
                logger=self.logger,
                file_handler=self.file_handler,
                job_runner_service=self.job_runner_service,
                error_handler=self.error_handler
            )
            
            # Initialize specialized workflows
            self.specialized_workflows = SpecializedWorkflows(
                config=self.config,
                logger=self.logger,
                s3_manager=self.s3_manager,
                pacer_db=self.pacer_db,
                mdl_processor=self.mdl_processor,
                error_handler=self.error_handler
            )
            
            self.log_info("All modular components initialized successfully")
            
        except Exception as e:
            error = DependencyException(
                f"Failed to initialize modular components: {str(e)}",
                dependency_name="modular_components",
                dependency_type="system",
                required_by="DataTransformer"
            )
            self.log_error(str(error))
            raise error
    
    async def async_init(self) -> None:
        """Initialize async components."""
        if self._initialized:
            self.log_debug("DataTransformer already initialized")
            return
        
        try:
            # Initialize storage
            self._async_storage = AsyncDynamoDBStorage()
            
            # Initialize repositories
            self._pacer_repo = PacerRepositoryAsync(self._async_storage)
            self._district_courts_repo = DistrictCourtsRepositoryAsync(self._async_storage)
            
            # Legacy references
            self.pacer_db = self._pacer_repo
            self.district_court_db = self._district_courts_repo
            
            # Initialize S3
            self.s3_manager = S3Manager(
                bucket_name=self._s3_config['bucket_name'],
                region=self._s3_config['region']
            )
            
            # Initialize components
            self.component_factory = ComponentFactory(
                config=self.config,
                logger=self.logger,
                openai_client=self._llm_clients.get('openai'),
                deepseek_service=self._llm_clients.get('deepseek'),
                mistral_service=self._llm_clients.get('mistral')
            )
            
            self.file_handler = FileHandler(config=self.config, logger=self.logger)
            self.docket_processor = DocketProcessor(
                config=self.config,
                logger=self.logger,
                llm_client=self.component_factory.get_llm_client()
            )
            self.mdl_processor = MdlProcessor(
                config=self.config,
                logger=self.logger,
                mdl_path=str(self.mdl_path),
                district_court_db=self.district_court_db
            )
            
            # Load MDL data immediately after initializing the MDL processor
            if self.mdl_path.exists():
                try:
                    self.log_info(f"Loading MDL litigation data from {self.mdl_path}")
                    mdl_litigations_df = self.component_factory.load_mdl_data(str(self.mdl_path))
                    if self.mdl_processor and hasattr(self.mdl_processor, 'utils'):
                        self.mdl_processor.utils.mdl_litigations = mdl_litigations_df
                        self.log_info(f"Successfully loaded {len(mdl_litigations_df)} MDL litigation entries")
                except Exception as e:
                    self.log_warning(f"Failed to load MDL data: {e}")
            else:
                self.log_warning(f"MDL lookup file not found: {self.mdl_path}")
            
            # Initialize modular components
            await self._initialize_modular_components()
            
            self._initialized = True
            self.log_info("DataTransformer async initialization complete")
            
        except DependencyException:
            # Re-raise dependency exceptions as-is
            raise
        except Exception as e:
            error = DependencyException(
                f"Failed to initialize DataTransformer: {str(e)}",
                dependency_name="DataTransformer",
                dependency_type="system",
                required_by="transformation_pipeline"
            )
            self.log_error(str(error))
            raise error
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.async_init()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup_resources()
        
        if exc_type:
            self.log_error(f"Error in DataTransformer context: {exc_val}")
            
        # Close storage connections
        if self._async_storage:
            try:
                await self._async_storage.close()
            except Exception as e:
                resource_error = ResourceException(
                    f"Error closing async storage: {str(e)}",
                    resource_type="storage",
                    resource_path="dynamodb",
                    operation="close"
                )
                self.log_error(str(resource_error))
                # Don't re-raise during cleanup
        
        # Cleanup S3
        if self.s3_manager:
            try:
                # S3Manager cleanup if needed
                pass
            except Exception as e:
                resource_error = ResourceException(
                    f"Error cleaning up S3: {str(e)}",
                    resource_type="storage",
                    resource_path="s3",
                    operation="cleanup"
                )
                self.log_error(str(resource_error))
                # Don't re-raise during cleanup
    
    async def start(self, 
                   upload: bool = False,
                   reprocess_files: bool = False,
                   start_from_incomplete: bool = False,
                   skip_files: Set[str] = None,
                   force_reprocess: bool = False,
                   num_workers: int = 4,
                   run_law_firm_normalization_only: bool = False,
                   run_mdl_title_update_only: bool = False,
                   post_process: bool = False) -> Any:
        """Start the transformation process."""
        if not self._initialized:
            await self.async_init()
        
        self.log_info("="*80)
        self.log_info("Starting DataTransformer Service")
        self.log_info(f"Configuration: upload={upload}, workers={num_workers}")
        self.log_info("="*80)
        
        try:
            # Handle specialized workflows
            if run_law_firm_normalization_only:
                return await self.specialized_workflows.run_law_firm_normalization_only(num_workers)
            
            if run_mdl_title_update_only:
                return await self.specialized_workflows.run_mdl_title_update_only(num_workers)
            
            # Regular processing
            result = await self.process_federal_filings_async(
                reprocess_files=reprocess_files,
                start_from_incomplete=start_from_incomplete,
                skip_files=skip_files or set(),
                force_reprocess=force_reprocess,
                num_workers=num_workers
            )
            
            self.log_docket_summary()
            return result
            
        except (WorkflowException, ProcessingException):
            # Re-raise workflow and processing exceptions as-is
            raise
        except Exception as e:
            error = wrap_processing_error(
                e, 
                "DataTransformer.start",
                stage="main_workflow",
                item_id=None
            )
            self.log_error(str(error))
            raise error
    
    async def process_federal_filings_async(self,
                                          reprocess_files: bool = False,
                                          start_from_incomplete: bool = False,
                                          skip_files: Set[str] = None,
                                          force_reprocess: bool = False,
                                          num_workers: int = 4) -> Any:
        """Process federal filings asynchronously."""
        if not self._initialized:
            await self.async_init()
        
        skip_files = skip_files or set()
        
        try:
            # Use job orchestration service
            result = await self.job_orchestration_service.process_files_async(
                reprocess_files=reprocess_files,
                start_from_incomplete=start_from_incomplete,
                skip_files=skip_files,
                force_reprocess=force_reprocess,
                num_workers=num_workers,
                shutdown_event=self.shutdown_event
            )
            
            # Update summary
            if result and 'summary' in result:
                self.docket_summary.update(result['summary'])
            
            return result
            
        except WorkflowException:
            # Re-raise workflow exceptions as-is
            raise
        except Exception as e:
            error = WorkflowException(
                f"Failed to process federal filings: {str(e)}",
                workflow_name="federal_filings_processing",
                stage="main_processing",
                recovery_suggestion="Check data directory and file integrity"
            )
            self.log_error(str(error))
            raise error
    
    def _format_elapsed_time(self, elapsed_seconds: float) -> str:
        """Format elapsed time in human-readable format."""
        hours = int(elapsed_seconds // 3600)
        minutes = int((elapsed_seconds % 3600) // 60)
        seconds = int(elapsed_seconds % 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    
    def log_docket_summary(self) -> None:
        """Log summary of processed dockets."""
        elapsed = time.time() - self.start_time
        elapsed_str = self._format_elapsed_time(elapsed)
        
        self.log_info("="*80)
        self.log_info("TRANSFORMATION SUMMARY")
        self.log_info("="*80)
        self.log_info(f"Total dockets processed: {self.docket_summary['total']}")
        self.log_info(f"Relevant dockets: {self.docket_summary['relevant']}")
        self.log_info(f"Processing time: {elapsed_str}")
        self.log_info("="*80)
    
    def _check_shutdown(self) -> bool:
        """No graceful shutdown - always return False."""
        return False
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get summary of processing statistics."""
        elapsed = time.time() - self.start_time
        
        return {
            'total_processed': self.docket_summary['total'],
            'relevant_cases': self.docket_summary['relevant'],
            'processing_time_seconds': elapsed,
            'processing_time_formatted': self._format_elapsed_time(elapsed),
            'status': 'shutdown_requested' if self._check_shutdown() else 'active',
            'components_initialized': self._initialized
        }
    
    async def cleanup_resources(self) -> None:
        """Cleanup all resources."""
        self.log_info("Cleaning up DataTransformer resources...")
        
        # Cleanup modular components
        for component in [self.job_orchestration_service, self.job_runner_service,
                         self.specialized_workflows, self.file_operations_manager,
                         self.data_processing_engine, self.error_handler]:
            if component and hasattr(component, 'cleanup'):
                try:
                    await component.cleanup()
                except Exception as e:
                    resource_error = ResourceException(
                        f"Error cleaning up component: {str(e)}",
                        resource_type="component",
                        resource_path=str(type(component).__name__),
                        operation="cleanup"
                    )
                    self.log_error(str(resource_error))
                    # Don't re-raise during cleanup
    
    def get_component_status(self) -> Dict[str, Any]:
        """Get status of all components."""
        return {
            'core_components': {
                'component_factory': 'initialized' if self.component_factory else 'not initialized',
                'file_handler': 'initialized' if self.file_handler else 'not initialized',
                'docket_processor': 'initialized' if self.docket_processor else 'not initialized',
                'mdl_processor': 'initialized' if self.mdl_processor else 'not initialized',
                'llm_client': 'initialized' if self.llm_client else 'not initialized'
            },
            'modular_components': {
                'data_processing_engine': 'initialized' if self.data_processing_engine else 'not initialized',
                'file_operations_manager': 'initialized' if self.file_operations_manager else 'not initialized',
                'specialized_workflows': 'initialized' if self.specialized_workflows else 'not initialized',
                'error_handler': 'initialized' if self.error_handler else 'not initialized',
                'job_runner_service': 'initialized' if self.job_runner_service else 'not initialized',
                'job_orchestration_service': 'initialized' if self.job_orchestration_service else 'not initialized'
            },
            'storage_components': {
                's3_manager': 'initialized' if self.s3_manager else 'not initialized',
                'async_storage': 'initialized' if self._async_storage else 'not initialized',
                'pacer_repo': 'initialized' if self._pacer_repo else 'not initialized',
                'district_courts_repo': 'initialized' if self._district_courts_repo else 'not initialized'
            }
        }
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate configuration settings."""
        issues = []
        warnings = []
        
        # Check MDL path
        if not self.mdl_path.exists():
            warnings.append(f"MDL lookup file not found: {self.mdl_path}")
        
        # Check bucket configuration
        if not self._s3_config.get('bucket_name'):
            issues.append("S3 bucket name not configured")
        
        # Check worker count
        num_workers = self.config.get('num_workers', 4)
        if num_workers < 1:
            issues.append(f"Invalid number of workers: {num_workers}")
        elif num_workers > 20:
            warnings.append(f"High number of workers ({num_workers}) may cause resource issues")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'config_summary': {
                'mdl_path': str(self.mdl_path),
                'bucket_name': self._s3_config.get('bucket_name'),
                'num_workers': num_workers,
                'llm_provider': self.config.get('llm_provider', 'unknown')
            }
        }
    
    def get_modular_components_summary(self) -> Dict[str, Any]:
        """Get detailed summary of modular components."""
        summary = {
            'data_processing_engine': {
                'initialized': self.data_processing_engine is not None,
                'description': 'Handles core data transformation logic'
            },
            'file_operations_manager': {
                'initialized': self.file_operations_manager is not None,
                'description': 'Manages file I/O and processing'
            },
            'specialized_workflows': {
                'initialized': self.specialized_workflows is not None,
                'description': 'Handles specialized processing workflows',
                'workflows': ['law_firm_normalization', 'mdl_title_update'] if self.specialized_workflows else []
            },
            'error_handler': {
                'initialized': self.error_handler is not None,
                'description': 'Centralized error handling and recovery'
            },
            'job_runner_service': {
                'initialized': self.job_runner_service is not None,
                'description': 'Executes individual transformation jobs'
            },
            'job_orchestration_service': {
                'initialized': self.job_orchestration_service is not None,
                'description': 'Orchestrates parallel job execution'
            },
            'html_integration_service': {
                'initialized': self.html_integration_service is not None,
                'description': 'Handles HTML processing and integration'
            }
        }
        
        # Add status counts
        initialized_count = sum(1 for comp in summary.values() if comp['initialized'])
        summary['summary'] = {
            'total_components': len(summary) - 1,  # Exclude summary itself
            'initialized': initialized_count,
            'not_initialized': len(summary) - 1 - initialized_count
        }
        
        return summary
    
    async def validate_async_storage(self) -> Dict[str, Any]:
        """Validate async storage connections."""
        results = {
            'valid': True,
            'storage_status': {},
            'errors': []
        }
        
        # Test DynamoDB connection
        if self._async_storage:
            try:
                # Simple test query
                test_result = await self._pacer_repo.query_by_filing_date('20240101', limit=1)
                results['storage_status']['dynamodb'] = 'connected'
            except Exception as e:
                resource_error = ResourceException(
                    f"DynamoDB connection error: {str(e)}",
                    resource_type="database",
                    resource_path="dynamodb",
                    operation="test_connection"
                )
                results['storage_status']['dynamodb'] = 'error'
                results['errors'].append(str(resource_error))
                results['valid'] = False
        else:
            results['storage_status']['dynamodb'] = 'not initialized'
            results['valid'] = False
        
        # Test S3 connection
        if self.s3_manager:
            try:
                # Check if bucket exists
                exists = self.s3_manager.bucket_exists()
                results['storage_status']['s3'] = 'connected' if exists else 'bucket not found'
                if not exists:
                    results['valid'] = False
            except Exception as e:
                resource_error = ResourceException(
                    f"S3 connection error: {str(e)}",
                    resource_type="storage",
                    resource_path="s3",
                    operation="test_connection"
                )
                results['storage_status']['s3'] = 'error'
                results['errors'].append(str(resource_error))
                results['valid'] = False
        else:
            results['storage_status']['s3'] = 'not initialized'
            results['valid'] = False
        
        return results
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get detailed processing statistics."""
        elapsed = time.time() - self.start_time
        
        stats = {
            'runtime': {
                'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
                'elapsed_seconds': elapsed,
                'elapsed_formatted': self._format_elapsed_time(elapsed)
            },
            'processing': {
                'total_dockets': self.docket_summary['total'],
                'relevant_dockets': self.docket_summary['relevant'],
                'relevance_rate': (self.docket_summary['relevant'] / self.docket_summary['total'] * 100
                                 if self.docket_summary['total'] > 0 else 0)
            },
            'performance': {
                'dockets_per_second': (self.docket_summary['total'] / elapsed 
                                     if elapsed > 0 else 0),
                'average_time_per_docket': (elapsed / self.docket_summary['total']
                                          if self.docket_summary['total'] > 0 else 0)
            },
            'system': {
                'shutdown_requested': self._check_shutdown(),
                'initialized': self._initialized,
                'config_valid': self.validate_configuration()['valid']
            }
        }
        
        return stats