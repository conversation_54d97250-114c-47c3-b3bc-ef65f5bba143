# /src/services/transformer/jobs/job_orchestration_service.py
# -*- coding: utf-8 -*-
# !/usr/bin/env python3
"""
Job orchestration service for the transformer service.
"""
import asyncio
import logging
from typing import List, Set, Union, Dict

from tqdm.asyncio import tqdm as tqdm_async

from src.services.transformer.jobs.job_models import TransformationJob
from src.services.transformer.jobs.job_runner_service import JobRunnerService


class JobOrchestrationService:
    """
    Orchestrates the execution of transformation jobs.
    """

    def __init__(self,
                 job_runner_service: JobRunnerService,
                 file_operations_manager: "FileOperationsManager",
                 shutdown_event: asyncio.Event,
                 logger: logging.Logger,
                 ):
        self.job_runner_service = job_runner_service
        self.file_operations_manager = file_operations_manager
        self.shutdown_event = shutdown_event
        self.logger = logger

    async def run_jobs_async(self,
                             reprocess_files: Union[bool, List[str], Set[str]] = False,
                             start_from_incomplete: bool = False,
                             skip_files: Set[str] = None,
                             force_reprocess: bool = False,
                             num_workers: int = 4,
                             transformer: "DataTransformer" = None,
                             ) -> List[Dict]:
        """
        Process federal filings with async concurrency control.
        """
        skip_files = skip_files or set()

        if reprocess_files is True:
            force_reprocess = True
            self.logger.info("reprocess_files=True detected, enabling force_reprocess for all files")
        elif isinstance(reprocess_files, (list, set)) and reprocess_files:
            force_reprocess = True
            self.logger.info(f"reprocess_files list detected with {len(reprocess_files)} files, enabling force_reprocess for specific files")

        files_to_process = await self.file_operations_manager.get_files_to_process(
            reprocess_files, start_from_incomplete, skip_files
        )

        if not files_to_process:
            self.logger.warning("No files to process.")
            return []

        self.logger.info(f"Starting async processing of {len(files_to_process)} files with {num_workers} workers")

        if self._check_shutdown():
            self.logger.info("Shutdown requested before starting file processing")
            return []

        semaphore = asyncio.Semaphore(num_workers)

        async def process_with_semaphore(job: TransformationJob):
            if self._check_shutdown():
                self.logger.info(f"Shutdown requested, skipping job: {job.json_path}")
                return None
            async with semaphore:
                return await self.job_runner_service.run_job_async(job)

        jobs = [
            TransformationJob(
                json_path=p,
                force_reprocess=force_reprocess,
                transformer=transformer,
            ) for p in files_to_process
        ]
        tasks = [process_with_semaphore(job) for job in jobs]
        results = await tqdm_async.gather(*tasks, desc="Processing files", unit="file")

        valid_results = [r for r in results if r is not None]
        success_count = sum(1 for r in valid_results if r.get('status') == 'success')

        self.logger.info(f"Processing completed: {success_count}/{len(valid_results)} successful")

        return valid_results

    def _check_shutdown(self) -> bool:
        """
        No graceful shutdown - always return False.
        """
        return False
