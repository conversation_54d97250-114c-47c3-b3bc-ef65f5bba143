# /src/services/orchestration/upload_orchestrator.py

import asyncio
import json
import os
from typing import List, Dict, Optional, Set, Any


from src.config_models.base import WorkflowConfig
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage  # For DynamoDB storage
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.repositories.pacer_repository import PacerRepository  # For DynamoDB uploads
from src.services.transformer.data_upload_service import DataUploadService
from src.services.transformer._file_components.file_handler_core import FileHandlerCore


class UploadOrchestrator(AsyncServiceBase):
    def __init__(self,
                 logger: LoggerProtocol =None,
                 config: WorkflowConfig =None,
                 data_upload_service: DataUploadService =None,
                 file_handler: FileHandlerCore =None,
                 shutdown_event: Optional[asyncio.Event] = None):
        super().__init__(logger, config.model_dump() if hasattr(config, 'model_dump') else {})
        self.config = config
        self.shutdown_event = shutdown_event

        # Store injected dependencies with fallback handling
        if data_upload_service is None:
            self.log_warning("DataUploadService not properly injected - upload operations will be disabled")
        self.data_upload_service = data_upload_service
        
        if file_handler is None:
            self.log_warning("FileHandlerCore not properly injected - file operations will be limited")
        self.file_handler = file_handler
        
        # Initialize S3 async manager as None (optional dependency)
        self.s3_async_manager = None

        self.log_info("Using injected S3AsyncStorage client")
        self.log_info("PacerRepository initialized with provided DynamoDB storage client")
        self.log_info("UploadOrchestrator initialized with dependency injection")

    def _check_shutdown(self) -> bool:
        """No graceful shutdown - always return False."""
        return False

    async def __aenter__(self):
        """Async context manager entry."""
        # Initialize S3 client if we created our own S3 manager
        if self.s3_async_manager and hasattr(self.s3_async_manager, '__aenter__'):
            await self.s3_async_manager.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit to properly cleanup resources."""
        # Close S3 client if we created our own S3 manager
        if self.s3_async_manager and hasattr(self.s3_async_manager, '__aexit__'):
            await self.s3_async_manager.__aexit__(exc_type, exc_val, exc_tb)
            self.log_info("S3AsyncStorage closed successfully in UploadOrchestrator")

    async def _execute_action(self, data: Any) -> Any:
        """Execute the upload orchestration workflow."""
        return await self.execute(data)

    async def execute(self, processed_summary: Optional[List[Dict]]) -> Dict:
        self.log_info("Executing upload tasks")
        
        # Debug config values
        self.log_info(f"🔍 UPLOAD DEBUG: Config type: {type(self.config)}")
        self.log_info(f"🔍 UPLOAD DEBUG: Config upload: {getattr(self.config, 'upload', 'NOT_FOUND')}")
        self.log_info(f"🔍 UPLOAD DEBUG: Config force_upload_all: {getattr(self.config, 'force_upload_all', 'NOT_FOUND')}")
        self.log_info(f"🔍 UPLOAD DEBUG: Config upload_json_to_dynamodb: {getattr(self.config, 'upload_json_to_dynamodb', 'NOT_FOUND')}")
        self.log_info(f"🔍 UPLOAD DEBUG: Config upload_pdfs_to_s3: {getattr(self.config, 'upload_pdfs_to_s3', 'NOT_FOUND')}")
        
        if hasattr(self.config, 'model_dump'):
            config_dump = self.config.model_dump()
            self.log_info(f"🔍 UPLOAD DEBUG: Config dump keys: {list(config_dump.keys())}")
            upload_related = {k: v for k, v in config_dump.items() if 'upload' in k.lower()}
            self.log_info(f"🔍 UPLOAD DEBUG: Upload-related config: {upload_related}")

        if self._check_shutdown():
            return {"status": "interrupted", "uploaded_count": 0}

        # Extract upload flags from config
        # These fields may be in the YAML but not defined in the model, so use getattr with defaults
        upload_json_flag = getattr(self.config, 'upload_json_to_dynamodb', True)
        upload_pdfs_flag = getattr(self.config, 'upload_pdfs_to_s3', True)
        # upload_reports_flag = getattr(self.config, 'upload_reports_to_s3', False) # If reports are handled here

        self.log_info(f"🔍 UPLOAD DEBUG: Extracted upload_json_flag: {upload_json_flag}")
        self.log_info(f"🔍 UPLOAD DEBUG: Extracted upload_pdfs_flag: {upload_pdfs_flag}")

        if not upload_json_flag and not upload_pdfs_flag:
            self.log_info("No upload types enabled (JSON to DynamoDB or PDFs to S3). Skipping upload")
            return {'uploaded': [], 'skipped': [], 'failed': ['No upload types enabled']}

        # Check for force_upload_all flag first
        force_upload_all = getattr(self.config, 'force_upload_all', False)
        self.log_info(f"🔍 UPLOAD DEBUG: Extracted force_upload_all: {force_upload_all}")

        if processed_summary is None and not force_upload_all:
            self.log_warning(
                "Upload workflow received None for processed_summary (processing failed) and force_upload_all is False. Skipping upload")
            return {'uploaded': [], 'skipped': [], 'failed': ['Processing step failed']}

        json_files_to_upload = []
        if force_upload_all:
            self.log_info(
                "force_upload_all is True. Scanning directory for ALL files to upload regardless of processing status")
            json_files_to_upload = await self.file_handler.get_json_files()
            if not json_files_to_upload:
                self.log_warning("No JSON files found in the target directory for upload")
                return {'uploaded': [], 'skipped': [], 'failed': ['No files found in directory']}
            self.log_info("Found JSON files to force upload", {"file_count": len(json_files_to_upload)})
        elif processed_summary:  # Non-empty list means processing ran successfully
            self.log_info("Upload workflow using summary of processed files", {"file_count": len(processed_summary)})
            
            # Debug: Log first item structure to understand the data
            if processed_summary:
                first_item = processed_summary[0]
                self.log_debug(f"DEBUG: First processed_summary item keys: {list(first_item.keys())}")
                self.log_debug(f"DEBUG: Looking for filenames - final_filename: {first_item.get('final_filename')}, filename: {first_item.get('filename')}")
            
            target_docket_dir = self.file_handler.get_target_docket_directory()
            if not target_docket_dir:
                self.log_error("Cannot determine target docket directory for upload. Aborting upload")
                return {'uploaded': [], 'skipped': [], 'failed': ['Target directory unknown']}

            for item in processed_summary:
                # DataTransformer returns 'final_filename', not 'filename'
                final_filename = item.get('final_filename') or item.get('filename')
                if final_filename and isinstance(final_filename, str):
                    if not final_filename.endswith('.json'):
                        final_filename = f"{final_filename}.json"
                    full_path = os.path.join(target_docket_dir, final_filename)
                    if await asyncio.to_thread(os.path.exists, full_path):
                        json_files_to_upload.append(full_path)
                    else:
                        self.log_warning("File reported in summary not found for upload", {"file_path": full_path})

            if not json_files_to_upload:
                self.log_warning("Processed summary provided, but no valid/existing JSON files extracted for upload")
                return {'uploaded': [], 'skipped': [], 'failed': ['No valid files from summary']}
        else:  # Empty list [] means processing was skipped or found nothing to process
            # REGRESSION FIX: Check if specific files were requested via reprocess_files
            reprocess_files = getattr(self.config, 'reprocess_files', False)
            if isinstance(reprocess_files, (list, set)) and reprocess_files:
                # If specific files were requested, only upload those files (not all files)
                self.log_info(f"Empty processed summary but reprocess_files specified ({len(reprocess_files)} files). "
                             f"Only uploading the originally requested files, not all directory files")
                target_docket_dir = self.file_handler.get_target_docket_directory()
                if not target_docket_dir:
                    self.log_error("Cannot determine target docket directory for reprocess_files upload")
                    return {'uploaded': [], 'skipped': [], 'failed': ['Target directory unknown']}
                
                for file_name in reprocess_files:
                    if isinstance(file_name, str):
                        if not file_name.endswith('.json'):
                            file_name = f"{file_name}.json"
                        full_path = os.path.join(target_docket_dir, file_name)
                        if await asyncio.to_thread(os.path.exists, full_path):
                            json_files_to_upload.append(full_path)
                        else:
                            self.log_warning(f"Reprocess file not found for upload: {full_path}")
                
                if not json_files_to_upload:
                    self.log_warning("No valid reprocess_files found for upload")
                    return {'uploaded': [], 'skipped': [], 'failed': ['No valid reprocess files found']}
            else:
                # Original logic: scan directory for all files
                self.log_info("No processed summary provided and no specific reprocess_files. Scanning directory for all JSON files for upload")
                json_files_to_upload = await self.file_handler.get_json_files()
                if not json_files_to_upload:
                    self.log_warning("No JSON files found in the target directory for upload")
                    return {'uploaded': [], 'skipped': [], 'failed': ['No files found in directory']}

        self.log_info("Identified JSON files for potential upload", {"file_count": len(json_files_to_upload)})

        upload_targets: Set[str] = set()
        if upload_json_flag: upload_targets.add('dynamodb')
        if upload_pdfs_flag: upload_targets.add('s3')

        # force_s3_upload from config, assuming it exists (e.g., self.config.force_s3_upload)
        # Defaulting to False if not present for safety.
        force_s3 = getattr(self.config, 'force_s3_upload', False) or getattr(self.config, 'force_upload', False)

        upload_results = {'uploaded': [], 'skipped': [], 'failed': ['Upload not fully attempted yet']}

        # Manage S3 client lifecycle if it was created here
        s3_entered_locally = False
        if self.s3_async_manager and not hasattr(self.s3_async_manager, '_entered_externally'):  # Simple check
            # If s3_async_manager was created by this orchestrator, manage its context
            try:
                await self.s3_async_manager.__aenter__()
                s3_entered_locally = True
            except Exception as e_s3_enter:
                self.log_error("Failed to enter S3 manager context", {"error": str(e_s3_enter)})
                return {'uploaded': [], 'skipped': [], 'failed': [f'S3 context enter failed: {e_s3_enter}']}

        # Manage DynamoDB client for Uploader if needed (currently pacer_db_repo is None)
        # If self.pacer_db_repo was initialized (e.g. from a factory-provided dynamo client):
        # async with self.pacer_db_repo.storage as db_storage_for_uploader:
        #    self.uploader.pacer_db = self.pacer_db_repo # Ensure uploader has the repo with active storage
        #    upload_results = await self.uploader.upload_batch_to_aws_async(...)
        # Else, if Uploader can work without pacer_db for S3-only uploads:

        if not self.data_upload_service:
            self.log_error("DataUploadService not available. Cannot upload.")
            return {'uploaded': [], 'skipped': [], 'failed': ['DataUploadService unavailable']}

        upload_results = {'uploaded': [], 'skipped': [], 'failed': []}
        
        if json_files_to_upload:
            try:
                # Determine upload types based on config
                upload_types = []
                if self.config.upload_json_to_dynamodb:
                    upload_types.append('dynamodb')
                if self.config.upload_pdfs_to_s3 or self.config.upload:
                    upload_types.append('s3')
                
                # Prepare batch upload data with ALL files at once
                upload_data = {
                    'json_paths': json_files_to_upload,  # Pass ALL file paths for batch upload
                    'upload_types': upload_types or ['dynamodb'],  # Default to DynamoDB
                    'force_s3_upload': self.config.force_s3_upload
                }
                
                self.log_info(f"Uploading batch of {len(json_files_to_upload)} files with types: {upload_types}")
                
                # Call upload_docket which internally uses upload_batch_to_aws_async
                success = await self.data_upload_service.upload_docket(upload_data)
                
                if success:
                    # If batch upload succeeded, mark all files as uploaded
                    upload_results['uploaded'] = [os.path.basename(f) for f in json_files_to_upload]
                    self.log_info(f"Batch upload successful for {len(json_files_to_upload)} files")
                else:
                    # If batch upload failed, mark all files as failed
                    upload_results['failed'] = [os.path.basename(f) for f in json_files_to_upload]
                    self.log_error("Batch upload failed")
                    
            except Exception as e:
                self.log_error(f"Error during batch upload: {e}")
                upload_results['failed'] = [os.path.basename(f) for f in json_files_to_upload]
                
        return upload_results
