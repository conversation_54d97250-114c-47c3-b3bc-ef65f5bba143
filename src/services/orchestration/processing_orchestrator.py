# /src/services/orchestration/processing_orchestrator.py

import asyncio
from typing import List, Dict, Optional, Any


from src.config_models.base import WorkflowConfig
from src.config_models.utils import convert_config_to_dict
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import OrchestrationServiceError
from src.infrastructure.protocols.logger import LoggerProtocol
from src.services.transformer.data_transformer import DataTransformer


class ProcessingOrchestrator(AsyncServiceBase):
    def __init__(self,
                 logger: LoggerProtocol =None,
                 config: WorkflowConfig =None,
                 data_transformer: DataTransformer =None,
                 shutdown_event: Optional[asyncio.Event] = None):
        # Use centralized conversion utility for consistent config handling
        config_data = convert_config_to_dict(config, convert_datetime=True, logger=logger)
        super().__init__(logger, config_data)
        self.config = config
        self.shutdown_event = shutdown_event
        self._config_dict = self.config.get_transformer_config_dict()
        self.data_transformer = data_transformer

    def _check_shutdown(self) -> bool:
        """No graceful shutdown - always return False."""
        return False

    async def _execute_action(self, data: Any) -> Any:
        """Execute the processing orchestration workflow."""
        return await self.execute()

    async def execute(self) -> Optional[List[Dict]]:
        # Log configuration check
        config_dict = convert_config_to_dict(self.config, convert_datetime=True, logger=self.logger)
        self.log_info("ProcessingOrchestrator Config Check", {
            "date": config_dict.get('date'),
            "iso_date": config_dict.get('iso_date'),
            "start_date": config_dict.get('start_date'),
            "end_date": config_dict.get('end_date')
        })

        self.log_info("Executing post-processing tasks directly with DataTransformer")

        if self._check_shutdown():
            return []

        processing_summary: Optional[List[Dict]] = None

        run_general_post_process_flag = self.config.post_process
        reprocess_arg = self.config.reprocess_files
        start_incomplete_arg = self.config.start_from_incomplete
        skip_files_arg = self.config.skip_files
        num_workers = self.config.num_workers

        normalize_law_firm_names_flag = self.config.normalize_law_firm_names
        run_mdl_title_update_flag = self.config.reprocess_mdl_num

        standard_processing_triggered = (
                run_general_post_process_flag or
                (isinstance(reprocess_arg, (list, set)) and bool(reprocess_arg)) or
                reprocess_arg is True or
                start_incomplete_arg
        )
        cleanup_operations_requested = normalize_law_firm_names_flag or run_mdl_title_update_flag
        should_run_transformer = standard_processing_triggered or cleanup_operations_requested

        if not should_run_transformer:
            self.log_info("DataTransformer run skipped (no relevant flags/conditions met)")
            return []

        async with self.data_transformer as dt_instance:
            try:
                if cleanup_operations_requested:
                    cleanup_modes = []
                    if normalize_law_firm_names_flag: cleanup_modes.append("Law Firm Normalization")
                    if run_mdl_title_update_flag: cleanup_modes.append("MDL Title Update")
                    self.log_info("DataTransformer will run cleanup operations", {"cleanup_modes": cleanup_modes})

                if standard_processing_triggered:
                    self.log_info("DataTransformer will run in 'Standard Processing/Reprocessing' mode")

                force_reprocess_dt_arg = isinstance(reprocess_arg, (list, set)) and bool(reprocess_arg)

                processing_summary = await dt_instance.start(
                    upload=False,  # Upload is handled by UploadOrchestrator, not here
                    reprocess_files=reprocess_arg,
                    start_from_incomplete=start_incomplete_arg,
                    skip_files=skip_files_arg,
                    force_reprocess=force_reprocess_dt_arg,
                    num_workers=num_workers,
                    post_process=run_general_post_process_flag,
                    run_law_firm_normalization_only=normalize_law_firm_names_flag,
                    run_mdl_title_update_only=run_mdl_title_update_flag
                )

                if processing_summary is None:
                    self.log_error("DataTransformer.start returned None, indicating a potential error")
                elif not processing_summary:
                    self.log_warning("DataTransformer.start completed but returned no summary (empty list)")
                else:
                    self.log_info("DataTransformer.start completed", {"outcomes_count": len(processing_summary)})

            except Exception as e:
                self.log_error("Error during DataTransformer execution", {"error": str(e)})
                raise OrchestrationServiceError(f"ProcessingOrchestrator execution failed: {str(e)}",
                                                {"original_error": e})

        return processing_summary

    async def __aenter__(self):
        """Async context manager entry."""
        self.log_info("ProcessingOrchestrator entering async context")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        self.log_info("ProcessingOrchestrator exiting async context")

        # Perform cleanup operations
        try:
            # Clean up DataTransformer if it exists
            if hasattr(self, 'data_transformer') and self.data_transformer:
                if hasattr(self.data_transformer, 'cleanup'):
                    await self.data_transformer.cleanup()

            # Log completion
            self.log_info("ProcessingOrchestrator async context cleanup completed")
        except Exception as e:
            self.log_error(f"Error during ProcessingOrchestrator async context cleanup: {e}")

        # Don't suppress exceptions by default
        return False
