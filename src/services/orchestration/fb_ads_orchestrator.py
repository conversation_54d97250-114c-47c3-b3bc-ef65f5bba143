# /src/services/orchestration/fb_ads_orchestrator.py

import asyncio
import logging
import os
import sys
from typing import Any

import aiohttp  # FacebookAdsOrchestrator expects an aiohttp.ClientSession

from src.config_models.base import WorkflowConfig
from src.config_models.utils import convert_datetime_fields_to_strings
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import OrchestrationServiceError

# FacebookAdsOrchestrator is in src.services.fb_ads module


try:
    from src.services.fb_ads import FacebookAdsOrchestrator
except ImportError:
    FacebookAdsOrchestrator = None
    logging.getLogger(__name__).warning(
        "FbAdsOrchestrator: FacebookAdsOrchestrator not found or failed to import. FB Ads functionality will be disabled."
    )


class FbAdsOrchestrator(AsyncServiceBase):
    def __init__(
        self,
        config: WorkflowConfig = None,
        shutdown_event: asyncio.Event | None = None,
        ai_orchestrator: Any | None = None,
        deepseek_service: Any | None = None,
        prompt_manager: Any | None = None,
        storage_container: Any | None = None,
        fb_ads_container: Any | None = None,
    ):
        logger = logging.getLogger(__name__)
        super().__init__(
            logger, config.model_dump() if hasattr(config, "model_dump") else {}
        )
        self.config = config
        self.shutdown_event = shutdown_event

        # Store injected services - NO MANUAL CREATION!
        self.ai_orchestrator = ai_orchestrator
        self.deepseek_service = deepseek_service
        self.prompt_manager = prompt_manager
        self.storage_container = storage_container
        self.fb_ads_container = fb_ads_container
        
        # Validate critical dependency injection
        if not self.storage_container:
            self.log_error("❌ CRITICAL: No storage container injected - violates DI pattern")
            self.log_error("Storage container must be injected via constructor from main factory")
            import sys
            sys.exit(1)
            
        if not self.fb_ads_container:
            self.log_error("❌ CRITICAL: No FB ads container injected - violates DI pattern")
            self.log_error("FB ads container must be injected via constructor from main factory")
            import sys
            sys.exit(1)

        self.log_info("FbAdsOrchestrator initialized with DI services")
        if self.ai_orchestrator:
            self.log_info("✅ AI Orchestrator injected via DI")
        if self.deepseek_service:
            self.log_info("✅ DeepSeek service injected via DI")
        if self.prompt_manager:
            self.log_info("✅ Prompt Manager injected via DI")

    def _check_shutdown(self) -> bool:
        """No graceful shutdown - always return False."""
        return False

    def _validate_aws_credentials(self) -> None:
        """
        Validate AWS credentials and configuration required for FB ads processing.
        Tests actual AWS connectivity, not just environment variable existence.
        Exits with status code 1 if critical AWS dependencies are missing.
        """
        import sys
        import boto3
        from botocore.exceptions import ClientError, NoCredentialsError
        
        missing_credentials = []
        
        # Check for required AWS credentials
        if not os.getenv("AWS_ACCESS_KEY_ID"):
            missing_credentials.append("AWS_ACCESS_KEY_ID")
            
        if not os.getenv("AWS_SECRET_ACCESS_KEY"):
            missing_credentials.append("AWS_SECRET_ACCESS_KEY")
        
        # AWS_REGION has a default, so it's not critical
        aws_region = os.getenv("AWS_REGION", "us-west-2")
        s3_bucket = os.getenv("S3_BUCKET_NAME", "lexgenius-dockets")
        
        if missing_credentials:
            self.log_error("❌ CRITICAL: Missing required AWS credentials for FB ads processing")
            self.log_error(f"Missing environment variables: {', '.join(missing_credentials)}")
            self.log_error("FB ads processing requires AWS S3 for image storage and data operations")
            self.log_error("Please set the following environment variables:")
            for cred in missing_credentials:
                self.log_error(f"  export {cred}=<your-{cred.lower().replace('_', '-')}>")
            self.log_error("Alternatively, configure AWS credentials using 'aws configure' or IAM roles")
            sys.exit(1)
        
        # Test actual AWS connectivity
        try:
            # Create a boto3 session to test credentials
            session = boto3.Session(
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=aws_region
            )
            
            # Test S3 access specifically
            s3_client = session.client('s3')
            s3_client.head_bucket(Bucket=s3_bucket)
            
            self.log_info("✅ AWS credentials and S3 connectivity validated successfully")
            self.log_info(f"Using AWS region: {aws_region}")
            self.log_info(f"Using S3 bucket: {s3_bucket}")
            
        except NoCredentialsError:
            self.log_error("❌ CRITICAL: AWS credentials are invalid or inaccessible")
            self.log_error("Check your AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY values")
            sys.exit(1)
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchBucket':
                self.log_error(f"❌ CRITICAL: S3 bucket '{s3_bucket}' does not exist")
                self.log_error("Please create the bucket or set S3_BUCKET_NAME to an existing bucket")
            elif error_code == 'Forbidden':
                self.log_error(f"❌ CRITICAL: No access to S3 bucket '{s3_bucket}'")
                self.log_error("Check your AWS credentials have S3 permissions")
            else:
                self.log_error(f"❌ CRITICAL: AWS S3 connectivity test failed: {e}")
            sys.exit(1)
        except Exception as e:
            self.log_error(f"❌ CRITICAL: Unexpected error testing AWS connectivity: {e}")
            sys.exit(1)

    async def _execute_action(self, data: Any) -> Any:
        """Execute the Facebook Ads orchestration workflow."""
        return await self.execute()

    async def execute(self):
        self.log_info("Executing Facebook Ads tasks")

        if self._check_shutdown():
            return

        if not FacebookAdsOrchestrator:
            self.log_warning(
                "FacebookAdsOrchestrator library not available. Skipping FB Ads processing"
            )
            return

        # Extract FB ads specific config. WorkflowConfig should have a section for this.
        # e.g., self.config.facebook_ads which is a Pydantic model itself.
        # The original MainProcessor used:
        # fb_config = self.config.get('facebook_ads') or self.params.get('facebook_ads', {})
        # merged_config = self.config.copy() and then updated specific keys.
        # With WorkflowConfig, these should be attributes.

        # Check if FB ads are enabled via the main config - fb_ads is a simple boolean in WorkflowConfig
        if not (hasattr(self.config, "fb_ads") and self.config.fb_ads):
            # Check for a simple top-level boolean flag too, if that's how it's structured in params
            if not (
                hasattr(self.config, "run_fb_ads_module")
                and self.config.run_fb_ads_module
            ):  # Example alternate flag
                self.log_info("Facebook ads processing is disabled in configuration")
                return

        # Assuming self.config.facebook_ads holds all necessary sub-fields like app_id, etc.
        # FacebookAdsOrchestrator's constructor takes (config, session)
        # The config passed to it should be the one containing all FB related settings.
        # Original code merged general config with FB specific params.
        # Here, self.config is already the comprehensive WorkflowConfig.
        # FacebookAdsOrchestrator might need specific parts or the whole dict.
        # For now, pass the whole config object, assuming FacebookAdsOrchestrator can handle it
        # or extract the relevant part (e.g., self.config.facebook_ads if it's a sub-model, or model_dump() if dict needed).

        # FacebookAdsOrchestrator expects a dictionary.
        # Convert datetime objects to strings for compatibility
        fb_config_data = convert_datetime_fields_to_strings(self.config.model_dump())

        # CRITICAL: Re-expand environment variables that may have been lost during model_dump()
        # The ConfigLoader expands env vars during initial load, but model_dump() returns raw values
        from src.config_models.loader import ConfigLoader
        config_loader = ConfigLoader()
        fb_config_data = config_loader._expand_env_vars(fb_config_data)
        
        # Log proxy credentials to verify expansion (mask the actual values)
        if 'oxy_labs_mobile_password' in fb_config_data:
            password_value = fb_config_data['oxy_labs_mobile_password']
            if password_value and password_value.startswith('${'):
                self.log_error(f"❌ CRITICAL: Environment variable not expanded: {password_value}")
                self.log_error("Check that environment variables are set correctly")
            else:
                self.log_info(f"✅ Mobile proxy password expanded correctly (length: {len(password_value) if password_value else 0})")
        else:
            self.log_warning("⚠️ oxy_labs_mobile_password not found in config data")
            self.log_info(f"Config keys: {list(fb_config_data.keys())}")
        
        # Ensure iso_date is included (it's set in __init__ but might not be in dump)
        if hasattr(self.config, "iso_date") and self.config.iso_date:
            fb_config_data["iso_date"] = self.config.iso_date

        # Also ensure DATA_DIR is included
        if hasattr(self.config, "DATA_DIR") and self.config.DATA_DIR:
            fb_config_data["DATA_DIR"] = self.config.DATA_DIR
        else:
            # Fallback to environment variable or default
            fb_config_data["DATA_DIR"] = os.environ.get(
                "LEXGENIUS_DATA_DIR", 
                os.environ.get("DATA_DIR", os.path.join(os.getcwd(), "data"))
            )

        # API keys should now come from the YAML config with environment variable expansion
        # Only inject from environment as fallback if missing

        if "openai_api_key" not in fb_config_data or not fb_config_data.get(
            "openai_api_key"
        ):
            fb_config_data["openai_api_key"] = os.environ.get("OPENAI_API_KEY", "")
        if "deepseek_api_key" not in fb_config_data or not fb_config_data.get(
            "deepseek_api_key"
        ):
            fb_config_data["deepseek_api_key"] = os.environ.get("DEEPSEEK_API_KEY", "")

        try:
            async with aiohttp.ClientSession() as session:
                # Ensure that the config passed to FacebookAdsOrchestrator is what it expects.
                # If it expects only the 'facebook_ads' sub-dict:
                # fb_ads_settings_dict = self.config.facebook_ads.model_dump() if self.config.facebook_ads else {}
                # fb_orchestrator = FacebookAdsOrchestrator(fb_ads_settings_dict, session)
                # For now, passing the full config dict as per original broader pattern.
                # AI services are now self-contained in FB ads container - no injection needed
                # The FB ads container has its own DeepSeek service and AI orchestrator

                # CRITICAL: Validate AWS credentials before proceeding
                self._validate_aws_credentials()

                # Use injected storage container (configured with .env secrets via main factory)
                # This follows the mandated DI container-facade pattern
                self.log_info("✅ Using injected storage container with .env configuration")
                
                # CRITICAL: Validate S3 storage works with injected container
                try:
                    s3_test = self.storage_container.s3_async_storage()
                    if s3_test is None:
                        self.log_error("❌ CRITICAL: S3AsyncStorage creation failed - returned None")
                        self.log_error("Check AWS credentials and S3 bucket configuration")
                        import sys
                        sys.exit(1)
                    else:
                        self.log_info("✅ S3 storage creation validated successfully")
                except Exception as e:
                    self.log_error(f"❌ CRITICAL: S3 storage creation failed: {e}")
                    self.log_error("FB ads processing requires functional S3 storage")
                    import sys
                    sys.exit(1)

                # Use pre-configured FB ads container (already wired with storage, config, etc.)
                fb_ads_container = self.fb_ads_container
                
                # Override only session-specific dependencies
                fb_ads_container.config.from_dict(fb_config_data)
                fb_ads_container.http_session.override(session)
                
                # CRITICAL: Use the injected logger from dependency injection
                # DO NOT create a separate logger - this violates DI principles
                # The container should already have the correct logger injected
                self.log_info("✅ Using injected logger from dependency injection container")
                
                # DEBUG: Check if proxy manager was already created and needs to be recreated
                self.log_info("🔄 Checking if proxy manager needs to be recreated with new config...")
                try:
                    # Force recreation of proxy manager with new config
                    fb_ads_container.proxy_manager.reset()
                    self.log_info("✅ Proxy manager reset - will be recreated with new config")
                except Exception as e:
                    self.log_debug(f"Proxy manager reset failed (expected): {e}")
                
                # Also reset the camoufox session manager to ensure it gets the new proxy_manager
                try:
                    fb_ads_container.camoufox_session_manager.reset()
                    self.log_info("✅ Camoufox session manager reset - will be recreated with new proxy_manager")
                except Exception as e:
                    self.log_debug(f"Camoufox session manager reset failed (expected): {e}")
                
                # FB ads container is now self-contained - no AI service injection needed
                self.log_info("✅ Using self-contained FB ads container")
                self.log_info("✅ AI services are self-contained within FB ads container")

                # Get the properly configured FacebookAdsOrchestrator
                fb_orchestrator = fb_ads_container.facebook_ads_orchestrator()
                await fb_orchestrator.run()
            self.log_info("Facebook ads processing completed")
        except TypeError as te:
            import traceback
            full_traceback = traceback.format_exc()
            print(f"FULL TRACEBACK FOR TYPEERROR: {full_traceback}", file=sys.stderr)
            if "FacebookAdsOrchestrator() takes no arguments" in str(
                te
            ) or "'NoneType' object is not callable" in str(
                te
            ):  # It means FacebookAdsOrchestrator was None
                self.log_error(
                    "Failed to instantiate FacebookAdsOrchestrator, likely because the import failed. Ensure 'fb_ads' module and FacebookAdsOrchestrator class are available",
                    {"error": str(te), "traceback": full_traceback}, exc_info=True
                )
            else:
                self.log_error(
                    f"TypeError in Facebook ads processing: {str(te)}",
                    {"error": str(te), "traceback": full_traceback}, exc_info=True
                )
                print(f"FULL TRACEBACK: {full_traceback}")
            raise
        except Exception as e:
            import traceback
            full_traceback = traceback.format_exc()
            self.log_error("Error in Facebook ads processing", {"error": str(e), "traceback": full_traceback}, exc_info=True)
            raise OrchestrationServiceError(
                f"FbAdsOrchestrator execution failed: {str(e)}", {"original_error": e}
            )

        self.log_info("Facebook Ads tasks finished")

    async def __aenter__(self):
        """Async context manager entry."""
        self.log_info("FbAdsOrchestrator entering async context")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        self.log_info("FbAdsOrchestrator exiting async context")

        # Perform cleanup operations
        try:
            # Clean up any resources if needed
            # FB ads orchestrator typically doesn't need special cleanup

            # Log completion
            self.log_info("FbAdsOrchestrator async context cleanup completed")
        except Exception as e:
            self.log_error(f"Error during FbAdsOrchestrator async context cleanup: {e}", exc_info=True)

        # Don't suppress exceptions by default
        return False
