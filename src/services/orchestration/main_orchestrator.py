# /src/services/orchestration/main_orchestrator.py

import asyncio
import logging
from datetime import datetime
from typing import Any

from src.config_models.base import WorkflowConfig
from src.factories.main_factory import MainServiceFactory
from src.infrastructure.patterns.component_base import AsyncServiceBase


class MainOrchestrator(AsyncServiceBase):
    def __init__(
        self,
        config: WorkflowConfig = None,
        factory: MainServiceFactory = None,
        shutdown_event: asyncio.Event | None = None,
    ):
        logger = logging.getLogger(__name__)
        # Initialize with proper config handling for DI container
        super().__init__(logger, config.__dict__ if hasattr(config, "__dict__") else {})
        self.config = config
        self.factory = factory
        self.shutdown_event = shutdown_event
        # Maintain backward compatibility for tests
        self.main_processor_success = True
        self.log_info(
            "MainOrchestrator initialized",
            {"config_name": getattr(config, "config_name", "Unknown")},
        )

    async def _execute_action(self, data: Any) -> Any:
        """Execute the main orchestration workflow."""
        return await self.run()

    def _check_shutdown(self) -> bool:
        """No graceful shutdown - always return False."""
        return False

    async def run(self):
        self.log_info(
            "MainOrchestrator run started",
            {
                "config_name": getattr(self.config, "config_name", "Unknown"),
                "scraper": getattr(self.config, "scraper", False),
                "post_process": getattr(self.config, "post_process", False),
                "upload": getattr(self.config, "upload", False),
            },
        )

        processed_summary: list[dict] | None = None
        # Use instance variable for backward compatibility with tests
        # main_processor_success = True # Track success for dependent steps

        try:
            # Scraping Phase
            if hasattr(self.config, "scraper") and self.config.scraper:
                if self._check_shutdown():
                    return
                self.log_info("Scraping phase initiated")
                scraper = await self.factory.create_scraping_orchestrator()
                await scraper.execute()
                self.log_info("Scraping phase completed")
            else:
                self.log_info("Scraping phase skipped by configuration")

            # Post-Processing Phase (DataTransformer)
            if hasattr(self.config, "post_process") and self.config.post_process:
                if self._check_shutdown():
                    return
                self.log_info("Post-processing phase initiated")
                processor = await self.factory.create_processing_orchestrator()
                processed_summary = await processor.execute()
                self.log_info(
                    "Post-processing phase completed",
                    {
                        "summary_count": (
                            len(processed_summary) if processed_summary else 0
                        )
                    },
                )
            else:
                self.log_info("Post-processing phase skipped by configuration")
                # If post-processing is skipped, upload might still need to run on existing files.
                # UploadOrchestrator's execute method handles processed_summary being None or empty.
                # If upload relies on a fresh summary, this behavior might need adjustment.
                # For now, pass along None, and UploadOrchestrator will scan directory if summary is empty/None.

            # Uploading Phase
            self.log_info(f"🔍 MAIN DEBUG: Config type: {type(self.config)}")
            self.log_info(f"🔍 MAIN DEBUG: hasattr(config, 'upload'): {hasattr(self.config, 'upload')}")
            self.log_info(f"🔍 MAIN DEBUG: config.upload value: {getattr(self.config, 'upload', 'NOT_FOUND')}")
            
            if hasattr(self.config, "upload") and self.config.upload:
                if self._check_shutdown():
                    return
                self.log_info("Upload phase initiated")
                uploader = await self.factory.create_upload_orchestrator()
                async with uploader:
                    await uploader.execute(processed_summary=processed_summary)
                self.log_info("Upload phase completed")
            else:
                self.log_info("Upload phase skipped by configuration")
                self.log_info(f"🔍 MAIN DEBUG: Upload skipped - hasattr: {hasattr(self.config, 'upload')}, value: {getattr(self.config, 'upload', 'NOT_FOUND')}")

            # Facebook Ads Processing Phase
            # Check if fb_ads is enabled - it's a simple boolean in WorkflowConfig
            fb_ads_enabled = (
                hasattr(self.config, "fb_ads") and self.config.fb_ads
            ) or (
                hasattr(self.config, "run_fb_ads_module")
                and self.config.run_fb_ads_module
            )

            if fb_ads_enabled:
                if self._check_shutdown():
                    return
                self.log_info("Facebook Ads processing phase initiated")
                fb_ads_runner = await self.factory.create_fb_ads_orchestrator()
                await fb_ads_runner.execute()
                self.log_info("Facebook Ads processing phase completed")
            else:
                self.log_info("Facebook Ads processing phase skipped by configuration")

            # Report Generation Phase
            self.log_info(f"🔍 REPORT DEBUG: hasattr(config, 'report_generator') = {hasattr(self.config, 'report_generator')}")
            self.log_info(f"🔍 REPORT DEBUG: config.report_generator = {getattr(self.config, 'report_generator', 'NOT_FOUND')}")
            if (
                hasattr(self.config, "report_generator")
                and self.config.report_generator
            ):
                if self._check_shutdown():
                    return
                self.log_info(f"🔍 REPORT DEBUG: main_processor_success = {self.main_processor_success}")
                if not self.main_processor_success:
                    self.log_warning(
                        "Report generation skipped due to failures in preceding critical phases"
                    )
                else:
                    try:
                        # Check if we should generate weekly or daily report
                        weekly_flag = getattr(self.config, "weekly", False)
                        self.log_info(f"🔍 WEEKLY DEBUG: weekly_flag = {weekly_flag}")
                        self.log_info(f"🔍 WEEKLY DEBUG: config.weekly value = {getattr(self.config, 'weekly', 'NOT_FOUND')}")
                        
                        output_type = getattr(self.config, "output_types", "both")
                        
                        if weekly_flag:
                            # Generate ONLY weekly report when weekly flag is true
                            self.log_info("Weekly report generation phase initiated (weekly flag is set)")
                            self.log_info(f"🔍 WEEKLY DEBUG: About to call factory.create_reports_orchestrator_service(is_weekly=True)")
                            weekly_reports_orchestrator = (
                                await self.factory.create_reports_orchestrator_service(
                                    is_weekly=True
                                )
                            )
                            self.log_info(f"🔍 WEEKLY DEBUG: Weekly orchestrator created, is_weekly = {getattr(weekly_reports_orchestrator, 'is_weekly', 'NOT_SET')}")
                            await weekly_reports_orchestrator.generate_report(
                                output_type=output_type
                            )
                            await weekly_reports_orchestrator.cleanup()
                            self.log_info("Weekly report generation phase completed")
                        else:
                            # Generate ONLY daily report when weekly flag is false
                            self.log_info("Daily report generation phase initiated")
                            daily_reports_orchestrator = (
                                await self.factory.create_reports_orchestrator_service(
                                    is_weekly=False
                                )
                            )
                            await daily_reports_orchestrator.generate_report(
                                output_type=output_type
                            )
                            await daily_reports_orchestrator.cleanup()  # Add cleanup
                            self.log_info("Daily report generation phase completed")

                    except Exception as report_e:
                        self.log_error(
                            "Report generation encountered an error",
                            {
                                "error": str(report_e),
                                "error_type": type(report_e).__name__,
                            },
                            exc_info=True,
                        )
                        # Optionally set main_processor_success = False if report failure is critical
            else:
                self.log_info("Report generation phase skipped by configuration")

        except Exception as e:
            self.log_error(
                "MainOrchestrator encountered an error during run",
                {"error": str(e), "error_type": type(e).__name__},
                exc_info=True,
            )
            self.main_processor_success = False
            # Don't raise to maintain backward compatibility with existing tests
            # raise OrchestrationServiceError(f"MainOrchestrator execution failed: {str(e)}", {"original_error": e})

        if self.main_processor_success:
            self.log_info("MainOrchestrator run completed successfully")
        else:
            self.log_warning("MainOrchestrator run completed with errors")

        # Note: The original MainProcessor had complex logic for deciding to run,
        # especially in single date vs. date range mode. This MainOrchestrator assumes
        # it's being run for a single, resolved configuration (e.g., one date).
        # The logic for date iteration (DateProcessor) would use this MainOrchestrator
        # for each date, providing a date-specific config.

    async def __aenter__(self):
        """Async context manager entry."""
        self.log_info("MainOrchestrator entering async context")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        self.log_info("MainOrchestrator exiting async context")

        # Perform cleanup operations
        try:
            # Clean up any resources or connections
            if hasattr(self, "factory") and self.factory:
                # If factory has cleanup methods, call them
                if hasattr(self.factory, "cleanup"):
                    await self.factory.cleanup()

            # Log completion
            self.log_info("MainOrchestrator async context cleanup completed")
        except Exception as e:
            self.log_error(f"Error during MainOrchestrator async context cleanup: {e}")

        # Don't suppress exceptions by default
        return False
