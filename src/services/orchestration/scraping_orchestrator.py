# /src/services/orchestration/scraping_orchestrator.py

import asyncio
import logging
from typing import Any, Optional


from src.config_models.base import WorkflowConfig
from src.config_models.utils import convert_datetime_fields_to_strings
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.protocols.exceptions import OrchestrationServiceError
from src.pacer.facades.pacer_orchestrator_facade import PacerOrchestratorService
from src.utils.date import FORMAT_US_SHORT, FORMAT_ISO


class ScrapingOrchestrator(AsyncServiceBase):
    def __init__(self,
                 config: WorkflowConfig =None,
                 pacer_service: PacerOrchestratorService =None,
                 shutdown_event: Optional[asyncio.Event] =None):
        logger = logging.getLogger(__name__)
        super().__init__(logger, config.model_dump() if hasattr(config, 'model_dump') else {})
        self.config = config
        self.pacer_service = pacer_service
        self.shutdown_event = shutdown_event
        self.log_info("ScrapingOrchestrator initialized")

    def _check_shutdown(self) -> bool:
        """No graceful shutdown - always return False."""
        return False

    async def _execute_action(self, data: Any) -> Any:
        """Execute the scraping orchestration workflow."""
        return await self.execute()

    async def execute(self):
        self.log_info("Executing scraping tasks")

        if self._check_shutdown():
            return

        # Extract necessary parameters from self.config (Pydantic model)
        # These names assume WorkflowConfig has these attributes directly or nested.
        # Adjust if names/nesting are different (e.g., self.config.scraper_options.docket_num)
        docket_num_param = self.config.docket_num
        court_ids_param = self.config.process_single_court  # List of courts
        
        # Log the courts we're processing
        if court_ids_param:
            self.log_info(f"Processing courts: {court_ids_param}")
        html_only_param = self.config.html_only
        docket_list_input = self.config.docket_list_for_orchestrator  # List of dicts

        # Get general scraper flag
        run_general_scraper = self.config.scraper

        # Log the court list configuration - NO FALLBACK, respect the explicit list
        if court_ids_param:
            self.log_info(f"Processing ONLY these specific courts: {court_ids_param}")
        else:
            self.log_info("No specific courts specified in process_single_court - will not process any courts")

        # Get date parameters - PacerOrchestratorService methods expect date objects
        start_date_str = self.config.start_date.strftime(FORMAT_US_SHORT) if self.config.start_date else None
        end_date_str = self.config.date.strftime(
            FORMAT_US_SHORT) if self.config.date else None  # 'date' is the run date (end_date for range)

        start_date_obj = self.config.start_date
        end_date_obj = self.config.date  # Pydantic model should have these as datetime.date
        iso_date_str = end_date_obj.strftime(FORMAT_ISO) if end_date_obj else None

        # Pass the config as a dictionary to Pacer services if they expect dicts
        # Convert datetime objects to strings for compatibility
        config_dict = convert_datetime_fields_to_strings(self.config.model_dump())

        # Use PacerOrchestratorService as async context manager for proper S3 cleanup
        async with self.pacer_service:
            try:
                # --- Mode 1: List of specific dockets from 'docket_list_for_orchestrator' ---
                if docket_list_input and isinstance(docket_list_input, list):
                    self.log_info("Processing specific list of dockets", {"docket_count": len(docket_list_input)})
                    await self.pacer_service.process_courts(
                        court_ids=[],  # Not used when docket_list_input is provided
                        context=None,
                        iso_date=iso_date_str,
                        start_date=start_date_obj,
                        end_date=end_date_obj,
                        docket_list_input=docket_list_input
                    )
                    self.log_info("Specific docket list processing complete")

                # --- Mode 2: Single Docket Processing ---
                elif docket_num_param and court_ids_param:
                    if isinstance(court_ids_param, list) and len(court_ids_param) > 0:
                        target_court = court_ids_param[0]
                        if len(court_ids_param) > 1:
                            self.log_warning(
                                "Multiple courts provided but processing single docket for first court only", {
                                    "courts_provided": court_ids_param,
                                    "docket_num": docket_num_param,
                                    "target_court": target_court
                                })

                        self.log_info("Processing single docket", {
                            "court": target_court,
                            "docket": docket_num_param,
                            "html_only": html_only_param
                        })

                        # Ideal: Use self.pacer_service for this.
                        # self.pacer_service might need a method like process_single_docket.
                        # For now, if process_single_docket_standalone is the way:
                        # It creates its own PacerOrchestratorService. This is not ideal for dependency injection.
                        # If process_single_docket_standalone is to be kept, it should be refactored
                        # to accept a PacerOrchestratorService instance or just config.
                        # For now, let's assume PacerOrchestratorService has or will have a method:
                        # iso_date_str is already defined in the execute method
                        # The PacerOrchestratorService's process_single_docket method will handle context creation if None is passed.
                        # html_only behavior is expected to be handled via the config passed to PacerOrchestratorService.
                        await self.pacer_service.process_single_docket(
                            court_id=target_court,
                            docket_num=docket_num_param,
                            context=None,  # Let PacerOrchestratorService manage context if needed
                            iso_date=iso_date_str
                        )
                        self.log_info("Single docket processing complete")
                    else:
                        self.log_error(
                            "Cannot process single docket: 'process_single_court' list is empty or not a list", {
                                "docket_num": docket_num_param
                            })

                # --- Mode 3: Report Scraping (Multi-Court) ---
                elif run_general_scraper:
                    target_courts_for_reports = court_ids_param
                    if not target_courts_for_reports:  # Load all courts when list is empty
                        self.log_info("process_single_court is empty, loading all available courts")
                        # Import here to avoid circular imports
                        from src.main import get_court_ids_async
                        all_courts = await get_court_ids_async()
                        self.log_info(f"Loaded {len(all_courts)} courts from district courts file")
                        
                        # Apply skip_courts filter
                        skip_list = getattr(self.config, 'skip_courts', []) or []
                        if skip_list:
                            target_courts_for_reports = [c for c in all_courts if c not in skip_list]
                            self.log_info(f"Applied skip_courts filter. Remaining: {len(target_courts_for_reports)} courts")
                        else:
                            target_courts_for_reports = all_courts
                    
                    if target_courts_for_reports:
                        self.log_info("Running PacerOrchestratorService for report scraping", {
                            "court_count": len(target_courts_for_reports),
                            "courts": target_courts_for_reports
                        })
                        
                        # Log the exact courts being processed
                        self.log_info(f"Processing courts for reports: {target_courts_for_reports}")

                        # For sequential processing, let the orchestrator manage its own browser context
                        # The PacerOrchestratorService will create its own browser context when needed
                        await self.pacer_service.process_courts(
                            court_ids=target_courts_for_reports,
                            context=None,  # Let the service manage its own context
                            iso_date=iso_date_str,
                            start_date=start_date_obj,
                            end_date=end_date_obj
                        )
                        self.log_info("PacerOrchestratorService multi-court report scraping complete")
                    else:
                        self.log_error("No courts available to process for report scraping")
                else:
                    self.log_info(
                        "Scraping skipped (no specific dockets, not single docket mode, and scraper flag not set for report mode)")

            except Exception as e:
                self.log_error("Error during scraping execution", {"error": str(e)})
                raise OrchestrationServiceError(f"ScrapingOrchestrator execution failed: {str(e)}",
                                                {"original_error": e})

        self.log_info("Scraping tasks completed")

    async def __aenter__(self):
        """Async context manager entry."""
        self.log_info("ScrapingOrchestrator entering async context")
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        self.log_info("ScrapingOrchestrator exiting async context")

        # Perform cleanup operations
        try:
            # Clean up any resources if needed
            if hasattr(self, 'pacer_service') and self.pacer_service:
                # If pacer_service has cleanup methods, call them
                if hasattr(self.pacer_service, 'cleanup'):
                    await self.pacer_service.cleanup()

            # Log completion
            self.log_info("ScrapingOrchestrator async context cleanup completed")
        except Exception as e:
            self.log_error(f"Error during ScrapingOrchestrator async context cleanup: {e}")

        # Don't suppress exceptions by default
        return False
