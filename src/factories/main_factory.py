import asyncio
import logging
import os

from src.config_models.base import WorkflowConfig
from src.config_models.utils import convert_datetime_fields_to_strings
from src.containers.core import MainContainer, create_container
from src.services.orchestration.fb_ads_orchestrator import FbAdsOrchestrator
from src.services.orchestration.processing_orchestrator import ProcessingOrchestrator
from src.services.orchestration.scraping_orchestrator import ScrapingOrchestrator
from src.services.orchestration.upload_orchestrator import UploadOrchestrator
from src.pacer.facades.pacer_orchestrator_facade import PacerOrchestratorService
from src.services.reports.reports_orchestrator_service import ReportsOrchestratorService


class MainServiceFactory:
    """Factory for creating services using dependency injection container."""

    def __init__(
        self, config: WorkflowConfig, shutdown_event: asyncio.Event | None = None
    ):
        self.config = config
        self.shutdown_event = shutdown_event
        self.logger = logging.getLogger(__name__)
        self._container: MainContainer | None = None

        self.logger.info(
            f"MainServiceFactory initialized with config: {config.config_name if hasattr(config, 'config_name') else 'Unknown config'}"
        )
        self.logger.info("🚀 Using dependency-injector framework")

    def _prepare_config_dict(self) -> dict:
        """Prepare configuration dictionary from WorkflowConfig."""
        # Convert config to dictionary
        if hasattr(self.config, "model_dump"):
            # Pydantic model
            config_dict = self.config.model_dump()
        elif isinstance(self.config, dict):
            # Already a dictionary
            config_dict = self.config.copy()
        else:
            # Object with attributes
            config_dict = self.config.__dict__

        # Add environment variables
        config_dict.update(
            {
                "aws_region": os.getenv("AWS_REGION", "us-west-2"),
                "aws_access_key_id": os.getenv("AWS_ACCESS_KEY_ID", ""),
                "aws_secret_access_key": os.getenv("AWS_SECRET_ACCESS_KEY", ""),
                "dynamodb_endpoint": os.getenv("DYNAMODB_ENDPOINT"),
                "s3_bucket_name": os.getenv("S3_BUCKET_NAME", "lexgenius-data"),
                "deepseek_api_key": os.getenv("DEEPSEEK_API_KEY", ""),
                "openai_api_key": os.getenv("OPENAI_API_KEY", ""),
                "llava_base_url": os.getenv("LLAVA_BASE_URL", "http://localhost:11434"),
                "llava_model": os.getenv("LLAVA_MODEL", "llava"),
                "llava_timeout": int(os.getenv("LLAVA_TIMEOUT", "60")),
                "fb_ciphers": os.getenv("FB_CIPHERS", "TLS_AES_128_GCM_SHA256"),
                "username_prod": os.getenv("PACER_USERNAME_PROD") or os.getenv("PACER_USERNAME"),
                "password_prod": os.getenv("PACER_PASSWORD_PROD") or os.getenv("PACER_PASSWORD"),
            }
        )

        # Add root-level configurations for PACER container dependency injection
        config_dict["headless"] = self.config.headless
        config_dict["run_parallel"] = getattr(self.config, 'run_parallel', True)
        config_dict["timeout_ms"] = getattr(self.config, 'timeout_ms', 60000)
        
        # DEBUG: Log MainServiceFactory config transformation
        self.logger.warning(f"🔍 DEBUG FACTORY: self.config.headless = {getattr(self.config, 'headless', 'NOT_FOUND')}")
        self.logger.warning(f"🔍 DEBUG FACTORY: self.config.run_parallel = {getattr(self.config, 'run_parallel', 'NOT_FOUND')}")
        self.logger.warning(f"🔍 DEBUG FACTORY: config_dict['headless'] = {config_dict.get('headless', 'NOT_FOUND')}")
        self.logger.warning(f"🔍 DEBUG FACTORY: config_dict['run_parallel'] = {config_dict.get('run_parallel', 'NOT_FOUND')}")

        # Add nested configs if not present
        config_dict.setdefault(
            "pacer",
            {
                "workflow": config_dict.get("pacer", {}),
                "browser": {"headless": self.config.headless},
                "username_prod": config_dict.get("username_prod"),
                "password_prod": config_dict.get("password_prod"),
                # CRITICAL FIX: Add root-level config values to pacer sub-config
                "headless": config_dict.get("headless", False),
                "run_parallel": config_dict.get("run_parallel", True),
                "timeout_ms": config_dict.get("timeout_ms", 60000),
            },
        )
        
        # DEBUG: Log pacer sub-config
        self.logger.warning(f"🔍 DEBUG FACTORY: config_dict['pacer'] = {config_dict.get('pacer', {})}")
        
        config_dict.setdefault("storage", {})
        config_dict.setdefault("fb_ads", {})
        config_dict.setdefault("transformer", {})
        config_dict.setdefault("reports", {})

        # Inject project root and directories
        project_root = os.environ.get("LEXGENIUS_PROJECT_ROOT", os.getcwd())
        config_dict["project_root"] = project_root
        config_dict.setdefault("directories", {})
        config_dict["directories"]["base_dir"] = project_root

        # Convert datetime fields and generate iso_date
        config_dict = convert_datetime_fields_to_strings(config_dict)

        return config_dict

    async def __aenter__(self):
        self.logger.info("Entering MainServiceFactory async context.")

        # Create and configure container
        config_dict = self._prepare_config_dict()
        self._container = create_container(config_dict)

        # Wire container to necessary modules
        self._container.wire(
            modules=[
                "src.pacer.facades.pacer_orchestrator_facade",
                "src.pacer.authentication_service",
                "src.pacer.pacer_browser_service",
                "src.pacer.navigation_service",
                "src.pacer.html_processing_service",
                "src.services.fb_ads",
                "src.services.transformer",
                "src.services.reports",
                "src.services.orchestration",
                "src.services.ai",
                "src.services.html",
                "src.services.document",
                "src.services.uploader",
                "src.services.infrastructure",
                "src.services.monitoring",
                "src.services.district_courts",
                "src.services.law_firms",
                "src.services.pacer_dockets",
                "src.services.fb_archive",
            ]
        )

        self.logger.info("DI Container created and wired successfully")

        # Initialize resources (this will create HTTP session, etc.)
        init_result = self._container.init_resources()
        if init_result is not None:
            await init_result

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.logger.info("Exiting MainServiceFactory async context.")

        if self._container:
            # Clean up AWS storage services BEFORE container shutdown to prevent shutdown-phase session creation
            try:
                self.logger.info("=== Pre-shutdown: Cleaning up AWS storage services ===")
                
                # Get and cleanup DynamoDB storage
                try:
                    dynamodb_storage = self._container.storage.async_dynamodb_storage()
                    if hasattr(dynamodb_storage, '_cleanup_service'):
                        await dynamodb_storage._cleanup_service()
                        self.logger.debug("DynamoDB storage cleaned up")
                except Exception as e:
                    self.logger.warning(f"Error cleaning up DynamoDB storage: {e}")
                
                # Get and cleanup S3 storage
                try:
                    s3_storage = self._container.storage.s3_async_storage()
                    if hasattr(s3_storage, '_cleanup_service'):
                        await s3_storage._cleanup_service()
                        self.logger.debug("S3 storage cleaned up")
                except Exception as e:
                    self.logger.warning(f"Error cleaning up S3 storage: {e}")
                    
                self.logger.info("AWS storage services pre-cleanup completed")
                
            except Exception as cleanup_e:
                self.logger.error(f"🚨 ERROR in pre-shutdown AWS cleanup: {cleanup_e}", exc_info=True)
            
            # Shutdown resources
            shutdown_result = self._container.shutdown_resources()
            if shutdown_result is not None:
                await shutdown_result
            # Unwire container
            self._container.unwire()
            self.logger.info("DI Container cleaned up successfully.")

    def get_dynamodb_storage(self):
        """Returns the DynamoDB storage from the container."""
        if not self._container:
            return None
        return self._container.storage.async_dynamodb_storage()

    def get_s3_storage(self):
        """Returns the S3 storage from the container."""
        if not self._container:
            return None
        return self._container.storage.s3_async_storage()

    async def create_pacer_orchestrator_service(
        self, deepseek_service=None
    ) -> PacerOrchestratorService:
        """Create PacerOrchestratorService via DI Container."""
        self.logger.info("Creating PacerOrchestratorService via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create PacerOrchestratorService"
            )

        # Provide the shutdown event to the pacer container
        with self._container.pacer.shutdown_event.override(self.shutdown_event):
            service = self._container.pacer.orchestration.pacer_orchestrator_service()

        self.logger.info("✅ PacerOrchestratorService created via DI Container")
        return service

    async def create_reports_orchestrator_service(
        self, is_weekly: bool = False
    ) -> ReportsOrchestratorService:
        """Create ReportsOrchestratorService via DI Container."""
        self.logger.info(f"🔍 FACTORY DEBUG: Creating ReportsOrchestratorService with is_weekly={is_weekly}")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create ReportsOrchestratorService"
            )

        service = self._container.reports.reports_orchestrator()
        
        # Override the is_weekly setting from the container with the parameter
        self.logger.info(f"🔍 FACTORY DEBUG: Before override - service.is_weekly = {getattr(service, 'is_weekly', 'NOT_SET')}")
        service.is_weekly = is_weekly
        self.logger.info(f"🔍 FACTORY DEBUG: After override - service.is_weekly = {service.is_weekly}")
        service.log_info(f"ReportsOrchestratorService is_weekly overridden to: {is_weekly}")
        
        self.logger.info("✅ ReportsOrchestratorService created via DI Container")
        return service

    async def create_scraping_orchestrator(self) -> ScrapingOrchestrator:
        """Create ScrapingOrchestrator via DI Container."""
        self.logger.info("Creating ScrapingOrchestrator via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create ScrapingOrchestrator"
            )

        # ScrapingOrchestrator needs to be added to a container
        # For now, create it directly with dependencies
        pacer_orchestrator = await self.create_pacer_orchestrator_service()

        service = ScrapingOrchestrator(
            config=self.config,
            pacer_service=pacer_orchestrator,
            shutdown_event=self.shutdown_event,
        )

        self.logger.info("✅ ScrapingOrchestrator created")
        return service

    async def create_processing_orchestrator(self) -> ProcessingOrchestrator:
        """Create ProcessingOrchestrator via DI Container."""
        self.logger.info("Creating ProcessingOrchestrator via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create ProcessingOrchestrator"
            )

        # ProcessingOrchestrator needs transformer services
        # Only provide the shutdown_event dependency that's required
        self._container.transformer.shutdown_event.override(self.shutdown_event)
        data_transformer = self._container.transformer.data_transformer()

        service = ProcessingOrchestrator(
            config=self.config, data_transformer=data_transformer, logger=self.logger
        )

        self.logger.info("✅ ProcessingOrchestrator created")
        return service

    async def create_upload_orchestrator(self) -> UploadOrchestrator:
        """Create UploadOrchestrator via DI Container."""
        self.logger.info("Creating UploadOrchestrator via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create UploadOrchestrator"
            )

        # Get dependencies from container
        data_upload_service = self._container.transformer.data_upload_service()
        file_handler = self._container.transformer.file_handler_core()

        service = UploadOrchestrator(
            logger=self.logger,
            config=self.config,
            data_upload_service=data_upload_service,
            file_handler=file_handler,
            shutdown_event=self.shutdown_event,
        )

        self.logger.info("✅ UploadOrchestrator created")
        return service

    async def create_fb_ads_orchestrator(self) -> FbAdsOrchestrator:
        """Create FbAdsOrchestrator via DI Container."""
        self.logger.info("Creating FbAdsOrchestrator via DI Container...")

        if not self._container:
            raise RuntimeError(
                "DI Container not initialized - cannot create FbAdsOrchestrator"
            )

        # Get dependencies from FB ADS container only - NO OTHER CONTAINERS!
        ai_orchestrator = self._container.fb_ads.ai_orchestrator()
        deepseek_service = self._container.fb_ads.deepseek_service()
        prompt_manager = self._container.fb_ads.prompt_manager()

        # Get storage container from main container (with .env configuration)
        storage_container = self._container.storage
        
        # Get pre-configured FB ads container (properly wired with dependencies)
        fb_ads_container = self._container.fb_ads
        
        # Create the orchestrator with proper DI injection
        service = FbAdsOrchestrator(
            config=self.config,
            shutdown_event=self.shutdown_event,
            ai_orchestrator=ai_orchestrator,
            deepseek_service=deepseek_service,
            prompt_manager=prompt_manager,
            storage_container=storage_container,
            fb_ads_container=fb_ads_container,
        )

        self.logger.info("✅ FbAdsOrchestrator created via FB ADS container only")
        return service
