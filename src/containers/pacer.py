"""
PACER Container Module - Simplified Master Orchestrator

Master container that orchestrates domain-specific containers.
Reduces complexity from 1,200+ lines to ~150 lines by delegating to domain containers.
"""

import logging
from dependency_injector import containers, providers

# Import configuration loader
from src.config_models.loader import load_config

# Import domain containers
from src.containers.browser_container import BrowserContainer
from src.containers.data_container import DataContainer
from src.containers.orchestration_container import OrchestrationContainer
from src.containers.verification_container import VerificationContainer


class PacerContainer(containers.DeclarativeContainer):
    """Master PACER container orchestrating domain containers."""

    # === CORE DEPENDENCIES ===
    config = providers.Configuration()
    logger = providers.Singleton(logging.getLogger, 'pacer')
    storage_container = providers.DependenciesContainer()
    shutdown_event = providers.Dependency()

    # === DOMAIN CONTAINERS ===
    # VerificationContainer - verification, download, file operations (self-contained)
    verification = providers.Container(
        VerificationContainer,
        config=config,
        logger=logger,
        storage_container=storage_container
    )

    # DataContainer - data processing, analytics, AI services  
    data = providers.Container(
        DataContainer,
        config=config,
        logger=logger,
        storage_container=storage_container,
        # File service dependency injected declaratively
        file_service=verification.pacer_file_service,
    )

    # BrowserContainer - browser operations, authentication, navigation
    browser = providers.Container(
        BrowserContainer,
        config=config,
        logger=logger,
        storage_container=storage_container,
        # Cross-container dependencies injected declaratively
        default_ignore_download_service=verification.ignore_download_service,
        default_file_management_service=verification.file_management_service,
        default_case_parser_service=data.case_parser_service,
    )

    # OrchestrationContainer - workflow orchestration, job management
    orchestration = providers.Container(
        OrchestrationContainer,
        config=config,
        logger=logger,
        # Cross-container dependencies declared upfront
        deepseek_service=data.deepseek_service,
        transfer_handler=data.transfer_service,
        html_processing_service_factory=data.html_processing_service_factory_callable,
        interactive_service=data.interactive_service,
        query_service=data.query_service,
        export_service=data.export_service,
        navigation_service=browser.navigation_service_factory_callable,
        browser_service_factory=browser.pacer_browser_service,
        auth_service=browser.authentication_service,
        file_service=verification.pacer_file_service,
        s3_async_storage=storage_container.s3_async_storage,
    )

    # === DOMAIN CONTAINER ACCESS ONLY ===
    # All services are accessed through their respective domain containers:
    # - browser.* for browser services
    # - data.* for data processing services  
    # - verification.* for file and verification services
    # - orchestration.* for workflow orchestration services


def wire_cross_container_dependencies(container: PacerContainer):
    """Wire dependencies between domain containers after instantiation."""
    # Dependencies are now provided declaratively through container parameters
    # No manual wiring needed - all dependencies configured in container definitions
    pass


def get_container() -> PacerContainer:
    """Get configured PACER container with declarative dependency injection."""
    from src.infrastructure.patterns.component_base import ComponentImplementation
    from src.infrastructure.protocols.exceptions import ConfigurationError, ServiceError
    import os
    import logging
    
    logger = logging.getLogger('pacer.container')
    
    try:
        # Create PACER container
        container = PacerContainer()
        
        # Load configuration with fail-fast error handling
        config_dict = load_config('pacer')
        if not config_dict:
            error_msg = "Failed to load PACER configuration - cannot proceed"
            logger.error(error_msg, extra={"component": "PacerContainer"})
            raise ConfigurationError(error_msg)
        
        # Validate required environment variables
        required_env_vars = ['AWS_REGION', 'S3_BUCKET_NAME']
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]
        if missing_vars:
            error_msg = f"Missing required environment variables: {missing_vars}"
            logger.error(error_msg, extra={"component": "PacerContainer", "missing_vars": missing_vars})
            raise ConfigurationError(error_msg)
            
        # Add environment variables to config
        config_dict.update({
            'headless': os.getenv('HEADLESS', 'true').lower() == 'true',
            'timeout_ms': int(os.getenv('TIMEOUT_MS', '30000')),
            'deepseek_api_key': os.getenv('DEEPSEEK_API_KEY', ''),
            'aws_region': os.getenv('AWS_REGION', 'us-west-2'),
            'aws_access_key': os.getenv('AWS_ACCESS_KEY', ''),
            'aws_secret_key': os.getenv('AWS_SECRET_KEY', ''),
            'dynamodb_endpoint': os.getenv('DYNAMODB_ENDPOINT'),
            's3_bucket_name': os.getenv('S3_BUCKET_NAME', 'lexgenius-data'),
        })
        
        container.config.from_dict(config_dict)
        logger.info("PACER container configuration loaded successfully", extra={"component": "PacerContainer"})
        
        # Create and configure storage container
        from src.containers.storage import StorageContainer
        from src.containers.core import LoggerWrapper
        
        storage_container = StorageContainer()
        storage_container.config.from_dict(config_dict)
        storage_container.logger.override(LoggerWrapper(logging.getLogger('storage')))
        storage_container.aws_region.override(config_dict['aws_region'])
        storage_container.aws_access_key_id.override(config_dict['aws_access_key'])
        storage_container.aws_secret_access_key.override(config_dict['aws_secret_key']) 
        storage_container.dynamodb_endpoint.override(config_dict['dynamodb_endpoint'])
        storage_container.s3_bucket_name.override(config_dict['s3_bucket_name'])
        
        # Inject storage container dependency
        container.storage_container.override(storage_container)
        logger.info("Storage container configured and injected", extra={"component": "PacerContainer"})
        
        # Wire cross-container dependencies declaratively  
        wire_cross_container_dependencies(container)
        logger.info("Cross-container dependencies wired successfully", extra={"component": "PacerContainer"})
        
        return container
        
    except (ConfigurationError, ServiceError):
        # Re-raise framework errors without wrapping
        raise
    except Exception as e:
        error_msg = f"Failed to create PACER container: {str(e)}"
        logger.exception(error_msg, extra={"component": "PacerContainer", "error": str(e)})
        raise ServiceError(error_msg, {"original_error": e})


# def get_legacy_container():
#     """Get legacy container for backward compatibility during migration."""
#     # This imports the old pacer.py for gradual migration
#     from .pacer_original_backup import PacerContainer as LegacyPacerContainer
#     return LegacyPacerContainer()
