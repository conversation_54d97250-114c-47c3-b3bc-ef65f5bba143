"""
Storage Container Module

Provides storage services and repository providers.
"""

from dependency_injector import containers, providers

# Import storage services
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.repositories.district_courts_repository import DistrictCourtsRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.fb_image_hash_repository import FBImageHashRepository
from src.repositories.law_firms_repository import LawFirmsRepository
from src.repositories.pacer_dockets_repository import PacerDocketsRepository

# Import repositories
from src.repositories.pacer_repository import PacerRepository
from src.utils.law_firm import LawFirmNameHandler


class StorageContainer(containers.DeclarativeContainer):
    """Container for storage services and repositories."""

    # Configuration and dependencies
    config = providers.Configuration()
    logger = providers.Dependency()
    aws_region = providers.Dependency()
    aws_access_key_id = providers.Dependency()
    aws_secret_access_key = providers.Dependency()
    dynamodb_endpoint = providers.Dependency()
    s3_bucket_name = providers.Dependency()

    # Storage Services
    async_dynamodb_storage = providers.Singleton(
        AsyncDynamoDBStorage,
        config=providers.Dict(
            {
                "aws_region": aws_region,
                "dynamodb_endpoint": dynamodb_endpoint,
            }
        ),
        logger=logger,
    )

    s3_async_storage = providers.Singleton(
        S3AsyncStorage,
        logger=logger,
        bucket_name=s3_bucket_name,
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        aws_region=aws_region,
        disable_versioning=True,  # Permanently disable S3 versioning to ensure overwrite behavior
    )

    # Repositories
    pacer_repository = providers.Singleton(
        PacerRepository, storage=async_dynamodb_storage
    )

    fb_archive_repository = providers.Singleton(
        FBArchiveRepository, storage=async_dynamodb_storage
    )

    law_firms_repository = providers.Singleton(
        LawFirmsRepository, storage=async_dynamodb_storage
    )

    district_courts_repository = providers.Singleton(
        DistrictCourtsRepository, storage=async_dynamodb_storage
    )

    # DEBUG: Add factory to log when district_courts_repository is created
    @staticmethod
    def _debug_district_courts_repo_factory():
        import logging
        logger = logging.getLogger("StorageContainer")
        logger.warning("🔍 DEBUG STORAGE: Creating DistrictCourtsRepository instance")
        storage = async_dynamodb_storage()
        repo = DistrictCourtsRepository(storage=storage)
        logger.warning(f"🔍 DEBUG STORAGE: DistrictCourtsRepository created: {repo}")
        return repo

    district_courts_repository_debug = providers.Singleton(_debug_district_courts_repo_factory)

    fb_image_hash_repository = providers.Singleton(
        FBImageHashRepository, storage=async_dynamodb_storage
    )

    pacer_dockets_repository = providers.Singleton(
        PacerDocketsRepository, storage=async_dynamodb_storage
    )

    # Utility services
    law_firm_handler = providers.Singleton(LawFirmNameHandler)
