"""
Browser Domain DI Container

Provides browser-related service providers for PACER browser operations.
Extracted from the monolithic PacerContainer to follow domain separation principles.
"""

from pathlib import Path
from dependency_injector import containers, providers

# Import browser components using direct imports to avoid circular dependencies
from src.pacer.components.browser import (
    <PERSON><PERSON><PERSON><PERSON>anager,
    AuthenticationH<PERSON>ler, 
    NavigationHandler,
    BrowserLifecycleManager,
    BrowserSessionManager
)

# Import browser service facade
from src.pacer.pacer_browser_service import PacerBrowserService

# Import orchestration browser manager  
from src.pacer.components.orchestration.browser_manager import PacerBrowserManager as OrchestratorBrowserManager

# Import court processing components
from src.pacer.components.court_processing.court_session_manager import CourtSessionManager
from src.pacer.components.court_processing.court_data_processor import CourtDataProcessor
from src.pacer.components.court_processing.court_workflow_coordinator import CourtWorkflowCoordinator
from src.pacer.components.court_processing.court_processing_executor import CourtProcessingExecutor

# Import legacy services for backward compatibility
from src.pacer.authentication_service import Pacer<PERSON><PERSON>entica<PERSON><PERSON>er<PERSON>
from src.pacer.navigation_service import PacerNavigationService


class BrowserContainer(containers.DeclarativeContainer):
    """Browser domain DI container - handles all browser-related services and components."""
    
    # External dependencies injected by parent container
    config = providers.Dependency()
    logger = providers.Dependency()
    storage_container = providers.Dependency()
    
    # Cross-container dependencies for NavigationServiceFactory
    # These will be overridden by parent container with actual services
    default_ignore_download_service = providers.Dependency()
    default_file_management_service = providers.Dependency()
    default_case_parser_service = providers.Dependency()
    
    # === CORE BROWSER COMPONENTS ===
    
    # BrowserManager - handles Playwright browser lifecycle and connection management
    browser_manager = providers.Factory(
        BrowserManager,
        headless=True,  # Default value, will be overridden by config at runtime
        timeout_ms=30000,  # Default value, will be overridden by config at runtime
        logger=logger,
        config=config,
    )
    
    # BrowserLifecycleManager - manages browser lifecycle and context operations
    browser_lifecycle_manager = providers.Factory(
        BrowserLifecycleManager,
        headless=True,  # Default value, will be overridden by config at runtime
        timeout_ms=30000,  # Default value, will be overridden by config at runtime
        logger=logger,
        config=config,
    )
    
    # BrowserSessionManager - manages browser sessions and state persistence
    browser_session_manager = providers.Factory(
        BrowserSessionManager,
        logger=logger,
        config=config,
    )
    
    # === AUTHENTICATION COMPONENTS ===
    
    # AuthenticationHandler - handles PACER ECF authentication operations
    authentication_handler = providers.Factory(
        AuthenticationHandler,
        logger=logger,
        config=config,
    )
    
    # === NAVIGATION COMPONENTS ===
    
    # NavigationHandler - handles low-level browser interactions and page navigation
    # CRITICAL FIX: NavigationHandler factory that creates instances at runtime with actual page
    class NavigationHandlerFactory:
        """Factory class to create NavigationHandler instances at runtime with actual page."""
        
        def __init__(self, config, logger):
            self.config = config
            self.logger = logger
            
        def __call__(self, page, screenshot_dir=None, timeout_ms=None):
            """Create NavigationHandler with validated page instance."""
            if page is None:
                raise ValueError("NavigationHandler requires a valid Page instance, got None")
            return NavigationHandler(
                page=page,
                config=self.config,
                screenshot_dir=screenshot_dir,
                timeout_ms=timeout_ms,
                logger=self.logger
            )
    
    navigation_handler_factory = providers.Factory(
        NavigationHandlerFactory,
        config=config,
        logger=logger
    )
    
    # === BROWSER SERVICE FACADE ===
    
    # PacerBrowserService - main facade that orchestrates browser components for high-level operations
    # CRITICAL FIX: Pass navigation_handler=None to allow runtime creation with actual page
    pacer_browser_service = providers.Factory(
        PacerBrowserService,
        browser_manager=browser_manager,
        auth_handler=authentication_handler,
        navigation_handler=None,  # Will be created at runtime with actual page instance
        logger=logger,
        config=config,
    )
    
    # Browser service factory - alias to pacer_browser_service for job_runner_service
    # This provides the factory that job_runner_service needs to create browser services
    # CRITICAL FIX: Ensure this is the primary browser service factory used by other containers
    browser_service_factory = pacer_browser_service
    
    # === ORCHESTRATION BROWSER MANAGER ===
    
    # OrchestratorBrowserManager - specialized browser context management for orchestration workflows
    orchestrator_browser_manager = providers.Factory(
        OrchestratorBrowserManager,
        logger=logger,
        config=config,
    )
    
    # === COURT PROCESSING COMPONENTS ===
    
    # CourtSessionManager - manages court browser sessions and context
    court_session_manager = providers.Factory(
        CourtSessionManager,
        logger=logger,
        config=config,
    )
    
    # CourtDataProcessor - processes court-specific data and configuration
    court_data_processor = providers.Factory(
        CourtDataProcessor,
        logger=logger,
        config=config,
    )
    
    # CourtWorkflowCoordinator - coordinates court processing workflows
    court_workflow_coordinator = providers.Factory(
        CourtWorkflowCoordinator,
        logger=logger,
        config=config,
    )
    
    # CourtProcessingExecutor - executes court processing operations
    court_processing_executor = providers.Factory(
        CourtProcessingExecutor,
        logger=logger,
        config=config,
    )
    
    # === NAVIGATION SERVICES ===
    
    # PacerNavigationService - high-level navigation service for PACER workflows  
    navigation_service = providers.Factory(
        PacerNavigationService,
        logger=logger,
        config=config,
        ignore_download_service=providers.Dependency(),  # Will be resolved by parent container
        file_management_service=providers.Dependency(),  # Will be resolved by parent container
        case_parser_service=providers.Dependency(),      # Will be resolved by parent container
        html_processing_service=None,  # Will be assigned by orchestrator at runtime
    )
    
    # === LEGACY COMPATIBILITY SERVICES ===
    
    # Legacy authentication service - backward compatibility wrapper
    authentication_service = providers.Singleton(
        PacerAuthenticationService,
        logger=logger,
        config=config,
    )
    
    # Legacy browser service compatibility alias - maps to BrowserManager during migration
    browser_service = providers.Factory(
        BrowserManager,  # Temporary mapping during migration phase
        headless=True,  # Default value, will be overridden by config at runtime
        timeout_ms=30000,  # Default value, will be overridden by config at runtime
        logger=logger,
        config=config,
    )
    
    # === BROWSER CONTEXT FACTORY ===
    
    class BrowserContextFactory:
        """Factory for creating browser contexts at runtime with proper dependency injection."""
        
        def __init__(self, browser_service):
            if browser_service is None:
                raise ValueError("Browser service cannot be None")
            self.browser_service = browser_service
        
        async def create_context(self, **context_options):
            """Create browser context - delegate to browser service's new_context method."""
            try:
                # Extract download path if provided
                downloads_path = context_options.pop('downloads_path', None)
                
                # Call create_context with download_path parameter
                # Note: PacerBrowserService uses create_context, not new_context
                context = await self.browser_service.create_context(download_path=downloads_path)
                
                if context is None:
                    raise RuntimeError("Browser service returned None context")
                
                return context
            except Exception as e:
                raise RuntimeError(f"Failed to create browser context: {e}") from e
    
    # === FACTORY CLASSES FOR RUNTIME CREATION ===
    
    class NavigationServiceFactory:
        """Factory for creating NavigationService instances with runtime dependencies."""
        
        def __init__(self, navigation_service_provider, default_ignore_download_service=None, 
                     default_file_management_service=None, default_case_parser_service=None):
            if navigation_service_provider is None:
                raise ValueError("Navigation service provider cannot be None")
            self.navigation_service_provider = navigation_service_provider
            self.default_ignore_download_service = default_ignore_download_service
            self.default_file_management_service = default_file_management_service
            self.default_case_parser_service = default_case_parser_service
        
        def __call__(self, **kwargs):
            """Create navigation service instance with provided or default dependencies."""
            try:
                service = self.navigation_service_provider(
                    ignore_download_service=kwargs.get('ignore_download_service', self.default_ignore_download_service),
                    file_management_service=kwargs.get('file_management_service', self.default_file_management_service),
                    case_parser_service=kwargs.get('case_parser_service', self.default_case_parser_service),
                    html_processing_service=kwargs.get('html_processing_service', None)
                )
                
                if service is None:
                    raise RuntimeError("Navigation service provider returned None")
                
                return service
            except Exception as e:
                raise RuntimeError(f"Failed to create navigation service: {e}") from e
    
    class NavigatorFactory:
        """Factory for creating page-specific PacerNavigator instances at runtime."""
        
        def __init__(self, logger, config):
            self.logger = logger
            self.config = config
            
        def create_navigator(self, page, screenshot_dir=None, timeout_ms=30000):
            """Create a PacerNavigator instance with the given page."""
            try:
                from src.pacer.browser.navigator import PacerNavigator as NewPacerNavigator
                
                # Use provided screenshot_dir or create default
                if screenshot_dir is None:
                    screenshot_dir = str(Path("/tmp/screenshots"))
                
                navigator = NewPacerNavigator(
                    page=page,
                    config=self.config,
                    screenshot_dir=screenshot_dir,
                    timeout_ms=timeout_ms,
                )
                
                self.logger.info(f"✅ Navigator created successfully for page: {page.url if page else 'None'}")
                return navigator
                
            except Exception as e:
                self.logger.error(f"❌ Failed to create navigator: {e}")
                raise RuntimeError(f"Failed to create navigator: {e}") from e
    
    # === FACTORY PROVIDERS ===
    
    # NavigationServiceFactory provider for runtime navigation service creation
    navigation_service_factory_callable = providers.Factory(
        NavigationServiceFactory,
        navigation_service_provider=navigation_service.provider,
        default_ignore_download_service=default_ignore_download_service,  # Cross-container dependency
        default_file_management_service=default_file_management_service,  # Cross-container dependency
        default_case_parser_service=default_case_parser_service,          # Cross-container dependency
    )
    
    # NavigatorFactory provider for creating page-specific navigators
    navigator_factory = providers.Factory(
        NavigatorFactory,
        logger=logger,
        config=config,
    )
    
    # BrowserContextFactory provider for creating browser contexts at runtime
    browser_context_factory = providers.Factory(
        BrowserContextFactory,
        browser_service=browser_manager,
    )


def get_browser_container() -> BrowserContainer:
    """
    Factory function to create and return a configured BrowserContainer instance.
    
    This function provides a clean interface for obtaining the browser domain container
    without exposing the internal container implementation details.
    
    Returns:
        BrowserContainer: Configured container with all browser-related services
        
    Example:
        >>> container = get_browser_container()
        >>> container.config.from_dict({'headless': True, 'timeout_ms': 30000})
        >>> container.logger.override(logging.getLogger(__name__))
        >>> browser_service = container.pacer_browser_service()
    """
    return BrowserContainer()


# === SERVICE REGISTRATION SUMMARY ===
"""
Browser Container Services:

CORE BROWSER COMPONENTS:
- browser_manager: BrowserManager - Playwright browser lifecycle management
- browser_lifecycle_manager: BrowserLifecycleManager - Browser context lifecycle
- browser_session_manager: BrowserSessionManager - Session state management

AUTHENTICATION & NAVIGATION:
- authentication_handler: AuthenticationHandler - PACER ECF authentication
- navigation_handler: NavigationHandler - Low-level browser interactions (runtime page required)
- navigation_service: PacerNavigationService - High-level navigation workflows

FACADE SERVICES:
- pacer_browser_service: PacerBrowserService - Main browser facade orchestrating all components

ORCHESTRATION:
- orchestrator_browser_manager: OrchestratorBrowserManager - Browser management for workflows

LEGACY COMPATIBILITY:
- authentication_service: PacerAuthenticationService - Legacy authentication wrapper
- browser_service: BrowserManager - Temporary alias during migration

RUNTIME FACTORIES:
- navigation_service_factory_callable: Factory for creating navigation services with dependencies
- navigator_factory: Factory for creating page-specific navigators

EXTERNAL DEPENDENCIES:
- config: Configuration object (injected by parent)
- logger: Logger instance (injected by parent)
- storage_container: Storage services container (injected by parent)
- ignore_download_service: Download policy service (resolved by parent)
- file_management_service: File operations service (resolved by parent)
- case_parser_service: HTML parsing service (resolved by parent)
"""