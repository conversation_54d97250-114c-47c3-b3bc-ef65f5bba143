"""
Core Container Module

Provides the main application container and core infrastructure services.
"""

import logging
from typing import Any

import aiohttp
from dependency_injector import containers, providers

from . import fb_ads, pacer, reports, storage, transformer
from src.infrastructure.protocols.logger import LoggerProtocol


class LoggerWrapper:
    """Simple wrapper to make standard logger compatible with LoggerProtocol."""
    
    def __init__(self, logger: logging.Logger):
        self._logger = logger
    
    def debug(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.debug(message, extra=extra)
    
    def info(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.info(message, extra=extra)
    
    def warning(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.warning(message, extra=extra)
    
    def error(self, message: str, extra: dict[str, Any] | None = None, exc_info: bool = False) -> None:
        self._logger.error(message, extra=extra, exc_info=exc_info)
    
    def exception(self, message: str, extra: dict[str, Any] | None = None) -> None:
        self._logger.exception(message, extra=extra)
    
    def isEnabledFor(self, level: int) -> bool:
        """Check if logger is enabled for the given level."""
        return self._logger.isEnabledFor(level)


class CoreContainer(containers.DeclarativeContainer):
    """Core container for application-wide resources."""

    # Configuration provider
    config = providers.Configuration()

    # Logger factory - wrapped to provide LoggerProtocol interface
    logger = providers.Factory(
        lambda: LoggerWrapper(logging.getLogger("lexgenius"))
    )

    # HTTP Session resource - using Factory for now to avoid initializer issue
    http_session = providers.Factory(
        aiohttp.ClientSession,
        timeout=aiohttp.ClientTimeout(total=300),
    )


class MainContainer(containers.DeclarativeContainer):
    """Main application container that wires everything together."""

    # Configuration from environment and config files
    config = providers.Configuration()

    # Core services container
    core = providers.Container(CoreContainer, config=config)

    # Storage and repositories container
    storage = providers.Container(
        storage.StorageContainer,
        config=config.storage,
        logger=core.logger,
        aws_region=config.aws_region,
        aws_access_key_id=config.aws_access_key_id,
        aws_secret_access_key=config.aws_secret_access_key,
        dynamodb_endpoint=config.dynamodb_endpoint,
        s3_bucket_name=config.s3_bucket_name,
    )

    # PACER services container
    pacer = providers.Container(
        pacer.PacerContainer,
        config=config,  # Pass full config instead of just config.pacer for AI services
        logger=core.logger,
        storage_container=storage,
        shutdown_event=providers.Dependency(),  # Provided at runtime
    )

    # Facebook Ads services container
    fb_ads = providers.Container(
        fb_ads.FbAdsContainer,
        config=config,  # Pass full config instead of just config.fb_ads
        logger=core.logger,
        storage_container=storage,
        http_session=core.http_session,
    )

    # Reports services container
    reports = providers.Container(
        reports.ReportsContainer,
        config=config,  # Pass full config instead of just config.reports
        logger=core.logger,
        storage_container=storage,
        # Removed cross-container dependencies - each container should be self-contained
    )

    # Transformer services container
    transformer = providers.Container(
        transformer.TransformerContainer,
        config=config,  # Pass full config instead of just config.transformer
        logger=core.logger,
        storage_container=storage,
        # Removed cross-container dependencies - each container should be self-contained
        shutdown_event=providers.Dependency(),  # Provided at runtime
    )


def create_container(config_dict: dict[str, Any]) -> MainContainer:
    """
    Create and configure the main DI container.

    Args:
        config_dict: Configuration dictionary

    Returns:
        Configured MainContainer instance
    """
    container = MainContainer()

    # Set default configuration values
    defaults = {
        "aws_region": "us-west-2",
        "dynamodb_endpoint": None,
        "s3_bucket_name": "lexgenius-data",
        "aws_access_key": "",
        "aws_secret_key": "",
        "deepseek_api_key": "",
        "openai_api_key": "",
        "llava_base_url": "http://localhost:11434",
        "llava_model": "llava",
        "llava_timeout": 60,
        "fb_ciphers": "TLS_AES_128_GCM_SHA256",
        "headless": False,
        "run_parallel": True,
        "timeout_ms": 60000,
        # FB Ads NLP Configuration defaults
        "ner_model_name": "en_core_web_sm",
        "spacy_pipe_batch_size": 1000,
        "dynamodb_scan_workers": None,
        "ner_processing_workers": None,
        "text_fields": ["Title", "Body"],
        "cluster_min_k": 2,
        "cluster_max_k": 10,
        "cluster_k_step": 1,
        "cluster_output_enabled": True,
        "use_local_dynamodb": False,
        "dynamodb_update_workers": None,
        "storage": {},
        "pacer": {"headless": False, "run_parallel": True, "timeout_ms": 60000},
        "fb_ads": {},
        "transformer": {},
        "reports": {},
    }

    # Merge defaults with provided config
    final_config = {**defaults, **config_dict}

    # Configure the container
    container.config.from_dict(final_config)
    
    # Wire cross-container dependencies for PACER container
    # These dependencies are required for the BrowserContainer within PacerContainer
    container.pacer.browser.default_ignore_download_service.override(
        container.pacer.verification.ignore_download_service
    )
    container.pacer.browser.default_file_management_service.override(
        container.pacer.verification.file_management_service
    )
    container.pacer.browser.default_case_parser_service.override(
        container.pacer.data.case_parser_service
    )
    
    # Wire cross-container dependencies for DataContainer
    # Override file_service dependency with VerificationContainer's file service
    container.pacer.data.file_service.override(
        container.pacer.verification.pacer_file_service
    )
    
    # CRITICAL FIX: Ensure orchestrator browser manager gets proper browser service injection
    # The browser service factory should now resolve to the actual pacer_browser_service
    
    # Note: PACER container now uses its own PacerTransferService instead of transformer's TransferHandler

    return container


def wire_container(container: MainContainer, modules: list):
    """
    Wire the container to specified modules.

    Args:
        container: The container to wire
        modules: List of modules to wire
    """
    container.wire(modules=modules)


def unwire_container(container: MainContainer):
    """Unwire the container."""
    container.unwire()


# Export all key items
__all__ = [
    "CoreContainer",
    "MainContainer",
    "create_container",
    "wire_container",
    "unwire_container",
]
