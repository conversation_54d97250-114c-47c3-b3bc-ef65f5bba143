"""
Orchestration Domain Container Module

Provides orchestration-related service providers including workflow management,
service coordination, error recovery, metrics collection, and orchestrator facades.
"""

from dependency_injector import containers, providers

# Import orchestration components
from src.pacer.components.orchestration.configuration_manager import ConfigurationManager
from src.pacer.components.orchestration.error_recovery_manager import ErrorRecoveryManager
from src.pacer.components.orchestration.job_scheduler import JobScheduler
from src.pacer.components.orchestration.metrics_collector import MetricsCollector
from src.pacer.components.orchestration.progress_tracker import ProgressTracker
from src.pacer.components.orchestration.resource_manager import ResourceManager
from src.pacer.components.orchestration.result_aggregator import ResultAggregator
from src.pacer.components.orchestration.result_collector import ResultCollector
from src.pacer.components.orchestration.result_validator import ResultValidator
from src.pacer.components.orchestration.result_formatter import ResultFormatter
from src.pacer.components.orchestration.service_coordinator import PacerServiceCoordinator
from src.pacer.components.orchestration.workflow_manager import WorkflowManager
from src.pacer.components.orchestration.pacer_orchestrator import PacerOrchestrator

# Import workflow and orchestrator components
from src.pacer.components.orchestration.workflow_orchestrator import PacerWorkflowOrchestrator
from src.pacer.components.orchestration.browser_manager import PacerBrowserManager as OrchestratorBrowserManager
from src.pacer.components.orchestration.report_workflow_engine import PacerReportWorkflowEngine
from src.pacer.components.orchestration.docket_processor import PacerDocketProcessor
from src.pacer.components.orchestration.orchestrator_facade import PacerOrchestratorFacade

# Import main orchestrator facade
from src.pacer.facades.pacer_orchestrator_facade import PacerOrchestratorFacade as MainOrchestratorFacade, PacerOrchestratorService

# Import report facade
from src.pacer.facades.pacer_report_facade import PacerReportFacade

# Import docket processing orchestrator
from src.pacer.docket_processing_orchestrator_service import PacerDocketProcessingOrchestratorService

# Import missing services
from src.pacer.configuration_service import PacerConfigurationService
from src.pacer.components.court_processing.court_processing_service import PacerCourtProcessingService
from src.pacer.service_factory import PacerServiceFactory
from src.pacer.jobs.job_runner_service import PacerJobRunnerService
from src.pacer.jobs.job_orchestration_service import PacerJobOrchestrationService

# Import file operations components that were missing
from src.pacer.components.file_operations.directory_manager import DirectoryManager
from src.pacer.components.file_operations.historical_tracker import HistoricalTracker
from src.pacer.components.file_operations.download_manager import DownloadManager



class OrchestrationContainer(containers.DeclarativeContainer):
    """Orchestration domain DI container"""

    # Configuration and dependencies
    config = providers.Configuration()
    logger = providers.Dependency()

    # Provide a default logger for testing
    default_logger = providers.Resource(
        "logging.getLogger",
        "orchestration_container"
    )

    # External dependencies from other containers
    deepseek_service = providers.Dependency()  # From DataContainer via PacerContainer

    # Cross-container dependencies - these will be wired by PacerContainer
    transfer_handler = providers.Dependency()  # From DataContainer
    html_processing_service_factory = providers.Dependency()  # From DataContainer
    interactive_service = providers.Dependency()  # From DataContainer
    query_service = providers.Dependency()  # From DataContainer
    export_service = providers.Dependency()  # From DataContainer
    navigation_service = providers.Dependency()  # From BrowserContainer
    browser_service_factory = providers.Dependency()  # From BrowserContainer
    auth_service = providers.Dependency()  # From BrowserContainer
    file_service = providers.Dependency()  # From VerificationContainer
    s3_async_storage = providers.Dependency()  # From StorageContainer

    # === CORE ORCHESTRATION COMPONENTS ===

    # Configuration Manager - handles all configuration operations
    orchestration_configuration_manager = providers.Singleton(
        ConfigurationManager,
        logger=logger,
        config=config,
    )

    # Error Recovery Manager - handles error detection and recovery
    orchestration_error_recovery_manager = providers.Singleton(
        ErrorRecoveryManager,
        logger=logger,
        config=config,
    )

    # Job Scheduler - manages job scheduling and execution
    orchestration_job_scheduler = providers.Singleton(
        JobScheduler,
        logger=logger,
        config=config,
    )

    # Metrics Collector - collects performance metrics and analytics
    orchestration_metrics_collector = providers.Singleton(
        MetricsCollector,
        logger=logger,
        config=config,
    )

    # Progress Tracker - tracks and reports operation progress
    orchestration_progress_tracker = providers.Singleton(
        ProgressTracker,
        logger=logger,
        config=config,
    )

    # Resource Manager - handles browser contexts, S3, and resource allocation
    orchestration_resource_manager = providers.Singleton(
        ResourceManager,
        logger=logger,
        config=config,
    )

    # === RESULT PROCESSING COMPONENTS ===

    # Result Collector - handles result collection and storage
    orchestration_result_collector = providers.Singleton(
        ResultCollector,
        logger=logger,
        config=config,
    )

    # Result Validator - handles result aggregation and validation
    orchestration_result_validator = providers.Singleton(
        ResultValidator,
        logger=logger,
        config=config,
    )

    # Result Formatter - handles result export and formatting
    orchestration_result_formatter = providers.Singleton(
        ResultFormatter,
        logger=logger,
        config=config,
    )

    # Result Aggregator - coordinates result operations with DI injection
    orchestration_result_aggregator = providers.Singleton(
        ResultAggregator,
        logger=logger,
        config=config,
        result_collector=orchestration_result_collector,
        result_validator=orchestration_result_validator,
        result_formatter=orchestration_result_formatter,
    )

    # Export result aggregator as primary interface
    result_aggregator = orchestration_result_aggregator
    result_collector = orchestration_result_collector
    result_validator = orchestration_result_validator
    result_formatter = orchestration_result_formatter

    # Service Coordinator - will be defined later after dependencies are created
    # Moving this after service dependencies are defined

    # Workflow Manager - coordinates business workflows
    orchestration_workflow_manager = providers.Singleton(
        WorkflowManager,
        logger=logger,
        config=config,
    )

    # === WORKFLOW ORCHESTRATION COMPONENTS ===

    # OrchestratorBrowserManager - browser context management for orchestration
    orchestrator_browser_manager = providers.Factory(
        OrchestratorBrowserManager,
        logger=logger,
        config=config,
        # Note: browser_service will be injected via set_browser_service() after creation
        # This allows proper factory pattern initialization without circular dependencies
    )

    # PacerReportWorkflowEngine - report generation workflows
    # Note: court_processing_service will be injected via override after it's defined
    pacer_report_workflow_engine = providers.Factory(
        PacerReportWorkflowEngine,
        logger=logger,
        config=config,
        navigation_service=navigation_service,  # Cross-container dependency
        file_service=file_service,  # Cross-container dependency
        browser_manager=orchestrator_browser_manager,  # Inject BrowserManager for context operations
        court_processing_service=None,  # Will be overridden after court_processing_service is defined
    )

    # PacerDocketProcessor - docket processing logic
    pacer_docket_processor = providers.Factory(
        PacerDocketProcessor,
        logger=logger,
        config=config,
    )

    # PacerServiceCoordinator - service lifecycle management (Factory for component facade)
    pacer_service_coordinator = providers.Factory(
        PacerServiceCoordinator,
        logger=logger,
        config=config,
        browser_service_factory=browser_service_factory,  # Inject browser service factory
    )

    # PacerWorkflowOrchestrator - main workflow coordination (defined after dependencies)
    pacer_workflow_orchestrator = providers.Factory(
        PacerWorkflowOrchestrator,
        logger=logger,
        config=config,
        browser_manager=orchestrator_browser_manager,
        report_engine=pacer_report_workflow_engine,
        docket_processor=pacer_docket_processor,
        service_coordinator=pacer_service_coordinator,
    )

    # === ORCHESTRATOR FACADES ===

    # Component-based orchestrator facade - backward compatibility facade
    pacer_orchestrator_facade_component = providers.Factory(
        PacerOrchestratorFacade,
        workflow_orchestrator=pacer_workflow_orchestrator,
        browser_manager=orchestrator_browser_manager,
        report_workflow_engine=pacer_report_workflow_engine,
        docket_processor=pacer_docket_processor,
        service_coordinator=pacer_service_coordinator,
        logger=logger,
        config=config,
    )

    # === MAIN ORCHESTRATOR FACADE ===
    # Use the component-based facade as the main one
    pacer_orchestrator_facade = pacer_orchestrator_facade_component

    # === DOCKET PROCESSING ORCHESTRATOR ===

    # Docket processing orchestrator service
    docket_processing_orchestrator = providers.Singleton(
        PacerDocketProcessingOrchestratorService,
        logger=logger,
        config=config,
        transfer_handler=transfer_handler,  # Cross-container dependency
        s3_async_storage=s3_async_storage,  # Cross-container dependency
        html_processing_service_factory=html_processing_service_factory,  # Cross-container dependency
        navigation_service=navigation_service,  # Cross-container dependency
    )

    # === CONFIGURATION AND COURT SERVICES ===

    # Configuration service
    configuration_service = providers.Singleton(
        PacerConfigurationService,
        logger=logger,
        config=config,
    )

    # Court processing specialized components
    court_session_manager = providers.Singleton(
        "src.pacer.components.court_processing.court_session_manager.CourtSessionManager",
        logger=logger,
        config=config,
        file_service=file_service,  # Cross-container dependency
    )

    court_data_processor = providers.Singleton(
        "src.pacer.components.court_processing.court_data_processor.CourtDataProcessor",
        logger=logger,
        config=config,
        file_service=file_service,  # Cross-container dependency
    )

    court_workflow_coordinator = providers.Singleton(
        "src.pacer.components.court_processing.court_workflow_coordinator.CourtWorkflowCoordinator",
        logger=logger,
        config=config,
        # Workflow coordinator needs auth and navigation services
        auth_service=auth_service,  # Cross-container dependency
        navigation_service=navigation_service,  # Cross-container dependency
    )

    court_data_extractor = providers.Singleton(
        "src.pacer.components.court_processing.court_data_extractor.CourtDataExtractor",
        logger=logger,
        config=config,
    )

    court_validator = providers.Singleton(
        "src.pacer.components.court_processing.court_validator.CourtValidator",
        logger=logger,
        config=config,
    )

    court_metadata_manager = providers.Singleton(
        "src.pacer.components.court_processing.court_metadata_manager.CourtMetadataManager",
        logger=logger,
        config=config,
    )

    # Court processing service facade
    court_processing_service = providers.Singleton(
        PacerCourtProcessingService,
        logger=logger,
        config=config,
        session_manager=court_session_manager,
        data_processor=court_data_processor,
        workflow_coordinator=court_workflow_coordinator,
        data_extractor=court_data_extractor,
        validator=court_validator,
        metadata_manager=court_metadata_manager,
        deepseek_service=deepseek_service,
        transfer_handler=transfer_handler,  # Cross-container dependency
        docket_processing_orchestrator=docket_processing_orchestrator,
        html_processing_service_factory=html_processing_service_factory,  # Cross-container dependency
        navigation_service=navigation_service,  # Cross-container dependency
    )

    # Override court_processing_service in report workflow engine now that it's defined
    # CRITICAL FIX: Ensure browser manager has proper browser service injection
    pacer_report_workflow_engine.override(
        providers.Factory(
            PacerReportWorkflowEngine,
            logger=logger,
            config=config,
            navigation_service=navigation_service,  # Cross-container dependency
            file_service=file_service,  # Cross-container dependency
            browser_manager=orchestrator_browser_manager,  # Inject BrowserManager for context operations
            court_processing_service=court_processing_service,  # Now properly inject the defined service
        )
    )

    # === SERVICE FACTORY ===

    # Service factory
    service_factory = providers.Factory(
        PacerServiceFactory,
        logger=logger,
        config=config,
        html_processing_service_factory=html_processing_service_factory,  # Cross-container dependency
    )

    # Service Coordinator - now defined after dependencies are available
    orchestration_service_coordinator = providers.Singleton(
        PacerServiceCoordinator,
        logger=logger,
        config=config,
        service_factory=service_factory,
        config_service=configuration_service,
        court_processing_service=court_processing_service,
        deepseek_service=deepseek_service,
        browser_service_factory=browser_service_factory,  # Inject browser service factory
    )

    # === JOB SERVICES ===

    # Job runner service - remove circular dependency by making pacer_orchestrator optional
    # CRITICAL FIX: Add validation for browser service factory resolution

    class BrowserServiceFactoryValidator:
        """Validator for browser service factory to ensure it returns valid instances."""

        def __init__(self, browser_service_factory):
            self.browser_service_factory = browser_service_factory

        def __call__(self):
            """Validate and return browser service factory."""
            try:
                factory = self.browser_service_factory()
                if factory is None:
                    raise RuntimeError("Browser service factory returned None - DI resolution failed")
                # Test that the factory can create a valid browser service
                service = factory()
                if service is None:
                    raise RuntimeError("Browser service factory created None service - invalid factory")
                return factory
            except Exception as e:
                raise RuntimeError(f"Browser service factory validation failed: {e}") from e

    validated_browser_service_factory = providers.Factory(
        BrowserServiceFactoryValidator,
        browser_service_factory=browser_service_factory
    )

    job_runner_service = providers.Factory(
        PacerJobRunnerService,
        config=config,
        logger=logger,
        pacer_orchestrator=None,  # Will be injected at runtime to avoid circular dependency
        browser_service_factory=validated_browser_service_factory,  # Validated browser service factory
    )

    # Job orchestration service
    job_orchestration_service = providers.Factory(
        PacerJobOrchestrationService,
        config=config,
        logger=logger,
        job_runner_service=job_runner_service,
    )

    # === REPORT FACADE ===
    # PacerReportFacade - consolidates report_service and interactive_service
    pacer_report_facade = providers.Factory(
        PacerReportFacade,
        logger=logger,
        config=config,
        interactive_service=interactive_service,  # Cross-container dependency
        query_service=query_service,  # Cross-container dependency
        export_service=export_service,  # Cross-container dependency
    )

    # Backward compatibility aliases
    report_service = pacer_report_facade
    report_facade = pacer_report_facade

    # === FILE OPERATIONS COMPONENTS ===

    # Directory Manager - handles directory creation and management
    directory_manager = providers.Singleton(
        DirectoryManager,
        config_service=configuration_service,
    )

    # Historical Tracker - tracks download history and analytics
    historical_tracker = providers.Singleton(
        HistoricalTracker,
        config_service=configuration_service,
    )

    # Download Manager - coordinates file downloads with DI
    download_manager = providers.Singleton(
        DownloadManager,
        config_service=configuration_service,
        directory_manager=directory_manager,
        historical_tracker=historical_tracker,
    )

    # === BROWSER SERVICE INJECTION HOOK ===
    # Factory method to properly inject browser service into browser manager post-construction

    class BrowserServiceInjector:
        """Helper class to inject browser service into browser manager after construction."""

        def __init__(self, logger, browser_service_factory):
            if logger is None:
                raise ValueError("Logger cannot be None for BrowserServiceInjector")
            if browser_service_factory is None:
                raise ValueError("Browser service factory cannot be None for BrowserServiceInjector")

            self.logger = logger
            self.browser_service_factory = browser_service_factory

        async def inject_browser_service(self, browser_manager):
            """Inject browser service into browser manager with validation."""
            try:
                if browser_manager is None:
                    raise ValueError("Browser manager cannot be None for injection")

                # Get browser service from factory with validation
                self.logger.info("Resolving browser service from factory...")
                factory = self.browser_service_factory()
                if factory is None:
                    raise RuntimeError("Browser service factory resolution returned None")

                browser_service = factory()
                if browser_service is None:
                    raise RuntimeError("Browser service factory created None service")

                # Validate browser manager has the injection method
                if not hasattr(browser_manager, 'set_browser_service'):
                    raise RuntimeError("Browser manager does not have set_browser_service method")

                # Inject into browser manager
                browser_manager.set_browser_service(browser_service)

                self.logger.info("✅ Browser service successfully injected into browser manager")
                return browser_manager

            except Exception as e:
                self.logger.error(f"❌ Failed to inject browser service: {e}")
                raise RuntimeError(f"Browser service injection failed: {e}") from e

    # Browser service injector factory with validation
    browser_service_injector = providers.Factory(
        BrowserServiceInjector,
        logger=logger,
        browser_service_factory=validated_browser_service_factory,  # Use validated factory
    )

    # === MAIN PACER ORCHESTRATOR ===
    # Defined at the end after all dependencies are created

    # New orchestrator facade that coordinates all orchestration components
    pacer_orchestrator = providers.Singleton(
        PacerOrchestrator,
        logger=logger,
        config=config,
        config_service=configuration_service,
        service_factory=service_factory,
        deepseek_service=deepseek_service,
        court_processing_service=court_processing_service,
        # Inject all orchestration components
        configuration_manager=orchestration_configuration_manager,
        service_coordinator=orchestration_service_coordinator,
        resource_manager=orchestration_resource_manager,
        workflow_manager=orchestration_workflow_manager,
        job_scheduler=orchestration_job_scheduler,
        progress_tracker=orchestration_progress_tracker,
        error_recovery_manager=orchestration_error_recovery_manager,
        result_aggregator=orchestration_result_aggregator,
        metrics_collector=orchestration_metrics_collector,
    )

    # === PACER ORCHESTRATOR SERVICE ===
    # Main orchestrator service using proper DI for PacerOrchestratorService facade
    pacer_orchestrator_service = providers.Factory(
        PacerOrchestratorService,
        logger=logger,
        config=config,
        config_service=configuration_service,
        service_factory=service_factory,
        deepseek_service=deepseek_service,
        court_processing_service=court_processing_service,
        # DI-injected components that PacerOrchestratorFacade expects
        workflow_orchestrator=pacer_workflow_orchestrator,
        browser_manager=orchestrator_browser_manager,
        report_workflow_engine=pacer_report_workflow_engine,
        docket_processor=pacer_docket_processor,
        service_coordinator=pacer_service_coordinator,
    )

