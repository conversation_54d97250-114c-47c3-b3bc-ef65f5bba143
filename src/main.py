#!/usr/bin/env python3

import argparse  # Added argparse
import asyncio
import csv
import glob
import logging

# import pydevd_pycharm
#
# pydevd_pycharm.settrace('localhost', port=5678, stdoutToServer=True, stderrToServer=True)

# KILL ALL BOTO3/AWS LOGGING IMMEDIATELY AND COMPLETELY
logging.basicConfig(level=logging.WARNING)
for logger_name in [
    "boto3",
    "botocore",
    "botocore.auth",
    "botocore.endpoint",
    "botocore.regions",
    "botocore.action",
    "botocore.credentials",
    "botocore.httpsession",
    "botocore.awsrequest",
    "botocore.parsers",
    "botocore.retryhandler",
    "urllib3",
    "aioboto3",
    "aiobotocore",
]:
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.CRITICAL)
    logger.disabled = True
    logger.propagate = False

# Suppress PyTorch deprecation warnings
import warnings

warnings.filterwarnings(
    "ignore", message=".*torch.distributed.reduce_op.*", category=FutureWarning
)

# Enable resource tracking early to catch all aiohttp sessions
try:
    import os
    import sys

    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
    from src.lib.utils.enable_resource_tracking import resource_tracker
except ImportError:
    resource_tracker = None

# Now it's safe to import multiprocessing
import multiprocessing
import os

# Load environment variables from .env file
try:
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    logging.warning(
        "python-dotenv not available, environment variables from .env file will not be loaded"
    )
import subprocess

# Import our enhanced date utilities
import sys
import threading
import time as time_module  # Rename to avoid conflict
from datetime import timedelta
from pathlib import Path
from typing import Any  # Added Any

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import psutil
import yaml  # Added yaml
from playwright.async_api import async_playwright
from rich.console import Console
from rich.logging import RichHandler

from src.utils.date import FORMAT_ISO, FORMAT_US_SHORT, DateUtils

_csv_lock = threading.Lock()

# Add the project root to the Python path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
)  # Add parent directory to path

from src.config_models.loader import load_config as load_config_new
from src.factories.main_factory import MainServiceFactory

# Import cleanup library
from src.infrastructure.lifecycle.resource_cleanup import (
    cleanup_everything,
    patch_resource_tracker,
    register_cleanup,
)
from src.services.orchestration.main_orchestrator import MainOrchestrator

# Try to import FacebookAdsOrchestrator, but provide a fallback if it fails
try:
    from src.services.fb_ads import FacebookAdsOrchestrator
except ImportError:
    FacebookAdsOrchestrator = None
    logging.warning(
        "FacebookAdsOrchestrator not found or failed to import. FB Ads functionality disabled."
    )

# Import from the refactored pacer client module
# Assuming the refactored file is named pacer_client_refactored.py
# and is accessible via src.lib.pacer.client
# Adjust the import path if necessary based on your project structure
try:
    # Import only what we need directly from the modules
    # from src.pacer.orchestrator import (
    #     PacerOrchestrator as OriginalPacerOrchestrator,
    #     process_single_docket_standalone
    # )

    # Import new service architecture for feature flag support
    from src.pacer.facades.pacer_orchestrator_facade import PacerOrchestratorService
    from src.pacer.pacer_browser_service import PacerBrowserService

    # PacerOrchestrator proxy class has been removed. Use PacerOrchestratorService directly.

except ImportError as e:
    logging.error(
        f"Failed to import Pacer components from src.lib.pacer.orchestrator or related modules: {e}"
    )
    # Create dummy implementations
    print(
        f"Warning: Using dummy implementations for PacerOrchestrator due to import error: {e}"
    )

    def _get_processed_logs_path(
        config: dict, logger_for_path: logging.Logger | None = None
    ) -> Path | None:
        """
        Constructs the full path to the processed_courts.log file.
        Returns None if essential config keys (DATA_DIR, iso_date) are missing or invalid.
        """
        current_logger = (
            logger_for_path
            if logger_for_path
            else logging.getLogger(__name__ + "._get_processed_logs_path")
        )

        data_dir_str = config.get("DATA_DIR")
        iso_date = config.get("iso_date")

        current_logger.debug(
            f"Inside _get_processed_logs_path: Initial DATA_DIR from config: '{data_dir_str}', iso_date: '{iso_date}'"
        )

        if not data_dir_str:
            current_logger.error(
                "CRITICAL: 'DATA_DIR' is missing in the config for _get_processed_logs_path. Cannot determine log path."
            )
            return None

        data_dir = Path(data_dir_str)
        if not data_dir.is_absolute():
            current_logger.warning(
                f"DATA_DIR '{data_dir_str}' is not absolute. Resolving it relative to CWD '{Path.cwd()}'."
            )
            data_dir = data_dir.resolve()
            current_logger.info(f"Resolved DATA_DIR to absolute path: '{data_dir}'")

        if not iso_date:  # Handles None or empty string
            current_logger.error(
                "CRITICAL: 'iso_date' is missing or empty in the config for _get_processed_logs_path. Cannot determine log path."
            )
            # Fallback to a generic name or error out, depending on desired behavior.
            # For now, let's prevent file creation in an unexpected place by returning None.
            return None

        # Validate iso_date format (simple check for YYYYMMDD)
        if not (
            isinstance(iso_date, str) and len(iso_date) == 8 and iso_date.isdigit()
        ):
            current_logger.error(
                f"CRITICAL: 'iso_date' ('{iso_date}') in config is not in YYYYMMDD format. Cannot determine log path."
            )
            return None

        try:
            # Construct the path
            log_path = data_dir / iso_date / "logs" / "processed_courts.log"
            current_logger.debug(
                f"_get_processed_logs_path: Constructed log_path: {log_path}"
            )
            return log_path
        except TypeError as e:
            current_logger.error(
                f"CRITICAL: TypeError during path construction in _get_processed_logs_path (likely due to invalid iso_date or DATA_DIR type). Error: {e}. Config snapshot: DATA_DIR='{data_dir_str}', iso_date='{iso_date}'"
            )
            return None
        except (
            Exception
        ) as e_path:  # Catch any other unexpected error during Path object creation
            current_logger.error(
                f"CRITICAL: Unexpected error constructing Path object in _get_processed_logs_path. Error: {e_path}. Config snapshot: DATA_DIR='{data_dir_str}', iso_date='{iso_date}'",
                exc_info=True,
            )
            return None

    def _append_processed_log_entry(
        config: dict, court_stats: dict[str, Any], logger: logging.Logger
    ) -> None:
        """
        Appends a single court's processing statistics to the processed_courts.log file.
        Manages header creation if the file does not exist.
        """
        logger.info(
            f"--- _append_processed_log_entry called for CourtID: {court_stats.get('CourtID')} ---"
        )
        logger.debug(
            f"Config passed to _append_processed_log_entry: DATA_DIR='{config.get('DATA_DIR')}', iso_date='{config.get('iso_date')}'"
        )
        logger.debug(f"Court stats: {court_stats}")

        header = [
            "CourtID",
            "RowsAttempted",
            "RowsSuccessful",
            "RowsDownloaded",
            "RowsSkippedDate",
            "RowsSkippedExistsDbGsi",
            "RowsSkippedExistsLocalDateRange",
            "RowsSkippedIrrelevantLocalJsonToday",
            "RowsSkippedDownloadedLocalJsonToday",
            "RowsSkippedReportDataIrrelevant",
            "RowsSkippedOtherRelevance",
            "RowsSkippedHistoricalIrrelevant",
            "RowsSkippedNosDefendantCheck",
            "RowsSkippedMdCase",
            "RowsFailed",
            "RowsFailedExtractElements",
            "RowsFailedGetDocketNum",
        ]
        key_mapping = {
            "CourtID": "CourtID",
            "attempted": "RowsAttempted",
            "successful": "RowsSuccessful",
            "downloaded": "RowsDownloaded",
            "skipped_date": "RowsSkippedDate",
            "skipped_exists_db_gsi": "RowsSkippedExistsDbGsi",
            "skipped_exists_local_date_range": "RowsSkippedExistsLocalDateRange",
            "skipped_review_local_json_today": "RowsSkippedIrrelevantLocalJsonToday",
            "skipped_downloaded_local_json_today": "RowsSkippedDownloadedLocalJsonToday",
            "skipped_report_data_review": "RowsSkippedReportDataIrrelevant",
            "skipped_other_relevance": "RowsSkippedOtherRelevance",
            "skipped_historical_review": "RowsSkippedHistoricalIrrelevant",
            "skipped_nos_defendant_check": "RowsSkippedNosDefendantCheck",
            "skipped_md_case": "RowsSkippedMdCase",
            "failed": "RowsFailed",
            "failed_extract_elements": "RowsFailedExtractElements",
            "failed_get_docket_num": "RowsFailedGetDocketNum",
        }

        formatted_stats = {
            csv_key: court_stats.get(orc_key, 0)
            for orc_key, csv_key in key_mapping.items()
        }
        if (
            "CourtID" not in formatted_stats or not formatted_stats["CourtID"]
        ):  # Ensure CourtID is present
            formatted_stats["CourtID"] = court_stats.get(
                "CourtID", "UNKNOWN_COURT_ID_IN_FORMAT"
            )

        log_path = _get_processed_logs_path(config, logger)  # Pass logger for context
        if not log_path:
            logger.error(
                "CRITICAL: Failed to get processed_courts.log path. Cannot append entry."
            )
            return

        logger.info(f"Attempting to write processed_courts.log entry to: {log_path}")

        try:
            log_path.parent.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured parent directory exists: {log_path.parent}")
        except Exception as e_mkdir:
            logger.error(
                f"CRITICAL: Failed to create parent directory {log_path.parent} for processed_courts.log: {e_mkdir}",
                exc_info=True,
            )
            return

        with _csv_lock:
            file_exists = log_path.exists()
            logger.debug(f"File {log_path} exists: {file_exists}")
            try:
                with open(log_path, "a", newline="", encoding="utf-8") as f:
                    writer = csv.DictWriter(f, fieldnames=header)
                    if not file_exists:
                        writer.writeheader()
                        logger.info(
                            f"Created new processed_courts.log with header at {log_path}"
                        )

                    logger.info(
                        f"Writing row to processed_courts.log: {formatted_stats}"
                    )
                    writer.writerow(formatted_stats)
                    logger.info(
                        f"Appended processed log for {formatted_stats.get('CourtID', 'N/A')}"
                    )
            except OSError as e:
                logger.error(
                    f"IOError appending to processed_courts.log at {log_path}: {e}",
                    exc_info=True,
                )
            except ValueError as e_val:
                logger.error(
                    f"ValueError writing CSV row to {log_path} (likely field mismatch): {e_val}. Row data: {formatted_stats}",
                    exc_info=True,
                )
            except Exception as e_write:
                logger.error(
                    f"Unexpected error writing to processed_courts.log at {log_path}: {e_write}",
                    exc_info=True,
                )

    class PacerOrchestrator:
        def __init__(self, *args, **kwargs):
            pass

        async def process_courts(self, *args, **kwargs):
            logging.error(
                "Dummy PacerOrchestrator.process_courts called - real implementation not available"
            )
            return []

    async def process_single_docket_standalone(*args, **kwargs):
        logging.error(
            "Dummy process_single_docket_standalone called - real implementation not available"
        )
        return {}


# Remove duplicate import that's causing namespace collision
# from src.lib.pacer import PacerOrchestrator, get_court_ids_async
import json
from pathlib import Path

# --- END OF IMPORTS ---

# Initialize project paths based on current file location (don't rely on config yet)
PROJECT_ROOT = os.path.dirname(
    os.path.dirname(os.path.abspath(__file__))
)  # Go up from src/ to project root
DISTRICT_COURTS_PATH = os.path.join(
    PROJECT_ROOT, "src", "config", "courts", "district_courts.json"
)

# Initialize Rich console
console = Console()


# main.py


async def get_court_ids_async() -> list[str]:
    """
    Asynchronously loads court IDs from the specified JSON file, cleans them
    to the expected format (e.g., 'cacd', 'nysd'), and returns a unique, sorted list.
    Handles variations like 'cacd1' or 'cacdc'.
    """
    logger = logging.getLogger(__name__ + ".get_court_ids_async")
    courts_file = Path(DISTRICT_COURTS_PATH)

    try:
        logger.debug(f"Attempting to read district courts file: {courts_file}")

        if not await asyncio.to_thread(courts_file.exists):
            logger.error(f"District courts file not found at {courts_file}")
            raise FileNotFoundError(f"District courts file not found at {courts_file}")

        # Read file content asynchronously using asyncio.to_thread
        def read_sync():
            with open(courts_file) as f:
                return f.read()

        file_content = await asyncio.to_thread(read_sync)

        # Parse JSON (CPU-bound, okay in async context)
        district_courts_data = json.loads(file_content)
        if not isinstance(district_courts_data, list):
            logger.error(
                f"Invalid format in district courts file: Expected a list, got {type(district_courts_data)}."
            )
            raise ValueError("Invalid format in district courts file: Expected a list.")

        # Extract and clean IDs robustly
        ids = set()  # Use a set for automatic deduplication
        for court in district_courts_data:
            court_id_raw = court.get("court_id")

            if isinstance(court_id_raw, str) and court_id_raw.strip():
                cleaned_id = court_id_raw.strip().lower()  # Work with lowercase

                # Remove trailing digit if present
                if cleaned_id and cleaned_id[-1].isdigit():
                    cleaned_id = cleaned_id[:-1]

                # Remove trailing 'c' if present (handles cases like 'akdc')
                if cleaned_id and cleaned_id.endswith("c"):
                    cleaned_id = cleaned_id[:-1]

                # Final check: Ensure it's not empty and looks like a court ID (e.g., at least 3 chars, common pattern)
                # Basic check - can be refined if needed
                if cleaned_id and len(cleaned_id) >= 3 and cleaned_id.isalnum():
                    ids.add(cleaned_id)
                    # logger.debug(f"Extracted and cleaned court ID: {court_id_raw} -> {cleaned_id}") # Keep debug noise low maybe
                else:
                    logger.debug(
                        f"Skipping invalid or empty court_id after cleaning: '{court_id_raw}' -> '{cleaned_id}'"
                    )
            else:
                logger.debug(
                    f"Skipping invalid or non-string court_id entry: {court_id_raw}"
                )

        unique_ids_list = sorted(list(ids))  # Convert set to sorted list
        logger.info(
            f"Loaded {len(unique_ids_list)} unique court IDs."
        )  # This should now be > 0
        if not unique_ids_list:
            logger.warning(
                "get_court_ids_async: Loaded 0 unique court IDs after filtering/cleaning."
            )
        # +++ Add logging to see the actual list being returned +++
        if unique_ids_list:
            logger.debug(f"Returning court IDs (first 10): {unique_ids_list[:10]}")
        # +++ End logging +++
        return unique_ids_list

    except FileNotFoundError as e:
        logger.error(f"Error loading court IDs: {e}")
        raise  # Re-raise specific error
    except json.JSONDecodeError as e:
        logger.error(
            f"Error decoding JSON from district courts file {courts_file}: {e}"
        )
        raise ValueError(f"Error decoding JSON from {courts_file}") from e
    except Exception as e:
        logger.error(f"Unexpected error loading court IDs: {e}", exc_info=True)
        raise RuntimeError("Unexpected error loading court IDs") from e


def setup_rich_logging(config):
    """Configure Rich logging with both console and file output"""
    log_dir = config.get("directories", {}).get(
        "log_dir",
        os.path.join(
            config.get("DATA_DIR", os.path.join(os.getcwd(), "data")),
            f"{config['iso_date']}/logs",
        ),
    )
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, "app.log")

    # Create a standard formatter for file logging
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # Remove any existing handlers from root logger
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.setLevel(logging.DEBUG)

    # File handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)

    # Use Rich console handler
    console_handler = RichHandler(
        rich_tracebacks=True, console=console, show_time=True, show_path=True
    )
    # Respect LEXGENIUS_LOG_LEVEL environment variable
    log_level = getattr(
        logging, os.environ.get("LEXGENIUS_LOG_LEVEL", "DEBUG").upper(), logging.DEBUG
    )
    console_handler.setLevel(log_level)
    root_logger.addHandler(console_handler)

    # Set external libraries to ERROR level to eliminate debug noise
    for lib_name in [
        "botocore",
        "boto3",
        "urllib3",
        "playwright",
        "botocore.auth",
        "botocore.endpoint",
        "botocore.regions",
        "botocore.action",
    ]:
        logging.getLogger(lib_name).setLevel(logging.ERROR)

    # Disable specific noisy AWS loggers
    for aws_logger in [
        "botocore.credentials",
        "botocore.httpsession",
        "botocore.awsrequest",
        "botocore.parsers",
        "botocore.retryhandler",
    ]:
        logging.getLogger(aws_logger).setLevel(logging.ERROR)

    # Configure separate log files for specific components
    components_to_log_separately = {
        "pacer": "src.pacer",
        "reports_services": "src.services.reports",
        "transformer": "src.services.transformer",  # Updated to new path, keeps transformer.log
        "logging_config_module": "src.logging_config",
        "fb_ads": "src.services.fb_ads",  # Add Facebook Ads logging
    }

    for component_file_basename, logger_name in components_to_log_separately.items():
        component_log_file_path = os.path.join(
            log_dir, f"{component_file_basename}.log"
        )

        component_file_handler = logging.FileHandler(component_log_file_path)
        component_file_handler.setFormatter(file_formatter)
        component_file_handler.setLevel(logging.DEBUG)

        component_logger = logging.getLogger(logger_name)
        for handler in component_logger.handlers[:]:  # Clear existing handlers
            component_logger.removeHandler(handler)
        component_logger.addHandler(component_file_handler)
        component_logger.setLevel(logging.DEBUG)  # Ensure it's set to DEBUG

        if logger_name in [
            "src.pacer",
            "src.services.transformer",
            "src.services.reports",
        ]:  # MODIFICATION HERE
            component_logger.propagate = True
            logging.info(
                f"Log propagation ENABLED for '{logger_name}' to root (console). Check both console and {component_log_file_path}"
            )
        else:
            component_logger.propagate = False

        logging.info(
            f"Configured separate log file for '{logger_name}' at: {component_log_file_path}"
        )


def monitor_process():
    while True:
        process = psutil.Process()
        print(f"CPU Usage: {process.cpu_percent()}%")
        print(f"Memory Usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")
        time_module.sleep(5)


class DateProcessor:
    """
    Processes files for specific dates, utilizing enhanced date utilities.
    This class handles date range generation, file discovery, and processing orchestration.
    """

    def __init__(self, params: dict, shutdown_event: asyncio.Event | None = None):
        self.params = params
        self.shutdown_event = shutdown_event
        # Use PROJECT_ROOT variable which is already defined at the top of the file
        self.base_path = params.get(
            "base_data_path", os.path.join(PROJECT_ROOT, "data")
        )
        self.logger = logging.getLogger(__name__)
        # self.logger.propagate = False # Let root handler manage output

    def _get_json_files(self, dir_path: str) -> list[str]:
        """
        Get all JSON files in the directory.

        Args:
            dir_path (str): Path to the directory to check

        Returns:
            List[str]: List of paths to JSON files
        """
        try:
            json_files = glob.glob(os.path.join(dir_path, "*.json"))
            if json_files:
                self.logger.info(f"Found {len(json_files)} JSON files in {dir_path}")
            else:
                self.logger.info(f"No JSON files found in {dir_path}")
            return json_files
        except Exception as e:
            self.logger.error(f"Error getting JSON files from {dir_path}: {str(e)}")
            return []

    def _convert_date_format(self, date: str) -> str:
        """
        Convert date from MM/DD/YY to YYYYMMDD format for directory paths.

        Args:
            date (str): Date in MM/DD/YY format

        Returns:
            str: Date in YYYYMMDD format
        """
        try:
            # Use DateUtils for more robust date conversion
            iso_date = DateUtils.format_date(date, FORMAT_ISO, FORMAT_US_SHORT)
            if not iso_date:
                raise ValueError(f"Could not convert date '{date}' to ISO format")
            return iso_date
        except Exception as e:
            self.logger.error(f"Error converting date format: {e}")
            raise

    def _get_directory_path(self, date: str) -> str:
        """
        Get the full directory path for a given date.

        Args:
            date (str): Date in MM/DD/YY format

        Returns:
            str: Full directory path for the date
        """
        try:
            formatted_date = self._convert_date_format(date)
            # Use the derived 'iso_date' path structure
            return os.path.join(self.base_path, formatted_date, "dockets")
        except Exception as e:
            self.logger.error(f"Error creating directory path for date {date}: {e}")
            raise

    def generate_dates(self, start_date: str, end_date: str) -> list[str]:
        """
        Generate a list of dates between start_date and end_date inclusive.

        Args:
            start_date (str): Start date in MM/DD/YY format
            end_date (str): End date in MM/DD/YY format

        Returns:
            List[str]: List of dates in MM/DD/YY format
        """
        try:
            # Use DateUtils for more robust date range generation
            return DateUtils.generate_date_range(start_date, end_date, FORMAT_US_SHORT)
        except ValueError as e:
            self.logger.error(f"Error generating dates: {e}")
            raise ValueError(
                f"Invalid date format. Please use MM/DD/YY format. Error: {e}"
            )

    def _check_files_for_date(self, dir_path: str) -> list[tuple[str, str]]:
        """
        Check for matching JSON and PDF files in the directory.
        Returns list of tuples containing paths to matching JSON and PDF files.
        """
        matching_files = []
        json_files = glob.glob(os.path.join(dir_path, "*.json"))

        for json_file in json_files:
            root_name = os.path.splitext(os.path.basename(json_file))[0]
            pdf_file = os.path.join(dir_path, f"{root_name}.pdf")

            if os.path.exists(pdf_file):
                matching_files.append((json_file, pdf_file))
            else:
                self.logger.warning(f"Missing PDF file for JSON: {json_file}")

        return matching_files

    async def process_single_date(self, date: str) -> bool:
        """
        Process all JSON files for a single date.

        Args:
            date (str): Date string in MM/DD/YY format

        Returns:
            bool: True if processing was attempted, False if no files found or other error.
        """
        self.logger.info(f"Starting process_single_date for: {date}")
        try:
            # Get directory path for the date
            dir_path = self._get_directory_path(date)

            # Check if directory exists
            if not os.path.exists(dir_path):
                self.logger.info(f"No directory found for date {date}: {dir_path}")
                return False

            # --- Determine which files to process ---
            reprocess_param = self.params.get("reprocess_files")
            files_to_process_param = None

            if isinstance(reprocess_param, (list, set)):
                # Check if the list contains full paths or just filenames
                has_dir_paths = any(os.path.dirname(p) for p in reprocess_param)

                if has_dir_paths:
                    # Filter the provided list to only those relevant for *this specific date*
                    filtered_list = [
                        p
                        for p in reprocess_param
                        if os.path.normpath(os.path.dirname(p))
                        == os.path.normpath(dir_path)
                    ]
                else:
                    # If just filenames (no directory), use them as-is for this date's directory
                    filtered_list = list(reprocess_param)
                    self.logger.info(
                        f"reprocess_files contains filenames only, will look for them in {dir_path}/dockets"
                    )

                if not filtered_list:
                    self.logger.info(
                        f"Provided reprocess_files list has no files for date {date} in {dir_path}."
                    )
                    return (
                        False  # Treat as skip if specific list is empty for this date
                    )
                files_to_process_param = filtered_list
                self.logger.info(
                    f"Will process {len(files_to_process_param)} specific files from list for date {date}."
                )
            elif reprocess_param is True:
                files_to_process_param = True
                self.logger.info(
                    f"Will reprocess all applicable files for date {date} (reprocess_files=True)."
                )
            else:
                files_to_process_param = False
                self.logger.info(
                    f"Standard processing triggered for date {date} (reprocess_files not specified or False)."
                )

            # Create updated parameters for this batch, including the filtered file list
            updated_params = self.params.copy()
            updated_params["date"] = date  # Set the specific date being processed
            # Pass the determined files_to_process_param down, which might be List, True, or False
            updated_params["reprocess_files"] = files_to_process_param

            # Keep original start/end date if they exist in params (might be used by scraper even in date loop)
            if "start_date" in self.params:
                updated_params["start_date"] = self.params["start_date"]
            if "end_date" in self.params:
                updated_params["end_date"] = self.params["end_date"]

            # If weekly report is requested and running a report_generator task,
            # ensure we calculate the 7-day start date from the current date
            if updated_params.get("weekly", False) and updated_params.get(
                "report_generator", False
            ):
                try:
                    # Use DateUtils to get date 7 days before current date
                    seven_days_prior = DateUtils.get_date_before(date, 7)
                    # Convert to MM/DD/YY format for params
                    start_date_formatted = DateUtils.format_date(
                        seven_days_prior, FORMAT_US_SHORT
                    )

                    if start_date_formatted:
                        # Set the calculated start date for weekly reports
                        updated_params["start_date"] = start_date_formatted
                        self.logger.info(
                            f"Weekly report requested for {date}. Using date range: {updated_params['start_date']} to {date}"
                        )
                    else:
                        self.logger.error(
                            f"Failed to calculate start date for weekly report using date: {date}"
                        )
                except Exception as e:
                    self.logger.error(f"Error calculating weekly date range: {e}")
                    # Keep the existing start_date if any

            # --- Instantiate MainProcessor with updated params for this date ---
            try:
                if (
                    "config_yaml_path" in self.params
                ):  # Assuming DateProcessor gets the main yaml path in its self.params
                    # Load the base config
                    config_name = Path(self.params["config_yaml_path"]).stem
                    base_workflow_config = load_config_new(config_name)
                    # Create a dictionary of overrides for the specific date
                    # Pass all updated_params as overrides to preserve all configuration
                    date_specific_overrides = updated_params.copy()
                    date_specific_overrides["date"] = (
                        date  # Ensure the specific date is set
                    )
                    # Create a new config instance by updating the base config with overrides
                    # This assumes the Pydantic model can be updated this way or load_config_new can take overrides.
                    # A common Pydantic pattern: new_config = OldModel(**old_model.model_dump(), **overrides)
                    config_for_date_dict = base_workflow_config.model_dump()
                    config_for_date_dict.update(date_specific_overrides)
                    # Re-validate to get the correct Pydantic model type with overrides
                    config_for_date = type(base_workflow_config)(**config_for_date_dict)
                    # DATA_DIR is now part of the Pydantic model, ensure it's correctly set if needed (e.g. via env var or YAML)
                    # If base_path needs to override, it should be part of date_specific_overrides
                    # config_for_date.data_dir = self.base_path # If direct override is still needed and data_dir is a field
                else:
                    raise ValueError(
                        "DateProcessor requires 'config_yaml_path' in its params."
                    )

            except Exception as config_err:
                self.logger.error(
                    f"Error loading configuration for date {date}: {config_err}",
                    exc_info=True,
                )
                return False

            self.logger.info(
                f"Processing for date {date} using config with iso_date: {config_for_date.iso_date}"
            )  # Assuming iso_date is an attribute
            setup_rich_logging(
                config_for_date
            )  # Re-setup logging for the correct date's log file (config_for_date is Pydantic model)

            # Check if there's actually any work to do based on flags
            needs_processing = (
                updated_params.get("post_process")
                or updated_params.get("start_from_incomplete")
                or isinstance(updated_params.get("reprocess_files"), (list, set))
                or updated_params.get("reprocess_files") is True
            )
            needs_upload = updated_params.get("upload")
            needs_scraping = updated_params.get("scraper") or updated_params.get(
                "docket_num"
            )  # Check if scraping is triggered
            needs_weekly_report = updated_params.get(
                "report_generator"
            ) and updated_params.get("weekly", False)

            # Determine if any action is requested
            action_requested = (
                needs_processing
                or needs_upload
                or needs_scraping
                or updated_params.get("fb_ads")
                or updated_params.get("report_generator")
            )

            if not action_requested:
                self.logger.info(
                    f"No processing, upload, scraping, ads, or report flags set for date {date}. Skipping MainProcessor run."
                )
                return True  # Considered successful as no work needed

            # --- Execute MainOrchestrator via Factory ---
            try:
                # config_for_date is already a Pydantic model from the previous refactoring
                async with MainServiceFactory(
                    config_for_date, self.shutdown_event
                ) as factory:
                    main_orchestrator_instance = MainOrchestrator(
                        config_for_date, factory, self.shutdown_event
                    )
                    await main_orchestrator_instance.run()

                self.logger.info(f"Completed processing run for date {date}")
                return True

            except Exception as e:
                self.logger.error(
                    f"Error running MainOrchestrator for date {date}: {str(e)}",
                    exc_info=True,
                )
                return False  # Indicate failure

        except Exception as e:
            self.logger.error(
                f"Unexpected error in process_single_date for {date}: {str(e)}",
                exc_info=True,
            )
            return False

    async def check_and_process(
        self, start_date: str, end_date: str
    ) -> dict[str, list[str]]:
        """
        Process all dates between start_date and end_date inclusive.
        Returns a dictionary with results for each date.
        """
        results = {
            "successful_dates": [],
            "failed_dates": [],
            "skipped_dates": [],  # Dates where directory/files weren't found or no work needed
        }

        try:
            dates = self.generate_dates(start_date, end_date)
        except ValueError as e:
            self.logger.error(f"Failed to generate dates: {e}")
            return results

        total_dates = len(dates)
        self.logger.info(
            f"Starting batch processing of {total_dates} dates from {start_date} to {end_date}"
        )

        for index, date in enumerate(dates, 1):
            self.logger.info(f"--- Processing Date {index}/{total_dates}: {date} ---")

            try:
                success = await self.process_single_date(date)  # Now awaited
                if success:
                    # Check if the run was truly successful or just skipped
                    # Need more granular return from process_single_date or check logs?
                    # For now, assume True means success/skip
                    results["successful_dates"].append(date)  # Could be success or skip
                    self.logger.info(f"Completed run (or skipped) for date {date}.")
                else:
                    # False generally indicates an error occurred during the run
                    results["failed_dates"].append(date)
                    self.logger.warning(
                        f"Processing run for date {date} resulted in failure."
                    )

            except Exception as e:
                # This catches errors *calling* process_single_date
                self.logger.error(
                    f"FATAL: Unexpected error initiating processing for date {date}: {str(e)}",
                    exc_info=True,
                )
                results["failed_dates"].append(date)
                # Decide if to continue or stop batch on fatal error
                continue  # Continue to the next date

        # Log summary
        self.logger.info("\n=== Batch Processing Summary ===")
        self.logger.info(
            f"Completed/Skipped Runs: {len(results['successful_dates'])} dates"
        )
        # self.logger.info(f"Skipped (No work/dir): {len(results['skipped_dates'])} dates") # Distinction needs work
        self.logger.info(f"Failed Runs (Errors): {len(results['failed_dates'])} dates")
        self.logger.info("================================")

        return results


class PlaywrightSetup:
    # --- PlaywrightSetup code remains unchanged ---
    @staticmethod
    async def check_browser_installation():
        """Check if browsers are installed for Playwright."""
        logger = logging.getLogger("PlaywrightSetup")
        logger.info("Checking Playwright browser installation...")

        try:
            # Run playwright install command to ensure browsers are available
            result = subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "playwright",
                    "install",
                    "chromium",
                ],  # Use sys.executable
                capture_output=True,
                text=True,
                check=False,  # Don't raise exception on non-zero exit
            )

            if result.returncode == 0:
                logger.info(
                    "Playwright browsers check command executed successfully (might not mean installed)."
                )
                # Try a more definitive check
                return await PlaywrightSetup.run_test(silent=True)
            else:
                logger.warning(
                    "Playwright browser install check command failed. Attempting install..."
                )
                # Try installing default browsers
                install_result = subprocess.run(
                    [
                        sys.executable,
                        "-m",
                        "playwright",
                        "install",
                    ],  # Use sys.executable
                    capture_output=True,
                    text=True,
                    check=False,
                )
                if install_result.returncode == 0:
                    logger.info("Playwright install command successful.")
                    return await PlaywrightSetup.run_test(silent=True)
                else:
                    logger.error(
                        f"Playwright browser INSTALLATION failed:\nSTDOUT: {install_result.stdout}\nSTDERR: {install_result.stderr}"
                    )
                    return False
        except FileNotFoundError:
            logger.error(
                f"Could not run Playwright command. Is '{sys.executable}' in PATH and Playwright installed?"
            )
            return False
        except Exception as e:
            logger.error(
                f"Error checking/installing Playwright: {str(e)}", exc_info=True
            )
            return False

    @staticmethod
    async def run_test(silent=False):
        """Test Playwright browser launch."""
        logger = logging.getLogger("PlaywrightSetup")
        if not silent:
            logger.info("Testing Playwright browser launch...")
        pw = None
        browser = None
        try:
            pw = await async_playwright().start()
            browser = await pw.chromium.launch()
            await browser.close()
            await pw.stop()
            if not silent:
                logger.info("Playwright browser launch test successful.")
            return True
        except Exception as e:
            if not silent:
                logger.error(
                    f"Failed to launch Playwright browser: {str(e)}", exc_info=True
                )
            # Ensure cleanup even on failure
            if browser and browser.is_connected():
                await browser.close()
            if pw:
                await pw.stop()
            return False

    @staticmethod
    async def reset_playwright():
        """Ensure Playwright is properly configured."""
        logger = logging.getLogger("PlaywrightSetup")

        # First check if browsers are installed
        installed = await PlaywrightSetup.check_browser_installation()
        if not installed:
            logger.error(
                "Playwright check/installation failed. Please install manually ('python -m playwright install') or check environment."
            )
            sys.exit(1)  # Exit if setup fails

        # Test browser launch again after potential install
        success = await PlaywrightSetup.run_test()
        if not success:
            logger.error(
                "Failed to verify Playwright setup after installation attempt."
            )
            sys.exit(1)

        logger.info("Playwright setup verified successfully")


class ScraperManager:
    def __init__(self, config: dict):
        self.config = config
        self.logger = logging.getLogger(__name__)

    @staticmethod
    def _process_court_wrapper(
        args: tuple[str, dict, str | None, str | None],
    ) -> tuple[str, bool, dict[str, Any]]:
        """
        Spawn-safe worker: runs one court's scraping task using PacerOrchestrator.
        Accepts start and end dates for the orchestrator.
        """
        court_id, cfg, start_date_str, end_date_str = args  # Unpack arguments

        # --- Worker-Specific Logging Setup ---
        # Derive log directory from config passed to worker
        iso_date_worker = cfg.get(
            "iso_date", "unknown_date"
        )  # Get iso_date from config
        log_dir_base = cfg.get("directories", {}).get(
            "log_dir_base",  # Allow specifying a base dir for worker logs
            os.path.join(
                cfg.get("DATA_DIR", "./data"), iso_date_worker, "logs", "workers"
            ),
        )
        os.makedirs(log_dir_base, exist_ok=True)
        log_file = os.path.join(log_dir_base, f"worker_{court_id}_{os.getpid()}.log")

        # Setup basic file logging for the worker
        worker_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        worker_file_handler = logging.FileHandler(log_file)
        worker_file_handler.setFormatter(worker_formatter)

        # Configure the root logger for this worker process
        # Avoid interfering with parent's RichHandler
        worker_root_logger = logging.getLogger()
        # Remove handlers inherited from parent if any (important for spawn)
        for handler in worker_root_logger.handlers[:]:
            worker_root_logger.removeHandler(handler)
        worker_root_logger.addHandler(worker_file_handler)
        worker_root_logger.setLevel(logging.INFO)  # Set desired level for worker logs

        # Set library levels for worker
        for lib in ["botocore", "boto3", "urllib3", "playwright"]:
            logging.getLogger(lib).setLevel(logging.WARNING)

        logger = logging.getLogger(f"Worker-{court_id}")
        logger.info(f"Worker started for court {court_id}. Logging to: {log_file}")
        logger.info(f"Worker using config with iso_date: {iso_date_worker}")
        logger.info(
            f"Worker processing for start: {start_date_str}, end: {end_date_str}"
        )

        async def _run_orchestrator_for_court():
            """Async function to run the orchestrator for a single court."""
            try:
                # CRITICAL FIX: Ensure cfg has proper run_parallel and headless values
                # The cfg passed to workers must match the YAML configuration
                cfg_fixed = cfg.copy()
                cfg_fixed["run_parallel"] = cfg.get(
                    "run_parallel", True
                )  # Default to True for workers
                cfg_fixed["headless"] = cfg.get(
                    "headless", False
                )  # Default to False for workers

                # DEBUG: Log worker config before creating orchestrator
                logger.warning(
                    f"🔍 DEBUG WORKER: cfg_fixed['run_parallel'] = {cfg_fixed.get('run_parallel')}"
                )
                logger.warning(
                    f"🔍 DEBUG WORKER: cfg_fixed['headless'] = {cfg_fixed.get('headless')}"
                )

                # Convert cfg to Pydantic model and use MainServiceFactory with DI container
                from src.config_models.base import WorkflowConfig

                # Create a basic WorkflowConfig from the cfg_fixed dict
                workflow_config = WorkflowConfig(**cfg_fixed)

                # Use MainServiceFactory with DI container to get properly configured orchestrator
                async with MainServiceFactory(workflow_config, None) as factory:
                    orchestrator = await factory.create_pacer_orchestrator_service()

                    # PacerOrchestratorService.process_courts takes Pydantic models for dates, not strings.
                    # This part of ScraperManager might need further refactoring if cfg is a dict.
                    # For now, assuming PacerOrchestratorService handles dict config internally for date parsing.
                    # If cfg is already a Pydantic model, date conversion might happen there.
                    # The call signature for process_courts in PacerOrchestratorService is:
                    # async def process_courts(self, court_ids: List[str], context: Optional[BrowserContext],
                    #                          iso_date: Optional[str], start_date: Optional[date],
                    #                          end_date: Optional[date], docket_list_input: Optional[List[Dict]] = None)
                    # This worker passes string dates; PacerOrchestratorService will need to parse them.
                    # The new service returns a dict like {'failed_courts': [], 'court_results': []}
                    result = await orchestrator.process_courts(
                        court_ids=[court_id],
                        context=None,  # BrowserContext is managed per-court by the service in parallel mode
                        iso_date=(
                            DateUtils.format_date(
                                end_date_str, FORMAT_ISO, FORMAT_US_SHORT
                            )
                            if end_date_str
                            else None
                        ),
                        start_date=(
                            DateUtils.parse_date(start_date_str, FORMAT_US_SHORT)
                            if start_date_str
                            else None
                        ),
                        end_date=(
                            DateUtils.parse_date(end_date_str, FORMAT_US_SHORT)
                            if end_date_str
                            else None
                        ),
                        docket_list_input=cfg.get("docket_list_for_orchestrator"),
                    )
                failed_courts_internal = result.get("failed_courts", [])
                processed_stats_list_internal = result.get("court_results", [])
                success_status = court_id not in failed_courts_internal
                return success_status, (
                    processed_stats_list_internal[0]
                    if processed_stats_list_internal
                    else {}
                )
            except Exception as e:
                logger.error(
                    f"Exception during orchestrator run for {court_id}: {e}",
                    exc_info=True,
                )
                return False, {}

        try:
            # Ensure cfg passed to PacerOrchestratorService is a dict if it expects one, or Pydantic model if it expects that.
            # PacerOrchestratorService.__init__ takes config: Dict[str, Any].
            # If self.config in ScraperManager is a Pydantic model, it should be cfg.model_dump().
            # Here, cfg is passed from worker_args, derived from self.config.
            # Let's assume cfg is already a dict as PacerOrchestratorService expects.
            success, processed_stats = asyncio.run(_run_orchestrator_for_court())
            if success:
                logger.info(f"Worker finished successfully for court {court_id}")
            else:
                logger.warning(
                    f"Worker finished for court {court_id}, but processing reported failure."
                )
            return court_id, success, processed_stats
        except Exception as e:
            logger.error(
                f"Worker failed unexpectedly for court {court_id}: {e}", exc_info=True
            )
            return court_id, False, {}  # Return empty dict on worker crash

    async def run_scraper(self, court_ids: list[str]) -> None:
        """
        Decide sequential vs parallel execution.
        Fetch court list with async helper if caller passed [].
        """
        # Make a copy to avoid modifying the list passed from params
        target_court_ids = list(court_ids)

        if not target_court_ids and not self.config.get(
            "process_single_court"
        ):  # Check original param here
            self.logger.info(
                "Fetching full court list (process_single_court was empty)..."
            )
            try:
                target_court_ids = await get_court_ids_async()
            except Exception as e:
                self.logger.error(f"Could not fetch court IDs: {e}")
                return

        if not target_court_ids:
            self.logger.warning("No courts specified or fetched to scrape.")
            return

        # --- Execution Mode ---
        if self.config.get("run_parallel", False):
            self.logger.info("Running scraper in PARALLEL mode.")
            await self._run_parallel_processing(target_court_ids)
        else:
            self.logger.info("Running scraper in SEQUENTIAL mode.")
            await self._run_sequential_processing(target_court_ids)

    async def _run_parallel_processing(
        self, court_ids: list[str], max_workers: int | None = None
    ) -> None:
        """Process courts in parallel using multiprocessing."""
        if not court_ids:
            self.logger.warning("No court IDs provided for parallel scraping.")
            return

        if not max_workers:
            max_workers = self.config.get(
                "scraper_workers", multiprocessing.cpu_count()
            )
        self.logger.info(
            f"Parallel scraping starting with {max_workers} workers for {len(court_ids)} courts"
        )

        # Pass start/end dates from config to worker
        start_date_str = self.config.get("start_date")
        end_date_str = self.config.get(
            "end_date"
        )  # Use 'end_date' from config (defaults to 'date' if not set)

        # Log the config that ScraperManager is using for _append_processed_log_entry
        self.logger.info(
            f"ScraperManager (parallel) using self.config for DATA_DIR: '{self.config.get('DATA_DIR')}', iso_date: '{self.config.get('iso_date')}'"
        )

        ctx = multiprocessing.get_context("spawn")
        with ctx.Pool(processes=max_workers) as pool:
            worker_args = [
                (cid, self.config, start_date_str, end_date_str) for cid in court_ids
            ]

            results_iter = pool.imap_unordered(
                ScraperManager._process_court_wrapper,
                worker_args,
            )

            failed_courts: list[str] = []
            successful_courts: list[str] = []
            processed_count = 0
            start_time = time_module.monotonic()

            for court_id, success, processed_stats in results_iter:
                processed_count += 1
                elapsed_time = time_module.monotonic() - start_time
                avg_time = elapsed_time / processed_count if processed_count > 0 else 0
                remaining = len(court_ids) - processed_count
                eta = avg_time * remaining if avg_time > 0 else 0

                status_msg = f"Processed: {processed_count}/{len(court_ids)} | Failed: {len(failed_courts)} | Avg: {avg_time:.2f}s/court | ETA: {timedelta(seconds=int(eta))}"
                console.print(status_msg, end="\r")

                if success:
                    successful_courts.append(court_id)
                else:
                    failed_courts.append(court_id)

                if processed_stats:
                    self.logger.debug(
                        f"ScraperManager (parallel): About to call _append_processed_log_entry for court {court_id}."
                    )
                    self.logger.debug(
                        f"ScraperManager (parallel): self.config DATA_DIR: '{self.config.get('DATA_DIR')}', iso_date: '{self.config.get('iso_date')}'"
                    )
                    self.logger.debug(
                        f"ScraperManager (parallel): processed_stats for {court_id}: {processed_stats}"
                    )
                    _append_processed_log_entry(
                        self.config, processed_stats, self.logger
                    )
                else:
                    self.logger.warning(
                        f"No detailed processing stats returned for court {court_id} in parallel run. Skipping CSV log entry."
                    )

            console.print()  # Newline after loop finishes
            self.logger.info("Parallel scraping finished.")
            if failed_courts:
                self.logger.warning(
                    f"{len(failed_courts)} courts failed processing: {failed_courts}"
                )
            else:
                self.logger.info("All courts processed successfully in parallel run.")

    async def _run_sequential_processing(self, court_ids: list[str]) -> None:
        """Process courts sequentially using PacerOrchestrator."""
        if not court_ids:
            self.logger.warning("No court IDs provided for sequential processing.")
            return

        self.logger.info(
            f"Initializing PacerOrchestrator for sequential run of {len(court_ids)} courts."
        )
        # Log the config that ScraperManager is using for _append_processed_log_entry
        self.logger.info(
            f"ScraperManager (sequential) using self.config for DATA_DIR: '{self.config.get('DATA_DIR')}', iso_date: '{self.config.get('iso_date')}'"
        )

        # Instantiate orchestrator ONCE with the config
        try:
            # Directly use PacerOrchestratorService
            # If self.config is a Pydantic model, pass self.config.model_dump()
            # PacerOrchestratorService.__init__ takes config: Dict[str, Any]
            config_param = (
                self.config.model_dump()
                if hasattr(self.config, "model_dump")
                else self.config
            )

            # CRITICAL FIX: Ensure config_param has proper run_parallel and headless values
            config_param_fixed = (
                config_param.copy() if isinstance(config_param, dict) else config_param
            )
            if isinstance(config_param_fixed, dict):
                config_param_fixed["run_parallel"] = config_param_fixed.get(
                    "run_parallel", True
                )
                config_param_fixed["headless"] = config_param_fixed.get(
                    "headless", False
                )

                # DEBUG: Log sequential config before creating orchestrator
                self.logger.warning(
                    f"🔍 DEBUG SEQUENTIAL: config_param_fixed['run_parallel'] = {config_param_fixed.get('run_parallel')}"
                )
                self.logger.warning(
                    f"🔍 DEBUG SEQUENTIAL: config_param_fixed['headless'] = {config_param_fixed.get('headless')}"
                )

            # Convert config to Pydantic model and use MainServiceFactory with DI container
            from src.config_models.base import WorkflowConfig

            # Create a basic WorkflowConfig from the config_param_fixed dict
            workflow_config = WorkflowConfig(**config_param_fixed)

            # Use MainServiceFactory with DI container to get properly configured orchestrator
            async with MainServiceFactory(workflow_config, None) as factory:
                orchestrator = await factory.create_pacer_orchestrator_service()

                # The new service returns a dict like {'failed_courts': [], 'court_results': []}
                # It also expects date objects, not strings, for start_date and end_date.
                start_date_obj = (
                    DateUtils.parse_date(self.config.get("start_date"), FORMAT_US_SHORT)
                    if self.config.get("start_date")
                    else None
                )
                end_date_obj = (
                    DateUtils.parse_date(self.config.get("end_date"), FORMAT_US_SHORT)
                    if self.config.get("end_date")
                    else None
                )
                iso_date_str = (
                    DateUtils.format_date(
                        self.config.get("date"), FORMAT_ISO, FORMAT_US_SHORT
                    )
                    if self.config.get("date")
                    else None
                )

                # For sequential processing, a single browser context can be created and passed.
                # PacerOrchestratorService can create a template context if its browser_service is initialized.
                # This requires PacerOrchestratorService to have an async context manager or an explicit init/close for its browser_service.
                # For now, let PacerOrchestratorService manage its own browser context internally as it does for parallel.
                # It will create one if not provided.
                result = await orchestrator.process_courts(
                    court_ids=court_ids,
                    context=None,  # Let the service manage context
                    iso_date=iso_date_str,
                    start_date=start_date_obj,
                    end_date=end_date_obj,
                    docket_list_input=self.config.get("docket_list_for_orchestrator"),
                )
            failed_courts_seq = result.get("failed_courts", [])
            processed_stats_list_seq = result.get("court_results", [])

        except Exception as init_err:
            self.logger.error(
                f"Failed to initialize or run PacerOrchestratorService for sequential run: {init_err}",
                exc_info=True,
            )
            return

        # Append all collected stats sequentially in the main process
        if processed_stats_list_seq:
            self.logger.info(
                f"Appending {len(processed_stats_list_seq)} processed stats from sequential run."
            )
            for stats in processed_stats_list_seq:
                # Added diagnostic logging
                court_id_from_stats = stats.get("CourtID", "UNKNOWN_IN_SEQ_LOOP")
                self.logger.debug(
                    f"ScraperManager (sequential): About to call _append_processed_log_entry for court {court_id_from_stats}."
                )
                self.logger.debug(
                    f"ScraperManager (sequential): self.config DATA_DIR: '{self.config.get('DATA_DIR')}', iso_date: '{self.config.get('iso_date')}'"
                )
                self.logger.debug(
                    f"ScraperManager (sequential): stats for {court_id_from_stats}: {stats}"
                )
                _append_processed_log_entry(self.config, stats, self.logger)
        else:
            self.logger.info("No processing stats returned from sequential run.")

        if failed_courts_seq:
            self.logger.warning(
                f"Sequential processing failed for {len(failed_courts_seq)} courts: {failed_courts_seq}"
            )
        else:
            self.logger.info("All courts processed successfully in sequential run.")

    async def run_single_docket(
        self, court_id: str, docket: str, html_only: bool = False
    ) -> None:
        """Process a single docket using the standalone function."""
        self.logger.info(
            f"Processing single docket {docket} for court {court_id} (HTML only={html_only}) using standalone function."
        )
        try:
            # Assuming process_single_docket_standalone also returns processed stats
            # or it's handled internally in the PacerOrchestrator's internal logging for standalone.
            # For now, let's assume if it produces a log, it will be handled by the orchestrator itself.
            await process_single_docket_standalone(
                config=self.config,
                court_id=court_id,
                docket_num=docket,
                html_only=html_only,
            )
            self.logger.info(
                f"Single docket processing complete for {court_id}/{docket}."
            )
        except Exception as e:
            self.logger.error(
                f"Error processing single docket {court_id}/{docket}: {e}",
                exc_info=True,
            )
            # Decide if error should be re-raised


# MainProcessor class definition will be deleted from here down to where load_failed_downloads starts.
# This is a placeholder for the diff tool to remove the MainProcessor class.


def load_failed_downloads(base_data_dir, iso_date):
    """
    Load failed downloads from docket files in data/YYYYMMDD/dockets directory.
    Returns a list of dockets with 'Download failed.' in _processing_notes.
    Excludes cases with 'Matched ignore_download config.' to avoid reprocessing intentionally ignored cases.
    """
    logger = logging.getLogger(__name__)
    failed_downloads = []
    excluded_ignore_download_count = 0

    dockets_directory = Path(base_data_dir) / iso_date / "dockets"

    if not dockets_directory.exists():
        logger.warning(f"Dockets directory not found: {dockets_directory}")
        return failed_downloads

    logger.info(f"Scanning for failed downloads in: {dockets_directory}")

    json_files = list(dockets_directory.glob("*.json"))
    if not json_files:
        logger.warning(f"No JSON files found in {dockets_directory}")
        return failed_downloads

    logger.info(f"Checking {len(json_files)} docket files for failed downloads...")

    for json_file in json_files:
        try:
            with open(json_file, encoding="utf-8") as f:
                data = json.load(f)

            # Check if this file has "Download failed." in _processing_notes
            processing_notes = data.get("_processing_notes", "")
            if "Download failed." in processing_notes:
                # Exclude ignore_download cases from failed processing
                if "Matched ignore_download config." in processing_notes:
                    logger.debug(
                        f"Excluding ignore_download case from failed processing: {json_file.name}"
                    )
                    excluded_ignore_download_count += 1
                    continue

                court_id = data.get("court_id")
                docket_num = data.get("docket_num")

                if court_id and docket_num:
                    failed_downloads.append(
                        {"court_id": court_id, "docket_num": docket_num}
                    )
                    logger.debug(f"Found failed download: {court_id}/{docket_num}")
                else:
                    logger.warning(
                        f"File {json_file.name} has 'Download failed.' but missing court_id or docket_num"
                    )

        except json.JSONDecodeError:
            logger.error(f"Error reading JSON from {json_file.name}. Skipping...")
        except Exception as e:
            logger.error(f"Unexpected error reading {json_file.name}: {str(e)}")

    logger.info(
        f"Found {len(failed_downloads)} failed downloads. Excluded {excluded_ignore_download_count} ignore_download cases."
    )
    return failed_downloads


async def process_case_batch(custom_params, shutdown_event=None):
    """
    Process a batch of cases using the provided custom parameters.
    (Tailored for DateProcessor)
    """
    logger = logging.getLogger(__name__)
    config_for_batch = None
    try:
        # Config loading for process_case_batch - assuming custom_params contains 'config_yaml_path'
        if "config_yaml_path" in custom_params:
            config_name = Path(custom_params["config_yaml_path"]).stem
            base_config = load_config_new(config_name)

            # Apply date-specific overrides from custom_params
            overrides = {
                "date": custom_params.get("date"),
                "start_date": custom_params.get("start_date"),
                # testing, use_proxy, headless should be in YAML or environment
            }
            if custom_params.get(
                "base_data_path"
            ):  # If base_data_path is provided, override data_dir
                overrides["data_dir"] = custom_params.get("base_data_path")

            config_dict = base_config.model_dump()
            config_dict.update(overrides)
            config_for_batch = type(base_config)(**config_dict)
        else:
            # Fallback or error if no yaml path - this path might need more robust handling
            # For now, let's assume 'config_yaml_path' is expected.
            raise ValueError(
                "process_case_batch requires 'config_yaml_path' in custom_params."
            )

        # Setup logging specifically for this batch run if needed
        setup_rich_logging(
            config_for_batch.model_dump()
        )  # config_for_batch is Pydantic model
        logger.info(
            f"Processing cases for date {custom_params.get('date')} with derived iso_date {config_for_batch.iso_date}"
        )  # Assuming iso_date attribute

        # Ensure 'config_yaml_path' is in custom_params
        # This should already be set by the caller, but included for defensive programming
        if "config_yaml_path" not in custom_params:
            logger.warning(
                "config_yaml_path not found in custom_params for process_case_batch"
            )

        # Create orchestrator and run directly since we're already in an async context
        async with MainServiceFactory(config_for_batch, shutdown_event) as factory:
            orchestrator = MainOrchestrator(config_for_batch, factory, shutdown_event)
            await orchestrator.run()
    except Exception as e:
        logger.error(
            f"Error in process_case_batch for date {custom_params.get('date')}: {e}",
            exc_info=True,
        )
        raise  # Re-raise to signal failure to the caller (e.g., DateProcessor)


async def main(args):
    """
    Main entry point for the application. Sets parameters and runs the main processor or orchestrator.
    Ensures processing/upload happens before report generation if both are requested.

    Args:
        args: Parsed command line arguments from argparse
    """
    base_data_dir = os.environ.get(
        "LEXGENIUS_DATA_DIR", os.path.join(PROJECT_ROOT, "data")
    )
    os.environ["DATA_DIR"] = base_data_dir

    # Set up signal handlers for immediate termination
    import signal
    import sys

    def signal_handler(sig, frame):
        """Immediately terminate on signal"""
        print(f"\nReceived signal {sig}. Terminating immediately.")
        sys.exit(130)  # Exit code 130 for SIGINT

    # Register signal handlers for immediate termination
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # shutdown_event is kept as None (no graceful shutdown)
    shutdown_event = None

    params = {
        "date": None,
        "start_date": None,
        "end_date": None,
        "llm_provider": "deepseek",
        "scraper": False,
        "headless": False,
        "reset_chrome": False,
        "docket_num": None,
        "process_single_court": [],
        "docket_list_for_orchestrator": None,
        "skip_courts": [],
        "start_after_court": None,
        "start_at_court": None,
        "html_only": False,
        "run_parallel": True,
        "post_process": False,
        "reprocess_files": False,
        "start_from_incomplete": False,
        "skip_files": [],
        "reprocess_md": False,
        "force_openrouter_paid": False,
        "upload": False,
        "upload_types": ["s3", "dynamodb"],
        "force_upload": False,
        "num_workers": (os.cpu_count() or 1) * 2,
        "fb_ads": False,
        "report_generator": False,
        "skip_ads": False,
        "skip_invalidate": False,
        "weekly": False,
        "process_review_cases": False,  # Process cases that need review
        "process_review_cases_legacy": False,  # DEPRECATED: Use process_review_cases instead
        "reprocess_failed": False,  # New parameter for reprocessing failed downloads
    }

    # Arguments are now passed in from safe_run_main after being parsed outside async context

    if args.params:
        try:
            with open(args.params) as f:
                yaml_config = yaml.safe_load(f)
                if yaml_config:
                    logging.info(f"Loading parameters from YAML file: {args.params}")
                    # Update params with all YAML values first
                    params.update(yaml_config)

                    # Now, specifically handle logic for 'multiple_courts', 'process_review_cases', and 'reprocess_failed'
                    # which might override or set 'docket_list_for_orchestrator'

                    # Handle process_review_cases and reprocess_failed flags
                    docket_list_from_flags = []

                    # Get iso_date for path construction (needed for both flags)
                    temp_config_for_iso_date = None
                    iso_date = None
                    if (
                        params.get("process_review_cases")
                        or params.get("process_review_cases_legacy")
                        or params.get("reprocess_failed")
                    ):
                        # Use load_config_new here. args.params should be the path to the main YAML.
                        # The Pydantic model loaded by load_config_new should provide 'iso_date'.
                        # We only need 'date' and 'start_date' from 'params' if they are not in the YAML
                        # or if they need to override the YAML for this specific iso_date calculation.
                        # Assuming load_config_new correctly derives iso_date from the 'date' field in the YAML/overrides.
                        config_name = Path(args.params).stem
                        temp_config_for_iso_date = load_config_new(config_name, params)
                        iso_date = (
                            temp_config_for_iso_date.iso_date
                        )  # Accessing as an attribute
                        if not iso_date:
                            logging.error(
                                "Cannot determine iso_date for loading dockets. 'date' parameter might be missing or invalid in YAML."
                            )

                    # Handle process_review_cases (new) or process_review_cases_legacy (legacy)
                    if params.get("process_review_cases") or params.get(
                        "process_review_cases_legacy"
                    ):
                        flag_name = (
                            "process_review_cases"
                            if params.get("process_review_cases")
                            else "process_review_cases_legacy"
                        )
                        logging.info(
                            f"{flag_name} is True. Attempting to load dockets from review_cases_final.json."
                        )
                        if iso_date:
                            # Try new filename first, then legacy filename
                            review_cases_path = (
                                Path(base_data_dir)
                                / iso_date
                                / "logs"
                                / "review_cases_final.json"
                            )
                            legacy_cases_path = (
                                Path(base_data_dir)
                                / iso_date
                                / "logs"
                                / "review_cases_final.json"
                            )

                            cases_path = (
                                review_cases_path
                                if review_cases_path.exists()
                                else legacy_cases_path
                            )

                            if cases_path.exists():
                                try:
                                    with open(cases_path) as icf:
                                        review_dockets_data = json.load(icf)

                                    # Handle both flat and nested structures
                                    flat_dockets = []

                                    if isinstance(review_dockets_data, list):
                                        for item in review_dockets_data:
                                            if isinstance(item, dict):
                                                # Check if it's a flat docket object
                                                if (
                                                    "court_id" in item
                                                    and "docket_num" in item
                                                    and "review_cases" not in item
                                                ):
                                                    flat_dockets.append(item)
                                                # Check if it's a nested structure with review_cases
                                                elif (
                                                    "court_id" in item
                                                    and "review_cases" in item
                                                    and isinstance(
                                                        item["review_cases"], list
                                                    )
                                                ):
                                                    # Extract all cases from the nested structure
                                                    for case in item["review_cases"]:
                                                        if (
                                                            isinstance(case, dict)
                                                            and "court_id" in case
                                                            and "docket_num" in case
                                                        ):
                                                            flat_dockets.append(case)

                                    if flat_dockets:
                                        logging.info(
                                            f"Extracted {len(flat_dockets)} total dockets from {cases_path}"
                                        )

                                        # Filter out ignore_download cases from review processing
                                        try:
                                            from src.services.pacer.ignore_download_service import (
                                                PacerIgnoreDownloadService,
                                            )

                                            ignore_service = PacerIgnoreDownloadService(
                                                params
                                            )
                                            filtered_review_dockets = []
                                            excluded_count = 0

                                            for item in flat_dockets:
                                                court_id = item.get("court_id")
                                                if (
                                                    court_id
                                                    and ignore_service.should_skip_review_processing(
                                                        court_id
                                                    )
                                                ):
                                                    logging.debug(
                                                        f"Excluding ignore_download case from review processing: {court_id}/{item.get('docket_num')}"
                                                    )
                                                    excluded_count += 1
                                                else:
                                                    filtered_review_dockets.append(item)

                                            docket_list_from_flags.extend(
                                                filtered_review_dockets
                                            )
                                            logging.info(
                                                f"Loaded {len(filtered_review_dockets)} dockets from {cases_path}. Excluded {excluded_count} ignore_download cases."
                                            )

                                        except Exception as e:
                                            logging.warning(
                                                f"Error filtering ignore_download cases from review list: {e}. Using unfiltered list."
                                            )
                                            docket_list_from_flags.extend(flat_dockets)
                                            logging.info(
                                                f"Loaded {len(flat_dockets)} dockets from {cases_path}."
                                            )
                                    else:
                                        logging.warning(
                                            f"File {cases_path} does not contain a valid list of docket objects. Skipping."
                                        )
                                except json.JSONDecodeError:
                                    logging.error(
                                        f"Error decoding JSON from {cases_path}. Skipping."
                                    )
                                except Exception as e_load_review:
                                    logging.error(
                                        f"Error loading {cases_path}: {e_load_review}"
                                    )
                            else:
                                logging.warning(
                                    f"review_cases_final.json not found at {review_cases_path}. No dockets loaded for {flag_name} mode."
                                )

                    # Validate that review cases mode actually loaded dockets
                    if (
                        params.get("process_review_cases")
                        or params.get("process_review_cases_legacy")
                    ) and not docket_list_from_flags:
                        logging.error(
                            f"process_review_cases is enabled but no review cases were loaded. "
                            f"Either the review_cases_final.json file doesn't exist, is empty, or contains no valid dockets. "
                            f"Stopping execution to prevent processing all courts."
                        )
                        raise SystemExit(1)

                    if params.get("reprocess_failed"):
                        logging.info(
                            "reprocess_failed is True. Attempting to load failed downloads from docket files."
                        )
                        if iso_date:
                            try:
                                failed_downloads_data = load_failed_downloads(
                                    base_data_dir, iso_date
                                )
                                if failed_downloads_data:
                                    docket_list_from_flags.extend(failed_downloads_data)
                                    logging.info(
                                        f"Loaded {len(failed_downloads_data)} failed downloads for reprocessing."
                                    )

                                    # Save failed downloads to logs directory for reference
                                    logs_directory = (
                                        Path(base_data_dir) / iso_date / "logs"
                                    )
                                    logs_directory.mkdir(parents=True, exist_ok=True)
                                    failed_downloads_path = (
                                        logs_directory / "failed_downloads.json"
                                    )
                                    with open(failed_downloads_path, "w") as f:
                                        json.dump(failed_downloads_data, f, indent=4)
                                    logging.info(
                                        f"Saved failed downloads list to {failed_downloads_path}"
                                    )
                                else:
                                    logging.info(
                                        "No failed downloads found for reprocessing."
                                    )
                            except Exception as e_load_failed:
                                logging.error(
                                    f"Error loading failed downloads: {e_load_failed}"
                                )

                    # Process multiple_courts from YAML if present
                    mc_data = yaml_config.get("multiple_courts")
                    valid_mc_dockets = []
                    if isinstance(mc_data, list):
                        valid_mc_dockets = [
                            item
                            for item in mc_data
                            if isinstance(item, dict)
                            and "court_id" in item
                            and "docket_num" in item
                        ]

                    # Merge docket_list_from_flags (from process_review_cases/reprocess_failed)
                    # with valid_mc_dockets (from multiple_courts)
                    combined_dockets = docket_list_from_flags + valid_mc_dockets

                    # DEBUG: Log what's happening with court lists
                    logging.warning(
                        f"🔍 DEBUG: process_single_court BEFORE combined_dockets check: {params.get('process_single_court')}"
                    )
                    logging.warning(
                        f"🔍 DEBUG: docket_list_from_flags: {docket_list_from_flags}"
                    )
                    logging.warning(f"🔍 DEBUG: valid_mc_dockets: {valid_mc_dockets}")
                    logging.warning(f"🔍 DEBUG: combined_dockets: {combined_dockets}")

                    if combined_dockets:
                        params["docket_list_for_orchestrator"] = combined_dockets
                        logging.info(
                            f"Combined docket list: {len(docket_list_from_flags)} from flags + {len(valid_mc_dockets)} from multiple_courts = {len(combined_dockets)} total dockets."
                        )
                        # Clear other docket-related params to avoid conflict
                        logging.error(
                            f"⚠️ WARNING: CLEARING process_single_court which was: {params.get('process_single_court')}"
                        )
                        params["process_single_court"] = []
                        params["docket_num"] = None
                        logging.debug(
                            "Clearing 'process_single_court' and 'docket_num' as merged docket list is used."
                        )
                    elif (
                        mc_data is not None and not combined_dockets
                    ):  # If mc_data was provided but yielded no valid_mc_dockets and no flag dockets
                        logging.info(
                            f"'multiple_courts' key in YAML provided data ('{str(mc_data)[:100]}...'), but it was empty or contained only invalid/malformed entries. "
                            "It will not populate 'docket_list_for_orchestrator' or clear other court/docket params."
                        )
                    # If mc_data was None (key not present or value null in YAML), this block does nothing further,
                    # leaving params['docket_list_for_orchestrator'] as None and not clearing other params.
                else:
                    logging.warning(f"YAML config file {args.params} is empty.")
        except FileNotFoundError:
            logging.error(f"Error: YAML config file not found at {args.params}")
            sys.exit(1)
        except yaml.YAMLError as e:
            logging.error(f"Error parsing YAML config file {args.params}: {e}")
            sys.exit(1)
        except Exception as e:
            logging.error(
                f"Unexpected error loading YAML config file {args.params}: {e}"
            )
            sys.exit(1)

    logging.info(f"Final parameters: {json.dumps(params, indent=2, default=str)}")

    # --- Load Configuration (Initial Setup, this one sets up logging properly) ---
    try:
        # Extract config name from file path
        config_name = Path(args.params).stem
        initial_config_model = load_config_new(config_name, params)
        # Convert Pydantic model to dict for compatibility with existing code
        initial_config = initial_config_model.model_dump()
        initial_config["DATA_DIR"] = base_data_dir
        initial_config["data_path"] = base_data_dir
        initial_config["log_dir"] = initial_config.get("directories", {}).get(
            "log_dir",
            os.path.join(
                base_data_dir, initial_config.get("iso_date", "unknown_date"), "logs"
            ),
        )

    except Exception as config_err:
        logging.basicConfig(level=logging.ERROR)
        logging.error(
            f"CRITICAL: Failed to load initial configuration: {config_err}",
            exc_info=True,
        )
        sys.exit(1)

    setup_rich_logging(initial_config)
    logger = logging.getLogger(__name__)
    logger.info(
        f"Initial logging setup complete. Log directory: {initial_config.get('log_dir')}"
    )
    logger.info(f"Base data directory: {base_data_dir}")

    # --- Apply Court Filtering Logic for Report Scraping Mode ---
    # This filtering applies IF 'scraper' is true, AND
    # NEITHER 'docket_list_for_orchestrator' is set (by multiple_courts, process_review_cases, or process_review_cases_legacy)
    # NOR 'docket_num' (for single specific docket) is set
    # AND 'process_single_court' is initially empty (meaning all courts are intended for report scraping).

    # DEBUG: Log the state before the check
    logger.info(f"DEBUG: Before court filtering check:")
    logger.info(f"  - scraper: {params.get('scraper')}")
    logger.info(
        f"  - docket_list_for_orchestrator: {params.get('docket_list_for_orchestrator')}"
    )
    logger.info(f"  - docket_num: {params.get('docket_num')}")
    logger.info(
        f"  - process_single_court: {params.get('process_single_court')} (type: {type(params.get('process_single_court'))})"
    )
    logger.info(
        f"  - process_single_court truthiness: {bool(params.get('process_single_court'))}"
    )

    # Fixed: Check if process_single_court is None or an empty list
    # The original check would trigger for empty lists since `not []` is True
    # Also prevent processing all courts when process_review_cases is enabled
    process_single_court = params.get("process_single_court")
    if (
        params.get("scraper")
        and not params.get("docket_list_for_orchestrator")
        and not params.get("docket_num")
        and not params.get(
            "process_review_cases"
        )  # Don't process all courts if review cases mode is enabled
        and not params.get("process_review_cases_legacy")
        and (
            process_single_court is None
            or (
                isinstance(process_single_court, list)
                and len(process_single_court) == 0
            )
        )
    ):  # Only load all courts if process_single_court is truly not specified and not in review mode
        logger.info(
            "Scraper enabled for report mode (no specific dockets/courts defined in params). Fetching/filtering full court list..."
        )
        try:
            all_courts = await get_court_ids_async()
            if not all_courts:
                logger.warning(
                    "Could not retrieve court IDs for report scraping. Scraping will target no courts."
                )
                params["process_single_court"] = []
            else:
                logger.info(
                    f"Retrieved {len(all_courts)} court IDs for potential report scraping."
                )
                filtered_courts = sorted(all_courts)
                skip_list = params.get("skip_courts", [])
                if skip_list:
                    filtered_courts = [c for c in filtered_courts if c not in skip_list]
                    logger.info(
                        f"Applied skip_courts filter. Remaining for report scraping: {len(filtered_courts)} courts."
                    )
                start_after = params.get("start_after_court")
                start_at = params.get("start_at_court")
                start_index = 0
                if start_at and start_after:
                    logger.warning(
                        f"Both 'start_at_court' ({start_at}) and 'start_after_court' ({start_after}) are set. Prioritizing 'start_at_court'."
                    )
                    start_after = None
                if start_at:
                    try:
                        start_index = next(
                            i for i, c in enumerate(filtered_courts) if c >= start_at
                        )
                    except StopIteration:
                        start_index = len(filtered_courts)
                elif start_after:
                    try:
                        start_index = next(
                            i for i, c in enumerate(filtered_courts) if c > start_after
                        )
                    except StopIteration:
                        start_index = len(filtered_courts)

                params["process_single_court"] = filtered_courts[start_index:]
                if not params["process_single_court"]:
                    logger.warning(
                        "Report scraping: Filtering resulted in an empty list of courts."
                    )
                else:
                    logger.info(
                        f"Report scraping: Will target {len(params['process_single_court'])} courts after filtering."
                    )
        except Exception as filter_err:
            logger.error(
                f"Error during court ID fetching/filtering for report scraping: {filter_err}",
                exc_info=True,
            )
            params["process_single_court"] = []

    # --- Single Date Run Only (DateRange mode disabled) ---
    # start_date and end_date are used as PACER query parameters, not for sequential processing
    process_start_date = params.get("start_date")
    process_end_date = params.get("end_date")
    single_date_run = params.get("date")
    run_in_date_range_mode = False  # Always disabled

    # Ensure we have a target date
    if not single_date_run:
        logger.error("No target date ('date') specified. This parameter is required.")
        sys.exit(1)

    # If end_date is not provided, default to the target date
    if not process_end_date:
        process_end_date = single_date_run
        params["end_date"] = single_date_run
        logger.info(
            f"No end_date specified, defaulting to target date: {single_date_run}"
        )

    # Validate date formats if start_date is provided
    if process_start_date:
        try:
            start_dt = DateUtils.parse_date(process_start_date, FORMAT_US_SHORT)
            end_dt = DateUtils.parse_date(process_end_date, FORMAT_US_SHORT)
            if not start_dt or not end_dt:
                logger.error(
                    f"Could not parse dates: start='{process_start_date}', end='{process_end_date}'. Use MM/DD/YY format."
                )
                sys.exit(1)
            if start_dt > end_dt:
                logger.error(
                    f"Invalid query range: Start date {process_start_date} > end date {process_end_date}."
                )
                sys.exit(1)
            logger.info(
                f"PACER query date range: {process_start_date} to {process_end_date}"
            )
        except Exception as e:
            logger.error(f"Error validating date formats: {e}")
            sys.exit(1)

    # --- Initialize and Run ---
    if run_in_date_range_mode:
        logger.warning(
            "Running in Date Range mode. Report Generation (ReportOrchestrator) will be SKIPPED within the loop."
        )
        date_processor_params = params.copy()
        date_processor_params["base_data_path"] = base_data_dir
        date_processor_params["report_generator"] = (
            False  # Disable report gen inside loop
        )
        date_processor = DateProcessor(date_processor_params, shutdown_event)
        try:
            results = await date_processor.check_and_process(
                process_start_date, process_end_date
            )  # Awaited
            logger.info(f"Date range processing finished. Results: {results}")
        except Exception as e:
            logger.critical(
                f"Unhandled error during DateProcessor run: {str(e)}", exc_info=True
            )
            sys.exit(1)
    else:  # Single date run
        target_date = single_date_run
        logger.info(f"Preparing for single date run: {target_date}")
        try:
            # Extract config name from file path
            config_name = Path(args.params).stem
            final_config = load_config_new(
                config_name, params
            )  # final_config is Pydantic model

            # DEBUG: Log Pydantic config values
            logger.warning(
                f"🔍 DEBUG MAIN.PY: Pydantic final_config.run_parallel = {getattr(final_config, 'run_parallel', 'NOT_FOUND')}"
            )
            logger.warning(
                f"🔍 DEBUG MAIN.PY: Pydantic final_config.headless = {getattr(final_config, 'headless', 'NOT_FOUND')}"
            )
            logger.warning(
                f"🔍 DEBUG MAIN.PY: config_name = {config_name}, params keys = {list(params.keys())}"
            )
            if "run_parallel" in params:
                logger.warning(
                    f"🔍 DEBUG MAIN.PY: params['run_parallel'] = {params['run_parallel']}"
                )
            else:
                logger.warning(f"🔍 DEBUG MAIN.PY: 'run_parallel' NOT in params")
            # Ensure 'config_yaml_path' is available in params if DateProcessor needs it.
            # params['config_yaml_path'] = args.params # This was for old DateProcessor config loading.
            # Not needed if final_config is passed directly.

            setup_rich_logging(final_config.model_dump())
            logger.info(
                f"Logging re-configured for target date {target_date}. Log dir: {final_config.log_dir}"
            )
        except Exception as config_err:
            logger.critical(
                f"Failed to load final config for {target_date}: {config_err}",
                exc_info=True,
            )
            sys.exit(1)

        # The decision to run various parts (scraper, processor, uploader, fb_ads, reports)
        # is now handled by MainOrchestrator based on the WorkflowConfig (final_config).
        # We don't need run_main_processor or run_report_orchestrator flags here anymore.

        logger.info("--- Initializing Main Orchestration for Single Date Run ---")
        try:
            async with MainServiceFactory(final_config, shutdown_event) as factory:
                main_orchestrator_instance = MainOrchestrator(
                    final_config, factory, shutdown_event
                )
                await main_orchestrator_instance.run()
                logger.info(
                    "🔍 DEBUG: Main orchestrator run() completed, starting cleanup..."
                )

                # Clean up LLM clients immediately after main orchestrator completes
                # but BEFORE the factory context exits and cleans up resources
                try:
                    logger.info("=== Post-run: Direct Client Cleanup (LLM + AWS) ===")
                    import gc

                    # Find and clean up LLM clients and AWS clients directly (same logic as fallback cleanup)
                    llm_clients = []
                    aws_clients = []
                    for obj in gc.get_objects():
                        try:
                            if hasattr(obj, "__class__"):
                                class_name = obj.__class__.__name__
                                # LLM clients
                                if class_name in [
                                    "DeepSeekClient",
                                    "OpenAIClient",
                                    "GPTClient",
                                ] and hasattr(obj, "close_session"):
                                    llm_clients.append(obj)
                                # AWS/S3 clients
                                elif class_name in [
                                    "S3AsyncStorage",
                                    "DynamoDBAsync",
                                ] and hasattr(obj, "_cleanup_service"):
                                    aws_clients.append(obj)
                        except Exception:
                            # Skip objects that can't be inspected
                            continue

                    if llm_clients:
                        logger.info(
                            f"Found {len(llm_clients)} LLM clients to clean up after main orchestrator"
                        )
                        for client in llm_clients:
                            try:
                                await client.close_session()
                                logger.debug(
                                    f"Cleaned up {client.__class__.__name__} session"
                                )
                            except Exception as e:
                                logger.warning(
                                    f"Error cleaning up {client.__class__.__name__} session: {e}"
                                )
                    else:
                        logger.debug(
                            "No LLM clients found for cleanup after main orchestrator"
                        )

                    if aws_clients:
                        logger.info(
                            f"Found {len(aws_clients)} AWS clients to clean up after main orchestrator"
                        )
                        for client in aws_clients:
                            try:
                                await client._cleanup_service()
                                logger.debug(
                                    f"Cleaned up {client.__class__.__name__} service"
                                )
                            except Exception as e:
                                logger.warning(
                                    f"Error cleaning up {client.__class__.__name__} service: {e}"
                                )
                    else:
                        logger.debug(
                            "No AWS clients found for cleanup after main orchestrator"
                        )

                except Exception as cleanup_e:
                    logger.error(
                        f"🚨 ERROR in post-run cleanup: {cleanup_e}", exc_info=True
                    )

            logger.info("MainOrchestrator run completed for single date.")

        except Exception as e:
            logger.critical(
                f"Error during MainOrchestrator run for single date: {str(e)}",
                exc_info=True,
            )
            # main_processor_success = False # Old flag, error is logged by MainOrchestrator

        # Report generation is now part of MainOrchestrator.
        # The old logic for conditional report generation based on main_processor_success
        # should be implicitly handled by MainOrchestrator if it's designed to stop on critical errors.
        # If MainOrchestrator's sub-components throw exceptions, it might stop further processing.
        # The check for 'report_generator' flag and subsequent call to ReportsOrchestratorService
        # is now inside MainOrchestrator.run()

        # Old conditional logic for report orchestrator:
        # if run_report_orchestrator:
        #    if not report_depends_on_main_processor or main_processor_success:
        # ... this is now inside MainOrchestrator ...

        # Old log if no actions were enabled:
        # if not run_main_processor and not run_report_orchestrator:
        #    logger.info("No actions enabled. Processing skipped for this date.")
        # This state (no actions enabled) should be reflected by MainOrchestrator logging if it runs but does nothing.
        # If no top-level flags like scraper, post_process, upload, fb_ads, report_generator are true in WorkflowConfig,
        # MainOrchestrator will log that it's skipping those phases.
        # A specific "No actions enabled" log might be added to MainOrchestrator if desired.
        if not (
            final_config.scraper
            or final_config.post_process
            or final_config.upload
            or (hasattr(final_config, "fb_ads") and final_config.fb_ads)
            or (
                hasattr(final_config, "run_fb_ads_module")
                and final_config.run_fb_ads_module
            )
            or final_config.report_generator
        ):
            logger.info(
                "No primary actions (scraper, post_process, upload, fb_ads, report_generator) enabled in config. MainOrchestrator run might be minimal."
            )

    logger.info("Main function execution finished.")

    # Explicit cleanup for any remaining aiohttp sessions or resources
    await cleanup_async_resources()


async def cleanup_async_resources():
    """Cleanup any remaining async resources to prevent unclosed aiohttp sessions and browser contexts."""
    import logging

    cleanup_logger = logging.getLogger(__name__)

    try:
        import asyncio
        import gc

        import aiohttp

        # Try to use our resource tracker if available
        try:
            from src.lib.utils.resource_tracker import (
                cleanup_all_resources,
                resource_tracker,
            )

            # First print tracked resources (should now be clean)
            cleanup_logger.info("=== Resource Tracker Report ===")
            resource_tracker.print_unclosed_resources()

            # Then do comprehensive cleanup
            await cleanup_all_resources()

        except ImportError:
            cleanup_logger.debug(
                "Resource tracker not available, using fallback cleanup"
            )

        # Check for any open aiohttp sessions and warn
        open_sessions = []
        browser_contexts = []
        browser_services = []
        llm_clients = []

        for obj in gc.get_objects():
            try:
                if isinstance(obj, aiohttp.ClientSession) and not obj.closed:
                    # Log more details about the session for debugging
                    session_info = f"Session ID: {id(obj)}"
                    if hasattr(obj, "_connector") and obj._connector:
                        session_info += f", Connector: {type(obj._connector).__name__}"
                        if hasattr(obj._connector, "_acquired_per_host"):
                            # Show which hosts have active connections
                            active_hosts = [
                                host
                                for host, conns in obj._connector._acquired_per_host.items()
                                if conns
                            ]
                            if active_hosts:
                                session_info += f", Active hosts: {active_hosts}"
                    cleanup_logger.debug(
                        f"Found unclosed aiohttp session: {session_info}"
                    )
                    open_sessions.append(obj)
                # Check for browser contexts and services
                elif hasattr(obj, "__class__"):
                    class_name = obj.__class__.__name__
                    if class_name == "BrowserContext" and hasattr(obj, "close"):
                        context_info = f"Context ID: {id(obj)}"
                        if hasattr(obj, "_impl") and obj._impl:
                            context_info += ", has _impl"
                        cleanup_logger.debug(
                            f"Found unclosed browser context: {context_info}"
                        )
                        browser_contexts.append(obj)
                    elif class_name == "PacerBrowserService" and hasattr(obj, "close"):
                        service_info = f"Service ID: {id(obj)}"
                        if hasattr(obj, "_browser") and obj._browser:
                            service_info += ", has _browser"
                        cleanup_logger.debug(
                            f"Found unclosed browser service: {service_info}"
                        )
                        browser_services.append(obj)
                    # Check for LLM clients that might have unclosed sessions
                    elif class_name in [
                        "DeepSeekClient",
                        "OpenAIClient",
                        "GPTClient",
                    ] and hasattr(obj, "close_session"):
                        client_info = f"Client ID: {id(obj)}, Type: {class_name}"
                        if hasattr(obj, "_session") and obj._session:
                            client_info += f", Session: {id(obj._session)}"
                        cleanup_logger.debug(
                            f"Found LLM client for cleanup: {client_info}"
                        )
                        llm_clients.append(obj)
            except Exception:
                # Skip objects that can't be inspected
                continue

        if open_sessions:
            cleanup_logger.warning(
                f"Found {len(open_sessions)} unclosed aiohttp sessions - attempting to close them"
            )
            for session in open_sessions:
                try:
                    await session.close()
                    cleanup_logger.debug("Closed an aiohttp session")
                except Exception as e:
                    cleanup_logger.warning(f"Error closing session: {e}")

        if browser_contexts:
            cleanup_logger.warning(
                f"Found {len(browser_contexts)} unclosed browser contexts - attempting to close them"
            )
            for context in browser_contexts:
                try:
                    if (
                        hasattr(context, "_impl") and context._impl
                    ):  # Check if context is still valid
                        await context.close()
                        cleanup_logger.debug("Closed a browser context")
                except Exception as e:
                    cleanup_logger.warning(f"Error closing browser context: {e}")

        if browser_services:
            cleanup_logger.warning(
                f"Found {len(browser_services)} unclosed browser services - attempting to close them"
            )
            for service in browser_services:
                try:
                    await service.close()
                    cleanup_logger.debug("Closed a browser service")
                except Exception as e:
                    cleanup_logger.warning(f"Error closing browser service: {e}")

        if llm_clients:
            cleanup_logger.debug(
                f"Found {len(llm_clients)} unclosed LLM clients in fallback cleanup - this should not happen if resource manager works correctly"
            )
            for client in llm_clients:
                try:
                    await client.close_session()
                    cleanup_logger.debug(
                        f"Fallback: Closed {client.__class__.__name__} session"
                    )
                except Exception as e:
                    cleanup_logger.warning(
                        f"Error closing LLM client in fallback cleanup: {e}"
                    )

        # Force garbage collection to clean up any dangling references
        gc.collect()

        # Wait a brief moment for any pending cleanup
        await asyncio.sleep(0.1)

        cleanup_logger.info("Async resources cleanup completed")
    except Exception as e:
        cleanup_logger.warning(f"Error during async cleanup: {e}")


def parse_arguments():
    """Parse command line arguments. This must be done before creating async tasks."""
    cli_parser = argparse.ArgumentParser(description="LexGenius Processing Script")
    cli_parser.add_argument(
        "--params",
        help="Path to YAML configuration file to override default parameters.",
    )
    return cli_parser.parse_args()


def safe_run_main():
    """Safely run the main async function with cleanup."""
    loop = None
    exit_code = 0
    main_task = None

    try:
        # Parse arguments first, outside of async context to avoid SystemExit in tasks
        args = parse_arguments()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        main_task = loop.create_task(main(args), name="MainApplicationTask")
        loop.run_until_complete(main_task)

        # Check if task completed with exception
        if main_task.done() and not main_task.cancelled():
            exception = main_task.exception()
            if exception:
                raise exception

    except KeyboardInterrupt:
        # Immediately exit without cleanup
        print("\nProcess interrupted by user. Terminating immediately.")
        sys.exit(130)
    except TimeoutError:
        print("\nProcess timed out.")
        logging.error("Main execution timed out.")
        exit_code = 1
        if main_task and not main_task.done():
            main_task.cancel()
    except SystemExit as e:
        print(f"Exiting program (Code: {e.code}).")
        logging.warning(f"Exiting program (Code: {e.code}).")
        exit_code = e.code if isinstance(e.code, int) else 1
    except Exception as e:
        logging.critical(
            f"Critical error in safe_run_main execution: {str(e)}", exc_info=True
        )
        exit_code = 1
        if main_task and not main_task.done():
            main_task.cancel()
    finally:
        logging.info("Entering final cleanup phase...")
        if loop:
            try:
                if main_task and (main_task.done() or main_task.cancelled()):
                    if main_task.cancelled():
                        logging.info(
                            "Main task was cancelled. Attempting to gather results/exceptions from it."
                        )
                    try:
                        # Check if the task completed with an exception
                        if main_task.done() and not main_task.cancelled():
                            exception = main_task.exception()
                            if isinstance(exception, SystemExit):
                                logging.info(
                                    f"Main task exited with code: {exception.code}"
                                )
                                return (
                                    exception.code if exception.code is not None else 1
                                )
                            elif exception:
                                logging.error(
                                    f"Main task completed with exception: {exception}",
                                    exc_info=True,
                                )
                        elif main_task.cancelled():
                            logging.info("Main task was cancelled.")
                    except Exception as e_main_task_final:
                        logging.error(
                            f"Exception while checking main_task result: {e_main_task_final}",
                            exc_info=True,
                        )

                logging.info(
                    "Gathering all remaining tasks after main task completion/cancellation..."
                )
                pending_tasks = [
                    task for task in asyncio.all_tasks(loop=loop) if not task.done()
                ]

                if pending_tasks:
                    logging.info(
                        f"Found {len(pending_tasks)} pending tasks. Attempting to gather them with a timeout."
                    )

                    # Log task details for debugging
                    for i, task in enumerate(pending_tasks[:10]):  # Show first 10 tasks
                        task_name = (
                            task.get_name() if hasattr(task, "get_name") else "unnamed"
                        )
                        logging.debug(f"Pending task {i}: {task_name}")

                    try:
                        # Give more time for pending tasks to complete their own cleanup.
                        done, pending = loop.run_until_complete(
                            asyncio.wait(pending_tasks, timeout=20.0)
                        )
                        if done:
                            logging.info(
                                f"Completed {len(done)} tasks during cleanup wait"
                            )
                    except TimeoutError:
                        logging.warning(
                            "Timeout waiting for pending tasks to complete. Proceeding to cancel."
                        )

                    final_pending_tasks = [
                        task for task in asyncio.all_tasks(loop=loop) if not task.done()
                    ]
                    if final_pending_tasks:
                        logging.info(
                            f"Cancelling {len(final_pending_tasks)} stubborn tasks..."
                        )

                        # Cancel all tasks first
                        for task in final_pending_tasks:
                            if (
                                not task.done() and not task.cancelling()
                            ):  # Check if not already cancelling
                                task.cancel()

                        # Give tasks a chance to handle cancellation
                        try:
                            # Wait for cancellations to be processed with a shorter timeout
                            loop.run_until_complete(
                                asyncio.wait(final_pending_tasks, timeout=5.0)
                            )
                        except TimeoutError:
                            logging.warning(
                                "Some tasks did not respond to cancellation in time"
                            )

                        # Force gather remaining tasks with return_exceptions
                        remaining_tasks = [
                            task for task in final_pending_tasks if not task.done()
                        ]
                        if remaining_tasks:
                            logging.info(
                                f"Force gathering {len(remaining_tasks)} remaining tasks..."
                            )
                            loop.run_until_complete(
                                asyncio.gather(*remaining_tasks, return_exceptions=True)
                            )

                        logging.info("Stubborn tasks processed for cancellation.")
                else:
                    logging.info("No other pending tasks found after main task.")

                # Grace period for final callbacks BEFORE stopping the loop
                if loop.is_running():
                    logging.info(
                        "Loop is running. Sleeping for a short moment for final Playwright/network callbacks..."
                    )
                    try:
                        # Increased sleep duration
                        loop.run_until_complete(asyncio.sleep(1.0))
                        logging.info("Short grace sleep completed.")
                    except RuntimeError as e_sleep_runtime:
                        logging.debug(
                            f"Could not run final grace sleep, loop might have been stopped: {e_sleep_runtime}"
                        )
                    except Exception as e_final_sleep:
                        logging.error(
                            f"Error during final grace sleep: {e_final_sleep}",
                            exc_info=True,
                        )

                # Explicitly run garbage collection before stopping/closing the loop.
                if not loop.is_closed():
                    import gc

                    logging.info(
                        "Explicitly running garbage collection (first pass)..."
                    )
                    gc.collect()

                    # Give the loop a chance to process tasks scheduled by __del__ methods
                    try:
                        loop.run_until_complete(asyncio.sleep(0.1))  # Short yield
                    except RuntimeError:  # Loop might be closed by a __del__ method
                        pass

                    logging.info(
                        "Explicitly running garbage collection (second pass)..."
                    )
                    gc.collect()
                    logging.info("Garbage collection finished.")

                if loop.is_running():
                    logging.info("Stopping event loop...")
                    loop.stop()

            except Exception as e_task_cleanup:
                logging.error(
                    f"Error during asyncio task cleanup: {e_task_cleanup}",
                    exc_info=True,
                )
            finally:
                if not loop.is_closed():
                    logging.info("Closing event loop...")
                    loop.close()
                    logging.info("Event loop closed.")
                else:
                    logging.info("Event loop was already closed.")

        asyncio.set_event_loop(None)
        logging.info("Current thread's event loop policy reset.")

        logging.info("Running synchronous cleanup_everything...")
        if "cleanup_everything" in globals() and callable(cleanup_everything):
            try:
                cleanup_everything()
                logging.info("cleanup_everything completed.")
            except Exception as e_sync_cleanup:
                logging.error(
                    f"Error in synchronous cleanup_everything: {e_sync_cleanup}",
                    exc_info=True,
                )
        else:
            logging.warning(
                "cleanup_everything function not found, skipping synchronous cleanup call."
            )

        logging.info(f"safe_run_main finished with exit_code: {exit_code}")
        return exit_code


if __name__ == "__main__":
    # Setup debugging if enabled
    debug_enabled = os.environ.get("ENABLE_DEBUG", "").lower() in ("true", "1", "yes")
    # if debug_enabled:
    #     try:
    #         import pydevd_pycharm
    #
    #         pydevd_pycharm.settrace(
    #             host='127.0.0.1',
    #             port=12345,
    #             stdoutToServer=False,  # Keep terminal output intact
    #             stderrToServer=False,
    #             suspend=False  # Don't suspend immediately
    #         )
    #         print("PyCharm debugger connected")
    #     except Exception as e:
    #         print(f"Failed to connect to debugger: {e}")

    exit_code = 0
    print(f"Starting main.py with Python {sys.version}")
    try:
        # Set start method appropriately for cross-platform compatibility
        if (
            multiprocessing.get_start_method(allow_none=True) is None
            or sys.platform == "win32"
        ):
            multiprocessing.set_start_method("spawn", force=True)
            print("Set multiprocessing start method to 'spawn'.")

        # Ensure cleanup utils are available before calling them
        if "patch_resource_tracker" in globals() and callable(patch_resource_tracker):
            patch_resource_tracker()  # Apply patches early
        else:
            print(
                "WARNING: patch_resource_tracker function not found.", file=sys.stderr
            )

        if "register_cleanup" in globals() and callable(register_cleanup):
            register_cleanup()  # Setup signal and atexit handlers
        else:
            print("WARNING: register_cleanup function not found.", file=sys.stderr)

        # Call safe_run_main and store its exit code
        exit_code = safe_run_main()

    except Exception as e:
        # Catch errors during setup (logging might not be fully configured yet)
        print(f"FATAL SETUP ERROR: {str(e)}", file=sys.stderr)
        # Use basic logging if available, otherwise just print
        if logging.getLogger().handlers:  # Check if handlers are configured
            logging.critical(f"FATAL SETUP ERROR: {str(e)}", exc_info=True)
        exit_code = 1  # Exit with error
    finally:
        # The cleanup_resources call is now reliably handled by safe_run_main's finally block
        # and the atexit handler. Removing the redundant call here.
        print("Main script exit.")
        sys.exit(exit_code)  # Ensure script exits with correct code
