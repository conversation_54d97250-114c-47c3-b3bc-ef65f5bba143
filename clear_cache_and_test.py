#!/usr/bin/env python3
"""
Script to clear cache and test report generation.
"""

import os
import sys

def clear_cache_and_test():
    """Clear cache and test report generation."""
    
    print("=== CLEARING CACHE AND TESTING REPORT GENERATION ===")
    
    # Clear Python cache
    os.system("find . -name '*.pyc' -delete")
    os.system("find . -name '__pycache__' -type d -exec rm -rf {} + 2>/dev/null || true")
    
    print("✅ Cleared Python cache")
    
    # Test report generation with debug output
    print("\n--- Testing report generation ---")
    print("Run this command to test:")
    print("python src/main.py --params config/report.yml --date 07/14/25")
    
    print("\n--- Look for these debug messages in the output ---")
    print("- 🔍 PACER Query messages")
    print("- 📄 Sample item messages")
    print("- Any 'Unknown Title' warnings")
    
    print("\n--- If you still see 'Unknown Title', check: ---")
    print("1. The actual DynamoDB data (run check_dynamodb_data.py)")
    print("2. The repository field conversion")
    print("3. The service cache")

if __name__ == "__main__":
    clear_cache_and_test()