#!/usr/bin/env python3
"""
One-time script to convert uppercase court IDs to lowercase.

This script:
1. Renames docket report log files from uppercase to lowercase
2. Processes docket JSON files to convert court_id and related fields to lowercase
3. Renames docket JSON and ZIP files to use lowercase court IDs

Usage:
    python convert_court_ids_to_lowercase.py [--dry-run]
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse
from rich.console import Console
from rich.progress import Progress, TaskID
from rich.table import Table
from rich.panel import Panel

console = Console()

# Directory paths
DATA_DIR = Path("/Users/<USER>/PycharmProjects/lexgenius/data/20250703")
DOCKET_REPORT_DIR = DATA_DIR / "logs" / "docket_report"
DOCKETS_DIR = DATA_DIR / "dockets"
BACKUP_DIR = DATA_DIR / "backup_court_id_conversion"

class CourtIdConverter:
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.console = Console()
        self.changes_made = []
        
    def backup_files(self, files_to_backup: List[Path]) -> None:
        """Create backup of files before modification."""
        if not files_to_backup:
            return
            
        BACKUP_DIR.mkdir(parents=True, exist_ok=True)
        
        for file_path in files_to_backup:
            if file_path.exists():
                backup_path = BACKUP_DIR / file_path.name
                if not self.dry_run:
                    shutil.copy2(file_path, backup_path)
                    
        console.print(f"[green]✓ Backed up {len(files_to_backup)} files to {BACKUP_DIR}[/green]")
    
    def rename_docket_report_files(self) -> List[Tuple[str, str]]:
        """Rename docket report log files from uppercase to lowercase."""
        if not DOCKET_REPORT_DIR.exists():
            console.print(f"[red]Directory not found: {DOCKET_REPORT_DIR}[/red]")
            return []
            
        json_files = list(DOCKET_REPORT_DIR.glob("*.json"))
        uppercase_files = [f for f in json_files if f.stem.isupper()]
        
        if not uppercase_files:
            console.print("[yellow]No uppercase docket report files found to rename[/yellow]")
            return []
        
        renames = []
        files_to_backup = []
        
        for file_path in uppercase_files:
            new_name = file_path.stem.lower() + file_path.suffix
            new_path = file_path.parent / new_name
            
            if new_path.exists():
                console.print(f"[yellow]⚠ Target file already exists: {new_path}[/yellow]")
                continue
                
            renames.append((str(file_path), str(new_path)))
            files_to_backup.append(file_path)
        
        if files_to_backup:
            self.backup_files(files_to_backup)
            
        console.print(f"[green]Renaming {len(renames)} docket report files...[/green]")
        for old_path, new_path in renames:
            if not self.dry_run:
                os.rename(old_path, new_path)
            self.changes_made.append(f"Renamed: {Path(old_path).name} → {Path(new_path).name}")
            console.print(f"  {Path(old_path).name} → {Path(new_path).name}")
        
        return renames
    
    def process_docket_files(self) -> List[Dict]:
        """Process docket JSON files to convert court_id and related fields."""
        if not DOCKETS_DIR.exists():
            console.print(f"[red]Directory not found: {DOCKETS_DIR}[/red]")
            return []
            
        json_files = list(DOCKETS_DIR.glob("*.json"))
        processed_files = []
        
        console.print(f"[green]Processing {len(json_files)} docket files...[/green]")
        for i, json_file in enumerate(json_files, 1):
            console.print(f"  [{i}/{len(json_files)}] {json_file.name}")
            result = self._process_single_docket_file(json_file)
            if result:
                processed_files.append(result)
        
        return processed_files
    
    def _process_single_docket_file(self, json_file: Path) -> Optional[Dict]:
        """Process a single docket JSON file."""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            court_id = data.get('court_id', '')
            
            # Skip if court_id is not all uppercase
            if not court_id or not court_id.isupper():
                return None
                
            # Create backup
            self.backup_files([json_file])
            
            # Convert court_id to lowercase
            old_court_id = court_id
            new_court_id = court_id.lower()
            
            # Update JSON content
            changes = self._update_json_content(data, old_court_id, new_court_id)
            
            if not self.dry_run:
                # Write updated JSON
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Rename files (handles dry_run internally)
            self._rename_associated_files(json_file, old_court_id, new_court_id)
            
            return {
                'file': json_file.name,
                'old_court_id': old_court_id,
                'new_court_id': new_court_id,
                'changes': changes
            }
            
        except Exception as e:
            console.print(f"[red]Error processing {json_file}: {e}[/red]")
            return None
    
    def _update_json_content(self, data: Dict, old_court_id: str, new_court_id: str) -> List[str]:
        """Update JSON content replacing old court_id with new court_id."""
        changes = []
        
        def update_field(field_name: str, value):
            if isinstance(value, str) and old_court_id in value:
                new_value = value.replace(old_court_id, new_court_id)
                data[field_name] = new_value
                changes.append(f"{field_name}: {value} → {new_value}")
                return new_value
            return value
        
        # Update specific fields
        fields_to_update = ['court_id', 'base_filename', 'new_filename', 's3_html', 's3_link']
        
        for field in fields_to_update:
            if field in data:
                update_field(field, data[field])
        
        # Update any other string fields that might contain the court_id
        for key, value in data.items():
            if key not in fields_to_update and isinstance(value, str) and old_court_id in value:
                update_field(key, value)
        
        return changes
    
    def _rename_associated_files(self, json_file: Path, old_court_id: str, new_court_id: str) -> None:
        """Rename JSON and associated ZIP files."""
        # Rename associated ZIP file first (if it exists)
        zip_file = json_file.with_suffix('.zip')
        if zip_file.exists():
            new_zip_name = zip_file.name.replace(old_court_id, new_court_id, 1)
            new_zip_path = zip_file.parent / new_zip_name
            
            if new_zip_path != zip_file and not new_zip_path.exists():
                if not self.dry_run:
                    os.rename(zip_file, new_zip_path)
                self.changes_made.append(f"Renamed ZIP: {zip_file.name} → {new_zip_name}")
        
        # Rename JSON file
        new_json_name = json_file.name.replace(old_court_id, new_court_id, 1)
        new_json_path = json_file.parent / new_json_name
        
        if new_json_path != json_file and not new_json_path.exists():
            if not self.dry_run:
                os.rename(json_file, new_json_path)
            self.changes_made.append(f"Renamed JSON: {json_file.name} → {new_json_name}")
    
    def run(self) -> None:
        """Run the complete conversion process."""
        console.print(Panel.fit(
            "[bold blue]Court ID Conversion Script[/bold blue]\n"
            f"Data Directory: {DATA_DIR}\n"
            f"Mode: {'DRY RUN' if self.dry_run else 'LIVE RUN'}",
            title="Starting Conversion"
        ))
        
        # Step 1: Rename docket report files
        console.print("\n[bold]Step 1: Renaming docket report log files[/bold]")
        docket_report_renames = self.rename_docket_report_files()
        
        # Step 2: Process docket files
        console.print("\n[bold]Step 2: Processing docket JSON files[/bold]")
        processed_files = self.process_docket_files()
        
        # Display results
        self._display_results(docket_report_renames, processed_files)
    
    def _display_results(self, docket_report_renames: List[Tuple[str, str]], processed_files: List[Dict]) -> None:
        """Display summary of changes made."""
        console.print("\n[bold green]Conversion Complete![/bold green]")
        
        # Docket report renames
        if docket_report_renames:
            table = Table(title="Docket Report Files Renamed")
            table.add_column("Original", style="red")
            table.add_column("New", style="green")
            
            for old_path, new_path in docket_report_renames:
                table.add_row(Path(old_path).name, Path(new_path).name)
            
            console.print(table)
        
        # Docket files processed
        if processed_files:
            table = Table(title="Docket Files Processed")
            table.add_column("File", style="cyan")
            table.add_column("Court ID", style="yellow")
            table.add_column("Changes", style="green")
            
            for file_info in processed_files:
                changes_str = f"{len(file_info['changes'])} fields updated"
                table.add_row(
                    file_info['file'],
                    f"{file_info['old_court_id']} → {file_info['new_court_id']}",
                    changes_str
                )
            
            console.print(table)
        
        # Summary
        console.print(f"\n[bold]Summary:[/bold]")
        console.print(f"• Docket report files renamed: {len(docket_report_renames)}")
        console.print(f"• Docket files processed: {len(processed_files)}")
        console.print(f"• Total changes made: {len(self.changes_made)}")
        
        if self.dry_run:
            console.print("\n[yellow]⚠ This was a DRY RUN - no actual changes were made[/yellow]")
        else:
            console.print(f"\n[green]✓ Backup created in: {BACKUP_DIR}[/green]")


def main():
    parser = argparse.ArgumentParser(description="Convert uppercase court IDs to lowercase")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be changed without making changes")
    args = parser.parse_args()
    
    converter = CourtIdConverter(dry_run=args.dry_run)
    converter.run()


if __name__ == "__main__":
    main()