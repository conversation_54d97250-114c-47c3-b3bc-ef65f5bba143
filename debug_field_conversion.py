#!/usr/bin/env python3
"""
Debug script to test the field conversion from PascalCase to snake_case.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import re
from typing import Dict, Any, List

def pascal_to_snake(pascal_str: str) -> str:
    """Convert PascalCase to snake_case (same logic as in repository)."""
    # Insert underscore before capital letters and convert to lowercase
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", pascal_str)
    s2 = re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1)
    return s2.lower()

def convert_dict_to_snake(data: Dict[str, Any]) -> Dict[str, Any]:
    """Convert dictionary keys from PascalCase to snake_case."""
    if not isinstance(data, dict):
        return data
    
    converted = {}
    for key, value in data.items():
        snake_key = pascal_to_snake(key)
        if isinstance(value, dict):
            converted[snake_key] = convert_dict_to_snake(value)
        elif isinstance(value, list):
            converted[snake_key] = [convert_dict_to_snake(item) if isinstance(item, dict) else item for item in value]
        else:
            converted[snake_key] = value
    
    return converted

def test_field_conversion():
    """Test the field conversion logic."""
    
    print("=== TESTING FIELD CONVERSION ===")
    
    # Sample DynamoDB data (PascalCase)
    dynamodb_data = {
        'Title': 'AngioDynamics Inc. and Navilyst Medical Inc. Port Catheter Products Liability Litigation',
        'LawFirm': 'Dickerson Oxton LLC',
        'FilingDate': '20250714',
        'DocketNum': '3:25-cv-01789',
        'CourtId': 'casd',
        'AddedOn': '20250714',
        'Versus': 'Weber  v. Angiodynamics, Inc. et al',
        'MdlNum': '3125',
        'Allegations': 'Multiple plaintiffs are suing AngioDynamics...',
        'S3Link': 'https://cdn.lexgenius.ai/20250714/dockets/casd_25_01789_Weber_v_Angiodynamics_Inc_et_al.pdf',
        'S3Html': 'https://cdn.lexgenius.ai/20250714/html/casd_25_01789_Weber_v_Angiodynamics_Inc_et_al.html',
        'HtmlOnly': False,
        'IsRemoved': False,
        'PendingCto': False,
        'IsTransferred': False,
        'NumPlaintiffs': 1,
        'Claims': None
    }
    
    print("1. Original DynamoDB data (PascalCase):")
    for key, value in dynamodb_data.items():
        print(f"   {key}: {repr(value)}")
    
    # Test individual field conversion
    print(f"\n2. Individual field conversions:")
    for key in dynamodb_data.keys():
        snake_key = pascal_to_snake(key)
        print(f"   {key} → {snake_key}")
    
    # Test full dictionary conversion
    print(f"\n3. Full dictionary conversion:")
    converted_data = convert_dict_to_snake(dynamodb_data)
    
    for key, value in converted_data.items():
        print(f"   {key}: {repr(value)}")
    
    # Check specific fields
    print(f"\n4. Specific field checks:")
    title_value = converted_data.get('title')
    print(f"   title field: '{title_value}'")
    print(f"   title type: {type(title_value)}")
    print(f"   title length: {len(title_value) if title_value else 'N/A'}")
    
    law_firm_value = converted_data.get('law_firm')
    print(f"   law_firm field: '{law_firm_value}'")
    
    # Test if the converted data would cause issues
    print(f"\n5. Would this cause 'Unknown Title'?")
    is_none = title_value is None
    is_empty = title_value == ''
    is_nan_str = title_value == 'nan'
    is_whitespace = title_value.strip() == '' if title_value else False
    
    print(f"   Is None: {is_none}")
    print(f"   Is empty string: {is_empty}")
    print(f"   Is 'nan' string: {is_nan_str}")
    print(f"   Is whitespace only: {is_whitespace}")
    
    would_be_unknown = is_none or is_empty or is_nan_str or is_whitespace
    print(f"   Would become 'Unknown Title': {would_be_unknown}")
    
    return converted_data

def test_repository_conversion():
    """Test if there's an issue with the repository conversion."""
    
    print(f"\n=== TESTING REPOSITORY CONVERSION LOGIC ===")
    
    # Test the specific mapping used in PacerRepository
    pacer_mappings = {
        'title': 'Title',
        'law_firm': 'LawFirm',
        'filing_date': 'FilingDate',
        'docket_num': 'DocketNum',
        'court_id': 'CourtId',
        'added_on': 'AddedOn',
        'versus': 'Versus',
        'mdl_num': 'MdlNum',
        'allegations': 'Allegations',
        's3_link': 'S3Link',
        's3_html': 'S3Html',
        'html_only': 'HtmlOnly',
        'is_removed': 'IsRemoved',
        'pending_cto': 'PendingCto',
        'is_transferred': 'IsTransferred',
        'num_plaintiffs': 'NumPlaintiffs',
        'claims': 'Claims'
    }
    
    print("Repository field mappings (snake_case → PascalCase):")
    for snake, pascal in pacer_mappings.items():
        print(f"   {snake} → {pascal}")
    
    # Test reverse mapping
    print(f"\nReverse mapping (PascalCase → snake_case):")
    reverse_mappings = {v: k for k, v in pacer_mappings.items()}
    for pascal, snake in reverse_mappings.items():
        print(f"   {pascal} → {snake}")
    
    # Test with actual conversion function
    print(f"\nUsing pascal_to_snake function:")
    for pascal in pacer_mappings.values():
        snake = pascal_to_snake(pascal)
        expected = [k for k, v in pacer_mappings.items() if v == pascal][0]
        match = snake == expected
        print(f"   {pascal} → {snake} {'✅' if match else '❌ (expected ' + expected + ')'}")

if __name__ == "__main__":
    converted_data = test_field_conversion()
    test_repository_conversion()
    
    print(f"\n=== CONCLUSION ===")
    title_value = converted_data.get('title')
    if title_value and title_value != 'Unknown Title':
        print("✅ Field conversion is working correctly")
        print("The issue is likely elsewhere in the pipeline")
    else:
        print("❌ Field conversion has an issue")
        print("This could be causing the 'Unknown Title' problem")