#!/usr/bin/env python3
"""
Recover downloaded files from temp directories.
These files cost money and should not be lost!
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json

def recover_temp_downloads(data_dir="/Users/<USER>/PycharmProjects/lexgenius/data", date_filter=None):
    """Find and recover any PDF/ZIP files in temp directories."""
    
    temp_files_found = []
    
    # If date filter is specified, only search in that date directory
    if date_filter:
        search_dir = Path(data_dir) / date_filter
        if not search_dir.exists():
            print(f"Date directory {search_dir} does not exist.")
            return
        print(f"Searching only in date directory: {search_dir}")
    else:
        search_dir = Path(data_dir)
        print(f"Searching all of: {search_dir}")
    
    # Search for temp directories
    for root, dirs, files in os.walk(search_dir):
        if 'temp' in root or 'dl_stage' in root or 'attempt_' in root:
            for file in files:
                file_path = Path(root) / file
                # Skip hidden files and directories
                if not file.startswith('.'):
                    temp_files_found.append(file_path)
    
    if not temp_files_found:
        print("No PDF/ZIP files found in temp directories.")
        return
    
    print(f"\nFound {len(temp_files_found)} files in temp directories:")
    
    # Create recovery directory with date-specific naming
    if date_filter:
        recovery_dir = Path(data_dir) / f"recovered_files_{date_filter}"
    else:
        recovery_dir = Path(data_dir) / "recovered_downloads" / datetime.now().strftime("%Y%m%d_%H%M%S")
    recovery_dir.mkdir(parents=True, exist_ok=True)
    
    recovered_files = []
    
    for file_path in temp_files_found:
        print(f"\nFound: {file_path}")
        print(f"  Size: {file_path.stat().st_size / 1024:.1f} KB")
        print(f"  Modified: {datetime.fromtimestamp(file_path.stat().st_mtime)}")
        
        # Try to determine court and docket from path
        path_parts = str(file_path).split('/')
        court_id = None
        for part in path_parts:
            if '_ctx_dl_report' in part:
                court_id = part.split('_ctx_dl_report')[0]
                break
        
        # Copy to recovery directory
        dest_name = file_path.name
        if court_id:
            dest_name = f"{court_id}_{dest_name}"
        
        dest_path = recovery_dir / dest_name
        
        try:
            shutil.copy2(file_path, dest_path)
            print(f"  ✅ Recovered to: {dest_path}")
            recovered_files.append({
                'original': str(file_path),
                'recovered': str(dest_path),
                'size': file_path.stat().st_size,
                'court_id': court_id
            })
        except Exception as e:
            print(f"  ❌ Failed to recover: {e}")
    
    # Save recovery manifest
    if recovered_files:
        manifest_path = recovery_dir / "recovery_manifest.json"
        with open(manifest_path, 'w') as f:
            json.dump({
                'recovery_date': datetime.now().isoformat(),
                'files_recovered': len(recovered_files),
                'files': recovered_files
            }, f, indent=2)
        
        print(f"\n✅ Recovered {len(recovered_files)} files to: {recovery_dir}")
        print(f"📄 Recovery manifest saved to: {manifest_path}")
    
    return recovery_dir

if __name__ == "__main__":
    import sys
    date_filter = sys.argv[1] if len(sys.argv) > 1 else None
    recover_temp_downloads(date_filter=date_filter)