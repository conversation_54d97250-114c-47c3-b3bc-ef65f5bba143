#!/usr/bin/env python3
"""Get docket info as a Python list."""

import json
from pathlib import Path

data_dir = "data/20250617/dockets"
results = []

for json_file in Path(data_dir).glob("*.json"):
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
            court_id = data.get('court_id')
            docket_num = data.get('docket_num')
            if court_id and docket_num:
                results.append({"court_id": court_id, "docket_num": docket_num})
    except:
        pass

results.sort(key=lambda x: x['court_id'])

# Print as Python list
print(results)