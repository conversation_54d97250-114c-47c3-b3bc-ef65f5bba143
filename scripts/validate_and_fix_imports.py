#!/usr/bin/env python3
"""
Validate and fix all dependency-injector imports in the codebase.
"""
import ast
import os
from pathlib import Path


def check_file_syntax(file_path):
    """Check if a Python file has valid syntax."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, e


def has_inject_decorator(file_path):
    """Check if a file uses @inject decorator."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        return '@inject' in content
    except:
        return False


def has_inject_import(file_path):
    """Check if a file has the inject import."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        return 'from dependency_injector.wiring import inject' in content
    except:
        return False


def fix_missing_imports():
    """Find and fix files with @inject but missing import."""
    print("Checking for missing dependency-injector imports...")
    
    issues_found = []
    files_fixed = 0
    
    # Check all Python files in src/services
    for py_file in Path('src/services').rglob('*.py'):
        if '__pycache__' in str(py_file):
            continue
            
        # Check syntax first
        valid, error = check_file_syntax(py_file)
        if not valid:
            print(f"❌ Syntax error in {py_file}: {error}")
            issues_found.append((py_file, f"Syntax error: {error}"))
            continue
        
        # Check if it uses @inject but lacks import
        if has_inject_decorator(py_file) and not has_inject_import(py_file):
            print(f"⚠️  {py_file} uses @inject but missing import")
            
            # Add the import
            with open(py_file, 'r') as f:
                lines = f.readlines()
            
            # Find where to insert the import
            import_inserted = False
            for i, line in enumerate(lines):
                # Insert after other imports
                if line.strip().startswith('from ') or line.strip().startswith('import '):
                    # Find the last import
                    j = i
                    while j < len(lines) - 1 and (lines[j+1].strip().startswith('from ') or 
                                                  lines[j+1].strip().startswith('import ')):
                        j += 1
                    # Insert after last import
                    lines.insert(j + 1, 'from dependency_injector.wiring import inject, Provide\n')
                    import_inserted = True
                    break
            
            if not import_inserted:
                # If no imports found, add after docstring
                for i, line in enumerate(lines):
                    if i > 0 and line.strip() == '"""':
                        lines.insert(i + 1, '\nfrom dependency_injector.wiring import inject, Provide\n')
                        import_inserted = True
                        break
            
            if import_inserted:
                with open(py_file, 'w') as f:
                    f.writelines(lines)
                print(f"✅ Fixed {py_file}")
                files_fixed += 1
    
    return issues_found, files_fixed


def main():
    """Main validation and fixing function."""
    print("Validating and fixing dependency-injector imports...")
    print("=" * 60)
    
    # Step 1: Check and fix missing imports
    issues, fixed_count = fix_missing_imports()
    
    # Step 2: Report results
    print("=" * 60)
    print(f"✅ Fixed {fixed_count} files with missing imports")
    
    if issues:
        print(f"\n❌ Found {len(issues)} files with issues:")
        for file_path, issue in issues:
            print(f"  - {file_path}: {issue}")
        print("\nThese files need manual attention.")
    else:
        print("\n✅ All files have valid syntax and proper imports!")
    
    # Step 3: Final validation
    print("\nRunning final validation...")
    remaining_issues = 0
    
    for py_file in Path('src/services').rglob('*.py'):
        if '__pycache__' in str(py_file):
            continue
            
        valid, error = check_file_syntax(py_file)
        if not valid:
            print(f"❌ Still has syntax error: {py_file}")
            remaining_issues += 1
    
    if remaining_issues == 0:
        print("✅ All files have valid syntax!")
    else:
        print(f"❌ {remaining_issues} files still have syntax errors")


if __name__ == '__main__':
    main()