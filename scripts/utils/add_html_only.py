#!/usr/bin/env python3
"""
Add html_only: true to JSON files that don't have matching .md files.

Usage:
    python scripts/utils/add_html_only.py --date YYYYMMDD

This script:
1. Finds all JSON files in data/YYYYMMDD/dockets/*.json
2. Checks if each has a matching .md file with the same base filename
3. Updates JSON with html_only: true if key is false or doesn't exist and no .md file exists
"""

import argparse
import json
import os
from pathlib import Path
from typing import Dict, Any


def get_base_filename(filepath: Path) -> str:
    """Extract base filename without extension."""
    return filepath.stem


def has_matching_md_file(json_path: Path) -> bool:
    """Check if a JSON file has a matching .md file with the same base filename."""
    base_name = get_base_filename(json_path)
    md_path = json_path.parent / f"{base_name}.md"
    return md_path.exists()


def should_update_html_only(data: Dict[str, Any]) -> bool:
    """Check if html_only should be updated (false or missing)."""
    return data.get("html_only", False) is False


def update_json_file(json_path: Path) -> bool:
    """Update JSON file with html_only: true. Returns True if updated."""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if should_update_html_only(data):
            data["html_only"] = True
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
        
        return False
    
    except (json.JSONDecodeError, IOError) as e:
        print(f"Error processing {json_path}: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Add html_only: true to JSON files without matching .md files")
    parser.add_argument("--date", required=True, help="Date in YYYYMMDD format")
    args = parser.parse_args()
    
    # Construct path to dockets directory
    date_str = args.date
    dockets_dir = Path("data") / date_str / "dockets"
    
    if not dockets_dir.exists():
        print(f"Directory not found: {dockets_dir}")
        return
    
    # Find all JSON files
    json_files = list(dockets_dir.glob("*.json"))
    
    if not json_files:
        print(f"No JSON files found in {dockets_dir}")
        return
    
    print(f"Found {len(json_files)} JSON files in {dockets_dir}")
    
    updated_count = 0
    processed_count = 0
    
    for json_path in json_files:
        processed_count += 1
        
        # Check if matching .md file exists
        if has_matching_md_file(json_path):
            continue
        
        # Update JSON file if needed
        if update_json_file(json_path):
            updated_count += 1
            print(f"Updated: {json_path.name}")
    
    print(f"\nProcessed {processed_count} files")
    print(f"Updated {updated_count} files with html_only: true")


if __name__ == "__main__":
    main()