#!/usr/bin/env python3

import os
from typing import Dict, <PERSON><PERSON>
from PyPDF2 import PdfReader
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Prompt
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm


# Import the project root handling function
try:
    from scripts.utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config_adapter import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('/')
else:
    PROJECT_ROOT = get_project_root()


console = Console()

def validate_date(date_str: str) -> bool:
    """Validate the date string format."""
    try:
        datetime.strptime(date_str, "%Y%m%d")
        return True
    except ValueError:
        return False

def count_pdf_pages(pdf_path: str) -> int:
    """Count pages in a PDF file."""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PdfReader(file)
            return len(reader.pages)
    except Exception as e:
        console.print(f"[red]Error processing {pdf_path}: {str(e)}[/red]")
        return 0

def process_directory(dir_path: str) -> Dict[str, Tuple[int, int]]:
    """Process a directory and return {filename: (pages, size_kb)}."""
    results = {}
    
    if not os.path.exists(dir_path):
        return results

    pdf_files = [f for f in os.listdir(dir_path) if f.lower().endswith('.pdf')]
    
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = {
            executor.submit(count_pdf_pages, os.path.join(dir_path, pdf_file)): pdf_file 
            for pdf_file in pdf_files
        }
        
        for future in tqdm(as_completed(futures), total=len(pdf_files), desc=f"Processing {os.path.basename(dir_path)}"):
            pdf_file = futures[future]
            pages = future.result()
            if pages > 0:
                file_size = os.path.getsize(os.path.join(dir_path, pdf_file)) / 1024  # size in KB
                results[pdf_file] = (pages, file_size)

    return results

def display_results(date_str: str, results: Dict[str, Tuple[int, int]]):
    """Display results in a rich table format."""
    if not results:
        console.print("[yellow]No PDF files found.[/yellow]")
        return

    table = Table(title=f"PDF Analysis for {date_str}")
    table.add_column("Filename", style="cyan")
    table.add_column("Pages", justify="right", style="green")
    table.add_column("Size (KB)", justify="right", style="blue")
    table.add_column("Pages/KB", justify="right", style="yellow")

    total_pages = 0
    total_size = 0

    for filename, (pages, size) in sorted(results.items()):
        pages_per_kb = round(pages / size, 2) if size > 0 else 0
        table.add_row(
            filename,
            str(pages),
            f"{size:.1f}",
            f"{pages_per_kb:.2f}"
        )
        total_pages += pages
        total_size += size

    console.print(table)

    # Display summary panel
    avg_pages_per_file = total_pages / len(results) if results else 0
    avg_size_per_file = total_size / len(results) if results else 0
    avg_pages_per_kb = total_pages / total_size if total_size > 0 else 0

    summary = Panel(
        f"[bold green]Summary Statistics:[/bold green]\n"
        f"Total Files: [cyan]{len(results)}[/cyan]\n"
        f"Total Pages: [cyan]{total_pages:,}[/cyan]\n"
        f"Total Size: [cyan]{total_size:.1f} KB[/cyan]\n"
        f"Average Pages/File: [yellow]{avg_pages_per_file:.1f}[/yellow]\n"
        f"Average Size/File: [yellow]{avg_size_per_file:.1f} KB[/yellow]\n"
        f"Average Pages/KB: [yellow]{avg_pages_per_kb:.2f}[/yellow]",
        title="Summary",
        border_style="blue"
    )
    console.print(summary)

def main():
    console.print("[bold blue]PDF Page Counter[/bold blue]")
    
    while True:
        date_str = Prompt.ask(
            "[yellow]Enter date[/yellow]",
            default=datetime.now().strftime("%Y%m%d")
        )
        if validate_date(date_str):
            break
        console.print("[red]Invalid date format. Please use YYYYMMDD format.[/red]")

    base_path = os.path.join(PROJECT_ROOT, "data")
    docket_path = os.path.join(base_path, date_str, "dockets")

    if not os.path.exists(docket_path):
        console.print(f"[red]Directory not found: {docket_path}[/red]")
        return

    results = process_directory(docket_path)
    display_results(date_str, results)

if __name__ == "__main__":
    main()