import asyncio
import pprint
import sys

from src.lib.config_adapter import load_config
from src.repositories.pacer_repository import PacerRepository

# cases = [
#     ('scd', '2:25-cv-00024'),
#     ('scd', '2:25-cv-00042'),
#     ('scd', '2:25-cv-00043'),
#     ('scd', '2:25-cv-00044'),
#     ('scd', '2:25-cv-00045'),
#     ('scd', '2:25-cv-00046'),
#     ('scd', '2:25-cv-00047'),
#     ('scd', '2:25-cv-00048'),
#     ('scd', '2:25-cv-00049'),
#     ('scd', '2:25-cv-00050'),
#     ('scd', '2:25-cv-00051'),
#     ('scd', '2:25-cv-00052'),
#     ('scd', '2:25-cv-00053'),
#     ('scd', '2:25-cv-00054'),
#     ('scd', '2:25-cv-00055'),
#     ('scd', '2:25-cv-00056'),
#     ('scd', '2:25-cv-00057'),
#     ('scd', '2:25-cv-00065'),
#     ('scd', '2:25-cv-00066'),
#     ('scd', '2:25-cv-00067'),
#     ('scd', '2:25-cv-00068'),
#     ('scd', '2:25-cv-00069'),
#     ('scd', '2:25-cv-00070'),
#     ('scd', '2:25-cv-00071'),
#     ('scd', '2:25-cv-00072'),
#     ('scd', '2:25-cv-00078'),
#     ('scd', '2:25-cv-00079'),
#     ('scd', '2:25-cv-00081'),
#     ('scd', '2:25-cv-00082'),
#     ('scd', '2:25-cv-00083'),
#     ('scd', '2:25-cv-00085'),
#     ('scd', '2:25-cv-00086'),
#     ('scd', '2:25-cv-00087'),
#     ('scd', '2:25-cv-00088'),
#     ('scd', '2:25-cv-00089'),
#     ('scd', '2:25-cv-00090'),
#     ('scd', '2:25-cv-00091'),
#     ('scd', '2:25-cv-00092'),
#     ('scd', '2:25-cv-00093'),
#     ('scd', '2:25-cv-00094'),
#     ('scd', '2:25-cv-00095'),
#     ('scd', '2:25-cv-00096'),
#     ('scd', '2:25-cv-00097'),
#     ('scd', '2:25-cv-00098'),
#     ('scd', '2:25-cv-00099'),
#     ('scd', '2:25-cv-00100'),
#     ('scd', '2:25-cv-00101'),
#     ('scd', '2:25-cv-00102'),
#     ('scd', '2:25-cv-00103'),
#     ('scd', '2:25-cv-00104'),
#     ('scd', '2:25-cv-00105'),
#     ('scd', '2:25-cv-00106'),
#     ('scd', '2:25-cv-00107'),
#     ('scd', '2:25-cv-00108'),
#     ('scd', '2:25-cv-00109'),
#     ('scd', '2:25-cv-00110'),
#     ('scd', '2:25-cv-00113'),
#     ('scd', '2:25-cv-00114'),
#     ('scd', '2:25-cv-00115'),
#     ('scd', '2:25-cv-00117'),
#     ('scd', '2:25-cv-00118'),
#     ('scd', '2:25-cv-00119'),
#     ('scd', '2:25-cv-00120'),
#     ('scd', '2:25-cv-00121'),
#     ('scd', '2:25-cv-00125'),
#     ('scd', '2:25-cv-00126'),
#     ('scd', '2:25-cv-00134'),
#     ('scd', '2:25-cv-00135'),
#     ('scd', '2:25-cv-00136'),
#     ('scd', '2:25-cv-00137'),
#     ('scd', '2:25-cv-00138'),
#     ('scd', '2:25-cv-00140'),
#     ('scd', '2:25-cv-00141'),
#     ('scd', '2:25-cv-00142'),
#     ('scd', '2:25-cv-00143'),
#     ('scd', '2:25-cv-00144'),
#     ('scd', '2:25-cv-00146'),
#     ('scd', '2:25-cv-00147'),
#     ('scd', '2:25-cv-00148'),
#     ('scd', '2:25-cv-00149'),
#     ('scd', '2:25-cv-00150'),
#     ('scd', '2:25-cv-00151'),
#     ('scd', '2:25-cv-00152'),
#     ('scd', '2:25-cv-00159'),
#     ('scd', '2:25-cv-00169'),
#     ('scd', '2:25-cv-00175'),
#     ('scd', '2:25-cv-00176'),
#     ('scd', '2:25-cv-00177'),
#     ('scd', '2:25-cv-00191'),
#     ('scd', '2:25-cv-00192'),
#     ('scd', '2:25-cv-00193'),
#     ('scd', '2:25-cv-00194'),
#     ('scd', '2:25-cv-00204'),
#     ('scd', '2:25-cv-00219'),
#     ('scd', '2:25-cv-00221'),
#     ('scd', '2:25-cv-00223'),
#     ('scd', '2:25-cv-00224'),
#     ('scd', '2:25-cv-00225'),
#     ('scd', '2:25-cv-00227'),
#     ('scd', '2:25-cv-00233'),
#     ('scd', '2:25-cv-00234'),
#     ('scd', '2:25-cv-00235'),
#     ('scd', '2:25-cv-00236'),
#     ('scd', '2:25-cv-00237'),
#     ('scd', '2:25-cv-00238'),
#     ('scd', '2:25-cv-00239'),
#     ('scd', '2:25-cv-00240'),
#     ('scd', '2:25-cv-00241'),
#     ('scd', '2:25-cv-00242'),
#     ('scd', '2:25-cv-00252'),
#     ('scd', '2:25-cv-00253'),
#     ('scd', '2:25-cv-00254'),
#     ('scd', '2:25-cv-00255'),
#     ('scd', '2:25-cv-00256'),
#     ('scd', '2:25-cv-00257'),
#     ('scd', '2:25-cv-00258'),
#     ('scd', '2:25-cv-00259'),
#     ('scd', '2:25-cv-00260'),
#     ('scd', '2:25-cv-00261'),
#     ('scd', '2:25-cv-00262'),
#     ('scd', '2:25-cv-00263'),
#     ('scd', '2:25-cv-00269'),
#     ('scd', '2:25-cv-00270'),
#     ('scd', '2:25-cv-00271'),
#     ('scd', '2:25-cv-00282'),
#     ('scd', '2:25-cv-00283'),
#     ('scd', '2:25-cv-00284'),
#     ('scd', '2:25-cv-00285'),
#     ('scd', '2:25-cv-00287'),
#     ('scd', '2:25-cv-00288'),
#     ('scd', '2:25-cv-00289'),
#     ('scd', '2:25-cv-00290'),
#     ('scd', '2:25-cv-00291'),
#     ('scd', '2:25-cv-00292'),
#     ('scd', '2:25-cv-00295'),
#     ('scd', '2:25-cv-00296'),
#     ('scd', '2:25-cv-00297'),
#     ('scd', '2:25-cv-00298'),
#     ('scd', '2:25-cv-00299'),
#     ('scd', '2:25-cv-00303'),
#     ('scd', '2:25-cv-00304'),
#     ('scd', '2:25-cv-00305'),
#     ('scd', '2:25-cv-00306'),
#     ('scd', '2:25-cv-00307'),
#     ('scd', '2:25-cv-00308'),
#     ('scd', '2:25-cv-00313'),
#     ('scd', '2:25-cv-00319'),
#     ('scd', '2:25-cv-00320'),
#     ('scd', '2:25-cv-00321'),
#     ('scd', '2:25-cv-00322'),
#     ('scd', '2:25-cv-00323'),
#     ('scd', '2:25-cv-00324'),
#     ('scd', '2:25-cv-00325'),
#     ('scd', '2:25-cv-00326'),
#     ('scd', '2:25-cv-00327'),
#     ('scd', '2:25-cv-00328'),
#     ('scd', '2:25-cv-00329'),
#     ('scd', '2:25-cv-00339'),
#     ('scd', '2:25-cv-00340'),
#     ('scd', '2:25-cv-00341'),
#     ('scd', '2:25-cv-00342'),
#     ('scd', '2:25-cv-00343'),
#     ('scd', '2:25-cv-00346'),
#     ('scd', '2:25-cv-00348'),
#     ('scd', '2:25-cv-00349'),
#     ('scd', '2:25-cv-00351'),
#     ('scd', '2:25-cv-00352'),
#     ('scd', '2:25-cv-00353'),
#     ('scd', '2:25-cv-00354'),
#     ('scd', '2:25-cv-00355'),
#     ('scd', '2:25-cv-00356'),
#     ('scd', '2:25-cv-00357'),
#     ('scd', '2:25-cv-00358'),
#     ('scd', '2:25-cv-00359'),
#     ('scd', '2:25-cv-00360'),
#     ('scd', '2:25-cv-00361'),
#     ('scd', '2:25-cv-00362'),
#     ('scd', '2:25-cv-00363'),
#     ('scd', '2:25-cv-00364'),
#     ('scd', '2:25-cv-00365'),
#     ('scd', '2:25-cv-00366'),
#     ('scd', '2:25-cv-00367'),
#     ('scd', '2:25-cv-00368'),
#     ('scd', '2:25-cv-00369'),
#     ('scd', '2:25-cv-00370'),
#     ('scd', '2:25-cv-00372'),
#     ('scd', '2:25-cv-00373'),
#     ('scd', '2:25-cv-00374'),
#     ('scd', '2:25-cv-00375'),
#     ('scd', '2:25-cv-00376'),
#     ('scd', '2:25-cv-00377'),
#     ('scd', '2:25-cv-00378'),
#     ('scd', '2:25-cv-00382'),
#     ('scd', '2:25-cv-00385'),
#     ('scd', '2:25-cv-00386'),
#     ('scd', '2:25-cv-00388'),
#     ('scd', '2:25-cv-00391'),
#     ('scd', '2:25-cv-00392'),
#     ('scd', '2:25-cv-00395'),
#     ('scd', '2:25-cv-00397'),
#     ('scd', '2:25-cv-00399'),
#     ('scd', '2:25-cv-00400'),
#     ('scd', '2:25-cv-00401'),
#     ('scd', '2:25-cv-00410'),
#     ('scd', '2:25-cv-00411'),
#     ('scd', '2:25-cv-00412'),
#     ('scd', '2:25-cv-00413'),
#     ('scd', '2:25-cv-00414'),
#     ('scd', '2:25-cv-00415'),
#     ('scd', '2:25-cv-00417'),
#     ('scd', '2:25-cv-00419'),
#     ('scd', '2:25-cv-00422'),
#     ('scd', '2:25-cv-00423'),
#     ('scd', '2:25-cv-00424'),
#     ('scd', '2:25-cv-00425'),
#     ('scd', '2:25-cv-00426'),
#     ('scd', '2:25-cv-00449'),
#     ('scd', '2:25-cv-00450'),
#     ('scd', '2:25-cv-00451'),
#     ('scd', '2:25-cv-00452'),
#     ('scd', '2:25-cv-00456'),
#     ('scd', '2:25-cv-00460'),
#     ('scd', '2:25-cv-00461'),
#     ('scd', '2:25-cv-00462'),
#     ('scd', '2:25-cv-00464'),
#     ('scd', '2:25-cv-00465'),
#     ('scd', '2:25-cv-00468'),
#     ('scd', '2:25-cv-00469'),
#     ('scd', '2:25-cv-00470'),
#     ('scd', '2:25-cv-00473'),
#     ('scd', '2:25-cv-00478'),
#     ('scd', '2:25-cv-00482'),
#     ('scd', '2:25-cv-00483'),
#     ('scd', '2:25-cv-00484'),
#     ('scd', '2:25-cv-00501'),
#     ('scd', '2:25-cv-00502'),
#     ('scd', '2:25-cv-00503'),
#     ('scd', '2:25-cv-00504'),
#     ('scd', '2:25-cv-00505'),
#     ('scd', '2:25-cv-00506'),
#     ('scd', '2:25-cv-00507'),
#     ('scd', '2:25-cv-00522'),
#     ('scd', '2:25-cv-00523'),
#     ('scd', '2:25-cv-00524'),
#     ('scd', '2:25-cv-00525'),
#     ('scd', '2:25-cv-00526'),
#     ('scd', '2:25-cv-00527'),
#     ('scd', '2:25-cv-00528'),
#     ('scd', '2:25-cv-00530'),
#     ('scd', '2:25-cv-00540'),
#     ('scd', '2:25-cv-00541'),
#     ('scd', '2:25-cv-00542'),
#     ('scd', '2:25-cv-00543'),
#     ('scd', '2:25-cv-00544'),
#     ('scd', '2:25-cv-00545'),
#     ('scd', '2:25-cv-00546'),
#     ('scd', '2:25-cv-00547'),
#     ('scd', '2:25-cv-00548'),
#     ('scd', '2:25-cv-00549'),
# ]

async def check_dockets_in_db(cases):
    """Check if the specified dockets exist in the database."""
    config = load_config()
    repository = PacerRepository()
    
    async with repository:
        found_cases = []
        missing_cases = []
        
        for court_id, case_number in cases:
            try:
                # Search for docket by court and case number
                results = await repository.query_by_court_and_case(court_id, case_number)
                if results:
                    found_cases.append((court_id, case_number))
                    print(f"✓ Found: {court_id} - {case_number}")
                else:
                    missing_cases.append((court_id, case_number))
                    print(f"✗ Missing: {court_id} - {case_number}")
            except Exception as e:
                missing_cases.append((court_id, case_number))
                print(f"✗ Error checking {court_id} - {case_number}: {e}")
        
        print(f"\nSummary:")
        print(f"Found: {len(found_cases)}")
        print(f"Missing: {len(missing_cases)}")
        
        if missing_cases:
            print("\nMissing cases:")
            pprint.pprint(missing_cases)
        
        return found_cases, missing_cases

if __name__ == "__main__":
    # Example usage - uncomment and modify the cases list above to use
    test_cases = [
        ('scd', '2:25-cv-00024'),
        ('scd', '2:25-cv-00042'),
    ]
    
    if len(sys.argv) > 1:
        court_id = sys.argv[1]
        case_number = sys.argv[2] if len(sys.argv) > 2 else None
        if case_number:
            test_cases = [(court_id, case_number)]
        else:
            print("Usage: python check_if_docket_in_db.py <court_id> <case_number>")
            sys.exit(1)
    
    asyncio.run(check_dockets_in_db(test_cases))
