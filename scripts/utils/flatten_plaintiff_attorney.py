#!/usr/bin/env python3
"""
Flatten plaintiff and attorney data structure in JSON files.

This script processes JSON files in data/20250613/dockets/ and flattens the plaintiff
structure by separating plaintiff names from attorney information.

Original structure:
"plaintiff": [
    {
        "name": "Plaintiff Name",
        "attorneys": [...]
    }
]

Flattened structure:
"plaintiff": ["Plaintiff Name"],
"attorney": [...]
"""

import json
import os
import argparse
from pathlib import Path
from typing import Dict, List, Any
from rich.console import Console
from rich.progress import Progress, TaskID


def flatten_plaintiff_attorney_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Flatten plaintiff data structure by separating names from attorneys.
    Also handles the newer 'plaintiffs' field format and deduplicates attorneys.
    
    Args:
        data: JSON data dictionary
        
    Returns:
        Modified data dictionary with flattened structure
    """
    # Check for both 'plaintiff' and 'plaintiffs' fields
    plaintiff_field = None
    if "plaintiffs" in data:
        plaintiff_field = "plaintiffs"
    elif "plaintiff" in data:
        plaintiff_field = "plaintiff"
    else:
        return data
    
    plaintiffs = data[plaintiff_field]
    if not isinstance(plaintiffs, list):
        return data
    
    # Extract plaintiff names and collect attorneys
    plaintiff_names = []
    attorney_dict = {}  # Use dict to deduplicate by (name, law_firm) tuple
    
    for plaintiff in plaintiffs:
        if isinstance(plaintiff, dict):
            # Add plaintiff name if it exists
            if "name" in plaintiff:
                plaintiff_names.append(plaintiff["name"])
            
            # Collect all attorneys
            if "attorneys" in plaintiff and isinstance(plaintiff["attorneys"], list):
                for attorney in plaintiff["attorneys"]:
                    if isinstance(attorney, dict):
                        # Create unique key based on attorney name and law firm
                        key = (
                            attorney.get("attorney_name", ""),
                            attorney.get("law_firm", "")
                        )
                        # Only add if we haven't seen this attorney/firm combination
                        if key not in attorney_dict and key != ("", ""):
                            attorney_dict[key] = attorney
    
    # Update the data structure
    data[plaintiff_field] = plaintiff_names
    
    # Convert deduplicated attorneys to list
    if attorney_dict:
        data["attorney"] = list(attorney_dict.values())
    
    return data


def process_json_file(file_path: Path, console: Console, dry_run: bool = False) -> bool:
    """
    Process a single JSON file to flatten plaintiff/attorney data.
    
    Args:
        file_path: Path to the JSON file
        console: Rich console for output
        dry_run: If True, only show what would be changed without modifying files
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Read JSON file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Flatten the data
        has_plaintiffs = ("plaintiff" in data or "plaintiffs" in data)
        if has_plaintiffs:
            plaintiff_field = "plaintiffs" if "plaintiffs" in data else "plaintiff"
            original_structure = isinstance(data.get(plaintiff_field), list)
            
            if original_structure and data[plaintiff_field] and isinstance(data[plaintiff_field][0], dict):
                # Create a copy for dry run
                flattened_data = flatten_plaintiff_attorney_data(data.copy())
                
                if dry_run:
                    # Show the transformation
                    console.print(f"\n[cyan]File: {file_path.name}[/cyan]")
                    console.print("[yellow]Before:[/yellow]")
                    console.print(f"  {plaintiff_field}: {len(data[plaintiff_field])} entries with nested attorneys")
                    
                    # Count total attorneys before deduplication
                    total_attorneys = sum(
                        len(p.get("attorneys", [])) 
                        for p in data[plaintiff_field] 
                        if isinstance(p, dict)
                    )
                    
                    console.print(f"  Total attorneys (with duplicates): {total_attorneys}")
                    
                    console.print("\n[green]After:[/green]")
                    console.print(f"  {plaintiff_field}: {flattened_data[plaintiff_field]}")
                    console.print(f"  attorney: {len(flattened_data.get('attorney', []))} unique attorneys")
                    
                    # Show unique attorneys
                    if "attorney" in flattened_data:
                        console.print("\n  [dim]Unique attorneys:[/dim]")
                        for att in flattened_data["attorney"]:
                            console.print(f"    - {att.get('attorney_name')} ({att.get('law_firm')})")
                else:
                    # Actually write the file
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(flattened_data, f, indent=4, ensure_ascii=False)
                
                return True
        
        console.print(f"[yellow]Skipped {file_path.name} - no plaintiff structure to flatten[/yellow]")
        return False
            
    except Exception as e:
        console.print(f"[red]Error processing {file_path.name}: {e}[/red]")
        return False


def main():
    """Main function to process all JSON files in the dockets directory."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Flatten plaintiff and attorney data in JSON files')
    parser.add_argument('--date', type=str, default='20250613',
                        help='Date in YYYYMMDD format (default: 20250613)')
    parser.add_argument('--dry-run', action='store_true',
                        help='Show what would be changed without modifying files')
    args = parser.parse_args()
    
    console = Console()
    
    # Define the dockets directory based on the date
    dockets_dir = Path(f"data/{args.date}/dockets")
    
    if not dockets_dir.exists():
        console.print(f"[red]Directory not found: {dockets_dir}[/red]")
        return
    
    # Find all JSON files
    json_files = list(dockets_dir.glob("*.json"))
    
    if not json_files:
        console.print(f"[yellow]No JSON files found in {dockets_dir}[/yellow]")
        return
    
    if args.dry_run:
        console.print(f"[cyan]DRY RUN MODE - No files will be modified[/cyan]")
    
    console.print(f"[green]Found {len(json_files)} JSON files to process[/green]")
    
    # Process files with progress bar
    processed_count = 0
    skipped_count = 0
    
    with Progress() as progress:
        task = progress.add_task("[green]Processing files...", total=len(json_files))
        
        for json_file in json_files:
            if process_json_file(json_file, console, dry_run=args.dry_run):
                processed_count += 1
            else:
                skipped_count += 1
            
            progress.update(task, advance=1)
    
    # Summary
    console.print(f"\n[green]Processing complete![/green]")
    console.print(f"Files processed: {processed_count}")
    console.print(f"Files skipped: {skipped_count}")
    console.print(f"Total files: {len(json_files)}")
    
    if args.dry_run:
        console.print(f"\n[cyan]This was a dry run - no files were modified[/cyan]")


if __name__ == "__main__":
    main()