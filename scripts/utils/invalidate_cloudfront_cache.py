#!/usr/bin/env python3
"""
CloudFront Cache Invalidation Utility

This script allows you to invalidate specific paths in CloudFront cache.
Useful for manually clearing cache when needed.

Usage:
    python scripts/utils/invalidate_cloudfront_cache.py 20250610/*
    python scripts/utils/invalidate_cloudfront_cache.py assets/*
    python scripts/utils/invalidate_cloudfront_cache.py /index.html
    python scripts/utils/invalidate_cloudfront_cache.py 20250610/ads/* 20250610/dockets/*
"""

import asyncio
import argparse
import logging
import os
import sys
from typing import List
from dotenv import load_dotenv

# Add project root to path so we can import modules
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

# Load environment variables from .env file
env_path = os.path.join(project_root, '.env')
load_dotenv(env_path)

try:
    from src.infrastructure.messaging.cloudfront import CloudFrontService
except ImportError:
    print("ERROR: CloudFrontService not found. Make sure you're running from the project root.")
    sys.exit(1)


def setup_logging():
    """Setup basic logging for the script."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def normalize_paths(paths: List[str]) -> List[str]:
    """Normalize cache paths to ensure they start with /."""
    normalized = []
    for path in paths:
        # Ensure path starts with /
        if not path.startswith('/'):
            path = '/' + path
        normalized.append(path)
    return normalized


async def invalidate_cache_paths(paths: List[str]) -> bool:
    """Invalidate the specified CloudFront cache paths."""
    logger = logging.getLogger(__name__)
    
    # Get CloudFront distribution ID from environment
    distribution_id = os.environ.get('CLOUDFRONT_DISTRIBUTION_ID')
    if not distribution_id:
        logger.error("CLOUDFRONT_DISTRIBUTION_ID environment variable not set")
        return False
    
    # Get AWS credentials from environment
    aws_access_key = os.environ.get('AWS_ACCESS_KEY')
    aws_secret_key = os.environ.get('AWS_SECRET_KEY')
    
    if not aws_access_key or not aws_secret_key:
        logger.error("AWS credentials not found in environment variables")
        logger.error("Set AWS_ACCESS_KEY and AWS_SECRET_KEY")
        return False
    
    # Normalize paths
    normalized_paths = normalize_paths(paths)
    
    logger.info(f"Invalidating {len(normalized_paths)} paths in CloudFront distribution: {distribution_id}")
    logger.info(f"Paths: {normalized_paths}")
    
    try:
        # Create CloudFront service
        cf_service = CloudFrontService(
            distribution_id=distribution_id,
            aws_access_key=aws_access_key,
            aws_secret_key=aws_secret_key
        )
        
        # Invalidate paths
        async with cf_service as cf:
            success = await cf.invalidate_paths(normalized_paths)
            
        if success:
            logger.info("✅ CloudFront invalidation submitted successfully")
            return True
        else:
            logger.error("❌ CloudFront invalidation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error during CloudFront invalidation: {e}")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Invalidate CloudFront cache paths",
        epilog="""
Examples:
  %(prog)s 20250610/*
  %(prog)s assets/*
  %(prog)s /index.html
  %(prog)s 20250610/ads/* 20250610/dockets/*
  %(prog)s 20250610/reports/mdl_summary_20250610.json
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        'paths',
        nargs='+',
        help='CloudFront paths to invalidate (e.g., 20250610/*, assets/*, /index.html)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be invalidated without actually doing it'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    setup_logging()
    
    logger = logging.getLogger(__name__)
    
    if args.dry_run:
        normalized_paths = normalize_paths(args.paths)
        logger.info("🔍 DRY RUN: Would invalidate the following paths:")
        for path in normalized_paths:
            logger.info(f"  - {path}")
        return
    
    # Check environment variables
    if not os.environ.get('CLOUDFRONT_DISTRIBUTION_ID'):
        logger.error("❌ CLOUDFRONT_DISTRIBUTION_ID environment variable not set")
        logger.error("💡 Make sure your .env file contains: CLOUDFRONT_DISTRIBUTION_ID=your_distribution_id")
        sys.exit(1)
    
    if not os.environ.get('AWS_ACCESS_KEY') or not os.environ.get('AWS_SECRET_KEY'):
        logger.error("❌ AWS credentials not found in environment variables")
        logger.error("💡 Make sure your .env file contains:")
        logger.error("   AWS_ACCESS_KEY=your_access_key")
        logger.error("   AWS_SECRET_KEY=your_secret_key")
        sys.exit(1)
    
    # Run the invalidation
    try:
        success = asyncio.run(invalidate_cache_paths(args.paths))
        if success:
            logger.info("✅ Cache invalidation completed successfully")
            sys.exit(0)
        else:
            logger.error("❌ Cache invalidation failed")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("⏹️  Interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()