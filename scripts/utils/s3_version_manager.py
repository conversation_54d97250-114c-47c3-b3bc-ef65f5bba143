#!/usr/bin/env python3
"""
S3 Version Management Utility

This script provides utilities for managing S3 object versions:
1. Upload files with guaranteed overwrites (no versioning)
2. Clean up old versions while keeping the latest
3. Bulk version cleanup for multiple objects

Usage:
    python scripts/utils/s3_version_manager.py --help
"""

import asyncio
import argparse
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.storage.s3_async import S3AsyncStorage
from src.infrastructure.protocols.logger import LoggerProtocol


class SimpleLogger:
    """Simple logger implementation for standalone script."""
    
    def debug(self, msg: str):
        print(f"[DEBUG] {msg}")
    
    def info(self, msg: str):
        print(f"[INFO] {msg}")
    
    def warning(self, msg: str):
        print(f"[WARNING] {msg}")
    
    def error(self, msg: str):
        print(f"[ERROR] {msg}")


async def upload_with_overwrite_demo(s3_service: S3AsyncStorage, object_key: str, content: str):
    """Demonstrate upload with guaranteed overwrite."""
    print(f"\n=== Upload with Overwrite Demo ===")
    print(f"Object Key: {object_key}")
    print(f"Content Length: {len(content)} bytes")
    
    # Upload with overwrite
    success = await s3_service.upload_with_overwrite(content, object_key, "text/plain")
    
    if success:
        print("✅ Upload with overwrite successful")
    else:
        print("❌ Upload with overwrite failed")
    
    return success


async def cleanup_versions_demo(s3_service: S3AsyncStorage, object_key: str, keep_latest: int = 1):
    """Demonstrate version cleanup for a single object."""
    print(f"\n=== Version Cleanup Demo ===")
    print(f"Object Key: {object_key}")
    print(f"Keep Latest: {keep_latest} versions")
    
    # Cleanup old versions
    result = await s3_service.cleanup_old_versions(object_key, keep_latest)
    
    if "error" in result:
        print(f"❌ Cleanup failed: {result['error']}")
        return False
    
    print("✅ Cleanup successful:")
    print(f"  - Versions deleted: {result['versions_deleted']}")
    print(f"  - Versions kept: {result['versions_kept']}")
    print(f"  - Delete markers removed: {result['delete_markers_removed']}")
    print(f"  - Total versions before: {result['total_versions_before']}")
    
    return True


async def bulk_cleanup_demo(s3_service: S3AsyncStorage, prefix: str, keep_latest: int = 1):
    """Demonstrate bulk version cleanup."""
    print(f"\n=== Bulk Cleanup Demo ===")
    print(f"Prefix: '{prefix}'")
    print(f"Keep Latest: {keep_latest} versions per object")
    
    # Bulk cleanup
    result = await s3_service.bulk_cleanup_versions(prefix, keep_latest)
    
    if "error" in result:
        print(f"❌ Bulk cleanup failed: {result['error']}")
        return False
    
    print("✅ Bulk cleanup successful:")
    print(f"  - Objects processed: {result['objects_processed']}")
    print(f"  - Objects with errors: {result['objects_with_errors']}")
    print(f"  - Total versions deleted: {result['total_versions_deleted']}")
    print(f"  - Total versions kept: {result['total_versions_kept']}")
    
    # Show individual results if requested
    if result['objects_with_errors'] > 0:
        print("\n⚠️  Objects with errors:")
        for obj_result in result['results']:
            if 'error' in obj_result:
                print(f"  - {obj_result['object_key']}: {obj_result['error']}")
    
    return True


async def list_object_versions(s3_service: S3AsyncStorage, object_key: str):
    """List all versions of a specific object."""
    print(f"\n=== Object Versions ===")
    print(f"Object Key: {object_key}")
    
    try:
        await s3_service.initialize()
        
        response = await s3_service._client.list_object_versions(
            Bucket=s3_service.bucket_name,
            Prefix=object_key
        )
        
        versions = [v for v in response.get("Versions", []) if v["Key"] == object_key]
        delete_markers = [m for m in response.get("DeleteMarkers", []) if m["Key"] == object_key]
        
        if not versions and not delete_markers:
            print("  No versions found")
            return
        
        # Sort versions by date
        versions.sort(key=lambda x: x["LastModified"], reverse=True)
        
        print(f"  Found {len(versions)} versions:")
        for i, version in enumerate(versions):
            status = "LATEST" if i == 0 else "OLD"
            print(f"    {status}: {version['VersionId']} ({version['LastModified']}) - {version['Size']} bytes")
        
        if delete_markers:
            print(f"  Found {len(delete_markers)} delete markers:")
            for marker in delete_markers:
                print(f"    MARKER: {marker['VersionId']} ({marker['LastModified']})")
    
    except Exception as e:
        print(f"❌ Error listing versions: {e}")


async def main():
    parser = argparse.ArgumentParser(description="S3 Version Management Utility")
    parser.add_argument("--bucket", required=True, help="S3 bucket name")
    parser.add_argument("--region", default="us-west-2", help="AWS region")
    parser.add_argument("--action", choices=["upload", "cleanup", "bulk-cleanup", "list-versions"], 
                        required=True, help="Action to perform")
    parser.add_argument("--object-key", help="S3 object key")
    parser.add_argument("--prefix", help="S3 object prefix for bulk operations")
    parser.add_argument("--content", help="Content to upload (for upload action)")
    parser.add_argument("--file", help="File to upload (for upload action)")
    parser.add_argument("--keep-latest", type=int, default=1, help="Number of latest versions to keep")
    parser.add_argument("--disable-versioning", action="store_true", help="Disable versioning controls")
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.action in ["upload", "cleanup", "list-versions"] and not args.object_key:
        print("❌ --object-key is required for this action")
        return 1
    
    if args.action == "bulk-cleanup" and not args.prefix:
        print("❌ --prefix is required for bulk-cleanup action")
        return 1
    
    if args.action == "upload" and not args.content and not args.file:
        print("❌ Either --content or --file is required for upload action")
        return 1
    
    # Get content for upload
    content = None
    if args.action == "upload":
        if args.file:
            if not os.path.exists(args.file):
                print(f"❌ File not found: {args.file}")
                return 1
            with open(args.file, 'r') as f:
                content = f.read()
        else:
            content = args.content
    
    # Initialize S3 service
    logger = SimpleLogger()
    
    s3_config = {
        "bucket_name": args.bucket,
        "aws_region": args.region,
        # AWS credentials will be loaded from environment
    }
    
    s3_service = S3AsyncStorage(
        logger=logger,
        config=s3_config,
        disable_versioning=not args.disable_versioning
    )
    
    try:
        await s3_service.initialize()
        print(f"✅ Connected to S3 bucket: {args.bucket}")
        
        # Perform requested action
        if args.action == "upload":
            success = await upload_with_overwrite_demo(s3_service, args.object_key, content)
            return 0 if success else 1
        
        elif args.action == "cleanup":
            success = await cleanup_versions_demo(s3_service, args.object_key, args.keep_latest)
            return 0 if success else 1
        
        elif args.action == "bulk-cleanup":
            success = await bulk_cleanup_demo(s3_service, args.prefix, args.keep_latest)
            return 0 if success else 1
        
        elif args.action == "list-versions":
            await list_object_versions(s3_service, args.object_key)
            return 0
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    finally:
        await s3_service.cleanup()


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))