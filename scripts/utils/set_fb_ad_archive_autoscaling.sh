#!/bin/bash

# Configuration
TABLE_NAME="FBAdArchive"
MAX_CAPACITY=15
MIN_CAPACITY=1
TARGET_UTILIZATION=50.0
COOLDOWN_PERIOD=10

# List all GSIs
echo "Fetching Global Secondary Indexes for table $TABLE_NAME..."
INDEXES=($(aws dynamodb describe-table \
  --table-name $TABLE_NAME \
  --query "Table.GlobalSecondaryIndexes[].IndexName" \
  --output text))

if [ ${#INDEXES[@]} -eq 0 ]; then
  echo "No GSIs found for table $TABLE_NAME"
  exit 0
fi

echo "Configuring auto-scaling for ${#INDEXES[@]} GSIs: ${INDEXES[*]}"

# Configure each GSI
for INDEX in "${INDEXES[@]}"; do
  echo -e "\nProcessing GSI: $INDEX"

  RESOURCE_ID="table/$TABLE_NAME/index/$INDEX"

  # Delete existing policies first
  echo "Removing existing scaling policies..."
  for DIMENSION in "ReadCapacityUnits" "WriteCapacityUnits"; do
    POLICIES=$(aws application-autoscaling describe-scaling-policies \
      --service-namespace dynamodb \
      --resource-id "$RESOURCE_ID" \
      --scalable-dimension "dynamodb:index:$DIMENSION" \
      --query "ScalingPolicies[].PolicyName" \
      --output text)

    for POLICY in $POLICIES; do
      echo "Deleting policy $POLICY"
      aws application-autoscaling delete-scaling-policy \
        --service-namespace dynamodb \
        --resource-id "$RESOURCE_ID" \
        --scalable-dimension "dynamodb:index:$DIMENSION" \
        --policy-name "$POLICY"
    done
  done

  # Configure Read Capacity
  echo "Setting up Read Capacity auto-scaling..."
  aws application-autoscaling register-scalable-target \
    --service-namespace dynamodb \
    --resource-id "$RESOURCE_ID" \
    --scalable-dimension "dynamodb:index:ReadCapacityUnits" \
    --min-capacity $MIN_CAPACITY \
    --max-capacity $MAX_CAPACITY

  aws application-autoscaling put-scaling-policy \
    --policy-name "$INDEX-ReadScaling" \
    --service-namespace dynamodb \
    --resource-id "$RESOURCE_ID" \
    --scalable-dimension "dynamodb:index:ReadCapacityUnits" \
    --policy-type "TargetTrackingScaling" \
    --target-tracking-scaling-policy-configuration "{
        \"TargetValue\": $TARGET_UTILIZATION,
        \"ScaleInCooldown\": $COOLDOWN_PERIOD,
        \"ScaleOutCooldown\": $COOLDOWN_PERIOD,
        \"PredefinedMetricSpecification\": {
            \"PredefinedMetricType\": \"DynamoDBReadCapacityUtilization\"
        }
    }"

  # Configure Write Capacity
  echo "Setting up Write Capacity auto-scaling..."
  aws application-autoscaling register-scalable-target \
    --service-namespace dynamodb \
    --resource-id "$RESOURCE_ID" \
    --scalable-dimension "dynamodb:index:WriteCapacityUnits" \
    --min-capacity $MIN_CAPACITY \
    --max-capacity $MAX_CAPACITY

  aws application-autoscaling put-scaling-policy \
    --policy-name "$INDEX-WriteScaling" \
    --service-namespace dynamodb \
    --resource-id "$RESOURCE_ID" \
    --scalable-dimension "dynamodb:index:WriteCapacityUnits" \
    --policy-type "TargetTrackingScaling" \
    --target-tracking-scaling-policy-configuration "{
        \"TargetValue\": $TARGET_UTILIZATION,
        \"ScaleInCooldown\": $COOLDOWN_PERIOD,
        \"ScaleOutCooldown\": $COOLDOWN_PERIOD,
        \"PredefinedMetricSpecification\": {
            \"PredefinedMetricType\": \"DynamoDBWriteCapacityUtilization\"
        }
    }"

  echo "Successfully configured auto-scaling for $INDEX"
done

echo -e "\nAuto-scaling configuration completed for all GSIs!"