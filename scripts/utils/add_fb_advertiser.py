#!/usr/bin/env python3

import argparse
import subprocess
import sys
import os

def main():
    parser = argparse.ArgumentParser(description='Add a Facebook advertiser using the orchestrator script.')
    parser.add_argument('--mobile-proxy', action='store_true', help='Use a mobile proxy for the operation.')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    args = parser.parse_args()

    # Get the project root directory
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
    
    # Set up the Python path to include the project root
    env = os.environ.copy()
    python_path = env.get('PYTHONPATH', '')
    if python_path:
        env['PYTHONPATH'] = f"{project_root}:{python_path}"
    else:
        env['PYTHONPATH'] = project_root
    
    # Build the command to run the orchestrator CLI
    cmd = [sys.executable, '-m', 'src.services.orchestration.fb_ads_orchestrator_cli', '--add', '--defer-image-processing']
    
    if args.mobile_proxy:
        cmd.append('--mobile-proxy')
    
    if args.debug:
        cmd.append('--debug')
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True, env=env)
    except subprocess.CalledProcessError as e:
        print(f"Error running orchestrator script: {e}", file=sys.stderr)
        sys.exit(e.returncode)

if __name__ == "__main__":
    main()
