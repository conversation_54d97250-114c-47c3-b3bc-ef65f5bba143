from PIL import Image
import numpy as np


def crop_whitespace(image_path, output_path):
    # Open the image
    img = Image.open(image_path)

    # Convert to numpy array
    img_array = np.array(img)

    # Find non-white rows
    non_empty_columns = np.where(img_array.min(axis=0).min(axis=1) < 245)[0]
    non_empty_rows = np.where(img_array.min(axis=1).min(axis=1) < 245)[0]

    # Crop the image
    cropBox = (min(non_empty_columns), min(non_empty_rows), max(non_empty_columns), max(non_empty_rows))
    img_cropped = img.crop(cropBox)

    # Save the cropped image
    img_cropped.save(output_path)


def crop_excess_color(image_path, output_path, tolerance=10):
    # Open the image
    img = Image.open(image_path)

    # Convert to numpy array
    img_array = np.array(img)

    # Get the color of the top-left pixel (assumed to be the excess color)
    excess_color = img_array[0, 0]

    # Create a mask where True indicates pixels different from the excess color
    mask = np.any(np.abs(img_array - excess_color) > tolerance, axis=2)

    # Find the bounding box of the non-excess color region
    rows = np.any(mask, axis=1)
    cols = np.any(mask, axis=0)
    ymin, ymax = np.where(rows)[0][[0, -1]]
    xmin, xmax = np.where(cols)[0][[0, -1]]

    # Crop the image
    img_cropped = img.crop((xmin, ymin, xmax + 1, ymax + 1))

    # Save the cropped image
    img_cropped.save(output_path)

# Use the function
crop_whitespace('crop_image.jpg', 'output_image.jpg')
crop_excess_color('crop_image2.jpg', 'output_image2.jpg')