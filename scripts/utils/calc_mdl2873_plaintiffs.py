#!/usr/bin/env python3
"""
Calculate MDL plaintiffs by summing cases grouped by MDL number from NJD docket report.
"""

import argparse
import json
import sys
from pathlib import Path
from collections import defaultdict
from rich.console import Console
from rich.table import Table

console = Console()

def load_docket_data(date: str) -> dict:
    """Load docket report data for the specified date."""
    data_path = Path(f"data/{date}/logs/docket_report/njd.json")
    
    if not data_path.exists():
        console.print(f"[red]Error: File not found: {data_path}[/red]")
        sys.exit(1)
    
    try:
        with open(data_path, 'r') as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        console.print(f"[red]Error: Invalid JSON in {data_path}: {e}[/red]")
        sys.exit(1)

def calculate_mdl_plaintiffs_by_date(data: dict) -> dict:
    """Calculate total cases by MDL number grouped by filing date."""
    mdl_by_date = defaultdict(lambda: defaultdict(int))
    
    if 'cases' not in data:
        console.print("[red]Error: No 'cases' key found in data[/red]")
        return {}
    
    for case in data['cases']:
        mdl_num = case.get('mdl_num')
        filing_date = case.get('filing_date')
        
        if mdl_num and filing_date:
            mdl_by_date[mdl_num][filing_date] += 1
    
    # Convert defaultdicts to regular dicts for easier handling
    return {mdl: dict(dates) for mdl, dates in mdl_by_date.items()}

def display_results(mdl_by_date: dict, metadata: dict):
    """Display results in a formatted table grouped by MDL and date."""
    console.print(f"\n[bold]MDL Plaintiff Counts by Filing Date - {metadata.get('court_id', 'Unknown').upper()}[/bold]")
    
    total_all_mdl_cases = 0
    
    for mdl_num in sorted(mdl_by_date.keys()):
        dates_data = mdl_by_date[mdl_num]
        
        # Create table for each MDL
        table = Table(title=f"MDL{mdl_num}")
        table.add_column("Filing Date", style="cyan", no_wrap=True)
        table.add_column("Cases", style="magenta", justify="right")
        
        mdl_total = 0
        for filing_date in sorted(dates_data.keys()):
            count = dates_data[filing_date]
            table.add_row(filing_date, str(count))
            mdl_total += count
        
        # Add total row
        table.add_row("[bold]TOTAL[/bold]", f"[bold]{mdl_total}[/bold]")
        
        console.print(table)
        console.print()
        
        total_all_mdl_cases += mdl_total
    
    # Overall Summary
    console.print(f"[bold]Overall Summary:[/bold]")
    console.print(f"Total MDL cases across all MDLs: {total_all_mdl_cases}")
    console.print(f"Total cases in file: {metadata.get('total_cases', 'Unknown')}")
    console.print(f"Date range: {metadata.get('date_range', 'Unknown')}")
    console.print(f"Number of MDLs: {len(mdl_by_date)}")

def main():
    parser = argparse.ArgumentParser(description="Calculate MDL plaintiffs from NJD docket report")
    parser.add_argument("--date", required=True, help="Date in YYYYMMDD format")
    
    args = parser.parse_args()
    
    # Validate date format
    if len(args.date) != 8 or not args.date.isdigit():
        console.print("[red]Error: Date must be in YYYYMMDD format[/red]")
        sys.exit(1)
    
    console.print(f"[blue]Loading docket data for {args.date}...[/blue]")
    
    data = load_docket_data(args.date)
    mdl_by_date = calculate_mdl_plaintiffs_by_date(data)
    
    if not mdl_by_date:
        console.print("[yellow]No MDL cases found in the data[/yellow]")
        return
    
    display_results(mdl_by_date, data.get('metadata', {}))

if __name__ == "__main__":
    main()