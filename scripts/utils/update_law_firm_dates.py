#!/usr/bin/env python3
"""
Update Law Firm Dates Script

Updates ad_archive_last_updated field for all law firms to specified date.
Optionally deletes FB archive records with matching AddedOn date.

Usage:
    python scripts/utils/update_law_firm_dates.py --date 20241231
"""

import argparse
import asyncio
import sys
import os
from typing import List, Dict, Any

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from rich.console import Console
from rich.progress import Progress, TaskID
from rich.prompt import Confirm

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.law_firms_repository import LawFirmsRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
import logging


class SimpleConfig:
    """Simple configuration for the script"""
    def __init__(self):
        self.dynamodb_max_retries = 3
        self.dynamodb_base_delay = 1.0
        self.dynamodb_max_delay = 30.0

# Setup rich console
console = Console()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def update_law_firms(date: str, law_firms_repo: LawFirmsRepository) -> int:
    """
    Update ad_archive_last_updated field for all law firms
    
    Args:
        date: Date in YYYYMMDD format
        law_firms_repo: Law firms repository instance
        
    Returns:
        Number of law firms updated
    """
    console.print(f"[bold blue]Updating law firms with date: {date}[/bold blue]")
    
    # Get all law firms
    all_firms = await law_firms_repo.scan_all()
    
    if not all_firms:
        console.print("[yellow]No law firms found in database[/yellow]")
        return 0
    
    console.print(f"Found {len(all_firms)} law firms to update")
    
    updated_count = 0
    failed_count = 0
    
    with Progress() as progress:
        task = progress.add_task("[green]Updating law firms...", total=len(all_firms))
        
        for firm in all_firms:
            # Extract ID and Name from PascalCase format returned by scan_all
            firm_id = firm.get('ID')
            firm_name = firm.get('Name')
            
            if not firm_id or not firm_name:
                logger.warning(f"Skipping firm with missing ID or Name: {firm}")
                failed_count += 1
                progress.update(task, advance=1)
                continue
            
            try:
                # Update using snake_case - repository will convert to PascalCase
                success = await law_firms_repo.update_attributes(
                    firm_id,
                    firm_name,
                    {'ad_archive_last_updated': date}
                )
                
                if success:
                    updated_count += 1
                else:
                    failed_count += 1
                    logger.error(f"Failed to update firm {firm_id} - {firm_name}")
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"Error updating firm {firm_id} - {firm_name}: {e}")
            
            progress.update(task, advance=1)
    
    console.print(f"[bold green]Updated {updated_count} law firms successfully[/bold green]")
    if failed_count > 0:
        console.print(f"[bold red]{failed_count} law firms failed to update[/bold red]")
    
    return updated_count


async def delete_fb_ads_by_last_updated(date: str, fb_repo: FBArchiveRepository) -> int:
    """
    Delete FB archive records where LastUpdated equals the specified date
    
    Args:
        date: Date in YYYYMMDD format
        fb_repo: FB archive repository instance
        
    Returns:
        Number of records deleted
    """
    console.print(f"[bold yellow]Searching for FB ads with LastUpdated = {date}[/bold yellow]")
    
    # Query for records with matching LastUpdated date
    records = await fb_repo.query_by_last_updated(date)
    
    if not records:
        console.print("[green]No FB archive records found with that LastUpdated date[/green]")
        return 0
    
    console.print(f"[bold red]Found {len(records)} FB archive records to delete[/bold red]")
    
    # Show user what will be deleted and confirm
    if not Confirm.ask(f"Are you sure you want to delete {len(records)} FB archive records with LastUpdated = {date}?"):
        console.print("[yellow]Deletion cancelled by user[/yellow]")
        return 0
    
    # Prepare keys for batch deletion
    keys = []
    for record in records:
        key = {
            'AdArchiveID': record.get('ad_archive_id'),
            'StartDate': record.get('start_date')
        }
        if key['AdArchiveID'] and key['StartDate']:
            keys.append(key)
    
    if not keys:
        console.print("[red]No valid keys found for deletion[/red]")
        return 0
    
    console.print(f"[bold red]Deleting {len(keys)} FB archive records...[/bold red]")
    
    # Perform batch deletion
    deleted_count = await fb_repo.batch_delete_records(keys)
    
    console.print(f"[bold green]Deleted {deleted_count} FB archive records[/bold green]")
    
    return deleted_count


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Update law firm dates and optionally delete FB archive records')
    parser.add_argument('--date', required=True, help='Date in YYYYMMDD format')
    
    args = parser.parse_args()
    
    # Validate date format
    date = args.date
    if len(date) != 8 or not date.isdigit():
        console.print("[bold red]Error: Date must be in YYYYMMDD format[/bold red]")
        sys.exit(1)
    
    console.print(f"[bold cyan]Script ready for date: {date}[/bold cyan]")
    console.print("\n" + "="*60)
    console.print("[bold yellow]CHOOSE OPERATION[/bold yellow]")
    console.print("="*60)
    
    # Get user choices
    update_law_firms_choice = Confirm.ask(f"Update LawFirms ad_archive_last_update to {date}?")
    delete_fb_ads_choice = Confirm.ask(f"Delete FB Ads with last_updated == {date}?")
    
    if not update_law_firms_choice and not delete_fb_ads_choice:
        console.print("[yellow]No operations selected. Exiting.[/yellow]")
        return
    
    # Initialize storage and repositories only if needed
    storage = None
    try:
        config = SimpleConfig()
        storage = AsyncDynamoDBStorage(config, logger)
        await storage.__aenter__()
        
        law_firms_repo = LawFirmsRepository(storage)
        fb_repo = FBArchiveRepository(storage)
        
        updated_count = 0
        deleted_count = 0
        
        # Step 1: Update law firms (only if user chose this)
        if update_law_firms_choice:
            updated_count = await update_law_firms(date, law_firms_repo)
        
        # Step 2: Delete FB archive records (only if user chose this)
        if delete_fb_ads_choice:
            deleted_count = await delete_fb_ads_by_last_updated(date, fb_repo)
        
        # Summary
        console.print("\n" + "="*60)
        console.print("[bold green]SUMMARY[/bold green]")
        console.print("="*60)
        console.print(f"Law firms updated: {updated_count}")
        console.print(f"FB archive records deleted: {deleted_count}")
        console.print(f"Target date: {date}")
        
    except Exception as e:
        console.print(f"[bold red]Error: {e}[/bold red]")
        logger.error(f"Script failed: {e}", exc_info=True)
        sys.exit(1)
    
    finally:
        if storage:
            await storage.__aexit__(None, None, None)


if __name__ == "__main__":
    asyncio.run(main())