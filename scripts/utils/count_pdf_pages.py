#!/usr/bin/env python3
import os
import re
import json
import argparse
from PyPDF2 import PdfReader
from tqdm import tqdm
import logging
from collections import defaultdict
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define the base path to your data directory
base_path = os.getenv('LEXGENIUS_DATA_DIR', '/Users/<USER>/PycharmProjects/lexgenius/data')
console = Console()


# Function to count the number of pages in a PDF file
def count_pdf_pages(pdf_file):
    """Count pages in a PDF file with better error handling."""
    try:
        with open(pdf_file, 'rb') as f:
            reader = PdfReader(f)
            page_count = len(reader.pages)
            return page_count
    except Exception as e:
        logger.error(f"Error processing {pdf_file}: {str(e)}")
        return 0


def has_corresponding_md_file(pdf_path, dockets_dir):
    """Check if a PDF has a corresponding MD file with the same base filename."""
    base_name = os.path.splitext(os.path.basename(pdf_path))[0]
    md_file = os.path.join(dockets_dir, f"{base_name}.md")
    return os.path.exists(md_file)


def analyze_json_html_fields(json_file):
    """Analyze a JSON file for s3_html and other HTML/URL fields."""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            has_s3_html = 's3_html' in data and data['s3_html'] is not None and data['s3_html'].strip() != ""
            
            # Check for other HTML/URL fields if s3_html is not present
            has_other_html_url = False
            if not has_s3_html:
                # Look for any key containing 'html' or 'url'
                for key, value in data.items():
                    if ('html' in key.lower() or 'url' in key.lower()) and value is not None and str(value).strip() != "":
                        has_other_html_url = True
                        break
            
            return has_s3_html, has_other_html_url
    except Exception as e:
        logger.error(f"Error processing JSON {json_file}: {str(e)}")
        return False, False




def process_directories(data_path, start_date=None, single_date=None):
    """Process YYYYMMDD directories and count PDF pages."""
    results = []
    files_without_s3_html = {}  # Store files without s3_html by date
    
    # Collect all valid YYYYMMDD directories
    valid_dirs = []
    
    # If single_date is specified, only process that one directory
    if single_date:
        item_path = os.path.join(data_path, single_date)
        if os.path.isdir(item_path):
            dockets_dir = os.path.join(item_path, 'dockets')
            if os.path.exists(dockets_dir):
                valid_dirs.append((single_date, dockets_dir))
            else:
                logger.warning(f"Dockets subdirectory not found in {item_path}")
        else:
            logger.error(f"Directory {item_path} does not exist")
    else:
        # Walk through YYYYMMDD structure
        for item in os.listdir(data_path):
            item_path = os.path.join(data_path, item)
            if not os.path.isdir(item_path):
                continue
                
            # Check if it matches YYYYMMDD pattern
            if re.match(r'^\d{8}$', item):
                # Filter by start_date if provided
                if start_date and item < start_date:
                    continue
                    
                # Look for dockets subdirectory
                dockets_dir = os.path.join(item_path, 'dockets')
                if os.path.exists(dockets_dir):
                    valid_dirs.append((item, dockets_dir))
    
    logger.info(f"Found {len(valid_dirs)} valid directories to process")
    
    # Process each directory with a progress bar
    for date_str, dockets_dir in tqdm(valid_dirs, desc="Processing directories"):
        pdf_files = [f for f in os.listdir(dockets_dir) if f.lower().endswith('.pdf')]
        json_files = [f for f in os.listdir(dockets_dir) if f.lower().endswith('.json')]
        total_pages = 0
        total_pages_without_md = 0
        num_files = 0
        num_files_without_md = 0
        json_with_s3_html = 0
        total_json_files = len(json_files)
        
        # Process JSONs for s3_html and other HTML/URL fields
        json_with_other_html_url = 0
        date_files_without_s3_html = []
        
        for json_file in json_files:
            json_path = os.path.join(dockets_dir, json_file)
            has_s3_html, has_other_html_url = analyze_json_html_fields(json_path)
            if has_s3_html:
                json_with_s3_html += 1
            elif has_other_html_url:
                json_with_other_html_url += 1
            else:
                # This file has neither s3_html nor other HTML/URL fields
                date_files_without_s3_html.append(json_file)
        
        # Store files without s3_html for this date
        if date_files_without_s3_html:
            files_without_s3_html[date_str] = date_files_without_s3_html
        
        # Process PDFs in the current directory with a nested progress bar
        for pdf_file in tqdm(pdf_files, desc=f"Processing PDFs in {date_str}", leave=False):
            pdf_path = os.path.join(dockets_dir, pdf_file)
            pages = count_pdf_pages(pdf_path)
            if pages > 0:
                num_files += 1
                total_pages += pages
                
                # Check if this PDF should be excluded (has corresponding MD file)
                if not has_corresponding_md_file(pdf_path, dockets_dir):
                    num_files_without_md += 1
                    total_pages_without_md += pages
        
        results.append({
            'date': date_str,
            'num_files': num_files,
            'total_pages': total_pages,
            'num_files_without_md': num_files_without_md,
            'total_pages_without_md': total_pages_without_md,
            'total_json_files': total_json_files,
            'json_with_s3_html': json_with_s3_html,
            'json_with_other_html_url': json_with_other_html_url
        })
        json_percentage = (json_with_s3_html / total_json_files * 100) if total_json_files > 0 else 0
        other_percentage = (json_with_other_html_url / total_json_files * 100) if total_json_files > 0 else 0
        logger.info(f"Directory {date_str}: {num_files} files ({total_pages} pages), {num_files_without_md} w/o MD ({total_pages_without_md} pages), {json_with_s3_html}/{total_json_files} JSON w/ s3_html ({json_percentage:.1f}%), {json_with_other_html_url} w/ other HTML/URL ({other_percentage:.1f}%)")
    
    return sorted(results, key=lambda x: x['date']), files_without_s3_html  # Sort by date


def generate_summaries(results):
    """Generate date, month, and total summaries."""
    # Group by month
    monthly_stats = defaultdict(lambda: {
        'total_pages': 0, 'total_pages_without_md': 0,
        'num_files': 0, 'num_files_without_md': 0, 'num_dates': 0,
        'total_json_files': 0, 'json_with_s3_html': 0, 'json_with_other_html_url': 0
    })
    
    # Calculate totals and monthly stats
    total_stats = {
        'total_pages': 0, 'total_pages_without_md': 0,
        'num_files': 0, 'num_files_without_md': 0,
        'total_json_files': 0, 'json_with_s3_html': 0, 'json_with_other_html_url': 0
    }
    
    for result in results:
        date_str = result['date']
        month_key = date_str[:6]  # YYYYMM
        
        # Add to monthly stats
        monthly_stats[month_key]['total_pages'] += result['total_pages']
        monthly_stats[month_key]['total_pages_without_md'] += result['total_pages_without_md']
        monthly_stats[month_key]['num_files'] += result['num_files']
        monthly_stats[month_key]['num_files_without_md'] += result['num_files_without_md']
        monthly_stats[month_key]['total_json_files'] += result['total_json_files']
        monthly_stats[month_key]['json_with_s3_html'] += result['json_with_s3_html']
        monthly_stats[month_key]['json_with_other_html_url'] += result['json_with_other_html_url']
        monthly_stats[month_key]['num_dates'] += 1
        
        # Add to total stats
        total_stats['total_pages'] += result['total_pages']
        total_stats['total_pages_without_md'] += result['total_pages_without_md']
        total_stats['num_files'] += result['num_files']
        total_stats['num_files_without_md'] += result['num_files_without_md']
        total_stats['total_json_files'] += result['total_json_files']
        total_stats['json_with_s3_html'] += result['json_with_s3_html']
        total_stats['json_with_other_html_url'] += result['json_with_other_html_url']
    
    return monthly_stats, total_stats




def display_results(results):
    """Display results using rich formatting."""
    monthly_stats, total_stats = generate_summaries(results)
    
    # Daily Results Table
    daily_table = Table(title="Daily PDF & JSON Analysis")
    daily_table.add_column("Date", style="cyan")
    daily_table.add_column("PDF Files", justify="right", style="green")
    daily_table.add_column("Total Pages", justify="right", style="yellow")
    daily_table.add_column("Files w/o MD", justify="right", style="green")
    daily_table.add_column("Pages w/o MD", justify="right", style="yellow")
    daily_table.add_column("JSON Files", justify="right", style="blue")
    daily_table.add_column("w/ s3_html", justify="right", style="magenta")
    daily_table.add_column("s3_html %", justify="right", style="red")
    daily_table.add_column("w/ other URL", justify="right", style="orange1")
    daily_table.add_column("other %", justify="right", style="orange3")
    
    for result in results:
        json_percentage = (result['json_with_s3_html'] / result['total_json_files'] * 100) if result['total_json_files'] > 0 else 0
        other_percentage = (result['json_with_other_html_url'] / result['total_json_files'] * 100) if result['total_json_files'] > 0 else 0
        daily_table.add_row(
            result['date'],
            str(result['num_files']),
            str(result['total_pages']),
            str(result['num_files_without_md']),
            str(result['total_pages_without_md']),
            str(result['total_json_files']),
            str(result['json_with_s3_html']),
            f"{json_percentage:.1f}%",
            str(result['json_with_other_html_url']),
            f"{other_percentage:.1f}%"
        )
    
    console.print(daily_table)
    console.print()
    
    # Monthly Summary Table
    monthly_table = Table(title="Monthly Summary")
    monthly_table.add_column("Month", style="cyan")
    monthly_table.add_column("Days", justify="right", style="blue")
    monthly_table.add_column("PDF Files", justify="right", style="green")
    monthly_table.add_column("Total Pages", justify="right", style="yellow")
    monthly_table.add_column("Files w/o MD", justify="right", style="green")
    monthly_table.add_column("Pages w/o MD", justify="right", style="yellow")
    monthly_table.add_column("JSON Files", justify="right", style="blue")
    monthly_table.add_column("w/ s3_html", justify="right", style="magenta")
    monthly_table.add_column("s3_html %", justify="right", style="red")
    monthly_table.add_column("w/ other URL", justify="right", style="orange1")
    monthly_table.add_column("other %", justify="right", style="orange3")
    
    for month_key in sorted(monthly_stats.keys()):
        stats = monthly_stats[month_key]
        # Format month as YYYY-MM
        formatted_month = f"{month_key[:4]}-{month_key[4:]}"
        json_percentage = (stats['json_with_s3_html'] / stats['total_json_files'] * 100) if stats['total_json_files'] > 0 else 0
        other_percentage = (stats['json_with_other_html_url'] / stats['total_json_files'] * 100) if stats['total_json_files'] > 0 else 0
        monthly_table.add_row(
            formatted_month,
            str(stats['num_dates']),
            str(stats['num_files']),
            str(stats['total_pages']),
            str(stats['num_files_without_md']),
            str(stats['total_pages_without_md']),
            str(stats['total_json_files']),
            str(stats['json_with_s3_html']),
            f"{json_percentage:.1f}%",
            str(stats['json_with_other_html_url']),
            f"{other_percentage:.1f}%"
        )
    
    console.print(monthly_table)
    console.print()
    
    # Total Summary Panel
    summary_text = Text()
    summary_text.append(f"Total PDF Files: {total_stats['num_files']:,}\n", style="green bold")
    summary_text.append(f"Total Pages: {total_stats['total_pages']:,}\n", style="yellow bold")
    summary_text.append(f"Files w/o MD: {total_stats['num_files_without_md']:,}\n", style="green bold")
    summary_text.append(f"Pages w/o MD: {total_stats['total_pages_without_md']:,}\n", style="yellow bold")
    summary_text.append(f"Total JSON Files: {total_stats['total_json_files']:,}\n", style="blue bold")
    summary_text.append(f"JSON w/ s3_html: {total_stats['json_with_s3_html']:,}\n", style="magenta bold")
    summary_text.append(f"JSON w/ other URL: {total_stats['json_with_other_html_url']:,}\n", style="orange1 bold")
    
    overall_json_percentage = (total_stats['json_with_s3_html'] / total_stats['total_json_files'] * 100) if total_stats['total_json_files'] > 0 else 0
    overall_other_percentage = (total_stats['json_with_other_html_url'] / total_stats['total_json_files'] * 100) if total_stats['total_json_files'] > 0 else 0
    summary_text.append(f"s3_html Coverage: {overall_json_percentage:.1f}%\n", style="red bold")
    summary_text.append(f"Other URL Coverage: {overall_other_percentage:.1f}%\n", style="orange3 bold")
    summary_text.append(f"Total Directories: {len(results)}", style="blue bold")
    
    summary_panel = Panel(summary_text, title="Overall Summary", style="bright_blue")
    console.print(summary_panel)


def display_files_without_s3_html(files_without_s3_html):
    """Display files that don't contain s3_html organized by YYYYMMDD date."""
    if not files_without_s3_html:
        console.print("\n[green]✓ All JSON files contain s3_html or other HTML/URL fields![/green]")
        return
    
    console.print(f"\n[bold red]Files without s3_html by Date ({len(files_without_s3_html)} dates):[/bold red]")
    
    # Calculate total files without s3_html
    total_files_without_s3_html = sum(len(files) for files in files_without_s3_html.values())
    console.print(f"[yellow]Total files without s3_html: {total_files_without_s3_html}[/yellow]\n")
    
    # Create table for files without s3_html
    files_table = Table(title="Files without s3_html by Date")
    files_table.add_column("Date", style="cyan")
    files_table.add_column("Count", justify="right", style="red")
    files_table.add_column("Files", style="white")
    
    # Sort dates and display
    for date in sorted(files_without_s3_html.keys()):
        files = files_without_s3_html[date]
        # Limit displayed filenames to avoid overwhelming output
        display_files = files[:5]  # Show first 5 files
        files_text = ", ".join(display_files)
        if len(files) > 5:
            files_text += f" ... (+{len(files) - 5} more)"
        
        files_table.add_row(
            date,
            str(len(files)),
            files_text
        )
    
    console.print(files_table)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Count PDF pages and analyze JSON files in YYYYMMDD directories')
    parser.add_argument('--date', 
                       help='Start date in YYYYMMDD format. Only process directories >= this date (e.g., 20250101)',
                       type=str)
    parser.add_argument('--single', 
                       help='Process only a single directory with this exact YYYYMMDD date (e.g., 20250509)',
                       type=str)
    return parser.parse_args()


def validate_date_format(date_str):
    """Validate that date string is in YYYYMMDD format."""
    if not date_str:
        return True
    
    if not re.match(r'^\d{8}$', date_str):
        return False
    
    # Basic validation of date components
    year = int(date_str[:4])
    month = int(date_str[4:6])
    day = int(date_str[6:8])
    
    if year < 1900 or year > 2100:
        return False
    if month < 1 or month > 12:
        return False
    if day < 1 or day > 31:
        return False
    
    return True


def main():
    args = parse_arguments()
    
    # Validate that only one of --date or --single is provided
    if args.date and args.single:
        console.print("[red]Error: Cannot specify both --date and --single options. Please use only one.[/red]")
        return
    
    # Validate date format if provided
    if args.date and not validate_date_format(args.date):
        console.print(f"[red]Error: Invalid date format '{args.date}'. Please use YYYYMMDD format (e.g., 20250101)[/red]")
        return
    
    # Validate single date format if provided
    if args.single and not validate_date_format(args.single):
        console.print(f"[red]Error: Invalid date format '{args.single}'. Please use YYYYMMDD format (e.g., 20250509)[/red]")
        return
    
    start_date = args.date
    single_date = args.single
    
    if single_date:
        logger.info(f"Starting PDF page count process in {base_path} for single date: {single_date}")
    elif start_date:
        logger.info(f"Starting PDF page count process in {base_path} for dates >= {start_date}")
    else:
        logger.info(f"Starting PDF page count process in {base_path}")
    
    if not os.path.exists(base_path):
        logger.error(f"Base path does not exist: {base_path}")
        return
    
    results, files_without_s3_html = process_directories(base_path, start_date, single_date)
    
    if not results:
        if single_date:
            console.print(f"[red]No valid directory found for date {single_date} or dockets subdirectory missing.[/red]")
        elif start_date:
            console.print(f"[red]No valid directories found with dockets subdirectories for dates >= {start_date}.[/red]")
        else:
            console.print("[red]No valid directories found with dockets subdirectories.[/red]")
        return
    
    display_results(results)
    
    # Display files without s3_html
    display_files_without_s3_html(files_without_s3_html)
    
    if single_date:
        logger.info(f"Process complete. Processed single directory for date {single_date}.")
    elif start_date:
        logger.info(f"Process complete. Processed {len(results)} directories for dates >= {start_date}.")
    else:
        logger.info(f"Process complete. Processed {len(results)} directories.")

if __name__ == "__main__":
    main()
