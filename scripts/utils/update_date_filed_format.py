#!/usr/bin/env python3
"""
Update filing_date field in docket JSON files from "Case filed: MM/DD/YYYY" to "YYYYMMDD".

Usage:
    python update_date_filed_format.py --date YYYYMMDD
    python update_date_filed_format.py --date YYYYMMDD --dry-run
"""

import argparse
import json
import logging
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.progress import track
from rich.logging import RichHandler

console = Console()

logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    handlers=[RichHandler(console=console, rich_tracebacks=True)]
)
logger = logging.getLogger(__name__)


def parse_date_filed(date_string: str) -> str:
    """Convert MM/DD/YYYY to YYYYMMDD format."""
    try:
        date_obj = datetime.strptime(date_string, "%m/%d/%Y")
        return date_obj.strftime("%Y%m%d")
    except ValueError:
        logger.warning(f"Could not parse date: {date_string}")
        return date_string


def extract_date_from_filing_date(filing_date: str) -> str:
    """Extract MM/DD/YYYY date from filing_date string like 'Case filed: 06/17/2025'."""
    import re
    # Look for MM/DD/YYYY pattern
    match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', filing_date)
    if match:
        return match.group(1)
    return None


def update_json_file(file_path: Path, dry_run: bool = False) -> tuple[bool, str, str]:
    """Update filing_date to contain just YYYYMMDD instead of full string.
    
    Returns:
        (updated, before_value, after_value)
    """
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        if 'filing_date' in data and isinstance(data['filing_date'], str):
            original_filing_date = data['filing_date']
            
            # Check if filing_date contains the "Case filed:" pattern
            if 'Case' in original_filing_date or '/' in original_filing_date:
                # Extract date from filing_date string like "Case filed: 06/17/2025"
                extracted_date = extract_date_from_filing_date(original_filing_date)
                if extracted_date:
                    new_date = parse_date_filed(extracted_date)
                    
                    if not dry_run:
                        data['filing_date'] = new_date
                        with open(file_path, 'w') as f:
                            json.dump(data, f, indent=2)
                    
                    logger.debug(f"{'Would update' if dry_run else 'Updated'} {file_path.name}: {original_filing_date} -> {new_date}")
                    return True, original_filing_date, new_date
                else:
                    logger.debug(f"Skipping {file_path.name}: no date found in filing_date")
                    return False, '', ''
            else:
                logger.debug(f"Skipping {file_path.name}: filing_date already in correct format")
                return False, original_filing_date, original_filing_date
        else:
            logger.debug(f"Skipping {file_path.name}: no filing_date field found")
            return False, '', ''
        
    except Exception as e:
        logger.error(f"Error processing {file_path}: {e}")
        return False, '', ''


def main():
    parser = argparse.ArgumentParser(description="Update date_filed format in docket JSON files")
    parser.add_argument('--date', required=True, help='Date in YYYYMMDD format')
    parser.add_argument('--dry-run', action='store_true', help='Show changes without updating files')
    args = parser.parse_args()
    
    dockets_path = Path(f"data/{args.date}/dockets")
    
    if not dockets_path.exists():
        console.print(f"[red]Error: Directory {dockets_path} does not exist[/red]")
        return
    
    json_files = list(dockets_path.glob("*.json"))
    
    if not json_files:
        console.print(f"[yellow]No JSON files found in {dockets_path}[/yellow]")
        return
    
    console.print(f"[green]Found {len(json_files)} JSON files to process[/green]")
    if args.dry_run:
        console.print("[yellow]DRY RUN MODE - No changes will be made[/yellow]\n")
    
    updated_count = 0
    changes = []
    
    for file_path in track(json_files, description="Processing files..."):
        updated, before, after = update_json_file(file_path, dry_run=args.dry_run)
        if updated:
            updated_count += 1
            changes.append((file_path.name, before, after))
    
    if args.dry_run and changes:
        console.print("\n[bold cyan]Changes to be made:[/bold cyan]")
        for filename, before, after in changes:
            console.print(f"[blue]{filename}[/blue]: [red]{before}[/red] → [green]{after}[/green]")
    
    if args.dry_run:
        console.print(f"\n[yellow]Would update {updated_count} files[/yellow]")
        console.print(f"[blue]Would skip {len(json_files) - updated_count} files[/blue]")
    else:
        console.print(f"\n[green]✓ Updated {updated_count} files[/green]")
        console.print(f"[blue]ℹ Skipped {len(json_files) - updated_count} files[/blue]")


if __name__ == "__main__":
    main()