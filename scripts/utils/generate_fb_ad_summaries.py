#!/usr/bin/env python3
"""
Generate summaries for Facebook ads that don't have them yet.

This script:
1. Iterates through HTML files in fb_ad_archive/html/ to get page IDs
2. For each page ID, queries DynamoDB for records with the specified last_updated date
3. Generates summaries for records that don't have them using AI services
4. Updates the records in DynamoDB with the new summaries

Usage:
    python scripts/utils/generate_fb_ad_summaries.py --date 20241201
"""

import argparse
import asyncio
import logging
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import sys
import yaml
import json
import re

from rich.console import Console
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, MofNCompleteColumn
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.repositories.fb_archive_repository import FBArchiveRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.services.ai.ai_orchestrator import AIOrchestrator
from src.services.ai.deepseek_service import DeepSeekService
from src.services.ai.prompt_manager import PromptManager
from src.infrastructure.external.deepseek_client import DeepSeekClient


class FBAdSummaryGenerator:
    """Generator for Facebook ad summaries"""
    
    def __init__(self, date: str = None, start_date: str = None, end_date: str = None, query_mode: str = 'page_date', reprocess: bool = False, batch_size: int = 50, no_preview: bool = False):
        self.date = date
        self.start_date = start_date
        self.end_date = end_date
        self.query_mode = query_mode  # 'page_date', 'last_updated', or 'date_range'
        self.reprocess = reprocess
        self.batch_size = batch_size  # Configurable batch size for parallel processing
        self.no_preview = no_preview  # Skip preview confirmation
        self.console = Console()
        self.logger = self._setup_logging()
        
        # Load config
        self.config = self._load_config()
        
        # Initialize storage and repository
        self.storage = AsyncDynamoDBStorage(self.config, self.logger)
        self.repository = FBArchiveRepository(self.storage)
        
        # Initialize AI services
        self.deepseek_service = None
        
        # Load litigation prompt
        self.litigation_prompt = self._load_litigation_prompt()
        
        # Stats
        self.stats = {
            'processed': 0,
            'summaries_generated': 0,
            'already_had_summary': 0,
            'errors': 0,
            'no_records_found': 0,
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        config_path = project_root / "config" / "report.yml"
        
        if not config_path.exists():
            # Fallback to fb_ads.yml if report.yml doesn't exist
            config_path = project_root / "config" / "fb_ads.yml"
        
        if not config_path.exists():
            self.console.print(f"❌ Config file not found at {config_path}", style="red")
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            self.console.print(f"✅ Loaded config from {config_path}", style="green")
            return config
        except Exception as e:
            self.console.print(f"❌ Error loading config: {e}", style="red")
            raise
    
    def _load_litigation_prompt(self) -> str:
        """Load litigation-specific prompt from configuration"""
        prompt_path = project_root / "src" / "config" / "prompts" / "fb_ads" / "ad_summary" / "system.md"
        
        if not prompt_path.exists():
            self.console.print(f"⚠️ Litigation prompt file not found at {prompt_path}", style="yellow")
            return None
        
        try:
            with open(prompt_path, 'r') as f:
                prompt_content = f.read()
            self.console.print(f"✅ Loaded litigation-specific prompt", style="green")
            return prompt_content
        except Exception as e:
            self.console.print(f"❌ Error loading litigation prompt: {e}", style="red")
            return None
    
    async def initialize_ai_services(self):
        """Initialize AI services for summary generation"""
        try:
            # Load environment variables from .env file
            from dotenv import load_dotenv
            load_dotenv(project_root / '.env')
            
            # Initialize DeepSeek client with config to ensure OpenRouter credentials are used
            ai_config = {
                'openrouter_api_key': os.getenv('OPENROUTER_API_KEY'),
                'openrouter_site_url': os.getenv('OPENROUTER_SITE_URL'),
                'openrouter_site_name': os.getenv('OPENROUTER_SITE_NAME'),
                'deepseek_api_key': os.getenv('DEEPSEEK_API_KEY'),  # Optional
            }
            
            self.console.print(f"[dim]OpenRouter API Key: {'✓' if ai_config['openrouter_api_key'] else '✗'}[/dim]")
            
            deepseek_client = DeepSeekClient(config=ai_config)
            
            # Initialize prompt manager - using config/prompts if it exists
            prompts_dir = project_root / "config" / "prompts"
            if not prompts_dir.exists():
                prompts_dir = project_root / "src" / "prompts"
            if not prompts_dir.exists():
                # Create a minimal prompts directory structure
                prompts_dir = project_root / "prompts"
                prompts_dir.mkdir(exist_ok=True)
            
            prompt_manager = PromptManager(self.logger, {'prompts_dir': str(prompts_dir)})
            
            # Initialize DeepSeek service with all required dependencies
            ai_service_config = {
                'temperature': 0.0,
                'max_tokens': 100,
                'chunk_size': 100000
            }
            self.deepseek_service = DeepSeekService(
                logger=self.logger,
                client=deepseek_client,
                prompt_manager=prompt_manager,
                config=ai_service_config
            )
            
            # Check if we have usable credentials
            if ai_config['openrouter_api_key']:
                self.console.print("✅ AI services initialized with OpenRouter credentials", style="green")
            elif ai_config['deepseek_api_key']:
                self.console.print("✅ AI services initialized with DeepSeek credentials", style="green") 
            else:
                self.console.print("⚠️ AI services initialized but no API keys found - will use fallback", style="yellow")
            
        except Exception as e:
            self.console.print(f"❌ Failed to initialize AI services: {e}", style="red")
            raise
    
    def get_page_ids_from_html_files(self) -> List[str]:
        """Extract page IDs from HTML filenames in fb_ad_archive/html/"""
        html_dir = project_root / "fb_ad_archive" / "html"
        
        if not html_dir.exists():
            self.console.print(f"❌ HTML directory not found: {html_dir}", style="red")
            return []
        
        page_ids = []
        for html_file in html_dir.glob("*.html"):
            # Extract page ID from filename (e.g., "102907127873927.html" -> "102907127873927")
            page_id = html_file.stem
            if page_id.isdigit():
                page_ids.append(page_id)
            else:
                self.logger.warning(f"Skipping non-numeric filename: {html_file.name}")
        
        self.console.print(f"📁 Found {len(page_ids)} page IDs from HTML files", style="blue")
        return sorted(page_ids)
    
    async def process_page_id(self, page_id: str, progress: Progress, task_id: TaskID) -> Dict[str, int]:
        """Process a single page ID"""
        page_stats = {'processed': 0, 'generated': 0, 'had_summary': 0, 'errors': 0}
        
        try:
            # Query records for this page_id and date
            records = await self.repository.query_by_page_id_and_date(page_id, self.date)
            
            if not records:
                self.stats['no_records_found'] += 1
                progress.update(task_id, description=f"Page {page_id}: No records found")
                return page_stats
            
            progress.update(task_id, description=f"Page {page_id}: Processing {len(records)} records")
            
            for record in records:
                ad_archive_id = record.get('ad_archive_id', 'Unknown')
                
                try:
                    # Check if summary already exists (repository returns snake_case)
                    if record.get('summary'):
                        page_stats['had_summary'] += 1
                        continue
                    
                    # Generate summary
                    summary = await self.generate_summary(record)
                    
                    if summary:
                        # Update record in DynamoDB
                        await self.update_record_with_summary(
                            ad_archive_id, 
                            record.get('start_date', ''), 
                            summary
                        )
                        
                        page_stats['generated'] += 1
                        
                        # Display the result
                        self.console.print(Panel(
                            f"[bold blue]Ad Archive ID:[/bold blue] {ad_archive_id}\n"
                            f"[bold green]Summary:[/bold green] {summary[:200]}{'...' if len(summary) > 200 else ''}",
                            title=f"Page {page_id}",
                            expand=False
                        ))
                    
                    page_stats['processed'] += 1
                    
                except Exception as e:
                    self.logger.error(f"Error processing record {ad_archive_id}: {e}")
                    page_stats['errors'] += 1
            
            progress.update(task_id, description=f"Page {page_id}: Complete")
            
        except Exception as e:
            self.logger.error(f"Error processing page {page_id}: {e}")
            page_stats['errors'] += 1
        
        return page_stats
    
    async def generate_summary(self, record: Dict[str, Any]) -> Optional[str]:
        """Generate summary for a record using AI services"""
        # Extract text content from record
        text_content = self.extract_text_content(record)
        
        if not text_content:
            self.logger.warning(f"No text content found for record {record.get('ad_archive_id')}")
            return None
        
        
        # Check if AI service is available
        if self.deepseek_service:
            try:
                # Use the litigation-specific prompt if loaded
                if self.litigation_prompt:
                    # The system.md file contains the full prompt, use it directly
                    base_prompt = self.litigation_prompt.strip()
                    
                else:
                    # Fallback prompt that matches the expected format
                    base_prompt = """Objective: Create a concise summary of potential litigation or investigation topics from Facebook ad content, focusing specifically on the entity involved and the nature of the legal matter, without mentioning the law firm name. Summaries MUST be between 4-7 words. Do not exceed 7 words under any circumstances.

CRITICAL RULES:
1. Return "NA" for ANY generic legal ad that doesn't mention a SPECIFIC company, product, or ongoing litigation
2. Return "NA" for general personal injury, car accident, slip and fall, or "get compensation" ads
3. ONLY create a summary if there is a SPECIFIC company/product being investigated or sued
4. Include the name of the company or entity under scrutiny
5. Clearly specify the nature of the issue being investigated or litigated
6. NEVER mention the law firm's name in the summary
7. All summaries MUST be exactly 4-7 words. Count the words carefully."""
                
                # Extract relevant fields from record
                title = record.get('title', '') or record.get('ad_creative_link_title', '') or ''
                body = record.get('body', '') or record.get('ad_creative_body', '') or ''
                link_description = record.get('link_description', '') or record.get('ad_creative_link_description', '') or ''
                law_firm = record.get('law_firm', '') or 'Unknown Law Firm'
                image_text = record.get('image_text', '')
                
                # Handle 'None' string values
                if body == 'None' or body == 'null':
                    body = ''
                if link_description == 'None' or link_description == 'null':
                    link_description = ''
                
                # Include image text in the body if body is empty
                if not body.strip() and image_text:
                    body = image_text
                elif body.strip() and image_text:
                    body = f"{body}\n\n{image_text}"
                
                # Use "NA" for empty fields as per prompt specification
                if not body.strip():
                    body = "NA"
                if not link_description.strip():
                    link_description = "NA"
                
                # Format the prompt with the ad content
                formatted_prompt = f"""{base_prompt}

Title: {title}
Body: {body}
Link Description: {link_description}
Law Firm: {law_firm}
ImageText: {image_text}

Return JSON: {{"summary": "your 4-7 word summary"}}
If no specific litigation found, return: {{"summary": "NA"}}"""
                
                # Debug log the prompt
                self.logger.info(f"Sending prompt for ad {record.get('ad_archive_id')}:")
                self.logger.info(f"Title: {title}")
                self.logger.info(f"Body: {body[:100]}..." if len(body) > 100 else f"Body: {body}")
                self.logger.info(f"Link Description: {link_description}")
                
                # Use the DeepSeek client's generate_text method
                summary_response = await self.deepseek_service.client.generate_text(
                    prompt=formatted_prompt,
                    temperature=0.0,
                    max_tokens=100
                )
                
                if summary_response:
                    # Log the raw response for debugging
                    self.logger.info(f"Raw AI response: {summary_response}")
                    
                    # Try to parse JSON response
                    import json
                    import re
                    
                    # First try to extract JSON from markdown code blocks
                    json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', summary_response, re.DOTALL)
                    if json_match:
                        summary_response = json_match.group(1)
                    
                    try:
                        json_response = json.loads(summary_response)
                        if 'summary' in json_response:
                            summary = json_response['summary']
                            if summary:
                                return summary.strip()  # Return "NA" or actual summary
                    except json.JSONDecodeError:
                        # If not JSON, try to extract summary from text
                        if '"summary"' in summary_response:
                            import re
                            match = re.search(r'"summary":\s*"([^"]+)"', summary_response)
                            if match:
                                summary = match.group(1)
                                if summary:
                                    return summary.strip()  # Return "NA" or actual summary
                        
                        # If we get here, the response wasn't proper JSON
                        self.logger.warning(f"AI response was not valid JSON: {summary_response[:100]}...")
                        
                        # Try to use the response directly if it looks like a summary
                        if len(summary_response) < 100 and not summary_response.startswith('{'):
                            return summary_response.strip()
                
            except Exception as e:
                self.logger.error(f"Error generating summary: {e}")
        
        # Fallback: return NA if AI is not available
        self.logger.warning("AI summary generation failed, returning NA")
        return "NA"
    
    def _generate_simple_summary(self, text_content: str) -> str:
        """Generate a simple extractive summary without AI"""
        # Clean and prepare text
        text = text_content.replace('\n', ' ').replace('\r', ' ')
        text = ' '.join(text.split())  # Normalize whitespace
        
        # Extract key information
        sentences = [s.strip() for s in text.split('.') if s.strip()]
        
        # Prioritize sentences with key words
        key_words = ['law', 'legal', 'attorney', 'lawyer', 'lawsuit', 'injury', 'compensation', 'claim', 'settlement', 'case']
        
        scored_sentences = []
        for sentence in sentences[:5]:  # Only check first 5 sentences
            score = sum(1 for word in key_words if word.lower() in sentence.lower())
            if len(sentence) > 20 and len(sentence) < 200:  # Reasonable length
                scored_sentences.append((score, sentence))
        
        # Sort by score and take best sentences
        scored_sentences.sort(key=lambda x: x[0], reverse=True)
        
        if scored_sentences:
            # Take top sentence(s)
            summary_parts = [scored_sentences[0][1]]
            if len(scored_sentences) > 1 and len(summary_parts[0]) < 100:
                summary_parts.append(scored_sentences[1][1])
            
            summary = '. '.join(summary_parts)
            if not summary.endswith('.'):
                summary += '.'
                
            # Limit length
            if len(summary) > 200:
                summary = summary[:197] + '...'
                
            return summary
        
        # Final fallback
        if len(text) > 100:
            return text[:97] + '...'
        return text
    
    def extract_text_content(self, record: Dict[str, Any]) -> str:
        """Extract text content from record for summarization"""
        content_parts = []
        
        # Get various text fields
        text_fields = [
            'body', 'ad_creative_body', 'ad_creative_body_html',
            'title', 'caption', 'link_description'
        ]
        
        for field in text_fields:
            value = record.get(field)
            if value and isinstance(value, str) and value.strip():
                content_parts.append(value.strip())
        
        # Include image text if available
        image_text = record.get('image_text')
        if image_text and isinstance(image_text, str) and image_text.strip():
            content_parts.append(f"Image Text: {image_text.strip()}")
        
        return "\n\n".join(content_parts)
    
    async def update_record_with_summary(self, ad_archive_id: str, start_date: str, summary: str) -> bool:
        """Update record in DynamoDB with summary"""
        try:
            self.logger.info(f"Updating summary for {ad_archive_id} with start_date={start_date}")
            self.logger.debug(f"Summary value: {summary}")
            
            success = await self.repository.update_attributes(
                ad_archive_id=ad_archive_id,
                start_date=start_date,
                updates={'Summary': summary}  # Must use PascalCase - update_attributes has no decorator!
            )
            
            if success:
                self.logger.info(f"Successfully updated summary for {ad_archive_id}")
            else:
                self.logger.error(f"Failed to update summary for {ad_archive_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error updating record {ad_archive_id}: {e}", exc_info=True)
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        # Close DeepSeek client session if it exists
        if self.deepseek_service and hasattr(self.deepseek_service.client, 'close_session'):
            await self.deepseek_service.client.close_session()
            self.logger.debug("Closed DeepSeek client session")
    
    async def run(self):
        """Main execution method"""
        self._start_time = time.time()  # Track start time for performance metrics
        
        if self.query_mode == 'last_updated':
            mode_desc = "Last Updated"
            date_info = f"Date: {self.date}"
        elif self.query_mode == 'date_range':
            mode_desc = "Date Range"
            date_info = f"Start: {self.start_date}, End: {self.end_date}"
        else:
            mode_desc = "Page Date"
            date_info = f"Date: {self.date}"
            
        reprocess_info = "\n[bold yellow]Reprocess Mode: ON[/bold yellow] (regenerating all summaries)" if self.reprocess else ""
        self.console.print(Panel(
            f"[bold blue]Facebook Ad Summary Generator[/bold blue]\n"
            f"Mode: {mode_desc}\n"
            f"{date_info}{reprocess_info}\n"
            f"Batch Size: {self.batch_size} records",
            title="🚀 Starting Process"
        ))
        
        try:
            # Initialize AI services
            await self.initialize_ai_services()
            
            # Use async context manager for storage
            async with self.storage:
                if self.query_mode == 'last_updated':
                    await self.run_last_updated_mode()
                elif self.query_mode == 'date_range':
                    await self.run_date_range_mode()
                else:
                    await self.run_page_date_mode()
            
            # Display final statistics
            self.display_final_stats()
        finally:
            # Always cleanup resources
            await self.cleanup()
    
    async def run_last_updated_mode(self):
        """Run in last_updated mode - query all records by last_updated date"""
        self.console.print(f"📊 Querying all records with LastUpdated = {self.date}")
        
        # Query all records by last_updated
        records = await self.repository.query_by_last_updated(self.date)
        
        if not records:
            self.console.print("❌ No records found for the specified date", style="red")
            return
        
        # Filter records without summaries (unless reprocess flag is set)
        if self.reprocess:
            records_without_summary = records
            self.console.print(f"🔄 Reprocess mode: Processing all {len(records)} records (including those with summaries)")
        else:
            records_without_summary = [r for r in records if not r.get('summary')]
            
            if not records_without_summary:
                self.console.print(f"✅ All {len(records)} records already have summaries!", style="green")
                return
            
            self.console.print(f"📋 Found {len(records)} total records, {len(records_without_summary)} need summaries")
        
        # Skip preview if no_preview flag is set
        if self.no_preview:
            # Process all records directly
            await self._process_all_records(records_without_summary)
            return
        
        # Process first 10 records for preview
        preview_records = records_without_summary[:10]
        
        self.console.print(Panel(
            f"[bold yellow]Preview Mode[/bold yellow]\n"
            f"Generating summaries for first {len(preview_records)} records...",
            title="🔍 Preview"
        ))
        
        # Generate summaries for preview records in parallel
        self.console.print(f"[dim]Processing {len(preview_records)} preview records in parallel...[/dim]")
        
        # Create tasks for parallel preview processing - process with full record update
        preview_tasks = []
        for record in preview_records:
            preview_tasks.append(self._process_single_record(record))
        
        # Execute all preview tasks in parallel
        preview_results = await asyncio.gather(*preview_tasks, return_exceptions=True)
        
        # Process results and display
        preview_summaries = []
        successful_count = 0
        for i, (record, result) in enumerate(zip(preview_records, preview_results)):
            ad_id = record.get('ad_archive_id', 'Unknown')
            
            if isinstance(result, Exception):
                self.logger.error(f"Error generating preview summary for {ad_id}: {result}")
                self.stats['errors'] += 1
                continue
            
            if result:  # result is True/False from _process_single_record
                successful_count += 1
                self.stats['summaries_generated'] += 1
                # Get the summary that was just saved (repository returns snake_case)
                summary = record.get('summary', 'Summary generated')
                preview_summaries.append((record, summary))
                
                # Display preview
                self.console.print(Panel(
                    f"[bold blue]Ad Archive ID:[/bold blue] {ad_id}\n"
                    f"[bold green]Summary:[/bold green] {summary}",
                    title=f"Preview {i+1}",
                    expand=False
                ))
            else:
                self.stats['errors'] += 1
        
        if not preview_summaries:
            self.console.print("❌ No summaries could be generated. Exiting.", style="red")
            return
        
        # Ask user to continue
        self.console.print(f"\n[bold yellow]Generated {successful_count} preview summaries[/bold yellow]")
        user_input = self.console.input("[bold cyan]Continue processing remaining records? (y/n): [/bold cyan]")
        
        if user_input.lower() not in ['y', 'yes']:
            self.console.print("❌ Operation cancelled by user", style="yellow")
            return
        
        # Process remaining records
        remaining_records = records_without_summary[10:]
        if remaining_records:
            self.console.print(f"🔄 Processing remaining {len(remaining_records)} records...")
            
            # Process in parallel batches
            batch_size = self.batch_size  # Use configurable batch size
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                MofNCompleteColumn(),
                console=self.console
            ) as progress:
                
                task = progress.add_task("Processing remaining records...", total=len(remaining_records))
                
                # Process in batches
                for batch_start in range(0, len(remaining_records), batch_size):
                    batch_end = min(batch_start + batch_size, len(remaining_records))
                    batch = remaining_records[batch_start:batch_end]
                    
                    batch_num = batch_start//batch_size + 1
                    total_batches = (len(remaining_records) + batch_size - 1) // batch_size
                    progress.update(task, description=f"Processing batch {batch_num}/{total_batches} ({len(batch)} records in parallel)")
                    
                    # Create tasks for parallel processing
                    tasks = []
                    for record in batch:
                        tasks.append(self._process_single_record(record))
                    
                    # Execute batch in parallel
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # Process results
                    for result in results:
                        if isinstance(result, Exception):
                            self.logger.error(f"Error in batch processing: {result}")
                            self.stats['errors'] += 1
                        elif result:
                            self.stats['summaries_generated'] += 1
                        else:
                            self.stats['errors'] += 1
                        
                        self.stats['processed'] += 1
                    
                    progress.update(task, completed=min(batch_end, len(remaining_records)))
        
        self.console.print(f"✅ Completed processing {len(records_without_summary)} records")
    
    async def run_date_range_mode(self):
        """Run in date_range mode - query all records within a date range"""
        self.console.print(f"📊 Querying all records between {self.start_date} and {self.end_date}")
        
        # Generate list of dates in range
        from datetime import datetime, timedelta
        start_dt = datetime.strptime(self.start_date, "%Y%m%d")
        end_dt = datetime.strptime(self.end_date, "%Y%m%d")
        
        all_records = []
        current_date = start_dt
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            console=self.console
        ) as progress:
            
            # Calculate total days
            total_days = (end_dt - start_dt).days + 1
            date_task = progress.add_task("Querying dates...", total=total_days)
            
            while current_date <= end_dt:
                date_str = current_date.strftime("%Y%m%d")
                progress.update(date_task, description=f"Querying date: {date_str}")
                
                # Query records for this date
                records = await self.repository.query_by_last_updated(date_str)
                if records:
                    all_records.extend(records)
                    self.console.print(f"📅 {date_str}: Found {len(records)} records")
                
                current_date += timedelta(days=1)
                progress.update(date_task, advance=1)
        
        if not all_records:
            self.console.print("❌ No records found in the specified date range", style="red")
            return
        
        # Filter records without summaries (unless reprocess flag is set)
        if self.reprocess:
            records_without_summary = all_records
            self.console.print(f"🔄 Reprocess mode: Processing all {len(all_records)} records (including those with summaries)")
        else:
            records_without_summary = [r for r in all_records if not r.get('summary')]
            
            if not records_without_summary:
                self.console.print(f"✅ All {len(all_records)} records already have summaries!", style="green")
                return
            
            self.console.print(f"📋 Found {len(all_records)} total records, {len(records_without_summary)} need summaries")
        
        # Skip preview if no_preview flag is set
        if self.no_preview:
            # Process all records directly
            await self._process_all_records(records_without_summary)
            return
        
        # Process first 10 records for preview
        preview_records = records_without_summary[:10]
        
        self.console.print(Panel(
            f"[bold yellow]Preview Mode[/bold yellow]\n"
            f"Generating summaries for first {len(preview_records)} records...",
            title="🔍 Preview"
        ))
        
        # Generate summaries for preview records in parallel
        self.console.print(f"[dim]Processing {len(preview_records)} preview records in parallel...[/dim]")
        
        # Create tasks for parallel preview processing - process with full record update
        preview_tasks = []
        for record in preview_records:
            preview_tasks.append(self._process_single_record(record))
        
        # Execute all preview tasks in parallel
        preview_results = await asyncio.gather(*preview_tasks, return_exceptions=True)
        
        # Process results and display
        successful_count = 0
        for i, (record, result) in enumerate(zip(preview_records, preview_results)):
            ad_id = record.get('ad_archive_id', 'Unknown')
            
            if isinstance(result, Exception):
                self.logger.error(f"Error generating preview summary for {ad_id}: {result}")
                self.stats['errors'] += 1
                continue
            
            if result:  # result is True/False from _process_single_record
                successful_count += 1
                self.stats['summaries_generated'] += 1
                # Get the summary that was just saved
                summary = record.get('summary', 'Summary generated')
                
                # Display preview
                self.console.print(Panel(
                    f"[bold blue]Ad Archive ID:[/bold blue] {ad_id}\n"
                    f"[bold green]Summary:[/bold green] {summary}",
                    title=f"Preview {i+1}",
                    expand=False
                ))
            else:
                self.stats['errors'] += 1
        
        if successful_count == 0:
            self.console.print("❌ No summaries could be generated. Exiting.", style="red")
            return
        
        # Ask user to continue
        self.console.print(f"\n[bold yellow]Generated {successful_count} preview summaries[/bold yellow]")
        user_input = self.console.input("[bold cyan]Continue processing remaining records? (y/n): [/bold cyan]")
        
        if user_input.lower() not in ['y', 'yes']:
            self.console.print("❌ Operation cancelled by user", style="yellow")
            return
        
        # Process remaining records
        remaining_records = records_without_summary[10:]
        if remaining_records:
            await self._process_all_records(remaining_records)
        
        self.console.print(f"✅ Completed processing {len(records_without_summary)} records from date range")
    
    async def _process_all_records(self, records: List[Dict[str, Any]]) -> None:
        """Process all records without preview"""
        self.console.print(f"🔄 Processing {len(records)} records in batches of {self.batch_size}...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Processing records...", total=len(records))
            
            # Process in batches
            for batch_start in range(0, len(records), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(records))
                batch = records[batch_start:batch_end]
                
                batch_num = batch_start//self.batch_size + 1
                total_batches = (len(records) + self.batch_size - 1) // self.batch_size
                progress.update(task, description=f"Processing batch {batch_num}/{total_batches} ({len(batch)} records in parallel)")
                
                # Create tasks for parallel processing
                tasks = []
                for record in batch:
                    tasks.append(self._process_single_record(record))
                
                # Execute batch in parallel
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for result in results:
                    if isinstance(result, Exception):
                        self.logger.error(f"Error in batch processing: {result}")
                        self.stats['errors'] += 1
                    elif result:
                        self.stats['summaries_generated'] += 1
                    else:
                        self.stats['errors'] += 1
                    
                    self.stats['processed'] += 1
                
                progress.update(task, completed=min(batch_end, len(records)))
        
        self.console.print(f"✅ Completed processing {len(records)} records")
    
    async def _process_single_record(self, record: Dict[str, Any]) -> bool:
        """Process a single record - generate summary and update DB"""
        try:
            summary = await self.generate_summary(record)
            if summary:
                success = await self.update_record_with_summary(
                    record.get('ad_archive_id'),
                    record.get('start_date', ''),
                    summary
                )
                # Store the summary in the record for display purposes
                if success:
                    record['summary'] = summary
                return success
            return False
        except Exception as e:
            self.logger.error(f"Error processing record {record.get('ad_archive_id')}: {e}")
            return False
    
    async def run_page_date_mode(self):
        """Run in page_date mode - iterate through HTML files"""
        # Get page IDs from HTML files
        page_ids = self.get_page_ids_from_html_files()
        
        if not page_ids:
            self.console.print("❌ No page IDs found. Exiting.", style="red")
            return
        
        # Process each page ID with progress tracking
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            console=self.console
        ) as progress:
            
            main_task = progress.add_task("Processing pages...", total=len(page_ids))
            
            for i, page_id in enumerate(page_ids):
                page_task = progress.add_task(f"Page {page_id}", total=1)
                
                # Process this page
                page_stats = await self.process_page_id(page_id, progress, page_task)
                
                # Update overall stats
                self.stats['processed'] += page_stats['processed']
                self.stats['summaries_generated'] += page_stats['generated']
                self.stats['already_had_summary'] += page_stats['had_summary']
                self.stats['errors'] += page_stats['errors']
                
                progress.update(page_task, completed=1)
                progress.update(main_task, completed=i + 1)
    
    def display_final_stats(self):
        """Display final processing statistics"""
        table = Table(title="📊 Processing Summary")
        table.add_column("Metric", style="cyan")
        table.add_column("Count", justify="right", style="green")
        
        table.add_row("Records Processed", str(self.stats['processed']))
        table.add_row("Summaries Generated", str(self.stats['summaries_generated']))
        table.add_row("Already Had Summary", str(self.stats['already_had_summary']))
        table.add_row("Pages with No Records", str(self.stats['no_records_found']))
        table.add_row("Errors", str(self.stats['errors']))
        table.add_row("Batch Size", str(self.batch_size))
        
        self.console.print(table)
        
        if self.stats['summaries_generated'] > 0:
            self.console.print(f"✅ Successfully generated {self.stats['summaries_generated']} summaries!", style="bold green")
            # Show processing rate
            if hasattr(self, '_start_time'):
                elapsed = time.time() - self._start_time
                rate = self.stats['summaries_generated'] / elapsed if elapsed > 0 else 0
                self.console.print(f"⚡ Processing rate: {rate:.1f} summaries/second", style="dim")
        else:
            self.console.print("ℹ️ No new summaries were generated.", style="yellow")


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Generate summaries for Facebook ads")
    parser.add_argument(
        "--date",
        required=False,
        help="Date in YYYYMMDD format (e.g., 20241201)"
    )
    parser.add_argument(
        "--last_updated",
        required=False,
        help="LastUpdated date in YYYYMMDD format to query all records by last_updated (e.g., 20241201)"
    )
    parser.add_argument(
        "--reprocess",
        action="store_true",
        help="Reprocess all records, even those that already have summaries"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=50,
        help="Number of records to process in parallel (default: 50)"
    )
    parser.add_argument(
        "--start-date",
        required=False,
        help="Start date in YYYYMMDD format for date range queries (e.g., 20250101)"
    )
    parser.add_argument(
        "--end-date",
        required=False,
        help="End date in YYYYMMDD format for date range queries (e.g., 20250301)"
    )
    parser.add_argument(
        "--no-preview",
        action="store_true",
        help="Skip preview and process all records without confirmation"
    )
    
    args = parser.parse_args()
    
    # Validate that either single date or date range is provided
    single_date_modes = [args.date, args.last_updated]
    date_range_mode = [args.start_date, args.end_date]
    
    single_date_count = sum(1 for x in single_date_modes if x)
    date_range_count = sum(1 for x in date_range_mode if x)
    
    if single_date_count == 0 and date_range_count == 0:
        print("❌ Must provide either --date, --last_updated, or --start-date/--end-date")
        sys.exit(1)
    
    if single_date_count > 1:
        print("❌ Only one of --date or --last_updated can be provided")
        sys.exit(1)
    
    if single_date_count > 0 and date_range_count > 0:
        print("❌ Cannot combine single date mode with date range mode")
        sys.exit(1)
    
    if date_range_count > 0 and date_range_count != 2:
        print("❌ Both --start-date and --end-date must be provided for date range mode")
        sys.exit(1)
    
    # Validate date formats
    dates_to_validate = []
    if args.date:
        dates_to_validate.append(args.date)
    if args.last_updated:
        dates_to_validate.append(args.last_updated)
    if args.start_date:
        dates_to_validate.append(args.start_date)
    if args.end_date:
        dates_to_validate.append(args.end_date)
    
    for date_val in dates_to_validate:
        if not date_val.isdigit() or len(date_val) != 8:
            print("❌ All dates must be in YYYYMMDD format (e.g., 20241201)")
            sys.exit(1)
    
    # Determine query mode and create generator
    if args.last_updated:
        query_mode = 'last_updated'
        generator = FBAdSummaryGenerator(
            date=args.last_updated,
            query_mode=query_mode,
            reprocess=args.reprocess,
            batch_size=args.batch_size,
            no_preview=args.no_preview
        )
    elif args.start_date and args.end_date:
        query_mode = 'date_range'
        generator = FBAdSummaryGenerator(
            start_date=args.start_date,
            end_date=args.end_date,
            query_mode=query_mode,
            reprocess=args.reprocess,
            batch_size=args.batch_size,
            no_preview=args.no_preview
        )
    else:
        query_mode = 'page_date'
        generator = FBAdSummaryGenerator(
            date=args.date,
            query_mode=query_mode,
            reprocess=args.reprocess,
            batch_size=args.batch_size,
            no_preview=args.no_preview
        )
    
    try:
        await generator.run()
    except KeyboardInterrupt:
        print("\n⏹️ Process interrupted by user")
        await generator.cleanup()
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        await generator.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())