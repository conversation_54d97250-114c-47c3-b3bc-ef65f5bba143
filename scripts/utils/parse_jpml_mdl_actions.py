#!/usr/bin/env python3
"""
Parse JPML MDL Actions Pending JSON data and convert to CSV format with court name enrichment.

This script:
1. Loads JSON data from JPML actions pending file
2. Transforms each MDL record to the required CSV format
3. Displays results in Rich format for user confirmation
4. Enriches data with court names from DynamoDB
5. Outputs final CSV file

Usage:
    python scripts/utils/parse_jpml_mdl_actions.py
"""

import json
import csv
import re
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional

import pandas as pd
from rich.console import Console
from rich.table import Table
from rich.prompt import Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rprint

# Add project root to path for imports
PROJECT_ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    # Try new migration helper first
    from src.migration import create_manager_replacement
    _using_new_architecture = True
except ImportError:
    _using_new_architecture = False
    from src.lib.district_courts_manager import DistrictCourtsManager
    
try:
    from src.lib.config import PROJECT_ROOT as CONFIG_PROJECT_ROOT
except ImportError as e:
    rprint(f"[red]Error importing required modules: {e}[/red]")
    rprint("[yellow]Please ensure you're running from the project root directory[/yellow]")
    sys.exit(1)

console = Console()


class JPMLMDLParser:
    """Parser for JPML MDL Actions Pending data."""
    
    def __init__(self, json_file_path: str):
        self.json_file_path = json_file_path
        self.mdl_data = []
        self.transformed_data = []
        self.enriched_data = []
        
    def load_json_data(self) -> bool:
        """Load and parse JSON data from file."""
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.mdl_data = data.get('mdl_data', [])
            console.print(f"[green]✓[/green] Loaded {len(self.mdl_data)} MDL records from JSON")
            return True
            
        except FileNotFoundError:
            console.print(f"[red]✗[/red] File not found: {self.json_file_path}")
            return False
        except json.JSONDecodeError as e:
            console.print(f"[red]✗[/red] Invalid JSON format: {e}")
            return False
        except Exception as e:
            console.print(f"[red]✗[/red] Error loading JSON: {e}")
            return False
    
    def extract_mdl_number(self, docket_no: str) -> str:
        """Extract 4-digit MDL number from docket string."""
        if not docket_no:
            return ""
        
        # Find 4-digit number in the docket string
        match = re.search(r'(\d{4})', docket_no)
        return match.group(1) if match else ""
    
    def transform_mdl_data(self) -> None:
        """Transform MDL data to CSV format."""
        self.transformed_data = []
        previous_district = ""
        
        for i, record in enumerate(self.mdl_data):
            # Handle empty district by using previous item
            district = record.get('district', '').strip()
            if not district and previous_district:
                district = previous_district
            else:
                previous_district = district
            
            # Create court_id
            court_id = district.lower() + 'd' if district else ""
            
            # Extract MDL number
            mdl_num = self.extract_mdl_number(record.get('docket_no', ''))
            
            # Get actions pending and total actions
            actions_pending = record.get('actions_pending', 0)
            total_actions = record.get('total_actions', actions_pending)
            
            transformed_record = {
                'report_date': '20250601',
                'court_id': court_id,
                'judge': record.get('judge', ''),
                'title': record.get('title', ''),
                'mdl_num': mdl_num,
                'litigation': record.get('litigation', ''),
                'actions_now_pending': actions_pending,
                'total_actions': total_actions
            }
            
            self.transformed_data.append(transformed_record)
        
        console.print(f"[green]✓[/green] Transformed {len(self.transformed_data)} records")
    
    def display_transformed_data(self) -> None:
        """Display transformed data in Rich table format."""
        if not self.transformed_data:
            console.print("[yellow]No data to display[/yellow]")
            return
        
        # Create table
        table = Table(title="Transformed MDL Data", show_header=True, header_style="bold magenta")
        table.add_column("Report Date", style="cyan", no_wrap=True)
        table.add_column("Court ID", style="green", no_wrap=True)
        table.add_column("Judge", style="blue")
        table.add_column("Title", style="yellow")
        table.add_column("MDL #", style="red", no_wrap=True)
        table.add_column("Actions Pending", style="magenta", no_wrap=True)
        table.add_column("Total Actions", style="bright_blue", no_wrap=True)
        table.add_column("Litigation", style="white")
        
        # Add rows (limit to first 10 for display)
        for i, record in enumerate(self.transformed_data[:10]):
            table.add_row(
                record['report_date'],
                record['court_id'],
                record['judge'][:30] + "..." if len(record['judge']) > 30 else record['judge'],
                record['title'][:20] + "..." if len(record['title']) > 20 else record['title'],
                record['mdl_num'],
                str(record['actions_now_pending']),
                str(record['total_actions']),
                record['litigation'][:50] + "..." if len(record['litigation']) > 50 else record['litigation']
            )
        
        console.print(table)
        
        if len(self.transformed_data) > 10:
            console.print(f"[yellow]... and {len(self.transformed_data) - 10} more records[/yellow]")
        
        # Show summary stats
        total_pending = sum(record['actions_now_pending'] for record in self.transformed_data)
        total_historical = sum(record['total_actions'] for record in self.transformed_data)
        
        console.print(f"\n[bold]Summary:[/bold]")
        console.print(f"Total Records: {len(self.transformed_data)}")
        console.print(f"Total Actions Pending: {total_pending:,}")
        console.print(f"Total Historical Actions: {total_historical:,}")
    
    def load_district_courts_data(self) -> Optional[pd.DataFrame]:
        """Load district courts data from DynamoDB."""
        try:
            console.print("[blue]Loading district courts data from DynamoDB...[/blue]")
            
            # Initialize config (basic config for DynamoDB access)
            config = {
                'aws_region': 'us-west-2',  # Default region
                'use_local_dynamo': False
            }
            
            # Initialize manager
            if _using_new_architecture:
                courts_manager = create_manager_replacement('DistrictCourtsManager', config)
            else:
                courts_manager = DistrictCourtsManager(config)
            
            # Scan table and collect all records
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Scanning district courts table...", total=None)
                
                records = []
                for record in courts_manager.scan_table():
                    records.append(record)
                    progress.update(task, advance=1)
            
            if not records:
                console.print("[yellow]No district courts data found[/yellow]")
                return None
            
            # Convert to DataFrame and deduplicate on CourtId
            df = pd.DataFrame(records)
            initial_count = len(df)
            df = df.drop_duplicates(subset=['CourtId'])
            final_count = len(df)
            
            console.print(f"[green]✓[/green] Loaded {final_count} unique courts (removed {initial_count - final_count} duplicates)")
            return df
            
        except Exception as e:
            console.print(f"[red]✗[/red] Error loading district courts data: {e}")
            console.print("[yellow]Continuing without court name enrichment...[/yellow]")
            return None
    
    def enrich_with_court_names(self, courts_df: pd.DataFrame) -> None:
        """Enrich MDL data with court names."""
        if courts_df is None:
            console.print("[yellow]Skipping court name enrichment - no courts data available[/yellow]")
            self.enriched_data = self.transformed_data.copy()
            return
        
        # Create court lookup dictionary
        court_lookup = dict(zip(courts_df['CourtId'], courts_df['CourtName']))
        
        self.enriched_data = []
        missing_courts = set()
        
        for record in self.transformed_data:
            court_id = record['court_id']
            court_name = court_lookup.get(court_id, '')
            
            if not court_name and court_id:
                missing_courts.add(court_id)
            
            # Create enriched record matching the sample format
            enriched_record = {
                'CourtId': court_id,
                'MdlNum': record['mdl_num'],
                'ActionsNowPending': record['actions_now_pending'],
                'CourtName': court_name,
                'HistoricalActions': record['total_actions'],
                'Litigation': record['litigation'],
                'LoginUrl': f"https://ecf.{court_id}.uscourts.gov" if court_id else ""
            }
            
            self.enriched_data.append(enriched_record)
        
        console.print(f"[green]✓[/green] Enriched {len(self.enriched_data)} records with court names")
        
        if missing_courts:
            console.print(f"[yellow]Warning: No court names found for: {', '.join(sorted(missing_courts))}[/yellow]")
    
    def display_enriched_data(self) -> None:
        """Display enriched data in Rich table format."""
        if not self.enriched_data:
            console.print("[yellow]No enriched data to display[/yellow]")
            return
        
        table = Table(title="Enriched MDL Data with Court Names", show_header=True, header_style="bold magenta")
        table.add_column("Court ID", style="green", no_wrap=True)
        table.add_column("MDL #", style="red", no_wrap=True)
        table.add_column("Actions Pending", style="magenta", no_wrap=True)
        table.add_column("Court Name", style="blue")
        table.add_column("Historical Actions", style="bright_blue", no_wrap=True)
        table.add_column("Litigation", style="white")
        
        # Show first 10 records
        for record in self.enriched_data[:10]:
            table.add_row(
                record['CourtId'],
                record['MdlNum'],
                str(record['ActionsNowPending']),
                record['CourtName'][:40] + "..." if len(record['CourtName']) > 40 else record['CourtName'],
                str(record['HistoricalActions']),
                record['Litigation'][:50] + "..." if len(record['Litigation']) > 50 else record['Litigation']
            )
        
        console.print(table)
        
        if len(self.enriched_data) > 10:
            console.print(f"[yellow]... and {len(self.enriched_data) - 10} more records[/yellow]")
    
    def save_to_csv(self, output_path: str) -> bool:
        """Save enriched data to CSV file."""
        try:
            # Use original CSV headers format
            headers = ['report_date', 'court_id', 'judge', 'title', 'mdl_num', 'litigation', 'actions_now_pending', 'total_actions']
            
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=headers)
                writer.writeheader()
                
                # Convert enriched data back to original format for CSV
                for record in self.transformed_data:
                    writer.writerow(record)
            
            console.print(f"[green]✓[/green] CSV file saved to: {output_path}")
            console.print(f"[green]✓[/green] {len(self.transformed_data)} records written")
            return True
            
        except Exception as e:
            console.print(f"[red]✗[/red] Error saving CSV: {e}")
            return False


def main():
    """Main function."""
    console.print("\n[bold blue]JPML MDL Actions Pending Parser[/bold blue]\n")
    
    # Define file paths
    json_file = "/Users/<USER>/PycharmProjects/lexgenius/src/data/mdls/json/20250601-jpml-actions-pending.json"
    output_csv = "/Users/<USER>/PycharmProjects/lexgenius/src/data/mdls/mdls_actions_pending/20250601-jpml-actions-pending.csv"
    
    # Initialize parser
    parser = JPMLMDLParser(json_file)
    
    # Phase 1: Load and transform data
    console.print("[bold]Phase 1: Loading and transforming JSON data[/bold]")
    
    if not parser.load_json_data():
        console.print("[red]Failed to load JSON data. Exiting.[/red]")
        return
    
    parser.transform_mdl_data()
    parser.display_transformed_data()
    
    # Get user confirmation to proceed
    if not Confirm.ask("\n[bold]Continue with court name enrichment?[/bold]"):
        console.print("[yellow]Operation cancelled by user.[/yellow]")
        return
    
    # Phase 2: Enrich with court names
    console.print("\n[bold]Phase 2: Enriching with court names[/bold]")
    
    courts_df = parser.load_district_courts_data()
    parser.enrich_with_court_names(courts_df)
    parser.display_enriched_data()
    
    # Get final confirmation to save
    if not Confirm.ask(f"\n[bold]Save data to CSV file?[/bold]\n[dim]{output_csv}[/dim]"):
        console.print("[yellow]Save cancelled by user.[/yellow]")
        return
    
    # Save to CSV
    console.print("\n[bold]Phase 3: Saving to CSV[/bold]")
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_csv), exist_ok=True)
    
    if parser.save_to_csv(output_csv):
        console.print(f"\n[bold green]✓ Process completed successfully![/bold green]")
        console.print(f"[green]CSV file saved to: {output_csv}[/green]")
    else:
        console.print(f"\n[bold red]✗ Failed to save CSV file[/bold red]")


if __name__ == "__main__":
    main()