# -*- coding: utf-8 -*-
# --- START OF FILE src/scripts/hash_fb_images.py ---
#!/usr/bin/env python3
import argparse
import asyncio
import logging
import os
import sys
import time
from typing import Any, Dict, Optional

import boto3
from botocore.config import Config
from botocore.exceptions import ClientError, NoCredentialsError
from rich.logging import RichHandler
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn, TimeElapsedColumn, \
    MofNCompleteColumn

# --- UPDATED Imports ---
# Ensure src/lib is in the Python path or adjust relative imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.lib.config_adapter import load_config
from src.services.fb_ads import (
    extract_ids_from_key,
    process_single_image_async # Import the async version
)
from src.services.fb_ads.image_utils import FBImageHashManager
# --- END UPDATED Imports ---


# Configure logging
# Use RichHandler for better console output
logging.basicConfig(
    level="INFO",
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True, show_path=False)]
)
logger = logging.getLogger(__name__)
logging.getLogger("botocore").setLevel(logging.WARNING)
logging.getLogger("boto3").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("PIL").setLevel(logging.WARNING)

# Constants
BATCH_SIZE = 25 # DynamoDB BatchWriteItem limit


# --- REMOVED process_single_image function definition ---
# It is now imported from src.lib.fb_ads.image_utils


async def process_images_in_script(
    config: Dict[str, Any],
    bucket_name: str, # Bucket name is now passed in directly
    use_local_db: bool,
    local_db_port: int,
    max_workers: int,
    limit: Optional[int] = None
):
    """
    (Script-specific version) Lists images in the specified S3 prefix, calculates hashes,
    and stores them in DynamoDB.
    """
    s3_prefix = config.get('s3_fb_ad_archive_prefix', 'adarchive/fb/')
    expected_key_format = config.get('s3_key_format', 'original') # e.g., 'original' or 'archive_id_creative_id'

    logger.info(f"(Script) Starting processing for bucket='{bucket_name}', prefix='{s3_prefix}'") # Use the passed bucket_name
    logger.info(f"(Script) Using {'local' if use_local_db else 'AWS'} DynamoDB. Workers={max_workers}, BatchSize={BATCH_SIZE}")
    logger.info(f"(Script) Expecting S3 key format: '{expected_key_format}'")

    # --- Initialize S3 and DynamoDB Clients/Managers ---
    s3_client = None
    hash_manager = None
    try:
        # *** Configure S3 client with increased connection pool ***
        s3_config = Config(max_pool_connections=max_workers + 5) # Adjust pool size based on workers

        if use_local_db:
            logger.info(f"(Script) Initializing LOCAL mode for table '{config['dynamodb']['fb_image_hash_table_name']}' port {local_db_port}")
            aws_profile = config.get('aws_profile')
            session = boto3.Session(profile_name=aws_profile) if aws_profile else boto3.Session()
            s3_client = session.client('s3', region_name=config['aws_region'], config=s3_config) # Pass config

            hash_manager = FBImageHashManager(
                table_name=config['dynamodb']['fb_image_hash_table_name'],
                config=config, # Pass the main config dictionary
                use_local=use_local_db,
                local_port=local_db_port,
                remove_empty_str=False # Explicitly prevent removing empty keys
            )
        else:
            # Production DynamoDB and S3
            logger.info(f"(Script) Initializing AWS mode for table '{config['dynamodb']['fb_image_hash_table_name']}' region '{config['aws_region']}'")
            aws_profile = config.get('aws_profile')
            session = boto3.Session(profile_name=aws_profile) if aws_profile else boto3.Session()
            s3_client = session.client('s3', region_name=config['aws_region'], config=s3_config) # Pass config

            hash_manager = FBImageHashManager(
                table_name=config['dynamodb']['fb_image_hash_table_name'],
                config=config, # Pass the main config dictionary
                use_local=False,
                remove_empty_str=False # Explicitly prevent removing empty keys
            )

        # Ensure table exists (applies to both local and AWS)
        if not hash_manager.table_exists():
            logger.error(f"(Script) DynamoDB table '{hash_manager.table_name}' not found or accessible.")
            return # Exit processing if table isn't there

    except NoCredentialsError:
        logger.error("(Script) AWS credentials not found. Configure credentials (e.g., ~/.aws/credentials, environment variables, IAM role).")
        sys.exit(1)
    except ClientError as e:
         logger.error(f"(Script) AWS ClientError during initialization: {e}", exc_info=True)
         sys.exit(1)
    except Exception as e:
        logger.error(f"(Script) Failed to initialize AWS clients or DynamoDB manager: {e}", exc_info=True)
        sys.exit(1)

    # --- List S3 Objects ---
    logger.info("(Script) Listing S3 objects...")
    paginator = s3_client.get_paginator('list_objects_v2')
    objects_to_process = []
    processed_object_count = 0 # Renamed for clarity
    start_time = time.time()

    try:
        for page in paginator.paginate(Bucket=bucket_name, Prefix=s3_prefix):
            if 'Contents' not in page:
                continue
            for obj in page['Contents']:
                s3_key = obj['Key']
                # Basic filtering for common image/video types
                if not any(s3_key.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']): # Script focuses on images only?
                    continue

                ad_archive_id, ad_creative_id = extract_ids_from_key(s3_key, expected_key_format)

                # Stricter validation for extracted IDs
                is_valid_archive_id = ad_archive_id and isinstance(ad_archive_id, str) and len(ad_archive_id.strip()) > 0
                is_valid_creative_id = ad_creative_id and isinstance(ad_creative_id, str) and len(ad_creative_id.strip()) > 0

                if not is_valid_archive_id or not is_valid_creative_id:
                    logger.debug( # Use debug level for potentially noisy logs
                        f"(Script) Could not extract valid, non-empty ArchiveID ('{ad_archive_id}') "
                        f"and CreativeID ('{ad_creative_id}') from S3 key: {s3_key}. Skipping."
                    )
                    continue

                objects_to_process.append({
                    'key': s3_key,
                    'archive_id': ad_archive_id,
                    'creative_id': ad_creative_id
                })
                processed_object_count += 1

                if limit and processed_object_count >= limit:
                    logger.info(f"(Script) Reached processing limit of {limit} S3 objects.")
                    break # Stop processing objects
            if limit and processed_object_count >= limit:
                break # Stop paginating

    except ClientError as e:
        logger.error(f"(Script) Error listing S3 objects: {e}", exc_info=True)
        return
    except Exception as e:
        logger.error(f"(Script) Unexpected error during S3 listing: {e}", exc_info=True)
        return

    elapsed_time = time.time() - start_time
    logger.info(f"(Script) Found {len(objects_to_process)} objects to process in {elapsed_time:.2f}s.")

    if not objects_to_process:
        logger.info("(Script) No images found to process. Exiting.")
        return

    # --- Process Images Concurrently ---
    hashes_to_write = []
    processed_image_count = 0
    failed_image_count = 0
    future_to_info: Dict[Any, str] = {} # Map future to s3_key for logging

    # Setup Rich progress bar
    with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            transient=False  # Keep progress bar after completion
        ) as progress:

        task_id = progress.add_task("Processing images (Script)", total=len(objects_to_process))

        try:
            # Use async concurrency instead of ThreadPoolExecutor
            semaphore = asyncio.Semaphore(max_workers)
            
            async def process_with_semaphore(image_info):
                async with semaphore:
                    s3_key = image_info['key']
                    ad_archive_id = image_info['archive_id']
                    ad_creative_id = image_info['creative_id']
                    return await process_single_image_async(
                        s3_client, bucket_name, s3_key, ad_archive_id, ad_creative_id
                    ), s3_key
            
            # Create tasks for all images
            tasks = [process_with_semaphore(image_info) for image_info in objects_to_process]
            
            # Process results as they complete
            for coro in asyncio.as_completed(tasks):
                    try:
                        image_hash_record, s3_key = await coro
                        if image_hash_record:
                            hashes_to_write.append(image_hash_record)
                            processed_image_count += 1

                            # Write to DynamoDB in batches
                            if len(hashes_to_write) >= BATCH_SIZE:
                                logger.debug(f"(Script) Writing batch of {len(hashes_to_write)} hashes to repository...")
                                try:
                                    success_count, failure_count = hash_manager.batch_insert_items(hashes_to_write)
                                    logger.info(f"(Script) Batch write result: Successful={success_count}, Failed={failure_count}")
                                except Exception as e:
                                    logger.error(f"(Script) Batch write failed: {e}")
                                    failure_count = len(hashes_to_write)
                                    success_count = 0
                                hashes_to_write = [] # Clear batch
                        else:
                            # Hash calculation or download failed
                            failed_image_count += 1

                    except Exception as exc:
                        logger.error(f"(Script) Error processing image: {exc}", exc_info=True)
                        failed_image_count += 1
                    finally:
                         progress.update(task_id, advance=1) # Advance progress bar for each completed task

            # Write any remaining hashes after the loop finishes
            if hashes_to_write:
                logger.info(f"(Script) Writing final batch of {len(hashes_to_write)} hashes to repository...")
                try:
                    success_count, failure_count = hash_manager.batch_insert_items(hashes_to_write)
                    logger.info(f"(Script) Final batch write result: Successful={success_count}, Failed={failure_count}")
                except Exception as e:
                    logger.error(f"(Script) Final batch write failed: {e}")
                    failure_count = len(hashes_to_write)
                    success_count = 0

        except KeyboardInterrupt:
             logger.warning("(Script) Keyboard interrupt received. Shutting down workers and attempting to write remaining hashes...")
             # Executor shutdown happens automatically in __exit__
             if hashes_to_write:
                 logger.info(f"(Script) Writing final batch of {len(hashes_to_write)} hashes due to interrupt...")
                 try:
                     success_count, failure_count = hash_manager.batch_insert_items(hashes_to_write)
                     logger.info(f"(Script) Final batch write result: Successful={success_count}, Failed={failure_count}")
                 except Exception as e:
                     logger.error(f"(Script) Final batch write failed: {e}")
             logger.info("(Script) Processing stopped by user.")
             # Allow finally block to run
        except Exception as e:
             logger.error(f"(Script) An unexpected error occurred during concurrent processing: {e}", exc_info=True)
             # Allow finally block to run
        finally:
             # Ensure progress bar stops cleanly
             if not progress.finished:
                 progress.update(task_id, completed=progress.tasks[task_id].completed)
                 
    finally:
        # Clean up hash manager connection
        if hash_manager:
            try:
                # FBImageHashManager doesn't have async exit, so no cleanup needed
                logger.debug("(Script) Hash manager cleanup completed")
            except Exception as e:
                logger.error(f"Error cleaning up hash manager: {e}")

    logger.info(f"(Script) Finished processing.")
    logger.info(f"(Script) Successfully processed and attempted write for {processed_image_count} images.")
    logger.info(f"(Script) Failed to process/hash {failed_image_count} images.")


# --- Main Execution (Script) ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="(Script) Calculate perceptual hashes for FB ad images in S3 and store in DynamoDB.")
    # Bucket is now sourced from config, same as in image_utils.py
    parser.add_argument("--local-db", action='store_true', help="Use local DynamoDB instance.")
    parser.add_argument("--db-port", type=int, default=8000, help="Port for local DynamoDB instance.")
    parser.add_argument("--workers", type=int, default=os.cpu_count() or 4, help="Number of concurrent workers for processing.")
    parser.add_argument("--limit", type=int, default=None, help="Limit the number of S3 objects listed for processing (for testing).")
    parser.add_argument("--debug", action='store_true', help="Enable debug logging.")

    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.setLevel(logging.DEBUG)
        for handler in logging.getLogger().handlers:
            if isinstance(handler, RichHandler):
                handler.setLevel(logging.DEBUG)
        logger.debug("(Script) Debug logging enabled.")

    # --- Load Configuration ---
    try:
        # Use a default date or make it configurable if needed for the script
        config = load_config('01/01/70')
        if not config.get('aws_region'):
            logger.warning("(Script) AWS region not found in config, defaulting to 'us-west-2'.")
            config['aws_region'] = config.get('aws_region', 'us-west-2')
        if not config.get('dynamodb', {}).get('fb_image_hash_table_name'):
             raise ValueError("Missing 'dynamodb.fb_image_hash_table_name' in config.")
    except FileNotFoundError:
        logger.error(f"(Script) Configuration file not found. Ensure config exists at expected location for load_config.")
        sys.exit(1)
    except ValueError as ve:
         logger.error(f"(Script) Configuration Error: {ve}")
         sys.exit(1)
    except Exception as e:
        logger.error(f"(Script) Failed to load configuration: {e}", exc_info=True)
        sys.exit(1)

    # --- Determine Bucket Name (from config ONLY) ---
    config_bucket_key = 'bucket_name'
    bucket_to_use = config.get(config_bucket_key)
    if not bucket_to_use:
        logger.error(f"(Script) S3 bucket name must be defined as '{config_bucket_key}' in the configuration file.")
        sys.exit(1)
    else:
        logger.info(f"(Script) Using bucket name '{bucket_to_use}' from configuration key '{config_bucket_key}'.")

    # --- Validate Worker Count ---
    if args.workers <= 0:
        logger.error("(Script) Number of workers must be positive.")
        sys.exit(1)
    if args.workers > 100:
        logger.warning(f"(Script) High number of workers ({args.workers}) requested. Ensure sufficient system resources.")

    # --- Run Processing ---
    asyncio.run(process_images_in_script( # Call the async script-specific wrapper
        config=config,
        bucket_name=bucket_to_use, # Use the bucket name from config
        use_local_db=args.local_db,
        local_db_port=args.db_port,
        max_workers=args.workers,
        limit=args.limit,
    ))
# --- END OF FILE src/scripts/hash_fb_images.py ---