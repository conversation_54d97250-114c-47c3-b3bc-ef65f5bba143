#!/usr/bin/env python3

import argparse
import os
from pathlib import Path
from typing import List, Set, <PERSON><PERSON>


def match_html_to_json(date: str) -> List[Tuple[str, str]]:
    """
    Compare JSON and HTML files for a given date and return (court_id, docket_num) tuples
    for files that exist in JSON but not in HTML.

    Args:
        date: Date string in YYYYMMDD format

    Returns:
        List of (court_id, docket_num) tuples that exist in JSON but not in HTML
    """
    data_dir = Path("data") / date
    dockets_dir = data_dir / "dockets"
    html_dir = data_dir / "html"

    # Get base filenames from JSON files
    json_files: Set[str] = set()
    if dockets_dir.exists():
        for json_file in dockets_dir.glob("*.json"):
            json_files.add(json_file.stem)

    # Get base filenames from HTML files
    html_files: Set[str] = set()
    if html_dir.exists():
        for html_file in html_dir.glob("*.html"):
            html_files.add(html_file.stem)

    # Return files in JSON but not in HTML as (court_id, docket_num) tuples
    missing_in_html = json_files - html_files
    result = []
    for filename in sorted(missing_in_html):
        # Split filename assuming format: court_id_docket_num
        parts = filename.split('_', 1)
        if len(parts) >= 2:
            court_id = parts[0]
            docket_num = parts[1]
            result.append((court_id, docket_num))
        else:
            # Handle edge case where filename doesn't follow expected pattern
            result.append((filename, ""))

    return result


def main():
    parser = argparse.ArgumentParser(description="Match HTML files to JSON files")
    parser.add_argument("--date", required=True, help="Date in YYYYMMDD format")

    args = parser.parse_args()

    missing_files = match_html_to_json(args.date)

    if missing_files:
        print(f"Files in JSON but not in HTML for {args.date}:")
        for court_id, docket_num in missing_files:
            print(f"  {court_id}, {docket_num}")
        print(f"\nTotal: {len(missing_files)} files")
    else:
        print(f"All JSON files have corresponding HTML files for {args.date}")


if __name__ == "__main__":
    main()