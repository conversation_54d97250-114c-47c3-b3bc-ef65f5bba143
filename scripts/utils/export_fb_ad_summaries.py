#!/usr/bin/env python3
"""
Export Facebook Ad Summaries - Query ads from last 30 days and export data
"""
import argparse
import asyncio
import json
import logging
import sys
from collections import Counter, defaultdict
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from pathlib import Path
from typing import Dict, List, Any, Set
import glob

from rich.console import Console
from rich.table import Table
from rich.text import Text
from rich.prompt import Prompt

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.repositories.fb_archive_repository import FBArchiveRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.protocols.logger import LoggerProtocol


class SimpleLogger(LoggerProtocol):
    """Simple console logger implementation"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(name)
        
    def debug(self, message: str, extra: Dict[str, Any] = None):
        self.logger.debug(f"{message} {extra or ''}")
        
    def info(self, message: str, extra: Dict[str, Any] = None):
        self.logger.info(f"{message} {extra or ''}")
        
    def warning(self, message: str, extra: Dict[str, Any] = None):
        self.logger.warning(f"{message} {extra or ''}")
        
    def error(self, message: str, extra: Dict[str, Any] = None, exc_info: bool = False):
        self.logger.error(f"{message} {extra or ''}", exc_info=exc_info)
        
    def exception(self, message: str, extra: Dict[str, Any] = None):
        self.logger.exception(f"{message} {extra or ''}")


async def query_ads_by_last_updated_and_date_range(repository: FBArchiveRepository, start_date: str, end_date: str) -> List[Dict[str, Any]]:
    """Query ads by LastUpdated with StartDate/EndDate filtering"""
    print(f"🔍 Querying ads by LastUpdated from {start_date} to {end_date}...")
    print(f"📋 Filtering: StartDate >= {start_date} OR EndDate >= {start_date}")
    
    try:
        # Query each day individually using LastUpdated index for efficiency
        print("  🔄 Querying by LastUpdated per day...")
        current_date = datetime.strptime(start_date, '%Y%m%d')
        end_date_dt = datetime.strptime(end_date, '%Y%m%d')
        all_ads = []
        
        while current_date <= end_date_dt:
            date_str = current_date.strftime('%Y%m%d')
            print(f"    📅 LastUpdated: {date_str}")
            
            try:
                # Query by specific LastUpdated date
                ads = await repository.query_by_last_updated(date_str)
                if ads:
                    all_ads.extend(ads)
                    print(f"      ✅ Found {len(ads)} ads")
            except Exception as e:
                print(f"      ❌ Error querying {date_str}: {e}")
                
            current_date += timedelta(days=1)
        
        print(f"  📊 Found {len(all_ads)} total ads by LastUpdated")
        
        if not all_ads:
            print("  ⚠️  No ads found in LastUpdated range")
            return []
        
        # Filter by StartDate/EndDate criteria
        print("  🎯 Filtering by StartDate/EndDate criteria...")
        filtered_ads = []
        
        for ad in all_ads:
            ad_start_date = ad.get('start_date', '')
            ad_end_date = ad.get('end_date', '')
            
            # Include if StartDate >= start_date OR EndDate >= start_date
            include_ad = False
            
            if ad_start_date and ad_start_date >= start_date:
                include_ad = True
            elif ad_end_date and ad_end_date >= start_date:
                include_ad = True
            
            if include_ad:
                filtered_ads.append(ad)
        
        print(f"  ✅ Filtered to {len(filtered_ads)} ads matching date criteria")
        
    except Exception as e:
        print(f"  ❌ Error querying by LastUpdated: {e}")
        return []
    
    print(f"🎯 Total ads found: {len(filtered_ads)}")
    return filtered_ads


def convert_decimal(obj):
    """Convert Decimal objects to int/float for JSON serialization"""
    if isinstance(obj, Decimal):
        return float(obj) if obj % 1 else int(obj)
    return obj

def normalize_law_firm(law_firm: Any) -> str:
    """Normalize law firm names for consistent grouping"""
    if not law_firm or law_firm in ['', 'NA', 'N/A', 'None', None]:
        return 'Unknown'
    
    law_firm_str = str(law_firm).strip()
    if not law_firm_str:
        return 'Unknown'
    
    return law_firm_str

def analyze_law_firms_by_summary(ads: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze law firms by summary with unique ad counts"""
    print("🏢 Analyzing law firms by summary...")
    
    # Group by summary -> law_firm -> set of unique ad_archive_ids
    summary_law_firm_ads = defaultdict(lambda: defaultdict(set))
    
    for ad in ads:
        summary = ad.get('summary')
        law_firm = normalize_law_firm(ad.get('law_firm'))
        ad_archive_id = convert_decimal(ad.get('ad_archive_id'))
        
        if summary and ad_archive_id:
            summary_law_firm_ads[summary][law_firm].add(str(ad_archive_id))
    
    # Convert to final structure with counts and sorting
    analysis = {}
    
    for summary, law_firms in summary_law_firm_ads.items():
        # Calculate law firm stats
        law_firm_stats = []
        total_unique_ads = 0
        
        for law_firm, ad_ids in law_firms.items():
            unique_ad_count = len(ad_ids)
            total_unique_ads += unique_ad_count
            law_firm_stats.append({
                'name': law_firm,
                'unique_ads': unique_ad_count
            })
        
        # Sort law firms by unique ad count (descending)
        law_firm_stats.sort(key=lambda x: x['unique_ads'], reverse=True)
        
        analysis[summary] = {
            'total_unique_ads': total_unique_ads,
            'law_firm_count': len(law_firm_stats),
            'law_firms': law_firm_stats
        }
    
    print(f"  🎯 Analyzed {len(analysis)} summaries")
    print(f"  🏢 Found law firm data for {sum(1 for data in analysis.values() if data['law_firm_count'] > 0)} summaries")
    
    return analysis

def process_ads_data(ads: List[Dict[str, Any]]) -> tuple[List[Dict[str, Any]], List[tuple[str, int]], Dict[str, Any]]:
    """Process ads data to extract required fields and perform analysis"""
    print("📊 Processing ads data...")
    
    # Track unique ad_archive_ids per summary using sets
    summary_unique_ads = defaultdict(set)
    processed_ads_dict = {}  # Use dict to deduplicate by ad_archive_id
    
    total_records = 0
    valid_records = 0
    
    for ad in ads:
        total_records += 1
        
        # Convert any Decimal values
        ad_archive_id = convert_decimal(ad.get('ad_archive_id'))
        ad_creative_id = convert_decimal(ad.get('ad_creative_id'))
        summary = ad.get('summary')
        
        # Only require ad_archive_id to be present (most critical field)
        if ad_archive_id:
            valid_records += 1
            ad_archive_id_str = str(ad_archive_id)
            
            # Create ad data structure
            ad_data = {
                'ad_archive_id': ad_archive_id,
                'ad_creative_id': ad_creative_id,
                'summary': summary
            }
            
            # Track unique ad_archive_ids per summary (including None/empty summaries)
            if summary:  # Only track summaries that exist
                summary_unique_ads[summary].add(ad_archive_id_str)
            
            # Deduplicate processed_ads by ad_archive_id (keep latest)
            processed_ads_dict[ad_archive_id_str] = ad_data
    
    # Convert to list for backward compatibility
    processed_ads = list(processed_ads_dict.values())
    
    # Create summary counts based on unique ad_archive_ids
    summary_counts = [(summary, len(ad_ids)) for summary, ad_ids in summary_unique_ads.items()]
    sorted_summaries = sorted(summary_counts, key=lambda x: x[1], reverse=True)
    
    # Perform enhanced law firm analysis
    law_firm_analysis = analyze_law_firms_by_summary(ads)
    
    # Calculate deduplication stats
    duplicates_removed = valid_records - len(processed_ads)
    
    print(f"  ✅ Processed {len(processed_ads)} unique ads from {valid_records} valid records")
    print(f"  🔄 Removed {duplicates_removed} duplicate ad_archive_ids")
    print(f"  📈 Found {len(sorted_summaries)} unique summaries")
    
    return processed_ads, sorted_summaries, law_firm_analysis


def display_law_firm_analysis(law_firm_analysis: Dict[str, Any], date: str):
    """Display law firm analysis in rich console format and save to log file"""
    
    # Create console for both display and file output
    console = Console()
    
    # Create log file path
    project_root = Path(__file__).parent.parent.parent
    log_filename = f"fb_analysis_log_{date}.txt"
    log_path = project_root / log_filename
    
    # Create file console for logging (without colors for clean text output)
    file_console = Console(file=open(log_path, 'w', encoding='utf-8'), width=120, legacy_windows=False)
    
    try:
        # Sort summaries by total unique ads for display
        sorted_analysis = sorted(
            law_firm_analysis.items(),
            key=lambda x: x[1]['total_unique_ads'],
            reverse=True
        )
        
        # Header for both console and file
        header_line = "="*80
        header_text = "🏢 LAW FIRM ANALYSIS BY SUMMARY"
        
        # Display on console with rich formatting
        console.print("\n" + header_line, style="bold blue")
        console.print(header_text, style="bold blue", justify="center")
        console.print(header_line, style="bold blue")
        
        # Log to file with plain text
        file_console.print(f"\n{header_line}")
        file_console.print(header_text.center(80))
        file_console.print(header_line)
        
        for summary, data in sorted_analysis:  # Show ALL summaries
            if data['total_unique_ads'] == 0:
                continue
                
            # Summary header
            summary_header = f"📋 {summary}"
            summary_stats = f"   Total: {data['total_unique_ads']} ads • {data['law_firm_count']} law firms"
            
            # Display on console
            console.print(f"\n[bold cyan]{summary_header}[/bold cyan]")
            console.print(f"[dim]{summary_stats}[/dim]")
            
            # Log to file
            file_console.print(f"\n{summary_header}")
            file_console.print(summary_stats)
            
            # Create table for law firms
            table = Table(show_header=False, box=None, padding=(0, 2))
            table.add_column("Law Firm", style="green", min_width=30)
            table.add_column("Ads", style="yellow", justify="right")
            
            # Add ALL law firm rows to console table
            for law_firm in data['law_firms']:  # Show ALL law firms
                table.add_row(
                    f"  • {law_firm['name']}",
                    f"{law_firm['unique_ads']} ads"
                )
            
            console.print(table)
            
            # Add law firms to file (plain text format)
            for law_firm in data['law_firms']:
                law_firm_line = f"    • {law_firm['name']:<40} {law_firm['unique_ads']:>3} ads"
                file_console.print(law_firm_line)
        
        # Footer
        console.print("\n" + header_line, style="bold blue")
        file_console.print(f"\n{header_line}")
        
        print(f"\n📄 Rich analysis logged to: {log_path}")
        
    finally:
        # Close the file
        file_console.file.close()

def save_results(processed_ads: List[Dict[str, Any]], sorted_summaries: List[tuple[str, int]], law_firm_analysis: Dict[str, Any], date: str):
    """Save results to fixed JSON files (overwrite existing)"""
    project_root = Path(__file__).parent.parent.parent
    
    # Fixed filenames - OVERWRITE existing files
    ads_filename = "fb_ads_export.json"
    summaries_filename = "fb_summaries_export.json"
    law_firm_filename = "fb_law_firm_analysis.json"
    summary_freq_filename = "fb_summary_frequencies.json"
    
    ads_path = project_root / ads_filename
    summaries_path = project_root / summaries_filename
    law_firm_path = project_root / law_firm_filename
    summary_freq_path = project_root / summary_freq_filename
    
    print(f"💾 Saving ads data to {ads_filename} (OVERWRITING)...")
    with open(ads_path, 'w', encoding='utf-8') as f:
        json.dump(processed_ads, f, indent=2, ensure_ascii=False)
    
    summaries_data = [
        {"summary": summary, "count": count}
        for summary, count in sorted_summaries
    ]
    
    print(f"📋 Saving summaries to {summaries_filename} (OVERWRITING)...")
    with open(summaries_path, 'w', encoding='utf-8') as f:
        json.dump(summaries_data, f, indent=2, ensure_ascii=False)
    
    # Sort summaries by total unique ads for better readability
    sorted_analysis = dict(sorted(
        law_firm_analysis.items(),
        key=lambda x: x[1]['total_unique_ads'],
        reverse=True
    ))
    
    print(f"🏢 Saving law firm analysis to {law_firm_filename} (OVERWRITING)...")
    with open(law_firm_path, 'w', encoding='utf-8') as f:
        json.dump(sorted_analysis, f, indent=2, ensure_ascii=False)
    
    # NEW: Save just the unique summary frequencies in descending order
    summary_freq_list = [summary for summary, count in sorted_summaries]
    
    print(f"📊 Saving summary frequencies to {summary_freq_filename} (OVERWRITING)...")
    with open(summary_freq_path, 'w', encoding='utf-8') as f:
        json.dump(summary_freq_list, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Export complete!")
    print(f"  📁 Ads file: {ads_path}")
    print(f"  📁 Summaries file: {summaries_path}")
    print(f"  📁 Law firm analysis: {law_firm_path}")
    print(f"  📁 Summary frequencies: {summary_freq_path}")
    
    return ads_path, summaries_path, law_firm_path, summary_freq_path


def find_latest_ads_file() -> Path:
    """Find the fb_ads_export.json file"""
    project_root = Path(__file__).parent.parent.parent
    ads_file = project_root / "fb_ads_export.json"
    
    if not ads_file.exists():
        raise FileNotFoundError("fb_ads_export.json file not found")
    
    return ads_file


def interactive_summary_analysis():
    """Interactive mode to analyze summaries from latest ads file"""
    console = Console()
    
    try:
        # Find and load latest ads file
        latest_file = find_latest_ads_file()
        console.print(f"\n🔍 Loading latest ads file: [bold blue]{latest_file.name}[/bold blue]")
        
        with open(latest_file, 'r', encoding='utf-8') as f:
            ads_data = json.load(f)
        
        console.print(f"📊 Loaded {len(ads_data)} ads from raw data")
        
        # Create summary frequency count from raw ads data
        summary_counts = Counter()
        for ad in ads_data:
            summary = ad.get('summary')
            if summary:
                summary_counts[summary] += 1
        
        # Sort by frequency (descending)
        sorted_summaries = summary_counts.most_common()
        
        console.print(f"📈 Found {len(sorted_summaries)} unique summaries\n")
        
        # Display summary frequency table
        table = Table(title="📋 Summary Frequency Analysis (Descending Order)")
        table.add_column("Rank", style="cyan", width=6)
        table.add_column("Summary", style="green", min_width=40)
        table.add_column("Count", style="yellow", justify="right", width=8)
        table.add_column("% of Total", style="magenta", justify="right", width=10)
        
        total_ads = len(ads_data)
        
        for rank, (summary, count) in enumerate(sorted_summaries, 1):
            percentage = (count / total_ads) * 100
            table.add_row(
                str(rank),
                summary[:60] + "..." if len(summary) > 60 else summary,
                str(count),
                f"{percentage:.1f}%"
            )
        
        console.print(table)
        
        # Interactive options
        while True:
            console.print("\n[bold cyan]Interactive Options:[/bold cyan]")
            console.print("1. Show top N summaries")
            console.print("2. Search for specific summary")
            console.print("3. Show summaries with minimum count")
            console.print("4. Export current view to file")
            console.print("5. Exit")
            
            choice = Prompt.ask("Choose an option", choices=["1", "2", "3", "4", "5"])
            
            if choice == "1":
                n = int(Prompt.ask("Show top N summaries", default="10"))
                top_table = Table(title=f"📊 Top {n} Summaries")
                top_table.add_column("Rank", style="cyan", width=6)
                top_table.add_column("Summary", style="green", min_width=40)
                top_table.add_column("Count", style="yellow", justify="right", width=8)
                
                for rank, (summary, count) in enumerate(sorted_summaries[:n], 1):
                    top_table.add_row(str(rank), summary, str(count))
                
                console.print(top_table)
                
            elif choice == "2":
                search_term = Prompt.ask("Enter search term")
                matches = [(s, c) for s, c in sorted_summaries if search_term.lower() in s.lower()]
                
                if matches:
                    search_table = Table(title=f"🔍 Search Results for '{search_term}'")
                    search_table.add_column("Summary", style="green", min_width=40)
                    search_table.add_column("Count", style="yellow", justify="right", width=8)
                    
                    for summary, count in matches:
                        search_table.add_row(summary, str(count))
                    
                    console.print(search_table)
                else:
                    console.print(f"[yellow]No summaries found containing '{search_term}'[/yellow]")
                    
            elif choice == "3":
                min_count = int(Prompt.ask("Minimum count", default="5"))
                filtered = [(s, c) for s, c in sorted_summaries if c >= min_count]
                
                filter_table = Table(title=f"📈 Summaries with ≥{min_count} ads")
                filter_table.add_column("Summary", style="green", min_width=40)
                filter_table.add_column("Count", style="yellow", justify="right", width=8)
                
                for summary, count in filtered:
                    filter_table.add_row(summary, str(count))
                
                console.print(filter_table)
                console.print(f"[dim]Showing {len(filtered)} summaries with ≥{min_count} ads[/dim]")
                
            elif choice == "4":
                export_filename = "fb_summaries_export.json"
                export_path = Path(__file__).parent.parent.parent / export_filename
                
                export_data = [
                    {"summary": summary, "count": count}
                    for summary, count in sorted_summaries
                ]
                
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)
                
                console.print(f"[green]✅ Exported to {export_filename} (OVERWRITTEN)[/green]")
                
            elif choice == "5":
                console.print("[green]👋 Goodbye![/green]")
                break
                
    except FileNotFoundError as e:
        console.print(f"[red]❌ {e}[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error in interactive mode: {e}[/red]")


async def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Export Facebook Ad Summaries')
    parser.add_argument('--date', help='Start date in YYYYMMDD format (required for export mode)')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive mode to analyze existing data')
    
    args = parser.parse_args()
    
    # Check for interactive mode
    if args.interactive:
        interactive_summary_analysis()
        return
    
    # Regular export mode requires --date
    if not args.date:
        print("❌ Error: --date is required for export mode. Use --interactive for analysis mode.")
        sys.exit(1)
    
    # Validate date format
    try:
        start_date_dt = datetime.strptime(args.date, '%Y%m%d')
    except ValueError:
        print("❌ Error: Date must be in YYYYMMDD format")
        sys.exit(1)
    
    # Calculate date range (last 30 days ending on specified date)
    end_date_dt = start_date_dt
    start_date_dt = start_date_dt - timedelta(days=30)
    start_date = start_date_dt.strftime('%Y%m%d')
    end_date = args.date
    
    print(f"🚀 Starting Facebook Ad Export")
    print(f"📅 Date range: {start_date} to {end_date} (30 days ending on {args.date})")
    print(f"🔄 Using LastUpdated query with StartDate/EndDate filtering")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = SimpleLogger(__name__)
    
    try:
        # Initialize storage and repository
        print("🔧 Initializing storage and repository...")
        
        # Create simple config object
        class SimpleConfig:
            dynamodb_max_retries = 5
            dynamodb_base_delay = 1.0
            dynamodb_max_delay = 30.0
        
        config = SimpleConfig()
        
        # Use context manager for storage
        async with AsyncDynamoDBStorage(config, logger) as storage:
            repository = FBArchiveRepository(storage, logger)
            
            # Query ads by LastUpdated with date filtering
            ads = await query_ads_by_last_updated_and_date_range(repository, start_date, end_date)
            
            if not ads:
                print("⚠️  No ads found in the specified date range")
                return
            
            # Process data
            processed_ads, sorted_summaries, law_firm_analysis = process_ads_data(ads)
            
            if not processed_ads:
                print("⚠️  No ads with complete data found")
                return
            
            # Save results (now returns 4 files)
            ads_path, summaries_path, law_firm_path, summary_freq_path = save_results(processed_ads, sorted_summaries, law_firm_analysis, args.date)
            
            # Display rich law firm analysis
            if law_firm_analysis:
                display_law_firm_analysis(law_firm_analysis, args.date)
            
            # Print enhanced summary stats
            print(f"\n📊 Export Summary:")
            print(f"  🎯 Total ads exported: {len(processed_ads)}")
            print(f"  📈 Unique summaries: {len(sorted_summaries)}")
            if sorted_summaries:
                print(f"  🥇 Most common summary: '{sorted_summaries[0][0]}' ({sorted_summaries[0][1]} times)")
            
            # Print law firm analysis summary
            if law_firm_analysis:
                total_summaries_with_law_firms = sum(1 for data in law_firm_analysis.values() if data['law_firm_count'] > 0)
                print(f"  🏢 Summaries with law firm data: {total_summaries_with_law_firms}")
                
                # Find summary with most law firms
                max_law_firm_summary = max(law_firm_analysis.items(), 
                                         key=lambda x: x[1]['law_firm_count'], 
                                         default=(None, {'law_firm_count': 0}))
                if max_law_firm_summary[0]:
                    print(f"  🏛️  Most competitive summary: '{max_law_firm_summary[0]}' ({max_law_firm_summary[1]['law_firm_count']} law firms)")
            
            # Ask if user wants to enter interactive mode
            print(f"\n🎮 Would you like to enter interactive mode to explore the summary data?")
            response = input("Enter 'y' or 'yes' to continue to interactive mode (or any other key to exit): ").lower().strip()
            
            if response in ['y', 'yes']:
                print(f"\n🚀 Entering interactive mode...")
                interactive_summary_analysis()
        
    except Exception as e:
        print(f"❌ Error during export: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())