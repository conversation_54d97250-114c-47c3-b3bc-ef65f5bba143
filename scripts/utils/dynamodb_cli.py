#!/usr/bin/env python3
# !/usr/bin/env python3
# Run from project root directory.
"""
dynamodb_cli.py - Manages synchronization between AWS and local DynamoDB.
                  Provides Full, Incremental, and Missing sync options with parallel processing.
                  
                  Features AWS data caching optimization: When multiple sync operations share
                  the same AWS source table (e.g., option 3: Pacer + PacerDockets), the AWS data
                  is scanned only once and cached for reuse, significantly improving performance.
"""
import asyncio
import contextlib
import logging
import os
import sys
import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

from boto3.dynamodb.conditions import Key
from tqdm import tqdm

# Botocore exceptions for specific handling

# Rich imports
try:
    from rich.console import Console
    from rich.logging import RichHandler
    from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, SpinnerColumn
    from rich.table import Table  # Import Table for displaying counts

    rich_available = True
except ImportError:
    rich_available = False


    # Define dummy classes/functions if rich is not available
    class Console:
        def print(self, *args, **kwargs): print(*args)

        def input(self, *args, **kwargs): return input(*args)


    class Progress:
        def __init__(self, *args, **kwargs): pass

        def __enter__(self): return self

        def __exit__(self, exc_type, exc_val, exc_tb): pass

        def add_task(self, *args, **kwargs): return 0

        def update(self, *args, **kwargs): pass

        def stop(self): pass


    class RichHandler(logging.StreamHandler):
        pass  # Basic fallback


    class Table:
        def __init__(self, *args, **kwargs): self._rows = []; self._title = kwargs.get('title', '')

        def add_column(self, *args, **kwargs): pass

        def add_row(self, *args, **kwargs): self._rows.append(args)

        def __str__(self):  # Basic string representation for fallback
            header = f"--- {self._title} ---"
            lines = [header] + [" | ".join(map(str, row)) for row in self._rows]
            return "\n".join(lines)


    # Columns are not strictly needed for fallback
    class BarColumn:
        pass


    class TextColumn:
        pass


    class TimeElapsedColumn:
        pass


    class SpinnerColumn:
        pass

# Configure Logging (using RichHandler if available)
log_handlers: List[logging.Handler]
if rich_available:
    log_handlers = [RichHandler(rich_tracebacks=True, markup=True, console=Console())]  # Enable markup for styling
else:
    log_handlers = [logging.StreamHandler(sys.stdout)]  # Basic fallback

logging.basicConfig(
    level=logging.INFO,  # Default level for CLI output
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s" if not rich_available else "%(message)s",
    datefmt="[%X]",
    handlers=log_handlers
)
cli_logger = logging.getLogger("dynamodb_cli")
console = Console()  # For rich printing (will be dummy if rich not installed)

# --- Relative Imports ---
try:
    # Set up PROJECT_ROOT first
    from pathlib import Path
    PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
    
    # Add project root to path if needed
    if str(PROJECT_ROOT) not in sys.path:
        sys.path.insert(0, str(PROJECT_ROOT))
    
    # Import base manager and serializer from new infrastructure
    from src.infrastructure.storage.dynamodb_legacy_adapter import DynamoDbBaseManager, json_decimal_serializer
    
    # Use new async repositories directly
    from src.repositories.pacer_repository import PacerRepository
    from src.repositories.fb_archive_repository import FBArchiveRepository
    _using_new_architecture = True
except ImportError:
    cli_logger.error("Failed relative imports. Ensure script is run correctly relative to module structure.",
                     exc_info=True)
    # Attempt absolute imports as fallback (adjust paths if necessary)
    try:
        # Adjust these based on your actual structure if running directly
        script_dir = os.path.dirname(os.path.abspath(__file__))
        PROJECT_ROOT = os.path.dirname(os.path.dirname(script_dir))  # Go up two levels from scripts/utils
        sys.path.insert(0, PROJECT_ROOT)
        
        from src.infrastructure.storage.dynamodb_legacy_adapter import DynamoDbBaseManager, json_decimal_serializer
        from src.repositories.pacer_repository import PacerRepository
        from src.repositories.fb_archive_repository import FBArchiveRepository
        # Create compatibility aliases for old manager names
        PacerManager = PacerRepository
        PacerDocketsManager = PacerRepository  # Both use same repository
        FBAdArchiveManager = FBArchiveRepository
    except ImportError:
        cli_logger.critical("Could not resolve imports. Exiting.")
        sys.exit(1)



async def get_local_table_counts(config: Dict[str, Any], display_name: str, manager_class: type) -> Dict[str, Any]:
    """Helper function to get item count for a single local table."""
    try:
        if hasattr(manager_class, 'count_table_items'):
            # Legacy sync manager
            manager = manager_class(config=config, use_local=True)
            count = manager.count_table_items()
        else:
            # New async repository - use boto3 client directly for counting (simpler than aioboto3)
            import boto3
            
            cli_logger.debug(f"Creating boto3 DynamoDB client for local endpoint")
            
            # Use boto3 client for simpler connection
            local_endpoint = os.getenv('LOCAL_DYNAMODB_ENDPOINT_URL', 'http://localhost:8000')
            dynamodb_client = boto3.client(
                'dynamodb',
                region_name='us-west-2',
                endpoint_url=local_endpoint,
                aws_access_key_id='dummy',
                aws_secret_access_key='dummy'
            )
            
            cli_logger.debug(f"Attempting to scan table '{display_name}' on local DynamoDB")
            
            # Count all items by scanning the entire table
            count = 0
            last_evaluated_key = None
            
            while True:
                scan_params = {
                    'TableName': display_name,
                    'Select': 'COUNT'
                }
                if last_evaluated_key:
                    scan_params['ExclusiveStartKey'] = last_evaluated_key
                
                try:
                    response = dynamodb_client.scan(**scan_params)
                    count += response.get('Count', 0)
                    
                    last_evaluated_key = response.get('LastEvaluatedKey')
                    if not last_evaluated_key:
                        break
                except Exception as scan_error:
                    cli_logger.error(f"Failed to scan table '{display_name}': {scan_error}")
                    raise
            
            cli_logger.debug(f"Successfully scanned table '{display_name}', found {count} items")
        return {"table": display_name, "local": count if count != -1 else "[red]Error[/red]"}
    except Exception as e:
        cli_logger.error(f"Error counting items in local table '{display_name}': {e}", exc_info=True)
        return {"table": display_name, "local": "[red]Error[/red]"}


def display_counts(counts: List[Dict[str, Any]]) -> None:
    """Display the item counts for all tables in a formatted table.
    
    Args:
        counts: List of dictionaries containing table names and their item counts
    """
    if not counts:
        console.print("[yellow]No table counts to display.[/yellow]")
        return
        
    # Create a table with columns
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Table", style="cyan", no_wrap=True)
    table.add_column("Item Count", justify="right")
    
    # Add rows for each table
    for count_info in counts:
        table_name = count_info["table"]
        count = count_info["local"]
        
        # Format the count with thousands separators if it's a number
        if isinstance(count, int):
            count_str = f"{count:,}"
        else:
            count_str = str(count)
            
        table.add_row(table_name, count_str)
    
    # Print the table
    console.print("\n[bold]Current Item Counts:[/bold]")
    console.print(table)

def get_config(end_date: Optional[str] = None) -> Dict[str, Any]:
    if not end_date:
        end_date = datetime.now().strftime("%Y%m%d")
        cli_logger.debug(f"End date default: {end_date}")
    try:
        # Load environment variables if not already loaded
        from dotenv import load_dotenv
        load_dotenv()
        
        # Use the new config system
        from src.config_models.loader import load_storage_config
        storage_config = load_storage_config()
        
        # Parse the date for iso_date format
        if end_date:
            try:
                # Try YYYYMMDD format first (database format)
                date_obj = datetime.strptime(end_date, "%Y%m%d")
                iso_date = date_obj.strftime("%Y%m%d")
                date_str = date_obj.strftime("%m/%d/%y")
            except ValueError:
                try:
                    # Fallback to MM/DD/YYYY format for backwards compatibility
                    date_obj = datetime.strptime(end_date, "%m/%d/%Y")
                    iso_date = date_obj.strftime("%Y%m%d")
                    date_str = date_obj.strftime("%m/%d/%y")
                except ValueError:
                    cli_logger.warning(f"Invalid date format: {end_date}, using today")
                    date_obj = datetime.now()
                    iso_date = date_obj.strftime("%Y%m%d")
                    date_str = date_obj.strftime("%m/%d/%y")
        else:
            date_obj = datetime.now()
            iso_date = date_obj.strftime("%Y%m%d")
            date_str = date_obj.strftime("%m/%d/%y")

        # Create combined config with new AWS credentials format
        # Fall back to environment variables if storage config doesn't have them
        config = {
            'aws_access_key': storage_config.aws_access_key_id or os.getenv('AWS_ACCESS_KEY'),
            'aws_secret_key': storage_config.aws_secret_access_key or os.getenv('AWS_SECRET_KEY'),
            'region_name': storage_config.aws_region or os.getenv('AWS_REGION', 'us-west-2'),
            'date': date_str,
            'iso_date': iso_date,
        }

        cli_logger.info(f"Config loaded for date: {config.get('date', 'unknown')} (Effective End Date: {end_date})")
        if not config.get('aws_access_key') or not config.get('aws_secret_key'):
            raise ValueError("AWS credentials not found in config.")
        if not config.get('region_name'):
            config['region_name'] = 'us-west-2'
            cli_logger.warning("AWS region defaulted to us-west-2.")
        return config
    except FileNotFoundError:
        cli_logger.error("[bold red]Config file (.env) not found.[/bold red]")
        sys.exit(1)
    except ValueError as ve:
        cli_logger.error(f"[bold red]{ve}[/bold red]")
        sys.exit(1)
    except TypeError as te:
        cli_logger.error(f"[bold red]Config loading type error: {te}[/bold red]", exc_info=True)
        sys.exit(1)
    except Exception as e:
        cli_logger.error(f"[bold red]Error loading config: {e}[/bold red]", exc_info=True)
        sys.exit(1)


class DynamoDBIncrementalSync:
    """
    Manages synchronization between AWS and local DynamoDB tables.
    Handles Full, Incremental, and Missing sync modes.
    Can sync between different table types/managers for AWS source and Local target.
    """

    def __init__(self, config: Dict[str, Any],
                 aws_source_table_name: str,
                 AwsSourceManagerClass: type,
                 local_target_table_name: str,
                 LocalTargetManagerClass: type,
                 console: Console,  # Add console for progress bars
                 aws_data_cache: Optional[Dict[str, Any]] = None):
        self.config = config
        self.aws_table_name = aws_source_table_name  # AWS source table
        self.AwsManagerClass = AwsSourceManagerClass  # Manager for AWS source
        self.local_table_name = local_target_table_name  # Local target table
        self.LocalManagerClass = LocalTargetManagerClass  # Manager for Local target
        self.console = console  # Store console instance
        self.aws_data_cache = aws_data_cache or {}  # Cache for AWS data

        self.logger = logging.getLogger(
            f"DynamoDBSync[{self.aws_table_name}(AWS)->{self.local_table_name}(Local)]")

        self.aws_manager: Optional[Any] = None
        self.local_manager: Optional[Any] = None

        self._initialize_local_manager()  # Initialize local first
        self.logger.debug("AWS Manager initialization deferred.")

        # Sync fields are determined by the AWS SOURCE table and its manager
        self.timestamp_field, self.timestamp_index = self._get_sync_fields(
            self.AwsManagerClass, self.aws_table_name
        )
        self.max_workers = min(os.cpu_count() * 4, 32)
        self.aws_read_batch_size = 100
        self.local_write_batch_size = 25
        self.key_names: List[str] = []  # This will be based on AWS source for "missing" sync

    def _initialize_local_manager(self):
        """Initializes the Local manager for the target local table."""
        self.logger.debug(
            f"Initializing Local manager using {self.LocalManagerClass.__name__} for local table '{self.local_table_name}'.")
        try:
            # Check if this is a new repository (takes storage) or legacy manager (takes config)
            if hasattr(self.LocalManagerClass, 'count_table_items'):
                # Legacy sync manager
                self.local_manager = self.LocalManagerClass(config=self.config, use_local=True)
            else:
                # New async repository - create storage instance
                from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
                from src.config_models.loader import load_storage_config

                storage_config = load_storage_config()
                local_endpoint = os.getenv('LOCAL_DYNAMODB_ENDPOINT_URL', 'http://localhost:8000')
                storage_config_dict = {
                    'aws_access_key_id': 'dummy',  # Required for local DynamoDB
                    'aws_secret_access_key': 'dummy',  # Required for local DynamoDB
                    'aws_region': storage_config.aws_region,
                    'dynamodb_endpoint': local_endpoint,  # Use LOCAL_DYNAMODB_ENDPOINT_URL
                    'dynamodb_max_retries': 15,
                    'dynamodb_base_delay': 1.0,
                    'dynamodb_max_delay': 300.0
                }

                # Create a simple config object with attributes
                class SimpleConfig:
                    def __init__(self, config_dict):
                        for key, value in config_dict.items():
                            setattr(self, key, value)

                config_obj = SimpleConfig(storage_config_dict)

                # Create storage and repository
                storage = AsyncDynamoDBStorage(config_obj, self.logger)
                self.local_manager = self.LocalManagerClass(storage=storage)
                self.local_storage = storage  # Keep reference for cleanup

            # For repositories, we don't check table names since they're hardcoded in the repository
            self.logger.info(f"Local manager for target table '{self.local_table_name}' initialized.")
        except Exception as e:
            self.logger.critical(f"Failed to initialize Local manager for target table '{self.local_table_name}': {e}",
                                 exc_info=True)
            raise

    def _ensure_aws_manager(self):
        """Initializes the AWS manager for the source AWS table."""
        if self.aws_manager is None:
            self.logger.debug(
                f"Initializing AWS manager using {self.AwsManagerClass.__name__} for source AWS table '{self.aws_table_name}'.")
            try:
                # Check if this is a new repository (takes storage) or legacy manager (takes config)
                if hasattr(self.AwsManagerClass, 'count_table_items'):
                    # Legacy sync manager
                    self.aws_manager = self.AwsManagerClass(config=self.config, use_local=False)
                else:
                    # New async repository - create storage instance
                    from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
                    from src.config_models.loader import load_storage_config

                    storage_config = load_storage_config()
                    storage_config_dict = {
                        'aws_access_key_id': storage_config.aws_access_key_id,
                        'aws_secret_access_key': storage_config.aws_secret_access_key,
                        'aws_region': storage_config.aws_region,
                        'dynamodb_endpoint': None,  # Use AWS for source
                        'dynamodb_max_retries': 15,
                        'dynamodb_base_delay': 1.0,
                        'dynamodb_max_delay': 300.0
                    }

                    # Create a simple config object with attributes
                    class SimpleConfig:
                        def __init__(self, config_dict):
                            for key, value in config_dict.items():
                                setattr(self, key, value)

                    config_obj = SimpleConfig(storage_config_dict)

                    # Create storage and repository
                    storage = AsyncDynamoDBStorage(config_obj, self.logger)
                    self.aws_manager = self.AwsManagerClass(storage=storage)
                    self.aws_storage = storage  # Keep reference for cleanup

                self.logger.info(f"AWS manager for source table '{self.aws_table_name}' initialized.")
            except Exception as e:
                self.logger.critical(f"Failed to initialize AWS manager for source table '{self.aws_table_name}': {e}",
                                     exc_info=True)
                raise
        else:
            self.logger.debug(f"AWS manager for source table '{self.aws_table_name}' already initialized.")

    def _get_sync_fields(self, SourceManagerClass: type, source_table_name: str) -> Tuple[str, Optional[str]]:
        """Determines timestamp field and index FOR THE AWS SOURCE TABLE for incremental sync."""
        manager_class_name = SourceManagerClass.__name__ if SourceManagerClass else "None"
        self.logger.debug(f"Getting sync fields for AWS source: {manager_class_name} / Table: {source_table_name}")

        if manager_class_name in ["PacerManager", "PacerRepository"]:
            return "AddedOn", "AddedOn-index"
        elif manager_class_name in ["PacerDocketsManager", "PacerDocketsRepository"]:
            # This case would apply if AWS PacerDockets was the source
            return "AddedOn", "AddedOn-index"
        elif manager_class_name in ["FBAdArchiveManager", "FBArchiveRepository"]:
            return "LastUpdated", "LastUpdated-index"
        elif source_table_name == "DocketActivity":
            return "FilingDate", None
        else:
            # Fallback logic (can be expanded)
            if hasattr(SourceManagerClass, 'DEFAULT_TIMESTAMP_FIELD') and hasattr(SourceManagerClass,
                                                                                  'DEFAULT_TIMESTAMP_INDEX'):
                self.logger.warning(
                    f"Using default sync fields from {SourceManagerClass.__name__} for AWS source table '{source_table_name}'")
                return SourceManagerClass.DEFAULT_TIMESTAMP_FIELD, SourceManagerClass.DEFAULT_TIMESTAMP_INDEX  # type: ignore

            # More robust fallback for Pacer-like tables if specifically used as a generic source
            if source_table_name.startswith("Pacer"):  # General Pacer source
                # Check for 'AddedOn' as a common pattern for Pacer tables
                base_attr_config = getattr(DynamoDbBaseManager, 'attribute_config', {})
                table_attrs = base_attr_config.get(source_table_name, {}).get('include', [])
                if 'AddedOn' in table_attrs:
                    key_conf = getattr(DynamoDbBaseManager, 'key_config', {}).get(source_table_name, {})
                    gsi_index_name = next(
                        (gsi.get('name') for gsi in key_conf.get('gsis', []) if gsi.get('hash_key') == 'AddedOn'), None)
                    if gsi_index_name:
                        self.logger.info(
                            f"Guessed 'AddedOn' and GSI '{gsi_index_name}' for source '{source_table_name}'.")
                        return "AddedOn", gsi_index_name
                    else:
                        self.logger.warning(
                            f"Guessed 'AddedOn' for source '{source_table_name}', but no matching GSI found.")
                        return "AddedOn", None

            raise ValueError(
                f"Incremental sync configuration (timestamp_field, timestamp_index) missing or ambiguous for AWS source table '{source_table_name}' / Manager '{manager_class_name}'. Define explicitly or update _get_sync_fields.")

    async def _perform_full_sync(self) -> None:
        """Performs a full sync by copying all items from AWS to local."""
        # Check cache first
        cache_key = f"full_{self.aws_table_name}"
        if cache_key in self.aws_data_cache:
            aws_items = self.aws_data_cache[cache_key]
            self.logger.info(f"Using cached AWS data for {self.aws_table_name} ({len(aws_items)} items)")
        else:
            self._ensure_aws_manager()
            if not self.aws_manager:
                raise RuntimeError("AWS Manager not initialized")

            # Get all items from AWS with a spinner
            try:
                with self.console.status(
                        f"[bold green]Scanning all items from AWS table '{self.aws_table_name}'... (this may take a while)"):
                    if hasattr(self.aws_manager, 'scan_table'):
                        # Legacy sync manager
                        scan_response = self.aws_manager.scan_table()
                        aws_items = list(scan_response) if scan_response else []
                    else:
                        # New async repository - use storage context
                        async with self.aws_storage:
                            aws_items = await self.aws_manager.scan_all()
                self.console.print(f"✓ Retrieved {len(aws_items)} items from AWS.")

                # Cache the data for reuse
                self.aws_data_cache[cache_key] = aws_items

            except Exception as e:
                self.logger.error(f"Error scanning AWS table: {str(e)}", exc_info=True)
                raise

        if not self.local_manager:
            raise RuntimeError("Local Manager not initialized")

        if not aws_items:
            self.logger.warning("No items found in AWS table to write.")
            return

        # Write all items to local with a progress bar
        total_items = len(aws_items)
        success_count, failed_count = 0, 0

        try:
            if hasattr(self.local_manager, 'batch_insert_items'):
                # Legacy sync manager - progress bar is harder here, so we keep old behavior
                self.logger.info(
                    f"Starting to write {total_items} items to local table {self.local_table_name} using legacy batch writer.")
                success_count, failed_count = self.local_manager.batch_insert_items(
                    records=aws_items,
                    batch_size=self.local_write_batch_size,
                    disable_progress=True
                )
            else:
                # New async repository - use a progress bar
                with Progress(
                        SpinnerColumn(),
                        TextColumn("[progress.description]{task.description}"),
                        BarColumn(),
                        TextColumn("[bold green]{task.completed}/{task.total}"),
                        TimeElapsedColumn(),
                        console=self.console,
                        transient=True
                ) as progress:
                    write_task = progress.add_task(
                        f"Writing items to local table [cyan]'{self.local_table_name}'[/cyan]", total=total_items)
                    async with self.local_storage:
                        for item in aws_items:
                            try:
                                await self.local_manager.add_or_update_record(item)
                                success_count += 1
                            except Exception as e:
                                self.logger.error(f"Failed to write item: {e}")
                                failed_count += 1
                            progress.update(write_task, advance=1)

            self.console.print(f"✓ Full sync completed. Success: {success_count}, Failed: {failed_count}")

        except Exception as e:
            self.logger.error(f"Error during full sync write operation: {str(e)}", exc_info=True)
            raise

    async def _perform_incremental_sync(self) -> None:
        """Performs an incremental sync by copying only new/updated items."""
        if not self.local_manager:
            raise RuntimeError("Local Manager not initialized")

        self.logger.info(
            f"Starting INCREMENTAL sync using field: '{self.timestamp_field}' on index '{self.timestamp_index}'")

        try:
            latest_local_timestamp_value = None
            days_to_check = 366

            with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    console=self.console,
                    transient=True
            ) as progress:
                search_task = progress.add_task("Searching for latest local data...", total=days_to_check)

                for i in range(days_to_check):
                    date_to_check = datetime.now() - timedelta(days=i)
                    date_to_check_str = date_to_check.strftime('%Y%m%d')
                    progress.update(search_task, advance=1,
                                    description=f"Checking local data for [magenta]{date_to_check_str}[/magenta]")

                    items_on_this_day = []
                    try:
                        # Refactored to use a direct, efficient query on the GSI
                        async with self.local_storage:
                            query_params = {
                                'table_name': self.local_table_name,
                                'key_condition': Key(self.timestamp_field).eq(date_to_check_str),
                                'index_name': self.timestamp_index,
                            }
                            # The query method fetches all pages, but we only need to know if it's non-empty.
                            items_on_this_day = await self.local_storage.query(**query_params)

                    except Exception as query_error:
                        self.logger.warning(f"Failed to query local data for date {date_to_check_str}: {query_error}")

                    if items_on_this_day:
                        latest_local_timestamp_value = date_to_check_str
                        progress.update(search_task, completed=days_to_check)  # Finish the bar
                        break

            if latest_local_timestamp_value:
                self.console.print(
                    f"✓ Found latest local data on: [bold magenta]{latest_local_timestamp_value}[/bold magenta]")
            else:
                self.logger.warning(
                    f"No local data found in the last {days_to_check} days. Falling back to a full sync.")
                await self._perform_full_sync()
                return

            # Query AWS for items from latest_local_timestamp_value up to now.
            cache_key = f"incremental_{self.aws_table_name}_{latest_local_timestamp_value}"
            if cache_key in self.aws_data_cache:
                aws_items_to_sync = self.aws_data_cache[cache_key]
                self.console.print(f"✓ Using cached AWS incremental data ({len(aws_items_to_sync)} items).")
            else:
                self._ensure_aws_manager()
                if not self.aws_manager:
                    raise RuntimeError("AWS Manager not initialized")

                aws_items_to_sync = []
                start_date = datetime.strptime(latest_local_timestamp_value, '%Y%m%d')
                end_date = datetime.now()

                with Progress(
                        SpinnerColumn(),
                        TextColumn("[progress.description]{task.description}"),
                        BarColumn(),
                        console=self.console,
                        transient=True
                ) as progress:
                    num_days = (end_date.date() - start_date.date()).days + 1
                    query_task = progress.add_task("Fetching new/updated items from AWS...", total=num_days)

                    date_iterator = start_date
                    while date_iterator.date() <= end_date.date():
                        date_str = date_iterator.strftime('%Y%m%d')
                        progress.update(query_task, advance=1,
                                        description=f"Querying AWS for [magenta]{date_str}[/magenta]")

                        try:
                            # Use direct, efficient query
                            async with self.aws_storage:
                                daily_items = await self.aws_storage.query(
                                    table_name=self.aws_table_name,
                                    key_condition=Key(self.timestamp_field).eq(date_str),
                                    index_name=self.timestamp_index
                                )
                                if daily_items:
                                    aws_items_to_sync.extend(daily_items)
                        except Exception as e:
                            self.logger.warning(f"Failed to query AWS for date {date_str}: {e}")

                        date_iterator += timedelta(days=1)

                self.aws_data_cache[cache_key] = aws_items_to_sync
                self.console.print(f"✓ Found {len(aws_items_to_sync)} new/updated items in AWS to sync.")

            if not aws_items_to_sync:
                self.logger.info("Local database is already up-to-date.")
                return

            # Write new/updated items to local with progress bar
            total_to_write = len(aws_items_to_sync)
            success_count, failed_count = 0, 0
            if hasattr(self.local_manager, 'batch_insert_items'):
                # Legacy sync manager
                success_count, failed_count = self.local_manager.batch_insert_items(records=aws_items_to_sync,
                                                                                    batch_size=self.local_write_batch_size,
                                                                                    disable_progress=True)
            else:
                with Progress(
                        SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(),
                        TextColumn("[bold green]{task.completed}/{task.total}"), TimeElapsedColumn(),
                        console=self.console, transient=True
                ) as progress:
                    write_task = progress.add_task(
                        f"Writing new items to local table [cyan]'{self.local_table_name}'[/cyan]",
                        total=total_to_write)
                    async with self.local_storage:
                        for item in aws_items_to_sync:
                            try:
                                await self.local_manager.add_or_update_record(item)
                                success_count += 1
                            except Exception as e:
                                self.logger.error(f"Failed to write item: {e}")
                                failed_count += 1
                            progress.update(write_task, advance=1)

            self.console.print(f"✓ Incremental sync completed. Success: {success_count}, Failed: {failed_count}")

        except Exception as e:
            self.logger.error(f"Error during incremental sync: {str(e)}", exc_info=True)
            raise

    async def _perform_missing_sync(self) -> None:
        """Performs a sync of only items that exist in AWS but not in local."""
        if not self.local_manager:
            raise RuntimeError("Local Manager not initialized")

        self.logger.info("Starting MISSING ITEMS sync")

        try:
            # Step 1: Get AWS keys
            with self.console.status(f"[bold green]Scanning keys from AWS table '{self.aws_table_name}'..."):
                if hasattr(self.aws_manager, 'get_key_attributes'):  # Ideal method
                    key_attr_names = self.aws_manager.get_key_attributes()
                else:  # Fallback for new repositories
                    key_config = self.AwsManagerClass.DEFAULT_TABLE_NAME
                    if self.aws_table_name == 'Pacer' or self.aws_table_name == 'PacerDockets':
                        key_attr_names = ['FilingDate', 'DocketNum']
                    elif self.aws_table_name == 'FBAdArchive':
                        key_attr_names = ['AdArchiveID', 'StartDate']
                    else:
                        raise ValueError(f"Cannot determine key attributes for {self.aws_table_name}")

                async with self.aws_storage:
                    # In a real-world scenario with very large tables, you'd want a projection scan.
                    # For simplicity, we scan all and project in memory.
                    aws_items = await self.aws_manager.scan_all()

                aws_keys = {tuple(item[attr] for attr in key_attr_names) for item in aws_items}
            self.console.print(f"✓ Found {len(aws_keys)} items in AWS.")

            # Step 2: Get Local keys
            with self.console.status(f"[bold green]Scanning keys from local table '{self.local_table_name}'..."):
                async with self.local_storage:
                    local_items = await self.local_manager.scan_all()
                local_keys = {tuple(item.get(attr) for attr in key_attr_names) for item in local_items}
            self.console.print(f"✓ Found {len(local_keys)} items in local.")

            # Step 3: Find missing keys
            missing_key_tuples = aws_keys - local_keys

            if not missing_key_tuples:
                self.console.print("✓ No missing items found. Local database is in sync.")
                return

            self.console.print(f"Found {len(missing_key_tuples)} missing items to sync.")

            # Step 4: Map full items to missing keys
            with self.console.status("[bold green]Extracting full data for missing items..."):
                aws_item_map = {tuple(item[attr] for attr in key_attr_names): item for item in aws_items}
                missing_items = [aws_item_map[key_tuple] for key_tuple in missing_key_tuples if
                                 key_tuple in aws_item_map]

            # Step 5: Write missing items to local with progress bar
            total_to_write = len(missing_items)
            success_count, failed_count = 0, 0
            if hasattr(self.local_manager, 'batch_insert_items'):
                # Legacy sync manager
                success_count, failed_count = self.local_manager.batch_insert_items(records=missing_items,
                                                                                    batch_size=self.local_write_batch_size,
                                                                                    disable_progress=True)
            else:
                with Progress(
                        SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(),
                        TextColumn("[bold green]{task.completed}/{task.total}"), TimeElapsedColumn(),
                        console=self.console, transient=True
                ) as progress:
                    write_task = progress.add_task(
                        f"Writing missing items to local table [cyan]'{self.local_table_name}'[/cyan]",
                        total=total_to_write)
                    async with self.local_storage:
                        for item in missing_items:
                            try:
                                await self.local_manager.add_or_update_record(item)
                                success_count += 1
                            except Exception as e:
                                self.logger.error(f"Failed to write item: {e}")
                                failed_count += 1
                            progress.update(write_task, advance=1)

            self.console.print(f"✓ Missing items sync completed. Success: {success_count}, Failed: {failed_count}")

        except Exception as e:
            self.logger.error(f"Error during missing items sync: {str(e)}", exc_info=True)
            raise


async def main():
    console.print("\n[bold blue]DynamoDB AWS <> Local Synchronization Tool[/bold blue]")
    console.print("========================================")

    try:
        # --- Configuration ---
        end_date_override = sys.argv[1] if len(sys.argv) > 1 else None
        if end_date_override:
            try:
                datetime.strptime(end_date_override, "%m/%d/%Y")
                console.print(f"Using command-line end date: {end_date_override}")
            except ValueError:
                console.print(f"[yellow]Invalid date format '{end_date_override}'. Using default (today).[/yellow]")
                end_date_override = None
        config = get_config(end_date_override)
        manager_config = {
            'aws_access_key': config.get('aws_access_key'),
            'aws_secret_key': config.get('aws_secret_key'),
            'iso_date': config.get('iso_date', config.get('date')),
            'region_name': config.get('region_name', 'us-west-2'),
            'date': config.get('date'),
        }
        manager_config = {k: v for k, v in manager_config.items() if v is not None}

        # Cache for AWS data to avoid duplicate scans
        aws_data_cache = {}

        # --- Define Tables and Managers ---
        # Format: display_name, DefaultLocalTargetManagerClass, DefaultAwsSourceTableName, DefaultAwsSourceManagerClass
        table_map = {
            "1": ("Pacer", PacerRepository, "Pacer", PacerRepository),
            "2": ("PacerDockets", PacerRepository, "PacerDockets", PacerRepository),
            # Assumes AWS PacerDockets for standalone sync
            "3": ("Pacer + PacerDockets (AWS Pacer -> Local Pacer & Local PacerDockets)", None, None, None),
            # Special combined option
            "4": ("FBAdArchive", FBArchiveRepository, "FBAdArchive", FBArchiveRepository)
        }

        tables_for_count_display = [
            ("Pacer", PacerRepository),
            ("PacerDockets", PacerRepository),
            ("FBAdArchive", FBArchiveRepository)
        ]

        console.print("\n[bold]Fetching initial local table counts...[/bold]")
        all_counts = []

        # Use async execution for table counts with proper timeout handling
        for display_name, m_class in tables_for_count_display:
            try:
                # Temporarily enable DEBUG logging for connection diagnostics
                original_level = cli_logger.level
                cli_logger.setLevel(logging.DEBUG)

                result = await asyncio.wait_for(
                    get_local_table_counts(manager_config, display_name, m_class),
                    timeout=10.0  # 10 second timeout per table
                )
                all_counts.append(result)

                # Restore original logging level
                cli_logger.setLevel(original_level)
            except asyncio.TimeoutError:
                cli_logger.setLevel(original_level)
                cli_logger.warning(
                    f"Timeout getting local counts for '{display_name}' (likely DynamoDB Local not running)")
                all_counts.append({"table": display_name, "local": "[yellow]Timeout[/yellow]"})
            except Exception as e:
                cli_logger.setLevel(original_level)
                cli_logger.error(f"Error getting local counts for '{display_name}': {e}")
                all_counts.append({"table": display_name, "local": "[red]Error[/red]"})

        all_counts.sort(key=lambda x: x["table"])
        display_counts(all_counts)

        console.print("\n[bold]Available Tables for Sync:[/bold]")
        for key, (name, _, _, _) in table_map.items():
            console.print(f"  {key}. {name}")
        console.print("  q. Quit")

        # Stores tuples of: (LocalTargetTableName, LocalTargetManagerClass, AwsSourceTableName, AwsSourceManagerClass)
        selected_sync_configurations: List[Tuple[str, type, str, type]] = []

        table_choice_input = console.input("\nSelect table number to sync (or 'q' to quit): ").strip()
        if table_choice_input == "3":
            # Config for AWS Pacer -> Local Pacer
            selected_sync_configurations.append(("Pacer", PacerRepository, "Pacer", PacerRepository))
            # Config for AWS Pacer -> Local PacerDockets (both use same repository)
            selected_sync_configurations.append(("PacerDockets", PacerRepository, "Pacer", PacerRepository))
            console.print(
                f"Selected: Combined sync. Target 1: Local [cyan]Pacer[/cyan] from AWS [cyan]Pacer[/cyan]. Target 2: Local [cyan]PacerDockets[/cyan] from AWS [cyan]Pacer[/cyan].")
        elif table_choice_input.lower() == 'q':
            console.print("\n[yellow]Exiting DynamoDB sync tool.[/yellow]")
            return
        elif table_choice_input in table_map:
            _, local_mgr_cls, aws_tbl_name, aws_mgr_cls = table_map[table_choice_input]
            # For single table syncs, the local target table name is the same as the AWS source table name by default.
            # The display name is used as the local target table name.
            local_target_table_name_for_single_sync = table_map[table_choice_input][0]

            if local_mgr_cls is None or aws_mgr_cls is None:  # Should not happen for non-combined valid options
                console.print("[red]Invalid table configuration in table_map. Exiting.[/red]")
                sys.exit(1)
            selected_sync_configurations.append(
                (local_target_table_name_for_single_sync, local_mgr_cls, aws_tbl_name, aws_mgr_cls))
            console.print(
                f"Selected: Local table [cyan]{local_target_table_name_for_single_sync}[/cyan] from AWS table [cyan]{aws_tbl_name}[/cyan].")
        else:
            console.print("[red]Invalid table selection. Exiting.[/red]")
            sys.exit(1)

        console.print("\n[bold]Sync Options:[/bold]")
        sync_map = {
            "1": ("full", "Full Download (Recreates Local Table)"),
            "2": ("incremental", "Incremental Update (Fetches New/Updated)"),
            "3": ("missing", "Missing Sync (Compares Keys, Fetches Missing)")
        }
        for key, (_, desc) in sync_map.items():
            console.print(f"  {key}. {desc}")
        console.print("  q. Quit")
        while True:
            sync_choice_input = console.input("\nSelect sync type number (or 'q' to quit): ").strip()
            if sync_choice_input.lower() == 'q':
                console.print("\n[yellow]Exiting DynamoDB sync tool.[/yellow]")
                return
            elif sync_choice_input in sync_map:
                sync_type_selected, sync_desc_selected = sync_map[sync_choice_input]
                break
            console.print("[red]Invalid selection. Please enter the number or 'q' to quit.[/red]")

        overall_sync_start_time = time.time()
        any_sync_failed = False

        # Check if multiple sync configs share the same AWS source
        aws_source_counts = {}
        for _, _, aws_src, _ in selected_sync_configurations:
            aws_source_counts[aws_src] = aws_source_counts.get(aws_src, 0) + 1

        # Display cache optimization notice if applicable
        for aws_src, count in aws_source_counts.items():
            if count > 1:
                console.print(
                    f"\n[green]✓ Cache optimization enabled:[/green] AWS table [cyan]{aws_src}[/cyan] will be scanned once and reused for {count} syncs")

        for config_index, (
                local_target_tbl_name, local_target_mgr_cls, aws_source_tbl_name, aws_source_mgr_cls) in enumerate(
            selected_sync_configurations):
            console.rule(
                f"[bold white]Sync Task {config_index + 1}/{len(selected_sync_configurations)}: Local Target='{local_target_tbl_name}', AWS Source='{aws_source_tbl_name}'[/bold white]",
                style="blue")
            console.print(
                f"\nInitializing sync for local table [cyan]'{local_target_tbl_name}'[/cyan] (Manager: {local_target_mgr_cls.__name__}) "
                f"from AWS table [cyan]'{aws_source_tbl_name}'[/cyan] (Manager: {aws_source_mgr_cls.__name__}) "
                f"using [magenta]{sync_desc_selected}[/magenta]...")

            updater_instance: Optional[DynamoDBIncrementalSync] = None
            try:
                updater_instance = DynamoDBIncrementalSync(
                    config=manager_config,
                    aws_source_table_name=aws_source_tbl_name,
                    AwsSourceManagerClass=aws_source_mgr_cls,
                    local_target_table_name=local_target_tbl_name,
                    LocalTargetManagerClass=local_target_mgr_cls,
                    console=console,  # Pass the console object
                    aws_data_cache=aws_data_cache  # Pass the shared cache
                )
                console.print("Sync manager initialized.")
            except Exception as init_e_loop:
                console.print(f"[bold red]Initialization failed for this sync task: {init_e_loop}[/bold red]")
                cli_logger.error(f"Initialization failed for sync task: {init_e_loop}", exc_info=True)
                any_sync_failed = True
                continue

            aws_display_name_loop = getattr(getattr(updater_instance, 'aws_manager', None), 'table_name',
                                            aws_source_tbl_name)
            local_display_name_loop = getattr(getattr(updater_instance, 'local_manager', None), 'table_name',
                                              local_target_tbl_name)

            console.print(
                f"\n[bold yellow]Action:[/bold yellow] Perform [magenta]{sync_type_selected}[/magenta] sync from AWS [cyan]'{aws_display_name_loop}'[/cyan] to Local [cyan]'{local_display_name_loop}'[/cyan].")

            if sync_type_selected == 'full':
                console.print(
                    f"[bold orange1]WARNING:[/bold orange1] Full sync will affect the local table "
                    f"'{local_display_name_loop}'. Depending on manager, it might be deleted and recreated or cleared.")

            proceed_sync = 'y'
            if config_index == 0:  # Only ask for confirmation for the first task or if only one task
                proceed_sync = console.input(
                    f"Proceed with [magenta]{sync_type_selected}[/magenta] sync for the selected task(s)? (y/N): ").strip().lower()
                if proceed_sync != 'y':
                    console.print("Operation cancelled by user. No sync tasks will run.")
                    any_sync_failed = True
                    break  # Exit the loop over sync tasks
            elif len(selected_sync_configurations) > 1:  # For subsequent tasks in a multi-task run
                console.print(
                    f"Proceeding with sync for Local: [cyan]{local_target_tbl_name}[/cyan] from AWS: [cyan]{aws_source_tbl_name}[/cyan]...")

            if proceed_sync == 'y':
                console.print(
                    f"\nStarting [magenta]{sync_type_selected}[/magenta] synchronization for Local: '{local_target_tbl_name}' from AWS: '{aws_source_tbl_name}'...")
                single_task_start_time = time.time()
                sync_success_current_task = False
                try:
                    if updater_instance:
                        if sync_type_selected == 'full':
                            await updater_instance._perform_full_sync()
                        elif sync_type_selected == 'incremental':
                            await updater_instance._perform_incremental_sync()
                        elif sync_type_selected == 'missing':
                            await updater_instance._perform_missing_sync()
                        sync_success_current_task = True
                    else:
                        cli_logger.error(f"Updater object was None for task. This should not happen if init succeeded.")
                        sync_success_current_task = False
                except Exception as sync_err_loop:
                    cli_logger.critical(
                        f"Sync operation failed for Local: '{local_target_tbl_name}' from AWS: '{aws_source_tbl_name}': {sync_err_loop}",
                        exc_info=True)
                    console.print(f"[bold red]Sync Error for this task:[/bold red] Check logs.")
                    sync_success_current_task = False

                if not sync_success_current_task:
                    any_sync_failed = True

                duration_current_task = time.time() - single_task_start_time
                result_color_current = "green" if sync_success_current_task else "red"
                console.print(
                    f"\n[{result_color_current}]Task (Local: {local_target_tbl_name} from AWS: {aws_source_tbl_name}) finished in {duration_current_task:.2f} seconds.[/{result_color_current}]")
            else:  # User cancelled for this specific task (should only happen if asked individually, not here with current logic)
                console.print(
                    f"Sync for Local: '{local_target_tbl_name}' from AWS: '{aws_source_tbl_name}' cancelled by user.")
                any_sync_failed = True

        overall_duration = time.time() - overall_sync_start_time
        final_result_color = "green" if not any_sync_failed else "red"
        console.print(
            f"\n[{final_result_color}]All selected sync operations finished in {overall_duration:.2f} seconds.[/{final_result_color}]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user (KeyboardInterrupt).[/yellow]")
    except Exception as e:
        cli_logger.critical(
            f"\n[bold red]CLI Error: An unexpected error occurred: {e}[/bold red]",
            exc_info=True)
        sys.exit(1)
    finally:
        console.print("\nDynamoDB sync tool finished.")


if __name__ == "__main__":
    # Force local DynamoDB for this CLI script only
    os.environ['USE_LOCAL_DYNAMODB'] = 'true'
    os.environ['LOCAL_DYNAMODB_ENDPOINT_URL'] = 'http://localhost:8000'
    
    # Configure logging levels for dependencies
    logging.getLogger("botocore").setLevel(logging.WARNING)
    logging.getLogger("boto3").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)

    # Set level for repositories
    logging.getLogger("src.repositories").setLevel(logging.INFO)

    # Allow setting CLI log level via environment variable, defaulting to INFO
    cli_log_level = os.environ.get("CLI_LOG_LEVEL", "INFO").upper()
    logging.getLogger("dynamodb_cli").setLevel(cli_log_level)
    # Also set the sync class logger level
    logging.getLogger("DynamoDBSync").setLevel(cli_log_level)

    asyncio.run(main())
