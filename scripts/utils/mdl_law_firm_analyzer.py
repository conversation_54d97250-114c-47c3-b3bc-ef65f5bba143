#!/usr/bin/env python3
"""
MDL Law Firm Analysis Script

This script analyzes law firm filing patterns for a specific MDL number by:
1. Querying PACER data from DynamoDB using the MdlNum-FilingDate-index
2. Extracting and normalizing law firm names from multiple fields
3. Generating a comprehensive markdown report with filing statistics
4. Supporting re-normalization without re-querying the database

Usage:
    python mdl_law_firm_analyzer.py --mdl-num 2873
    python mdl_law_firm_analyzer.py --mdl-num 2873 --re-normalize
    python mdl_law_firm_analyzer.py --mdl-num 2873 --output-dir custom_reports/
"""

import argparse
import asyncio
import json
import logging
import os
import sys
from collections import Counter, defaultdict
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Set
import hashlib
from decimal import Decimal

# Rich formatting imports
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Project imports
from src.repositories.pacer_repository import PacerRepository
from src.utils.law_firm_normalizer import normalize_law_firm_name
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Rich console
console = Console()


class DecimalEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle DynamoDB Decimal types."""
    
    def default(self, obj):
        if isinstance(obj, Decimal):
            # Convert Decimal to float for JSON serialization
            return float(obj)
        return super().default(obj)


class MDLAnalyzer:
    """Main class for analyzing MDL law firm data."""
    
    def __init__(self, mdl_num: str, output_dir: str = "reports/mdl_analysis", verbose: bool = False, start_date: Optional[str] = None, end_date: Optional[str] = None):
        self.mdl_num = mdl_num
        self.output_dir = Path(output_dir)
        self.verbose = verbose
        self.start_date = start_date
        self.end_date = end_date
        self.raw_data_dir = self.output_dir / "raw_data"
        self.console = console
        
        # Ensure output directories exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.raw_data_dir.mkdir(parents=True, exist_ok=True)
        
        # File paths (include date range in filename if specified)
        date_suffix = ""
        if start_date and end_date:
            date_suffix = f"_{start_date}_{end_date}"
        elif start_date:
            date_suffix = f"_{start_date}_onwards"
        elif end_date:
            date_suffix = f"_until_{end_date}"
            
        self.raw_data_file = self.raw_data_dir / f"mdl_{mdl_num}{date_suffix}_raw.json"
        self.report_file = self.output_dir / f"mdl_{mdl_num}{date_suffix}_analysis.md"
        
        # Statistics
        self.stats = {
            'total_records': 0,
            'records_with_law_firms': 0,
            'unique_law_firms': 0,
            'total_filings': 0,
            'processing_time': 0,
            'query_time': 0
        }
    
    async def query_mdl_data(self) -> List[Dict[str, Any]]:
        """Query DynamoDB for all records with the specified MDL number."""
        start_time = datetime.now()
        
        with Progress(
            SpinnerColumn(),
            TextColumn(f"[bold blue]Querying DynamoDB for MDL {self.mdl_num}..."),
            console=self.console
        ) as progress:
            task = progress.add_task("query")
            
            try:
                # Create async storage and repository
                from types import SimpleNamespace
                config = SimpleNamespace()
                storage = AsyncDynamoDBStorage(config, logger)
                async with storage:
                    pacer_repo = PacerRepository(storage)
                    
                    # Query by MDL number - bypass repository retry timeout for large datasets
                    try:
                        # Use storage.query directly to avoid the 30s timeout from @with_retry decorator
                        from boto3.dynamodb.conditions import Key
                        index_name = 'MdlNum-FilingDate-index'
                        key_condition = Key('MdlNum').eq(self.mdl_num)
                        
                        records = await asyncio.wait_for(
                            storage.query('Pacer', key_condition, index_name=index_name),
                            timeout=300.0  # 5 minute timeout for large result sets
                        )
                        
                        # Convert from PascalCase to snake_case manually since we bypassed repository
                        records = [pacer_repo._convert_dict_to_snake(record) for record in records]
                        
                        # Filter by date range in memory if date parameters provided
                        if self.start_date or self.end_date:
                            records = self._filter_records_by_date_range(records)
                    except asyncio.TimeoutError:
                        self.console.print(f"⚠️  Query timed out after 5 minutes. The dataset may be too large.", style="yellow")
                        return []
                    
                    progress.update(task, completed=100)
                    
                    self.stats['total_records'] = len(records)
                    self.stats['query_time'] = (datetime.now() - start_time).total_seconds()
                    
                    self.console.print(f"✅ Found {len(records)} records for MDL {self.mdl_num}")
                    
                    return records
                    
            except Exception as e:
                progress.update(task, completed=100)
                self.console.print(f"❌ Error querying database: {e}", style="red")
                logger.error(f"Database query error: {e}")
                return []
    
    def extract_law_firms_from_record(self, record: Dict[str, Any]) -> Set[str]:
        """Extract law firm names from a single PACER record."""
        law_firms = set()
        
        # Process ONLY the law_firm field
        if 'law_firm' in record and record['law_firm']:
            value = record['law_firm']
            if isinstance(value, str) and value.strip():
                # Don't normalize here - just extract the raw firm name
                # Normalization will happen during aggregation
                law_firms.add(value.strip())
        
        # Filter out common non-firm entries
        filtered_firms = set()
        for firm in law_firms:
            if self._is_valid_law_firm(firm):
                filtered_firms.add(firm)
        
        return filtered_firms
    
    def _parse_law_firm_string(self, value: str) -> Set[str]:
        """Parse a law firm string that might contain multiple firms."""
        if not value or not isinstance(value, str):
            return set()
            
        # Common separators for multiple law firms
        separators = [';', '|', '\n', ' and ', ' & ']
        
        firms = {value.strip()}
        
        for separator in separators:
            new_firms = set()
            for firm in firms:
                if separator in firm:
                    split_firms = [f.strip() for f in firm.split(separator)]
                    new_firms.update(split_firms)
                else:
                    new_firms.add(firm)
            firms = new_firms
        
        return firms
    
    def _is_valid_law_firm(self, firm: str) -> bool:
        """Check if a string represents a valid law firm name."""
        if not firm or not isinstance(firm, str):
            return False
            
        firm_lower = firm.lower().strip()
        
        # Filter out common non-firm entries
        invalid_entries = {
            'pro se', 'n/a', 'na', 'none', 'null', 'unknown', 'not applicable',
            'self-represented', 'self represented', 'unrepresented', 'individual',
            'plaintiff', 'defendant', 'petitioner', 'respondent', '-', '–', '—'
        }
        
        if firm_lower in invalid_entries:
            return False
            
        # Must have at least 2 characters
        if len(firm_lower) < 2:
            return False
            
        # Must contain at least one letter
        if not any(c.isalpha() for c in firm_lower):
            return False
            
        return True
    
    def _filter_records_by_date_range(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter records by date range in memory."""
        if not self.start_date and not self.end_date:
            return records
        
        filtered_records = []
        start_date = self.start_date or "19700101"
        end_date = self.end_date or "30000101"
        
        for record in records:
            filing_date = record.get('FilingDate', record.get('filing_date', ''))
            if filing_date and start_date <= filing_date <= end_date:
                filtered_records.append(record)
        
        return filtered_records
    
    def _normalize_and_sort_firm(self, firm: str) -> str:
        """Normalize and sort a law firm name."""
        # MDL-specific mappings
        if self.mdl_num == '2738' and firm == 'Duncan Stubbs':
            return 'Duncan Stubbs ; OnderLaw LLC'
        
        # Check if firm contains semicolon separator
        if ';' in firm:
            # Split by semicolon, normalize each part, sort alphabetically
            parts = [part.strip() for part in firm.split(';')]
            normalized_parts = []
            for part in parts:
                normalized_part = normalize_law_firm_name(part)
                normalized_parts.append(normalized_part)
            # Sort alphabetically and rejoin with semicolon
            sorted_parts = sorted(normalized_parts)
            return ' ; '.join(sorted_parts)
        else:
            # Single firm, just normalize
            normalized = normalize_law_firm_name(firm)
            
            # Debug output for Watts Law Firm issue and other normalizations
            if firm != normalized:
                self.console.print(f"[DEBUG] Normalization: '{firm}' → '{normalized}'", style="yellow")
            
            return normalized
    
    def normalize_law_firms(self, law_firms: Set[str]) -> Dict[str, str]:
        """Normalize law firm names and create mapping."""
        normalized_mapping = {}
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[bold green]Normalizing law firm names..."),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        ) as progress:
            task = progress.add_task("normalize", total=len(law_firms))
            
            for firm in law_firms:
                try:
                    normalized = normalize_law_firm_name(firm)
                    normalized_mapping[firm] = normalized
                    
                    if self.verbose:
                        if firm != normalized:
                            self.console.print(f"  {firm} → {normalized}", style="dim")
                            
                except Exception as e:
                    logger.warning(f"Error normalizing '{firm}': {e}")
                    normalized_mapping[firm] = firm
                    
                progress.update(task, advance=1)
        
        return normalized_mapping
    
    def aggregate_filings(self, records: List[Dict[str, Any]], normalized_mapping: Dict[str, str]) -> Dict[str, int]:
        """Aggregate filing counts by normalized law firm names."""
        # First collect all raw counts
        raw_firm_counts = Counter()
        
        for record in records:
            law_firms = self.extract_law_firms_from_record(record)
            
            for firm in law_firms:
                raw_firm_counts[firm] += 1
        
        # Now aggregate by normalized names
        firm_counts = Counter()
        for firm, count in raw_firm_counts.items():
            # Normalize the firm name for aggregation
            normalized_firm = self._normalize_and_sort_firm(firm)
            
            # Debug: Show when aggregation is happening
            if firm != normalized_firm and count > 0:
                self.console.print(f"[DEBUG] Aggregating: '{firm}' ({count}) → '{normalized_firm}'", style="cyan")
            
            firm_counts[normalized_firm] += count
        
        return dict(firm_counts)
    
    def save_raw_data(self, records: List[Dict[str, Any]]) -> None:
        """Save raw query results for re-processing."""
        data = {
            'metadata': {
                'mdl_num': self.mdl_num,
                'query_date': datetime.now().isoformat(),
                'total_records': len(records),
                'script_version': '1.0.0'
            },
            'records': records
        }
        
        # Generate checksum for data integrity
        data_str = json.dumps(data, sort_keys=True, cls=DecimalEncoder)
        data['metadata']['checksum'] = hashlib.sha256(data_str.encode()).hexdigest()
        
        try:
            with open(self.raw_data_file, 'w') as f:
                json.dump(data, f, indent=2, cls=DecimalEncoder)
            
            self.console.print(f"💾 Raw data saved to {self.raw_data_file}")
            
        except Exception as e:
            self.console.print(f"❌ Error saving raw data: {e}", style="red")
            logger.error(f"Error saving raw data: {e}")
    
    def load_raw_data(self) -> Optional[List[Dict[str, Any]]]:
        """Load raw data from file for re-processing."""
        if not self.raw_data_file.exists():
            self.console.print(f"❌ Raw data file not found: {self.raw_data_file}", style="red")
            return None
        
        try:
            with open(self.raw_data_file, 'r') as f:
                data = json.load(f)
            
            # Verify data integrity
            records = data.get('records', [])
            metadata = data.get('metadata', {})
            
            self.console.print(f"📁 Loaded {len(records)} records from {self.raw_data_file}")
            self.console.print(f"   Query date: {metadata.get('query_date', 'Unknown')}")
            
            return records
            
        except Exception as e:
            self.console.print(f"❌ Error loading raw data: {e}", style="red")
            logger.error(f"Error loading raw data: {e}")
            return None
    
    def generate_report(self, firm_counts: Dict[str, int], normalized_mapping: Dict[str, str]) -> None:
        """Generate markdown report with law firm statistics."""
        total_filings = sum(firm_counts.values())
        sorted_firms = sorted(firm_counts.items(), key=lambda x: x[1], reverse=True)
        
        # Update statistics
        self.stats['unique_law_firms'] = len(firm_counts)
        self.stats['total_filings'] = total_filings
        self.stats['records_with_law_firms'] = len([r for r in firm_counts.values() if r > 0])
        
        # Generate markdown content
        date_range_info = ""
        if self.start_date or self.end_date:
            date_range_info = f"\n- **Date Range**: "
            if self.start_date and self.end_date:
                date_range_info += f"{self.start_date} to {self.end_date}"
            elif self.start_date:
                date_range_info += f"from {self.start_date} onwards"
            elif self.end_date:
                date_range_info += f"until {self.end_date}"
        
        report_content = f"""# MDL {self.mdl_num} Law Firm Analysis Report

## Summary Statistics

- **Total Records**: {self.stats['total_records']:,}
- **Records with Law Firms**: {self.stats['records_with_law_firms']:,}
- **Total Filings**: {total_filings:,}
- **Unique Law Firms**: {len(firm_counts):,}{date_range_info}
- **Analysis Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Query Time**: {self.stats['query_time']:.2f} seconds

## Law Firm Filing Statistics

| Rank | Law Firm | Filing Count | Percentage |
|------|----------|--------------|------------|
"""
        
        for rank, (firm, count) in enumerate(sorted_firms, 1):
            percentage = (count / total_filings) * 100 if total_filings > 0 else 0
            report_content += f"| {rank} | {firm} | {count:,} | {percentage:.1f}% |\n"
        
        # Add normalization mapping section
        report_content += f"""
## Normalization Details

Total original law firm names: {len(normalized_mapping)}
Total normalized law firm names: {len(set(normalized_mapping.values()))}

### Normalization Mappings
"""
        
        # Show only cases where normalization changed the name
        changed_mappings = {k: v for k, v in normalized_mapping.items() if k != v}
        if changed_mappings:
            report_content += "\n| Original Name | Normalized Name |\n|---------------|----------------|\n"
            for original, normalized in sorted(changed_mappings.items()):
                report_content += f"| {original} | {normalized} |\n"
        else:
            report_content += "\nNo name changes were made during normalization.\n"
        
        # Add metadata
        report_content += f"""
## Processing Details

- **Script Version**: 1.0.0
- **Data Source**: DynamoDB PACER table
- **Index Used**: MdlNum-FilingDate-index
- **Raw Data File**: {self.raw_data_file}
- **Generated**: {datetime.now().isoformat()}
"""
        
        # Write report to file
        try:
            with open(self.report_file, 'w') as f:
                f.write(report_content)
            
            self.console.print(f"📄 Report generated: {self.report_file}")
            
        except Exception as e:
            self.console.print(f"❌ Error generating report: {e}", style="red")
            logger.error(f"Error generating report: {e}")
    
    def display_console_summary(self, firm_counts: Dict[str, int]) -> None:
        """Display summary statistics in the console."""
        total_filings = sum(firm_counts.values())
        sorted_firms = sorted(firm_counts.items(), key=lambda x: x[1], reverse=True)
        
        # Create summary panel
        summary_text = f"""
📊 **MDL {self.mdl_num} Analysis Complete**

Total Records: {self.stats['total_records']:,}
Total Filings: {total_filings:,}
Unique Law Firms: {len(firm_counts):,}
Query Time: {self.stats['query_time']:.2f}s
"""
        
        summary_panel = Panel(summary_text, title="Analysis Summary", border_style="green")
        self.console.print(summary_panel)
        
        # Create complete law firms table
        if sorted_firms:
            table = Table(title=f"All Law Firms - MDL {self.mdl_num}")
            table.add_column("Rank", style="cyan", no_wrap=True)
            table.add_column("Law Firm", style="magenta")
            table.add_column("Filings", style="green", justify="right")
            table.add_column("Percentage", style="yellow", justify="right")
            
            for rank, (firm, count) in enumerate(sorted_firms, 1):
                percentage = (count / total_filings) * 100 if total_filings > 0 else 0
                table.add_row(str(rank), firm, f"{count:,}", f"{percentage:.1f}%")
            
            self.console.print(table)
        
        # Show file locations
        self.console.print(f"\n📁 Files created:")
        self.console.print(f"   Report: {self.report_file}", style="blue")
        self.console.print(f"   Raw Data: {self.raw_data_file}", style="blue")


async def main():
    """Main function to run the MDL analysis."""
    parser = argparse.ArgumentParser(
        description="Analyze law firm filing patterns for a specific MDL number",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python mdl_law_firm_analyzer.py --mdl-num 2873
  python mdl_law_firm_analyzer.py --mdl-num 2873 --re-normalize
  python mdl_law_firm_analyzer.py --mdl-num 2873 --output-dir custom_reports/
  python mdl_law_firm_analyzer.py --mdl-num 2873 --start-date 20240101 --end-date 20241231
  python mdl_law_firm_analyzer.py --mdl-num 2873 --start-date 20240601 --verbose
"""
    )
    
    parser.add_argument(
        '--mdl-num',
        required=True,
        type=str,
        help='MDL number to analyze'
    )
    
    parser.add_argument(
        '--re-normalize',
        action='store_true',
        help='Re-normalize existing data without re-querying database'
    )
    
    parser.add_argument(
        '--output-dir',
        default='reports/mdl_analysis',
        help='Output directory for reports (default: reports/mdl_analysis)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Show detailed output during processing'
    )
    
    parser.add_argument(
        '--start-date',
        type=str,
        help='Start date for filtering (YYYYMMDD format, e.g., 20240101)'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        help='End date for filtering (YYYYMMDD format, e.g., 20241231)'
    )
    
    args = parser.parse_args()
    
    # Validate date format if provided
    if args.start_date and len(args.start_date) != 8:
        console.print("❌ Start date must be in YYYYMMDD format", style="red")
        return 1
    if args.end_date and len(args.end_date) != 8:
        console.print("❌ End date must be in YYYYMMDD format", style="red")
        return 1
    
    # Create analyzer instance
    analyzer = MDLAnalyzer(
        mdl_num=args.mdl_num,
        output_dir=args.output_dir,
        verbose=args.verbose,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    # Show analysis parameters
    analysis_msg = f"🔍 Starting MDL {args.mdl_num} analysis"
    if args.start_date or args.end_date:
        date_range = []
        if args.start_date:
            date_range.append(f"from {args.start_date}")
        if args.end_date:
            date_range.append(f"to {args.end_date}")
        analysis_msg += f" ({' '.join(date_range)})"
    console.print(analysis_msg + "...")
    
    start_time = datetime.now()
    
    try:
        # Load or query data
        if args.re_normalize:
            records = analyzer.load_raw_data()
            if records is None:
                console.print("❌ Cannot re-normalize without existing raw data", style="red")
                return 1
        else:
            records = await analyzer.query_mdl_data()
            if not records:
                console.print("❌ No records found for the specified MDL number", style="red")
                return 1
            
            # Save raw data for future re-processing
            analyzer.save_raw_data(records)
        
        # Extract law firms from all records
        console.print("🔍 Extracting law firm names from records...")
        all_law_firms = set()
        for record in records:
            law_firms = analyzer.extract_law_firms_from_record(record)
            all_law_firms.update(law_firms)
        
        console.print(f"📋 Found {len(all_law_firms)} unique law firm names")
        
        # Normalize law firm names
        normalized_mapping = analyzer.normalize_law_firms(all_law_firms)
        
        # Aggregate filings by normalized names
        firm_counts = analyzer.aggregate_filings(records, normalized_mapping)
        
        # Generate report
        analyzer.generate_report(firm_counts, normalized_mapping)
        
        # Display console summary
        analyzer.display_console_summary(firm_counts)
        
        # Final timing
        processing_time = (datetime.now() - start_time).total_seconds()
        console.print(f"\n✅ Analysis completed in {processing_time:.2f} seconds", style="green bold")
        
        return 0
        
    except KeyboardInterrupt:
        console.print("\n❌ Analysis interrupted by user", style="red")
        return 1
    except Exception as e:
        console.print(f"\n❌ Unexpected error: {e}", style="red")
        logger.error(f"Unexpected error: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))