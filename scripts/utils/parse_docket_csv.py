import os
#!/usr/bin/env python3

import csv
import re
from collections import defaultdict

# Import the project root handling function
try:
    from scripts.utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config_adapter import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('/')
else:
    PROJECT_ROOT = get_project_root()



def parse_docket_text(docket_text):
    """Extract attorney name, docket number, and versus from docket text."""
    # Extract versus - first try NOTICE OF REMOVAL pattern
    versus_match = re.search(r'NOTICE OF REMOVAL\s+(.*?)\s+Court Name', docket_text)
    if versus_match:
        versus = versus_match.group(1).strip()
    else:
        # Try COMPLAINT pattern with either Division
        versus_match = re.search(r'COMPLAINT\s+(.*?)\s+(?:Charleston|South Carolina) Division', docket_text)
        versus = versus_match.group(1).strip() if versus_match else 'NA'

    # Extract attorney name
    attorney_match = re.search(r'ASCDC-\d+\)\.\s*([^)]+?)\)', docket_text)
    attorney = 'NA'

    if attorney_match:
        potential_attorney = attorney_match.group(1).strip()
        if 'Attachments' not in potential_attorney:
            attorney = potential_attorney
            print(f"First pattern matched: '{attorney}'")
        else:
            attorney_match = re.search(r'Summons(?:\s+(?:List|[^)]+))?\)(.*?)\)', docket_text)
            if attorney_match:
                attorney = attorney_match.group(1).strip()
                print(f"Second pattern matched: '{attorney}'")
            else:
                attorney_match = re.search(r'additional defendants\)(.*?)\)', docket_text)
                if attorney_match:
                    attorney = attorney_match.group(1).strip()
                    print(f"Third pattern matched: '{attorney}'")
    else:
        attorney_match = re.search(r'Summons(?:\s+(?:List|[^)]+))?\)(.*?)\)', docket_text)
        if attorney_match:
            attorney = attorney_match.group(1).strip()
            print(f"Second pattern matched: '{attorney}'")
        else:
            attorney_match = re.search(r'additional defendants\)(.*?)\)', docket_text)
            if attorney_match:
                attorney = attorney_match.group(1).strip()
                print(f"Third pattern matched: '{attorney}'")

    if attorney == 'NA':
        attorney_match = re.search(r'\)(.*?)\)\s*Modified', docket_text)
        if attorney_match:
            attorney = attorney_match.group(1).strip()
            print(f"Final pattern matched: '{attorney}'")

    # Extract docket number - looks for Case #X:XX-cv-XXXXX pattern
    docket_match = re.search(r'Case #(\d:\d{2}-cv-\d{5})', docket_text)
    if not docket_match:
        # Try alternate pattern without Case #
        docket_match = re.search(r'(\d:\d{2}-cv-\d{5})', docket_text)
    docket_num = docket_match.group(1) if docket_match else 'NA'

    return attorney, docket_num, versus


def process_docket_csv(input_file, output_file, summary_file):
    """Process the docket CSV file and create attorney filing summary."""
    attorney_filings = defaultdict(int)
    processed_rows = []

    try:
        with open(input_file, 'r', newline='', encoding='utf-8') as infile:
            reader = csv.DictReader(infile)
            print(f"CSV Headers: {reader.fieldnames}")

            for row in reader:
                docket_text = row.get('Docket Text', '')
                if not docket_text:
                    continue

                attorney, docket_num, versus = parse_docket_text(docket_text)

                if attorney != 'NA':
                    attorney_filings[attorney] += 1
                    print(f"Found filing by: {attorney}")

                processed_row = {
                    'Date Filed': row.get('Date Filed', ''),
                    'Docket Text': docket_text,
                    'Attorney': attorney,
                    'Docket Number': docket_num,
                    'Versus': versus
                }
                processed_rows.append(processed_row)

        # Write detailed output
        if processed_rows:
            with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
                fieldnames = ['Date Filed', 'Docket Text', 'Attorney', 'Docket Number', 'Versus']
                writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(processed_rows)
                print(f"Processed data written to {output_file}")

        # Write summary
        if attorney_filings:
            with open(summary_file, 'w', newline='', encoding='utf-8') as summaryfile:
                summary_writer = csv.writer(summaryfile)
                summary_writer.writerow(['Attorney', 'Number of Filings'])

                sorted_attorneys = sorted(
                    attorney_filings.items(),
                    key=lambda x: (-x[1], x[0])
                )

                for attorney, count in sorted_attorneys:
                    summary_writer.writerow([attorney, count])
                    print(f"Summary: {attorney}: {count} filings")

            print(f"\nSummary statistics:")
            print(f"Total attorneys found: {len(attorney_filings)}")
            print(f"Total filings processed: {sum(attorney_filings.values())}")
            print(f"Summary saved to: {summary_file}")
        else:
            print("No attorney filings found to summarize")

    except FileNotFoundError:
        print(f"Error: Could not find input file: {input_file}")
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        raise  # Re-raise the exception for debugging

if __name__ == "__main__":
    input_file = os.path.join(PROJECT_ROOT, "src/scripts/afff-docket-entries.csv")
    output_file = os.path.join(PROJECT_ROOT, "src/scripts/processed-afff-docket.csv")
    summary_file = os.path.join(PROJECT_ROOT, "src/scripts/attorney_filing_summary.csv")
    process_docket_csv(input_file, output_file, summary_file)