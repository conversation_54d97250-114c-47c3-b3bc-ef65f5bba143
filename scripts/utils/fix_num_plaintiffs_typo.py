#!/usr/bin/env python3
"""
Fix num_plaintiffs field typo in JSON files.

This script fixes the typo where 'num_plaintiffs:' (with colon) was used instead of 'num_plaintiffs'.
It iterates through JSON files in the date range and renames the field without removing any data.

Usage:
    python scripts/utils/fix_num_plaintiffs_typo.py --start-date 20250601 --end-date 20250617 --dry-run
    python scripts/utils/fix_num_plaintiffs_typo.py --start-date 20250601 --end-date 20250617
"""

import argparse
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Tuple

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn

console = Console()


def get_json_files_for_date(date_str: str, data_dir: str) -> List[Path]:
    """Get all JSON files for a specific date."""
    date_dir = Path(data_dir) / date_str / "dockets"
    json_files = []
    
    if date_dir.exists():
        json_files = list(date_dir.glob("*.json"))
    
    return json_files


def fix_num_plaintiffs_typo(file_path: Path, dry_run: bool = True) -> Tuple[bool, str]:
    """
    Fix the num_plaintiffs typo in a JSON file.
    
    Returns:
        Tuple of (was_fixed, message)
    """
    try:
        # Read the JSON file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Check if the typo field exists
        if 'num_plaintiffs:' in data:
            value = data['num_plaintiffs:']
            
            if dry_run:
                return True, f"Would fix: '{value}'"
            else:
                # Rename the field (preserving the value)
                data['num_plaintiffs'] = value
                del data['num_plaintiffs:']
                
                # Write the file back
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=4, ensure_ascii=False)
                
                return True, f"Fixed: '{value}'"
        else:
            return False, "No typo found"
            
    except json.JSONDecodeError as e:
        return False, f"JSON error: {e}"
    except Exception as e:
        return False, f"Error: {e}"


def main():
    parser = argparse.ArgumentParser(description='Fix num_plaintiffs field typo in JSON files')
    parser.add_argument('--start-date', required=True, help='Start date (YYYYMMDD)')
    parser.add_argument('--end-date', required=True, help='End date (YYYYMMDD)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed without modifying files')
    parser.add_argument('--data-dir', help='Data directory path', 
                        default=os.environ.get('LEXGENIUS_DATA_DIR', 
                                               os.path.join(project_root, 'data')))
    
    args = parser.parse_args()
    
    # Validate dates
    try:
        start_date = datetime.strptime(args.start_date, '%Y%m%d')
        end_date = datetime.strptime(args.end_date, '%Y%m%d')
    except ValueError:
        console.print("[red]Invalid date format. Use YYYYMMDD.[/red]")
        sys.exit(1)
    
    if start_date > end_date:
        console.print("[red]Start date must be before or equal to end date.[/red]")
        sys.exit(1)
    
    # Display run mode
    if args.dry_run:
        console.print("[yellow]Running in DRY RUN mode - no files will be modified[/yellow]\n")
    else:
        console.print("[bold red]Running in MODIFY mode - files will be updated[/bold red]\n")
    
    # Process files
    total_files = 0
    fixed_files = 0
    error_files = 0
    
    # Collect all files to process
    all_files = []
    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y%m%d')
        json_files = get_json_files_for_date(date_str, args.data_dir)
        all_files.extend([(date_str, f) for f in json_files])
        current_date += timedelta(days=1)
    
    console.print(f"Found {len(all_files)} JSON files to check\n")
    
    # Process files with progress bar
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:
        task = progress.add_task("Processing files...", total=len(all_files))
        
        # Results table
        results = []
        
        for date_str, file_path in all_files:
            total_files += 1
            was_fixed, message = fix_num_plaintiffs_typo(file_path, args.dry_run)
            
            if was_fixed:
                fixed_files += 1
                results.append({
                    'date': date_str,
                    'file': file_path.name,
                    'status': 'Fixed' if not args.dry_run else 'Would fix',
                    'message': message
                })
            elif message.startswith("Error:") or message.startswith("JSON error:"):
                error_files += 1
                results.append({
                    'date': date_str,
                    'file': file_path.name,
                    'status': 'Error',
                    'message': message
                })
            
            progress.update(task, advance=1)
    
    # Display results
    if results:
        console.print("\n[bold]Files with changes:[/bold]")
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Date", style="cyan")
        table.add_column("File", style="cyan", max_width=50)
        table.add_column("Status", style="yellow")
        table.add_column("Details", style="green")
        
        for result in results:
            table.add_row(
                result['date'],
                result['file'],
                result['status'],
                result['message']
            )
        
        console.print(table)
    
    # Summary
    console.print(f"\n[bold]Summary:[/bold]")
    console.print(f"Total files checked: {total_files}")
    console.print(f"Files {'that would be' if args.dry_run else ''} fixed: {fixed_files}")
    console.print(f"Files with errors: {error_files}")
    console.print(f"Files without typo: {total_files - fixed_files - error_files}")
    
    if args.dry_run and fixed_files > 0:
        console.print(f"\n[yellow]Run without --dry-run to apply these changes.[/yellow]")


if __name__ == "__main__":
    main()