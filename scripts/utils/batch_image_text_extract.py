#!/usr/bin/env python3
import asyncio
import logging
import os
import argparse
from typing import Optional, Dict, List, Tuple, Union, Any

import pandas as pd
from colorama import Fore, init
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, BarColumn, TimeRemainingColumn

# Initialize Colorama for UI elements
init(autoreset=True)

# Library Imports
try:
    from src.lib.config_adapter import load_config
    from src.repositories.fb_archive_repository import FBArchiveRepository
    from src.lib.llava_vision import LlavaImageExtractor
    from src.infrastructure.storage.s3_async import S3AsyncStorage, S3_ACCESS_DENIED_MARKER
except ImportError as e:
    print(f"Error importing library: {e}. Please ensure src/lib, src/repositories, "
          f"and src/infrastructure are in your PYTHONPATH or correctly structured.")
    exit(1)
except FileNotFoundError as e:
    print(f"Error: Required script not found: {e}")
    exit(1)

# --- Global Constants and UI Components ---
# This marker is used internally by this script to signify a 403/forbidden status
# for records that couldn't be processed (e.g., S3 access denied or key not found).
FORBIDDEN_403_MARKER = "FORBIDDEN_403"

console = Console()

# Setup logging
logging.basicConfig(level=logging.INFO,  # Default to INFO, can be overridden by --debug
                    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                    datefmt='%H:%M:%S')
logger = logging.getLogger(__name__)

# --- Configuration (Defaults and Overrides) ---
# Load Ollama config if available
ollama_config = {}
try:
    # Attempt to load Ollama specific performance settings from config
    ollama_config = LlavaImageExtractor.load_ollama_config() or {}
except (FileNotFoundError, OSError, TypeError) as e:
    logger.warning(
        f"Could not load Ollama config (e.g., .env or config.json): {e}. Using default performance settings.")
except Exception as e:
    logger.error(f"Unexpected error loading Ollama config: {e}", exc_info=True)

perf_config = ollama_config.get('performance', {})

# Defaults (can be overridden by command line arguments)
OLLAMA_CONCURRENCY = 6  # Default number of concurrent LLaVA calls
DB_WRITE_CONCURRENCY = perf_config.get('db_write_concurrency', 16)  # Concurrent DynamoDB writes
BATCH_UPDATE_SIZE = 100  # Number of unique image pairs to process per chunk for LLaVA and DB writes

# LLaVA specific settings (usually from config or defaults in LlavaImageExtractor)
# -1 means auto-detect or use CPU if no GPU (depending on Ollama server setup)
OLLAMA_NUM_GPU_LAYERS = perf_config.get('ollama_num_gpu_layers', -1)
# Keep model loaded indefinitely - usually set via Ollama server env var or client config
OLLAMA_KEEP_ALIVE = perf_config.get('ollama_keep_alive', -1)

# Log initial configuration
logger.info(f"Initial Performance Config: LLaVA Concurrency={OLLAMA_CONCURRENCY}, "
            f"DB Write Concurrency={DB_WRITE_CONCURRENCY}, Batch Size={BATCH_UPDATE_SIZE}")


# --- Helper Function: Prepare DynamoDB Item ---
# This function was previously imported from src.scripts.add_fb_summary_to_missing
def prepare_dynamodb_item(row_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Prepares a dictionary for insertion or update into the DynamoDB FBAdArchive table.

    Args:
        row_dict: Dictionary containing Facebook Ad Archive data, typically from a DataFrame row.

    Returns:
        Dictionary formatted for DynamoDB insertion, or None if required fields are missing.
    """
    module_logger = logging.getLogger(__name__ + ".prepare_dynamodb_item")

    try:
        # Required fields for DynamoDB key
        if 'AdArchiveID' not in row_dict or 'StartDate' not in row_dict:
            module_logger.warning(
                f"Missing required key fields for DynamoDB item: AdArchiveID or StartDate. Row: {row_dict.keys()}")
            return None

        # Create a new dict with only the fields we want to store
        item = {}

        # Copy key fields (required)
        item['AdArchiveID'] = str(row_dict['AdArchiveID'])
        item['StartDate'] = str(row_dict['StartDate'])

        # Copy other important fields if they exist
        for field in ['AdCreativeId', 'ImageText', 'IsForbidden403', 'EndDate', 'LastUpdated']:
            if field in row_dict and row_dict[field] is not None:
                # Ensure correct type for specific fields
                if field == 'IsForbidden403':
                    item[field] = bool(row_dict[field])
                else:
                    item[field] = str(row_dict[field])

        # Handle cleaned ID fields, prioritizing them if present
        if 'AdCreativeId_Clean' in row_dict and row_dict['AdCreativeId_Clean'] is not None:
            item['AdCreativeId'] = str(row_dict['AdCreativeId_Clean'])

        if 'AdArchiveID_Clean' in row_dict and row_dict['AdArchiveID_Clean'] is not None:
            item['AdArchiveID'] = str(row_dict['AdArchiveID_Clean'])

        # Ensure IsForbidden403 is present and a boolean, defaulting to False
        if 'IsForbidden403' not in item:
            item['IsForbidden403'] = False
        else:
            item['IsForbidden403'] = bool(item['IsForbidden403'])  # Double-check type

        return item
    except Exception as e:
        module_logger.error(f"Error preparing DynamoDB item: {e}", exc_info=True)
        return None


# --- Core Logic: Image Text Extraction ---
async def extract_image_text_with_semaphore(
        semaphore: asyncio.Semaphore,
        llava_extractor: LlavaImageExtractor,
        s3_manager: S3AsyncStorage,
        archive_id: str,
        creative_id: str,
        progress: Progress,
        task_id,
        pair_to_indices: Dict[Tuple[str, str], List[int]],
        is_initial_403: bool,
        process_403_flag: bool
) -> Optional[Union[str, str]]:  # Returns extracted text (str), FORBIDDEN_403_MARKER, or None
    """
    Fetches an image from S3, handles known 403/not-found statuses, and extracts text using LLaVA.
    Treats S3 'Not Found' errors the same as 'Access Denied (403)' for the purpose of marking.

    Args:
        semaphore: An asyncio.Semaphore to limit concurrent LLaVA calls.
        llava_extractor: An instance of LlavaImageExtractor.
        s3_manager: An instance of S3AsyncStorage.
        archive_id: The AdArchiveID for the image.
        creative_id: The AdCreativeId for the image.
        progress: The Rich progress bar instance.
        task_id: The ID of the progress bar task to update.
        pair_to_indices: A dictionary mapping (archive_id, creative_id) to a list of DataFrame indices.
                         Used to advance the progress bar by the number of records associated with a pair.
        is_initial_403: A boolean indicating if this pair was initially marked as IsForbidden403=True in DB.
        process_403_flag: A boolean from command line indicating if known 403s should be re-attempted.

    Returns:
        - Extracted text (str) on successful LLaVA processing.
        - FORBIDDEN_403_MARKER (str) if S3 access denied (403) or if the S3 key is not found.
        - None on other errors (LLaVA failure, unexpected S3 issues, etc.).
    """
    module_logger = logging.getLogger(__name__ + ".extract_image_text")
    processing_pair = (archive_id, creative_id)
    num_records_for_pair = len(pair_to_indices.get(processing_pair, []))
    log_prefix = f"ArchiveID {archive_id}, CreativeID {creative_id}"
    s3_key = f"adarchive/fb/{archive_id}/{creative_id}.jpg"
    result_to_return: Optional[Union[str, str]] = None
    start_time = asyncio.get_running_loop().time()

    async with semaphore:
        # --- Handle known 403s based on flag ---
        if is_initial_403 and not process_403_flag:
            module_logger.info(
                f"{Fore.MAGENTA}Skipping known 403 pair {log_prefix} as --process-403 is not set.{Fore.RESET}")
            result_to_return = FORBIDDEN_403_MARKER  # Still mark as 403 for consistency if needed later
        else:
            # --- S3 Fetch and LLaVA Processing ---
            image_data: Optional[Union[bytes, str]] = None
            extracted_text_raw: Optional[str] = None

            try:
                if is_initial_403 and process_403_flag:
                    module_logger.info(
                        f"{Fore.CYAN}Re-processing known 403 pair {log_prefix} due to --process-403 flag.{Fore.RESET}")

                module_logger.debug(f"Attempting to fetch image from S3: {s3_manager.bucket_name}/{s3_key}")

                # Fetch image data from S3. This call is blocking so run in executor.
                # It returns bytes, S3_ACCESS_DENIED_MARKER, or None (for not found/other errors).
                image_data = await asyncio.get_running_loop().run_in_executor(
                    None, s3_manager.get_content_by_key, s3_key
                )

                # 2. Handle S3 Fetch Result
                if image_data == S3_ACCESS_DENIED_MARKER:
                    module_logger.warning(
                        f"{Fore.MAGENTA}S3 Access Denied (403) detected for {log_prefix} (Key: {s3_key}). Marking as forbidden.{Fore.RESET}")
                    result_to_return = FORBIDDEN_403_MARKER
                elif image_data is None:
                    # Treat 'Not Found' or other fetch errors as 403/forbidden for marking purposes
                    module_logger.warning(
                        f"{Fore.YELLOW}S3 Key not found or fetch failed for {log_prefix} (Key: {s3_key}). Treating as 403/forbidden.{Fore.RESET}")
                    result_to_return = FORBIDDEN_403_MARKER
                elif isinstance(image_data, bytes):
                    # 3. Process valid bytes with LLaVA
                    module_logger.debug(
                        f"Successfully fetched {len(image_data)} bytes from S3 for {log_prefix}. Calling LLaVA.")
                    try:
                        extracted_text_raw = await llava_extractor.extract_text_from_bytes(image_data)
                        module_logger.debug(
                            f"Raw result from llava_extractor (S3 bytes) for {log_prefix}: {extracted_text_raw!r}")

                        if extracted_text_raw is None:
                            module_logger.warning(
                                f"{Fore.YELLOW}LLaVA processing failed or returned None for {log_prefix} (S3 Key: {s3_key}).")
                            result_to_return = None
                        elif isinstance(extracted_text_raw, str):
                            extracted_text = extracted_text_raw.strip()
                            if extracted_text:
                                log_content_snippet = extracted_text[:200] + (
                                    '...' if len(extracted_text) > 200 else '')
                                module_logger.info(
                                    f"{Fore.GREEN}Success LLaVA {log_prefix} (from S3). Result: {log_content_snippet!r}")
                                result_to_return = extracted_text
                            else:
                                module_logger.warning(
                                    f"{Fore.YELLOW}LLaVA query succeeded but returned empty string for {log_prefix} (S3 Key: {s3_key}).")
                                result_to_return = ""  # Return empty string if LLaVA returns empty
                        else:
                            module_logger.error(
                                f"Unexpected type from llava_extractor for {log_prefix}: {type(extracted_text_raw)}")
                            result_to_return = None

                    except AttributeError as ae:
                        if 'extract_text_from_bytes' in str(ae):
                            module_logger.critical(
                                f"{Fore.RED}FATAL: LlavaImageExtractor missing 'extract_text_from_bytes'. Cannot process S3 images.",
                                exc_info=True)
                            raise ae  # Re-raise critical error
                        else:
                            module_logger.error(
                                f"{Fore.RED}AttributeError during LLaVA processing for {log_prefix}: {ae}",
                                exc_info=True)
                            result_to_return = None
                    except Exception as llava_err:
                        error_type = type(llava_err).__name__
                        module_logger.error(
                            f"{Fore.RED}LLaVA error processing S3 bytes for {log_prefix} ({error_type}): {llava_err}",
                            exc_info=True)
                        result_to_return = None
                else:
                    module_logger.error(
                        f"Unexpected data type received from S3 manager for {log_prefix}: {type(image_data)}")
                    result_to_return = None

            except Exception as e:
                # Catch errors during S3 fetch/outer block (excluding LLaVA specific AttributeError)
                if isinstance(e, AttributeError) and 'extract_text_from_bytes' in str(e):
                    raise e  # Let critical error propagate
                error_type = type(e).__name__
                module_logger.error(
                    f"{Fore.RED}Error during S3 fetch for {log_prefix} (S3 Key: {s3_key}) ({error_type}): {e}",
                    exc_info=True)
                result_to_return = None
            finally:
                latency = asyncio.get_running_loop().time() - start_time
                module_logger.debug(f"Total processing latency for {log_prefix}: {latency:.2f}s")

        # Advance progress regardless of outcome for this pair's records
        if num_records_for_pair > 0:
            progress.update(task_id, advance=num_records_for_pair)
        else:
            module_logger.warning(f"No records found for pair {processing_pair} in index map, progress not advanced.")
        return result_to_return


# --- DynamoDB Update Function ---
async def write_batch_to_dynamo(
        semaphore: asyncio.Semaphore,
        fb_repository: FBArchiveRepository,
        batch: List[Dict],
        batch_number: int
) -> Tuple[int, int]:
    """
    Updates DynamoDB records using individual update_item calls for better error handling and throughput.

    Args:
        semaphore: Limits concurrent DB operations.
        fb_repository: FBArchiveRepository instance for DB operations.
        batch: List of record dictionaries to update.
        batch_number: Batch identifier for logging.

    Returns:
        Tuple of (attempted_count, error_count).
    """
    module_logger = logging.getLogger(__name__ + ".write_batch")
    attempted_count = len(batch)
    errors_in_batch = 0
    successful_updates = 0

    if not batch:
        module_logger.info(f"DB Write Batch {batch_number} is empty, skipping write.")
        return 0, 0

    async with semaphore:
        try:
            module_logger.info(f"Starting individual updates for batch {batch_number} ({attempted_count} items)...")

            for i, item in enumerate(batch):
                try:
                    # Extract the primary key components
                    if 'AdArchiveID' not in item or 'StartDate' not in item:
                        module_logger.error(f"Record {i} in batch {batch_number} missing key fields: {item}")
                        errors_in_batch += 1
                        continue

                    key = {
                        'AdArchiveID': item['AdArchiveID'],
                        'StartDate': item['StartDate']
                    }

                    # Prepare update data (all fields except primary key)
                    update_data = {k: v for k, v in item.items() if k not in ['AdArchiveID', 'StartDate']}

                    if not update_data:
                        module_logger.warning(
                            f"Record {i} in batch {batch_number} has no data to update for key: {key}")
                        continue

                    # Call the async repository method to update a single item
                    success = await fb_repository.update_item(key, update_data)

                    if success:
                        successful_updates += 1
                    else:
                        module_logger.warning(
                            f"Failed to update record {i} in batch {batch_number}: {key}. Check logs for specific error.")
                        errors_in_batch += 1

                except Exception as item_err:
                    module_logger.error(f"Error updating individual record {i} in batch {batch_number}: {item_err}",
                                        exc_info=True)
                    errors_in_batch += 1

            module_logger.info(f"Completed individual updates for batch {batch_number}. "
                               f"Successful: {successful_updates}, Failed: {errors_in_batch}")

        except Exception as batch_err:
            module_logger.error(f"{Fore.RED}--- Critical error processing batch {batch_number} ---{Fore.RESET}",
                                exc_info=True)
            console.print(f"[bold red]Critical error processing batch {batch_number}: {batch_err}[/bold red]")
            # If a critical error occurs for the entire batch, assume all remaining failed
            errors_in_batch = attempted_count - successful_updates

        return attempted_count, errors_in_batch


# --- Main Orchestration Function ---
async def main(args: argparse.Namespace):
    console.print("[bold cyan]M4-Optimized Batch Image Processing[/bold cyan]")

    # Apply command line overrides
    global OLLAMA_CONCURRENCY, BATCH_UPDATE_SIZE
    if args.concurrency:
        OLLAMA_CONCURRENCY = args.concurrency
        logger.info(f"Using command line LLaVA concurrency: {OLLAMA_CONCURRENCY}")
    if args.batch_size:
        BATCH_UPDATE_SIZE = args.batch_size
        logger.info(f"Using command line batch size: {BATCH_UPDATE_SIZE}")

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Debug logging enabled.")

    if args.process_403:
        console.print(
            "[bold yellow]--process-403 flag set: Will attempt to re-process pairs previously marked as 403 Forbidden.[/bold yellow]")
    if args.reprocess_all:
        console.print(
            "[bold yellow]--reprocess-all flag set: Will reprocess ALL images regardless of existing ImageText.[/bold yellow]")
    if args.exclude_target_phrases:
        console.print(
            "[bold yellow]--exclude-target-phrases flag set: Will process items *except* those with target phrases.[/bold yellow]")

    config = load_config('01/01/1970')  # Date is arbitrary, config_adapter typically expects one.

    # Initialize LLaVA Extractor
    llava_model = args.model or config.get('llava_model_name', 'llama3.2-vision:11b')
    llava_extractor = LlavaImageExtractor(
        ollama_base_url=config.get('ollama_base_url', "http://localhost:11434"),
        model_name=llava_model,
        request_timeout=config.get('llava_timeout', 1200),
        default_temperature=config.get('llava_temperature', 0.1),
        num_gpu_layers=OLLAMA_NUM_GPU_LAYERS,
        use_client_semaphore=True,  # LLaVA client manages its own internal semaphore
        client_semaphore_count=OLLAMA_CONCURRENCY
    )
    console.print(f"[cyan]Initialized LLaVA with model: {llava_model}[/cyan]")

    # Initialize S3 Manager
    try:
        s3_bucket_name = config.get('s3_image_bucket', 'lexgenius-dockets')
        s3_manager = S3AsyncStorage(config, bucket_name=s3_bucket_name)
        console.print(f"[cyan]Initialized S3 Manager for bucket: {s3_bucket_name}[/cyan]")
    except KeyError as ke:
        console.print(f"[bold red]Failed to initialize S3 Manager: Missing AWS key in config ({ke})[/bold red]")
        await llava_extractor.close_session()
        return
    except Exception as s3_init_err:
        console.print(f"[bold red]Failed to initialize S3 Manager: {s3_init_err}[/bold red]")
        await llava_extractor.close_session()
        return

    # Semaphores for controlling concurrency
    ollama_semaphore = asyncio.BoundedSemaphore(OLLAMA_CONCURRENCY)
    db_write_semaphore = asyncio.Semaphore(DB_WRITE_CONCURRENCY)

    # --- Database Scanning and Preprocessing ---
    try:
        fb_repository = FBArchiveRepository()
        console.print("[cyan]Scanning local DynamoDB table for FBAdArchive records...[/cyan]")

        async with fb_repository:
            items = await fb_repository.scan_all()
        df = pd.DataFrame(items)
        if df.empty:
            console.print("[yellow]No items found in the local DynamoDB table. Exiting.[/yellow]")
            await llava_extractor.close_session()
            return
        initial_count = len(df)
        console.print(f"[green]Scan complete. Found {initial_count} total records.[/green]")

        console.print("[cyan]Preprocessing data...[/cyan]")
        required_cols = ['AdArchiveID', 'StartDate', 'AdCreativeId']
        missing_req_cols = [col for col in required_cols if col not in df.columns]
        if missing_req_cols:
            console.print(
                f"[bold red]Fatal Error: Missing required columns in DataFrame: {', '.join(missing_req_cols)}.[/bold red]")
            await llava_extractor.close_session()
            return

        # Ensure 'IsForbidden403' column exists and is boolean
        if 'IsForbidden403' not in df.columns:
            df['IsForbidden403'] = False
        else:
            # Convert various potential inputs (None, NaN, strings) to boolean False, keep existing True as True
            df['IsForbidden403'] = df['IsForbidden403'].apply(lambda x: isinstance(x, bool) and x).fillna(False).astype(
                bool)

        # Clean 'ImageText' column
        if 'ImageText' not in df.columns:
            df['ImageText'] = None
        else:
            df['ImageText'] = df['ImageText'].apply(lambda x: str(x) if pd.notna(x) else None)
            # Define values that indicate the text needs reprocessing or is invalid
            values_indicating_reprocessing = ['', 'nan', 'none', 'null', 'na', 'n/a', '<unknown>', '<coroutine object']
            df['ImageText'] = df['ImageText'].replace(values_indicating_reprocessing, None, regex=False)

        # Clean ID columns to ensure they are valid strings
        def clean_id(val):
            if pd.isna(val): return None
            s_val = str(val)
            if isinstance(val, float) and val.is_integer(): s_val = str(int(val))
            if s_val == '0' or not s_val.strip(): return None
            return s_val

        df['AdCreativeId_Clean'] = df['AdCreativeId'].apply(clean_id)
        df['AdArchiveID_Clean'] = df['AdArchiveID'].apply(clean_id)

        # --- FILTERING AND TASK IDENTIFICATION ---
        console.print("[cyan]Identifying records potentially needing processing...[/cyan]")

        # 1. Start with records that have valid cleaned IDs
        df_processable = df.dropna(subset=['AdArchiveID_Clean', 'AdCreativeId_Clean']).copy()
        num_invalid_ids = initial_count - len(df_processable)
        if num_invalid_ids > 0:
            console.print(
                f"[yellow]Note: {num_invalid_ids} records had invalid ArchiveID or CreativeID and were excluded from processing.[/yellow]")
        if df_processable.empty:
            console.print("[bold yellow]No records with valid IDs to process. Exiting.[/bold yellow]")
            await llava_extractor.close_session()
            return

        # 2. Apply --exclude-target-phrases or filter for target phrases
        target_prefixes = ("The image", "This image", "This appears", "This advertisement")
        starts_with_target = df_processable['ImageText'].str.startswith(target_prefixes, na=False)

        if args.exclude_target_phrases:
            df_filtered_by_phrases = df_processable[~starts_with_target].copy()
            logger.info(
                f"Excluded {starts_with_target.sum()} records with ImageText starting with target phrases (--exclude-target-phrases).")
        else:
            df_filtered_by_phrases = df_processable[starts_with_target].copy()
            logger.info(
                f"Kept {starts_with_target.sum()} records with ImageText starting with target phrases (default behavior).")

        if df_filtered_by_phrases.empty:
            console.print("[bold yellow]No records remaining after target phrase filtering. Exiting.[/bold yellow]")
            await llava_extractor.close_session()
            return

        # 3. Determine which records need LLaVA processing based on content and flags
        if args.reprocess_all:
            # Force reprocessing of all filtered records
            records_to_process = df_filtered_by_phrases.copy()
            console.print("[bold yellow]--reprocess-all flag set: Re-processing ALL filtered images.[/bold yellow]")
        else:
            # Default: process if ImageText is missing/invalid OR if it's a known 403 (and --process-403 is set)
            needs_text_mask = df_filtered_by_phrases['ImageText'].isna()
            potential_process_mask = needs_text_mask.copy()

            if args.process_403:
                # If flag is set, also consider rows currently marked as 403 for reprocessing
                potential_process_mask |= df_filtered_by_phrases['IsForbidden403']

            records_to_process = df_filtered_by_phrases[potential_process_mask].copy()
            processed_initially = len(df_processable) - len(records_to_process)
            console.print(
                f"[cyan]Filtered out {processed_initially} records with valid text and not marked as 403 (or --process-403 not set).[/cyan]")

        if records_to_process.empty:
            console.print("[bold yellow]No records need LLaVA processing after all filters. Exiting.[/bold yellow]")
            await llava_extractor.close_session()
            return

        # 4. Identify unique (ArchiveID, CreativeID) pairs from the final set of records to process
        console.print("[cyan]Identifying unique AdArchiveID/AdCreativeId pairs needing processing...[/cyan]")
        grouped_by_pair = records_to_process.groupby(['AdArchiveID_Clean', 'AdCreativeId_Clean'])
        initial_unique_pairs_to_consider = list(grouped_by_pair.groups.keys())

        # Create a map from (archive_id, creative_id) tuple to list of original DataFrame indices
        # This map includes ALL records matching the pair, not just the "to process" ones,
        # so that progress can be updated correctly for all associated records.
        pair_to_indices: Dict[Tuple[str, str], List[int]] = {
            pair: group.index.tolist()
            for pair, group in df.groupby(['AdArchiveID_Clean', 'AdCreativeId_Clean'])
        }

        # 5. Final filtering of unique pairs based on --process-403
        final_pairs_to_process: List[Tuple[str, str]] = []
        skipped_403_pairs_count = 0
        known_403_map: Dict[Tuple[str, str], bool] = {}  # Map to store initial 403 status for each unique pair

        for pair in initial_unique_pairs_to_consider:
            indices = pair_to_indices.get(pair, [])
            is_forbidden = False
            if indices:
                # Check if ANY record associated with this pair in the *original* DataFrame is marked True for IsForbidden403
                is_forbidden = df.loc[indices, 'IsForbidden403'].any()

            known_403_map[pair] = is_forbidden

            if is_forbidden and not args.process_403:
                skipped_403_pairs_count += 1
                logger.debug(f"Skipping pair {pair} due to existing IsForbidden403=True and --process-403 not set.")
            else:
                final_pairs_to_process.append(pair)

        if skipped_403_pairs_count > 0:
            console.print(
                f"[yellow]Skipped {skipped_403_pairs_count} unique pairs due to existing IsForbidden403=True. Use --process-403 to include them.[/yellow]")

        num_unique_pairs_to_process = len(final_pairs_to_process)
        if num_unique_pairs_to_process == 0:
            console.print("[bold yellow]No pairs remaining to process after all filtering. Exiting.[/bold yellow]")
            await llava_extractor.close_session()
            return

        # Calculate total records for the progress bar based *only* on pairs being processed
        total_records_for_progress = sum(len(pair_to_indices[pair]) for pair in final_pairs_to_process)

        console.print(
            f"[green]Will process {num_unique_pairs_to_process} unique image pairs, affecting {total_records_for_progress} records.[/green]")

        # --- Process LLaVA and Write to DB in Batches ---
        total_update_errors = 0
        total_successful_extractions = 0
        total_records_updated_in_df = 0
        total_marked_as_403 = 0
        processed_despite_initial_403 = 0

        console.print(
            f"[cyan]Starting LLaVA extraction and DB writes "
            f"(LLaVA Concurrency: {OLLAMA_CONCURRENCY}, DB Concurrency: {DB_WRITE_CONCURRENCY}, Batch Size: {BATCH_UPDATE_SIZE})...[/cyan]"
        )

        with Progress(SpinnerColumn(), "{task.description}", BarColumn(), "{task.percentage:>3.0f}%",
                      TimeRemainingColumn(), "{task.completed}/{task.total} records",
                      console=console) as progress:
            llava_progress_task_id = progress.add_task(
                "[yellow]Processing Records (S3/LLaVA + DB)",
                total=total_records_for_progress
            )

            db_write_tasks_list = []

            # Iterate through the FINAL list of pairs to process in chunks
            for i in range(0, num_unique_pairs_to_process, BATCH_UPDATE_SIZE):
                chunk_pairs = final_pairs_to_process[i: i + BATCH_UPDATE_SIZE]
                logger.info(f"Processing LLaVA chunk {i // BATCH_UPDATE_SIZE + 1} ({len(chunk_pairs)} unique pairs)")

                llava_chunk_tasks = []
                for archive_id, creative_id in chunk_pairs:
                    pair_key = (archive_id, creative_id)
                    is_initial_403_for_pair = known_403_map.get(pair_key, False)
                    task = asyncio.create_task(extract_image_text_with_semaphore(
                        ollama_semaphore,
                        llava_extractor,
                        s3_manager,
                        archive_id,
                        creative_id,
                        progress,
                        llava_progress_task_id,
                        pair_to_indices,
                        is_initial_403_for_pair,
                        args.process_403
                    ))
                    llava_chunk_tasks.append((pair_key, task))

                # Wait for LLaVA tasks *in this chunk* to complete
                await asyncio.gather(*(task for _, task in llava_chunk_tasks))

                # --- Process results and prepare DB write for the completed chunk ---
                items_for_db_batch = []
                indices_updated_in_chunk = set()  # Track indices modified in this chunk (ImageText or IsForbidden403 status)
                chunk_successful_extractions = 0
                chunk_marked_as_403 = 0

                for processing_pair, task in llava_chunk_tasks:
                    try:
                        result = task.result()  # Will be text (str), FORBIDDEN_403_MARKER, or None
                        indices_for_pair = pair_to_indices.get(processing_pair, [])
                        is_initial_403_for_pair = known_403_map.get(processing_pair, False)

                        if not indices_for_pair:
                            logger.error(f"Pair {processing_pair} not found in index map during result processing!")
                            continue

                        if result == FORBIDDEN_403_MARKER:
                            chunk_marked_as_403 += 1
                            # Update DF: Set IsForbidden403 to True for these indices if not already True
                            needs_update_indices = df.loc[indices_for_pair][
                                ~df.loc[indices_for_pair, 'IsForbidden403']].index
                            if not needs_update_indices.empty:
                                df.loc[needs_update_indices, 'IsForbidden403'] = True
                                logger.info(
                                    f"{Fore.MAGENTA}Marked {len(needs_update_indices)} records for pair {processing_pair} as IsForbidden403=True.{Fore.RESET}")
                                indices_updated_in_chunk.update(needs_update_indices)
                            else:
                                logger.debug(f"Pair {processing_pair} already marked as IsForbidden403=True in DF.")

                        elif isinstance(result, str):  # Successful extraction (can be empty string)
                            chunk_successful_extractions += 1
                            if is_initial_403_for_pair and args.process_403:
                                processed_despite_initial_403 += 1

                            # Update DF: Set ImageText and ensure IsForbidden403 is False
                            current_values = df.loc[indices_for_pair, ['ImageText', 'IsForbidden403']]
                            needs_update_mask = (
                                    (current_values['ImageText'] != result) |  # Text is different
                                    (current_values['IsForbidden403'])  # Or was previously 403
                            )
                            needs_update_indices = current_values[needs_update_mask].index

                            if not needs_update_indices.empty:
                                df.loc[needs_update_indices, 'ImageText'] = result
                                df.loc[needs_update_indices, 'IsForbidden403'] = False  # Mark as not forbidden
                                logger.debug(
                                    f"Updated ImageText/IsForbidden403 for {len(needs_update_indices)} records for pair {processing_pair}.")
                                indices_updated_in_chunk.update(needs_update_indices)
                            else:
                                logger.debug(
                                    f"Pair {processing_pair}: Extracted text matched existing or no update needed in DF.")
                        else:  # Result is None (S3 fail, LLaVA fail, etc. not related to 403)
                            # If it was marked 403 but wasn't processed due to --process-403, don't change.
                            # Otherwise, ensure IsForbidden403 is False if it was True, as it's not a 403.
                            if not (is_initial_403_for_pair and not args.process_403):
                                needs_update_indices = df.loc[indices_for_pair][
                                    df.loc[indices_for_pair, 'IsForbidden403']].index
                                if not needs_update_indices.empty:
                                    df.loc[needs_update_indices, 'IsForbidden403'] = False
                                    logger.debug(
                                        f"Marked {len(needs_update_indices)} records for pair {processing_pair} as IsForbidden403=False due to non-403 failure.")
                                    indices_updated_in_chunk.update(needs_update_indices)

                    except Exception as result_err:
                        logger.error(f"Error processing result for pair {processing_pair}: {result_err}", exc_info=True)

                total_successful_extractions += chunk_successful_extractions
                total_marked_as_403 += chunk_marked_as_403
                total_records_updated_in_df += len(indices_updated_in_chunk)

                # --- Prepare and schedule DB write for modified records in this chunk ---
                if indices_updated_in_chunk:
                    logger.debug(
                        f"Preparing DB items for {len(indices_updated_in_chunk)} updated indices in chunk {i // BATCH_UPDATE_SIZE + 1}")
                    for index in indices_updated_in_chunk:
                        if index in df.index:
                            try:
                                row_dict = df.loc[index].to_dict()
                                item = prepare_dynamodb_item(row_dict)
                                if item:
                                    items_for_db_batch.append(item)
                                else:
                                    logger.warning(
                                        f"prepare_dynamodb_item returned None for index {index} (ArchiveID: {row_dict.get('AdArchiveID_Clean', 'N/A')}). Skipping DB add.")
                            except Exception as prep_err:
                                archive_id_err = df.loc[index].get('AdArchiveID_Clean', 'N/A')
                                creative_id_err = df.loc[index].get('AdCreativeId_Clean', 'N/A')
                                logger.error(
                                    f"Error preparing item for index {index} (Pair: {archive_id_err}/{creative_id_err}): {prep_err}",
                                    exc_info=True)
                        else:
                            logger.warning(
                                f"Index {index} not found in DataFrame during DB prep for chunk. May have been filtered out earlier.")

                    if items_for_db_batch:
                        logger.info(
                            f"Scheduling DB write for chunk {i // BATCH_UPDATE_SIZE + 1} ({len(items_for_db_batch)} items)")
                        db_task = asyncio.create_task(write_batch_to_dynamo(
                            db_write_semaphore, fb_repository, items_for_db_batch,
                            i // BATCH_UPDATE_SIZE + 1
                        ))
                        db_write_tasks_list.append(db_task)
                    else:
                        logger.info(
                            f"No valid DB items prepared for chunk {i // BATCH_UPDATE_SIZE + 1} after filtering invalid ones.")
                else:
                    logger.info(
                        f"No records needed update in DataFrame for chunk {i // BATCH_UPDATE_SIZE + 1}. No DB write scheduled.")

            # --- Wait for all background DB writes to complete ---
            console.print("[cyan]Waiting for all pending DynamoDB writes to complete...[/cyan]")
            total_records_attempted_write = 0
            total_update_errors = 0
            if db_write_tasks_list:
                db_results: List[Tuple[int, int]] = await asyncio.gather(*db_write_tasks_list)
                for attempted, errors in db_results:
                    total_records_attempted_write += attempted
                    total_update_errors += errors
            total_records_successfully_written = total_records_attempted_write - total_update_errors

        # --- Final Summary ---
        console.print("\n" + "=" * 30 + " Summary " + "=" * 30)
        console.print(f"Total Records Scanned Initially: {initial_count}")
        console.print(f"Records with valid IDs and considered for processing: {len(df_processable)}")
        console.print(
            f"Unique Image Pairs Initially Identified for Consideration: {len(initial_unique_pairs_to_consider)}")
        if not args.process_403:
            console.print(f"Unique Pairs Skipped Due To IsForbidden403: {skipped_403_pairs_count}")
        console.print(f"Unique Image Pairs Attempted Processing (Final Count): {num_unique_pairs_to_process}")
        console.print(f"  - Pairs Resulting in S3 Access Denied (403/Not Found): {total_marked_as_403}")
        if args.process_403:
            console.print(f"  - Pairs Processed Successfully Despite Initial 403: {processed_despite_initial_403}")
        console.print(f"  - Pairs with Successful LLaVA Extractions: {total_successful_extractions}")
        console.print(f"Total Records Updated in DataFrame (Text or 403 Status): {total_records_updated_in_df}")
        console.print(f"Total Records Attempted Write to DB: {total_records_attempted_write}")
        console.print(f"Total Records Successfully Written to DB (Estimated): {total_records_successfully_written}")
        if total_update_errors > 0:
            console.print(
                f"[bold red]Errors During DB Write: {total_update_errors} records failed (estimated).[/bold red]")
        else:
            console.print(f"[green]DB Write Errors: 0.[/green]")
        console.print("=" * 70)

    except Exception as e:
        logger.critical("Unhandled exception in main:", exc_info=True)
        console.print(f"\n[bold red]Critical error in main: {e}[/bold red]")
        console.print_exception(show_locals=False, word_wrap=True)
    finally:
        if 'llava_extractor' in locals() and llava_extractor:
            await llava_extractor.close_session()
            logger.info("LLaVA Extractor session closed.")
        console.print("[bold green]--- Script Finished ---[/bold green]")


# --- Argument Parser and Script Entry Point ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Batch process FB Ad Archive images using LLaVA, fetching from S3 and updating DynamoDB.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter  # Show default values in help
    )
    parser.add_argument(
        '--process-403',
        action='store_true',
        help="Include records previously marked with IsForbidden403=True in the processing run."
    )
    parser.add_argument(
        '--reprocess-all',
        action='store_true',
        help="Force reprocessing of all images that pass initial filtering, including those with existing ImageText."
    )
    parser.add_argument(
        '--debug',
        action='store_true',
        help="Enable debug logging for more verbose output."
    )
    parser.add_argument(
        '--model',
        type=str,
        choices=['llama3.2-vision-ocr', 'llama3.2-vision:11b', 'llama3.2-vision:11b-instruct-q4_K_M', 'llava:7b',
                 'llava:7b-v1.6-mistral-q4_0'],
        help="Vision model to use for image text extraction. Overrides config value."
    )
    parser.add_argument(
        '--concurrency',
        type=int,
        default=OLLAMA_CONCURRENCY,  # Default from global variable
        help="Number of concurrent LLaVA vision model requests."
    )
    parser.add_argument(
        '--batch-size',
        type=int,
        default=BATCH_UPDATE_SIZE,  # Default from global variable
        help="Number of unique image pairs to process per LLaVA/DB write batch."
    )
    parser.add_argument(
        '--exclude-target-phrases',
        action='store_true',
        help="Filter items such that only those *NOT* starting with specific target phrases ('The image', etc.) in ImageText are processed. "
             "By default, only items *starting* with these phrases are processed."
    )
    parsed_args = parser.parse_args()

    try:
        # Pass parsed arguments to main asynchronous function
        asyncio.run(main(parsed_args))
    except KeyboardInterrupt:
        logger.warning("Script interrupted by user.")
        console.print("\n[bold yellow]Script interrupted by user.[/bold yellow]")
    except AttributeError as fatal_ae:
        # Catch specific critical errors like missing methods
        if 'extract_text_from_bytes' in str(fatal_ae):
            logger.critical(f"Script stopped due to missing method in LlavaImageExtractor: {fatal_ae}", exc_info=True)
            console.print(
                f"\n[bold red]FATAL ERROR: LlavaImageExtractor class needs the method 'extract_text_from_bytes'. "
                f"Please check your src/lib/llava_vision.py implementation.[/bold red]")
        else:
            logger.critical("Top-level script error (AttributeError):", exc_info=True)
            console.print(f"\n[bold red]Critical error: {fatal_ae}[/bold red]")
            console.print_exception(show_locals=False, word_wrap=True)
    except Exception as e:
        logger.critical("Top-level script error:", exc_info=True)
        console.print(f"\n[bold red]Critical error: {e}[/bold red]")
        console.print_exception(show_locals=False, word_wrap=True)