#!/usr/bin/env python3
"""
Scans a local DynamoDB table for image pHashes, calculates pairwise Hamming distances
in parallel, groups similar images based on distance, categorizes pairs by thresholds,
and saves both a detailed JSON report and a visual HTML report with grouped thumbnails.
"""
import argparse
import json
import logging
import multiprocessing
import os
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from decimal import Decimal
from functools import partial
from typing import Dict, List, Tuple, Any, Set

# --- Required Libraries ---
import boto3
import imagehash # For pHash calculations
import networkx as nx # For grouping/clustering
from botocore.exceptions import ClientError
from jinja2 import Environment, BaseLoader # For HTML report generation
from rich.logging import RichHandler # For nice console logging
from tqdm import tqdm # For progress bars

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',
                    handlers=[RichHandler(rich_tracebacks=True, show_path=False)]) # Hide path for cleaner logs
logger = logging.getLogger(__name__)

# Hamming Distance Thresholds for categorization in reports
THRESHOLDS = {
    "exact_0": (0, 0),
    "near_1_to_4": (1, 4),
    "similar_5_to_10": (5, 10),
}
# Max distance to consider two images part of the same group in the graph
MAX_DISTANCE_FOR_GROUPING = 10
# Default Image Extension
DEFAULT_IMAGE_EXTENSION = "jpg"

# --- Helper: Custom JSON Encoder ---
class DecimalEncoder(json.JSONEncoder):
    """ Custom JSON encoder to handle Decimal types from DynamoDB """
    def default(self, o):
        if isinstance(o, Decimal):
            if o % 1 == 0: return int(o)
            else: return float(o)
        if isinstance(o, imagehash.ImageHash): # Should not happen in final output
            return str(o)
        return super(DecimalEncoder, self).default(o)

# --- Helper: DynamoDB Connection ---
def get_local_dynamodb_resource(port: int, region: str):
    """Initializes and returns a DynamoDB resource connected to localhost."""
    endpoint_url = f'http://localhost:{port}'
    logger.info(f"Connecting to local DynamoDB at {endpoint_url} (Region: {region})")
    try:
        session = boto3.Session(aws_access_key_id='dummy', aws_secret_access_key='dummy', region_name=region)
        resource = session.resource('dynamodb', endpoint_url=endpoint_url)
        logger.info("DynamoDB resource created via specific session.")
        return resource
    except Exception as e:
        logger.error(f"Failed to create DynamoDB resource for endpoint {endpoint_url}: {e}", exc_info=True)
        logger.error("Ensure the local DynamoDB instance is running and accessible on the specified port.")
        sys.exit(1)

# --- Worker Function for Parallel Scan ---
def scan_segment_worker(
        table_name: str, segment_id: int, total_segments: int, dynamodb_resource
) -> List[Dict]:
    """Scans a single segment of the DynamoDB table."""
    segment_items = []
    try:
        table = dynamodb_resource.Table(table_name)
        logger.debug(f"ScanWorker-{segment_id}: Starting segment {segment_id}/{total_segments - 1}")
        scan_kwargs = {'Segment': segment_id, 'TotalSegments': total_segments}
        items_in_segment = 0
        while True:
            response = table.scan(**scan_kwargs)
            items = response.get('Items', [])
            segment_items.extend(items)
            items_in_segment += len(items)
            last_evaluated_key = response.get('LastEvaluatedKey')
            if not last_evaluated_key: break
            logger.debug(f"ScanWorker-{segment_id}: Paginating...")
            scan_kwargs['ExclusiveStartKey'] = last_evaluated_key
        logger.debug(f"ScanWorker-{segment_id}: Finished segment {segment_id}. Found {items_in_segment} items.")
        return segment_items
    except ClientError as e: logger.error(f"ScanWorker-{segment_id}: ClientError: {e}", exc_info=True); return []
    except Exception as e: logger.error(f"ScanWorker-{segment_id}: Unexpected error: {e}", exc_info=True); return []


# --- Data Preparation Function ---
def prepare_hash_data(items: List[Dict[str, Any]]) -> List[Tuple[Tuple[str, str], str, str, imagehash.ImageHash]]:
    """Converts raw DynamoDB items into usable tuples with ImageHash objects."""
    prepared_data = []
    processed_ids = set()
    logger.info("Preparing hash data and converting hex strings...")
    if not items: return []
    phash_key = 'PHash'; archive_id_key = 'AdArchiveID'; creative_id_key = 'AdCreativeID'
    for item in tqdm(items, desc="Preparing Hashes", unit="item", disable=len(items)<1000):
        p_hash_str = item.get(phash_key); archive_id = item.get(archive_id_key); creative_id = item.get(creative_id_key)
        if not p_hash_str or not archive_id or not creative_id:
            # Reduce logging noise for missing fields unless debugging
            # logger.warning(f"Skipping item missing fields. Item: {item.get(archive_id_key, 'UnknownID')}")
            continue
        unique_id = (str(archive_id), str(creative_id))
        if unique_id in processed_ids: continue
        processed_ids.add(unique_id)
        try:
            hash_obj = imagehash.hex_to_hash(p_hash_str)
            prepared_data.append((unique_id, str(archive_id), str(creative_id), hash_obj))
        except (ValueError, TypeError) as e: logger.warning(f"Skipping ID {unique_id}: Invalid pHash '{p_hash_str}'. Err: {e}")
        except Exception as e: logger.warning(f"Skipping ID {unique_id}: Error parsing hash '{p_hash_str}'. Err: {e}")
    logger.info(f"Prepared {len(prepared_data)} valid unique hash entries.")
    if not prepared_data: logger.warning("No valid data prepared.")
    return prepared_data


# --- Parallel Hamming Distance Comparison Worker ---
def compare_hashes_worker(
    indices_chunk: range,
    all_hashes_data: List[Tuple[Tuple[str, str], str, str, imagehash.ImageHash]]
) -> Dict[str, List[Tuple[Tuple[str, str], Tuple[str, str], int]]]:
    """Compares a subset of hashes against subsequent hashes."""
    results = {key: [] for key in THRESHOLDS}
    n = len(all_hashes_data)
    chunk_comparisons = 0
    for i in indices_chunk:
        id1, _, _, hash1 = all_hashes_data[i]
        for j in range(i + 1, n):
            id2, _, _, hash2 = all_hashes_data[j]
            chunk_comparisons += 1
            try:
                distance = hash1 - hash2
            except Exception as e:
                 # Log error sparingly or use a queue for centralized logging from processes
                 # print(f"PID {os.getpid()} Warning: Dist calc err: {e}", file=sys.stderr)
                 continue
            # Add to all relevant threshold categories
            for key, (min_dist, max_dist) in THRESHOLDS.items():
                if min_dist <= distance <= max_dist:
                    results[key].append((id1, id2, distance))
                    if distance == 0: break # Exact match, no need to check other thresholds
    # Optional: Log worker completion and comparisons done by this worker
    # logger.debug(f"Worker for indices {indices_chunk.start}-{indices_chunk.stop-1} finished. Compared: {chunk_comparisons}")
    return results


# --- CDN URL Helper Function ---
def construct_cdn_url(base_url: str, archive_id: str, creative_id: str, extension: str) -> str:
    """Constructs the CDN URL based on provided components."""
    # --- ADJUST THIS FORMATTING AS NEEDED ---
    # Current assumption: BASE_URL/{archive_id}_{creative_id}.{extension}
    url_path = f"{archive_id}_{creative_id}.{extension}"
    # --- End Adjustment ---
    base_url = base_url.rstrip('/') + '/'
    url_path = url_path.lstrip('/')
    return base_url + url_path


# --- Grouping Function ---
def group_similar_images(
    all_pairs: List[Tuple[Tuple[str, str], Tuple[str, str], int]],
    all_data_map: Dict[Tuple[str, str], Dict[str, str]],
    cdn_base_url: str,
    img_extension: str
) -> List[List[Dict[str, str]]]:
    """Groups images using NetworkX graph based on MAX_DISTANCE_FOR_GROUPING."""
    if not all_pairs: return []
    logger.info(f"Building similarity graph (distance <= {MAX_DISTANCE_FOR_GROUPING})...")
    G = nx.Graph()
    nodes_in_graph = set()
    for id1, id2, distance in all_pairs:
        if distance <= MAX_DISTANCE_FOR_GROUPING:
            G.add_edge(id1, id2); nodes_in_graph.update([id1, id2])
    if not G.nodes: return []
    logger.info(f"Graph: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges.")
    logger.info("Finding connected components (groups)...")
    connected_components = list(nx.connected_components(G))
    groups = []
    for component in connected_components:
        if len(component) > 1:
            group_details_with_url = []
            for unique_id in component:
                 details = all_data_map.get(unique_id)
                 if details:
                     archive_id = details['archive_id']
                     creative_id = details['creative_id']
                     cdn_url = construct_cdn_url(cdn_base_url, archive_id, creative_id, img_extension)
                     group_details_with_url.append({
                         "archive_id": archive_id,
                         "creative_id": creative_id,
                         "cdn_url": cdn_url
                     })
                 else: logger.warning(f"Details not found for unique_id {unique_id} in group.")
            if group_details_with_url: groups.append(group_details_with_url)
    logger.info(f"Found {len(groups)} similarity groups with >1 member.")
    return groups


# --- HTML Report Generation ---

# Basic HTML Template using Jinja2 syntax
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Similarity Report - {{ metadata.dynamodb_table }}</title>
    <style>
        body { font-family: sans-serif; line-height: 1.6; margin: 20px; background-color: #f8f9fa; color: #333; }
        h1, h2, h3 { color: #0056b3; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        h1 { text-align: center; margin-bottom: 30px; }
        .metadata, .group, .pair-category { background-color: #fff; border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .metadata p { margin: 5px 0; }
        .metadata strong { color: #555; min-width: 200px; display: inline-block;}
        .group-container { display: flex; flex-wrap: wrap; gap: 15px; margin-top: 10px; padding-top: 10px; border-top: 1px dashed #eee; }
        .image-item { text-align: center; border: 1px solid #eee; padding: 10px; border-radius: 4px; background-color: #fdfdfd; }
        .image-item a { text-decoration: none; color: #007bff; }
        .image-item img.thumbnail { max-width: 150px; max-height: 150px; height: auto; display: block; margin: 0 auto 5px auto; border: 1px solid #ddd; }
        .image-item p { font-size: 0.8em; margin: 2px 0; color: #666; word-break: break-all; }
        .pair-container { display: flex; align-items: center; gap: 10px; border-bottom: 1px dotted #eee; padding: 5px 0; }
        .pair-container .image-item { flex-shrink: 0; } /* Prevent items from shrinking too much */
        .pair-distance { font-weight: bold; margin-left: 15px; }
        .no-groups { font-style: italic; color: #777; }
    </style>
</head>
<body>
    <h1>Image Similarity Report</h1>

    <div class="metadata">
        <h2>Metadata</h2>
        {% for key, value in metadata.items() %}
            {% if key != 'summary_counts' and key != 'thresholds_used' %}
                <p><strong>{{ key.replace('_', ' ').title() }}:</strong> {{ value }}</p>
            {% endif %}
        {% endfor %}
        <p><strong>Max Distance For Grouping:</strong> {{ metadata.max_distance_for_grouping }}</p>
        <h3>Thresholds Used:</h3>
        <ul>
        {% for name, (min_d, max_d) in metadata.thresholds_used.items() %}
            <li>{{ name.replace('_', ' ').title() }}: {{ min_d }} - {{ max_d }}</li>
        {% endfor %}
        </ul>
         <h3>Summary Counts (Pairs Found):</h3>
        <ul>
        {% for name, count in metadata.summary_counts.items() %}
             <li>{{ name.replace('_', ' ').title() }}: {{ "{:,}".format(count) }}</li>
        {% endfor %}
        </ul>
    </div>

    <h2>Similarity Groups (Distance <= {{ metadata.max_distance_for_grouping }})</h2>
    <div class="groups">
        {% if similarity_groups %}
            {% for group in similarity_groups %}
            <div class="group">
                <h3>Group {{ loop.index }} ({{ group | length }} images)</h3>
                <div class="group-container">
                    {% for image in group %}
                    <div class="image-item">
                        <a href="{{ image.cdn_url }}" target="_blank" title="Open full image">
                            <img src="{{ image.cdn_url }}" alt="Archive: {{ image.archive_id }}, Creative: {{ image.creative_id }}" class="thumbnail" loading="lazy">
                        </a>
                        <p>Archive: {{ image.archive_id }}</p>
                        <p>Creative: {{ image.creative_id }}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <p class="no-groups">No similarity groups found within the specified distance.</p>
        {% endif %}
    </div>

    {# Optional: Add Detailed Pairs Section (Uncomment if needed)
    <h2>Detailed Pairs by Threshold</h2>
    {% for category, pairs in detailed_pairs_by_threshold.items() %}
        <div class="pair-category">
            <h3>{{ category.replace('_', ' ').title() }} ({{ pairs | length }} pairs)</h3>
            {% if pairs %}
                {% for pair_info in pairs %}
                    <div class="pair-container">
                        {% for image in pair_info[:2] %} {# Only first two are image dicts #}
                        <div class="image-item">
                            <a href="{{ image.cdn_url }}" target="_blank">
                                <img src="{{ image.cdn_url }}" alt="Archive: {{ image.archive_id }}, Creative: {{ image.creative_id }}" class="thumbnail" loading="lazy">
                            </a>
                             <p>Arch:{{ image.archive_id }} Cr:{{ image.creative_id }}</p>
                        </div>
                        {% endfor %}
                        {% if pair_info | length > 2 %} {# Check if distance is included #}
                            <span class="pair-distance">Dist: {{ pair_info[2] }}</span>
                        {% endif %}
                    </div>
                {% endfor %}
            {% else %}
                <p>No pairs found for this threshold.</p>
            {% endif %}
        </div>
    {% endfor %}
    #}

</body>
</html>
"""

def generate_html_report(report_data: Dict, output_path: str):
    """Generates the HTML report from the report data using Jinja2."""
    logger.info(f"Generating HTML report at '{output_path}'...")
    try:
        # Setup Jinja2 environment
        env = Environment(loader=BaseLoader())
        template = env.from_string(HTML_TEMPLATE)

        # Render the template
        html_content = template.render(
            metadata=report_data.get('metadata', {}),
            similarity_groups=report_data.get('similarity_groups', []),
            detailed_pairs_by_threshold=report_data.get('detailed_pairs_by_threshold', {})
        )

        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        logger.info(f"HTML report successfully generated: {output_path}")

    except ImportError:
        logger.error("Jinja2 library is not installed. Cannot generate HTML report.")
        logger.error("Please install it using: pip install Jinja2")
    except Exception as e:
        logger.error(f"Failed to generate HTML report: {e}", exc_info=True)


# --- Main Execution ---
def main():
    parser = argparse.ArgumentParser(
        description="Scan DynamoDB, compute Hamming distances, group, save JSON and HTML reports.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    # --- Arguments ---
    parser.add_argument("-t", "--table-name", required=True, help="DynamoDB table name.")
    parser.add_argument("-p", "--port", type=int, default=8000, help="Local DynamoDB port.")
    parser.add_argument("-r", "--region", default="us-west-2", help="AWS Region.")
    parser.add_argument("--scan-workers", type=int, default=max(4, (os.cpu_count() or 1)), help="Workers for scan.")
    parser.add_argument("--compare-workers", type=int, default=max(1, (os.cpu_count() or 1) - 1), help="Processes for comparison.")
    parser.add_argument("-o", "--output", default="hamming_distance_report.json", help="Output JSON report file.")
    # --- Added/Modified Arguments ---
    parser.add_argument("--html-output", default="hamming_distance_report.html", help="Output HTML report file path (optional). Set to 'none' to disable.")
    parser.add_argument("--cdn-base-url", required=True, help="Base URL for image CDN links (e.g., 'https://cdn.example.com/images').")
    parser.add_argument("--img-ext", default=DEFAULT_IMAGE_EXTENSION, help="Image file extension for CDN URLs.")
    parser.add_argument("--debug", action='store_true', help="Enable debug logging.")

    args = parser.parse_args()

    # --- Configure Logging ---
    log_level = logging.DEBUG if args.debug else logging.INFO
    logger.setLevel(log_level)
    # Ensure RichHandler level is also set if root logger level changes after basicConfig
    for handler in logging.getLogger().handlers:
        if isinstance(handler, RichHandler): handler.setLevel(log_level)
    logger.info(f"Logging level set to: {logging.getLevelName(log_level)}")

    # --- Validate Workers & CDN URL ---
    if args.scan_workers <= 0: logger.error("Scan workers must be > 0."); sys.exit(1)
    if args.compare_workers <= 0: logger.error("Compare workers must be > 0."); sys.exit(1)
    cdn_base_url = args.cdn_base_url.rstrip('/')
    if not cdn_base_url.startswith(('http://', 'https://')): logger.error("CDN URL must start with http/https."); sys.exit(1)
    logger.info(f"Scan Workers: {args.scan_workers}, Compare Workers: {args.compare_workers}, CDN Base: {cdn_base_url}/")

    # --- Connect to DynamoDB ---
    dynamodb_resource = get_local_dynamodb_resource(args.port, args.region)
    try:
        table = dynamodb_resource.Table(args.table_name); table.load()
        logger.info(f"Connected to local table '{args.table_name}'.")
    except Exception as e: logger.error(f"Error connecting to table '{args.table_name}': {e}", exc_info=True); sys.exit(1)

    # --- 1. Scan ---
    scan_start_time = time.time()
    # (Scan logic remains the same as previous version)
    all_items_raw = []
    scan_futures = []
    with ThreadPoolExecutor(max_workers=args.scan_workers, thread_name_prefix="scan_wrkr") as executor:
        for i in range(args.scan_workers): scan_futures.append(executor.submit(scan_segment_worker, args.table_name, i, args.scan_workers, dynamodb_resource))
        for future in tqdm(as_completed(scan_futures), total=len(scan_futures), desc="Scanning", unit="seg"):
            try: all_items_raw.extend(future.result())
            except Exception as exc: logger.error(f"Scan future error: {exc}", exc_info=True)
    scan_elapsed = time.time() - scan_start_time
    logger.info(f"Scan completed in {scan_elapsed:.2f}s. Items: {len(all_items_raw):,}")
    if not all_items_raw: logger.warning("No items found. Exiting."); sys.exit(0)

    # --- 2. Prepare Data ---
    prep_start_time = time.time()
    prepared_data = prepare_hash_data(all_items_raw)
    prep_elapsed = time.time() - prep_start_time
    logger.info(f"Data preparation took {prep_elapsed:.2f}s.")
    if not prepared_data: logger.warning("No valid data after preparation. Exiting."); sys.exit(0)
    n_items = len(prepared_data)
    logger.info(f"Comparing {n_items:,} unique image hashes.")

    # --- 3. Compare Hashes ---
    compare_start_time = time.time()
    # (Comparison logic remains the same)
    chunk_size = max(1, n_items // args.compare_workers)
    index_ranges = [range(i, min(i + chunk_size, n_items)) for i in range(0, n_items, chunk_size) if i < n_items]
    if not index_ranges and n_items > 0: index_ranges = [range(0, n_items)]
    worker_func = partial(compare_hashes_worker, all_hashes_data=prepared_data)
    aggregated_results = {key: [] for key in THRESHOLDS}
    total_expected = n_items * (n_items - 1) // 2 if n_items > 1 else 0
    logger.info(f"Expecting ~ {total_expected:,} comparisons.")
    pool_results = []
    try:
        with multiprocessing.Pool(processes=args.compare_workers) as pool:
            results_iter = pool.imap_unordered(worker_func, index_ranges)
            pbar = tqdm(total=len(index_ranges), desc="Comparing", unit="chunk")
            for res in results_iter: pool_results.append(res); pbar.update(1)
            pbar.close()
    except Exception as e: logger.error(f"Parallel comparison error: {e}", exc_info=True)
    for res in pool_results:
        for key, pairs in res.items(): aggregated_results[key].extend(pairs)
    compare_elapsed = time.time() - compare_start_time
    logger.info(f"Comparison finished in {compare_elapsed:.2f}s.")

    # --- 4. Summarize & Prepare Output (with URLs) ---
    logger.info("--- Hamming Distance Summary ---")
    output_categorized_pairs = {}
    all_pairs_for_grouping = []
    id_to_details_map = {item[0]: {"archive_id": item[1], "creative_id": item[2]} for item in prepared_data}
    for key, pairs in aggregated_results.items():
        count = len(pairs)
        logger.info(f"Threshold '{key}' ({THRESHOLDS[key][0]}-{THRESHOLDS[key][1]}): Found {count:,} pairs")
        output_pairs_with_urls = []
        for id1, id2, dist in pairs:
             d1 = id_to_details_map.get(id1); d2 = id_to_details_map.get(id2)
             if d1 and d2:
                 url1 = construct_cdn_url(cdn_base_url, d1['archive_id'], d1['creative_id'], args.img_ext)
                 url2 = construct_cdn_url(cdn_base_url, d2['archive_id'], d2['creative_id'], args.img_ext)
                 img1 = {"archive_id": d1['archive_id'], "creative_id": d1['creative_id'], "cdn_url": url1}
                 img2 = {"archive_id": d2['archive_id'], "creative_id": d2['creative_id'], "cdn_url": url2}
                 pair_info = [img1, img2]
                 if key != "exact_0": pair_info.append(dist)
                 output_pairs_with_urls.append(pair_info)
                 if dist <= MAX_DISTANCE_FOR_GROUPING: all_pairs_for_grouping.append((id1, id2, dist))
        output_categorized_pairs[key] = output_pairs_with_urls
    logger.info("--- End Summary ---")

    # --- 5. Grouping ---
    grouping_start_time = time.time()
    unique_pairs = list({tuple(sorted((p[0], p[1]))): p for p in all_pairs_for_grouping}.values())
    groups = group_similar_images(unique_pairs, id_to_details_map, cdn_base_url, args.img_ext)
    grouping_elapsed = time.time() - grouping_start_time
    logger.info(f"Grouping finished in {grouping_elapsed:.2f}s.")

    # --- 6. Prepare Final Report Data ---
    final_report = {
        "metadata": {
            "report_generated_at": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "dynamodb_table": args.table_name,
            "cdn_base_url_used": cdn_base_url + '/',
            "image_extension_used": args.img_ext,
            "total_items_scanned": len(all_items_raw),
            "total_unique_hashes_compared": n_items,
            "scan_workers": args.scan_workers,
            "compare_workers": args.compare_workers,
            "scan_duration_seconds": round(scan_elapsed, 2),
            "preparation_duration_seconds": round(prep_elapsed, 2),
            "compare_duration_seconds": round(compare_elapsed, 2),
            "grouping_duration_seconds": round(grouping_elapsed, 2),
            "thresholds_used": THRESHOLDS,
            "max_distance_for_grouping": MAX_DISTANCE_FOR_GROUPING,
            "summary_counts": {key: len(pairs) for key, pairs in aggregated_results.items()},
        },
        "similarity_groups": groups,
        "detailed_pairs_by_threshold": output_categorized_pairs
    }

    # --- 7. Save JSON Report ---
    logger.info(f"Saving JSON report to '{args.output}'...")
    try:
        with open(args.output, 'w') as f:
            json.dump(final_report, f, indent=2, cls=DecimalEncoder)
        logger.info(f"JSON report saved successfully.")
    except Exception as e: logger.error(f"Error saving JSON report: {e}", exc_info=True)

    # --- 8. Generate and Save HTML Report (if requested) ---
    if args.html_output.lower() != 'none':
        generate_html_report(final_report, args.html_output)

    # --- Finish ---
    total_script_time = time.time() - scan_start_time
    logger.info(f"Script finished in {total_script_time:.2f} seconds.")

if __name__ == "__main__":
    try:
        # Set 'spawn' for safety, particularly on macOS/Windows
        if multiprocessing.get_start_method(allow_none=True) != 'spawn':
             multiprocessing.set_start_method('spawn', force=True)
             logger.debug("Set multiprocessing start method to 'spawn'.")
    except Exception as e: logger.warning(f"Could not force 'spawn' start method: {e}")
    main()