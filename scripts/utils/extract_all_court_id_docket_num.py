#!/usr/bin/env python3
"""Extract court_id and docket_num from all JSON files in dockets directory."""

import argparse
import json
from pathlib import Path
from typing import List, Dict
from rich.console import Console
from rich.progress import track

console = Console()


def extract_court_docket_info(date: str) -> List[Dict[str, str]]:
    """Extract court_id and docket_num from all JSON files in the dockets directory.

    Args:
        date: Date in YYYYMMDD format

    Returns:
        List of dictionaries with court_id and docket_num
    """
    data_dir = Path(f"data/{date}/dockets")

    if not data_dir.exists():
        console.print(f"[red]Directory {data_dir} does not exist[/red]")
        return []

    results = []
    json_files = list(data_dir.glob("*.json"))

    if not json_files:
        console.print(f"[yellow]No JSON files found in {data_dir}[/yellow]")
        return results

    console.print(f"[green]Found {len(json_files)} JSON files to process[/green]")

    for json_file in track(json_files, description="Processing JSON files"):
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)

            # Extract court_id and docket_num
            court_id = data.get('court_id', '')
            docket_num = data.get('docket_num', '')

            if court_id and docket_num:
                results.append({
                    "court_id": court_id,
                    "docket_num": docket_num
                })
            else:
                console.print(f"[yellow]Missing court_id or docket_num in {json_file.name}[/yellow]")

        except json.JSONDecodeError:
            console.print(f"[red]Failed to parse JSON in {json_file.name}[/red]")
        except Exception as e:
            console.print(f"[red]Error processing {json_file.name}: {e}[/red]")

    return results


def main():
    parser = argparse.ArgumentParser(description="Extract court_id and docket_num from docket JSON files")
    parser.add_argument(
        "--date",
        required=True,
        help="Date in YYYYMMDD format"
    )
    parser.add_argument(
        "--output",
        help="Optional output JSON file path"
    )

    args = parser.parse_args()

    # Validate date format
    if len(args.date) != 8 or not args.date.isdigit():
        console.print("[red]Date must be in YYYYMMDD format[/red]")
        return

    # Extract court and docket information
    results = extract_court_docket_info(args.date)

    if results:
        console.print(f"\n[green]Extracted {len(results)} court/docket pairs[/green]")

        # Output to file if specified
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2)
            console.print(f"[green]Results saved to {args.output}[/green]")
        else:
            # Print to console
            console.print("\n[bold]Results:[/bold]")
            print(json.dumps(results, indent=2))
    else:
        console.print("[yellow]No court/docket information extracted[/yellow]")


if __name__ == "__main__":
    main()