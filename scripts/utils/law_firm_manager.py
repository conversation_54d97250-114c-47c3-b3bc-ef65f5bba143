#!/usr/bin/env python3
"""
Interactive CLI tool for managing law firm name normalizations using SQLite database.
"""

import sqlite3
import json
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from pathlib import Path

from rich.console import Console
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.text import Text
from rich import box
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.columns import Columns

console = Console()


class LawFirmManager:
    def __init__(self, db_path: str = None):
        """Initialize the law firm manager with SQLite database."""
        if db_path is None:
            base_dir = Path(__file__).parents[3]  # Go up to project root
            db_dir = base_dir / "sqlite"
            db_dir.mkdir(exist_ok=True)
            db_path = str(db_dir / "law_firm_normalizations.db")
        
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row
        self.create_tables()
    
    def create_tables(self):
        """Create the necessary tables if they don't exist."""
        cursor = self.conn.cursor()
        
        # Create law firm normalizations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS law_firm_normalizations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                variant_name TEXT NOT NULL UNIQUE COLLATE NOCASE,
                canonical_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                source TEXT DEFAULT 'manual',
                notes TEXT
            )
        """)
        
        # Create index for faster searches
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_variant_name 
            ON law_firm_normalizations(variant_name COLLATE NOCASE)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_canonical_name 
            ON law_firm_normalizations(canonical_name COLLATE NOCASE)
        """)
        
        # Create audit log table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS normalization_audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                variant_name TEXT,
                canonical_name TEXT,
                old_canonical_name TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user TEXT DEFAULT 'system'
            )
        """)
        
        self.conn.commit()
    
    def import_from_json(self, json_path: str, source: str = "import"):
        """Import normalizations from a JSON file."""
        try:
            with open(json_path, 'r') as f:
                data = json.load(f)
            
            cursor = self.conn.cursor()
            imported = 0
            skipped = 0
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task(f"Importing from {os.path.basename(json_path)}...", total=len(data))
                
                for variant, canonical in data.items():
                    if not variant.strip().startswith("//"):  # Skip comments
                        try:
                            cursor.execute("""
                                INSERT INTO law_firm_normalizations (variant_name, canonical_name, source)
                                VALUES (?, ?, ?)
                            """, (variant, canonical, source))
                            imported += 1
                        except sqlite3.IntegrityError:
                            # Update if already exists
                            cursor.execute("""
                                UPDATE law_firm_normalizations 
                                SET canonical_name = ?, updated_at = CURRENT_TIMESTAMP
                                WHERE variant_name = ?
                            """, (canonical, variant))
                            skipped += 1
                    progress.update(task, advance=1)
            
            self.conn.commit()
            return imported, skipped
            
        except Exception as e:
            console.print(f"[red]Error importing JSON: {e}[/red]")
            return 0, 0
    
    def search_firms(self, query: str, search_canonical: bool = False) -> List[Dict]:
        """Search for law firms by variant or canonical name."""
        cursor = self.conn.cursor()
        
        if search_canonical:
            cursor.execute("""
                SELECT * FROM law_firm_normalizations 
                WHERE canonical_name LIKE ? 
                ORDER BY canonical_name, variant_name
                LIMIT 50
            """, (f"%{query}%",))
        else:
            cursor.execute("""
                SELECT * FROM law_firm_normalizations 
                WHERE variant_name LIKE ? OR canonical_name LIKE ?
                ORDER BY 
                    CASE 
                        WHEN variant_name LIKE ? THEN 0
                        ELSE 1
                    END,
                    variant_name
                LIMIT 50
            """, (f"%{query}%", f"%{query}%", f"{query}%"))
        
        return [dict(row) for row in cursor.fetchall()]
    
    def get_all_canonical_names(self) -> List[str]:
        """Get all unique canonical names."""
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT DISTINCT canonical_name 
            FROM law_firm_normalizations 
            ORDER BY canonical_name
        """)
        return [row[0] for row in cursor.fetchall()]
    
    def add_normalization(self, variant: str, canonical: str, notes: str = None) -> bool:
        """Add a new normalization mapping."""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO law_firm_normalizations (variant_name, canonical_name, notes)
                VALUES (?, ?, ?)
            """, (variant, canonical, notes))
            
            # Log the action
            cursor.execute("""
                INSERT INTO normalization_audit_log (action, variant_name, canonical_name)
                VALUES ('ADD', ?, ?)
            """, (variant, canonical))
            
            self.conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
    
    def update_normalization(self, variant: str, new_canonical: str, notes: str = None) -> bool:
        """Update an existing normalization."""
        cursor = self.conn.cursor()
        
        # Get old canonical name for audit
        cursor.execute("SELECT canonical_name FROM law_firm_normalizations WHERE variant_name = ?", (variant,))
        result = cursor.fetchone()
        if not result:
            return False
        
        old_canonical = result[0]
        
        cursor.execute("""
            UPDATE law_firm_normalizations 
            SET canonical_name = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
            WHERE variant_name = ?
        """, (new_canonical, notes, variant))
        
        # Log the action
        cursor.execute("""
            INSERT INTO normalization_audit_log (action, variant_name, canonical_name, old_canonical_name)
            VALUES ('UPDATE', ?, ?, ?)
        """, (variant, new_canonical, old_canonical))
        
        self.conn.commit()
        return True
    
    def delete_normalization(self, variant: str) -> bool:
        """Delete a normalization mapping."""
        cursor = self.conn.cursor()
        
        # Get canonical name for audit
        cursor.execute("SELECT canonical_name FROM law_firm_normalizations WHERE variant_name = ?", (variant,))
        result = cursor.fetchone()
        if not result:
            return False
        
        canonical = result[0]
        
        cursor.execute("DELETE FROM law_firm_normalizations WHERE variant_name = ?", (variant,))
        
        # Log the action
        cursor.execute("""
            INSERT INTO normalization_audit_log (action, variant_name, canonical_name)
            VALUES ('DELETE', ?, ?)
        """, (variant, canonical))
        
        self.conn.commit()
        return True
    
    def get_statistics(self) -> Dict:
        """Get database statistics."""
        cursor = self.conn.cursor()
        
        stats = {}
        
        # Total mappings
        cursor.execute("SELECT COUNT(*) FROM law_firm_normalizations")
        stats['total_mappings'] = cursor.fetchone()[0]
        
        # Unique canonical names
        cursor.execute("SELECT COUNT(DISTINCT canonical_name) FROM law_firm_normalizations")
        stats['unique_canonical'] = cursor.fetchone()[0]
        
        # Recent additions
        cursor.execute("""
            SELECT COUNT(*) FROM law_firm_normalizations 
            WHERE date(created_at) = date('now')
        """)
        stats['added_today'] = cursor.fetchone()[0]
        
        # Most common canonical names
        cursor.execute("""
            SELECT canonical_name, COUNT(*) as count 
            FROM law_firm_normalizations 
            GROUP BY canonical_name 
            ORDER BY count DESC 
            LIMIT 5
        """)
        stats['top_canonical'] = cursor.fetchall()
        
        return stats
    
    def export_to_json(self, output_path: str) -> int:
        """Export normalizations to JSON file."""
        cursor = self.conn.cursor()
        cursor.execute("SELECT variant_name, canonical_name FROM law_firm_normalizations ORDER BY variant_name")
        
        data = {row[0]: row[1] for row in cursor.fetchall()}
        
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=2, sort_keys=True)
        
        return len(data)
    
    def close(self):
        """Close database connection."""
        self.conn.close()


def display_search_results(results: List[Dict]):
    """Display search results in a formatted table."""
    if not results:
        console.print("[yellow]No results found.[/yellow]")
        return
    
    table = Table(title=f"Search Results ({len(results)} found)", box=box.ROUNDED)
    table.add_column("Variant Name", style="cyan")
    table.add_column("Canonical Name", style="green")
    table.add_column("Source", style="dim")
    table.add_column("Updated", style="dim")
    
    for result in results:
        updated = datetime.fromisoformat(result['updated_at']).strftime("%Y-%m-%d")
        table.add_row(
            result['variant_name'],
            result['canonical_name'],
            result['source'],
            updated
        )
    
    console.print(table)


def display_statistics(stats: Dict):
    """Display database statistics."""
    # Create statistics panels
    panels = [
        Panel(f"[bold cyan]{stats['total_mappings']:,}[/bold cyan]", title="Total Mappings", border_style="cyan"),
        Panel(f"[bold green]{stats['unique_canonical']:,}[/bold green]", title="Unique Firms", border_style="green"),
        Panel(f"[bold yellow]{stats['added_today']:,}[/bold yellow]", title="Added Today", border_style="yellow"),
    ]
    
    console.print(Columns(panels, equal=True, expand=True))
    
    # Top canonical names table
    if stats['top_canonical']:
        table = Table(title="Most Common Canonical Names", box=box.SIMPLE)
        table.add_column("Law Firm", style="cyan")
        table.add_column("Variants", justify="right", style="green")
        
        for name, count in stats['top_canonical']:
            table.add_row(name, str(count))
        
        console.print(table)


def main_menu():
    """Display main menu and handle user interaction."""
    manager = LawFirmManager()
    
    # Import existing JSON files on first run
    base_dir = Path(__file__).parents[3]
    json_files = [
        base_dir / "src" / "config" / "law_firms" / "name_normalization.json",
        base_dir / "config" / "data" / "law_firms" / "name_normalization.json"
    ]
    
    # Check if database is empty
    cursor = manager.conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM law_firm_normalizations")
    if cursor.fetchone()[0] == 0:
        console.print("[yellow]Database is empty. Importing from existing JSON files...[/yellow]")
        for json_file in json_files:
            if json_file.exists():
                imported, updated = manager.import_from_json(str(json_file), source=str(json_file.name))
                console.print(f"✓ Imported {imported} entries from {json_file.name} ({updated} updated)")
    
    while True:
        console.print("\n" + "="*60)
        console.print("[bold cyan]Law Firm Name Normalization Manager[/bold cyan]")
        console.print("="*60)
        
        # Display statistics
        stats = manager.get_statistics()
        display_statistics(stats)
        
        console.print("\n[bold]Options:[/bold]")
        console.print("1. Search law firms")
        console.print("2. Add new normalization")
        console.print("3. Update existing normalization")
        console.print("4. Delete normalization")
        console.print("5. Export to JSON")
        console.print("6. Import from JSON")
        console.print("7. Exit")
        
        choice = Prompt.ask("\nSelect option", choices=["1", "2", "3", "4", "5", "6", "7"])
        
        if choice == "1":
            # Search
            query = Prompt.ask("\nEnter search term")
            search_canonical = Confirm.ask("Search in canonical names only?", default=False)
            results = manager.search_firms(query, search_canonical)
            display_search_results(results)
            
        elif choice == "2":
            # Add new
            variant = Prompt.ask("\nEnter variant name")
            
            # Check if already exists
            existing = manager.search_firms(variant)
            if any(r['variant_name'].lower() == variant.lower() for r in existing):
                console.print(f"[red]Variant '{variant}' already exists![/red]")
                continue
            
            # Show existing canonical names for selection
            canonical_names = manager.get_all_canonical_names()
            console.print("\n[bold]Select canonical name:[/bold]")
            console.print("0. Enter new canonical name")
            
            # Display first 20 canonical names
            for i, name in enumerate(canonical_names[:20], 1):
                console.print(f"{i}. {name}")
            
            if len(canonical_names) > 20:
                console.print(f"... and {len(canonical_names) - 20} more")
            
            choice = Prompt.ask("Select option (0 for new)")
            
            if choice == "0":
                canonical = Prompt.ask("Enter new canonical name")
            else:
                try:
                    idx = int(choice) - 1
                    canonical = canonical_names[idx]
                except (ValueError, IndexError):
                    console.print("[red]Invalid selection[/red]")
                    continue
            
            notes = Prompt.ask("Notes (optional)", default="")
            
            if manager.add_normalization(variant, canonical, notes if notes else None):
                console.print(f"[green]✓ Added mapping: '{variant}' -> '{canonical}'[/green]")
            else:
                console.print(f"[red]Failed to add mapping[/red]")
                
        elif choice == "3":
            # Update
            variant = Prompt.ask("\nEnter variant name to update")
            results = manager.search_firms(variant)
            
            if not results:
                console.print(f"[red]Variant '{variant}' not found[/red]")
                continue
            
            # Show exact match or let user select
            exact_match = None
            for r in results:
                if r['variant_name'].lower() == variant.lower():
                    exact_match = r
                    break
            
            if exact_match:
                console.print(f"\nCurrent mapping: '{exact_match['variant_name']}' -> '{exact_match['canonical_name']}'")
                variant = exact_match['variant_name']
            else:
                display_search_results(results)
                variant = Prompt.ask("Enter exact variant name from results")
            
            new_canonical = Prompt.ask("Enter new canonical name")
            notes = Prompt.ask("Notes (optional)", default="")
            
            if manager.update_normalization(variant, new_canonical, notes if notes else None):
                console.print(f"[green]✓ Updated mapping: '{variant}' -> '{new_canonical}'[/green]")
            else:
                console.print(f"[red]Failed to update mapping[/red]")
                
        elif choice == "4":
            # Delete
            variant = Prompt.ask("\nEnter variant name to delete")
            
            if Confirm.ask(f"Are you sure you want to delete '{variant}'?"):
                if manager.delete_normalization(variant):
                    console.print(f"[green]✓ Deleted mapping for '{variant}'[/green]")
                else:
                    console.print(f"[red]Variant '{variant}' not found[/red]")
                    
        elif choice == "5":
            # Export
            output_path = Prompt.ask("\nEnter output path", default="law_firm_normalizations_export.json")
            count = manager.export_to_json(output_path)
            console.print(f"[green]✓ Exported {count} mappings to {output_path}[/green]")
            
        elif choice == "6":
            # Import
            import_path = Prompt.ask("\nEnter JSON file path")
            if os.path.exists(import_path):
                imported, updated = manager.import_from_json(import_path)
                console.print(f"[green]✓ Imported {imported} new entries, updated {updated} existing[/green]")
            else:
                console.print(f"[red]File not found: {import_path}[/red]")
                
        elif choice == "7":
            # Exit
            console.print("\n[cyan]Goodbye![/cyan]")
            break
    
    manager.close()


if __name__ == "__main__":
    try:
        main_menu()
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")