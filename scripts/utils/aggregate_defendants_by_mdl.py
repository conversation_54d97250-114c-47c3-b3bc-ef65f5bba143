#!/usr/bin/env python3
"""
Aggregate Defendants and Flags by MDL Script

Queries PACER database for specific MDL numbers and aggregates unique defendants and flags.
Outputs results as a JSON file with defendant and flag lists for each MDL.

Usage:
    # Query specific MDL numbers:
    python scripts/utils/aggregate_defendants_by_mdl.py --mdl-nums "3010,2873,3092" [--local]
    
    # Query all records with valid MDL numbers:
    python scripts/utils/aggregate_defendants_by_mdl.py --all-valid-mdls [--local]
"""

import argparse
import ast
import asyncio
import json
import logging
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Set, Any, Optional

# Add project root to path for imports
script_dir = Path(__file__).parent
project_root = script_dir.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.config_models.loader import load_config
    from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
    from src.repositories.pacer_repository import PacerRepository
    from src.services.pacer.query_service import PacerQueryService
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print(f"Make sure you're running this script from the project root directory:")
    print(f"python scripts/utils/aggregate_defendants_by_mdl.py --help")
    sys.exit(1)


def parse_dynamodb_list_or_string(value: Any) -> List[str]:
    """
    Parse DynamoDB list format or string that may contain a list.
    
    Handles:
    - DynamoDB list format: [{'S': 'value1'}, {'S': 'value2'}]
    - String representation of list: "['value1', 'value2']"
    - Regular list: ['value1', 'value2']
    - Single string: 'value1'
    
    Args:
        value: The value to parse
        
    Returns:
        List of string values
    """
    if not value:
        return []
    
    # Handle DynamoDB list format
    if isinstance(value, list):
        # Check if it's DynamoDB format with type descriptors
        if value and isinstance(value[0], dict) and 'S' in value[0]:
            return [item.get('S', '') for item in value if item.get('S')]
        # Regular list
        else:
            return [str(item) for item in value if item]
    
    # Handle string format
    if isinstance(value, str):
        value = value.strip()
        if not value:
            return []
        
        # Try to parse as Python list literal
        if value.startswith('[') and value.endswith(']'):
            try:
                parsed = ast.literal_eval(value)
                if isinstance(parsed, list):
                    return [str(item) for item in parsed if item]
            except (ValueError, SyntaxError):
                pass
        
        # Single string value
        return [value]
    
    # Fallback for other types
    return [str(value)] if value else []


def parse_defendants_from_record(record: Dict[str, Any]) -> Set[str]:
    """
    Extract and parse defendants from a PACER record.
    
    Handles multiple fields and formats including DynamoDB list format:
    - Defendant (string, list, or DynamoDB list)
    - Defendants (string, list, or DynamoDB list)
    - Versus (string, parsed for defendant info)
    
    Args:
        record: PACER record dictionary
        
    Returns:
        Set of unique defendant names
    """
    defendants = set()
    
    # Extract from Defendant field (note: this is typically a list in DynamoDB)
    defendant_field = record.get('defendant', record.get('Defendant', ''))
    defendant_values = parse_dynamodb_list_or_string(defendant_field)
    for defendant in defendant_values:
        if defendant:
            defendants.update(_parse_defendant_string(defendant))
    
    # Extract from Defendants field
    defendants_field = record.get('defendants', record.get('Defendants', ''))
    defendants_values = parse_dynamodb_list_or_string(defendants_field)
    for defendant in defendants_values:
        if defendant:
            defendants.update(_parse_defendant_string(defendant))
    
    # Extract from Versus field (parse for defendant names)
    versus = record.get('versus', record.get('Versus', ''))
    if versus and isinstance(versus, str):
        defendants.update(_parse_versus_for_defendants(versus))
    
    # Clean and filter defendants
    cleaned_defendants = set()
    for defendant in defendants:
        cleaned = _clean_defendant_name(defendant)
        if cleaned and len(cleaned) > 2:  # Filter out very short names
            cleaned_defendants.add(cleaned)
    
    return cleaned_defendants


def _parse_defendant_string(defendant_str: str) -> Set[str]:
    """Parse a defendant string that may contain multiple defendants."""
    defendants = set()
    
    # Split on common separators
    separators = [';', ',', ' and ', ' & ', '\n', '\r\n']
    parts = [defendant_str]
    
    for separator in separators:
        new_parts = []
        for part in parts:
            new_parts.extend(part.split(separator))
        parts = new_parts
    
    for part in parts:
        cleaned = part.strip()
        if cleaned:
            defendants.add(cleaned)
    
    return defendants


def _parse_versus_for_defendants(versus_str: str) -> Set[str]:
    """
    Parse defendant names from a 'versus' field.
    Format is typically "Plaintiff(s) v. Defendant(s)"
    """
    defendants = set()
    
    # Look for patterns indicating defendants after "v." or "vs."
    vs_patterns = [' v. ', ' vs. ', ' v ', ' vs ']
    
    for pattern in vs_patterns:
        if pattern in versus_str.lower():
            parts = versus_str.lower().split(pattern.lower())
            if len(parts) >= 2:
                # Everything after the "v." is likely defendants
                defendant_part = parts[1].strip()
                defendants.update(_parse_defendant_string(defendant_part))
                break
    
    return defendants


def _clean_defendant_name(name: str) -> str:
    """Clean and normalize a defendant name."""
    if not name:
        return ""
    
    # Remove common legal suffixes and prefixes
    name = name.strip()
    
    # Remove quotes and extra spaces
    name = name.replace('"', '').replace("'", '')
    name = ' '.join(name.split())
    
    # Convert to title case for consistency
    name = name.title()
    
    # Filter out common non-defendant entries
    skip_terms = [
        'et al', 'et al.', 'etc', 'etc.', 'unknown', 'n/a', 
        'none', 'various', 'multiple', 'several', 'other',
        'defendant', 'defendants', 'doe', 'john doe', 'jane doe'
    ]
    
    if name.lower() in skip_terms:
        return ""
    
    return name


def parse_flags_from_record(record: Dict[str, Any]) -> Set[str]:
    """
    Extract and parse flags from a PACER record.
    
    Handles the Flags field including DynamoDB list format.
    
    Args:
        record: PACER record dictionary
        
    Returns:
        Set of unique flag values
    """
    flags = set()
    
    # Extract from Flags field (handle both case variations)
    flags_field = record.get('flags', record.get('Flags', ''))
    flag_values = parse_dynamodb_list_or_string(flags_field)
    
    for flag_item in flag_values:
        if flag_item:
            flags.update(_parse_flag_string(flag_item))
    
    # Clean and filter flags
    cleaned_flags = set()
    for flag in flags:
        cleaned = _clean_flag_value(flag)
        if cleaned and len(cleaned) > 1:  # Filter out very short flags
            cleaned_flags.add(cleaned)
    
    return cleaned_flags


def _parse_flag_string(flag_str: str) -> Set[str]:
    """Parse a flag string that may contain multiple flags."""
    flags = set()
    
    # Split on common separators
    separators = [';', ',', '|', '\n', '\r\n']
    parts = [flag_str]
    
    for separator in separators:
        new_parts = []
        for part in parts:
            new_parts.extend(part.split(separator))
        parts = new_parts
    
    for part in parts:
        cleaned = part.strip()
        if cleaned:
            flags.add(cleaned)
    
    return flags


def _clean_flag_value(flag: str) -> str:
    """Clean and normalize a flag value."""
    if not flag:
        return ""
    
    # Remove extra spaces and normalize
    flag = flag.strip()
    flag = ' '.join(flag.split())
    
    # Filter out common non-flag entries
    skip_terms = [
        'n/a', 'none', 'unknown', 'null', 'undefined', ''
    ]
    
    if flag.lower() in skip_terms:
        return ""
    
    return flag


def parse_mdl_numbers(mdl_string: str) -> List[str]:
    """Parse comma-separated MDL numbers from string."""
    if not mdl_string:
        return []
    
    mdl_nums = []
    for mdl in mdl_string.split(','):
        mdl = mdl.strip()
        if mdl:
            mdl_nums.append(mdl)
    
    return mdl_nums


def is_valid_mdl_number(mdl_value: Any) -> bool:
    """
    Check if an MDL number is valid (not NA, null, empty, etc.).
    
    Args:
        mdl_value: MDL value to validate
        
    Returns:
        True if MDL is valid, False otherwise
    """
    if mdl_value is None:
        return False
    
    # Convert to string for comparison
    mdl_str = str(mdl_value).strip().lower()
    
    # List of invalid values
    invalid_values = {
        '', 'na', 'n/a', 'null', 'none', 'unknown', 
        'nan', 'undefined', '0', 'null'
    }
    
    return mdl_str not in invalid_values and len(mdl_str) > 0


def _process_record_batch(records_batch: List[Dict[str, Any]], mdl_numbers: Optional[List[str]] = None) -> tuple:
    """
    Process a batch of records and extract defendants/flags.
    
    Args:
        records_batch: List of records to process
        mdl_numbers: Optional list of specific MDL numbers to filter
        
    Returns:
        Tuple of (defendants_by_mdl, flags_by_mdl, mdl_record_counts, valid_records, invalid_records)
    """
    defendants_by_mdl = {}
    flags_by_mdl = {}
    mdl_record_counts = {}
    valid_records = 0
    invalid_records = 0
    
    for record in records_batch:
        mdl_num = record.get('mdl_num')  # Records are in snake_case after conversion
        
        # Validate MDL number
        if not is_valid_mdl_number(mdl_num):
            invalid_records += 1
            continue
        
        # If specific MDL numbers requested, filter to only those
        if mdl_numbers and mdl_num not in mdl_numbers:
            continue
        
        valid_records += 1
        
        # Initialize MDL entries if needed
        if mdl_num not in defendants_by_mdl:
            defendants_by_mdl[mdl_num] = set()
            flags_by_mdl[mdl_num] = set()
            mdl_record_counts[mdl_num] = 0
        
        mdl_record_counts[mdl_num] += 1
        
        # Extract defendants and flags from this record
        defendants = parse_defendants_from_record(record)
        flags = parse_flags_from_record(record)
        
        # Add to MDL sets
        defendants_by_mdl[mdl_num].update(defendants)
        flags_by_mdl[mdl_num].update(flags)
    
    return defendants_by_mdl, flags_by_mdl, mdl_record_counts, valid_records, invalid_records


def _process_records_sequential(all_records: List[Dict[str, Any]], mdl_numbers: Optional[List[str]], logger) -> tuple:
    """Process records sequentially with progress logging."""
    defendants_by_mdl = {}
    flags_by_mdl = {}
    mdl_record_counts = {}
    valid_records = 0
    invalid_records = 0
    
    # Progress tracking for large datasets
    progress_interval = max(1000, len(all_records) // 100)  # Log every 1% or 1000 records
    
    for i, record in enumerate(all_records):
        # Progress logging
        if i > 0 and i % progress_interval == 0:
            logger.info(f"Processed {i:,} / {len(all_records):,} records ({i/len(all_records)*100:.1f}%)")
        
        mdl_num = record.get('mdl_num')  # Records are in snake_case after conversion
        
        # Validate MDL number
        if not is_valid_mdl_number(mdl_num):
            invalid_records += 1
            continue
        
        # If specific MDL numbers requested, filter to only those
        if mdl_numbers and mdl_num not in mdl_numbers:
            continue
        
        valid_records += 1
        
        # Initialize MDL entries if needed
        if mdl_num not in defendants_by_mdl:
            defendants_by_mdl[mdl_num] = set()
            flags_by_mdl[mdl_num] = set()
            mdl_record_counts[mdl_num] = 0
        
        mdl_record_counts[mdl_num] += 1
        
        # Extract defendants and flags from this record
        defendants = parse_defendants_from_record(record)
        flags = parse_flags_from_record(record)
        
        # Add to MDL sets
        defendants_by_mdl[mdl_num].update(defendants)
        flags_by_mdl[mdl_num].update(flags)
    
    return defendants_by_mdl, flags_by_mdl, mdl_record_counts, valid_records, invalid_records


async def _process_records_parallel(all_records: List[Dict[str, Any]], mdl_numbers: Optional[List[str]], logger) -> tuple:
    """Process records in parallel batches."""
    import concurrent.futures
    from functools import partial
    
    # Calculate optimal batch size
    batch_size = max(1000, len(all_records) // 8)  # 8 batches
    batches = [all_records[i:i + batch_size] for i in range(0, len(all_records), batch_size)]
    
    logger.info(f"Processing {len(batches)} batches of ~{batch_size:,} records each")
    
    # Process batches in parallel
    process_func = partial(_process_record_batch, mdl_numbers=mdl_numbers)
    
    loop = asyncio.get_event_loop()
    with concurrent.futures.ProcessPoolExecutor(max_workers=4) as executor:
        tasks = []
        for i, batch in enumerate(batches):
            logger.info(f"Starting batch {i+1}/{len(batches)}")
            task = loop.run_in_executor(executor, process_func, batch)
            tasks.append(task)
        
        # Wait for all batches to complete
        batch_results = await asyncio.gather(*tasks)
    
    # Merge results from all batches
    final_defendants_by_mdl = {}
    final_flags_by_mdl = {}
    final_mdl_record_counts = {}
    total_valid_records = 0
    total_invalid_records = 0
    
    for defendants_by_mdl, flags_by_mdl, mdl_record_counts, valid_records, invalid_records in batch_results:
        total_valid_records += valid_records
        total_invalid_records += invalid_records
        
        # Merge MDL data
        for mdl_num in defendants_by_mdl:
            if mdl_num not in final_defendants_by_mdl:
                final_defendants_by_mdl[mdl_num] = set()
                final_flags_by_mdl[mdl_num] = set()
                final_mdl_record_counts[mdl_num] = 0
            
            final_defendants_by_mdl[mdl_num].update(defendants_by_mdl[mdl_num])
            final_flags_by_mdl[mdl_num].update(flags_by_mdl[mdl_num])
            final_mdl_record_counts[mdl_num] += mdl_record_counts[mdl_num]
    
    logger.info(f"Parallel processing complete: merged {len(batch_results)} batches")
    return final_defendants_by_mdl, final_flags_by_mdl, final_mdl_record_counts, total_valid_records, total_invalid_records


def calculate_date_range(days: int) -> tuple[str, str]:
    """
    Calculate date range for the past N days.
    
    Args:
        days: Number of days back from today
        
    Returns:
        Tuple of (start_date, end_date) in YYYYMMDD format
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    return (
        start_date.strftime('%Y%m%d'),
        end_date.strftime('%Y%m%d')
    )


async def aggregate_defendants_and_flags_by_mdl(
    query_service: PacerQueryService,
    mdl_numbers: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Query PACER data for specific MDL numbers or all valid MDLs and aggregate defendants and flags.
    
    Args:
        query_service: Initialized PACER query service
        mdl_numbers: List of specific MDL numbers to query, or None for all valid MDLs
        
    Returns:
        Dictionary with query info, defendants, and flags by MDL
    """
    logger = logging.getLogger(__name__)
    
    if mdl_numbers:
        # Query specific MDL numbers
        logger.info(f"Querying PACER data for specific MDL numbers: {', '.join(mdl_numbers)}")
        query_type = "specific_mdls"
        
        all_records = []
        mdl_record_counts = {}
        
        # Query each MDL number
        for mdl_num in mdl_numbers:
            logger.info(f"Querying MDL {mdl_num}")
            try:
                records = await query_service.query_by_mdl_num(mdl_num)
                logger.info(f"Found {len(records)} records for MDL {mdl_num}")
                all_records.extend(records)
                mdl_record_counts[mdl_num] = len(records)
            except Exception as e:
                logger.error(f"Error querying MDL {mdl_num}: {e}")
                mdl_record_counts[mdl_num] = 0
    else:
        # Query all records and filter for valid MDLs
        logger.info("Querying all PACER records and filtering for valid MDL numbers")
        query_type = "all_valid_mdls"
        
        try:
            all_records = await query_service.repository.scan_all()
            logger.info(f"Retrieved {len(all_records)} total records from database")
        except Exception as e:
            logger.error(f"Error scanning all records: {e}")
            all_records = []
    
    logger.info(f"Processing {len(all_records)} records...")
    
    # Group by MDL number and collect defendants and flags
    defendants_by_mdl = {}
    flags_by_mdl = {}
    mdl_record_counts = {} if not mdl_numbers else mdl_record_counts
    valid_records = 0
    invalid_records = 0
    
    # Use parallel processing for large datasets when using local DynamoDB
    use_parallel = len(all_records) > 5000 and (not mdl_numbers or len(mdl_numbers) > 5)
    
    if use_parallel:
        logger.info(f"Using parallel processing for {len(all_records):,} records")
        defendants_by_mdl, flags_by_mdl, mdl_record_counts, valid_records, invalid_records = await _process_records_parallel(
            all_records, mdl_numbers, logger
        )
    else:
        logger.info(f"Using sequential processing for {len(all_records):,} records")
        defendants_by_mdl, flags_by_mdl, mdl_record_counts, valid_records, invalid_records = _process_records_sequential(
            all_records, mdl_numbers, logger
        )
    
    logger.info(f"Processing complete: {valid_records:,} valid records, {invalid_records:,} invalid MDL records")
    logger.info(f"Found {len(defendants_by_mdl)} unique valid MDL numbers")
    
    # Convert sets to sorted lists for JSON serialization
    result_data = {}
    target_mdls = mdl_numbers if mdl_numbers else sorted(defendants_by_mdl.keys())
    
    for mdl_num in target_mdls:
        defendants_list = sorted(list(defendants_by_mdl.get(mdl_num, set())))
        flags_list = sorted(list(flags_by_mdl.get(mdl_num, set())))
        
        result_data[mdl_num] = {
            "unique_defendants": defendants_list,
            "defendant_count": len(defendants_list),
            "unique_flags": flags_list,
            "flag_count": len(flags_list),
            "total_records": mdl_record_counts.get(mdl_num, 0)
        }
    
    # Calculate summary statistics
    total_defendants = sum(len(data["unique_defendants"]) for data in result_data.values())
    total_flags = sum(len(data["unique_flags"]) for data in result_data.values())
    total_records = sum(data["total_records"] for data in result_data.values())
    
    logger.info(f"Final statistics:")
    logger.info(f"  MDL numbers processed: {len(result_data)}")
    logger.info(f"  Total unique defendants: {total_defendants}")
    logger.info(f"  Total unique flags: {total_flags}")
    logger.info(f"  Total records: {total_records}")
    
    # Create result structure
    result = {
        "query_info": {
            "query_type": query_type,
            "mdl_numbers": mdl_numbers if mdl_numbers else sorted(defendants_by_mdl.keys()),
            "total_records": len(all_records),
            "valid_mdl_records": valid_records,
            "invalid_mdl_records": invalid_records,
            "unique_valid_mdls": len(defendants_by_mdl),
            "mdls_processed": len(result_data),
            "generated_at": datetime.now().isoformat()
        },
        "data_by_mdl": result_data,
        "summary": {
            "total_unique_defendants": total_defendants,
            "total_unique_flags": total_flags,
            "mdls_processed": len(result_data),
            "records_processed": total_records
        }
    }
    
    return result


async def main():
    """Main entry point for the script."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Aggregate defendants and flags by MDL from PACER database"
    )
    # Create mutually exclusive group for MDL selection
    mdl_group = parser.add_mutually_exclusive_group(required=True)
    mdl_group.add_argument(
        "--mdl-nums", 
        help="Comma-separated list of specific MDL numbers to query (e.g., '3010,2873,3092')"
    )
    mdl_group.add_argument(
        "--all-valid-mdls",
        action="store_true",
        help="Query all records with valid MDL numbers (excludes NA, null, empty, etc.)"
    )
    parser.add_argument(
        "--output", 
        help="Output JSON file path (default: defendants_flags_by_mdl_YYYYMMDD.json)"
    )
    parser.add_argument(
        "--local", 
        action="store_true", 
        help="Use local DynamoDB (automatically configures endpoint)"
    )
    parser.add_argument(
        "--config", 
        help="Path to configuration file (default: config/scrape.yml)"
    )
    parser.add_argument(
        "--verbose", 
        "-v", 
        action="store_true", 
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Suppress boto3 noise
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    logger = logging.getLogger(__name__)
    
    # Determine query mode and parse MDL numbers if provided
    if args.all_valid_mdls:
        mdl_numbers = None
        query_mode = "all valid MDLs"
        logger.info("Mode: Query all records with valid MDL numbers")
    else:
        mdl_numbers = parse_mdl_numbers(args.mdl_nums)
        if not mdl_numbers:
            logger.error("No valid MDL numbers provided")
            return
        query_mode = f"specific MDLs: {', '.join(mdl_numbers)}"
        logger.info(f"Mode: Query specific MDL numbers: {', '.join(mdl_numbers)}")
    
    # Load configuration - create minimal config for database access
    try:
        if args.config:
            # User specified custom config
            config = load_config(args.config)
        else:
            # Use minimal config for database connection only
            from src.config_models.scraper import ScraperConfig
            logger.info("Using minimal config for database connection")
            config = ScraperConfig(
                name="defendants_flags_script",  # Required workflow name
                date='01/14/25',  # Required date in MM/DD/YY format
                scraper=False,
                use_local=False,
                local_port=8000
            )
    except Exception as e:
        logger.warning(f"Could not load config file, using minimal config: {e}")
        # Fallback to minimal config
        from src.config_models.scraper import ScraperConfig
        config = ScraperConfig(
            name="defendants_flags_script",  # Required workflow name
            date='01/14/25',  # Required date in MM/DD/YY format
            scraper=False,
            use_local=False,
            local_port=8000
        )
    
    # Configure for local DynamoDB if requested
    if args.local:
        config.use_local = True
        config.local_port = 8000  # Standard local DynamoDB port
        logger.info("Configured for local DynamoDB on port 8000")
    
    # Set output file path
    if args.output:
        output_path = Path(args.output)
    else:
        today = datetime.now().strftime('%Y%m%d')
        if args.all_valid_mdls:
            output_path = Path(f"defendants_flags_all_valid_mdls_{today}.json")
        else:
            mdl_suffix = "_".join(mdl_numbers[:3])  # Use first 3 MDLs in filename
            if len(mdl_numbers) > 3:
                mdl_suffix += "_plus"
            output_path = Path(f"defendants_flags_mdl_{mdl_suffix}_{today}.json")
    
    try:
        # Initialize services with dependency injection pattern
        async with AsyncDynamoDBStorage(config, logger=logger) as storage:
            repository = PacerRepository(storage)
            query_service = PacerQueryService(repository=repository, logger=logger)
            
            # Aggregate defendants and flags by MDL
            result = await aggregate_defendants_and_flags_by_mdl(
                query_service, mdl_numbers
            )
            
            # Write results to JSON file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Results written to {output_path}")
            
            # Print summary
            print(f"\n=== Summary ===")
            print(f"Query mode: {query_mode}")
            if args.all_valid_mdls:
                print(f"Total database records: {result['query_info']['total_records']:,}")
                print(f"Valid MDL records: {result['query_info']['valid_mdl_records']:,}")
                print(f"Invalid MDL records: {result['query_info']['invalid_mdl_records']:,}")
                print(f"Unique valid MDLs found: {result['query_info']['unique_valid_mdls']}")
            else:
                print(f"MDL numbers: {', '.join(mdl_numbers)}")
            print(f"Records processed: {result['summary']['records_processed']:,}")
            print(f"Total unique defendants: {result['summary']['total_unique_defendants']:,}")
            print(f"Total unique flags: {result['summary']['total_unique_flags']:,}")
            print(f"Output file: {output_path}")
            
            # Print details for each MDL (limit to first 20 for all-valid-mdls mode)
            mdl_data = result['data_by_mdl']
            mdl_list = list(mdl_data.keys())
            
            if args.all_valid_mdls and len(mdl_list) > 20:
                print(f"\n=== Details for Top 20 MDLs (by defendant count) ===")
                # Sort by defendant count
                sorted_mdls = sorted(mdl_list, key=lambda x: mdl_data[x]['defendant_count'], reverse=True)[:20]
                print(f"(Showing top 20 of {len(mdl_list)} total MDLs)")
            else:
                print(f"\n=== Details by MDL ===")
                sorted_mdls = mdl_list
            
            for mdl_num in sorted_mdls:
                data = mdl_data.get(mdl_num, {})
                defendant_count = data.get('defendant_count', 0)
                flag_count = data.get('flag_count', 0)
                record_count = data.get('total_records', 0)
                
                print(f"MDL {mdl_num}:")
                print(f"  Records: {record_count:,}")
                print(f"  Unique defendants: {defendant_count}")
                print(f"  Unique flags: {flag_count}")
                
                # Show sample defendants and flags
                if defendant_count > 0:
                    defendants = data.get('unique_defendants', [])
                    sample_defendants = defendants[:3]  # Show fewer for brevity
                    print(f"  Sample defendants: {', '.join(sample_defendants)}")
                    if len(defendants) > 3:
                        print(f"    ... and {len(defendants) - 3} more")
                
                if flag_count > 0:
                    flags = data.get('unique_flags', [])
                    sample_flags = flags[:3]  # Show fewer for brevity
                    print(f"  Sample flags: {', '.join(sample_flags)}")
                    if len(flags) > 3:
                        print(f"    ... and {len(flags) - 3} more")
                print()
            
            if args.all_valid_mdls and len(mdl_list) > 20:
                print(f"Full details for all {len(mdl_list)} MDLs are available in the output JSON file.")
            
    except Exception as e:
        logger.error(f"Error processing data: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())