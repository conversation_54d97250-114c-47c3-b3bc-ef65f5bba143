import json
import os
import re
from concurrent.futures import ThreadPoolExecutor
from datetime import timedel<PERSON>, datetime

import pandas as pd
from tqdm import tqdm

from src.utils.pdf_utils import PDFExtractor
from src.utils.json_safety import safe_json_write, safe_json_read

# Import PROJECT_ROOT from config or set a fallback
try:
    from src.lib.config_adapter import PROJECT_ROOT
except ImportError:
    # If can't import, try to load from environment or use a fallback
    try:
        from dotenv import load_dotenv
        load_dotenv()
        PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
    except ImportError:
        PROJECT_ROOT = os.path.expanduser('/')


def process_filename(full_path):
    # Extract the filename without the extension
    filename = os.path.basename(full_path)
    name_without_extension = os.path.splitext(filename)[0]

    # Split the filename by underscores
    parts = name_without_extension.split('_')

    # Get the part after the 2nd underscore
    if len(parts) > 2:
        versus_name = '_'.join(parts[2:])  # Join the remaining parts after the second underscore
    else:
        versus_name = "Unknown"

    return name_without_extension, versus_name


def load_processed_files(output_file):
    # Load previously processed root filenames to avoid reprocessing
    processed_files = set()
    if os.path.exists(output_file):
        with open(output_file, 'r') as f:
            for line in f:
                _, root_filename = line.strip().split(', ')
                processed_files.add(root_filename)
    return processed_files


def search_for_amended(directory, config, output_file, processed_files, date_str):
    for filename in tqdm(os.listdir(directory), desc=f'Searching for amended in {date_str}', leave=False):
        if filename.endswith('.json'):
            root_filename = os.path.splitext(filename)[0]
            pdf_filename = f"{root_filename}.pdf"
            pdf_path = os.path.join(directory, pdf_filename)

            if root_filename in processed_files:
                print(f"Skipping {root_filename} (already processed)")
                continue

            if os.path.exists(pdf_path):
                pdf_extractor = PDFExtractor(config, pdf_path)
                text = pdf_extractor.extract_text_with_tesseract_local(pages=slice(0, 4))

                if re.search(r'\bamended\b|\bamended complaint\b', text, re.IGNORECASE):
                    print(f"Found amended complaint in {root_filename}")
                    with open(output_file, 'a') as f:
                        f.write(f"{date_str}, {root_filename}\n")
                    processed_files.add(root_filename)


def process_directory(date_str, config, output_file, processed_files):
    directory = os.path.join(PROJECT_ROOT, "src", "data", date_str, "dockets")
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return

    search_for_amended(directory, config, output_file, processed_files, date_str)


def find_amended_complaints(config):
    output_file = "amended_complaints.txt"
    processed_files = load_processed_files(output_file)

    date_range = [(datetime.strptime("20240505", "%Y%m%d") + timedelta(days=i)).strftime('%Y%m%d') for i in
                  range((datetime.today() - datetime.strptime("20240502", "%Y%m%d")).days + 1)]

    with ThreadPoolExecutor() as executor:
        futures = [executor.submit(process_directory, date_str, config, output_file, processed_files) for date_str in
                   date_range]
        for future in tqdm(futures, desc="Processing directories in parallel"):
            future.result()


def fix_versus_value(versus):
    # Remove the first 5 digits and everything before them
    versus = re.sub(r'^\w+_\d{5}_', '', versus)

    # Replace underscores with spaces
    versus = versus.replace('_', ' ')

    # Ensure 'v' is lowercase and followed by a period, ensuring no extra periods
    versus = re.sub(r'\bv\b', 'v.', versus)

    # Ensure 'et al' is lowercase with a period at the end if it appears
    versus = re.sub(r'\bet al\b', 'et al.', versus)

    # Capitalize appropriately, ensuring '3M', 'LLC', 'LLP', and 'dba' remain unchanged
    words = versus.split()
    for i in range(len(words)):
        if words[i].lower() not in ['v.', 'et', 'al.', 'dba']:
            words[i] = words[i].capitalize()
        elif words[i].lower() == '3m':
            words[i] = '3M'
        elif words[i].lower() == 'dba':
            words[i] = 'dba'
        elif words[i].lower() == 'llc':
            words[i] = 'LLC'
        elif words[i].lower() == 'lp':
            words[i] = 'LP'
        elif words[i].lower() == 'afam':
            words[i] = 'AFAM'
        elif words[i].lower() == 'jf':
            words[i] = 'JF'
        elif words[i].lower() == 'loreal':
            words[i] = 'L\u2019Oreal'
        elif words[i].lower() == 'usa':
            words[i] = 'USA'
        elif words[i].lower() == 'ag':
            words[i] = 'AG'
        elif words[i].lower() == 'angiodynamics':
            words[i] = 'AngioDynamics'
        elif words[i].lower() == 'bp':
            words[i] = 'BP'

    # Join the words back into a string
    versus = ' '.join(words)

    return versus


def fix_versus_field():
    date_str = input("Enter the date (YYYYMMDD): ")
    try:
        datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
        print("Invalid date format. Please use YYYYMMDD.")
        return

    directory = os.path.join(PROJECT_ROOT, "src", "data", date_str, "dockets")

    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return

    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            full_path = os.path.join(directory, filename)

            with open(full_path, 'r') as file:
                data = json.load(file)

            changes_made = False

            # Check if 'original_filename' field needs correction
            if 'original_filename' in data:
                original_filename = data['original_filename']
                corrected_versus = fix_versus_value(original_filename)

                if original_filename != corrected_versus:
                    data['versus'] = corrected_versus
                    changes_made = True
                    print(f"\nUpdated 'versus' field in {filename}")
                    print(f"Original: {original_filename}")
                    print(f"Corrected: {corrected_versus}")

            # Remove 'processing_error' and 'last_error_date' if they exist
            if 'processing_error' in data:
                del data['processing_error']
                changes_made = True
                print("Removed 'processing_error' field.")

            if 'last_error_date' in data:
                del data['last_error_date']
                changes_made = True
                print("Removed 'last_error_date' field.")

            # Save the changes if any modifications were made
            if changes_made:
                if safe_json_write(full_path, data):
                    print("File updated successfully.")
                else:
                    print(f"Error updating file {filename}")
            else:
                print(f"No changes were needed for {filename}")


def process_s3_url(data):
    if 's3_url' in data:
        s3_url = data['s3_url']
        if s3_url.lower().endswith('.html'):
            data['s3_html'] = s3_url
            del data['s3_url']
            return True
    return False


def process_s3_url_in_directory(date_str):
    directory = os.path.join(PROJECT_ROOT, "src", "data", date_str, "dockets")

    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return

    files_updated = 0

    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            full_path = os.path.join(directory, filename)

            with open(full_path, 'r') as file:
                data = json.load(file)

            if process_s3_url(data):
                if safe_json_write(full_path, data):
                    files_updated += 1
                    print(f"Updated file: {filename}")
                else:
                    print(f"Error updating file {filename}")

    print(f"Total files updated: {files_updated}")


def find_mdl_num_na(date_str):
    # Validate the date format
    try:
        datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
        print("Invalid date format. Please use YYYYMMDD.")
        return

    # Define the directory path
    directory = os.path.join(PROJECT_ROOT, "src", "data", date_str, "dockets")

    # Check if the directory exists
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return

    # List to hold the file names with mdl_num == "NA"
    files_with_na_mdl = []

    # Iterate through each file in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            full_path = os.path.join(directory, filename)

            # Open and read the JSON file
            try:
                with open(full_path, 'r') as file:
                    data = json.load(file)
            except json.JSONDecodeError:
                print(f"Error decoding JSON for file: {filename}")
                continue

            # Check if 'mdl_num' exists and if it is equal to "NA"
            mdl_num = data.get('mdl_num', None)

            # Debugging statement to show the mdl_num value
            print(f"Checking file: {filename}, mdl_num: {mdl_num}")

            # Check if mdl_num is exactly "NA"
            if mdl_num and mdl_num.strip().upper() == "NA":
                files_with_na_mdl.append(filename)

    # If no files found, print diagnostic message
    if not files_with_na_mdl:
        print("No files found with mdl_num == 'NA'")
    else:
        print(f"Files with mdl_num == 'NA': {files_with_na_mdl}")

    # Return the list of files
    return files_with_na_mdl


def process_attorneys_data(start_date_str):
    result = []
    error_files = []

    # Convert input date string to datetime object
    current_date = datetime.strptime(start_date_str, "%m/%d/%y")

    base_path = os.path.join(PROJECT_ROOT, "src", "data")
    output_path = f"{base_path}/law_firms/pacer_dockets2.json"
    error_log_path = f"{base_path}/law_firms/error_log.txt"

    while True:
        # Convert current_date to YYYYMMDD format for directory search
        date_str = current_date.strftime("%Y%m%d")
        directory = f"{base_path}/{date_str}/dockets"

        if not os.path.exists(directory):
            print(f"Directory does not exist, skipping: {directory}")
        else:
            print(f"Processing directory: {directory}")
            for filename in tqdm(os.listdir(directory), desc=f"Processing files in {date_str}"):
                if filename.endswith('.json'):
                    file_path = os.path.join(directory, filename)
                    try:
                        with open(file_path, 'r') as file:
                            data = json.load(file)

                        if 'attorneys_gpt' in data:
                            entry = {
                                'filing_date': data.get('filing_date'),
                                'date_filed': data.get('date_filed'),
                                'court_id': data.get('court_id'),
                                'docket_num': data.get('docket_num'),
                                'law_firm': data.get('law_firm'),
                                'law_firm2': data.get('law_firm2'),
                                'attorney': data.get('attorney'),
                                'attorneys_gpt': data['attorneys_gpt'],
                                's3_link': data.get('s3_link')
                            }
                            result.append(entry)
                    except json.JSONDecodeError as e:
                        error_message = f"Error in file {file_path}: {str(e)}"
                        print(error_message)
                        error_files.append(error_message)
                    except Exception as e:
                        error_message = f"Unexpected error in file {file_path}: {str(e)}"
                        print(error_message)
                        error_files.append(error_message)

        # Increment the date by one day
        current_date += timedelta(days=1)

        # Check if we've reached tomorrow's date
        if current_date > datetime.now():
            break

    # Save the result to the output file
    if not safe_json_write(output_path, result, indent=2):
        print(f"Error saving data to {output_path}")

    # Save error log
    with open(error_log_path, 'w') as error_file:
        for error in error_files:
            error_file.write(f"{error}\n")

    print(f"Data saved to {output_path}")
    print(f"Error log saved to {error_log_path}")
    print(f"Total files processed: {len(result)}")
    print(f"Total files with errors: {len(error_files)}")


def process_attorneys_data_to_df(base_path):
    data = []

    # Load the JSON file
    json_path = os.path.join(base_path, "law_firms", "pacer_dockets2.json")
    with open(json_path, 'r') as file:
        json_data = json.load(file)

    # Process each item in the JSON data
    for item in tqdm(json_data, desc="Processing entries"):
        filing_date = item.get('filing_date', None)
        date_filed = item.get('date_filed')
        law_firm = item.get('law_firm', None)
        law_firm2 = item.get('law_firm22', None)
        docket_num = item.get('docket_num', None)
        court_id = item.get('court_id', None)
        s3_link = item.get('s3_link', None)  # New field

        # Process each attorney entry in attorneys_gpt
        for attorney in item.get('attorneys_gpt', []):
            # Check if attorney is a dictionary
            if isinstance(attorney, dict):
                law_firm_gpt = attorney.get('law_firm')
                email_gpt = attorney.get('email_address', '')
                email_gpt = email_gpt.split('@')[-1] if email_gpt and '@' in email_gpt else ''

                # Only add the row if both law_firm_gpt and email_gpt are valid
                if law_firm_gpt not in ['NA', None, ''] and email_gpt not in ['NA', None, '']:
                    data.append({
                        'filing_date': filing_date,
                        'date_filed': date_filed,
                        'court_id': court_id,
                        'docket_num': docket_num,
                        'law_firm': law_firm,
                        'law_firm2': law_firm2,
                        'law_firm_gpt': law_firm_gpt,
                        'email_gpt': email_gpt,
                        's3_link': s3_link  # New field
                    })
            else:
                # If attorney is not a dictionary, log this occurrence
                print(f"Skipping non-dictionary attorney data: {attorney}")

    # Create DataFrame from the collected data
    df = pd.DataFrame(data)

    # Drop rows where law_firm_gpt or email_gpt is NaN
    df = df.dropna(subset=['law_firm_gpt', 'email_gpt'])

    # Drop rows where law_firm_gpt or email_gpt is 'NA'
    df = df[(df['law_firm_gpt'] != 'NA') & (df['email_gpt'] != 'NA')]

    # Drop duplicates based on law_firm_gpt and email_gpt, keeping the first occurrence
    df = df.drop_duplicates(subset=['law_firm_gpt', 'email_gpt'], keep='first')

    # Return the DataFrame for further use
    return df


def update_added_date_in_json():
    # Ask for the directory date
    date_str = input("Enter the directory date (YYYYMMDD): ")
    try:
        # Validate the directory date format
        datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
        print("Invalid date format for directory. Please use YYYYMMDD.")
        return

    # Ask for the new date to change `added_date` to
    new_date = input("Enter the new date to change 'added_date' to (MM/DD/YY): ")
    try:
        # Validate the new date format
        datetime.strptime(new_date, "%m/%d/%y")
    except ValueError:
        print("Invalid date format for 'added_date'. Please use MM/DD/YY.")
        return

    # Construct the directory path
    directory = os.path.join(PROJECT_ROOT, "src", "data", date_str, "dockets")

    # Check if the directory exists
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return

    files_updated = 0

    # Iterate through JSON files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            full_path = os.path.join(directory, filename)

            try:
                # Open and load the JSON file
                with open(full_path, 'r') as file:
                    data = json.load(file)

                # Check if 'added_date' exists and update it
                if 'added_date' in data:
                    old_date = data['added_date']
                    if old_date != new_date:
                        data['added_date'] = new_date  # Update the date
                        print(f"Updated 'added_date' in {filename} from {old_date} to {new_date}")

                        # Save the updated JSON file
                        if safe_json_write(full_path, data):
                            files_updated += 1
                        else:
                            print(f"Error updating file {filename}")

            except json.JSONDecodeError:
                print(f"Error decoding JSON for file: {filename}")
            except Exception as e:
                print(f"Unexpected error processing file {filename}: {e}")

    print(f"Total files updated: {files_updated}")


def delete_title_for_mdl_3140():
    directory = os.path.join(PROJECT_ROOT, "data", "20250225", "dockets")
    files_updated = 0

    # Check if the directory exists
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return

    # Iterate through JSON files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            full_path = os.path.join(directory, filename)

            try:
                # Open and load the JSON file
                with open(full_path, 'r') as file:
                    data = json.load(file)

                # Check if mdl_num is '3140'
                if data.get('mdl_num') == '3140':
                    if 'title' in data:
                        del data['title']
                        # Save the updated JSON file
                        if safe_json_write(full_path, data):
                            files_updated += 1
                            print(f"Deleted 'title' field in {filename}")
                        else:
                            print(f"Error updating file {filename}")

            except json.JSONDecodeError:
                print(f"Error decoding JSON for file: {filename}")
            except Exception as e:
                print(f"Unexpected error processing file {filename}: {e}")

    print(f"Total files updated: {files_updated}")


def delete_versus_field():
    directory = os.path.join(PROJECT_ROOT, "data", "20250225", "dockets")
    files_updated = 0

    # Check if the directory exists
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return

    # Iterate through JSON files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            full_path = os.path.join(directory, filename)

            try:
                # Open and load the JSON file
                with open(full_path, 'r') as file:
                    data = json.load(file)

                # Delete versus field if it exists
                if 'versus' in data:
                    del data['versus']
                    # Save the updated JSON file
                    if safe_json_write(full_path, data):
                        files_updated += 1
                        print(f"Deleted 'versus' field in {filename}")
                    else:
                        print(f"Error updating file {filename}")

            except json.JSONDecodeError:
                print(f"Error decoding JSON for file: {filename}")
            except Exception as e:
                print(f"Unexpected error processing file {filename}: {e}")

    print(f"Total files updated: {files_updated}")


def delete_num_plaintiffs():
    directory = os.path.join(PROJECT_ROOT, "data", "20250225", "dockets")
    files_updated = 0

    # Check if the directory exists
    if not os.path.exists(directory):
        print(f"Directory does not exist: {directory}")
        return

    # Iterate through JSON files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            full_path = os.path.join(directory, filename)

            try:
                # Open and load the JSON file
                with open(full_path, 'r') as file:
                    data = json.load(file)

                # Delete versus field if it exists
                if 'num_plaintiffs' in data:
                    del data['num_plaintiffs']
                    # Save the updated JSON file
                    if safe_json_write(full_path, data):
                        files_updated += 1
                        print(f"Deleted 'num_plaintiffs' field in {filename}")
                    else:
                        print(f"Error updating file {filename}")

            except json.JSONDecodeError:
                print(f"Error decoding JSON for file: {filename}")
            except Exception as e:
                print(f"Unexpected error processing file {filename}: {e}")

    print(f"Total files updated: {files_updated}")


if __name__ == "__main__":
    base_path = os.path.join(PROJECT_ROOT, "src", "data")
    delete_num_plaintiffs()  # Add this line to run the new function
