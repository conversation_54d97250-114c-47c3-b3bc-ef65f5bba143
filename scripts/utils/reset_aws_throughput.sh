#!/bin/bash

# Variables
TABLE_NAME="FBAdArchive"
GSI_NAMES=("AdArchiveID-index" "ID-LastUpdated-index" "LastUpdated-index" "PageID-StartDate-index" "StartDate-index")
MIN_CAPACITY=1
MAX_CAPACITY=15
TARGET_UTILIZATION=70

# Function to register scalable targets
register_scalable_target() {
    local resource_id=$1
    local scalable_dimension=$2

    aws application-autoscaling register-scalable-target \
        --service-namespace dynamodb \
        --resource-id "$resource_id" \
        --scalable-dimension "$scalable_dimension" \
        --min-capacity "$MIN_CAPACITY" \
        --max-capacity "$MAX_CAPACITY"
}

# Function to apply auto-scaling policies
apply_auto_scaling_policy() {
    local resource_id=$1
    local scalable_dimension=$2
    local policy_name=$3

    # Determine the correct predefined metric type based on the scalable dimension
    if [[ "$scalable_dimension" == *"WriteCapacityUnits"* ]]; then
        predefined_metric_type="DynamoDBWriteCapacityUtilization"
    else
        predefined_metric_type="DynamoDBReadCapacityUtilization"
    fi

    aws application-autoscaling put-scaling-policy \
        --service-namespace dynamodb \
        --resource-id "$resource_id" \
        --scalable-dimension "$scalable_dimension" \
        --policy-name "$policy_name" \
        --policy-type TargetTrackingScaling \
        --target-tracking-scaling-policy-configuration "{
            \"TargetValue\": $TARGET_UTILIZATION,
            \"PredefinedMetricSpecification\": {
                \"PredefinedMetricType\": \"$predefined_metric_type\"
            },
            \"ScaleOutCooldown\": 60,
            \"ScaleInCooldown\": 60
        }"
}

# Set provisioned capacity for the table
aws dynamodb update-table \
    --table-name "$TABLE_NAME" \
    --provisioned-throughput ReadCapacityUnits=1,WriteCapacityUnits=1

# Set provisioned capacity for all GSIs
for GSI_NAME in "${GSI_NAMES[@]}"; do
    aws dynamodb update-table \
        --table-name "$TABLE_NAME" \
        --global-secondary-index-updates "[
            {
                \"Update\": {
                    \"IndexName\": \"$GSI_NAME\",
                    \"ProvisionedThroughput\": {
                        \"ReadCapacityUnits\": 1,
                        \"WriteCapacityUnits\": 1
                    }
                }
            }
        ]"
done

# Register scalable targets and apply auto-scaling policies for the table
register_scalable_target "table/$TABLE_NAME" "dynamodb:table:ReadCapacityUnits"
register_scalable_target "table/$TABLE_NAME" "dynamodb:table:WriteCapacityUnits"

apply_auto_scaling_policy "table/$TABLE_NAME" "dynamodb:table:ReadCapacityUnits" "${TABLE_NAME}ReadScalingPolicy"
apply_auto_scaling_policy "table/$TABLE_NAME" "dynamodb:table:WriteCapacityUnits" "${TABLE_NAME}WriteScalingPolicy"

# Register scalable targets and apply auto-scaling policies for all GSIs
for GSI_NAME in "${GSI_NAMES[@]}"; do
    register_scalable_target "table/$TABLE_NAME/index/$GSI_NAME" "dynamodb:index:ReadCapacityUnits"
    register_scalable_target "table/$TABLE_NAME/index/$GSI_NAME" "dynamodb:index:WriteCapacityUnits"

    apply_auto_scaling_policy "table/$TABLE_NAME/index/$GSI_NAME" "dynamodb:index:ReadCapacityUnits" "${GSI_NAME}ReadScalingPolicy"
    apply_auto_scaling_policy "table/$TABLE_NAME/index/$GSI_NAME" "dynamodb:index:WriteCapacityUnits" "${GSI_NAME}WriteScalingPolicy"
done

echo "Auto-scaling and provisioned capacity configuration completed for table $TABLE_NAME and its GSIs."