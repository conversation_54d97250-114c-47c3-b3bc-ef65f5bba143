#!/usr/bin/env python3
"""
Regenerate missing ad HTML pages by parsing local HTML reports.

Usage:
    python -m src.scripts.regenerate_missing_ads --date 20250528
"""

import sys
import os
import argparse
import asyncio
import logging
from typing import List, Tuple
import pandas as pd
from bs4 import BeautifulSoup
import re
from datetime import datetime
from pathlib import Path

# Rich formatting imports
try:
    from rich.console import Console
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeRemainingColumn
    from rich.logging import RichHandler
    from rich.panel import Panel
    from rich.table import Table
    from rich import print as rprint
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Warning: 'rich' library not available. Install with: pip install rich")

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.lib.config_adapter import load_config
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.s3_async import S3AsyncStorage
from reports.config import ReportConfig
from reports.renderer import ReportRenderer
from reports.ad_page_generator import AdPageGenerator
from reports.data_loader import StaticDataLoader
from reports.processor import ReportDataProcessor


class AdPageRegenerator:
    """Handles regeneration of missing ad pages with rich formatting."""
    
    def __init__(self, report_date: str):
        self.report_date = report_date
        self.config = load_config(report_date)
        self.report_config = ReportConfig(self.config)
        
        # Initialize rich console if available
        self.console = Console() if RICH_AVAILABLE else None
        
        # Initialize async repositories
        self.fb_manager = FBArchiveRepository()
        self.s3_manager = S3AsyncStorage(self.config, bucket_name=self.report_config.bucket_name)
        self.pacer_manager = PacerRepository()
        
        # Initialize report components
        self.static_data_loader = StaticDataLoader()
        self.data_processor = ReportDataProcessor(self.report_config, self.pacer_manager)
        self.renderer = ReportRenderer(self.report_config, self.static_data_loader, self.data_processor)
        self.ad_page_generator = AdPageGenerator(self.report_config, self.renderer, self.s3_manager)
        
        # Set up logging
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Set up file and console logging with rich formatting."""
        # Create logs directory
        log_dir = Path('../../src/logs')
        log_dir.mkdir(exist_ok=True)
        
        # Create logger
        logger = logging.getLogger('regenerate_ads')
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        logger.handlers = []
        
        # File handler
        log_file = log_dir / f'regenerate_ads_{self.report_date}_{datetime.now().strftime("%H%M%S")}.log'
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # Console handler with rich formatting
        if RICH_AVAILABLE:
            console_handler = RichHandler(
                console=self.console,
                show_time=False,
                show_path=False
            )
            console_handler.setLevel(logging.INFO)
            logger.addHandler(console_handler)
        else:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter('%(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        logger.info(f"Starting ad regeneration for date: {self.report_date}")
        logger.info(f"Log file: {log_file}")
        
        return logger
    
    async def find_missing_ads_from_html(self) -> List[Tuple[str, str]]:
        """Find missing ads by parsing the local HTML report.
        
        Returns:
            List of tuples (ad_id, s3_key) for missing ads
        """
        # Look for the local report file
        report_path = Path('../../src/data') / self.report_date / 'reports' / 'index.html'
        
        if not report_path.exists():
            self.logger.error(f"Report file not found: {report_path}")
            if self.console:
                self.console.print(f"[red]✗ Report file not found: {report_path}[/red]")
            return []
        
        self.logger.info(f"Parsing report file: {report_path}")
        if self.console:
            self.console.print(f"[cyan]📄 Parsing report: {report_path}[/cyan]")
        
        try:
            # Parse HTML to find ad links
            with open(report_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find all links that point to ads on CDN
            ad_links = []
            cdn_pattern = re.compile(r'https://cdn\.lexgenius\.ai/ads/(\d+)/ads/(\d+)\.html')
            
            for link in soup.find_all('a', href=True):
                href = link['href']
                match = cdn_pattern.match(href)
                if match:
                    date_part = match.group(1)
                    ad_id = match.group(2)
                    s3_key = f"{date_part}/ads/{ad_id}.html"
                    ad_links.append((ad_id, s3_key, href))
            
            self.logger.info(f"Found {len(ad_links)} ad links in HTML report")
            if self.console:
                self.console.print(f"[green]✓ Found {len(ad_links)} ad links in report[/green]")
            
            # Check which ads are missing from S3
            missing_ads = []
            
            if RICH_AVAILABLE and self.console:
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TaskProgressColumn(),
                    TimeRemainingColumn(),
                    console=self.console
                ) as progress:
                    task = progress.add_task("[cyan]Checking S3 for existing ads...", total=len(ad_links))
                    
                    for ad_id, s3_key, href in ad_links:
                        if not self.s3_manager.file_exists(s3_key):
                            missing_ads.append((ad_id, s3_key))
                            self.logger.warning(f"Missing ad page in S3: {s3_key}")
                        progress.update(task, advance=1)
            else:
                # Fallback without rich
                checked_count = 0
                for ad_id, s3_key, href in ad_links:
                    checked_count += 1
                    if checked_count % 100 == 0:
                        self.logger.info(f"Progress: Checked {checked_count}/{len(ad_links)} ads...")
                    
                    if not self.s3_manager.file_exists(s3_key):
                        missing_ads.append((ad_id, s3_key))
                        self.logger.warning(f"Missing ad page in S3: {s3_key}")
            
            self.logger.info(f"Found {len(missing_ads)} missing ad pages")
            
            # Display summary
            if self.console and missing_ads:
                table = Table(title=f"Missing Ads Summary ({len(missing_ads)} total)")
                table.add_column("Ad ID", style="cyan")
                table.add_column("S3 Key", style="yellow")
                
                # Show first 10 missing ads
                for ad_id, s3_key in missing_ads[:10]:
                    table.add_row(ad_id, s3_key)
                
                if len(missing_ads) > 10:
                    table.add_row("...", f"... and {len(missing_ads) - 10} more")
                
                self.console.print(table)
            
            return missing_ads
            
        except Exception as e:
            self.logger.error(f"Error parsing HTML report: {e}", exc_info=True)
            if self.console:
                self.console.print(f"[red]✗ Error parsing HTML report: {e}[/red]")
            return []
    
    async def regenerate_ad_pages(self, missing_ads_info: List[Tuple[str, str]]) -> dict:
        """Regenerate HTML pages for missing ads.
        
        Args:
            missing_ads_info: List of tuples (ad_id, s3_key)
            
        Returns:
            Dictionary with success and failed counts
        """
        if not missing_ads_info:
            self.logger.warning("No ads to regenerate")
            return {'success': 0, 'failed': 0}
        
        ad_ids = [ad_id for ad_id, _ in missing_ads_info]
        
        self.logger.info(f"Starting regeneration of {len(ad_ids)} ad pages...")
        if self.console:
            self.console.print(Panel(f"[bold cyan]Regenerating {len(ad_ids)} missing ad pages[/bold cyan]"))
        
        # Fetch ad data from database
        all_ad_data = []
        failed_fetches = []
        
        if RICH_AVAILABLE and self.console:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task("[cyan]Fetching ad data from database...", total=len(ad_ids))
                
                async with self.fb_manager:
                    for ad_id in ad_ids:
                        try:
                            ads = await self.fb_manager.get_ads_by_archive_id(ad_id)
                            if ads:
                                all_ad_data.append(ads[0])
                            else:
                                failed_fetches.append(ad_id)
                                self.logger.warning(f"Ad {ad_id} not found in database")
                        except Exception as e:
                            failed_fetches.append(ad_id)
                            self.logger.error(f"Error fetching ad {ad_id}: {e}")
                        progress.update(task, advance=1)
        else:
            # Fallback without rich
            async with self.fb_manager:
                for idx, ad_id in enumerate(ad_ids):
                    if (idx + 1) % 50 == 0:
                        self.logger.info(f"Fetched {idx + 1}/{len(ad_ids)} ads...")
                    try:
                        ads = await self.fb_manager.get_ads_by_archive_id(ad_id)
                        if ads:
                            all_ad_data.append(ads[0])
                        else:
                            failed_fetches.append(ad_id)
                            self.logger.warning(f"Ad {ad_id} not found in database")
                    except Exception as e:
                        failed_fetches.append(ad_id)
                        self.logger.error(f"Error fetching ad {ad_id}: {e}")
        
        if not all_ad_data:
            self.logger.error("No ad data found for regeneration")
            if self.console:
                self.console.print("[red]✗ No ad data found in database[/red]")
            return {'success': 0, 'failed': len(ad_ids)}
        
        # Convert to DataFrame for the ad page generator
        ad_df = pd.DataFrame(all_ad_data)
        
        # Rename columns to match expected format
        column_mapping = {
            'AdArchiveID': 'ad_archive_id',
            'AdCreativeId': 'ad_creative_id',
            'IsActive': 'is_active',
            'LawFirm': 'law_firm',
            'PageName': 'page_name',
            'PageID': 'page_id',
            'StartDate': 'start_date',
            'EndDate': 'end_date',
            'Summary': 'summary',
            'CtaText': 'cta_text',
            'Title': 'title',
            'Body': 'body',
            'LinkDescription': 'link_description',
            'Caption': 'caption',
            'LinkUrl': 'link_url',
            'PublisherPlatform': 'publisher_platform',
            'OriginalImageUrl': 'original_image_url'
        }
        
        ad_df.rename(columns=column_mapping, inplace=True)
        
        # Convert date formats
        for date_col in ['start_date', 'end_date']:
            if date_col in ad_df.columns:
                ad_df[date_col] = pd.to_datetime(ad_df[date_col], format='%Y%m%d', errors='coerce').dt.strftime('%m/%d/%y').fillna('')
        
        self.logger.info(f"Prepared {len(ad_df)} ads for regeneration")
        if self.console:
            self.console.print(f"[green]✓ Prepared {len(ad_df)} ads for regeneration[/green]")
        
        # Generate and upload the ad pages
        if self.console:
            self.console.print("[cyan]Generating and uploading ad pages...[/cyan]")
        
        await self.ad_page_generator.generate_and_upload_ad_pages(ad_df)
        
        # Validate regeneration
        validation_results = await self.ad_page_generator.validate_ad_pages(
            [ad_id for ad_id, _ in missing_ads_info]
        )
        
        success_count = sum(1 for exists in validation_results.values() if exists)
        failed_count = len(missing_ads_info) - success_count + len(failed_fetches)
        
        # Display results
        if self.console:
            result_panel = Panel(
                f"[bold green]✓ Success: {success_count}[/bold green]\n"
                f"[bold red]✗ Failed: {failed_count}[/bold red]",
                title="Regeneration Results",
                border_style="green" if failed_count == 0 else "yellow"
            )
            self.console.print(result_panel)
        
        return {'success': success_count, 'failed': failed_count}
    
    async def run_automatic_regeneration(self):
        """Main method to automatically find and regenerate missing ads."""
        if self.console:
            self.console.print(Panel(
                f"[bold cyan]Ad Page Regeneration[/bold cyan]\n"
                f"Date: {self.report_date}",
                title="LexGenius Ad Regenerator"
            ))
        
        # Find missing ads
        missing_ads_info = await self.find_missing_ads_from_html()
        
        if not missing_ads_info:
            self.logger.info("No missing ads found - all ads exist in S3")
            if self.console:
                self.console.print("[green]✓ All ads already exist in S3 - nothing to regenerate[/green]")
            return
        
        # Regenerate missing ads
        results = await self.regenerate_ad_pages(missing_ads_info)
        
        # Log final results
        self.logger.info(f"Regeneration complete: {results['success']} successful, {results['failed']} failed")
        
        if self.console:
            if results['failed'] == 0:
                self.console.print(f"\n[bold green]✓ All {results['success']} ads regenerated successfully![/bold green]")
            else:
                self.console.print(f"\n[yellow]⚠ Regeneration complete with some failures[/yellow]")


async def main():
    parser = argparse.ArgumentParser(
        description='Automatically regenerate missing ad HTML pages',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m src.scripts.regenerate_missing_ads --date 20250528
  python src/scripts/regenerate_missing_ads.py --date 20250528
        """
    )
    
    parser.add_argument(
        '--date',
        required=True,
        help='Report date in YYYYMMDD format'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Only check for missing ads without regenerating'
    )
    
    args = parser.parse_args()
    
    # Validate date format
    try:
        datetime.strptime(args.date, '%Y%m%d')
    except ValueError:
        print(f"Error: Invalid date format '{args.date}'. Use YYYYMMDD format.")
        sys.exit(1)
    
    # Run regeneration
    regenerator = AdPageRegenerator(args.date)
    
    if args.dry_run:
        missing_ads = await regenerator.find_missing_ads_from_html()
        if regenerator.console:
            regenerator.console.print(f"\n[cyan]Dry run complete. Found {len(missing_ads)} missing ads.[/cyan]")
    else:
        await regenerator.run_automatic_regeneration()


if __name__ == "__main__":
    asyncio.run(main())