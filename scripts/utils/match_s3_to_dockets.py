#!/usr/bin/env python3
"""
Script to match S3 HTML files to docket JSON files.
Compares files in data/YYYYMMDD/dockets/ with S3 objects in YYYYMMDD/html/.
Updates JSON files with correct S3 HTML paths when matches are found.
Validates HTML content to identify files that need redownloading.
"""

import argparse
import asyncio
import json
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
import aiohttp

from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.text import Text

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.infrastructure.storage.s3_async import S3AsyncStorage

console = Console(stderr=True)  # Redirect all Rich console output to stderr


def extract_case_identifier(filename: str) -> str:
    """
    Extract case identifier in format {court_id}_YY_NNNNN from filename.

    Args:
        filename: File name to extract from

    Returns:
        Case identifier or empty string if not found
    """
    # Pattern: court_id_YY_NNNNN (e.g., mad_23_12345)
    pattern = r'([a-z]+d?)_(\d{2})_(\d{5})'
    match = re.search(pattern, filename.lower())
    if match:
        return f"{match.group(1)}_{match.group(2)}_{match.group(3)}"
    return ""


async def get_s3_html_files(s3_storage: S3AsyncStorage, date_str: str) -> List[str]:
    """
    Get list of HTML files from S3 for given date.

    Args:
        s3_storage: S3 storage instance
        date_str: Date in YYYYMMDD format

    Returns:
        List of S3 object keys
    """
    prefix = f"{date_str}/html/"

    with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
    ) as progress:
        task = progress.add_task(f"Fetching S3 objects from {prefix}...", total=None)

        try:
            objects = await s3_storage.list_objects(prefix)
            progress.update(task, completed=True)

            # Filter only HTML files
            html_files = [obj for obj in objects if obj.endswith('.html')]

            console.print(f"[green]Found {len(html_files)} HTML files in S3[/green]")
            return html_files

        except Exception as e:
            console.print(f"[red]Error fetching S3 objects: {e}[/red]")
            return []


def get_local_json_files(date_str: str) -> List[str]:
    """
    Get list of JSON files from local dockets directory.

    Args:
        date_str: Date in YYYYMMDD format

    Returns:
        List of JSON file paths
    """
    dockets_dir = Path(f"data/{date_str}/dockets")

    if not dockets_dir.exists():
        console.print(f"[yellow]Directory not found: {dockets_dir}[/yellow]")
        return []

    json_files = list(dockets_dir.glob("*.json"))
    console.print(f"[green]Found {len(json_files)} JSON files in {dockets_dir}[/green]")

    return [str(f) for f in json_files]


def match_files(json_files: List[str], s3_files: List[str]) -> Tuple[Dict[str, List[str]], List[str], Set[str]]:
    """
    Match JSON files to ALL S3 HTML files based on case identifier.

    Args:
        json_files: List of local JSON file paths
        s3_files: List of S3 object keys

    Returns:
        Tuple of (matches dict with multiple S3 matches per JSON, unmatched json files, unmatched s3 files)
    """
    # Extract identifiers for JSON files
    json_map = {}
    for json_file in json_files:
        identifier = extract_case_identifier(os.path.basename(json_file))
        if identifier:
            json_map[identifier] = json_file

    # Build map of S3 files by identifier (can have multiple files per identifier)
    s3_map = {}
    for s3_file in s3_files:
        identifier = extract_case_identifier(os.path.basename(s3_file))
        if identifier:
            if identifier not in s3_map:
                s3_map[identifier] = []
            s3_map[identifier].append(s3_file)

    # Find all matches for each JSON file
    matches = {}
    matched_s3_files = set()

    for identifier, json_file in json_map.items():
        if identifier in s3_map:
            matches[json_file] = s3_map[identifier]
            matched_s3_files.update(s3_map[identifier])

    # Find unmatched files
    unmatched_json = [f for id, f in json_map.items() if id not in s3_map]
    all_s3_files = set(s3_files)
    unmatched_s3 = all_s3_files - matched_s3_files

    return matches, unmatched_json, unmatched_s3


def update_json_s3_html(json_file: str, s3_path: str, dry_run: bool = True) -> Tuple[bool, str, str]:
    """
    Update the s3_html field in a JSON file.
    
    Args:
        json_file: Path to the JSON file
        s3_path: S3 object key (e.g., "20250616/html/file.html")
        dry_run: If True, don't actually update the file
        
    Returns:
        Tuple of (updated, old_value, new_value)
    """
    # Convert S3 path to relative URL format
    relative_path = f"/{s3_path}"
    
    try:
        # Read JSON file
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        old_value = data.get('s3_html', 'Not set')
        
        # Check if update is needed
        if old_value == relative_path:
            return False, old_value, relative_path
        
        # Update the field
        data['s3_html'] = relative_path
        
        # Write back if not dry run
        if not dry_run:
            with open(json_file, 'w') as f:
                json.dump(data, f, indent=2)
        
        return True, old_value, relative_path
        
    except Exception as e:
        console.print(f"[red]Error updating {json_file}: {e}[/red]")
        return False, "Error", "Error"


async def rename_s3_file(s3_storage: S3AsyncStorage, old_key: str, new_key: str) -> bool:
    """
    Rename an S3 file by copying to new location and deleting the old one.
    
    Args:
        s3_storage: S3 storage instance
        old_key: Current S3 object key
        new_key: New S3 object key
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Copy the object to the new key
        console.print(f"[yellow]Copying {old_key} to {new_key}...[/yellow]")
        copy_success = await s3_storage.copy_object(old_key, new_key)
        
        if not copy_success:
            console.print(f"[red]Failed to copy {old_key} to {new_key}[/red]")
            return False
        
        # Delete the old object
        console.print(f"[yellow]Deleting old file {old_key}...[/yellow]")
        delete_success = await s3_storage.delete_object(old_key)
        
        if not delete_success:
            console.print(f"[red]Failed to delete old file {old_key}[/red]")
            # Try to clean up the copy
            await s3_storage.delete_object(new_key)
            return False
        
        console.print(f"[green]✓ Successfully renamed {old_key} to {new_key}[/green]")
        return True
        
    except Exception as e:
        console.print(f"[red]Error renaming S3 file: {e}[/red]")
        return False


def compare_filenames(json_filename: str, s3_filename: str) -> bool:
    """
    Compare filenames and show differences if they're not identical.
    
    Args:
        json_filename: JSON filename
        s3_filename: S3 filename
        
    Returns:
        True if filenames are identical, False otherwise
    """
    json_name = os.path.basename(json_filename)
    s3_name = os.path.basename(s3_filename)
    
    if json_name == s3_name:
        return True
    
    # Show character-by-character differences
    console.print(f"  [yellow]⚠ Filename differences:[/yellow]")
    
    # Create side-by-side comparison
    max_len = max(len(json_name), len(s3_name))
    json_padded = json_name.ljust(max_len)
    s3_padded = s3_name.ljust(max_len)
    
    json_text = Text()
    s3_text = Text()
    
    for i in range(max_len):
        json_char = json_padded[i] if i < len(json_name) else ' '
        s3_char = s3_padded[i] if i < len(s3_name) else ' '
        
        if json_char == s3_char:
            json_text.append(json_char, style="white")
            s3_text.append(s3_char, style="white")
        else:
            json_text.append(json_char, style="red bold")
            s3_text.append(s3_char, style="red bold")
    
    console.print(f"    JSON: ", end="")
    console.print(json_text)
    console.print(f"    S3:   ", end="")
    console.print(s3_text)
    console.print()
    
    return False


async def validate_html_content(url: str, session: aiohttp.ClientSession) -> Tuple[bool, str]:
    """
    Validate HTML content by checking for COMPLAINT or REMOVAL text.
    
    Valid: Contains "COMPLAINT" or "REMOVAL"
    Invalid: Everything else
    
    Args:
        url: CDN URL to check
        session: aiohttp session for making requests
        
    Returns:
        Tuple of (is_valid, reason)
    """
    try:
        async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
            if response.status != 200:
                return False, f"HTTP {response.status}"
            
            content = await response.text()
            content_upper = content.upper()
            
            # Check for valid patterns - must contain "COMPLAINT" or "REMOVAL"
            if "COMPLAINT" in content_upper:
                return True, "Valid: Contains COMPLAINT"
            elif "REMOVAL" in content_upper:
                return True, "Valid: Contains REMOVAL"
            else:
                # Everything else is considered invalid
                return False, "Invalid: No COMPLAINT or REMOVAL"
            
    except asyncio.TimeoutError:
        return False, "Timeout"
    except Exception as e:
        return False, f"Error: {str(e)}"


async def display_results_with_validation(matches: Dict[str, List[str]], unmatched_json: List[str], 
                                         unmatched_s3: Set[str], dry_run: bool = True, 
                                         validate: bool = False, update_different_filenames: bool = False,
                                         rename_s3_files: bool = False, s3_storage: S3AsyncStorage = None) -> List[Dict]:
    """
    Display matching results with optional HTML validation.

    Args:
        matches: Dictionary of JSON file to S3 file matches
        unmatched_json: List of unmatched JSON files
        unmatched_s3: List of unmatched S3 files
        dry_run: If True, only show what would be updated
        validate: If True, validate HTML content
        update_different_filenames: If True, update JSON files with valid HTML links even when filenames are different
        rename_s3_files: If True, rename S3 files to match JSON filenames when valid but misnamed
        s3_storage: S3 storage instance (required if rename_s3_files is True)
        
    Returns:
        List of dictionaries with court_id and docket_num for Group 3 (invalid HTML) and unmatched JSON files
    """
    console.print("\n[bold cyan]═══ MATCHING RESULTS ═══[/bold cyan]\n")
    
    mode_text = "[yellow]DRY RUN MODE[/yellow]" if dry_run else "[bold red]UPDATE MODE[/bold red]"
    validation_text = " with [cyan]HTML validation[/cyan]" if validate else ""
    different_filenames_text = " [magenta](including different filenames)[/magenta]" if update_different_filenames else ""
    rename_text = " [cyan](with S3 file renaming)[/cyan]" if rename_s3_files else ""
    console.print(f"Mode: {mode_text}{validation_text}{different_filenames_text}{rename_text}\n")

    # Track statistics
    total_updates = 0
    exact_matches = 0
    different_filenames = 0
    valid_files = 0
    invalid_files = 0
    unknown_files = 0
    files_to_redownload = []
    
    # Track validation results for grouping
    validation_results = {}
    
    # Initialize groups for validation results
    group1_exact_match = []
    group2_different_names = []
    group3_invalid = []

    # Create aiohttp session for validation if needed
    session = None
    if validate:
        session = aiohttp.ClientSession()

    # Display matches
    if matches:
        console.print(f"[bold green]✓ Found {len(matches)} JSON files with S3 matches:[/bold green]\n")

        # Process matches with progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            console=console
        ) as progress:
            task = progress.add_task("Processing matches...", total=len(matches))
            
            for json_file, s3_files in sorted(matches.items()):
                json_name = os.path.basename(json_file)
                console.print(f"\n[cyan]Processing {json_name} ({len(s3_files)} S3 matches)[/cyan]")
                
                # Process ALL S3 matches for this JSON file
                valid_match_found = False
                best_match = None
                all_match_results = []
                
                for s3_file in sorted(s3_files):
                    s3_name = os.path.basename(s3_file)
                    console.print(f"  [blue]Checking:[/blue] {s3_file}")
                    
                    # Validate HTML content if requested
                    if validate and session:
                        cdn_url = f"https://cdn.lexgenius.ai/{s3_file}"
                        is_valid, reason = await validate_html_content(cdn_url, session)
                        
                        if is_valid is True:
                            console.print(f"    [green]✓ {reason}[/green]")
                            valid_files += 1
                            valid_match_found = True
                            
                            # Check if filenames match
                            json_base = json_name.replace('.json', '')
                            s3_base = s3_name.replace('.html', '')
                            exact_filename_match = json_base.lower() == s3_base.lower()
                            
                            all_match_results.append({
                                's3_file': s3_file,
                                's3_name': s3_name,
                                'is_valid': True,
                                'exact_match': exact_filename_match,
                                'reason': reason
                            })
                            
                            # Track validation results
                            if json_file not in validation_results:
                                validation_results[json_file] = []
                            validation_results[json_file].append({
                                's3_file': s3_file,
                                's3_name': s3_name,
                                'is_valid': True,
                                'exact_match': exact_filename_match
                            })
                            
                            # Prefer exact filename matches as best match
                            if not best_match or exact_filename_match:
                                best_match = s3_file
                                if exact_filename_match:
                                    exact_matches += 1
                                else:
                                    different_filenames += 1
                        else:
                            console.print(f"    [red]✗ {reason}[/red]")
                            invalid_files += 1
                            all_match_results.append({
                                's3_file': s3_file,
                                's3_name': s3_name,
                                'is_valid': False,
                                'exact_match': False,
                                'reason': reason
                            })
                            
                            # Track validation results
                            if json_file not in validation_results:
                                validation_results[json_file] = []
                            validation_results[json_file].append({
                                's3_file': s3_file,
                                's3_name': s3_name,
                                'is_valid': False,
                                'exact_match': False
                            })
                
                # If no valid match found, add to redownload list
                if not valid_match_found:
                    files_to_redownload.append({
                        'json': json_file,
                        's3': 'No valid S3 match',
                        'reason': 'All S3 matches invalid',
                        'all_matches': all_match_results
                    })
                elif best_match and not dry_run:
                    # Update s3_html field with best match
                    updated, old_val, new_val = update_json_s3_html(json_file, best_match, dry_run)
                    if updated or (old_val != new_val):
                        total_updates += 1
                        console.print(f"  [yellow]Would update s3_html to best match: {best_match}[/yellow]")
                
                progress.update(task, advance=1)
    else:
        console.print("[yellow]No matches found[/yellow]\n")
    
    # Close session if we created one
    if session:
        await session.close()

    # Display unmatched files
    if unmatched_json or unmatched_s3:
        console.print("\n[bold yellow]⚠ Unmatched Files:[/bold yellow]\n")

        if unmatched_json:
            console.print(f"[yellow]JSON files without S3 match ({len(unmatched_json)}):[/yellow]")
            for f in sorted(unmatched_json):
                console.print(f"  - {os.path.basename(f)}")
            console.print()

        if unmatched_s3:
            console.print(f"[yellow]S3 files without JSON match ({len(unmatched_s3)}):[/yellow]")
            for f in sorted(unmatched_s3):
                console.print(f"  - {f}")

    # Summary
    console.print("\n[bold]Summary:[/bold]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Type", style="cyan")
    table.add_column("Count", justify="right")

    table.add_row("Total Matches", str(len(matches)))
    table.add_row("├─ Exact filename matches", str(exact_matches))
    table.add_row("└─ Different filenames", str(different_filenames))
    
    if validate:
        table.add_row("", "")
        table.add_row("[bold]Validation Results[/bold]", "")
        table.add_row("├─ Valid files", str(valid_files))
        table.add_row("├─ Invalid files", str(invalid_files))
        table.add_row("└─ Unknown status", str(unknown_files))
    
    table.add_row("", "")
    table.add_row("Files to update" if dry_run else "Files updated", str(total_updates))
    table.add_row("", "")
    table.add_row("Unmatched JSON", str(len(unmatched_json)))
    table.add_row("Unmatched S3", str(len(unmatched_s3)))
    table.add_row("", "")
    table.add_row("Total JSON", str(len(matches) + len(unmatched_json)))
    table.add_row("Total S3", str(len(matches) + len(unmatched_s3)))

    console.print(table)
    
    # Action summary
    if dry_run and total_updates > 0:
        console.print(f"\n[yellow]Would update {total_updates} files. Use --update to apply changes.[/yellow]")
        if len(group2_different_names) > 0:
            console.print(f"[yellow]Found {len(group2_different_names)} files with different filenames. Use --update-different-filenames to update these.[/yellow]")
    elif not dry_run and total_updates > 0:
        console.print(f"\n[bold green]✓ Updated {total_updates} files successfully![/bold green]")
        if update_different_filenames:
            console.print(f"[green]Including {len(group2_different_names)} files with different filenames.[/green]")
    else:
        console.print(f"\n[green]All s3_html fields are already correct.[/green]")
    
    # Show files that need redownloading
    if validate and files_to_redownload:
        console.print(f"\n[bold red]Files that need redownloading ({len(files_to_redownload)}):[/bold red]\n")
        for file_info in files_to_redownload:
            console.print(f"[red]• {os.path.basename(file_info['json'])}[/red]")
            console.print(f"  Reason: {file_info['reason']}")
            console.print(f"  S3: {file_info['s3']}\n")
    
    # Show organized results based on validation
    if validate:
        # Clear groups before populating
        group1_exact_match.clear()
        group2_different_names.clear()
        group3_invalid.clear()
        
        # Organize files into groups based on validation results
        for json_file, s3_files in sorted(matches.items()):
            json_name = os.path.basename(json_file)
            
            # Get validation results for this JSON file
            if json_file in validation_results:
                file_results = validation_results[json_file]
                
                # Check if ANY match is valid
                valid_matches = [r for r in file_results if r['is_valid']]
                
                if not valid_matches:
                    # No valid matches - add to group 3
                    group3_invalid.append(json_name)
                else:
                    # Has valid matches - check for exact filename match
                    exact_match_found = any(r['exact_match'] for r in valid_matches)
                    
                    if exact_match_found:
                        group1_exact_match.append(json_name)
                    else:
                        # Only has different filename matches
                        first_valid = valid_matches[0]
                        group2_different_names.append((json_name, first_valid['s3_name']))
            else:
                # No validation results - shouldn't happen but handle it
                group3_invalid.append(json_name)
        
        # Display Group 1: Valid with matching filenames
        console.print(f"\n[bold green]GROUP 1: Valid files with matching filenames ({len(group1_exact_match)}):[/bold green]")
        for json_name in sorted(group1_exact_match):
            console.print(f"[green]✓ {json_name}[/green]")
        
        # Display Group 2: Valid with different filenames
        console.print(f"\n[bold yellow]GROUP 2: Valid files with different filenames ({len(group2_different_names)}):[/bold yellow]")
        console.print("[dim]Checking if expected S3 files exist...[/dim]")
        
        # Get all S3 files for checking
        all_s3_files = set()
        for s3_file_list in matches.values():
            all_s3_files.update(s3_file_list)
        
        for json_name, s3_name in sorted(group2_different_names):
            console.print(f"\n[green]{json_name}[/green]")
            console.print(f"  [red]↳ Found: {s3_name}[/red]")
            
            # Check if the expected S3 file exists
            json_base = json_name.replace('.json', '')
            expected_s3_name = f"{json_base}.html"
            
            # Search for expected file in S3
            expected_exists = False
            expected_s3_path = None
            for s3_path in all_s3_files:
                if os.path.basename(s3_path).lower() == expected_s3_name.lower():
                    expected_exists = True
                    expected_s3_path = s3_path
                    console.print(f"  [yellow]⚠ Expected file exists in S3: {os.path.basename(s3_path)}[/yellow]")
                    break
            
            if not expected_exists:
                console.print(f"  [dim]✓ Expected file NOT in S3: {expected_s3_name}[/dim]")
            
            # Handle S3 file renaming if requested
            if rename_s3_files and s3_storage and not expected_exists:
                # Find the valid S3 file path that needs renaming
                current_s3_path = None
                for s3_file_list in matches.values():
                    for s3_file in s3_file_list:
                        if os.path.basename(s3_file) == s3_name:
                            current_s3_path = s3_file
                            break
                    if current_s3_path:
                        break
                
                if current_s3_path:
                    # Construct the new S3 path with the expected filename
                    date_prefix = current_s3_path.split('/')[0]  # Get the date part
                    new_s3_path = f"{date_prefix}/html/{expected_s3_name}"
                    
                    if dry_run:
                        console.print(f"  [yellow]Would rename S3 file:[/yellow]")
                        console.print(f"    [red]From: {current_s3_path}[/red]")
                        console.print(f"    [green]To:   {new_s3_path}[/green]")
                    else:
                        # Actually rename the S3 file
                        console.print(f"  [yellow]Renaming S3 file...[/yellow]")
                        rename_success = await rename_s3_file(s3_storage, current_s3_path, new_s3_path)
                        
                        if rename_success:
                            # Update the s3_name for JSON update
                            s3_name = expected_s3_name
                            # Update the valid_s3_path to use the new path
                            for idx, (jn, sn) in enumerate(group2_different_names):
                                if jn == json_name:
                                    group2_different_names[idx] = (jn, expected_s3_name)
                                    break
            
            # Find the matching JSON file and valid S3 file
            if update_different_filenames and not dry_run:
                # Find the full JSON file path
                json_file_path = None
                for json_file in matches.keys():
                    if os.path.basename(json_file) == json_name:
                        json_file_path = json_file
                        break
                
                # Find the valid S3 file path
                valid_s3_path = None
                # If we renamed the file, use the new path
                if rename_s3_files and s3_name == expected_s3_name:
                    date_prefix = None
                    # Get date prefix from any S3 file
                    for s3_file_list in matches.values():
                        if s3_file_list:
                            date_prefix = s3_file_list[0].split('/')[0]
                            break
                    if date_prefix:
                        valid_s3_path = f"{date_prefix}/html/{expected_s3_name}"
                else:
                    # Otherwise find the original S3 file
                    for s3_file_list in matches.values():
                        for s3_file in s3_file_list:
                            if os.path.basename(s3_file) == s3_name:
                                valid_s3_path = s3_file
                                break
                        if valid_s3_path:
                            break
                
                # Update the JSON file with the valid S3 file path
                if json_file_path and valid_s3_path:
                    updated, old_val, new_val = update_json_s3_html(json_file_path, valid_s3_path, dry_run=False)
                    if updated:
                        console.print(f"  [bold green]✓ Updated s3_html to: {valid_s3_path}[/bold green]")
                        total_updates += 1
                    else:
                        console.print(f"  [yellow]No update needed, s3_html already set to: {old_val}[/yellow]")
        
        # Display Group 3: Invalid files
        console.print(f"\n[bold red]GROUP 3: Invalid files - need redownload ({len(group3_invalid)}):[/bold red]")
        for json_name in sorted(group3_invalid):
            console.print(f"[red]{json_name}[/red]")
        
        # GROUP 4: Files that need redownload and rename to match JSON base filename
        console.print(f"\n[bold red]GROUP 4: Files to REDOWNLOAD and RENAME to match JSON filename:[/bold red]")
        group4_redownload = []
        
        for json_file, s3_files in sorted(matches.items()):
            json_name = os.path.basename(json_file)
            json_base = json_name.replace('.json', '')
            expected_html_name = f"{json_base}.html"
            
            if json_file in validation_results:
                file_results = validation_results[json_file]
                
                # Find all invalid files that need to be replaced
                invalid_files_to_replace = []
                
                for result in file_results:
                    if not result['is_valid']:
                        # This is an invalid file that should be replaced
                        invalid_files_to_replace.append(result['s3_name'])
                
                # If we have invalid files, they need redownload
                if invalid_files_to_replace:
                    group4_redownload.append({
                        'json_name': json_name,
                        'expected_html': expected_html_name,
                        'current_invalid': invalid_files_to_replace
                    })
        
        if group4_redownload:
            console.print(f"\n[yellow]Invalid HTML files to replace:[/yellow]")
            for item in group4_redownload:
                for invalid in item['current_invalid']:
                    console.print(f"[red]{invalid}[/red] → [green]{item['expected_html']}[/green]")
        else:
            console.print("[dim]None - all exact matches are valid[/dim]")
        
        # Also show unmatched JSON files as invalid
        if unmatched_json:
            console.print(f"\n[bold red]Unmatched JSON files ({len(unmatched_json)}):[/bold red]")
            for json_file in sorted(unmatched_json):
                console.print(f"[red]{os.path.basename(json_file)}[/red]")
                
    # Collect court_id and docket_num for Group 3 and unmatched JSON files
    invalid_cases = []
    
    # Process Group 3 (invalid HTML files)
    for json_name in group3_invalid:
        # Find the full JSON file path
        json_file_path = None
        for json_file in matches.keys():
            if os.path.basename(json_file) == json_name:
                json_file_path = json_file
                break
                
        if json_file_path:
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    court_id = data.get('court_id', 'unknown')
                    docket_num = data.get('docket_num', 'unknown')
                    invalid_cases.append({
                        'court_id': court_id,
                        'docket_num': docket_num,
                        'filename': json_name,
                        'reason': 'Invalid HTML content'
                    })
            except Exception as e:
                console.print(f"[red]Error reading {json_file_path}: {e}[/red]")
    
    # Process unmatched JSON files
    for json_file in unmatched_json:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                court_id = data.get('court_id', 'unknown')
                docket_num = data.get('docket_num', 'unknown')
                invalid_cases.append({
                    'court_id': court_id,
                    'docket_num': docket_num,
                    'filename': os.path.basename(json_file),
                    'reason': 'No S3 HTML file found'
                })
        except Exception as e:
            console.print(f"[red]Error reading {json_file}: {e}[/red]")
    
    # Remove duplicates based on court_id and docket_num
    unique_cases = []
    seen = set()
    
    for case in invalid_cases:
        key = (case['court_id'], case['docket_num'])
        if key not in seen:
            seen.add(key)
            unique_cases.append({
                'court_id': case['court_id'],
                'docket_num': case['docket_num']
            })
    
    return unique_cases


async def main(sys_args=None) -> List[Dict]:
    """
    Main function.
    
    Args:
        sys_args: Command line arguments (optional, for testing)
    
    Returns:
        List of dictionaries with court_id and docket_num for cases needing attention
    """
    parser = argparse.ArgumentParser(description="Match S3 HTML files to docket JSON files and update s3_html paths")
    parser.add_argument(
        "--date",
        required=True,
        help="Date in YYYYMMDD format"
    )
    parser.add_argument(
        "--update",
        action="store_true",
        help="Actually update JSON files (default is dry-run)"
    )
    parser.add_argument(
        "--update-different-filenames",
        action="store_true",
        help="Update JSON files with valid HTML links even when filenames are different"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        default=True,
        help="Show what would be updated without making changes (default)"
    )
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate HTML content to identify invalid files that need redownloading"
    )
    parser.add_argument(
        "--rename-s3-files",
        action="store_true",
        help="Rename S3 HTML files to match JSON filenames when valid but misnamed"
    )

    args = parser.parse_args(sys_args)
    
    # If --update or --rename-s3-files is specified, turn off dry-run
    if args.update or args.update_different_filenames or args.rename_s3_files:
        args.dry_run = False

    # Validate date format
    if not re.match(r'^\d{8}$', args.date):
        console.print("[red]Error: Date must be in YYYYMMDD format[/red]")
        sys.exit(1)

    console.print(f"\n[bold]Matching S3 files to dockets for date: {args.date}[/bold]\n")

    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()

    # Get AWS credentials - check both standard and custom env var names
    aws_access_key = os.getenv('AWS_ACCESS_KEY_ID') or os.getenv('AWS_ACCESS_KEY')
    aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY') or os.getenv('AWS_SECRET_KEY')
    bucket_name = os.getenv('AWS_S3_BUCKET') or os.getenv('S3_BUCKET_NAME', 'lexgeniuswebsite')
    aws_region = os.getenv('AWS_REGION', 'us-west-2')

    if not aws_access_key or not aws_secret_key:
        console.print("[red]Error: AWS credentials not found in environment[/red]")
        console.print("[yellow]Expected: AWS_ACCESS_KEY_ID/AWS_ACCESS_KEY and AWS_SECRET_ACCESS_KEY/AWS_SECRET_KEY[/yellow]")
        sys.exit(1)

    # Initialize S3 storage
    async with S3AsyncStorage(
            bucket_name=bucket_name,
            aws_access_key=aws_access_key,
            aws_secret_key=aws_secret_key,
            aws_region=aws_region
    ) as s3_storage:
        # Get S3 files
        s3_files = await get_s3_html_files(s3_storage, args.date)

        # Get local JSON files
        json_files = get_local_json_files(args.date)

        if not json_files and not s3_files:
            console.print("[yellow]No files found to match[/yellow]")
            return []

        # Match files
        matches, unmatched_json, unmatched_s3 = match_files(json_files, s3_files)

        # Display results and update if requested
        cases_needing_attention = await display_results_with_validation(
            matches, unmatched_json, unmatched_s3, 
            args.dry_run, args.validate, args.update_different_filenames,
            args.rename_s3_files, s3_storage
        )
        
        # Return the list of cases needing attention (or empty list if none)
        return cases_needing_attention if cases_needing_attention else []


if __name__ == "__main__":
    # Run the main function and get cases needing attention
    cases_needing_attention = asyncio.run(main())
    
    # Ensure we have a list (not None)
    if cases_needing_attention is None:
        cases_needing_attention = []
    
    # Print ONLY the JSON output to stdout with nice formatting
    print(json.dumps(cases_needing_attention, indent=2))