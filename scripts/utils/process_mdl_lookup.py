import os
import pandas as pd
import re


# Import the project root handling function
try:
    from scripts.utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config_adapter import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('/')
else:
    PROJECT_ROOT = get_project_root()


def process_mdl_lookup(file_path: str) -> pd.DataFrame:
    # Read the markdown file
    with open(file_path, 'r') as file:
        lines = file.readlines()

    # Initialize lists to store data
    data = []
    started = False
    previous_district = None  # Track the previous district
    
    for line in lines:
        # Skip until we find the header row
        if '| District | Judge (Title)' in line:
            started = True
            continue
        # Stop when we reach "## Report Totals"
        if '## Report Totals' in line:
            break
        
        # Skip the separator row and empty lines
        if started and '|' in line and not line.startswith('| :--:'):
            # Split the line by | and remove leading/trailing whitespace
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            
            if len(cells) == 6:  # Ensure we have all expected columns
                # Handle district - use previous if current is empty
                current_district = cells[0]
                if not current_district:
                    current_district = previous_district
                else:
                    previous_district = current_district
                
                # Create court_id using the district
                court_id = current_district.lower() + 'd'
                
                # Extract judge name and title
                judge_title = cells[1]
                match = re.match(r'(.*?)\s*\((.*?)\)', judge_title)
                if match:
                    judge_name, title = match.groups()
                else:
                    judge_name, title = judge_title, ''
                
                # Extract MDL number
                mdl_match = re.search(r'MDL -(\d+)', cells[2])
                mdl_num = mdl_match.group(1) if mdl_match else ''
                
                # Convert action counts to integers
                actions_now = int(cells[4].replace(',', ''))
                total_actions = int(cells[5].replace(',', ''))
                
                data.append({
                    'court_id': court_id,
                    'judge': judge_name.strip(),
                    'title': title.strip(),
                    'mdl_num': mdl_num,
                    'litigation': cells[3].strip(),
                    'actions_now': actions_now,
                    'total_actions_pending': total_actions
                })
    
    return pd.DataFrame(data)

# Example usage
if __name__ == "__main__":
    file_path = os.path.join(PROJECT_ROOT, "src", "data", "mdls/mdl_lookup.md")
    df = process_mdl_lookup(file_path)
    print(df.head())
    print(f"\nTotal rows: {len(df)}")