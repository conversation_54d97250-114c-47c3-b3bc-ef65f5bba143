#!/usr/bin/env python3
"""
Test AI Enhancement Script

This script tests the AI enhancement pipeline with sample ad data to verify
that DeepSeek and GPT-4 services are working correctly for Facebook ads processing.
"""

import asyncio
import logging
import json
import sys
from pathlib import Path
from typing import Dict, Any, List

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.ai.ai_orchestrator import AIOrchestrator
from src.services.ai.deepseek_service import DeepSeekService
from src.infrastructure.external.deepseek_client import DeepSeekClient
from src.infrastructure.external.openai_client import OpenAIClient
from src.infrastructure.protocols.logger import LoggerProtocol


class TestLogger(LoggerProtocol):
    """Test logger that outputs to console with timestamps"""
    
    def __init__(self, name: str = "test_ai_enhancement"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # Create console handler with formatting
        handler = logging.StreamHandler(sys.stdout)
        handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '[%(asctime)s] %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        
        # Add handler to logger
        if not self.logger.handlers:
            self.logger.addHandler(handler)
    
    def info(self, message: str, **kwargs):
        self.logger.info(message)
    
    def error(self, message: str, **kwargs):
        self.logger.error(message)
    
    def warning(self, message: str, **kwargs):
        self.logger.warning(message)
    
    def debug(self, message: str, **kwargs):
        self.logger.debug(message)


def create_sample_ad_data() -> List[Dict[str, Any]]:
    """Create sample ad data for testing - using correct field names for AI orchestrator"""
    return [
        {
            "ad_archive_id": "TEST_AD_001",
            "page_name": "Test Law Firm",
            "title": "Car Accident Attorneys",
            "body": "If you or a loved one was injured in a car accident, you may be entitled to compensation. Our experienced personal injury lawyers have recovered millions for our clients. Call now for a free consultation.",
            "caption": "Personal Injury Lawyers",
            "cta_text": "Call Now",
            "link_description": "Get the compensation you deserve",
            "link_title": "Car Accident Attorneys",
            "image_text": "Personal Injury Law Firm - Free Consultation",
            "publisher_platform": ["facebook", "instagram"],
            "ad_delivery_start_time": "2024-01-15",
            "ad_delivery_stop_time": "2024-02-15",
            "page_id": "123456789",
            "impressions": {"lower_bound": 1000, "upper_bound": 5000},
            "spend": {"lower_bound": 100, "upper_bound": 999}
        },
        {
            "ad_archive_id": "TEST_AD_002", 
            "page_name": "Medical Malpractice Lawyers LLC",
            "title": "Medical Negligence Lawyers",
            "body": "Medical malpractice can devastate families. If a doctor's negligence caused you harm, our medical malpractice attorneys can help you seek justice and compensation.",
            "caption": "Medical Malpractice Attorneys",
            "cta_text": "Free Consultation",
            "link_description": "Free case evaluation",
            "link_title": "Medical Negligence Lawyers",
            "image_text": "Medical Malpractice Law - Justice for Victims",
            "publisher_platform": ["facebook"],
            "ad_delivery_start_time": "2024-01-10",
            "ad_delivery_stop_time": "2024-02-10",
            "page_id": "987654321",
            "impressions": {"lower_bound": 5000, "upper_bound": 10000},
            "spend": {"lower_bound": 500, "upper_bound": 1500}
        }
    ]


async def test_deepseek_service(logger: TestLogger) -> bool:
    """Test DeepSeek service independently"""
    logger.info("🧠 Testing DeepSeek Service...")
    
    try:
        # Create DeepSeek client with proper initialization
        deepseek_client = DeepSeekClient(logger=logger)
        
        # Test if API key is available
        if not deepseek_client.deepseek_api_key:
            logger.error("❌ DeepSeek API key not available (check DEEPSEEK_API_KEY in .env)")
            return False
        
        # Test direct client usage with simple prompt
        test_prompt = "Summarize this legal advertisement in 2-3 sentences: If you or a loved one was injured in a car accident, you may be entitled to compensation. Our experienced personal injury lawyers have recovered millions for our clients. Call now for a free consultation."
        
        logger.info(f"📝 Testing DeepSeek with prompt: {test_prompt[:100]}...")
        
        # Test chat completion
        response = await deepseek_client.chat_completion(
            model="deepseek-chat",
            messages=[{"role": "user", "content": test_prompt}],
            max_tokens=150,
            temperature=0.3
        )
        
        if response and response.get('choices'):
            summary = response['choices'][0]['message']['content']
            logger.info(f"✅ DeepSeek summary generated: {summary[:100]}...")
            return True
        else:
            logger.error("❌ DeepSeek returned empty response")
            return False
            
    except Exception as e:
        logger.error(f"❌ DeepSeek test failed: {str(e)}")
        return False


async def test_gpt4_client(logger: TestLogger) -> bool:
    """Test GPT-4 client independently"""
    logger.info("🤖 Testing GPT-4 Client...")
    
    try:
        # Get API key from environment
        import os
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.error("❌ OpenAI API key not available (check OPENAI_API_KEY in .env)")
            return False
        
        # Create OpenAI client with proper initialization
        openai_client = OpenAIClient(api_key=api_key, logger=logger)
        
        # Test with sample ad data
        sample_ad = create_sample_ad_data()[1]
        
        logger.info(f"📝 Testing GPT-4 with ad: {sample_ad['ad_archive_id']}")
        logger.info(f"📝 Ad body: {sample_ad['body'][:100]}...")
        
        # Create a simple test prompt
        test_prompt = f"Summarize this legal advertisement in 2-3 sentences: {sample_ad['body']}"
        
        # Test GPT-4 completion using async context manager
        async with openai_client:
            response = await openai_client.chat_completion(
                model="gpt-4",
                messages=[{"role": "user", "content": test_prompt}],
                max_tokens=150,
                temperature=0.3
            )
        
        if response and response.get('choices'):
            summary = response['choices'][0]['message']['content']
            logger.info(f"✅ GPT-4 summary generated: {summary[:100]}...")
            return True
        else:
            logger.error("❌ GPT-4 returned empty response")
            return False
            
    except Exception as e:
        logger.error(f"❌ GPT-4 test failed: {str(e)}")
        return False


async def test_ai_orchestrator(logger: TestLogger) -> bool:
    """Test AI orchestrator with both services"""
    logger.info("🎯 Testing AI Orchestrator...")
    
    try:
        # Get API keys from environment
        import os
        openai_api_key = os.getenv("OPENAI_API_KEY")
        
        # Create clients
        deepseek_client = DeepSeekClient(logger=logger)
        openai_client = None
        
        # Create services
        deepseek_service = None
        gpt4_client = None
        
        if deepseek_client.deepseek_api_key:
            deepseek_service = DeepSeekService(
                client=deepseek_client,
                logger=logger
            )
            logger.info("✅ DeepSeek service available for orchestrator")
        else:
            logger.warning("⚠️ DeepSeek service not available for orchestrator")
        
        if openai_api_key:
            openai_client = OpenAIClient(api_key=openai_api_key, logger=logger)
            gpt4_client = openai_client
            logger.info("✅ GPT-4 client available for orchestrator")
        else:
            logger.warning("⚠️ GPT-4 client not available for orchestrator")
        
        # Create AI orchestrator
        ai_orchestrator = AIOrchestrator(
            deepseek=deepseek_service,
            gpt4=gpt4_client,
            logger=logger
        )
        
        # Test with sample ad data
        sample_ads = create_sample_ad_data()
        
        logger.info(f"🔄 Testing AI orchestrator with {len(sample_ads)} ads")
        
        success_count = 0
        for ad in sample_ads:
            try:
                logger.info(f"🎯 Processing ad: {ad['ad_archive_id']}")
                
                # Test enhancement
                enhanced_ad = await ai_orchestrator.enhance_ad_data(ad)
                
                if enhanced_ad and enhanced_ad.get('summary'):
                    logger.info(f"✅ Enhanced ad {ad['ad_archive_id']}: {enhanced_ad['summary'][:100]}...")
                    success_count += 1
                else:
                    logger.error(f"❌ No enhancement for ad {ad['ad_archive_id']}")
                    
            except Exception as e:
                logger.error(f"❌ Enhancement failed for ad {ad['ad_archive_id']}: {str(e)}")
        
        logger.info(f"🎯 AI Orchestrator Results: {success_count}/{len(sample_ads)} ads enhanced")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ AI Orchestrator test failed: {str(e)}")
        return False


async def main():
    """Main test function"""
    print("🚀 Starting AI Enhancement Test Suite")
    print("=" * 60)
    
    # Create logger
    logger = TestLogger()
    
    # Test results
    results = {
        "deepseek": False,
        "gpt4": False,
        "orchestrator": False
    }
    
    # Test DeepSeek service
    results["deepseek"] = await test_deepseek_service(logger)
    print()
    
    # Test GPT-4 client
    results["gpt4"] = await test_gpt4_client(logger)
    print()
    
    # Test AI orchestrator
    results["orchestrator"] = await test_ai_orchestrator(logger)
    print()
    
    # Print final results
    print("=" * 60)
    print("🎯 AI Enhancement Test Results:")
    print(f"   DeepSeek Service: {'✅ PASS' if results['deepseek'] else '❌ FAIL'}")
    print(f"   GPT-4 Client: {'✅ PASS' if results['gpt4'] else '❌ FAIL'}")
    print(f"   AI Orchestrator: {'✅ PASS' if results['orchestrator'] else '❌ FAIL'}")
    print()
    
    # Overall status
    if any(results.values()):
        print("🎉 AI Enhancement: FUNCTIONAL")
        if not all(results.values()):
            print("⚠️  Some services failed - check API keys in .env file")
    else:
        print("❌ AI Enhancement: FAILED")
        print("💡 Check your .env file for DEEPSEEK_API_KEY and OPENAI_API_KEY")
    
    print("=" * 60)
    
    # Exit with appropriate code
    sys.exit(0 if any(results.values()) else 1)


if __name__ == "__main__":
    asyncio.run(main())