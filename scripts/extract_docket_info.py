#!/usr/bin/env python3
"""Extract court_id and docket_num from JSON files in dockets directory."""

import json
import os
from pathlib import Path
from typing import List, Dict
from collections import defaultdict
from rich.console import Console
from rich.table import Table
from rich.progress import track

console = Console()


def extract_docket_info(data_dir: str = "data/20250617/dockets") -> List[Dict[str, str]]:
    """Extract court_id and docket_num from JSON files in the specified directory."""
    results = []
    
    # Get all JSON files in the directory
    json_files = list(Path(data_dir).glob("*.json"))
    
    console.print(f"[cyan]Found {len(json_files)} JSON files in {data_dir}[/cyan]")
    
    # Process each JSON file
    for json_file in track(json_files, description="Processing JSON files..."):
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                
                # Extract court_id and docket_num
                court_id = data.get('court_id')
                docket_num = data.get('docket_num')
                
                if court_id and docket_num:
                    results.append({
                        "court_id": court_id,
                        "docket_num": docket_num
                    })
                else:
                    console.print(f"[yellow]Warning: Missing data in {json_file.name}[/yellow]")
                    
        except json.JSONDecodeError:
            console.print(f"[red]Error: Invalid JSON in {json_file.name}[/red]")
        except Exception as e:
            console.print(f"[red]Error processing {json_file.name}: {e}[/red]")
    
    # Sort by court_id alphabetically
    results.sort(key=lambda x: x['court_id'])
    
    return results


def display_results(results: List[Dict[str, str]]):
    """Display results grouped by court_id."""
    # Group by court_id
    grouped = defaultdict(list)
    for item in results:
        grouped[item['court_id']].append(item['docket_num'])
    
    # Display summary table
    table = Table(title="Docket Summary by Court")
    table.add_column("Court ID", style="cyan")
    table.add_column("Count", style="yellow")
    
    for court_id in sorted(grouped.keys()):
        table.add_row(court_id, str(len(grouped[court_id])))
    
    console.print(table)
    console.print(f"\n[bold]Total courts: {len(grouped)}[/bold]")
    console.print(f"[bold]Total dockets: {len(results)}[/bold]")
    
    # Display ALL items
    console.print("\n[bold cyan]ALL ITEMS (grouped by court_id):[/bold cyan]\n")
    for court_id in sorted(grouped.keys()):
        console.print(f"[bold yellow]{court_id}:[/bold yellow]")
        for docket_num in grouped[court_id]:
            console.print(f"  - {docket_num}")
        console.print()


def save_results(results: List[Dict[str, str]], output_file: str = "docket_info.json"):
    """Save results to JSON file."""
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    console.print(f"\n[green]Results saved to {output_file}[/green]")


def main():
    """Main function."""
    console.print("[bold blue]Extracting Court ID and Docket Numbers[/bold blue]\n")
    
    # Extract docket information
    results = extract_docket_info()
    
    if results:
        # Display results
        display_results(results)
        
        # Save results
        save_results(results)
        
        # Also print raw list for easy copying
        console.print("\n[bold]Raw list (ALL ITEMS):[/bold]")
        for item in results:
            console.print(f"  {item}")
    else:
        console.print("[red]No docket information found![/red]")


if __name__ == "__main__":
    main()