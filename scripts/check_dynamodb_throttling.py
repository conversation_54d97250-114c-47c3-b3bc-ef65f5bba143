#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check DynamoDB throttling metrics and capacity usage.
Helps diagnose rate limiting issues.
"""
import boto3
from datetime import datetime, timedelta
from typing import Dict, Any


def get_table_metrics(table_name: str, region: str = 'us-west-2') -> Dict[str, Any]:
    """
    Get current metrics and capacity information for a DynamoDB table.
    
    Args:
        table_name: Name of the DynamoDB table
        region: AWS region (default: us-west-2)
        
    Returns:
        Dictionary with table metrics
    """
    dynamodb = boto3.client('dynamodb', region_name=region)
    cloudwatch = boto3.client('cloudwatch', region_name=region)
    
    # Get table description
    table_info = dynamodb.describe_table(TableName=table_name)['Table']
    
    # Extract capacity info
    billing_mode = table_info.get('BillingModeSummary', {}).get('BillingMode', 'PROVISIONED')
    provisioned = table_info.get('ProvisionedThroughput', {})
    
    metrics = {
        'table_name': table_name,
        'billing_mode': billing_mode,
        'table_status': table_info['TableStatus'],
        'item_count': table_info.get('ItemCount', 0),
        'table_size_bytes': table_info.get('TableSizeBytes', 0),
    }
    
    if billing_mode == 'PROVISIONED':
        metrics['read_capacity'] = provisioned.get('ReadCapacityUnits', 0)
        metrics['write_capacity'] = provisioned.get('WriteCapacityUnits', 0)
        
        # Get CloudWatch metrics for the last hour
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=1)
        
        # Get consumed capacity
        consumed_read = get_cloudwatch_metric(
            cloudwatch, table_name, 'ConsumedReadCapacityUnits', start_time, end_time
        )
        consumed_write = get_cloudwatch_metric(
            cloudwatch, table_name, 'ConsumedWriteCapacityUnits', start_time, end_time
        )
        
        # Get throttled requests
        throttled_read = get_cloudwatch_metric(
            cloudwatch, table_name, 'UserErrors', start_time, end_time,
            dimensions=[
                {'Name': 'TableName', 'Value': table_name},
                {'Name': 'Operation', 'Value': 'GetItem'}
            ]
        )
        throttled_write = get_cloudwatch_metric(
            cloudwatch, table_name, 'UserErrors', start_time, end_time,
            dimensions=[
                {'Name': 'TableName', 'Value': table_name},
                {'Name': 'Operation', 'Value': 'PutItem'}
            ]
        )
        
        metrics['consumed_read_capacity'] = consumed_read
        metrics['consumed_write_capacity'] = consumed_write
        metrics['throttled_read_requests'] = throttled_read
        metrics['throttled_write_requests'] = throttled_write
        
        # Calculate utilization
        if metrics['read_capacity'] > 0:
            metrics['read_utilization'] = (consumed_read / metrics['read_capacity']) * 100
        if metrics['write_capacity'] > 0:
            metrics['write_utilization'] = (consumed_write / metrics['write_capacity']) * 100
    
    return metrics


def get_cloudwatch_metric(client, table_name: str, metric_name: str, 
                         start_time: datetime, end_time: datetime,
                         dimensions=None) -> float:
    """Get a CloudWatch metric value."""
    if dimensions is None:
        dimensions = [{'Name': 'TableName', 'Value': table_name}]
    
    try:
        response = client.get_metric_statistics(
            Namespace='AWS/DynamoDB',
            MetricName=metric_name,
            Dimensions=dimensions,
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,  # 1 hour
            Statistics=['Sum']
        )
        
        if response['Datapoints']:
            return sum(dp['Sum'] for dp in response['Datapoints'])
        return 0.0
    except Exception as e:
        print(f"Error getting metric {metric_name}: {e}")
        return 0.0


def print_table_status(metrics: Dict[str, Any]):
    """Print formatted table status."""
    print(f"\n=== DynamoDB Table: {metrics['table_name']} ===")
    print(f"Status: {metrics['table_status']}")
    print(f"Billing Mode: {metrics['billing_mode']}")
    print(f"Item Count: {metrics['item_count']:,}")
    print(f"Table Size: {metrics['table_size_bytes'] / (1024**2):.2f} MB")
    
    if metrics['billing_mode'] == 'PROVISIONED':
        print(f"\n--- Capacity Settings ---")
        print(f"Read Capacity: {metrics['read_capacity']} units")
        print(f"Write Capacity: {metrics['write_capacity']} units")
        
        print(f"\n--- Usage (Last Hour) ---")
        print(f"Consumed Read: {metrics['consumed_read_capacity']:.2f} units")
        print(f"Consumed Write: {metrics['consumed_write_capacity']:.2f} units")
        
        if 'read_utilization' in metrics:
            print(f"Read Utilization: {metrics['read_utilization']:.1f}%")
        if 'write_utilization' in metrics:
            print(f"Write Utilization: {metrics['write_utilization']:.1f}%")
        
        print(f"\n--- Throttling ---")
        print(f"Throttled Read Requests: {metrics['throttled_read_requests']:.0f}")
        print(f"Throttled Write Requests: {metrics['throttled_write_requests']:.0f}")
        
        # Warnings
        if metrics.get('write_utilization', 0) > 80:
            print("\n⚠️  WARNING: Write capacity utilization is above 80%!")
            print("   Consider increasing capacity or switching to on-demand.")
        
        if metrics['throttled_write_requests'] > 0:
            print("\n⚠️  WARNING: Write requests are being throttled!")
            print("   This causes the errors you're seeing.")
            print("   Recommended actions:")
            print("   1. Run: python scripts/switch_dynamodb_to_on_demand.py")
            print("   2. Or increase write capacity to at least 50-100 units")
    else:
        print("\n✓ Table is using on-demand billing mode.")
        print("  No throttling will occur with this mode.")


def main():
    """Main function to check throttling for LexGenius tables."""
    tables = ['Pacer']  # Add more tables as needed
    
    print("=== DynamoDB Throttling Check ===")
    print(f"Checking metrics for the last hour...")
    
    for table in tables:
        try:
            metrics = get_table_metrics(table)
            print_table_status(metrics)
        except Exception as e:
            print(f"\n✗ Error checking table '{table}': {e}")
    
    print("\n" + "="*50)
    print("\nFor detailed monitoring, set up CloudWatch alarms:")
    print("- ConsumedWriteCapacityUnits > 80% of provisioned")
    print("- UserErrors (throttling) > 5 per period")


if __name__ == "__main__":
    main()