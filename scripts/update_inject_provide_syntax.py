#!/usr/bin/env python3
"""
Script to update services with @inject decorators to use Provide syntax.
"""
import re
import os
from pathlib import Path


# Mapping of common parameter types to their container paths
DEPENDENCY_MAPPINGS = {
    'logger': 'Provide["core.logger"]',
    'config': 'Provide["core.config"]',
    'openai_client': 'Provide["transformer.openai_client"]',
    'deepseek_service': 'Provide["transformer.deepseek_service"]',
    'mistral_service': 'Provide["transformer.mistral_service"]',
    'shutdown_event': 'Provide["core.shutdown_event"]',
    's3_storage': 'Provide["storage.s3_async_storage"]',
    'file_manager': 'Provide["pacer.file_manager"]',
}


def update_inject_parameters(file_path):
    """Update @inject decorated methods to use Provide syntax."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Pattern to find @inject decorated __init__ methods
    pattern = r'(@inject\s*\n\s*def\s+__init__\s*\()([^)]+)(\):)'
    
    def replace_params(match):
        decorator = match.group(1)
        params_str = match.group(2)
        closing = match.group(3)
        
        # Parse parameters
        params = []
        current_param = ''
        paren_depth = 0
        
        for char in params_str:
            if char in '([{':
                paren_depth += 1
            elif char in ')]}':
                paren_depth -= 1
            elif char == ',' and paren_depth == 0:
                params.append(current_param.strip())
                current_param = ''
                continue
            current_param += char
        
        if current_param.strip():
            params.append(current_param.strip())
        
        # Process each parameter
        new_params = []
        for param in params:
            if param == 'self':
                new_params.append(param)
                continue
            
            # Check if it already has Provide
            if 'Provide[' in param:
                new_params.append(param)
                continue
            
            # Parse parameter name and type
            parts = param.split('=', 1)
            param_definition = parts[0].strip()
            default_value = parts[1].strip() if len(parts) > 1 else None
            
            # Extract parameter name
            param_match = re.match(r'(\w+)(?:\s*:\s*.*)?', param_definition)
            if not param_match:
                new_params.append(param)
                continue
            
            param_name = param_match.group(1)
            
            # Check if we have a mapping for this parameter
            if param_name in DEPENDENCY_MAPPINGS:
                # Preserve type annotation if present
                type_match = re.match(r'(\w+)\s*:\s*(.+)', param_definition)
                if type_match:
                    new_param = f"{type_match.group(1)}: {type_match.group(2)} = {DEPENDENCY_MAPPINGS[param_name]}"
                else:
                    new_param = f"{param_name} = {DEPENDENCY_MAPPINGS[param_name]}"
                new_params.append(new_param)
            else:
                # Keep original parameter
                new_params.append(param)
        
        # Reconstruct the method signature
        new_params_str = ',\n                 '.join(new_params)
        return f"{decorator}{new_params_str}{closing}"
    
    # Apply replacements
    new_content = re.sub(pattern, replace_params, content, flags=re.MULTILINE | re.DOTALL)
    
    if new_content != content:
        with open(file_path, 'w') as f:
            f.write(new_content)
        print(f"✅ Updated {file_path}")
        return True
    
    print(f"✓ {file_path} already uses Provide syntax or no changes needed")
    return False


def main():
    """Main function to update Provide syntax."""
    print("Updating services to use Provide syntax...")
    print("=" * 60)
    
    # Files that were just updated with @inject
    target_files = [
        'src/services/ai/prompt_manager.py',
        'src/services/fb_ads/concurrent_workflow_service.py',
        'src/services/fb_ads/failed_firms_manager.py',
        'src/services/fb_ads/jobs/job_orchestration_service.py',
        'src/services/fb_ads/jobs/job_runner_service.py',
        'src/services/fb_archive/delete_service.py',
        'src/services/fb_archive/update_service.py',
        'src/services/pacer/file_operations_service.py',
        'src/services/transformer/config.py',
        'src/services/transformer/data_transformer.py',
    ]
    
    modified_count = 0
    
    for file_path in target_files:
        if os.path.exists(file_path):
            if update_inject_parameters(file_path):
                modified_count += 1
        else:
            print(f"❌ File not found: {file_path}")
    
    print("=" * 60)
    print(f"✅ Modified {modified_count} files")
    print(f"📊 Total files processed: {len(target_files)}")


if __name__ == '__main__':
    main()