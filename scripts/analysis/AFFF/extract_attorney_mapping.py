#!/usr/bin/env python3
"""
Extract attorney to law firm mapping from attorney_comprehensive_data.json
"""

import json
from pathlib import Path
from datetime import datetime

def main():
    # Input and output paths
    input_file = Path("scripts/analysis/AFFF/output/attorney_comprehensive_data.json")
    output_file = Path("scripts/analysis/AFFF/attorney_law_firm_mapping.json")
    
    # Load comprehensive data
    print(f"Loading {input_file}...")
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    attorneys_data = data.get('attorneys', {})
    print(f"Found {len(attorneys_data)} attorneys")
    
    # Create attorney -> law_firm mapping
    attorney_mapping = {}
    for attorney_name, attorney_info in attorneys_data.items():
        law_firm = attorney_info.get('law_firm', 'Unknown')
        attorney_mapping[attorney_name] = law_firm
    
    # Create output structure with metadata
    output_data = {
        "mapping": attorney_mapping,
        "metadata": {
            "total_attorneys": len(attorney_mapping),
            "generated_from": str(input_file),
            "generated_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "unknown_count": sum(1 for firm in attorney_mapping.values() if firm == 'Unknown')
        }
    }
    
    # Save mapping
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Successfully created attorney mapping")
    print(f"   Output: {output_file}")
    print(f"   Total attorneys: {len(attorney_mapping)}")
    print(f"   Unknown firms: {output_data['metadata']['unknown_count']}")
    
    # Show sample
    print("\nSample mappings:")
    for i, (attorney, firm) in enumerate(attorney_mapping.items()):
        if i < 5:
            print(f"   {attorney} -> {firm}")
        else:
            break

if __name__ == "__main__":
    main()