# IMPORTANT: Attorney Law Firm Lookup Protection

## Critical File: `attorney_law_firm_lookup.json`

**This file is the AUTHORITATIVE SOURCE for attorney law firm affiliations and must NEVER be overwritten by any script.**

### Key Rules:

1. **READ-ONLY**: <PERSON><PERSON><PERSON> should only READ from `attorney_law_firm_lookup.json`, never write to it
2. **Manual Updates Only**: This file should only be updated manually when new law firm information is researched
3. **Always Match**: When parsing CSV data, attorney names must be matched against this lookup file
4. **Backup Before Changes**: Always create a backup before any manual edits

### Safe Usage:

```python
# CORRECT - Read from lookup file
with open('attorney_law_firm_lookup.json', 'r') as f:
    law_firm_data = json.load(f)

# WRONG - Never write to lookup file from scripts
# with open('attorney_law_firm_lookup.json', 'w') as f:
#     json.dump(data, f)  # DON'T DO THIS!
```

### If You Need to Update Law Firms:

1. Make a backup first:
   ```bash
   cp attorney_law_firm_lookup.json attorney_law_firm_lookup_backup_$(date +%Y%m%d).json
   ```

2. Edit the file manually or use a dedicated update script

3. Verify the changes before running any analysis

### Scripts That Use This File:

- `merge_attorney_data.py` - Reads law firm data and merges with filings
- `safe_merge.py` - Protected merge script with backup functionality
- `parse_afff_filings.py` - Should read from lookup when exporting

### Current Statistics:

- Total attorneys in lookup: 77
- Attorneys with known law firms: 76 (98.7%)
- Last manual update: Check file modification date

Remember: This file represents manually researched and verified data. Protect it!