# AFFF Scripts Documentation

## Main Pipeline Script

### `afff_complete_pipeline.py`
**The primary script that does everything:**
- Parses the AFFF CSV file
- Performs case-insensitive attorney matching
- Updates attorney_law_firm_lookup.json with new attorneys
- Generates comprehensive attorney data
- Creates filing analysis and summary statistics
- Generates visualization charts

**Usage:** `python afff_complete_pipeline.py`

## Supporting Scripts

### `analyze_filings.py`
- Analyzes filing data by law firm and date
- Shows top firms, filing trends, and missing attorneys
- Can be run independently after the pipeline

**Usage:** `python analyze_filings.py`

### `visualize_filings.py`
- Creates visualization charts from filing analysis data
- Generates filing_analysis_charts.png
- Updates filing_summary_stats.json

**Usage:** `python visualize_filings.py`

### `validate_lookup.py`
- Validates the integrity of attorney_law_firm_lookup.json
- Checks for duplicates, missing data, and consistency
- Shows coverage statistics

**Usage:** `python validate_lookup.py`

### `show_data_status.py`
- Shows comprehensive status of AFFF data
- Lists attorneys with unknown law firms
- Shows case mismatches between CSV and lookup
- Displays file modification times

**Usage:** `python show_data_status.py`

### `check_for_new_csv.py`
- Checks if there's a new AFFF CSV file to process
- Compares timestamps with existing parsed data
- Provides instructions if new data is found

**Usage:** `python check_for_new_csv.py`

### `check_law_firms.py`
- Quick check of law firm assignments
- Shows attorneys with unknown firms
- Useful for identifying research needs

**Usage:** `python check_law_firms.py`

### `run_afff_analysis.py`
- Interactive runner with menu options
- Can run full pipeline or individual steps
- Shows progress and summary statistics

**Usage:** `python run_afff_analysis.py [options]`

## Workflow

1. **New CSV arrives**: Run `check_for_new_csv.py` to verify
2. **Process data**: Run `afff_complete_pipeline.py`
3. **Check status**: Run `show_data_status.py` to see results
4. **Validate**: Run `validate_lookup.py` to ensure data integrity
5. **Research**: Update law firms for any "Unknown" attorneys in attorney_law_firm_lookup.json
6. **Reprocess**: Run `afff_complete_pipeline.py` again to update with new law firm data