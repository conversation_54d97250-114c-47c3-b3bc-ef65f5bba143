#!/usr/bin/env python3
"""
Complete AFFF pipeline: Parse CSV, match attorneys case-insensitively, 
update lookup, generate all summaries and visualizations.
"""

import csv
import json
import re
import shutil
from collections import defaultdict
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

def smart_title_case(text):
    """Convert text to title case while preserving special cases like 3M."""
    if not text:
        return text
    
    # Special cases that should remain as-is
    special_cases = {
        '3M': '3M',
        'AGC': 'AGC',
        'LLC': 'LLC',
        'L.L.C.': 'L.L.C.',
        'INC': 'Inc',
        'INC.': 'Inc.',
        'CO': 'Co',
        'CO.': 'Co.',
        'CORP': 'Corp',
        'CORP.': 'Corp.',
        'CORPORATION': 'Corporation',
        'COMPANY': 'Company',
        'F/K/A': 'f/k/a',
        'FKA': 'f/k/a',
        'USA': 'USA',
        'US': 'US',
        'KY': 'KY',
        'TN': 'TN',
        'MD': 'MD',
        'FL': 'FL',
        'GA': 'GA',
        'SC': 'SC',
        'NC': 'NC',
        'VA': 'VA',
        'AFFF': 'AFFF',
        'MDL': 'MDL',
        'ET': 'et',
        'AL': 'al',
        'AL.': 'al.',
    }
    
    # Split by spaces and process each word
    words = text.split()
    result_words = []
    
    for word in words:
        # Check if it's a special case (case-insensitive lookup)
        upper_word = word.upper()
        if upper_word in special_cases:
            result_words.append(special_cases[upper_word])
        # Check if it starts with a number (like 3M)
        elif word and word[0].isdigit():
            result_words.append(word.upper())
        # Handle hyphenated names
        elif '-' in word:
            parts = word.split('-')
            titled_parts = [part.capitalize() for part in parts]
            result_words.append('-'.join(titled_parts))
        else:
            # Regular title case
            result_words.append(word.capitalize())
    
    return ' '.join(result_words)

def parse_docket_text(text):
    """Parse docket text to extract attorney, plaintiff, defendant, case number."""
    result = {
        'plaintiff': None,
        'defendant': None,
        'attorney': None,
        'case_number': None
    }
    
    # Extract case number
    case_match = re.search(r'(\d+:\d+-cv-\d+)(?:-[A-Z]+)?', text)
    if case_match:
        result['case_number'] = case_match.group(1)
    
    # Attorney patterns - comprehensive list
    attorney_patterns = [
        # Original patterns
        r'\)\s*\.?\s*([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',
        r'\)([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',
        r'\)\s*([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*(?:Modified|$)',
        r'\)\s+([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',
        # New patterns
        r'Summons\)([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',
        r'\(([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s*(?:\(Entered|\s*$)',
        r'\)\s*\(([A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)',
        r'\.\s+([A-Z][A-Za-z]+\s+[A-Z][A-Za-z]+),\s*([A-Z][A-Za-z]+(?:\s+[A-Z][A-Za-z]+)*)\)\s+To',
    ]
    
    for pattern in attorney_patterns:
        attorney_match = re.search(pattern, text)
        if attorney_match:
            last_name = attorney_match.group(1)
            first_name = attorney_match.group(2)
            result['attorney'] = f"{first_name} {last_name}"
            break
    
    # Extract plaintiff vs defendant
    text_clean = re.sub(r'\(\s*Filing fee[^)]+\)', '', text)
    text_clean = re.sub(r'\(Attachments:[^)]+\)', '', text_clean)
    text_clean = re.sub(r'(?:Charleston|CHARLESTON)\s+Division\.?\s*', '', text_clean, flags=re.IGNORECASE)
    
    versus_match = re.search(r'COMPLAINT\s+(.+?)\s+vs\s+(.+?)(?:\s+\.|$)', text_clean)
    if versus_match:
        plaintiff = versus_match.group(1).strip()
        defendant = versus_match.group(2).strip()
        
        # Fix 3MCOMPANY -> 3M COMPANY first (before other cleanups)
        plaintiff = re.sub(r'3M\s*COMPANY', '3M COMPANY', plaintiff, flags=re.IGNORECASE)
        defendant = re.sub(r'3M\s*COMPANY', '3M COMPANY', defendant, flags=re.IGNORECASE)
        
        # Clean up plaintiff
        plaintiff = re.sub(r'\s+for the Estate of.+', '', plaintiff)
        # Clean up defendant - remove any company suffixes followed by Division
        defendant = re.sub(r'\s+(?:COMPANY|Company|CO\.|Corp\.?|Corporation|Inc\.?|LLC|L\.L\.C\.)\s+(?:Charleston|CHARLESTON)\s+Division', '', defendant, flags=re.IGNORECASE)
        defendant = re.sub(r'\s*\([^)]+\)', '', defendant)
        defendant = defendant.rstrip('. ')
        defendant = re.sub(r',?\s*et\.?\s*al\.?', ' et al', defendant)
        
        # Apply title case if the names are in all caps
        if plaintiff and plaintiff.isupper():
            plaintiff = smart_title_case(plaintiff)
        if defendant and defendant.isupper():
            defendant = smart_title_case(defendant)
            
        result['plaintiff'] = plaintiff.strip()
        result['defendant'] = defendant.strip()
    
    return result

def create_backup(file_path):
    """Create timestamped backup of a file."""
    if Path(file_path).exists():
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"{file_path.stem}_backup_{timestamp}{file_path.suffix}"
        backup_path = file_path.parent / backup_name
        shutil.copy2(file_path, backup_path)
        print(f"Created backup: {backup_path}")
        return backup_path
    return None

def generate_javascript_format(analysis_data, output_dir):
    """Generate JavaScript const format of the analysis data."""
    
    # Format filings_by_law_firm
    law_firms_js = []
    for firm_data in analysis_data['filings_by_law_firm']:
        attorneys_js = [
            f'{{ "name": "{attorney["name"]}", "filings": {attorney["filings"]} }}'
            for attorney in firm_data['attorneys']
        ]
        
        law_firm_js = f'''{{ "law_firm": "{firm_data['law_firm']}", "total_filings": {firm_data['total_filings']}, "attorney_count": {firm_data['attorney_count']}, "attorneys": [{', '.join(attorneys_js)}] }}'''
        law_firms_js.append(law_firm_js)
    
    # Format ALL filings_by_date entries
    dates_js = [
        f'{{ "date": "{date_data["date"]}", "count": {date_data["count"]} }}'
        for date_data in analysis_data['filings_by_date']
    ]
    
    # Format filings_by_month
    months_js = [
        f'{{ "month": "{month_data["month"]}", "count": {month_data["count"]} }}'
        for month_data in analysis_data['filings_by_month']
    ]
    
    # Format ALL law firms section
    newline_indent = ',\n    '
    law_firms_section = newline_indent.join(law_firms_js)
    
    # Format dates section
    dates_section = ', '.join(dates_js)
    
    # Format months section  
    months_section = ', '.join(months_js)
    
    # Create JavaScript const format
    js_content = f'''const filing_analysis_data = {{
  "filings_by_law_firm": [
    {law_firms_section}
  ],
  "filings_by_date": [
    {dates_section}
  ],
  "filings_by_month": [
    {months_section}
  ],
  "summary": {{
    "total_filings": {analysis_data['summary']['total_filings']}, "total_attorneys": {analysis_data['summary']['total_attorneys']}, "total_law_firms": {analysis_data['summary']['total_law_firms']}, "date_range": "{analysis_data['summary']['date_range']}", "filings_without_attorneys": {analysis_data['summary']['filings_without_attorneys']}, "total_filings_in_csv": {analysis_data['summary']['total_filings_in_csv']}
  }}
}};'''
    
    # Save to file
    with open(output_dir / 'filing_analysis_data.js', 'w', encoding='utf-8') as f:
        f.write(js_content)
    
    print("  ✓ Generated JavaScript const format: output/filing_analysis_data.js")

def main():
    """Run complete AFFF pipeline."""
    print("\n" + "="*60)
    print("AFFF COMPLETE PIPELINE")
    print("="*60)
    
    # Determine base directory - works whether run from project root or AFFF directory
    script_dir = Path(__file__).parent
    if script_dir.name == "AFFF":
        base_dir = script_dir
        project_root = script_dir.parent.parent
    else:
        # Running from somewhere else
        base_dir = Path.cwd() / "test_files" / "AFFF"
        project_root = Path.cwd()
    
    # File paths
    csv_file = base_dir / "AFFF-Filings-Case-Shell.csv"
    lookup_file = base_dir / "attorney_law_firm_lookup.json"
    
    if not csv_file.exists():
        print(f"ERROR: CSV file not found: {csv_file}")
        return
    
    # Step 1: Parse CSV
    print("\n[1/5] Parsing CSV file...")
    parsed_data = []
    attorney_filings = defaultdict(list)
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        next(f)  # Skip header
        
        for line in f:
            # Manual CSV parsing for quotes
            parts = []
            current = []
            in_quotes = False
            
            for char in line:
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(''.join(current).strip())
                    current = []
                else:
                    current.append(char)
            
            if current:
                parts.append(''.join(current).strip())
            
            if len(parts) >= 3:
                date_str = parts[0]
                doc_num = parts[1]
                docket_text = parts[2].strip('"')
                
                # Parse the docket text
                parsed = parse_docket_text(docket_text)
                parsed['date'] = date_str
                parsed['doc_num'] = doc_num
                
                if parsed['plaintiff'] and parsed['defendant']:
                    parsed['versus'] = f"{parsed['plaintiff']} vs {parsed['defendant']}"
                else:
                    parsed['versus'] = "No versus info"
                
                parsed_data.append(parsed)
                
                # Collect by attorney
                if parsed['attorney']:
                    attorney_filings[parsed['attorney']].append({
                        'date': parsed['date'],
                        'doc_num': parsed['doc_num'],
                        'docket_num': parsed['case_number'] or "",
                        'versus': parsed['versus']
                    })
    
    total_filings = len(parsed_data)
    total_with_attorneys = sum(1 for p in parsed_data if p['attorney'])
    
    print(f"  ✓ Parsed {total_filings} filings")
    print(f"  ✓ Found {len(attorney_filings)} unique attorneys")
    print(f"  ✓ Filings with attorneys: {total_with_attorneys}")
    print(f"  ✓ Filings without attorneys: {total_filings - total_with_attorneys}")
    
    # Step 2: Load and update attorney lookup (case-insensitive)
    print("\n[2/5] Updating attorney law firm lookup...")
    
    # Create backup
    backup_path = create_backup(lookup_file)
    
    # Load lookup
    if lookup_file.exists():
        with open(lookup_file, 'r') as f:
            lookup_data = json.load(f)
    else:
        lookup_data = {"attorneys": {}, "metadata": {}}
    
    # Create case-insensitive map
    lookup_map = {}
    for name in lookup_data["attorneys"].keys():
        lookup_map[name.lower()] = name
    
    # Check for new attorneys and case mismatches
    new_attorneys = []
    case_mismatches = []
    
    for attorney_name in attorney_filings.keys():
        attorney_lower = attorney_name.lower()
        
        if attorney_lower in lookup_map:
            # Check for case mismatch
            actual_name = lookup_map[attorney_lower]
            if actual_name != attorney_name:
                case_mismatches.append((attorney_name, actual_name))
        else:
            # New attorney
            new_attorneys.append(attorney_name)
            lookup_data["attorneys"][attorney_name] = {
                "law_firm": "Unknown",
                "notes": f"Added from CSV on {datetime.now().strftime('%Y-%m-%d')}"
            }
    
    # Update metadata
    lookup_data["metadata"]["total_attorneys"] = len(lookup_data["attorneys"])
    lookup_data["metadata"]["last_updated"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Save updated lookup
    if new_attorneys:
        with open(lookup_file, 'w') as f:
            json.dump(lookup_data, f, indent=2, ensure_ascii=False)
        print(f"  ✓ Added {len(new_attorneys)} new attorneys to lookup")
    
    if case_mismatches:
        print(f"  ℹ Found {len(case_mismatches)} case mismatches")
    
    print(f"  ✓ Total attorneys in lookup: {len(lookup_data['attorneys'])}")
    
    # Step 3: Create comprehensive data
    print("\n[3/5] Creating comprehensive attorney data...")
    
    comprehensive_data = {
        "attorneys": {},
        "metadata": {
            "created_date": datetime.now().strftime('%Y-%m-%d'),
            "total_attorneys": 0,
            "total_filings": 0,
            "attorneys_with_known_firms": 0,
            "attorneys_with_unknown_firms": 0
        }
    }
    
    # Process each attorney with case-insensitive matching
    for attorney_name, filings in attorney_filings.items():
        attorney_lower = attorney_name.lower()
        
        # Get law firm from lookup
        if attorney_lower in lookup_map:
            lookup_name = lookup_map[attorney_lower]
            law_firm = lookup_data["attorneys"][lookup_name]["law_firm"]
        else:
            law_firm = lookup_data["attorneys"][attorney_name]["law_firm"]
        
        # Track stats
        if law_firm and law_firm != "Unknown":
            comprehensive_data["metadata"]["attorneys_with_known_firms"] += 1
        else:
            comprehensive_data["metadata"]["attorneys_with_unknown_firms"] += 1
        
        # Clean versus fields
        for filing in filings:
            versus = filing["versus"]
            versus = versus.replace("Charleston - MDL 2873 Division", "").strip()
            versus = versus.title() if versus != "No versus info" else versus
            filing["versus"] = versus
        
        comprehensive_data["attorneys"][attorney_name] = {
            "law_firm": law_firm,
            "filing_count": len(filings),
            "filings": filings
        }
        
        comprehensive_data["metadata"]["total_attorneys"] += 1
        comprehensive_data["metadata"]["total_filings"] += len(filings)
    
    # Save comprehensive data to output directory
    output_dir = base_dir / 'output'
    output_dir.mkdir(exist_ok=True)
    with open(output_dir / 'attorney_comprehensive_data.json', 'w') as f:
        json.dump(comprehensive_data, f, indent=2, ensure_ascii=False)
    
    print(f"  ✓ Created comprehensive data for {comprehensive_data['metadata']['total_attorneys']} attorneys")
    
    # Step 4: Generate analysis
    print("\n[4/5] Generating filing analysis...")
    
    # Aggregate by law firm
    filings_by_firm = defaultdict(int)
    attorneys_by_firm = defaultdict(list)
    filings_by_date = defaultdict(int)
    filings_by_month = defaultdict(int)
    
    # Process ALL parsed data (including those without attorneys)
    for parsed in parsed_data:
        date = parsed["date"]
        filings_by_date[date] += 1
        
        # Extract month
        try:
            date_obj = datetime.strptime(date, "%m/%d/%Y")
            month_key = date_obj.strftime("%Y-%m (%B)")
            filings_by_month[month_key] += 1
        except:
            pass
    
    # Also aggregate by law firm for attorney data
    for attorney_name, data in comprehensive_data["attorneys"].items():
        law_firm = data["law_firm"]
        filings_by_firm[law_firm] += data["filing_count"]
        attorneys_by_firm[law_firm].append((attorney_name, data["filing_count"]))
    
    # Sort data
    sorted_firms = sorted(filings_by_firm.items(), key=lambda x: x[1], reverse=True)
    sorted_dates = sorted(filings_by_date.items())
    sorted_months = sorted(filings_by_month.items())
    
    # Find filings without attorneys
    missing_attorneys = []
    for parsed in parsed_data:
        if not parsed['attorney']:
            missing_attorneys.append({
                'doc_num': parsed['doc_num'],
                'date': parsed['date'],
                'versus': parsed['versus']
            })
    
    # Create analysis JSON
    analysis_data = {
        "filings_by_law_firm": [
            {
                "law_firm": firm,
                "total_filings": count,
                "attorney_count": len(attorneys_by_firm[firm]),
                "attorneys": [
                    {"name": name, "filings": filing_count}
                    for name, filing_count in sorted(attorneys_by_firm[firm], 
                                                    key=lambda x: x[1], 
                                                    reverse=True)
                ]
            }
            for firm, count in sorted_firms
        ],
        "filings_by_date": [{"date": date, "count": count} for date, count in sorted_dates],
        "filings_by_month": [{"month": month, "count": count} for month, count in sorted_months],
        "summary": {
            "total_filings": total_filings,  # Use the actual CSV total, not just attorney filings
            "total_filings_in_csv": total_filings,
            "total_attorneys": comprehensive_data["metadata"]["total_attorneys"],
            "total_law_firms": len(sorted_firms),
            "date_range": f"{sorted_dates[0][0]} to {sorted_dates[-1][0]}",
            "filings_without_attorneys": len(missing_attorneys)
        },
        "missing_attorneys": missing_attorneys
    }
    
    with open(output_dir / 'filing_analysis.json', 'w') as f:
        json.dump(analysis_data, f, indent=2, ensure_ascii=False)
    
    # Create summary stats
    summary_stats = {
        "total_filings": total_filings,
        "total_with_attorneys": comprehensive_data["metadata"]["total_filings"],
        "filings_without_attorneys": len(missing_attorneys),
        "top_firm": {
            "name": sorted_firms[0][0],
            "filings": sorted_firms[0][1],
            "percentage": round(sorted_firms[0][1] / comprehensive_data["metadata"]["total_filings"] * 100, 1)
        },
        "busiest_month": max(analysis_data['filings_by_month'], key=lambda x: x['count']),
        "busiest_day": max(analysis_data['filings_by_date'], key=lambda x: x['count']),
        "average_filings_per_attorney": round(comprehensive_data["metadata"]["total_filings"] / 
                                            comprehensive_data["metadata"]["total_attorneys"], 1),
        "average_filings_per_firm": round(comprehensive_data["metadata"]["total_filings"] / 
                                        len(sorted_firms), 1)
    }
    
    with open(output_dir / 'filing_summary_stats.json', 'w') as f:
        json.dump(summary_stats, f, indent=2)
    
    # Generate JavaScript format
    generate_javascript_format(analysis_data, output_dir)
    
    print("  ✓ Generated filing analysis and summary statistics")
    
    # Step 5: Create visualizations
    print("\n[5/5] Creating visualizations...")
    
    plt.style.use('seaborn-v0_8-darkgrid')
    sns.set_palette("husl")
    
    fig = plt.figure(figsize=(16, 12))
    
    # Top Law Firms
    ax1 = plt.subplot(2, 2, 1)
    top_firms = analysis_data['filings_by_law_firm'][:10]
    firms = [f['law_firm'][:30] + '...' if len(f['law_firm']) > 30 else f['law_firm'] 
             for f in top_firms]
    counts = [f['total_filings'] for f in top_firms]
    
    bars = ax1.barh(range(len(firms)), counts)
    ax1.set_yticks(range(len(firms)))
    ax1.set_yticklabels(firms)
    ax1.invert_yaxis()
    ax1.set_xlabel('Number of Filings')
    ax1.set_title('Top 10 Law Firms by Filing Volume', fontsize=14, fontweight='bold')
    
    for i, (bar, count) in enumerate(zip(bars, counts)):
        ax1.text(bar.get_width() + 5, bar.get_y() + bar.get_height()/2, 
                str(count), va='center')
    
    # Monthly Filings
    ax2 = plt.subplot(2, 2, 2)
    months = [item['month'] for item in analysis_data['filings_by_month']]
    month_counts = [item['count'] for item in analysis_data['filings_by_month']]
    
    ax2.plot(range(len(months)), month_counts, 'o-', linewidth=2, markersize=8)
    ax2.set_xticks(range(len(months)))
    ax2.set_xticklabels([m.split()[0] for m in months], rotation=45)
    ax2.set_ylabel('Number of Filings')
    ax2.set_title('Monthly Filing Trends', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    for i, count in enumerate(month_counts):
        ax2.text(i, count + 20, str(count), ha='center')
    
    # Daily Distribution
    ax3 = plt.subplot(2, 2, 3)
    daily_counts = [item['count'] for item in analysis_data['filings_by_date']]
    ax3.hist(daily_counts, bins=20, edgecolor='black', alpha=0.7)
    ax3.set_xlabel('Filings per Day')
    ax3.set_ylabel('Number of Days')
    ax3.set_title('Distribution of Daily Filing Volumes', fontsize=14, fontweight='bold')
    
    # Attorney Distribution
    ax4 = plt.subplot(2, 2, 4)
    attorney_counts = [len(f['attorneys']) for f in analysis_data['filings_by_law_firm']]
    unique_counts = list(set(attorney_counts))
    count_freq = [attorney_counts.count(c) for c in unique_counts]
    
    ax4.bar(unique_counts[:10], count_freq[:10])
    ax4.set_xlabel('Number of Filing Attorneys')
    ax4.set_ylabel('Number of Law Firms')
    ax4.set_title('Distribution of Filing Attorneys per Law Firm', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'filing_analysis_charts.png', dpi=300, bbox_inches='tight')
    
    print("  ✓ Created visualization charts")
    
    # Print final summary
    print("\n" + "="*60)
    print("PIPELINE COMPLETE - SUMMARY")
    print("="*60)
    print(f"Total Filings: {summary_stats['total_filings']:,}")
    print(f"  - With attorneys: {summary_stats['total_with_attorneys']:,}")
    print(f"  - Without attorneys: {summary_stats['filings_without_attorneys']}")
    
    if missing_attorneys:
        doc_nums = [m['doc_num'] for m in missing_attorneys]
        print(f"  - Missing doc nums: {', '.join(doc_nums)}")
    
    print(f"\nTop Firm: {summary_stats['top_firm']['name']} "
          f"({summary_stats['top_firm']['filings']} filings - "
          f"{summary_stats['top_firm']['percentage']}%)")
    print(f"Busiest Month: {summary_stats['busiest_month']['month']} "
          f"({summary_stats['busiest_month']['count']} filings)")
    print(f"Busiest Day: {summary_stats['busiest_day']['date']} "
          f"({summary_stats['busiest_day']['count']} filings)")
    
    print(f"\nTotal Attorneys: {comprehensive_data['metadata']['total_attorneys']}")
    print(f"Total Law Firms: {len(sorted_firms)}")
    print(f"Average per Attorney: {summary_stats['average_filings_per_attorney']} filings")
    print(f"Average per Firm: {summary_stats['average_filings_per_firm']} filings")
    
    if new_attorneys:
        print(f"\nNew attorneys added to lookup ({len(new_attorneys)}):")
        for name in sorted(new_attorneys)[:5]:
            print(f"  - {name}")
        if len(new_attorneys) > 5:
            print(f"  ... and {len(new_attorneys) - 5} more")
    
    print("\nGenerated Files:")
    print("  - attorney_law_firm_lookup.json (updated)")
    print("  - output/attorney_comprehensive_data.json")
    print("  - output/filing_analysis.json")
    print("  - output/filing_summary_stats.json")
    print("  - output/filing_analysis_data.js (JavaScript const format)")
    print("  - output/filing_analysis_charts.png")

if __name__ == "__main__":
    main()