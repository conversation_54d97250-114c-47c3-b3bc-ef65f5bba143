# TALC Analysis Scripts

This directory contains specialized scripts for analyzing and processing Johnson & Johnson talc litigation data from PacerMonitor and PACER sources. These scripts are part of the LexGenius legal intelligence platform for MDL 2738 (In Re: Johnson & Johnson Talcum Powder Products Marketing, Sales Practices and Products Liability Litigation).

## 📁 Files Overview

### Core Scripts

#### 🕷️ `scrape_pacermonitor.py`
**Primary scraping script for PacerMonitor talc litigation data**

- **Purpose**: Scrapes J&J talc case data from PacerMonitor using Google Custom Search
- **Features**:
  - Google Custom Search API integration for case discovery
  - SQLite caching to avoid duplicate downloads
  - Playwright-based stealth scraping with rotating profiles
  - Rich progress bars and console output
  - Rate limiting and error handling
- **Usage**: `python scrape_pacermonitor.py [--search-terms "talc johnson"] [--max-results 100]`
- **Output**: HTML files in `pacermon_html/` and metadata in SQLite database

#### 🎭 `pacermon_playwright_client.py`
**Stealth browser client for PacerMonitor access**

- **Purpose**: Provides anti-detection capabilities for web scraping
- **Features**:
  - Multiple browser profiles (Chrome, Firefox, Safari)
  - Rotating user agents and headers
  - Random viewport sizes and timezones
  - Oxylabs proxy integration support
  - Advanced stealth techniques (navigator overrides, WebGL fingerprinting resistance)
- **Classes**:
  - `BrowserProfileGenerator`: Creates realistic browser fingerprints
  - `PacerMonitorPlaywrightClient`: Main stealth client with async support

#### 🔧 `process_and_upload_talc_data.py`
**Data processing and DynamoDB upload pipeline**

- **Purpose**: Processes scraped TALC data and uploads to DynamoDB Pacer table
- **Features**:
  - Attorney-law firm mapping corrections using `attorney_mapping.json`
  - Data validation and field formatting
  - MDL 2738 classification and metadata enrichment
  - Async DynamoDB batch uploads
  - Progress tracking and error recovery
- **Usage**: `python process_and_upload_talc_data.py [--dry-run] [--batch-size 25]`

#### 🔍 `test_pacermon_parser.py`
**HTML parsing and data extraction**

- **Purpose**: Parses PacerMonitor HTML pages to extract case metadata
- **Features**:
  - BeautifulSoup-based HTML parsing
  - Case number, court, and attorney extraction
  - Filing date and jurisdiction parsing
  - Error handling for malformed HTML
- **Classes**:
  - `PacerMonitorParser`: Main parsing engine with field extraction methods

### Utility Scripts

#### 🔄 `rerun_failed_and_range.py`
**Retry mechanism for failed scraping operations**

- **Purpose**: Reprocesses failed downloads and handles date ranges
- **Features**:
  - Identifies failed/incomplete downloads from SQLite
  - Retry logic with exponential backoff
  - Date range processing for bulk updates
  - Progress tracking and logging

#### 🛠️ `fix_court_id_fuck_up.py`
**Data correction utility**

- **Purpose**: Fixes court ID inconsistencies in scraped data
- **Features**:
  - Batch court ID updates
  - Data validation and verification
  - Rollback capabilities for corrections

#### 🌐 `oxylabs_proxy_helper.py`
**Proxy management for residential IP rotation**

- **Purpose**: Manages Oxylabs residential proxy connections
- **Features**:
  - Proxy authentication and session management
  - IP rotation and geolocation control
  - Connection health monitoring
  - Error handling and failover

### Configuration Files

#### 📋 `attorney_mapping.json`
**Attorney-to-law firm corrections**

Contains mappings for attorney name corrections and law firm associations:
```json
{
  "attorney_mappings": [
    {
      "attorney_name": "DARSEY GLEAN",
      "filing_count": 906,
      "law_firm": "Pulaski Kherker PLLC",
      "notes": "Top attorney by filing count - needs law firm correction"
    }
  ]
}
```

#### 📊 `jj_cases_njd.json`
**Case metadata cache for New Jersey District Court**

Cached case information including:
- Case numbers and titles
- Filing dates and jurisdictions
- Attorney lists and law firm associations
- MDL transfer information

## 🚀 Getting Started

### Prerequisites

1. **Environment Setup**:
   ```bash
   conda activate lexgenius
   pip install playwright beautifulsoup4 requests rich
   playwright install chromium firefox webkit
   ```

2. **API Keys** (set as environment variables):
   ```bash
   export GOOGLE_API_KEY="your_google_api_key"
   export GOOGLE_CSE_ID="your_custom_search_engine_id"
   export OXYLABS_USERNAME="your_oxylabs_username"
   export OXYLABS_PASSWORD="your_oxylabs_password"
   ```

3. **Database Setup**:
   - SQLite database will be created automatically at `sqlite/pacermon_cache.db`
   - DynamoDB table `Pacer` should exist for uploads

### Basic Usage

1. **Scrape New Cases**:
   ```bash
   python scrape_pacermonitor.py --search-terms "talc johnson j&j" --max-results 500
   ```

2. **Process and Upload Data**:
   ```bash
   python process_and_upload_talc_data.py --batch-size 25
   ```

3. **Retry Failed Downloads**:
   ```bash
   python rerun_failed_and_range.py --retry-failures --date-range 2024-01-01,2024-12-31
   ```

## 📊 Data Flow

```
Google Search API → PacerMonitor URLs → Playwright Scraper → HTML Files
                                                                ↓
SQLite Cache ← Parsed Metadata ← BeautifulSoup Parser ← HTML Storage
     ↓
Attorney Mapping Corrections → Data Validation → DynamoDB Upload
                                                       ↓
                                                  Pacer Table
```

## 🔧 Configuration

### Scraping Parameters

- **Rate Limiting**: 2-5 second delays between requests
- **Retry Logic**: 3 attempts with exponential backoff
- **Batch Size**: 25 records per DynamoDB batch
- **Browser Profiles**: Rotates between 6+ realistic profiles

### Data Processing

- **MDL Classification**: All cases tagged with MDL 2738
- **Court ID Standardization**: NJDCE, EDPA, TXSD, etc.
- **Date Formatting**: YYYYMMDD and ISO formats
- **Field Validation**: Required fields checked before upload

## 🔍 Monitoring and Troubleshooting

### Common Issues

1. **Rate Limiting**: 
   - Increase delays in `scrape_pacermonitor.py`
   - Use proxy rotation via Oxylabs

2. **Parser Failures**:
   - Check HTML structure changes in PacerMonitor
   - Update selectors in `test_pacermon_parser.py`

3. **DynamoDB Errors**:
   - Verify AWS credentials and table permissions
   - Check field formatting in `process_and_upload_talc_data.py`

### Logging

- SQLite operations logged to console with Rich formatting
- DynamoDB uploads include progress bars and error tracking
- Failed operations logged with retry recommendations

## 📈 Performance

- **Scraping Speed**: ~2-3 cases per second with stealth mode
- **Processing Speed**: ~100 records per second for data validation
- **Upload Speed**: ~25 records per batch (DynamoDB limit)
- **Storage Efficiency**: HTML files compressed, metadata normalized

## 🔒 Security and Ethics

- **Stealth Browsing**: Anti-detection measures to avoid blocking
- **Rate Limiting**: Respectful scraping with delays
- **Proxy Rotation**: Residential IPs to distribute load
- **Data Privacy**: Public court records only, no PII collection

## 📚 Dependencies

### Python Packages
- `playwright` - Browser automation and stealth
- `beautifulsoup4` - HTML parsing and extraction
- `requests` - HTTP client for API calls
- `rich` - Console formatting and progress bars
- `sqlite3` - Local data caching
- `asyncio` - Async operations for performance

### External Services
- **Google Custom Search API** - Case discovery
- **Oxylabs Proxies** - IP rotation and geolocation
- **AWS DynamoDB** - Production data storage
- **PacerMonitor** - Source for case metadata

## 📝 Notes

- This toolset is specifically designed for MDL 2738 (J&J Talc litigation)
- All data collection follows ethical web scraping guidelines
- Scripts include extensive error handling and retry mechanisms
- Rich console output provides real-time progress tracking
- SQLite caching prevents duplicate work and improves efficiency

## 🤝 Contributing

When modifying these scripts:

1. **Test thoroughly** with small datasets first
2. **Maintain stealth features** to avoid detection
3. **Update mappings** in `attorney_mapping.json` as needed
4. **Document changes** in commit messages
5. **Verify DynamoDB uploads** don't introduce duplicates

---

*Last Updated: 2025-01-02*  
*Part of the LexGenius Legal Intelligence Platform*