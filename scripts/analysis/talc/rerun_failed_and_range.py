#!/usr/bin/env python3
"""
Rerun fetch_failed records and process docket range 3:25-cv-11583 to 3:25-cv-12293
"""

import sqlite3
import subprocess
import sys
from pathlib import Path

# Database configuration - use project root
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent  # Go up from @scripts/analysis/talc/
DB_DIR = PROJECT_ROOT / "sqlite"
DB_FILE = DB_DIR / "pacermon_cache.db"

def get_fetch_failed_dockets():
    """Get all dockets with fetch_failed status that have search results."""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.execute('''
        SELECT docket_num, search_query, timestamp 
        FROM pacermon_searches 
        WHERE status = 'fetch_failed' 
        AND results_json IS NOT NULL
        ORDER BY docket_num
    ''')
    failed_dockets = [(row[0], row[1], row[2]) for row in cursor.fetchall()]
    conn.close()
    return failed_dockets

def reset_failed_dockets():
    """Reset fetch_failed dockets for reprocessing."""
    conn = sqlite3.connect(DB_FILE)
    
    # Reset page_fetched and status for fetch_failed records
    cursor = conn.execute('''
        UPDATE pacermon_searches 
        SET page_fetched = 0, 
            html_file_path = NULL,
            status = 'success',
            retry_count = 0,
            last_error = NULL,
            needs_retry = 0
        WHERE status = 'fetch_failed' 
        AND results_json IS NOT NULL
    ''')
    
    reset_count = cursor.rowcount
    conn.commit()
    conn.close()
    
    return reset_count

def main():
    """Main function to reset failed dockets and run scraper."""
    
    print("🔍 Checking for fetch_failed dockets...")
    
    # Get failed dockets
    failed_dockets = get_fetch_failed_dockets()
    
    if failed_dockets:
        print(f"📋 Found {len(failed_dockets)} fetch_failed dockets:")
        for docket, query, timestamp in failed_dockets[:10]:
            print(f"  - {docket} ({timestamp})")
        if len(failed_dockets) > 10:
            print(f"  ... and {len(failed_dockets) - 10} more")
        
        # Reset the failed dockets
        print("\n🔄 Resetting fetch_failed dockets for reprocessing...")
        reset_count = reset_failed_dockets()
        print(f"✅ Reset {reset_count} fetch_failed dockets")
    else:
        print("✅ No fetch_failed dockets found")
    
    # Run the scraper with the specified range
    print("\n🚀 Starting scraper for range 3:25-cv-11583 to 3:25-cv-12293...")
    
    # Prepare input for the scraper
    input_data = "3:25-cv-11583\n3:25-cv-12293\n"
    
    try:
        # Run the scraper
        scraper_path = Path(__file__).parent / "scrape_pacermonitor.py"
        
        process = subprocess.run(
            [sys.executable, str(scraper_path)],
            input=input_data,
            text=True,
            cwd=PROJECT_ROOT,  # Run from project root
            capture_output=False  # Let output show in real-time
        )
        
        if process.returncode == 0:
            print("\n✅ Scraper completed successfully!")
        else:
            print(f"\n❌ Scraper failed with return code: {process.returncode}")
            
    except Exception as e:
        print(f"\n❌ Error running scraper: {e}")

if __name__ == "__main__":
    main()