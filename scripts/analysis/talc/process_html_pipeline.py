#!/usr/bin/env python3
"""
Process HTML Pipeline for TALC Cases
Extracts case URLs and titles from PacerMonitor HTML, then scrapes each case using stealth proxy
"""

import asyncio
import json
import re
import os
import sqlite3
import sys
import random
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Add project root to sys.path for imports
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Load environment variables before any other imports
from dotenv import load_dotenv
load_dotenv(PROJECT_ROOT / '.env')

from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
from src.services.scraping.proxy.proxy_manager import ProxyManager
from test_pacermon_parser import PacerMonitorParser

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
import logging

# Use same paths as scrape_pacermonitor.py
DB_DIR = PROJECT_ROOT / "sqlite"
DB_FILE = DB_DIR / "pacermon_cache.db"
HTML_DIR = PROJECT_ROOT / "pacermon_html"
HTML_DIR.mkdir(exist_ok=True)

# Import existing functions

def save_search_result(docket_num: str, search_query: str, results: dict, status: str, case_data: dict = None, html_file_path: str = None, exists_dynamo: bool = False):
    """Save search result to database."""
    conn = sqlite3.connect(DB_FILE)
    timestamp = datetime.now().isoformat()
    
    if case_data:
        # Save with parsed case data
        conn.execute('''
            INSERT OR REPLACE INTO pacermon_searches 
            (docket_num, search_query, results_json, timestamp, status, assigned_to, referred_to, nos, cause, 
             filing_date, defendant, plaintiff, attorney, versus, html_file_path, page_fetched, exists_dynamo)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            docket_num,
            search_query,
            json.dumps(results) if results else None,
            timestamp,
            status,
            case_data.get('assigned_to'),
            case_data.get('referred_to'),
            case_data.get('nos'),
            case_data.get('cause'),
            case_data.get('filing_date'),
            json.dumps(case_data.get('defendant', [])),
            json.dumps(case_data.get('plaintiff', [])),
            json.dumps(case_data.get('attorney', [])),
            case_data.get('versus'),
            html_file_path,
            True,
            exists_dynamo
        ))
    else:
        # Save with just basic info
        conn.execute('''
            INSERT OR REPLACE INTO pacermon_searches 
            (docket_num, search_query, results_json, timestamp, status, html_file_path, page_fetched, exists_dynamo)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            docket_num,
            search_query,
            json.dumps(results) if results else None,
            timestamp,
            status,
            html_file_path,
            bool(html_file_path),
            exists_dynamo
        ))
    
    conn.commit()
    conn.close()

class HTMLPipeline:
    def __init__(self, html_file: str):
        self.html_file = html_file
        self.parser = PacerMonitorParser()
        self.pacer_repo = None  # Will be initialized when needed
        self.dynamo_storage = None  # Will be initialized when needed
        
    def standardize_case_title(self, title: str) -> str:
        """Standardize case title formatting - ensure 'et al' becomes 'et al.'"""
        if not title:
            return title
            
        # Replace various forms of "et al" with standardized "et al."
        # Handle: "et al", "et al.", "Et Al", "ET AL", "et. al", etc.
        title = re.sub(r'\bet\s*\.?\s*al\.?(?!\w)', 'et al.', title, flags=re.IGNORECASE)
        
        return title
    
    def extract_search_results_data(self, tr_element) -> Dict[str, str]:
        """Extract case data directly from search results table row"""
        case_data = {}
        
        try:
            # Get the main td element
            td = tr_element.find('td')
            if not td:
                return case_data
            
            # Extract case title from the link
            link = td.find('a')
            if link:
                title = link.get_text(strip=True)
                case_data['case_title'] = self.standardize_case_title(title)
                case_data['href'] = link.get('href')
            
            # Extract fields from bkattrib spans
            for span in td.find_all('span', class_='bkattrib'):
                prev_label = span.find_previous_sibling('span', class_='bkattriblabel')
                if prev_label:
                    label = prev_label.get_text().strip().rstrip(':')
                    value = span.get_text(strip=True)
                    
                    if 'Court' in label:
                        case_data['court'] = value
                    elif 'Case Num' in label:
                        case_data['docket_num'] = value
                    elif 'Filed' in label:
                        case_data['filing_date'] = value
                    elif 'Nature of suit' in label:
                        case_data['nos'] = value
                    elif 'Cause' in label:
                        case_data['cause'] = value
            
        except Exception as e:
            print(f"Error extracting search results data: {e}")
        
        return case_data
        
    async def _init_dynamo_if_needed(self):
        """Initialize DynamoDB repository if not already initialized"""
        if self.pacer_repo is None:
            # Load environment variables if .env exists
            env_file = PROJECT_ROOT / '.env'
            if env_file.exists():
                from dotenv import load_dotenv
                load_dotenv(env_file)
            
            logger = logging.getLogger(__name__)
            
            # Configure AWS credentials from environment
            config = {
                'region_name': os.getenv('AWS_REGION', 'us-west-2'),
                'aws_access_key_id': os.getenv('AWS_ACCESS_KEY_ID'),
                'aws_secret_access_key': os.getenv('AWS_SECRET_ACCESS_KEY')
            }
            
            self.dynamo_storage = AsyncDynamoDBStorage(config, logger=logger)
            await self.dynamo_storage.__aenter__()
            self.pacer_repo = PacerRepository(self.dynamo_storage, logger)
        
    async def extract_case_data(self) -> List[Dict[str, Any]]:
        """Extract case URLs and titles from HTML file"""
        # FIRST: Get dockets that exist in SQLite AND have been processed to DynamoDB
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        # Only skip if exists_dynamo is True or status is 'success' AND exists_dynamo is not False
        cursor.execute("""
            SELECT DISTINCT docket_num FROM pacermon_searches 
            WHERE exists_dynamo = 1 
            OR (status = 'success' AND exists_dynamo IS NOT 0)
        """)
        skip_dockets = {row[0] for row in cursor.fetchall()}
        
        # Also get total count for reporting
        cursor.execute("SELECT COUNT(DISTINCT docket_num) FROM pacermon_searches")
        total_in_sqlite = cursor.fetchone()[0]
        conn.close()
        
        print(f"Found {total_in_sqlite} total dockets in SQLite, {len(skip_dockets)} fully processed (will skip)")
        
        # NOW parse HTML and filter immediately
        with open(self.html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        cases = []
        skipped_count = 0
        
        # Find all case links
        case_links = soup.find_all('a', href=re.compile(r'/public/case/\d+/'))
        
        for link in case_links:
            href = link.get('href')
            title = link.get_text(strip=True)
            
            # Standardize title formatting (et al -> et al.)
            title = self.standardize_case_title(title)
            
            # Only process Johnson & Johnson cases - must contain exact string
            if 'JOHNSON & JOHNSON' in title.upper() or 'JOHNSON &AMP; JOHNSON' in title.upper():
                # Find the parent element that contains the case info
                parent = link.parent
                while parent and parent.name != 'td':
                    parent = parent.parent
                
                # Extract docket number from the case info spans
                docket_num = None
                if parent:
                    # Look for Case Num: span
                    case_num_label = parent.find('span', string='Case Num:')
                    if case_num_label:
                        case_num_value = case_num_label.find_next_sibling('span', class_='bkattrib')
                        if case_num_value:
                            docket_num = case_num_value.get_text(strip=True)
                
                # Fallback: try to extract from title
                if not docket_num:
                    docket_match = re.search(r'(\d+:\d+-cv-\d+)', title)
                    if docket_match:
                        docket_num = docket_match.group(1)
                    else:
                        # Use a placeholder that will be updated after scraping
                        versus_clean = re.sub(r'[^a-zA-Z0-9_]+', '_', title)
                        docket_num = f"case_{versus_clean}"
                
                # CHECK IF FULLY PROCESSED - DON'T ADD TO LIST IF ALREADY IN DYNAMO
                if docket_num in skip_dockets:
                    skipped_count += 1
                    print(f"Skipping {docket_num} - already fully processed (exists in DynamoDB)")
                    continue  # SKIP THIS CASE ENTIRELY
                
                # ADDITIONAL CHECK: Query DynamoDB directly for court_id='njd'
                try:
                    # Initialize DynamoDB connection if needed
                    await self._init_dynamo_if_needed()
                    
                    # Check if docket exists in DynamoDB with court_id='njd'
                    docket_exists_dynamo = await self.pacer_repo.check_docket_exists('njd', docket_num)
                    
                    if docket_exists_dynamo:
                        skipped_count += 1
                        print(f"Skipping {docket_num} - found in DynamoDB (court_id='njd')")
                        continue  # SKIP THIS CASE ENTIRELY
                        
                except Exception as e:
                    print(f"Warning: DynamoDB check failed for {docket_num}: {str(e)}")
                    # Continue processing if DynamoDB check fails - don't block pipeline
                
                # Only add if NOT in SQLite AND NOT in DynamoDB
                case_url = urljoin('https://www.pacermonitor.com', href)
                cases.append({
                    'url': case_url,
                    'title': title,
                    'docket_num': docket_num,
                    'href': href
                })
        
        if skipped_count > 0:
            print(f"\nFiltered out {skipped_count} cases that are already fully processed (exist in DynamoDB)")
        
        return cases
    
    def create_google_referral(self, case_title: str) -> str:
        """Create Google search referral URL with random court identifier + case title"""
        # Clean title for search
        clean_title = re.sub(r'[^a-zA-Z0-9\s]', ' ', case_title)
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        
        # Randomly select court identifier
        court_identifiers = ['njd', 'new jersey', 'nj dc', 'new jersey court']
        random_court = random.choice(court_identifiers)
        
        # Create search query with random court identifier prefix
        search_query = f"{random_court} {clean_title}"
        return search_query
    
    async def scrape_case_with_new_client(self, case_data: Dict[str, Any], case_index: int, total_cases: int) -> bool:
        """Scrape case with a fresh client to ensure new proxy on retries"""
        docket_num = case_data['docket_num']
        case_title = case_data['title'][:50] + ("..." if len(case_data['title']) > 50 else "")
        
        max_retries = 5
        
        for retry in range(max_retries):
            # Create a new session manager for each attempt to ensure fresh proxy
            session_manager = None
            try:
                # Configure proxy manager for residential proxies
                proxy_config = {
                    'oxy_labs_residential_username': os.getenv('OXY_LABS_RESIDENTIAL_USERNAME'),
                    'oxy_labs_residential_password': os.getenv('OXY_LABS_RESIDENTIAL_PASSWORD'),
                    'oxylabs_num_proxies': 10000,
                    'use_proxy': True,
                    'mobile_proxy': False,
                    'rotate_proxy_per_page': True
                }
                
                # Initialize proxy manager
                proxy_manager = ProxyManager(proxy_config, logging.getLogger(__name__))
                
                # Configure session manager
                config = {
                    'camoufox': {
                        'browser': {
                            'headless': True,
                            'timeout': 60000,
                            'viewport': {'width': 1920, 'height': 1080}
                        },
                        'session': {
                            'min_duration_minutes': 3,
                            'max_duration_minutes': 5
                        },
                        'anti_bot': {
                            'humanize': True,
                            'disable_ad_blocker_detection': True
                        }
                    }
                }
                
                session_manager = CamoufoxSessionManager(
                    config=config,
                    logger=logging.getLogger(__name__),
                    proxy_manager=proxy_manager
                )
                
                success = await self.scrape_case(case_data, session_manager, case_index, total_cases, retry, max_retries)
                if success is not None:
                    return success
                # If None returned, it means we need to retry with new proxy
                
            except Exception as e:
                print(f"Error creating session manager: {e}")
                continue
            finally:
                if session_manager:
                    try:
                        await session_manager.cleanup()
                    except Exception as e:
                        print(f"Error closing session: {e}")
                
        # Max retries exhausted
        save_search_result(docket_num, self.create_google_referral(case_data['title']), None, 'rate_limited', None, None, False)
        print("FAILED - Max retries for rate limit")
        return False
    
    async def scrape_case(self, case_data: Dict[str, Any], session_manager: CamoufoxSessionManager, case_index: int, total_cases: int, retry: int = 0, max_retries: int = 5) -> Optional[bool]:
        """Scrape individual case page"""
        docket_num = case_data['docket_num']
        case_title = case_data['title'][:50] + ("..." if len(case_data['title']) > 50 else "")
        
        try:
            if retry == 0:
                print(f"Processing case {case_index}/{total_cases}: {case_title}", end=" - ")
            else:
                print(f"Retry {retry}/{max_retries} for case {case_index}/{total_cases}: {case_title}", end=" - ")
            
            # Create Google referral search query
            search_query = self.create_google_referral(case_data['title'])
            
            # Create HTML filename
            safe_docket = docket_num.replace(':', '_').replace('-', '_')
            html_filename = f"{safe_docket}.html"
            html_filepath = HTML_DIR / html_filename
            
            # Create browser and page without Facebook navigation
            if not await session_manager._create_browser_and_page():
                print(f"FAILED - Could not create browser")
                return False
            
            # Navigate directly to the target URL (skip Facebook setup)
            response = await session_manager.page.goto(case_data['url'], wait_until='domcontentloaded', timeout=60000)
            
            if response and response.status == 200:
                # Get page content
                html_content = await session_manager.page.content()
                status_code = 200
            else:
                html_content = None
                status_code = response.status if response else None
            
            if html_content:
                # Save HTML to file
                with open(html_filepath, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                # Parse the HTML content
                parsed_data = self.parser.parse_html_content(html_content)
                
                # Extract actual docket number from parsed data if available
                actual_docket_num = parsed_data.get('docket_num') or docket_num
                
                # UPDATE case_data with the real docket number for saving
                if parsed_data.get('docket_num'):
                    # Update the parsed_data to ensure it has the correct docket_num
                    parsed_data['docket_num'] = actual_docket_num
                
                # Since we already checked DynamoDB before scraping, set exists_dynamo=False
                # This indicates the docket was NOT found in DynamoDB during pre-check
                exists_dynamo = False
                
                # Save to database with the ACTUAL docket number, not the placeholder
                save_search_result(actual_docket_num, search_query, None, 'success', parsed_data, str(html_filepath), exists_dynamo)
                
                print("SUCCESS")
                return True
            elif status_code == 429:
                # Rate limited - need to retry with new proxy
                print(f"RATE LIMITED - Will create new client with fresh proxy...")
                return None  # Signal to create new client
            else:
                # Other failure (not rate limit)
                # For failures, we use the original docket_num since we couldn't parse anything
                save_search_result(docket_num, search_query, None, 'fetch_failed', None, None, False)
                print(f"FAILED - Status: {status_code}")
                return False
                
        except Exception as e:
            # Save error to database
            # For errors, we use the original docket_num since we couldn't parse anything
            save_search_result(docket_num, search_query, None, 'error', None, None, False)
            print(f"ERROR: {str(e)}")
            return False
    
    def _create_filename(self, case_data: Dict[str, Any]) -> str:
        """Create safe filename from case data"""
        docket_num = case_data['docket_num']
        safe_docket = docket_num.replace(':', '_').replace('-', '_')
        return f"{safe_docket}.html"
    
    async def run_pipeline(self, max_cases: int = None):
        """Run the complete pipeline"""
        # Extract case data (already filtered for J&J and existing in SQLite + DynamoDB)
        cases = await self.extract_case_data()
        
        if not cases:
            print("All Johnson & Johnson cases are already fully processed (exist in DynamoDB)!")
            return 0
        
        print(f"Found {len(cases)} new Johnson & Johnson cases to process")
        
        if max_cases:
            cases = cases[:max_cases]
            print(f"Test mode: limiting to {len(cases)} dockets")
        
        # Process each case
        success_count = 0
        
        for i, case_data in enumerate(cases, 1):
            success = await self.scrape_case_with_new_client(case_data, i, len(cases))
            if success:
                success_count += 1
            
            # Delay between requests with jitter to avoid predictable patterns
            delay = 2 + random.uniform(0.5, 1.5)  # 2.5-3.5 seconds
            await asyncio.sleep(delay)
        
        print(f"Processing complete: {success_count}/{len(cases)} dockets successful")
        
        # Cleanup DynamoDB connection if initialized
        if self.dynamo_storage:
            await self.dynamo_storage.__aexit__(None, None, None)
        
        return success_count

async def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Process TALC HTML and scrape cases")
    parser.add_argument("--max-cases", type=int, help="Maximum number of cases to process (for testing)")
    parser.add_argument("--date", type=str, help="Date for processing (YYYYMMDD format)")
    
    args = parser.parse_args()
    
    # Default HTML file - use the most recent one with July 16th cases
    html_file = current_dir / "jj_pacermon.html"
    
    if not os.path.exists(html_file):
        print(f"HTML file not found: {html_file}")
        return
    
    pipeline = HTMLPipeline(str(html_file))
    await pipeline.run_pipeline(max_cases=args.max_cases)

if __name__ == "__main__":
    asyncio.run(main())