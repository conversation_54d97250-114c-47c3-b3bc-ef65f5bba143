import requests
import json
import os
import re
import sqlite3
import argparse
import time
import random
from datetime import datetime
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, TaskID
from rich.table import Table
from rich import print as rprint
from bs4 import BeautifulSoup
from .pacermon_playwright_client import PacerMonitor<PERSON>laywright<PERSON>lient
from .test_pacermon_parser import PacerM<PERSON><PERSON>Parser

# --- Configuration ---
# BEST PRACTICE: Store these as environment variables, not directly in the code.
# To set them in your terminal (Linux/macOS):
# export GOOGLE_API_KEY="YOUR_API_KEY_HERE"
# export GOOGLE_CSE_ID="YOUR_CSE_ID_HERE"
#
# To set them in Windows Command Prompt:
# set GOOGLE_API_KEY="YOUR_API_KEY_HERE"
# set GOOGLE_CSE_ID="YOUR_CSE_ID_HERE"
GOOGLE_CSE_ID="e4f2dac2992d54359"
GOOGLE_API_KEY="AIzaSyD0TRBN8_o3qENXQPvkey4J4uWBi20DpUg"

API_KEY = GOOGLE_API_KEY
CSE_ID = GOOGLE_CSE_ID

if not API_KEY or not CSE_ID:
    print("Error: Please set GOOGLE_API_KEY and GOOGLE_CSE_ID environment variables.")
    exit()

# Initialize rich console
console = Console()

# SQLite database - use project root
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent  # Go up from @scripts/analysis/talc/
DB_DIR = PROJECT_ROOT / "sqlite"
DB_FILE = DB_DIR / "pacermon_cache.db"

# HTML storage directory - use project root
HTML_DIR = PROJECT_ROOT / "pacermon_html"
HTML_DIR.mkdir(exist_ok=True)


# --- Database Functions ---
def ensure_db():
    """Create database and table if they don't exist."""
    DB_DIR.mkdir(exist_ok=True)
    
    conn = sqlite3.connect(DB_FILE)
    
    # Create table with all columns
    conn.execute('''
        CREATE TABLE IF NOT EXISTS pacermon_searches (
            docket_num TEXT PRIMARY KEY,
            search_query TEXT NOT NULL,
            results_json TEXT,
            timestamp TEXT NOT NULL,
            status TEXT DEFAULT 'success',
            assigned_to TEXT,
            referred_to TEXT,
            nos TEXT,
            cause TEXT,
            filing_date TEXT,
            defendant TEXT,
            plaintiff TEXT,
            attorney TEXT,
            versus TEXT,
            html_file_path TEXT,
            page_fetched BOOLEAN DEFAULT 0,
            retry_count INTEGER DEFAULT 0,
            last_error TEXT,
            needs_retry BOOLEAN DEFAULT 0
        )
    ''')
    
    # Check if retry columns exist and add them if missing
    cursor = conn.execute("PRAGMA table_info(pacermon_searches)")
    columns = [row[1] for row in cursor.fetchall()]
    
    missing_columns = []
    if 'retry_count' not in columns:
        missing_columns.append(('retry_count', 'INTEGER DEFAULT 0'))
    if 'last_error' not in columns:
        missing_columns.append(('last_error', 'TEXT'))
    if 'needs_retry' not in columns:
        missing_columns.append(('needs_retry', 'BOOLEAN DEFAULT 0'))
    
    # Add missing columns
    for column_name, column_def in missing_columns:
        try:
            conn.execute(f'ALTER TABLE pacermon_searches ADD COLUMN {column_name} {column_def}')
            console.print(f"[yellow]Added missing column: {column_name}[/yellow]")
        except sqlite3.OperationalError as e:
            console.print(f"[red]Error adding column {column_name}: {e}[/red]")
    
    conn.commit()
    conn.close()

def is_docket_cached(docket_num):
    """Check if docket is already in cache."""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.execute('SELECT 1 FROM pacermon_searches WHERE docket_num = ?', (docket_num,))
    exists = cursor.fetchone() is not None
    conn.close()
    return exists

def has_search_results(docket_num):
    """Check if docket already has Google search results."""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.execute('SELECT results_json FROM pacermon_searches WHERE docket_num = ? AND results_json IS NOT NULL', (docket_num,))
    result = cursor.fetchone()
    conn.close()
    return result is not None

def get_cached_search_results(docket_num):
    """Get cached search results for a docket."""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.execute('SELECT results_json FROM pacermon_searches WHERE docket_num = ?', (docket_num,))
    result = cursor.fetchone()
    conn.close()
    
    if result and result[0]:
        return json.loads(result[0])
    return None

def is_page_already_processed(docket_num):
    """Check if docket page has already been fetched and processed."""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.execute('SELECT page_fetched FROM pacermon_searches WHERE docket_num = ? AND page_fetched = 1', (docket_num,))
    result = cursor.fetchone()
    conn.close()
    return result is not None

def needs_page_processing(docket_num):
    """Check if docket has search results but needs page processing."""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.execute('''
        SELECT results_json, page_fetched 
        FROM pacermon_searches 
        WHERE docket_num = ? AND results_json IS NOT NULL
    ''', (docket_num,))
    result = cursor.fetchone()
    conn.close()
    
    if result:
        results_json, page_fetched = result
        # Has search results but page not fetched
        return results_json and not page_fetched
    return False

def save_search_result(docket_num, search_query, results, status='success', case_data=None, html_file_path=None, retry_count=0, last_error=None, needs_retry=False):
    """Save search result and case data to database."""
    timestamp = datetime.now().isoformat()
    results_json = json.dumps(results) if results else None
    page_fetched = 1 if case_data else 0
    
    # Extract case data fields
    assigned_to = case_data.get('assigned_to') if case_data else None
    referred_to = case_data.get('referred_to') if case_data else None
    nos = case_data.get('nos') if case_data else None
    cause = case_data.get('cause') if case_data else None
    filing_date = case_data.get('filing_date') if case_data else None
    defendant = json.dumps(case_data.get('defendant', [])) if case_data else None
    plaintiff = json.dumps(case_data.get('plaintiff', [])) if case_data else None
    attorney = json.dumps(case_data.get('attorney', [])) if case_data else None
    versus = case_data.get('versus') if case_data else None
    
    conn = sqlite3.connect(DB_FILE)
    conn.execute('''
        INSERT OR REPLACE INTO pacermon_searches 
        (docket_num, search_query, results_json, timestamp, status, assigned_to, referred_to, 
         nos, cause, filing_date, defendant, plaintiff, attorney, versus, html_file_path, page_fetched,
         retry_count, last_error, needs_retry)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (docket_num, search_query, results_json, timestamp, status, assigned_to, referred_to,
          nos, cause, filing_date, defendant, plaintiff, attorney, versus, html_file_path, page_fetched,
          retry_count, last_error, needs_retry))
    conn.commit()
    conn.close()

def get_cached_dockets(docket_list):
    """Get list of dockets that are already cached."""
    if not docket_list:
        return set()
    
    conn = sqlite3.connect(DB_FILE)
    placeholders = ','.join('?' * len(docket_list))
    cursor = conn.execute(f'SELECT docket_num FROM pacermon_searches WHERE docket_num IN ({placeholders})', docket_list)
    cached = {row[0] for row in cursor.fetchall()}
    conn.close()
    return cached

def get_failed_dockets():
    """Get list of dockets that need retry due to API errors."""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.execute('SELECT docket_num, retry_count, last_error FROM pacermon_searches WHERE needs_retry = 1 AND retry_count < 5')
    failed = [(row[0], row[1], row[2]) for row in cursor.fetchall()]
    conn.close()
    return failed

def is_already_successful(docket_num):
    """Check if docket already has successful status."""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.execute('SELECT 1 FROM pacermon_searches WHERE docket_num = ? AND status = ?', (docket_num, 'success'))
    exists = cursor.fetchone() is not None
    conn.close()
    return exists

def load_dockets_from_json(json_file_path):
    """Load dockets from JSON file."""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        dockets = []
        if 'missing_dockets' in data:
            for docket_info in data['missing_dockets']:
                dockets.append({
                    'docket_num': docket_info['docket_num'],
                    'versus': docket_info.get('versus', ''),
                    'filing_date': docket_info.get('filing_date', ''),
                    'hyperlink': docket_info['hyperlink']
                })
        
        console.print(f"[green]Loaded {len(dockets)} dockets from {json_file_path}[/green]")
        return dockets
        
    except Exception as e:
        console.print(f"[red]Error loading JSON file: {e}[/red]")
        return []

def create_fake_search_results(docket_info):
    """Create fake Google search results with the provided hyperlink."""
    return {
        'searchInformation': {
            'totalResults': '1'
        },
        'items': [
            {
                'title': f"{docket_info['versus']} - {docket_info['docket_num']} - PacerMonitor.com",
                'link': docket_info['hyperlink'],
                'snippet': f"Case: {docket_info['versus']} Filed: {docket_info['filing_date']} District: New Jersey"
            }
        ]
    }

def parse_docket_input(docket_str):
    """Parse docket string to extract base pattern and number."""
    match = re.match(r'(.*?)([0-9]{5})$', docket_str)
    if match:
        base_pattern = match.group(1)
        number = int(match.group(2))
        return base_pattern, number
    else:
        raise ValueError(f"Invalid docket format: {docket_str}")

def generate_docket_range(start_docket, end_docket):
    """Generate list of docket numbers between start and end."""
    start_base, start_num = parse_docket_input(start_docket)
    end_base, end_num = parse_docket_input(end_docket)
    
    if start_base != end_base:
        raise ValueError("Start and end dockets must have the same base pattern")
    
    docket_list = []
    for num in range(start_num, end_num + 1):
        docket_list.append(f"{start_base}{num:05d}")
    
    return docket_list

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='PACER Monitor Batch Search')
    parser.add_argument('--test', action='store_true', 
                       help='Test mode: process only 5 dockets for verification')
    parser.add_argument('--from-json', type=str, 
                       help='Path to JSON file with docket data (e.g., docket_check_results.json)')
    return parser.parse_args()

def get_user_input(test_mode=False):
    """Get start and end docket numbers from user."""
    console.print("\n[bold blue]PACER Monitor Batch Search[/bold blue]")
    if test_mode:
        console.print("[yellow]TEST MODE: Will process only 5 dockets[/yellow]")
    console.print("Enter docket number range (e.g., 3:25-cv-10313 to 3:25-cv-12221)\n")
    
    start_docket = input("Start docket number: ").strip()
    end_docket = input("End docket number: ").strip()
    
    return start_docket, end_docket

# --- Script Logic ---
def filter_johnson_and_johnson_results(results):
    """Filter search results to only include Johnson & Johnson cases."""
    if not results or 'items' not in results:
        return None
    
    filtered_items = []
    for item in results['items']:
        title = item.get('title', '').lower()
        if 'johnson' in title and 'johnson' in title:
            # More precise check for "johnson & johnson" pattern
            if 'johnson & johnson' in title or 'johnson &amp; johnson' in title:
                filtered_items.append(item)
    
    if filtered_items:
        # Create new results object with filtered items
        filtered_results = results.copy()
        filtered_results['items'] = filtered_items
        return filtered_results
    
    return None

def search_pacer_monitor(query, max_wait_time=120):
    """
    Searches pacermonitor.com via the Google Custom Search API for a specific query with retry logic.
    Retries indefinitely with exponential backoff until success or max_wait_time reached.
    """
    url = "https://www.googleapis.com/customsearch/v1"

    # The parameters for the API request
    params = {
        'q': f"{query} site:pacermonitor.com",
        'cx': CSE_ID,
        'key': API_KEY,
    }

    console.print(f"Searching for: '{query}' on pacermonitor.com...")

    attempt = 0
    start_time = time.time()
    
    while True:
        try:
            # Explicitly disable proxies for Google Custom Search API
            response = requests.get(url, params=params, proxies={'http': None, 'https': None})
            response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)

            # The API returns results in JSON format
            results = response.json()
            
            # Filter for Johnson & Johnson cases only
            filtered_results = filter_johnson_and_johnson_results(results)
            
            if filtered_results:
                console.print(f"[green]Found {len(filtered_results['items'])} Johnson & Johnson results[/green]")
                return filtered_results, None  # Return results and no error
            else:
                console.print(f"[yellow]No Johnson & Johnson cases found for {query}[/yellow]")
                return None, None  # Return no results but no error

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:  # Rate limit
                # Check if we've exceeded max wait time
                elapsed_time = time.time() - start_time
                if elapsed_time >= max_wait_time:
                    error_msg = f"Rate limit exceeded after {max_wait_time} seconds of retrying"
                    console.print(f"[red]{error_msg}[/red]")
                    return None, error_msg
                
                # Exponential backoff with jitter, capped at 30 seconds
                wait_time = min(30, (2 ** min(attempt, 5)) + random.uniform(0, 1))
                attempt += 1
                
                remaining_time = max_wait_time - elapsed_time
                console.print(f"[yellow]Rate limited (429). Waiting {wait_time:.1f}s before retry #{attempt} (remaining time: {remaining_time:.1f}s)[/yellow]")
                time.sleep(wait_time)
                continue
            else:
                error_msg = f"HTTP error {e.response.status_code}: {e}"
                console.print(f"[red]HTTP error: {error_msg}[/red]")
                return None, error_msg
        
        except requests.exceptions.RequestException as e:
            error_msg = f"Request error: {e}"
            console.print(f"[red]Request error: {error_msg}[/red]")
            return None, error_msg

async def fetch_and_parse_page(url, search_query, docket_num):
    """Fetch HTML page and parse case data."""
    try:
        # Check if HTML file already exists
        safe_docket = docket_num.replace(':', '_')
        html_filename = f"{safe_docket}.html"
        html_filepath = HTML_DIR / html_filename
        
        if html_filepath.exists():
            console.print(f"[blue]Using cached HTML file: {html_filename}[/blue]")
            with open(html_filepath, 'r', encoding='utf-8') as f:
                html_content = f.read()
        else:
            # Fetch the HTML page with stealth Playwright and Oxylabs proxies
            console.print(f"[cyan]Fetching HTML page with stealth browser + Oxylabs proxies: {url}[/cyan]")
            async with PacerMonitorPlaywrightClient(
                headless=True, 
                use_oxylabs=True, 
                mobile_proxy=True,   # Use mobile proxies (you have mobile credentials)
                num_proxies=10000    # Generate 10,000 proxy sessions
            ) as client:
                html_content = await client.fetch_page(url, search_query=search_query)
            
            if not html_content:
                console.print(f"[red]Failed to fetch HTML for {docket_num}[/red]")
                return None
                
            # Save HTML to file
            with open(html_filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            console.print(f"[green]Saved HTML to: {html_filename}[/green]")
        
        # Parse the HTML content
        parser = PacerMonitorParser()
        case_data = parser.parse_html_content(html_content)
        
        if case_data:
            console.print(f"[green]Successfully parsed case data for {docket_num}[/green]")
            return case_data, str(html_filepath)
        else:
            console.print(f"[yellow]No case data extracted from HTML for {docket_num}[/yellow]")
            return None, str(html_filepath)
            
    except Exception as e:
        console.print(f"[red]Error fetching/parsing page for {docket_num}: {e}[/red]")
        return None

async def parse_case_data(case_data_result, docket_num, search_query, results):
    """Parse case data and save to database."""
    if case_data_result:
        case_data, html_file_path = case_data_result
        if case_data:
            # Save with case data
            save_search_result(docket_num, search_query, results, 'success', case_data, html_file_path)
            
            # Display parsed data
            table = Table(title=f"Parsed Case Data for {docket_num}")
            table.add_column("Field", style="cyan")
            table.add_column("Value", style="white")
            
            basic_fields = ['versus', 'assigned_to', 'referred_to', 'nos', 'cause', 'filing_date']
            for field in basic_fields:
                value = case_data.get(field, 'N/A')
                table.add_row(field.replace('_', ' ').title(), str(value))
            
            console.print(table)
        else:
            # Save with just the HTML file path
            save_search_result(docket_num, search_query, results, 'parsed_no_data', None, case_data_result[1])
    else:
        # Save search results without parsing
        save_search_result(docket_num, search_query, results, 'fetch_failed')


# --- Main Execution ---
import asyncio

async def main():
    """Main async function."""
    try:
        # Parse command line arguments
        args = parse_args()
        
        # Initialize database
        ensure_db()
        
        # Determine input mode
        if args.from_json:
            # Load dockets from JSON file
            console.print(f"\n[blue]Loading dockets from JSON file: {args.from_json}[/blue]")
            docket_data = load_dockets_from_json(args.from_json)
            if not docket_data:
                console.print("[red]No dockets loaded from JSON file. Exiting.[/red]")
                return
            
            # Filter out already successful dockets
            original_count = len(docket_data)
            docket_data = [d for d in docket_data if not is_already_successful(d['docket_num'])]
            skipped_successful = original_count - len(docket_data)
            
            if skipped_successful > 0:
                console.print(f"[blue]Skipped {skipped_successful} dockets with status='success'[/blue]")
            
            if not docket_data:
                console.print("[green]All dockets already processed successfully![/green]")
                return
                
            # Extract docket numbers for compatibility with existing code
            docket_list = [d['docket_num'] for d in docket_data]
            console.print(f"[green]Processing {len(docket_list)} dockets from JSON[/green]")
            
            # Create a lookup dict for docket info
            docket_info_lookup = {d['docket_num']: d for d in docket_data}
            
        else:
            # Original behavior: get user input for range
            start_docket, end_docket = get_user_input(test_mode=args.test)
            
            # Generate docket range
            console.print(f"\n[yellow]Generating docket range from {start_docket} to {end_docket}...[/yellow]")
            docket_list = generate_docket_range(start_docket, end_docket)
            console.print(f"[green]Generated {len(docket_list)} docket numbers[/green]")
            docket_info_lookup = None
        
        # Limit to 5 for test mode
        if args.test:
            docket_list = docket_list[:5]
            console.print(f"[yellow]Test mode: limiting to {len(docket_list)} dockets[/yellow]")
        
        # Filter out already processed dockets (only for non-JSON mode since JSON already filtered)
        if not args.from_json:
            cached_dockets = get_cached_dockets(docket_list)
            new_dockets = [d for d in docket_list if d not in cached_dockets]
            skipped_count = len(docket_list) - len(new_dockets)
        else:
            # For JSON mode, we already filtered successful ones
            new_dockets = docket_list
            skipped_count = 0
        
        # Add failed dockets that need retry
        failed_dockets = get_failed_dockets()
        retry_dockets = [docket for docket, retry_count, error in failed_dockets if retry_count < 3]  # Max 3 retries
        
        # Combine new and retry dockets
        all_dockets_to_process = new_dockets + retry_dockets
        
        if skipped_count > 0:
            console.print(f"[blue]Skipping {skipped_count} already processed dockets[/blue]")
        
        if retry_dockets:
            console.print(f"[yellow]Adding {len(retry_dockets)} failed dockets for retry[/yellow]")
        
        if not all_dockets_to_process:
            console.print("[green]All dockets already processed![/green]")
            exit(0)
        
        console.print(f"[yellow]Processing {len(all_dockets_to_process)} total dockets ({len(new_dockets)} new, {len(retry_dockets)} retries)...[/yellow]\\n")
        
        # Process dockets with progress bar
        with Progress() as progress:
            task = progress.add_task("[cyan]Searching dockets...", total=len(all_dockets_to_process))
            
            for docket in all_dockets_to_process:
                # Check if this is a retry
                is_retry = docket in retry_dockets
                if is_retry:
                    # Get current retry count
                    conn = sqlite3.connect(DB_FILE)
                    cursor = conn.execute('SELECT retry_count FROM pacermon_searches WHERE docket_num = ?', (docket,))
                    result = cursor.fetchone()
                    current_retry_count = result[0] if result else 0
                    conn.close()
                    console.print(f"[yellow]Retrying {docket} (attempt #{current_retry_count + 1})[/yellow]")
                else:
                    current_retry_count = 0
                search_query = f"njd {docket}"
                
                # Handle JSON mode vs regular mode
                if args.from_json and docket_info_lookup and docket in docket_info_lookup:
                    # JSON mode: use direct hyperlink, skip Google API
                    console.print(f"[cyan]Using direct hyperlink for {docket} from JSON[/cyan]")
                    docket_info = docket_info_lookup[docket]
                    results = create_fake_search_results(docket_info)
                    error = None
                else:
                    # Regular mode: check cache then Google API
                    if has_search_results(docket) and not is_retry:
                        console.print(f"[blue]Using cached search results for {docket}[/blue]")
                        results = get_cached_search_results(docket)
                        error = None
                    else:
                        console.print(f"[cyan]Making Google API call for {docket}[/cyan]")
                        results, error = search_pacer_monitor(search_query)
                
                # Handle API errors (429, rate limits, etc.)
                if error:
                    console.print(f"[red]API error for {docket}: {error}[/red]")
                    save_search_result(
                        docket, search_query, None, 
                        status='api_error', 
                        retry_count=current_retry_count + 1, 
                        last_error=error, 
                        needs_retry=(current_retry_count + 1) < 3  # Stop retrying after 3 attempts
                    )
                    progress.advance(task)
                    continue
                
                # Process results and fetch HTML pages
                status = 'success' if results and 'items' in results else 'no_results'
                
                if results and 'items' in results:
                    # Display search results
                    table = Table(title=f"Search Results for {docket}")
                    table.add_column("Title", style="cyan")
                    table.add_column("Link", style="blue")
                    table.add_column("Snippet", style="white")
                    
                    for item in results['items']:
                        title = item.get('title', 'N/A')[:50] + "..." if len(item.get('title', '')) > 50 else item.get('title', 'N/A')
                        link = item.get('link', 'N/A')
                        snippet = item.get('snippet', 'N/A').replace(chr(10), ' ')[:100] + "..." if len(item.get('snippet', '')) > 100 else item.get('snippet', 'N/A')
                        table.add_row(title, link, snippet)
                    
                    console.print(table)
                    
                    # Fetch and parse the first PacerMonitor page
                    first_item = results['items'][0]
                    pacermon_url = first_item.get('link')
                    
                    if pacermon_url and 'pacermonitor.com' in pacermon_url:
                        console.print(f"[yellow]Fetching and parsing PacerMonitor page...[/yellow]")
                        case_data_result = await fetch_and_parse_page(pacermon_url, search_query, docket)
                        await parse_case_data(case_data_result, docket, search_query, results)
                    else:
                        console.print(f"[yellow]No valid PacerMonitor URL found for {docket}[/yellow]")
                        save_search_result(docket, search_query, results, status)
                else:
                    console.print(f"[red]No Johnson & Johnson results found for {docket}[/red]")
                    save_search_result(docket, search_query, results, status)
                
                progress.advance(task)
                
                # Add small delay between requests to be respectful
                if len(all_dockets_to_process) > 1:
                    await asyncio.sleep(random.uniform(2, 5))
        
        console.print(f"\\n[green]✓ Completed processing {len(new_dockets)} dockets[/green]")
        
        # Check for failed API calls that need retry
        failed_dockets = get_failed_dockets()
        if failed_dockets:
            console.print(f"[yellow]⚠ {len(failed_dockets)} dockets failed due to API errors and need retry[/yellow]")
            console.print(f"[yellow]Run the script again to retry failed searches[/yellow]")
            
            # Show sample of failed dockets
            for docket, retry_count, error in failed_dockets[:5]:
                console.print(f"[red]  - {docket}: {error} (retry #{retry_count})[/red]")
            if len(failed_dockets) > 5:
                console.print(f"[yellow]  ... and {len(failed_dockets) - 5} more[/yellow]")
        
        console.print(f"[blue]Results saved to: {DB_FILE}[/blue]")
        
    except ValueError as e:
        console.print(f"[red]Error: {e}[/red]")
    except KeyboardInterrupt:
        console.print(f"\\n[yellow]Search interrupted by user[/yellow]")
        console.print(f"[blue]Partial results saved to: {DB_FILE}[/blue]")

if __name__ == "__main__":
    asyncio.run(main())