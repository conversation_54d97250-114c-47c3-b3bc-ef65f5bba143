#!/usr/bin/env python3
"""
Standalone PacerMonitor HTML Parser for Testing

This script can independently parse PacerMonitor docket pages to extract structured data.
Useful for testing extraction logic before integrating into the main application.

Usage:
    python test_pacermon_parser.py <html_file_path>
    python test_pacermon_parser.py examples/pacermon_docket_page.html
"""

import sys
import json
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from rich.console import Console
from rich.table import Table
from rich import print as rprint

console = Console()

class PacerMonitorParser:
    """Parses PacerMonitor docket pages to extract structured case data."""
    
    def __init__(self):
        self.console = Console()
        
    def standardize_case_title(self, title: str) -> str:
        """Standardize case title formatting - ensure 'et al' becomes 'et al.'"""
        if not title:
            return title
            
        # Replace various forms of "et al" with standardized "et al."
        # Handle: "et al", "et al.", "Et Al", "ET AL", "et. al", etc.
        title = re.sub(r'\bet\s*\.?\s*al\.?(?!\w)', 'et al.', title, flags=re.IGNORECASE)
        
        return title
    
    def parse_html_file(self, file_path: str) -> Dict[str, Any]:
        """Parse a PacerMonitor HTML file and extract case information."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            return self.parse_html_content(html_content)
        except Exception as e:
            self.console.print(f"[red]Error reading file {file_path}: {e}[/red]")
            return {}
    
    def parse_html_content(self, html_content: str) -> Dict[str, Any]:
        """Parse HTML content and extract case information."""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        case_data = {
            'assigned_to': None,
            'referred_to': None,
            'docket_num': None,
            'nos': None,
            'cause': None,
            'filing_date': None,
            'defendant': [],
            'plaintiff': [],
            'attorney': [],
            'versus': None
        }
        
        try:
            # Extract case title (versus)
            versus = self._extract_versus(soup)
            case_data['versus'] = self.standardize_case_title(versus) if versus else None
            
            # Extract basic case information from the table
            case_data.update(self._extract_case_info_table(soup))
            
            # Extract parties information
            parties_data = self._extract_parties_info(soup)
            case_data.update(parties_data)
            
            # Convert filing date to YYYYMMDD format
            if case_data['filing_date']:
                case_data['filing_date'] = self._convert_date_format(case_data['filing_date'])
            
        except Exception as e:
            self.console.print(f"[red]Error parsing HTML content: {e}[/red]")
        
        return case_data
    
    def _extract_versus(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract the case title (versus field)."""
        # Try to get from page header
        header = soup.find('h1')
        if header:
            return header.get_text().strip()
        
        # Try to get from title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Extract case name from title (before the parentheses)
            match = re.match(r'^([^(]+)', title_text)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _extract_case_info_table(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract case information from the main info table."""
        info = {}
        
        # Find all table rows with case information
        for row in soup.find_all('tr'):
            cells = row.find_all('td')
            if len(cells) >= 2:
                label = cells[0].get_text().strip().rstrip(':')
                value = cells[1].get_text().strip()
                
                if 'Judge' in label:
                    info['assigned_to'] = value
                elif 'Referred' in label:
                    info['referred_to'] = value
                elif 'Case #' in label:
                    info['docket_num'] = value
                elif 'Nature of Suit' in label:
                    info['nos'] = value
                elif 'Cause' in label:
                    info['cause'] = value
                elif 'Case Filed' in label:
                    info['filing_date'] = value
        
        return info
    
    def _extract_parties_info(self, soup: BeautifulSoup) -> Dict[str, List[Any]]:
        """Extract parties and attorney information."""
        parties_data = {
            'defendant': [],
            'plaintiff': [],
            'attorney': []
        }
        
        # Find the parties tab content
        parties_tab = soup.find('div', {'id': 'parties'})
        if not parties_tab:
            return parties_data
        
        # Find all party rows in the parties table
        for row in parties_tab.find_all('tr'):
            cells = row.find_all('td')
            if len(cells) >= 1:
                cell_content = cells[0]
                
                # Look for party type indicator
                party_type_div = cell_content.find('div', style='font-weight:bold;')
                if party_type_div:
                    party_type = party_type_div.get_text().strip().lower()
                    
                    # Get party name (next div after party type)
                    party_name_div = party_type_div.find_next_sibling('div')
                    if party_name_div:
                        party_name = party_name_div.get_text().strip()
                        
                        if 'defendant' in party_type:
                            parties_data['defendant'].append(party_name)
                        elif 'plaintiff' in party_type:
                            parties_data['plaintiff'].append(party_name)
                
                # Extract attorney information if present
                if len(cells) >= 2:
                    attorney_cell = cells[1]
                    attorney_info = self._extract_attorney_info(attorney_cell)
                    if attorney_info:
                        parties_data['attorney'].append(attorney_info)
        
        return parties_data
    
    def _extract_attorney_info(self, attorney_cell) -> Optional[Dict[str, str]]:
        """Extract attorney name and law firm from attorney cell - all 'Represented By' attorneys."""
        try:
            # Get all text to check for attorney information
            cell_text = attorney_cell.get_text()
            
            # Look for "Represented By" sections only
            if "Represented By" not in cell_text and "represented by" not in cell_text.lower():
                return None
            
            # Look for attorney name (usually in a span)
            name_span = attorney_cell.find('span')
            if name_span:
                attorney_name = name_span.get_text().strip()
                
                # Skip if the span contains status info rather than name
                if "Counsel Not Admitted" in attorney_name or "USDC-NJ Bar" in attorney_name:
                    # Look for the actual attorney name in previous span or text
                    spans = attorney_cell.find_all('span')
                    for span in spans:
                        span_text = span.get_text().strip()
                        if (span_text and 
                            "Counsel Not Admitted" not in span_text and 
                            "USDC-NJ Bar" not in span_text and
                            "contact info" not in span_text.lower()):
                            attorney_name = span_text
                            break
                
                # If we still don't have a valid name, skip this entry
                if not attorney_name or "Counsel Not Admitted" in attorney_name:
                    return None
                
                # Look for law firm (usually in italic span after br)
                law_firm_span = attorney_cell.find('span', style=lambda x: x and 'italic' in x)
                law_firm = law_firm_span.get_text().strip() if law_firm_span else "Unknown"
                
                # If the law firm is status information, mark as Unknown
                if "Counsel Not Admitted" in law_firm or "USDC-NJ Bar" in law_firm:
                    law_firm = "Unknown"
                
                return {
                    'attorney_name': attorney_name,
                    'law_firm': law_firm
                }
        except Exception as e:
            # Better error handling
            self.console.print(f"[yellow]Warning: Error extracting attorney info: {e}[/yellow]")
        
        return None
    
    def _convert_date_format(self, date_string: str) -> str:
        """Convert date from 'Jun 12, 2025' format to 'YYYYMMDD' format."""
        try:
            # Parse the date string
            date_obj = datetime.strptime(date_string, '%b %d, %Y')
            return date_obj.strftime('%Y%m%d')
        except ValueError:
            try:
                # Try alternative format
                date_obj = datetime.strptime(date_string, '%B %d, %Y')
                return date_obj.strftime('%Y%m%d')
            except ValueError:
                console.print(f"[yellow]Warning: Could not parse date '{date_string}'[/yellow]")
                return date_string

def display_results(case_data: Dict[str, Any]) -> None:
    """Display extracted case data in a formatted table."""
    table = Table(title="Extracted Case Information")
    table.add_column("Field", style="cyan")
    table.add_column("Value", style="white")
    
    # Display basic fields
    basic_fields = ['versus', 'docket_num', 'assigned_to', 'referred_to', 'nos', 'cause', 'filing_date']
    for field in basic_fields:
        value = case_data.get(field, 'N/A')
        table.add_row(field.replace('_', ' ').title(), str(value))
    
    # Display lists
    for field in ['defendant', 'plaintiff']:
        values = case_data.get(field, [])
        if values:
            table.add_row(field.title(), ', '.join(values))
        else:
            table.add_row(field.title(), 'N/A')
    
    # Display attorneys
    attorneys = case_data.get('attorney', [])
    if attorneys:
        attorney_strs = []
        for att in attorneys:
            attorney_strs.append(f"{att.get('attorney_name', 'N/A')} ({att.get('law_firm', 'N/A')})")
        table.add_row('Attorneys', '\n'.join(attorney_strs))
    else:
        table.add_row('Attorneys', 'N/A')
    
    console.print(table)

def main():
    """Main function to run the parser."""
    if len(sys.argv) != 2:
        console.print("[red]Usage: python test_pacermon_parser.py <html_file_path>[/red]")
        console.print("[yellow]Example: python test_pacermon_parser.py examples/pacermon_docket_page.html[/yellow]")
        sys.exit(1)
    
    html_file = sys.argv[1]
    
    if not Path(html_file).exists():
        console.print(f"[red]Error: File '{html_file}' not found[/red]")
        sys.exit(1)
    
    console.print(f"[blue]Parsing PacerMonitor HTML file: {html_file}[/blue]\n")
    
    parser = PacerMonitorParser()
    case_data = parser.parse_html_file(html_file)
    
    if case_data:
        display_results(case_data)
        
        # Also output as JSON for programmatic use
        console.print(f"\n[green]JSON Output:[/green]")
        rprint(json.dumps(case_data, indent=2))
    else:
        console.print("[red]No data extracted from HTML file[/red]")

if __name__ == "__main__":
    main()