#!/usr/bin/env python3
"""
Manual TALC database updates script.

This script performs three tasks:
1. Add law firm names to specific dockets
2. Delete specific dockets from the database
3. Reprocess specific dockets using html_processing_service.py

Usage:
    python talc_manual_updates.py [--dry-run] [--verbose]
"""

import sqlite3
import json
import argparse
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich import print as rprint

# Import the HTML processing service
import sys
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.containers.core import create_container
from src.services.pacer.html_processing_service import PacerHTMLProcessingService
from src.services.html import DataUpdaterService

console = Console()

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
DB_DIR = PROJECT_ROOT / "sqlite"
DB_FILE = DB_DIR / "pacermon_cache.db"
HTML_DIR = PROJECT_ROOT / "pacermon_html"

# Manual update data
LAW_FIRM_UPDATES = {
    "3:25-cv-11921": "Ferrell Law Group",
    "3:25-cv-13290": "Pulaski Kherker", 
    "3:25-cv-12616": "Johnson Law Group",
    "3:25-cv-12908": "Slater Slater Schulman LLP"
}

DOCKETS_TO_DELETE = [
    "3:25-cv-12560",
    "3:25-cv-12472", 
    "3:25-cv-13290",
    "3:25-cv-12879",
    "3:25-cv-12995"
]

DOCKETS_TO_REPROCESS = [
    "3:25-cv-11921",
    "3:25-cv-12616", 
    "3:25-cv-12908",
    "3:25-cv-12717",
    "3:25-cv-12732",
    "3:25-cv-12748",
    "3:25-cv-12768",
    "3:25-cv-12771",
    "3:25-cv-12786",
    "3:25-cv-12795",
    "3:25-cv-12805",
    "3:25-cv-12817",
    "3:25-cv-12821",
    "3:25-cv-12830",
    "3:25-cv-12842",
    "3:25-cv-12859",
    "3:25-cv-12861",
    "3:25-cv-12866",
    "3:25-cv-12883",
    "3:25-cv-12886"
]

class TALCManualUpdater:
    """Handles manual updates to TALC database records."""
    
    def __init__(self, dry_run: bool = False, verbose: bool = False):
        self.dry_run = dry_run
        self.verbose = verbose
        self.console = console
        self.stats = {
            'law_firms_updated': 0,
            'dockets_deleted': 0,
            'dockets_reprocessed': 0,
            'reprocess_errors': 0,
            'html_files_found': 0,
            'html_files_missing': 0
        }
        
    def docket_to_filename(self, docket_num: str) -> str:
        """Convert docket number to HTML filename format."""
        return docket_num.replace(':', '_').replace('-', '_') + '.html'
    
    def get_record_by_docket(self, docket_num: str) -> Optional[Dict[str, Any]]:
        """Get a record from SQLite by docket number."""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT rowid, docket_num, versus, filing_date, attorney, 
                       plaintiff, defendant, cause, nos, html_file_path
                FROM pacermon_searches 
                WHERE docket_num = ?
            """, (docket_num,))
            
            record = cursor.fetchone()
            conn.close()
            
            if record:
                return {
                    'rowid': record[0],
                    'docket_num': record[1],
                    'versus': record[2],
                    'filing_date': record[3],
                    'attorney': record[4],
                    'court_id': 'njd',  # Always njd for TALC cases
                    'base_filename': record[1].replace(':', '_').replace('-', '_'),  # Generate from docket_num
                    's3_html': None,  # Not in schema
                    'plaintiff': record[5],
                    'defendant': record[6],
                    'cause': record[7],
                    'nos': record[8],
                    'html_file_path': record[9]
                }
            return None
            
        except Exception as e:
            self.console.print(f"[red]Error querying record for {docket_num}: {e}[/red]")
            return None
    
    def update_law_firm_for_docket(self, docket_num: str, law_firm: str) -> bool:
        """Update the law firm for a specific docket."""
        try:
            record = self.get_record_by_docket(docket_num)
            if not record:
                self.console.print(f"[red]❌ Record not found for docket {docket_num}[/red]")
                return False
            
            # Parse existing attorney data
            attorney_data = []
            if record['attorney']:
                try:
                    attorney_data = json.loads(record['attorney'])
                except json.JSONDecodeError:
                    attorney_data = []
            
            # Update law firm for all attorneys
            updated_attorneys = []
            for attorney in attorney_data:
                if isinstance(attorney, dict):
                    attorney['law_firm'] = law_firm
                    updated_attorneys.append(attorney)
            
            # If no attorneys, create a placeholder entry
            if not updated_attorneys:
                updated_attorneys = [{"attorney_name": "Unknown", "law_firm": law_firm}]
            
            if self.dry_run:
                self.console.print(f"[yellow]DRY RUN: Would update {docket_num} with law firm: {law_firm}[/yellow]")
                return True
            
            # Update the database
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE pacermon_searches 
                SET attorney = ?
                WHERE docket_num = ?
            """, (json.dumps(updated_attorneys), docket_num))
            
            conn.commit()
            conn.close()
            
            self.stats['law_firms_updated'] += 1
            self.console.print(f"[green]✅ Updated {docket_num} with law firm: {law_firm}[/green]")
            return True
            
        except Exception as e:
            self.console.print(f"[red]Error updating law firm for {docket_num}: {e}[/red]")
            return False
    
    def delete_docket(self, docket_num: str) -> bool:
        """Delete a specific docket from the database."""
        try:
            record = self.get_record_by_docket(docket_num)
            if not record:
                self.console.print(f"[yellow]⚠️  Record not found for docket {docket_num} (already deleted?)[/yellow]")
                return True
            
            if self.dry_run:
                self.console.print(f"[yellow]DRY RUN: Would delete {docket_num}[/yellow]")
                return True
            
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM pacermon_searches WHERE docket_num = ?", (docket_num,))
            
            conn.commit()
            conn.close()
            
            self.stats['dockets_deleted'] += 1
            self.console.print(f"[green]✅ Deleted {docket_num}[/green]")
            return True
            
        except Exception as e:
            self.console.print(f"[red]Error deleting {docket_num}: {e}[/red]")
            return False
    
    async def reprocess_docket_with_html_service(self, docket_num: str) -> bool:
        """Reprocess a docket using the HTML processing service."""
        try:
            # Get the record
            record = self.get_record_by_docket(docket_num)
            if not record:
                self.console.print(f"[red]❌ Record not found for docket {docket_num}[/red]")
                return False
            
            # Check if HTML file exists
            filename = self.docket_to_filename(docket_num)
            html_file = HTML_DIR / filename
            
            if not html_file.exists():
                self.console.print(f"[red]❌ HTML file not found for {docket_num}: {filename}[/red]")
                self.stats['html_files_missing'] += 1
                return False
            
            self.stats['html_files_found'] += 1
            
            # Read HTML content
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            if self.dry_run:
                self.console.print(f"[yellow]DRY RUN: Would reprocess {docket_num} with HTML service[/yellow]")
                return True
            
            # Create the HTML processing service with dependency injection
            container = create_container({})
            
            # Create the service with proper dependencies
            html_service = PacerHTMLProcessingService(
                logger=container.logger(),
                config={'iso_date': '20250701'},  # Use appropriate date
                court_id=record.get('court_id', 'njd'),
                html_data_updater=None,  # Will be set if needed
                s3_async_storage=None   # Will be set if needed
            )
            
            # Prepare case details from record
            case_details = {
                'docket_num': record['docket_num'],
                'versus': record['versus'],
                'filing_date': record['filing_date'],
                'court_id': record.get('court_id', 'njd'),
                'base_filename': record.get('base_filename', ''),
                'plaintiff': json.loads(record['plaintiff']) if record['plaintiff'] else [],
                'defendant': json.loads(record['defendant']) if record['defendant'] else [],
                'cause': record['cause'],
                'nos': record['nos']
            }
            
            # Process the HTML content
            updated_case_details = await html_service.process_html_content(
                case_details, 
                html_content,
                None  # No json_path needed for this operation
            )
            
            # Update the database with the new information
            if updated_case_details.get('attorney'):
                await self.update_record_with_processed_data(record['rowid'], updated_case_details)
                self.stats['dockets_reprocessed'] += 1
                self.console.print(f"[green]✅ Reprocessed {docket_num} with HTML service[/green]")
                return True
            else:
                self.console.print(f"[yellow]⚠️  No attorney data found during reprocessing of {docket_num}[/yellow]")
                return False
            
        except Exception as e:
            self.console.print(f"[red]Error reprocessing {docket_num}: {e}[/red]")
            self.stats['reprocess_errors'] += 1
            return False
    
    async def update_record_with_processed_data(self, rowid: int, processed_data: Dict[str, Any]) -> bool:
        """Update a record with processed data from HTML service."""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            # Prepare update fields
            update_fields = []
            update_values = []
            
            if processed_data.get('attorney'):
                update_fields.append("attorney = ?")
                update_values.append(json.dumps(processed_data['attorney']))
            
            if processed_data.get('plaintiff'):
                update_fields.append("plaintiff = ?")
                update_values.append(json.dumps(processed_data['plaintiff']))
            
            if processed_data.get('defendant'):
                update_fields.append("defendant = ?")
                update_values.append(json.dumps(processed_data['defendant']))
            
            # Note: s3_html is not in the schema, so we skip it
            
            if update_fields:
                update_values.append(rowid)
                query = f"UPDATE pacermon_searches SET {', '.join(update_fields)} WHERE rowid = ?"
                cursor.execute(query, update_values)
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            self.console.print(f"[red]Error updating record {rowid}: {e}[/red]")
            return False
    
    def show_preview_table(self, title: str, data: List[tuple], headers: List[str]) -> None:
        """Show a preview table of data."""
        if not data:
            self.console.print(f"[yellow]No data for {title}[/yellow]")
            return
        
        self.console.print(f"\n[bold cyan]{title}[/bold cyan]")
        
        table = Table(show_header=True, header_style="bold blue")
        for header in headers:
            table.add_column(header)
        
        for row in data:
            table.add_row(*[str(item) for item in row])
        
        self.console.print(table)
    
    async def run_all_updates(self) -> None:
        """Run all manual updates."""
        self.console.print("[bold cyan]🔧 Starting TALC Manual Updates[/bold cyan]")
        
        if self.dry_run:
            self.console.print("[yellow]DRY RUN MODE - No changes will be made[/yellow]")
        
        # Show preview of what will be done
        self.show_preview_table(
            "📝 Law Firm Updates", 
            [(docket, firm) for docket, firm in LAW_FIRM_UPDATES.items()],
            ["Docket", "Law Firm"]
        )
        
        self.show_preview_table(
            "🗑️  Dockets to Delete",
            [(docket,) for docket in DOCKETS_TO_DELETE],
            ["Docket"]
        )
        
        self.show_preview_table(
            "🔄 Dockets to Reprocess",
            [(docket,) for docket in DOCKETS_TO_REPROCESS],
            ["Docket"]
        )
        
        # Task 1: Update law firms
        self.console.print("\n[bold cyan]📝 Task 1: Updating Law Firms[/bold cyan]")
        for docket, law_firm in LAW_FIRM_UPDATES.items():
            self.update_law_firm_for_docket(docket, law_firm)
        
        # Task 2: Delete dockets
        self.console.print("\n[bold cyan]🗑️  Task 2: Deleting Dockets[/bold cyan]")
        for docket in DOCKETS_TO_DELETE:
            self.delete_docket(docket)
        
        # Task 3: Reprocess dockets
        self.console.print("\n[bold cyan]🔄 Task 3: Reprocessing Dockets with HTML Service[/bold cyan]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            
            task = progress.add_task(
                "[cyan]Reprocessing dockets...", 
                total=len(DOCKETS_TO_REPROCESS)
            )
            
            for docket in DOCKETS_TO_REPROCESS:
                await self.reprocess_docket_with_html_service(docket)
                progress.update(task, advance=1)
        
        # Show final results
        self.show_results()
    
    def show_results(self) -> None:
        """Display final processing results."""
        self.console.print(f"\n[bold cyan]📊 PROCESSING RESULTS[/bold cyan]")
        
        results_table = Table(show_header=True, header_style="bold blue")
        results_table.add_column("Operation", style="cyan")
        results_table.add_column("Count", style="white")
        
        results_table.add_row("Law firms updated", str(self.stats['law_firms_updated']))
        results_table.add_row("Dockets deleted", str(self.stats['dockets_deleted']))
        results_table.add_row("Dockets reprocessed", str(self.stats['dockets_reprocessed']))
        results_table.add_row("Reprocessing errors", str(self.stats['reprocess_errors']))
        results_table.add_row("HTML files found", str(self.stats['html_files_found']))
        results_table.add_row("HTML files missing", str(self.stats['html_files_missing']))
        
        self.console.print(results_table)
        
        # Summary
        total_operations = (self.stats['law_firms_updated'] + 
                          self.stats['dockets_deleted'] + 
                          self.stats['dockets_reprocessed'])
        
        if total_operations > 0:
            self.console.print(f"\n[bold green]✅ SUCCESS: Completed {total_operations} operations![/bold green]")
        
        if self.stats['reprocess_errors'] > 0:
            self.console.print(f"[yellow]⚠️  {self.stats['reprocess_errors']} reprocessing errors occurred[/yellow]")
        
        if self.stats['html_files_missing'] > 0:
            self.console.print(f"[yellow]⚠️  {self.stats['html_files_missing']} HTML files were missing[/yellow]")

async def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(
        description="Perform manual TALC database updates"
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true', 
        help='Preview changes without updating database'
    )
    parser.add_argument(
        '--verbose', 
        action='store_true', 
        help='Show detailed processing information'
    )
    
    args = parser.parse_args()
    
    # Create and run the updater
    updater = TALCManualUpdater(
        dry_run=args.dry_run,
        verbose=args.verbose
    )
    
    try:
        await updater.run_all_updates()
    except KeyboardInterrupt:
        console.print("\n[red]❌ Operation cancelled by user[/red]")
    except Exception as e:
        console.print(f"\n[red]❌ Unexpected error: {e}[/red]")
        if args.verbose:
            import traceback
            console.print(f"[red]{traceback.format_exc()}[/red]")

if __name__ == "__main__":
    asyncio.run(main())