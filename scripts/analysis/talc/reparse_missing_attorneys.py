#!/usr/bin/env python3
"""
One-time repair script to fix records with missing attorney data.

This script:
1. Finds records in SQLite with attorney IS NULL or attorney = ''
2. Converts docket numbers to HTML filenames (3:25-cv-13219 -> 3_25-cv-13219.html)
3. Re-parses HTML files using the corrected parsing logic
4. Updates SQLite with the found attorney data

Usage:
    python reparse_missing_attorneys.py [--dry-run] [--verbose]
"""

import sqlite3
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich import print as rprint

# Import the fixed parser
from test_pacermon_parser import PacerMonitorParser

console = Console()

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
DB_DIR = PROJECT_ROOT / "sqlite"
DB_FILE = DB_DIR / "pacermon_cache.db"
HTML_DIR = PROJECT_ROOT / "pacermon_html"

class MissingAttorneyReparser:
    """Repairs records with missing attorney data by re-parsing HTML files."""
    
    def __init__(self, dry_run: bool = False, verbose: bool = False):
        self.dry_run = dry_run
        self.verbose = verbose
        self.console = console
        self.parser = PacerMonitorParser()
        self.stats = {
            'total_missing': 0,
            'html_found': 0,
            'html_missing': 0,
            'attorney_extracted': 0,
            'no_attorney_found': 0,
            'parse_errors': 0,
            'updated_records': 0
        }
    
    def docket_to_filename(self, docket_num: str) -> str:
        """Convert docket number to HTML filename format.
        
        Examples:
            3:25-cv-13219 -> 3_25_cv_13219.html
            1:24-cv-01234 -> 1_24_cv_01234.html
        """
        # Replace colon and hyphens with underscores
        filename = docket_num.replace(':', '_').replace('-', '_') + '.html'
        return filename
    
    def get_missing_attorney_records(self) -> List[Dict[str, Any]]:
        """Get all records from SQLite that have missing attorney data."""
        try:
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            # Query for records with missing attorney data
            query = """
                SELECT rowid, docket_num, versus, filing_date, attorney
                FROM pacermon_searches 
                WHERE attorney IS NULL OR attorney = '' OR attorney = '[]'
                ORDER BY filing_date DESC, docket_num
            """
            
            cursor.execute(query)
            records = cursor.fetchall()
            
            # Convert to dict format
            missing_records = []
            for record in records:
                missing_records.append({
                    'rowid': record[0],
                    'docket_num': record[1],
                    'versus': record[2],
                    'filing_date': record[3],
                    'attorney': record[4]
                })
            
            conn.close()
            return missing_records
            
        except Exception as e:
            self.console.print(f"[red]Error querying database: {e}[/red]")
            return []
    
    def parse_html_file(self, html_file: Path) -> Optional[Dict[str, Any]]:
        """Parse an HTML file and extract attorney information."""
        try:
            if not html_file.exists():
                return None
            
            # Parse the HTML file
            case_data = self.parser.parse_html_file(str(html_file))
            
            if self.verbose:
                self.console.print(f"[blue]Parsed {html_file.name}: {len(case_data.get('attorney', []))} attorneys found[/blue]")
            
            return case_data
            
        except Exception as e:
            self.console.print(f"[red]Error parsing {html_file}: {e}[/red]")
            self.stats['parse_errors'] += 1
            return None
    
    def update_record_attorney(self, rowid: int, attorney_data: List[Dict[str, Any]]) -> bool:
        """Update a record's attorney data in SQLite."""
        try:
            if self.dry_run:
                self.console.print(f"[yellow]DRY RUN: Would update rowid {rowid} with attorney data[/yellow]")
                return True
            
            conn = sqlite3.connect(DB_FILE)
            cursor = conn.cursor()
            
            # Convert attorney data to JSON string
            attorney_json = json.dumps(attorney_data) if attorney_data else None
            
            # Update the record
            cursor.execute("""
                UPDATE pacermon_searches 
                SET attorney = ?
                WHERE rowid = ?
            """, (attorney_json, rowid))
            
            conn.commit()
            conn.close()
            
            self.stats['updated_records'] += 1
            return True
            
        except Exception as e:
            self.console.print(f"[red]Error updating record {rowid}: {e}[/red]")
            return False
    
    def process_missing_records(self) -> None:
        """Main processing function to repair missing attorney data."""
        self.console.print("[bold cyan]🔍 Finding records with missing attorney data...[/bold cyan]")
        
        # Get records with missing attorney data
        missing_records = self.get_missing_attorney_records()
        self.stats['total_missing'] = len(missing_records)
        
        if not missing_records:
            self.console.print("[green]✅ No records found with missing attorney data![/green]")
            return
        
        self.console.print(f"[yellow]Found {len(missing_records)} records with missing attorney data[/yellow]")
        
        # Show preview of records to be processed
        self.preview_missing_records(missing_records)
        
        if self.dry_run:
            self.console.print("\n[yellow]DRY RUN MODE - No changes will be made to database[/yellow]")
        
        # Process each record
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            
            task = progress.add_task(
                "[cyan]Re-parsing HTML files...", 
                total=len(missing_records)
            )
            
            for record in missing_records:
                docket_num = record['docket_num']
                rowid = record['rowid']
                
                # Convert docket number to filename
                filename = self.docket_to_filename(docket_num)
                html_file = HTML_DIR / filename
                
                if html_file.exists():
                    self.stats['html_found'] += 1
                    
                    # Parse the HTML file
                    case_data = self.parse_html_file(html_file)
                    
                    if case_data and case_data.get('attorney'):
                        # Found attorney data
                        attorney_data = case_data['attorney']
                        self.stats['attorney_extracted'] += 1
                        
                        if self.verbose:
                            self.console.print(f"[green]✅ {docket_num}: Found {len(attorney_data)} attorneys[/green]")
                        
                        # Update the record
                        self.update_record_attorney(rowid, attorney_data)
                        
                    else:
                        # No attorney data found in HTML
                        self.stats['no_attorney_found'] += 1
                        if self.verbose:
                            self.console.print(f"[yellow]⚠️  {docket_num}: No attorney data in HTML[/yellow]")
                else:
                    # HTML file doesn't exist
                    self.stats['html_missing'] += 1
                    if self.verbose:
                        self.console.print(f"[red]❌ {docket_num}: HTML file not found ({filename})[/red]")
                
                progress.update(task, advance=1)
        
        # Show final results
        self.show_results()
    
    def preview_missing_records(self, records: List[Dict[str, Any]]) -> None:
        """Show a preview of records that will be processed."""
        self.console.print("\n[bold cyan]📋 Records to be processed:[/bold cyan]")
        
        table = Table(show_header=True, header_style="bold blue")
        table.add_column("Filing Date", style="yellow")
        table.add_column("Docket", style="cyan")
        table.add_column("Versus", style="white")
        table.add_column("HTML File", style="green")
        
        # Show first 10 records
        for record in records[:10]:
            docket_num = record['docket_num']
            filename = self.docket_to_filename(docket_num)
            html_file = HTML_DIR / filename
            
            file_status = "✅ Found" if html_file.exists() else "❌ Missing"
            versus = record['versus'] or 'N/A'
            
            table.add_row(
                record['filing_date'] or 'N/A',
                docket_num,
                versus[:50] + "..." if len(versus) > 50 else versus,
                file_status
            )
        
        if len(records) > 10:
            table.add_row("...", "...", "...", f"... and {len(records) - 10} more")
        
        self.console.print(table)
    
    def show_results(self) -> None:
        """Display final processing results."""
        self.console.print(f"\n[bold cyan]📊 PROCESSING RESULTS[/bold cyan]")
        
        results_table = Table(show_header=True, header_style="bold blue")
        results_table.add_column("Metric", style="cyan")
        results_table.add_column("Count", style="white")
        
        results_table.add_row("Total records with missing attorney data", str(self.stats['total_missing']))
        results_table.add_row("HTML files found", str(self.stats['html_found']))
        results_table.add_row("HTML files missing", str(self.stats['html_missing']))
        results_table.add_row("Records with attorney data extracted", str(self.stats['attorney_extracted']))
        results_table.add_row("Records with no attorney data in HTML", str(self.stats['no_attorney_found']))
        results_table.add_row("Parse errors", str(self.stats['parse_errors']))
        results_table.add_row("Database records updated", str(self.stats['updated_records']))
        
        self.console.print(results_table)
        
        # Summary
        if self.stats['attorney_extracted'] > 0:
            self.console.print(f"\n[bold green]✅ SUCCESS: Fixed {self.stats['attorney_extracted']} records with missing attorney data![/bold green]")
        
        if self.stats['html_missing'] > 0:
            self.console.print(f"[yellow]⚠️  {self.stats['html_missing']} records have no corresponding HTML files[/yellow]")
        
        if self.stats['no_attorney_found'] > 0:
            self.console.print(f"[yellow]⚠️  {self.stats['no_attorney_found']} HTML files contain no attorney data[/yellow]")

def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(
        description="Repair records with missing attorney data by re-parsing HTML files"
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true', 
        help='Preview changes without updating database'
    )
    parser.add_argument(
        '--verbose', 
        action='store_true', 
        help='Show detailed processing information'
    )
    
    args = parser.parse_args()
    
    # Create and run the reparser
    reparser = MissingAttorneyReparser(
        dry_run=args.dry_run,
        verbose=args.verbose
    )
    
    try:
        reparser.process_missing_records()
    except KeyboardInterrupt:
        console.print("\n[red]❌ Operation cancelled by user[/red]")
    except Exception as e:
        console.print(f"\n[red]❌ Unexpected error: {e}[/red]")

if __name__ == "__main__":
    main()