#!/usr/bin/env python3
"""
Playwright Client with Stealth Features for PacerMonitor

This module provides a Playwright-based client with advanced stealth features
to avoid detection when fetching PacerMonitor pages.
"""

import asyncio
import random
import urllib.parse
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from rich.console import Console
from oxylabs_proxy_helper import OxylabsProxyHelper

console = Console()

@dataclass
class BrowserProfile:
    """Fake browser profile configuration."""
    user_agent: str
    viewport: Dict[str, int]
    extra_http_headers: Dict[str, str]
    timezone: str
    locale: str
    browser_type: str  # 'chromium', 'firefox', 'webkit'

class BrowserProfileGenerator:
    """Generates fake browser profiles for stealth browsing."""
    
    CHROME_PROFILES = [
        {
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'headers': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
            }
        },
        {
            'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'headers': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"macOS"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
            }
        }
    ]
    
    FIREFOX_PROFILES = [
        {
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'headers': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-User': '?1',
            }
        },
        {
            'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
            'headers': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-User': '?1',
            }
        }
    ]
    
    SAFARI_PROFILES = [
        {
            'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'headers': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Upgrade-Insecure-Requests': '1',
            }
        }
    ]
    
    TIMEZONES = [
        'America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles',
        'America/Phoenix', 'America/Anchorage', 'Pacific/Honolulu'
    ]
    
    LOCALES = ['en-US', 'en-GB', 'en-CA']
    
    @classmethod
    def generate_random_profile(cls) -> BrowserProfile:
        """Generate a random browser profile."""
        # Choose browser type
        browser_profiles = {
            'chromium': cls.CHROME_PROFILES,
            'firefox': cls.FIREFOX_PROFILES,
            'webkit': cls.SAFARI_PROFILES
        }
        
        browser_type = random.choice(list(browser_profiles.keys()))
        profile_data = random.choice(browser_profiles[browser_type])
        
        # Generate random viewport
        viewport = {
            'width': random.randint(1200, 1920),
            'height': random.randint(800, 1080)
        }
        
        # Random timezone and locale
        timezone = random.choice(cls.TIMEZONES)
        locale = random.choice(cls.LOCALES)
        
        return BrowserProfile(
            user_agent=profile_data['user_agent'],
            viewport=viewport,
            extra_http_headers=profile_data['headers'],
            timezone=timezone,
            locale=locale,
            browser_type=browser_type
        )

class PacerMonitorPlaywrightClient:
    """Playwright client with stealth features for PacerMonitor pages."""
    
    STEALTH_SCRIPTS = [
        # Hide webdriver property
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
        
        # Override plugins array
        "Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})",
        
        # Override languages
        "Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})",
        
        # Override platform
        "Object.defineProperty(navigator, 'platform', {get: () => 'Win32'})",
        
        # Override hardwareConcurrency
        f"Object.defineProperty(navigator, 'hardwareConcurrency', {{get: () => {random.randint(4, 16)}}})",
        
        # Override deviceMemory
        f"Object.defineProperty(navigator, 'deviceMemory', {{get: () => {random.choice([4, 8, 16])}}})",
        
        # Override screen properties
        f"""
        Object.defineProperty(screen, 'width', {{get: () => {random.randint(1920, 2560)}}});
        Object.defineProperty(screen, 'height', {{get: () => {random.randint(1080, 1440)}}});
        Object.defineProperty(screen, 'availWidth', {{get: () => screen.width}});
        Object.defineProperty(screen, 'availHeight', {{get: () => screen.height - 40}});
        """,
        
        # Override chrome property
        "window.chrome = { runtime: {}, loadTimes: function() {}, csi: function() {} }",
        
        # Override notification permission
        "Object.defineProperty(Notification, 'permission', {get: () => 'default'})",
    ]
    
    def __init__(self, headless: bool = True, proxy_config: Optional[str] = None, 
                 use_oxylabs: bool = True, mobile_proxy: bool = False, num_proxies: int = 10000):
        """
        Initialize the Playwright client.
        
        Args:
            headless: Whether to run browser in headless mode
            proxy_config: Single proxy configuration string (e.g., "http://proxy:port")
            use_oxylabs: Whether to use Oxylabs proxy rotation (overrides proxy_config)
            mobile_proxy: True for mobile proxies, False for residential
            num_proxies: Number of Oxylabs proxies to generate
        """
        self.headless = headless
        self.proxy_config = proxy_config
        self.use_oxylabs = use_oxylabs
        self.mobile_proxy = mobile_proxy
        self.playwright = None
        self.browser = None
        self.context = None
        self.current_profile = None
        
        # Oxylabs proxy management
        self.oxylabs_proxies = []
        self.current_proxy_index = 0
        self.failed_proxies = set()
        
        # Initialize Oxylabs proxies if enabled
        if self.use_oxylabs:
            self._initialize_oxylabs_proxies(num_proxies)
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    def _initialize_oxylabs_proxies(self, num_proxies: int):
        """Initialize Oxylabs proxy list."""
        try:
            helper = OxylabsProxyHelper(mobile_proxy=self.mobile_proxy)
            self.oxylabs_proxies = helper.generate_proxy_list(num_proxies)
            
            if self.oxylabs_proxies:
                proxy_type = "mobile" if self.mobile_proxy else "residential"
                console.print(f"[green]Initialized {len(self.oxylabs_proxies)} Oxylabs {proxy_type} proxies[/green]")
            else:
                console.print(f"[red]Failed to initialize Oxylabs proxies - check credentials[/red]")
                self.use_oxylabs = False
                
        except Exception as e:
            console.print(f"[red]Error initializing Oxylabs proxies: {e}[/red]")
            self.use_oxylabs = False
    
    def _get_current_proxy(self) -> Optional[str]:
        """Get current proxy URL based on configuration."""
        if self.use_oxylabs and self.oxylabs_proxies:
            # Use Oxylabs proxy rotation
            available_proxies = [i for i in range(len(self.oxylabs_proxies)) 
                               if i not in self.failed_proxies]
            
            if not available_proxies:
                # Reset failed proxies if all are failed
                console.print("[yellow]All Oxylabs proxies failed, resetting failed list[/yellow]")
                self.failed_proxies.clear()
                available_proxies = list(range(len(self.oxylabs_proxies)))
            
            if available_proxies:
                self.current_proxy_index = random.choice(available_proxies)
                proxy_url = self.oxylabs_proxies[self.current_proxy_index]
                # Extract session ID for safe logging
                if 'sessid-' in proxy_url:
                    sessid = proxy_url.split('sessid-')[1].split('-')[0]
                    console.print(f"[cyan]Using Oxylabs proxy session: {sessid}[/cyan]")
                return proxy_url
                
        elif self.proxy_config:
            # Use single proxy configuration
            return self.proxy_config
            
        return None
    
    def _mark_proxy_failed(self):
        """Mark current Oxylabs proxy as failed."""
        if self.use_oxylabs and self.oxylabs_proxies and self.current_proxy_index < len(self.oxylabs_proxies):
            self.failed_proxies.add(self.current_proxy_index)
            proxy_url = self.oxylabs_proxies[self.current_proxy_index]
            if 'sessid-' in proxy_url:
                sessid = proxy_url.split('sessid-')[1].split('-')[0]
                console.print(f"[red]Marked Oxylabs proxy session {sessid} as failed[/red]")
    
    async def start(self):
        """Start the Playwright browser."""
        self.playwright = await async_playwright().start()
        
        # Generate random profile
        self.current_profile = BrowserProfileGenerator.generate_random_profile()
        
        console.print(f"[blue]Starting {self.current_profile.browser_type} browser with stealth features[/blue]")
        console.print(f"[cyan]Profile: {self.current_profile.user_agent[:50]}...[/cyan]")
        
        # Launch browser based on profile type
        browser_launcher = getattr(self.playwright, self.current_profile.browser_type)
        
        launch_options = {
            'headless': self.headless,
            'args': [
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-blink-features=AutomationControlled',
                '--exclude-switches=enable-automation',
                '--disable-web-security',
                '--allow-running-insecure-content',
                '--disable-features=TranslateUI',
                '--disable-iframes-during-prerender',
                '--disable-background-timer-throttling',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-client-side-phishing-detection',
                '--disable-default-apps',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-features=Translate',
                '--disable-hang-monitor',
                '--disable-popup-blocking',
                '--disable-prompt-on-repost',
                '--disable-sync',
                '--metrics-recording-only',
                '--no-sandbox',
                '--disable-setuid-sandbox',
            ]
        }
        
        # Add proxy if configured
        current_proxy = self._get_current_proxy()
        if current_proxy:
            launch_options['proxy'] = {'server': current_proxy}
        
        self.browser = await browser_launcher.launch(**launch_options)
        
        # Create context with stealth settings
        context_options = {
            'viewport': self.current_profile.viewport,
            'user_agent': self.current_profile.user_agent,
            'locale': self.current_profile.locale,
            'timezone_id': self.current_profile.timezone,
            'extra_http_headers': self.current_profile.extra_http_headers,
            'ignore_https_errors': True,
        }
        
        self.context = await self.browser.new_context(**context_options)
        
        # Add stealth scripts
        for script in self.STEALTH_SCRIPTS:
            await self.context.add_init_script(script)
        
        console.print(f"[green]Browser started with viewport {self.current_profile.viewport}[/green]")
    
    async def close(self):
        """Close the browser and cleanup."""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
        
        console.print("[blue]Browser closed[/blue]")
    
    def _build_google_referrer(self, search_query: str) -> str:
        """Build a realistic Google search referrer URL."""
        if not search_query:
            return 'https://www.google.com/'
        
        encoded_query = urllib.parse.quote_plus(search_query)
        
        google_referrers = [
            f'https://www.google.com/search?q={encoded_query}&oq={encoded_query}&aqs=chrome..69i57j0l7.{random.randint(1000,9999)}j0j7&sourceid=chrome&ie=UTF-8',
            f'https://www.google.com/search?q={encoded_query}&source=hp&ei={random.randint(100000,999999)}&iflsig=random&ved=random',
            f'https://www.google.com/search?q={encoded_query}&rlz=1C1CHBF_enUS{random.randint(100,999)}US{random.randint(100,999)}&oq={encoded_query}',
        ]
        
        return random.choice(google_referrers)
    
    async def fetch_page(self, url: str, search_query: str = None, timeout: int = 60000) -> tuple[Optional[str], Optional[int]]:
        """
        Fetch a web page with stealth features.
        
        Args:
            url: URL to fetch
            search_query: Original search query to simulate Google referral
            timeout: Timeout in milliseconds
            
        Returns:
            Tuple of (HTML content as string or None, HTTP status code or None)
        """
        try:
            page = await self.context.new_page()
            
            # Set Google referrer if search query provided
            if search_query:
                referrer = self._build_google_referrer(search_query)
                console.print(f"[blue]Using referrer: {referrer}[/blue]")
                await page.set_extra_http_headers({'Referer': referrer})
            
            console.print(f"[cyan]Fetching: {url}[/cyan]")
            
            # Navigate to page
            response = await page.goto(url, timeout=timeout, wait_until='domcontentloaded')
            
            if response and response.status == 200:
                # Get page content
                content = await page.content()
                
                # Check for paywall/pricing page
                if "Plans and Pricing" in content or "START FREE TRIAL" in content or "14-day free trial" in content:
                    console.print(f"[red]Hit paywall/pricing page - marking proxy as failed[/red]")
                    self._mark_proxy_failed()
                    await page.close()
                    return None, 403
                
                console.print(f"[green]Successfully fetched page ({len(content)} chars)[/green]")
                await page.close()
                return content, 200
            else:
                status = response.status if response else None
                console.print(f"[red]Failed to fetch page, status: {status}[/red]")
                
                # Mark proxy as failed if we got blocked
                if response and response.status in [403, 429, 503]:
                    console.print(f"[yellow]Marking proxy as failed due to status {response.status}[/yellow]")
                    self._mark_proxy_failed()
                
                await page.close()
                return None, status
                
        except Exception as e:
            console.print(f"[red]Error fetching page: {e}[/red]")
            
            # Mark proxy as failed on connection errors
            if "net::" in str(e) or "timeout" in str(e).lower():
                console.print(f"[yellow]Marking proxy as failed due to connection error[/yellow]")
                self._mark_proxy_failed()
                
            return None, None
    
    async def test_stealth_features(self) -> Dict[str, Any]:
        """Test stealth features to verify they're working."""
        test_results = {}
        
        try:
            page = await self.context.new_page()
            
            # Navigate to a test page
            await page.goto('https://bot.sannysoft.com/', timeout=30000)
            
            # Wait for page to load
            await page.wait_for_timeout(3000)
            
            # Take screenshot for manual verification
            screenshot = await page.screenshot()
            
            # Check for webdriver detection
            webdriver_detected = await page.evaluate('navigator.webdriver')
            test_results['webdriver_hidden'] = webdriver_detected is None
            
            # Check user agent
            user_agent = await page.evaluate('navigator.userAgent')
            test_results['user_agent'] = user_agent
            
            # Check viewport
            viewport = await page.evaluate('({width: window.innerWidth, height: window.innerHeight})')
            test_results['viewport'] = viewport
            
            # Check plugins
            plugins_length = await page.evaluate('navigator.plugins.length')
            test_results['plugins_spoofed'] = plugins_length > 0
            
            await page.close()
            
            console.print(f"[green]Stealth test completed[/green]")
            for key, value in test_results.items():
                console.print(f"[blue]{key}: {value}[/blue]")
            
            return test_results
            
        except Exception as e:
            console.print(f"[red]Stealth test failed: {e}[/red]")
            return {'error': str(e)}

# Example usage
async def main():
    """Example usage of the Playwright client."""
    async with PacerMonitorPlaywrightClient(headless=False, use_oxylabs=True, mobile_proxy=False, num_proxies=10000) as client:
        # Test stealth features
        await client.test_stealth_features()
        
        # Fetch a test page
        search_query = "njd 3:25-cv-10315"
        test_url = "https://www.pacermonitor.com/public/case/58525826/STICKEN_v_JOHNSON__JOHNSON_et_al"
        
        html_content, status = await client.fetch_page(test_url, search_query=search_query)
        
        if html_content:
            console.print(f"[green]Successfully fetched {len(html_content)} characters[/green]")
            console.print(f"[blue]First 200 characters:[/blue]")
            console.print(html_content[:200] + "...")
        else:
            console.print("[red]Failed to fetch page[/red]")

if __name__ == "__main__":
    asyncio.run(main())