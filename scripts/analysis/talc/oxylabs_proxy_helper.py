#!/usr/bin/env python3
"""
Oxylabs Proxy Helper for PacerMonitor Scraping

Reuses the existing LexGenius Oxylabs proxy configuration system to generate
proxy URLs compatible with Playwright and HTTP clients.
"""

import os
import random
import logging
from typing import List, Optional, Dict, Any
from pathlib import Path

class OxylabsProxyHelper:
    """Generates Oxylabs proxy URLs using existing LexGenius configuration."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, mobile_proxy: bool = False):
        """
        Initialize proxy helper.
        
        Args:
            config: Configuration dictionary (optional, will load from .env if None)
            mobile_proxy: True for mobile proxies, False for residential
        """
        self.config = config or self._load_config_from_env()
        self.mobile_proxy = mobile_proxy
        self.logger = logging.getLogger(__name__)
        
    def _load_config_from_env(self) -> Dict[str, Any]:
        """Load configuration from environment variables."""
        config = {}
        
        # Try to load from .env file if it exists (look in project root)
        # Go up from @scripts/analysis/talc/ to project root
        env_file = Path(__file__).parent.parent.parent.parent / '.env'
        if env_file.exists():
            try:
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            # Store with original case for environment variables
                            config[key.strip()] = value.strip().strip('"\'')
                            # Also store lowercase version for backward compatibility
                            config[key.strip().lower()] = value.strip().strip('"\'')
            except Exception as e:
                self.logger.warning(f"Failed to load .env file: {e}")
        
        # Override with actual environment variables
        for key, value in os.environ.items():
            config[key] = value
            config[key.lower()] = value
                
        return config
    
    def generate_proxy_list(self, num_proxies: int = 10000) -> List[str]:
        """
        Generate a list of Oxylabs proxy URLs.
        
        Args:
            num_proxies: Number of proxies to generate
            
        Returns:
            List of proxy URLs in format suitable for Playwright/requests
        """
        username, password = self._get_credentials()
        
        if not username or not password:
            self.logger.error("Oxylabs credentials not found. Please set environment variables.")
            return []
        
        proxy_list = []
        proxy_type_log = "MOBILE" if self.mobile_proxy else "RESIDENTIAL"
        base_url = 'pr.oxylabs.io:7777'
        
        self.logger.info(f"Generating {num_proxies} {proxy_type_log} proxies")
        
        # Generate unique random 10-digit session IDs
        generated_sessids = set()
        while len(generated_sessids) < num_proxies:
            random_sessid = str(random.randint(1000000000, 9999999999))
            generated_sessids.add(random_sessid)
        
        # Create proxy URLs using the generated session IDs
        for sessid in generated_sessids:
            proxy_url = f"http://customer-{username}-cc-us-sessid-{sessid}-sesstime-10:{password}@{base_url}"
            proxy_list.append(proxy_url)
        
        # Randomize the order
        random.shuffle(proxy_list)
        
        self.logger.info(f"Generated {len(proxy_list)} {proxy_type_log} Oxylabs proxies")
        if proxy_list:
            # Log sample format without credentials
            sample = proxy_list[0].split('@')[0].split(':')[0] + "@pr.oxylabs.io:7777"
            self.logger.debug(f"Sample proxy format: {sample}")
        
        return proxy_list
    
    def _get_credentials(self) -> tuple[Optional[str], Optional[str]]:
        """Get Oxylabs credentials, prioritizing specific proxy type."""
        username = None
        password = None
        proxy_type_log = "MOBILE" if self.mobile_proxy else "RESIDENTIAL"
        
        # 1. Try specific credentials first
        if self.mobile_proxy:
            specific_user_key = 'oxy_labs_mobile_username'
            specific_pass_key = 'oxy_labs_mobile_password'
            specific_user_env = 'OXY_LABS_MOBILE_USERNAME'
            specific_pass_env = 'OXY_LABS_MOBILE_PASSWORD'
        else:
            specific_user_key = 'oxy_labs_residential_username'
            specific_pass_key = 'oxy_labs_residential_password'
            specific_user_env = 'OXY_LABS_RESIDENTIAL_USERNAME'
            specific_pass_env = 'OXY_LABS_RESIDENTIAL_PASSWORD'
        
        # Try config first, then environment
        username = self.config.get(specific_user_key) or os.environ.get(specific_user_env)
        password = self.config.get(specific_pass_key) or os.environ.get(specific_pass_env)
        
        if username and password:
            self.logger.info(f"Using specific {proxy_type_log} credentials")
            return username, password
        
        # 2. Fallback to generic credentials
        self.logger.info(f"Specific {proxy_type_log} credentials not found. Using generic...")
        username = self.config.get('oxy_labs_username') or os.environ.get('OXY_LABS_USERNAME')
        password = self.config.get('oxy_labs_password') or os.environ.get('OXY_LABS_PASSWORD')
        
        if username and password:
            self.logger.info(f"Using generic credentials for {proxy_type_log} proxy")
            return username, password
        
        # 3. Final attempt - check environment with different case variations
        for user_key in ['OXYLABS_USERNAME', 'oxylabs_username']:
            for pass_key in ['OXYLABS_PASSWORD', 'oxylabs_password']:
                username = os.environ.get(user_key)
                password = os.environ.get(pass_key)
                if username and password:
                    self.logger.info(f"Using credentials from {user_key}/{pass_key}")
                    return username, password
        
        self.logger.error(f"No Oxylabs credentials found for {proxy_type_log} proxy")
        self.logger.error("Please set one of the following environment variable pairs:")
        self.logger.error("- OXY_LABS_USERNAME/OXY_LABS_PASSWORD")
        self.logger.error("- oxy_labs_username/oxy_labs_password")
        if self.mobile_proxy:
            self.logger.error("- OXY_LABS_MOBILE_USERNAME/OXY_LABS_MOBILE_PASSWORD")
        else:
            self.logger.error("- OXY_LABS_RESIDENTIAL_USERNAME/OXY_LABS_RESIDENTIAL_PASSWORD")
        
        return None, None
    
    def get_random_proxy(self, proxy_list: List[str]) -> Optional[str]:
        """Get a random proxy from the list."""
        if not proxy_list:
            return None
        return random.choice(proxy_list)
    
    def test_proxy_format(self, proxy_url: str) -> bool:
        """Test if proxy URL has correct format."""
        if not proxy_url:
            return False
        
        try:
            # Check basic format: *****************************:port
            if not proxy_url.startswith('http://'):
                return False
            
            # Should contain @ and :
            if '@' not in proxy_url or ':' not in proxy_url:
                return False
            
            # Should contain oxylabs domain
            if 'oxylabs.io' not in proxy_url:
                return False
            
            return True
            
        except Exception:
            return False

# Convenience functions
def generate_oxylabs_proxies(num_proxies: int = 10000, mobile: bool = False) -> List[str]:
    """
    Generate Oxylabs proxy list using environment credentials.
    
    Args:
        num_proxies: Number of proxies to generate
        mobile: True for mobile proxies, False for residential
        
    Returns:
        List of proxy URLs
    """
    helper = OxylabsProxyHelper(mobile_proxy=mobile)
    return helper.generate_proxy_list(num_proxies)

def get_random_oxylabs_proxy(mobile: bool = False) -> Optional[str]:
    """Get a single random Oxylabs proxy."""
    proxies = generate_oxylabs_proxies(num_proxies=10, mobile=mobile)
    if proxies:
        return random.choice(proxies)
    return None

# Example usage
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Test residential proxies
    print("Testing residential proxies...")
    helper = OxylabsProxyHelper(mobile_proxy=False)
    residential_proxies = helper.generate_proxy_list(5)
    
    if residential_proxies:
        print(f"Generated {len(residential_proxies)} residential proxies")
        sample = residential_proxies[0]
        print(f"Sample (without credentials): {sample.split('@')[0].split(':')[0]}@pr.oxylabs.io:7777")
        print(f"Format valid: {helper.test_proxy_format(sample)}")
    else:
        print("Failed to generate residential proxies - check credentials")
    
    # Test mobile proxies
    print("\nTesting mobile proxies...")
    helper_mobile = OxylabsProxyHelper(mobile_proxy=True)
    mobile_proxies = helper_mobile.generate_proxy_list(3)
    
    if mobile_proxies:
        print(f"Generated {len(mobile_proxies)} mobile proxies")
    else:
        print("Failed to generate mobile proxies - check credentials")