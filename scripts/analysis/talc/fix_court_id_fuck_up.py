#!/usr/bin/env python3
"""
Fix the court_id fuck up - change scd to njd for Johnson & Johnson cases.

This script:
1. Queries DynamoDB for MDL 2738 cases with court_id='scd' from 20250601-20250630
2. Checks if versus contains "johnson" (case insensitive)
3. Updates court_id to 'njd' for matching records
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import re

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from boto3.dynamodb.conditions import Key, Attr

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

console = Console()

class CourtIdFixer:
    """Fix the court_id fuck up for Johnson & Johnson cases."""
    
    def __init__(self):
        self.storage = None
        self.pacer_repo = None
        
    async def query_scd_records_by_date_range(self) -> List[Dict[str, Any]]:
        """Query all records with court_id='scd' and mdl_num='2738' from 20250601-20250630."""
        console.print("[yellow]Querying DynamoDB using MdlNum-FilingDate-index...[/yellow]")
        
        all_records = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            query_task = progress.add_task("[cyan]Querying MdlNum-FilingDate-index...", total=None)
            
            # Query using MdlNum-FilingDate-index GSI with date range
            key_condition = (
                Key('MdlNum').eq('2738') & 
                Key('FilingDate').between('20250601', '20250630')
            )
            
            # Add filter for court_id='scd' 
            filter_expression = Attr('CourtId').eq('scd')
            
            records = await self.storage.query(
                self.pacer_repo.table_name,
                key_condition,
                index_name='MdlNum-FilingDate-index',
                filter_expression=filter_expression
            )
            
            all_records.extend(records)
            progress.update(query_task, description=f"[cyan]Found {len(all_records)} SCD records")
        
        console.print(f"[green]Found {len(all_records)} records with court_id='scd' and mdl_num='2738'[/green]")
        return all_records
    
    def contains_johnson(self, versus: str) -> bool:
        """Check if versus field contains 'johnson' case insensitively."""
        if not versus:
            return False
        return bool(re.search(r'johnson', versus, re.IGNORECASE))
    
    async def update_court_id_to_njd(self, filing_date: str, docket_num: str) -> bool:
        """Update court_id from scd to njd for a specific record."""
        try:
            key = {
                'FilingDate': filing_date,
                'DocketNum': docket_num
            }
            
            update_expression = 'SET CourtId = :new_court_id'
            expression_values = {':new_court_id': 'njd'}
            
            await self.storage.update_item(
                self.pacer_repo.table_name,
                key,
                update_expression,
                expression_values
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update {docket_num}: {e}")
            return False
    
    async def fix_johnson_records(self):
        """Main method to fix all Johnson & Johnson records."""
        console.print("\n[bold red]FIXING COURT_ID FUCK UP FOR JOHNSON & JOHNSON CASES[/bold red]")
        console.print("=" * 60)
        
        # Initialize DynamoDB connection
        from types import SimpleNamespace
        config = SimpleNamespace()
        config.aws_region = 'us-west-2'
        config.dynamodb_endpoint = None
        self.storage = AsyncDynamoDBStorage(config, logger)
        
        async with self.storage:
            self.pacer_repo = PacerRepository(self.storage, logger)
            
            # Step 1: Query all SCD records in date range
            scd_records = await self.query_scd_records_by_date_range()
            
            if not scd_records:
                console.print("[green]No SCD records found to fix[/green]")
                return
            
            # Step 2: Filter for Johnson & Johnson cases
            console.print(f"\n[yellow]Filtering for Johnson & Johnson cases...[/yellow]")
            johnson_records = []
            
            for record in scd_records:
                versus = record.get('Versus', '')
                if self.contains_johnson(versus):
                    johnson_records.append(record)
                    console.print(f"[blue]FOUND JOHNSON CASE: {record.get('DocketNum')} - {versus}[/blue]")
            
            console.print(f"\n[red]Found {len(johnson_records)} Johnson & Johnson cases with wrong court_id='scd'[/red]")
            
            if not johnson_records:
                console.print("[green]No Johnson & Johnson cases found with incorrect court_id[/green]")
                return
            
            # Step 3: Update court_id to njd
            console.print(f"\n[yellow]Updating court_id from 'scd' to 'njd' for {len(johnson_records)} records...[/yellow]")
            
            success_count = 0
            fail_count = 0
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=console
            ) as progress:
                update_task = progress.add_task(
                    "[cyan]Updating court_id...", 
                    total=len(johnson_records)
                )
                
                for record in johnson_records:
                    filing_date = record['FilingDate']
                    docket_num = record['DocketNum']
                    versus = record.get('Versus', '')
                    
                    success = await self.update_court_id_to_njd(filing_date, docket_num)
                    
                    if success:
                        success_count += 1
                        console.print(f"[green]✓ UPDATED: {docket_num} - {versus}[/green]")
                    else:
                        fail_count += 1
                        console.print(f"[red]✗ FAILED: {docket_num} - {versus}[/red]")
                    
                    progress.advance(update_task)
            
            # Final summary
            console.print(f"\n[bold green]COURT_ID FIX COMPLETE:[/bold green]")
            console.print(f"[green]✓ Successfully updated: {success_count} records[/green]")
            console.print(f"[red]✗ Failed to update: {fail_count} records[/red]")
            
            if success_count > 0:
                console.print(f"[blue]All Johnson & Johnson cases now have court_id='njd'[/blue]")

async def main():
    """Main entry point."""
    fixer = CourtIdFixer()
    await fixer.fix_johnson_records()

if __name__ == "__main__":
    asyncio.run(main())