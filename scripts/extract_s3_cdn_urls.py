#!/usr/bin/env python3
"""Extract s3_html values from docket JSON files and create CDN URLs for files with matching PDFs."""

import json
import os
from pathlib import Path
from rich.console import Console
from rich.progress import track

console = Console()

def extract_cdn_urls(dockets_dir: str, output_file: str = None):
    """Extract s3_html values and create CDN URLs only for files with matching PDFs."""
    dockets_path = Path(dockets_dir)
    if not dockets_path.exists():
        console.print(f"[red]Error: Directory {dockets_dir} does not exist[/red]")
        return
    
    json_files = list(dockets_path.glob("*.json"))
    if not json_files:
        console.print(f"[yellow]No JSON files found in {dockets_dir}[/yellow]")
        return
    
    console.print(f"[green]Found {len(json_files)} JSON files to process[/green]")
    console.print(f"[cyan]Checking for matching PDFs in same directory[/cyan]")
    
    cdn_urls = []
    errors = []
    no_pdf_count = 0
    
    for json_file in track(json_files, description="Processing JSON files..."):
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            if 's3_html' in data and data['s3_html']:
                # Extract the base filename from s3_html path
                html_path = data['s3_html']
                html_filename = html_path.split('/')[-1]  # Get filename
                base_name = html_filename.replace('.html', '')  # Remove .html extension
                
                # Check if corresponding PDF exists in same directory
                pdf_filename = f"{base_name}.pdf"
                pdf_path = dockets_path / pdf_filename
                
                if pdf_path.exists():
                    cdn_url = f"https://cdn.lexgenius.ai{data['s3_html']}"
                    cdn_urls.append(cdn_url)
                else:
                    no_pdf_count += 1
        except json.JSONDecodeError as e:
            errors.append(f"JSON error in {json_file.name}: {str(e)}")
        except Exception as e:
            errors.append(f"Error processing {json_file.name}: {str(e)}")
    
    # Sort URLs alphabetically
    cdn_urls.sort()
    
    # Display results
    console.print(f"\n[green]Successfully extracted {len(cdn_urls)} CDN URLs with matching PDFs[/green]")
    console.print(f"[yellow]Skipped {no_pdf_count} files without matching PDFs[/yellow]")
    
    if errors:
        console.print(f"\n[red]Encountered {len(errors)} errors:[/red]")
        for error in errors[:5]:  # Show first 5 errors
            console.print(f"  - {error}")
        if len(errors) > 5:
            console.print(f"  ... and {len(errors) - 5} more errors")
    
    # Output URLs
    if output_file:
        with open(output_file, 'w') as f:
            for url in cdn_urls:
                f.write(url + '\n')
        console.print(f"\n[green]URLs written to {output_file} (sorted alphabetically)[/green]")
    else:
        console.print("\n[cyan]CDN URLs (sorted alphabetically):[/cyan]")
        for url in cdn_urls:
            console.print(url)
    
    return cdn_urls

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Extract s3_html values and create CDN URLs")
    parser.add_argument(
        "--date",
        default="20250617",
        help="Date directory to process (default: 20250617)"
    )
    parser.add_argument(
        "--output",
        "-o",
        help="Output file to write URLs (default: print to console)"
    )
    
    args = parser.parse_args()
    
    dockets_dir = f"data/{args.date}/dockets"
    extract_cdn_urls(dockets_dir, args.output)