import os

# Import the project root handling function
try:
    from scripts.utils import get_project_root
except ImportError:
    # Fall back to direct import if utils.py is not available
    try:
        from src.lib.config_adapter import PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            PROJECT_ROOT = os.getenv('PROJECT_ROOT', os.path.expanduser('~/PycharmProjects/lexgenius'))
        except ImportError:
            PROJECT_ROOT = os.path.expanduser('~/PycharmProjects/lexgenius')
else:
    PROJECT_ROOT = get_project_root()

#!/usr/bin/env python3
"""
Script to start a local DynamoDB instance using Docker.

This script:
1. Imports the DockerDynamoDBManager from src.lib
2. Creates a manager instance with a persistent volume at /Users/<USER>/PycharmProjects/lexgenius/.dynamodb
3. Starts the DynamoDB container (will automatically start if not running)
4. Displays the status and endpoint URL
"""

import os
import sys
import logging
import argparse
import socket

# Add the project root to the Python path to allow importing from src
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the DockerDynamoDBManager
try:
    from src.lib.docker_dynamodb_manager import DockerDynamoDBManager
    logger.info("Successfully imported DockerDynamoDBManager")
except ImportError as e:
    logger.error(f"Failed to import DockerDynamoDBManager: {e}")
    sys.exit(1)


def is_port_in_use(port):
    """Check if a port is in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0


def find_available_port(start_port, max_attempts=10):
    """Find an available port starting from start_port"""
    port = start_port
    for _ in range(max_attempts):
        if not is_port_in_use(port):
            return port
        port += 1
    return None


def main():
    """Main function to start local DynamoDB"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Start a local DynamoDB instance using Docker')
    parser.add_argument('--port', type=int, default=8000, help='Port to run DynamoDB on (default: 8000)')
    parser.add_argument('--auto-port', action='store_true', help='Automatically find an available port if the specified port is in use')
    args = parser.parse_args()

    # Check if the specified port is in use
    port = args.port
    if is_port_in_use(port):
        if args.auto_port:
            new_port = find_available_port(port + 1)
            if new_port:
                logger.warning(f"Port {port} is already in use. Using port {new_port} instead.")
                port = new_port
            else:
                logger.error(f"Port {port} is in use and no available ports found in range {port+1}-{port+10}")
                print(f"\nERROR: Port {port} is already in use and no available ports found.")
                print("Try specifying a different port with --port or use --auto-port to automatically find an available port.")
                sys.exit(1)
        else:
            logger.error(f"Port {port} is already in use")
            print(f"\nERROR: Port {port} is already in use.")
            print("Options:")
            print("1. Use --auto-port to automatically find an available port")
            print("2. Specify a different port with --port")
            print(f"3. Stop the process using port {port} and try again")
            sys.exit(1)

    try:
        # Define the persistent volume path
        base_volume_path = os.path.join(PROJECT_ROOT, ".dynamodb")

        # Create manager with specified volume path and port
        manager = DockerDynamoDBManager(port=port, project_name="lexgenius", base_volume_path=base_volume_path)
        logger.info(f"Created DockerDynamoDBManager instance with volume path: {base_volume_path} on port {port}")

        # Start DynamoDB (will automatically start if not running)
        manager.start_dynamodb()
        logger.info("Started DynamoDB container")

        # Check status
        status = manager.get_status()
        print("\n" + "="*50)
        print(f"DynamoDB running at: {status['endpoint_url']}")
        print(f"Container name: {status['container_name']}")
        print(f"Volume path: {status['volume_path']}")
        print("="*50 + "\n")

        print("DynamoDB is now running. Press Ctrl+C to exit.")
        print(f"Data is being stored in: {status['volume_path']}")

        # Keep the script running until interrupted
        try:
            while True:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nExiting. DynamoDB container will continue running in the background.")
            print("To stop the container, run this script again and press Ctrl+C, then run:")
            print(f"  python -c \"from src.lib.docker_dynamodb_manager import DockerDynamoDBManager; DockerDynamoDBManager(port={port}, project_name='lexgenius').stop_dynamodb()\"")

    except Exception as e:
        logger.error(f"Error starting DynamoDB: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()