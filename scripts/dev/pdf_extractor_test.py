#!/Users/<USER>/miniconda3/envs/lexgenius/bin/python
"""
PDF Extractor Test Script - Uses pdf_extractor2.py with marker-pdf

This script imports and uses pdf_extractor2.py to extract text from PDFs.
The pdf_extractor2.py module uses marker-pdf for text extraction.

Features:
- PDF text extraction using pdf_extractor2.py's marker-pdf implementation
- Page processing limit option
- Timeout handling
- Dependency checking
- Markdown report generation

Usage:
    ./pdf_extractor_test.py <pdf_path> [options]

Arguments:
    pdf_path: Path to the PDF file

Options:
    --output PATH       Output file path (default: src/scripts/extracted_text.md)
    --check-deps        Only check dependencies and exit
    --timeout SECONDS   Timeout in seconds (default: 300)
    --pages NUM         Max pages to process (default: all pages)
    --raw               Output raw text to stdout instead of saving to markdown

Examples:
    # Basic usage
    ./pdf_extractor_test.py document.pdf
    
    # Process only first 10 pages with 2-minute timeout
    ./pdf_extractor_test.py document.pdf --pages 10 --timeout 120
    
    # Check dependencies
    ./pdf_extractor_test.py --check-deps
"""

import argparse
import logging
import os
import sys
import time
from concurrent.futures import ThreadPoolExecutor, TimeoutError
from datetime import datetime
from functools import wraps

# Add parent directories to path to allow importing pdf_extractor2.py
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..', '..'))
sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("pdf_extractor_test")


def timeout_decorator(seconds):
    """Create a timeout decorator for functions."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(func, *args, **kwargs)
                try:
                    return future.result(timeout=seconds)
                except TimeoutError:
                    print(f"\n⚠️  Function {func.__name__} timed out after {seconds} seconds")
                    return f"TIMEOUT: Operation timed out after {seconds} seconds"
        return wrapper
    return decorator


def check_dependencies():
    """Check if required dependencies and modules are installed."""
    missing_deps = []
    
    # Check for marker-pdf package without importing it
    try:
        import pkg_resources
        pkg_resources.get_distribution("marker-pdf")
        print(f"✓ Found marker-pdf package")
    except (ImportError, pkg_resources.DistributionNotFound) as e:
        missing_deps.append(f"marker-pdf package - {e}")
        print(f"⚠️ marker-pdf package not found: {e}")
    
    # Check for pdf_extractor2.py without trying to import it directly
    pdf_extractor2_path = os.path.join(project_root, 'src', 'lib', 'pdf_extractor2.py')
    if os.path.exists(pdf_extractor2_path):
        print(f"✓ Found pdf_extractor2.py at {pdf_extractor2_path}")
    else:
        missing_deps.append(f"pdf_extractor2.py - File not found")
        print(f"⚠️ pdf_extractor2.py not found at expected path: {pdf_extractor2_path}")
    
    # Print Python version and path
    print(f"\nPython executable: {sys.executable}")
    print(f"Python version: {sys.version.split()[0]}")
    
    if missing_deps:
        print("\nWARNING: The following dependencies are missing:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nMake sure you're running with the correct conda environment:") 
        print("  conda activate lexgenius")
        print("  pip install marker-pdf")
        print("  ./src/scripts/pdf_extractor_test.py <pdf_path>\n")
    
    return len(missing_deps) == 0


def extract_with_pdf_extractor2(pdf_path, page_limit=None):
    """Extract text from PDF using pdf_extractor2.py.
    
    Args:
        pdf_path: Path to the PDF file
        page_limit: Optional limit on number of pages to process
    """
    try:
        print("Loading pdf_extractor2.py module...")
        
        # Import minimal config
        from src.lib.config_adapter import load_config
        
        # Import the PDFExtractor class
        from src.lib.pdf_extractor2 import PDFExtractor
        
        print("Successfully imported pdf_extractor2.py")
        
        # Initialize the PDFExtractor with the PDF path
        config = load_config('01/01/1970')  # minimal config
        print(f"Creating PDFExtractor for {pdf_path}...")
        extractor = PDFExtractor(config, pdf_source=pdf_path)
        
        # Extract text from the PDF
        if page_limit is not None:
            print(f"Processing pages 1-{page_limit}")
            text = extractor.extract_text_with_marker(first_page=1, last_page=page_limit)
        else:
            print(f"Processing all pages")
            text = extractor.extract_text_with_marker()
        
        # Clean up resources
        extractor.cleanup()
        
        if text:
            print(f"Successfully extracted {len(text)} characters")
            return text
        
        return "Error: pdf_extractor2.py returned no text"
    except Exception as e:
        logger.error(f"PDF extraction error with pdf_extractor2.py: {e}")
        return f"Error: {str(e)}"


def create_markdown_report(pdf_path, text_content, output_path, processing_time=None):
    """Create a markdown report with the extraction results.
    
    Args:
        pdf_path: Path to the processed PDF
        text_content: Extracted text content
        output_path: Where to save the Markdown report
        processing_time: Optional processing time in seconds
    """
    pdf_name = os.path.basename(pdf_path)

    with open(output_path, 'w', encoding='utf-8') as f:
        # Write header
        f.write(f"# PDF Extraction Test Results\n\n")
        f.write(f"**PDF File:** `{pdf_name}`\n\n")
        f.write(f"**Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"**PDF Path:** `{pdf_path}`\n\n")
        f.write(f"**Method:** pdf_extractor2.py with marker-pdf\n\n")

        # Write extraction results
        f.write("## Extraction Results\n\n")
        f.write("```\n")
        f.write(text_content)  # Include full text
        f.write("\n```\n\n")

        # Add statistics
        f.write("## Statistics\n\n")
        char_count = len(text_content)
        word_count = len(text_content.split())

        if processing_time is not None:
            f.write("| Method | Character Count | Word Count | Processing Time (s) |\n")
            f.write("|--------|----------------|------------|--------------------|\n")
            proc_time_str = f"{processing_time:.2f}" if isinstance(processing_time, (int, float)) else processing_time
            f.write(f"| pdf_extractor2.py with marker-pdf | {char_count} | {word_count} | {proc_time_str} |\n")
        else:
            f.write("| Method | Character Count | Word Count |\n")
            f.write("|--------|----------------|------------|\n")
            f.write(f"| pdf_extractor2.py with marker-pdf | {char_count} | {word_count} |\n")


def main():
    parser = argparse.ArgumentParser(description='Test PDF text extraction using pdf_extractor2.py with marker-pdf')
    parser.add_argument('pdf_path', help='Path to the PDF file to extract text from', nargs='?')
    parser.add_argument('--output', help='Output path for the markdown report',
                        default=os.path.join(os.path.dirname(__file__), 'extracted_text.md'))
    parser.add_argument('--check-deps', action='store_true', help='Only check dependencies and exit')
    parser.add_argument('--timeout', type=int, default=300,
                        help='Timeout in seconds for extraction operations (default: 300)')
    parser.add_argument('--pages', type=int, default=None,
                        help='Number of pages to process (default: all pages)')
    parser.add_argument('--raw', action='store_true',
                        help='Output the raw text to stdout instead of saving to markdown')

    args = parser.parse_args()

    # Check dependencies if requested
    deps_ok = check_dependencies()
    if args.check_deps:
        sys.exit(0 if deps_ok else 1)

    # Ensure pdf_path is provided if not just checking dependencies
    if not args.pdf_path:
        parser.error("pdf_path is required unless --check-deps is specified")
        sys.exit(1)

    # Validate input PDF path
    if not os.path.exists(args.pdf_path):
        logger.error(f"Error: PDF file not found at {args.pdf_path}")
        sys.exit(1)

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    print(f"Extracting text from {args.pdf_path}...")
    print(f"Processing with a timeout of {args.timeout} seconds and a maximum of {args.pages or 'all'} pages")

    # Create timed version of extraction function
    timed_extractor = timeout_decorator(args.timeout)(lambda: extract_with_pdf_extractor2(args.pdf_path, args.pages))

    # Run extraction using pdf_extractor2.py
    print("\n--- Testing extraction with pdf_extractor2.py (marker-pdf) ---")
    start_time = datetime.now()
    try:
        text_content = timed_extractor()
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        if not text_content.startswith("TIMEOUT") and not text_content.startswith("Error"):
            print(f"✓ Extraction completed successfully in {processing_time:.2f} seconds")
        elif text_content.startswith("TIMEOUT"):
            print(f"⏱ Extraction timed out after {args.timeout} seconds")
        else:
            print(f"✗ Extraction failed: {text_content}")
    except Exception as e:
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        print(f"✗ Extraction failed after {processing_time:.2f} seconds: {str(e)}")
        text_content = f"Error: {str(e)}"

    # Output results
    if args.raw:
        if text_content and not text_content.startswith("Error"):
            print(text_content)
        else:
            print("No text extracted")
    else:
        # Create and save the markdown report
        create_markdown_report(args.pdf_path, text_content, args.output, processing_time)

        # Print processing time summary
        print("\n--- Processing Time Summary ---")
        print(f"pdf_extractor2.py (marker-pdf): {processing_time:.2f} seconds")

        print(f"\nResults saved to {args.output}")


if __name__ == "__main__":
    main()