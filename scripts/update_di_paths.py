#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update all @inject decorator Provide paths to match new container structure.
"""

import os
import re
from pathlib import Path
from typing import List, Tuple

# Mapping of old Provide paths to new ones
PATH_MAPPINGS = {
    # Core services
    'Provide["logger"]': 'Provide["core.logger"]',
    'Provide["config"]': 'Provide["core.config"]',
    'Provide["http_session"]': 'Provide["core.http_session"]',
    
    # Storage services
    'Provide["s3_storage"]': 'Provide["storage.s3_async_storage"]',
    'Provide["async_dynamodb_storage"]': 'Provide["storage.async_dynamodb_storage"]',
    'Provide["dynamodb_storage"]': 'Provide["storage.async_dynamodb_storage"]',
    
    # Repositories
    'Provide["pacer_repository"]': 'Provide["storage.pacer_repository"]',
    'Provide["fb_archive_repository"]': 'Provide["storage.fb_archive_repository"]',
    'Provide["law_firms_repository"]': 'Provide["storage.law_firms_repository"]',
    'Provide["district_courts_repository"]': 'Provide["storage.district_courts_repository"]',
    'Provide["fb_image_hash_repository"]': 'Provide["storage.fb_image_hash_repository"]',
    'Provide["pacer_dockets_repository"]': 'Provide["storage.pacer_dockets_repository"]',
    
    # AI services
    'Provide["ai_orchestrator"]': 'Provide["transformer.ai_orchestrator"]',
    'Provide["ai_service_factory"]': 'Provide["transformer.ai_service_factory"]',
    'Provide["deepseek_service"]': 'Provide["transformer.deepseek_service"]',
    'Provide["mistral_service"]': 'Provide["transformer.mistral_service"]',
    'Provide["batch_processor"]': 'Provide["transformer.batch_processor"]',
    'Provide["prompt_manager"]': 'Provide["transformer.prompt_manager"]',
    
    # External clients
    'Provide["deepseek_client"]': 'Provide["transformer.deepseek_client"]',
    'Provide["openai_client"]': 'Provide["transformer.openai_client"]',
    
    # PACER services
    'Provide["pacer_orchestrator"]': 'Provide["pacer.pacer_orchestrator"]',
    'Provide["browser_service"]': 'Provide["pacer.browser_service"]',
    'Provide["pacer_navigator"]': 'Provide["pacer.pacer_navigator"]',
    'Provide["authentication_service"]': 'Provide["pacer.authentication_service"]',
    'Provide["configuration_service"]': 'Provide["pacer.configuration_service"]',
    'Provide["file_management_service"]': 'Provide["pacer.file_management_service"]',
    'Provide["file_operations_service"]': 'Provide["pacer.file_operations_service"]',
    'Provide["court_processing_service"]': 'Provide["pacer.court_processing_service"]',
    'Provide["case_processing_service"]': 'Provide["pacer.case_processing_service"]',
    'Provide["case_verification_service"]': 'Provide["pacer.case_verification_service"]',
    'Provide["case_classification_service"]': 'Provide["pacer.case_classification_service"]',
    'Provide["ignore_download_service"]': 'Provide["pacer.ignore_download_service"]',
    'Provide["html_processing_service"]': 'Provide["pacer.html_processing_service"]',
    'Provide["navigation_service"]': 'Provide["pacer.navigation_service"]',
    'Provide["row_processing_service"]': 'Provide["pacer.row_processing_service"]',
    'Provide["transfer_service"]': 'Provide["pacer.transfer_service"]',
    'Provide["relevance_service"]': 'Provide["pacer.relevance_service"]',
    'Provide["query_service"]': 'Provide["pacer.query_service"]',
    'Provide["pacer_dockets_query_service"]': 'Provide["pacer.pacer_dockets_query_service"]',
    'Provide["report_service"]': 'Provide["pacer.report_service"]',
    'Provide["download_orchestration_service"]': 'Provide["pacer.download_orchestration_service"]',
    'Provide["analytics_service"]': 'Provide["pacer.analytics_service"]',
    'Provide["export_service"]': 'Provide["pacer.export_service"]',
    'Provide["interactive_service"]': 'Provide["pacer.interactive_service"]',
    'Provide["service_factory"]': 'Provide["pacer.service_factory"]',
    'Provide["docket_processing_orchestrator"]': 'Provide["pacer.docket_processing_orchestrator"]',
    
    # FB Ads services
    'Provide["facebook_ads_orchestrator"]': 'Provide["fb_ads.facebook_ads_orchestrator"]',
    'Provide["session_manager"]': 'Provide["fb_ads.session_manager"]',
    'Provide["api_client"]': 'Provide["fb_ads.api_client"]',
    'Provide["disk_cache"]': 'Provide["fb_ads.disk_cache"]',
    'Provide["bandwidth_logger"]': 'Provide["fb_ads.bandwidth_logger"]',
    'Provide["image_hash_service"]': 'Provide["fb_ads.image_hash_service"]',
    'Provide["local_image_queue"]': 'Provide["fb_ads.local_image_queue"]',
    'Provide["image_handler"]': 'Provide["fb_ads.image_handler"]',
    'Provide["processing_tracker"]': 'Provide["fb_ads.processing_tracker"]',
    'Provide["error_handling_service"]': 'Provide["fb_ads.error_handling_service"]',
    'Provide["data_validation_service"]': 'Provide["fb_ads.data_validation_service"]',
    'Provide["ner_rule_analyzer"]': 'Provide["fb_ads.ner_rule_analyzer"]',
    'Provide["ad_ner_processor"]': 'Provide["fb_ads.ad_ner_processor"]',
    'Provide["legal_ad_analyzer"]': 'Provide["fb_ads.legal_ad_analyzer"]',
    'Provide["ad_categorizer"]': 'Provide["fb_ads.ad_categorizer"]',
    'Provide["ad_db_service"]': 'Provide["fb_ads.ad_db_service"]',
    'Provide["ad_processing_service"]': 'Provide["fb_ads.ad_processing_service"]',
    'Provide["ad_processor"]': 'Provide["fb_ads.ad_processor"]',
    'Provide["workflow_service"]': 'Provide["fb_ads.workflow_service"]',
    'Provide["concurrent_workflow_service"]': 'Provide["fb_ads.concurrent_workflow_service"]',
    'Provide["fb_archive_query_service"]': 'Provide["fb_ads.fb_archive_query_service"]',
    'Provide["fb_archive_data_conversion_service"]': 'Provide["fb_ads.fb_archive_data_conversion_service"]',
    'Provide["fb_archive_delete_service"]': 'Provide["fb_ads.fb_archive_delete_service"]',
    'Provide["fb_archive_update_service"]': 'Provide["fb_ads.fb_archive_update_service"]',
    
    # Transformer services
    'Provide["data_transformer"]': 'Provide["transformer.data_transformer"]',
    'Provide["docket_processor"]': 'Provide["transformer.docket_processor"]',
    'Provide["data_processing_engine"]': 'Provide["transformer.data_processing_engine"]',
    'Provide["component_factory"]': 'Provide["transformer.component_factory"]',
    'Provide["file_handler"]': 'Provide["transformer.file_handler"]',
    'Provide["file_operations_manager"]': 'Provide["transformer.file_operations_manager"]',
    'Provide["docket_file_manager"]': 'Provide["transformer.docket_file_manager"]',
    'Provide["error_handler"]': 'Provide["transformer.error_handler"]',
    'Provide["docket_text_handler"]': 'Provide["transformer.docket_text_handler"]',
    'Provide["cached_pdf_data"]': 'Provide["transformer.cached_pdf_data"]',
    'Provide["docket_data_cleaner"]': 'Provide["transformer.docket_data_cleaner"]',
    'Provide["docket_validator"]': 'Provide["transformer.docket_validator"]',
    'Provide["law_firm_processor"]': 'Provide["transformer.law_firm_processor"]',
    'Provide["law_firm_integration"]': 'Provide["transformer.law_firm_integration"]',
    'Provide["mdl_lookup_manager"]': 'Provide["transformer.mdl_lookup_manager"]',
    'Provide["mdl_description_manager"]': 'Provide["transformer.mdl_description_manager"]',
    'Provide["mdl_persistence_manager"]': 'Provide["transformer.mdl_persistence_manager"]',
    'Provide["mdl_processing_service"]': 'Provide["transformer.mdl_processing_service"]',
    'Provide["mdl_processor"]': 'Provide["transformer.mdl_processor"]',
    'Provide["litigation_classifier"]': 'Provide["transformer.litigation_classifier"]',
    'Provide["docket_html_processor"]': 'Provide["transformer.docket_html_processor"]',
    'Provide["html_integration_service"]': 'Provide["transformer.html_integration_service"]',
    'Provide["docket_llm_engine"]': 'Provide["transformer.docket_llm_engine"]',
    'Provide["court_data_processor"]': 'Provide["transformer.court_data_processor"]',
    'Provide["transfer_handler"]': 'Provide["transformer.transfer_handler"]',
    'Provide["afff_calculator"]': 'Provide["transformer.afff_calculator"]',
    'Provide["uploader"]': 'Provide["transformer.uploader"]',
    'Provide["specialized_workflows"]': 'Provide["transformer.specialized_workflows"]',
    
    # Reports services
    'Provide["reports_orchestrator"]': 'Provide["reports.reports_orchestrator"]',
    'Provide["reports_config_service"]': 'Provide["reports.reports_config_service"]',
    'Provide["data_loader_service"]': 'Provide["reports.data_loader_service"]',
    'Provide["ad_df_processor_service"]': 'Provide["reports.ad_df_processor_service"]',
    'Provide["ad_page_generator_service"]': 'Provide["reports.ad_page_generator_service"]',
    'Provide["processing_service"]': 'Provide["reports.processing_service"]',
    'Provide["rendering_service"]': 'Provide["reports.rendering_service"]',
    'Provide["publishing_service"]': 'Provide["reports.publishing_service"]',
    'Provide["case_parser_service"]': 'Provide["reports.case_parser_service"]',
    'Provide["data_updater_service"]': 'Provide["reports.data_updater_service"]',
    'Provide["pdf_processor_service"]': 'Provide["reports.pdf_processor_service"]',
    'Provide["district_courts_query_service"]': 'Provide["reports.district_courts_query_service"]',
    'Provide["law_firms_query_service"]': 'Provide["reports.law_firms_query_service"]',
    'Provide["upload_service"]': 'Provide["reports.upload_service"]',
    'Provide["s3_upload_service"]': 'Provide["reports.s3_upload_service"]',
    'Provide["performance_monitor_service"]': 'Provide["reports.performance_monitor_service"]',
    'Provide["resource_cleanup_service"]': 'Provide["reports.resource_cleanup_service"]',
    'Provide["performance_monitoring_service"]': 'Provide["reports.performance_monitoring_service"]',
}


def find_python_files(directory: str) -> List[Path]:
    """Find all Python files in the given directory."""
    return list(Path(directory).rglob("*.py"))


def update_file(file_path: Path) -> Tuple[bool, int]:
    """Update a single file with new Provide paths."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_count = 0
        
        # Apply all path mappings
        for old_path, new_path in PATH_MAPPINGS.items():
            if old_path in content:
                content = content.replace(old_path, new_path)
                changes_count += content.count(new_path) - original_content.count(new_path)
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True, changes_count
        
        return False, 0
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False, 0


def main():
    """Main function to update all Python files."""
    # Start from src/services directory
    services_dir = Path(__file__).parent.parent / "src" / "services"
    
    if not services_dir.exists():
        print(f"Services directory not found: {services_dir}")
        return
    
    print(f"Searching for Python files in: {services_dir}")
    
    python_files = find_python_files(services_dir)
    print(f"Found {len(python_files)} Python files")
    
    total_updated = 0
    total_changes = 0
    
    for file_path in python_files:
        updated, changes = update_file(file_path)
        if updated:
            total_updated += 1
            total_changes += changes
            print(f"✓ Updated {file_path.relative_to(services_dir.parent)} ({changes} changes)")
    
    print(f"\nSummary:")
    print(f"- Total files processed: {len(python_files)}")
    print(f"- Files updated: {total_updated}")
    print(f"- Total changes made: {total_changes}")


if __name__ == "__main__":
    main()