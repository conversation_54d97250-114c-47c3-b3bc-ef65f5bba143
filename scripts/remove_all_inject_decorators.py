#!/usr/bin/env python3
"""
Remove all @inject decorators and Provide usages from the codebase
"""

import re
import subprocess
from pathlib import Path


def remove_inject_decorators_from_file(file_path: Path) -> bool:
    """Remove @inject decorators and Provide usages from a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Remove @inject decorators (including any leading whitespace)
        content = re.sub(r'\s*@inject\s*\n', '\n', content)
        
        # Remove Provide imports
        content = re.sub(r'from dependency_injector\.wiring import.*Provide.*\n', '', content)
        content = re.sub(r'from dependency_injector import.*Provide.*\n', '', content)
        
        # Replace Provide["..."] with None in function parameters
        content = re.sub(r'=\s*Provide\["[^"]+"\]', '=None', content)
        
        # Remove any standalone Provide imports
        content = re.sub(r'^.*import.*Provide.*\n', '', content, flags=re.MULTILINE)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Cleaned {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Error cleaning {file_path}: {e}")
        return False


def find_files_with_inject():
    """Find all files with @inject decorators"""
    try:
        result = subprocess.run(
            ['grep', '-rn', '@inject', 'src/services/'],
            capture_output=True,
            text=True
        )
        
        files_with_inject = set()
        for line in result.stdout.split('\n'):
            if line and ':' in line:
                file_path = line.split(':')[0]
                files_with_inject.add(Path(file_path))
        
        return list(files_with_inject)
        
    except Exception as e:
        print(f"Error finding files with @inject: {e}")
        return []


def main():
    """Remove all @inject decorators from the codebase"""
    print("Finding files with @inject decorators...")
    files_with_inject = find_files_with_inject()
    
    print(f"Found {len(files_with_inject)} files with @inject decorators")
    
    cleaned_count = 0
    for file_path in files_with_inject:
        if remove_inject_decorators_from_file(file_path):
            cleaned_count += 1
    
    print(f"Cleaned {cleaned_count} files")
    
    # Test import
    print("\nTesting import...")
    try:
        result = subprocess.run(
            ['python', '-c', 'import src.services.transformer.data_transformer; print("Import successful")'],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ Import test passed!")
        else:
            print("❌ Import test failed:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ Error running import test: {e}")


if __name__ == "__main__":
    main()