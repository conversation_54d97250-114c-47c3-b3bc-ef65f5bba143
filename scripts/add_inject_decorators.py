#!/usr/bin/env python3
"""
Script to add @inject decorators to services that are missing them.
"""
import re
import os
from pathlib import Path


def add_inject_decorator(file_path):
    """Add @inject decorator to a service file if missing."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check if already has @inject
    if '@inject' in content:
        print(f"✓ {file_path} already has @inject decorator")
        return False
    
    # Check if it has dependency_injector import
    has_di_import = 'from dependency_injector.wiring import' in content
    
    # Find the class definition and its __init__ method
    class_pattern = r'(class\s+\w+(?:\([^)]+\))?:\s*(?:"""[^"]*"""\s*)?)'
    init_pattern = r'(\s+)(def\s+__init__\s*\([^)]+\)(?:\s*->\s*[^:]+)?:\s*)'
    
    modified = False
    
    # Add import if missing
    if not has_di_import:
        # Find where to add the import
        import_insert_pos = 0
        lines = content.split('\n')
        
        # Find the last import statement
        last_import_idx = -1
        for i, line in enumerate(lines):
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                last_import_idx = i
        
        if last_import_idx >= 0:
            # Insert after the last import
            lines.insert(last_import_idx + 1, 'from dependency_injector.wiring import inject, Provide')
            content = '\n'.join(lines)
            modified = True
            print(f"  Added dependency_injector import to {file_path}")
    
    # Find all __init__ methods and add @inject decorator
    lines = content.split('\n')
    new_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        
        # Check if this is an __init__ method definition
        if 'def __init__' in line and not line.strip().startswith('#'):
            # Check if previous line has @inject
            if i > 0 and '@inject' not in lines[i-1]:
                # Get the indentation of the def line
                indent_match = re.match(r'^(\s*)', line)
                indent = indent_match.group(1) if indent_match else ''
                
                # Add @inject with same indentation
                new_lines.append(f'{indent}@inject')
                modified = True
                print(f"  Added @inject decorator to __init__ in {file_path}")
        
        new_lines.append(line)
        i += 1
    
    if modified:
        # Write the modified content
        with open(file_path, 'w') as f:
            f.write('\n'.join(new_lines))
        print(f"✅ Modified {file_path}")
        return True
    
    return False


def find_services_needing_inject():
    """Find all service files that need @inject decorator."""
    services_dir = Path('src/services')
    files_needing_inject = []
    
    # Files identified as needing @inject
    target_files = [
        'src/services/ai/prompt_manager.py',
        'src/services/fb_ads/concurrent_workflow_service.py',
        'src/services/fb_ads/failed_firms_manager.py',
        'src/services/fb_ads/jobs/job_orchestration_service.py',
        'src/services/fb_ads/jobs/job_runner_service.py',
        'src/services/fb_archive/delete_service.py',
        'src/services/fb_archive/update_service.py',
        'src/services/pacer/file_operations_service.py',
        'src/services/transformer/config.py',
        'src/services/transformer/data_transformer.py',
    ]
    
    return target_files


def main():
    """Main function to add @inject decorators."""
    print("Adding @inject decorators to services...")
    print("=" * 60)
    
    files = find_services_needing_inject()
    modified_count = 0
    
    for file_path in files:
        if os.path.exists(file_path):
            if add_inject_decorator(file_path):
                modified_count += 1
        else:
            print(f"❌ File not found: {file_path}")
    
    print("=" * 60)
    print(f"✅ Modified {modified_count} files")
    print(f"📊 Total files processed: {len(files)}")


if __name__ == '__main__':
    main()