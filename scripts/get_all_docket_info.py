#!/usr/bin/env python3
"""Extract court_id and docket_num from JSON files and return complete list."""

import json
from pathlib import Path
from typing import List, Dict


def get_all_docket_info(data_dir: str = "data/20250617/dockets") -> List[Dict[str, str]]:
    """Extract court_id and docket_num from all JSON files."""
    results = []
    
    # Get all JSON files in the directory
    json_files = list(Path(data_dir).glob("*.json"))
    
    # Process each JSON file
    for json_file in json_files:
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                
                # Extract court_id and docket_num
                court_id = data.get('court_id')
                docket_num = data.get('docket_num')
                
                if court_id and docket_num:
                    results.append({
                        "court_id": court_id,
                        "docket_num": docket_num
                    })
                    
        except Exception:
            pass  # Skip any problematic files
    
    # Sort by court_id alphabetically
    results.sort(key=lambda x: x['court_id'])
    
    return results


if __name__ == "__main__":
    # Get the complete list
    all_items = get_all_docket_info()
    
    # Print the complete list as JSON
    print(json.dumps(all_items, indent=2))