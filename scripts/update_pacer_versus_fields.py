#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to query DynamoDB Pacer table for MDL 2873 cases filed since 2025-01-01
and update the Versus field with standardized text replacements.
"""

import asyncio
import os
import sys
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from boto3.dynamodb.conditions import Key
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.infrastructure.protocols.exceptions import LoggerProtocol
import logging

@dataclass
class Config:
    """Configuration for the script"""
    aws_region: str = 'us-west-2'
    dynamodb_endpoint: Optional[str] = None
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None

class SimpleLogger(LoggerProtocol):
    """Simple logger implementation for the script"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
    def log_info(self, message: str, context: Optional[Dict[str, Any]] = None) -> None:
        self.logger.info(message, extra=context or {})
        
    def log_error(self, message: str, context: Optional[Dict[str, Any]] = None) -> None:
        self.logger.error(message, extra=context or {})
        
    def log_warning(self, message: str, context: Optional[Dict[str, Any]] = None) -> None:
        self.logger.warning(message, extra=context or {})
        
    def log_debug(self, message: str, context: Optional[Dict[str, Any]] = None) -> None:
        self.logger.debug(message, extra=context or {})
        
    def log_exception(self, message: str, context: Optional[Dict[str, Any]] = None) -> None:
        self.logger.exception(message, extra=context or {})

class PacerVersusFieldUpdater:
    """Updates Versus fields in Pacer table for MDL 2873 cases"""
    
    def __init__(self, config: Config, logger: LoggerProtocol):
        self.config = config
        self.logger = logger
        self.console = Console()
        self.table_name = "Pacer"
        self.mdl_num = "2873"
        self.filing_date_start = "20250101"
        
        # Note: replacements are now handled in comprehensive normalization function
        # This dict is kept for reference but not used in the new logic
    
    def apply_versus_updates(self, versus_text: str) -> tuple[str, bool]:
        """
        Apply comprehensive text normalization to Versus field.
        
        Args:
            versus_text: Original Versus field text
            
        Returns:
            Tuple of (updated_text, was_changed)
        """
        import re
        
        if not versus_text:
            return versus_text, False
            
        original_text = versus_text
        updated_text = versus_text
        
        # 1. Remove " and Jury Demand" (case insensitive)
        updated_text = re.sub(r'\s+and\s+jury\s+demand', '', updated_text, flags=re.IGNORECASE)
        
        # 2. Standardize versus format to "v." using word boundaries
        versus_replacements = [
            (r'\bVs\.\b', 'v.'),
            (r'\bVs\b', 'v.'),
            (r'\bVS\.\b', 'v.'),
            (r'\bVS\b', 'v.')
        ]
        
        for pattern, replacement in versus_replacements:
            updated_text = re.sub(pattern, replacement, updated_text)
        
        # 3. Split on "v." to handle plaintiff vs defendant
        if ' v. ' in updated_text:
            parts = updated_text.split(' v. ', 1)
            if len(parts) == 2:
                plaintiff = parts[0].strip()
                defendant = parts[1].strip()
                
                # Handle 3M standardization in defendant section ONLY
                if re.search(r'\b3M\b', defendant, re.IGNORECASE):
                    # Any defendant containing 3M becomes "3M Company et al."
                    defendant = '3M Company et al.'
                
                updated_text = f"{plaintiff} v. {defendant}"
        
        # 4. Apply general normalizations to the entire text
        updated_text = re.sub(r'\bEt Al\b', 'et al.', updated_text, flags=re.IGNORECASE)
        updated_text = re.sub(r'\bet al\b(?!\.)', 'et al.', updated_text, flags=re.IGNORECASE)
        updated_text = re.sub(r'\bet\s+al\.{2,}', 'et al.', updated_text, flags=re.IGNORECASE)
        updated_text = re.sub(r'\bCompanympany\b', 'Company', updated_text, flags=re.IGNORECASE)
        
        return updated_text, updated_text != original_text
    
    async def query_pacer_cases(self, storage: AsyncDynamoDBStorage) -> List[Dict[str, Any]]:
        """
        Query Pacer table for MDL 2873 cases filed since 2025-01-01.
        
        Args:
            storage: DynamoDB storage instance
            
        Returns:
            List of case records
        """
        self.console.print(f"[bold blue]Querying Pacer table for MDL {self.mdl_num} cases since {self.filing_date_start}[/bold blue]")
        
        # Use MdlNum-FilingDate-index GSI
        key_condition = Key('MdlNum').eq(self.mdl_num) & Key('FilingDate').gte(self.filing_date_start)
        
        cases = await storage.query(
            table_name=self.table_name,
            key_condition=key_condition,
            index_name='MdlNum-FilingDate-index'
        )
        
        self.console.print(f"[green]Found {len(cases)} cases[/green]")
        return cases
    
    async def update_case_versus_field(self, storage: AsyncDynamoDBStorage, case: Dict[str, Any], 
                                     updated_versus: str) -> bool:
        """
        Update a single case's Versus field.
        
        Args:
            storage: DynamoDB storage instance
            case: Case record
            updated_versus: Updated Versus text
            
        Returns:
            True if update succeeded, False otherwise
        """
        try:
            # Primary key for Pacer table
            key = {
                'FilingDate': case['FilingDate'],
                'DocketNum': case['DocketNum']
            }
            
            # Update the Versus field
            await storage.update_item(
                table_name=self.table_name,
                key=key,
                update_data="SET Versus = :versus",
                expression_values={':versus': updated_versus}
            )
            
            return True
            
        except Exception as e:
            self.logger.log_error(f"Failed to update case {case.get('DocketNum', 'unknown')}", {
                'error': str(e),
                'filing_date': case.get('FilingDate'),
                'docket_num': case.get('DocketNum')
            })
            return False
    
    async def process_cases(self, dry_run: bool = True) -> None:
        """
        Main processing method to query and update cases.
        
        Args:
            dry_run: If True, only show what would be updated without making changes
        """
        config = self.config
        logger = self.logger
        
        async with AsyncDynamoDBStorage(config, logger) as storage:
            # Query cases
            cases = await self.query_pacer_cases(storage)
            
            if not cases:
                self.console.print("[yellow]No cases found for the specified criteria[/yellow]")
                return
            
            # Analyze cases that need updates
            cases_to_update = []
            for case in cases:
                versus_text = case.get('Versus', '')
                updated_versus, needs_update = self.apply_versus_updates(versus_text)
                
                if needs_update:
                    cases_to_update.append({
                        'case': case,
                        'original_versus': versus_text,
                        'updated_versus': updated_versus
                    })
            
            # Display summary
            self.display_summary(cases, cases_to_update)
            
            if not cases_to_update:
                self.console.print("[green]No cases require Versus field updates[/green]")
                return
            
            if dry_run:
                self.console.print(f"[yellow]DRY RUN: Would update {len(cases_to_update)} cases[/yellow]")
                self.display_update_preview(cases_to_update)
            else:
                # Perform actual updates
                await self.perform_updates(storage, cases_to_update)
    
    def display_summary(self, all_cases: List[Dict[str, Any]], cases_to_update: List[Dict[str, Any]]) -> None:
        """Display summary of cases found and updates needed"""
        
        summary_table = Table(title="Query Summary")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Count", style="magenta")
        
        summary_table.add_row("Total Cases Found", str(len(all_cases)))
        summary_table.add_row("Cases Needing Updates", str(len(cases_to_update)))
        summary_table.add_row("Cases Already Correct", str(len(all_cases) - len(cases_to_update)))
        
        self.console.print(summary_table)
    
    def display_update_preview(self, cases_to_update: List[Dict[str, Any]]) -> None:
        """Display preview of what would be updated"""
        
        if not cases_to_update:
            return
            
        preview_table = Table(title="Update Preview")
        preview_table.add_column("Docket Number", style="cyan")
        preview_table.add_column("Filing Date", style="blue")
        preview_table.add_column("Original Versus", style="red")
        preview_table.add_column("Updated Versus", style="green")
        
        for item in cases_to_update[:10]:  # Show first 10
            case = item['case']
            preview_table.add_row(
                case.get('DocketNum', 'N/A'),
                case.get('FilingDate', 'N/A'),
                item['original_versus'][:50] + "..." if len(item['original_versus']) > 50 else item['original_versus'],
                item['updated_versus'][:50] + "..." if len(item['updated_versus']) > 50 else item['updated_versus']
            )
        
        if len(cases_to_update) > 10:
            preview_table.add_row("...", "...", f"... and {len(cases_to_update) - 10} more", "...")
        
        self.console.print(preview_table)
    
    async def perform_updates(self, storage: AsyncDynamoDBStorage, cases_to_update: List[Dict[str, Any]]) -> None:
        """Perform the actual updates to DynamoDB"""
        
        self.console.print(f"[bold green]Updating {len(cases_to_update)} cases...[/bold green]")
        
        success_count = 0
        failure_count = 0
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Updating cases...", total=len(cases_to_update))
            
            for item in cases_to_update:
                case = item['case']
                updated_versus = item['updated_versus']
                
                success = await self.update_case_versus_field(storage, case, updated_versus)
                
                if success:
                    success_count += 1
                else:
                    failure_count += 1
                
                progress.update(task, advance=1)
        
        # Display results
        results_table = Table(title="Update Results")
        results_table.add_column("Result", style="cyan")
        results_table.add_column("Count", style="magenta")
        
        results_table.add_row("Successful Updates", str(success_count))
        results_table.add_row("Failed Updates", str(failure_count))
        results_table.add_row("Total Processed", str(len(cases_to_update)))
        
        self.console.print(results_table)
        
        if success_count > 0:
            self.console.print(f"[bold green]Successfully updated {success_count} cases[/bold green]")
        if failure_count > 0:
            self.console.print(f"[bold red]Failed to update {failure_count} cases[/bold red]")

async def main():
    """Main entry point"""
    console = Console()
    
    # Parse command line arguments
    dry_run = True
    if len(sys.argv) > 1 and sys.argv[1] == '--execute':
        dry_run = False
    
    # Show usage if needed
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        console.print(Panel.fit(
            "[bold]Pacer Versus Field Updater[/bold]\n\n"
            "Updates Versus fields in Pacer table for MDL 2873 cases.\n\n"
            "[bold]Usage:[/bold]\n"
            "  python update_pacer_versus_fields.py           # Dry run (preview only)\n"
            "  python update_pacer_versus_fields.py --execute # Execute updates\n\n"
            "[bold]Replacements:[/bold]\n"
            "  Remove ' and Jury Demand' (case insensitive)\n"
            "  ALL versus formats → 'v.' (eliminates Vs., Vs, VS, VS.)\n"
            "  Any defendant with '3M' → '3M Company et al.'\n"
            "  'Et Al' → 'et al.'\n"
            "  'et al...' → 'et al.'\n"
            "  Fix 'Companympany' → 'Company'"
        ))
        return
    
    # Initialize configuration and logger
    config = Config()
    logger = SimpleLogger()
    
    # Create updater and run
    updater = PacerVersusFieldUpdater(config, logger)
    
    if dry_run:
        console.print("[yellow]Running in DRY RUN mode - no changes will be made[/yellow]")
        console.print("[dim]Use --execute to perform actual updates[/dim]\n")
    else:
        console.print("[bold red]EXECUTING UPDATES - changes will be made to DynamoDB[/bold red]\n")
    
    try:
        await updater.process_cases(dry_run=dry_run)
    except Exception as e:
        console.print(f"[bold red]Error: {str(e)}[/bold red]")
        logger.log_exception("Script failed", {'error': str(e)})
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())