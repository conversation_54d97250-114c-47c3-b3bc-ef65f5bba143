#!/usr/bin/env python3
"""
Service Health Dashboard for PACER Services
Addresses service orchestration dependencies identified in troubleshooting analysis.
Extends existing monitoring infrastructure with comprehensive health monitoring and circuit breakers.
"""
import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import argparse
import time
import importlib.util

# Use existing infrastructure
from src.infrastructure.protocols.logger import LoggerProtocol
from src.infrastructure.monitoring.service_states import ServiceStateTracker, ServiceState, ServiceStateInfo
from src.infrastructure.monitoring.performance_monitor import monitor_async_performance, get_performance_monitor


class HealthStatus(Enum):
    """Health status levels that map to ServiceState."""
    HEALTHY = "healthy"      # Maps to ServiceState.RUNNING
    WARNING = "warning"      # Maps to ServiceState.RUNNING with warnings
    CRITICAL = "critical"    # Maps to ServiceState.ERROR or FAILED
    UNKNOWN = "unknown"      # Maps to ServiceState.CREATED


@dataclass 
class ServiceHealth:
    """Health information for a service that extends ServiceStateInfo."""
    service_name: str
    status: HealthStatus
    response_time_ms: float
    error_message: Optional[str] = None
    dependencies_healthy: bool = True
    last_check: Optional[str] = None
    additional_info: Dict[str, Any] = None
    service_state: Optional[ServiceState] = None  # Link to ServiceState


@dataclass
class DependencyCheck:
    """Dependency validation result."""
    dependency_name: str
    is_available: bool
    error_message: Optional[str] = None
    check_type: str = "import"


class ServiceHealthMonitor:
    """
    Comprehensive health monitoring for PACER services.
    Extends existing ServiceStateTracker with health monitoring and circuit breaker functionality.
    """
    
    def __init__(self, project_root: Optional[str] = None, logger: Optional[LoggerProtocol] = None, state_tracker: Optional[ServiceStateTracker] = None):
        # Use existing infrastructure
        self.logger = logger or self._create_default_logger()
        self.state_tracker = state_tracker or ServiceStateTracker()
        self.project_root = Path(project_root) if project_root else Path.cwd()
        
        # Add project root to Python path for imports
        if str(self.project_root) not in sys.path:
            sys.path.insert(0, str(self.project_root))
        
        # Service definitions
        self.pacer_services = self._define_pacer_services()
        self.external_dependencies = self._define_external_dependencies()
        
        # Health tracking (extends ServiceStateTracker)
        self.health_history: Dict[str, List[ServiceHealth]] = {}
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}
        
        # Register monitor service in state tracker
        self.monitor_service_name = "ServiceHealthMonitor"
        self.state_tracker.set_state(
            self.monitor_service_name,
            ServiceState.INITIALIZING,
            metadata={"project_root": str(self.project_root)}
        )
        
        self.logger.info("🏥 Service Health Monitor initialized with existing infrastructure")
        
        # Set ready state
        self.state_tracker.set_state(self.monitor_service_name, ServiceState.READY)
    
    def _create_default_logger(self) -> LoggerProtocol:
        """Create a default logger that implements LoggerProtocol."""
        import logging
        
        class DefaultLogger:
            def __init__(self):
                self._logger = logging.getLogger(__name__)
                
            def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.debug(message, extra=extra)
                
            def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.info(message, extra=extra)
                
            def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.warning(message, extra=extra)
                
            def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.error(message, extra=extra)
                
            def exception(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.exception(message, extra=extra)
        
        return DefaultLogger()
    
    def _define_pacer_services(self) -> Dict[str, Dict[str, Any]]:
        """Define PACER services and their dependencies."""
        return {
            "PacerOrchestratorService": {
                "module": "src.services.pacer.pacer_orchestrator_service",
                "class": "PacerOrchestratorService",
                "dependencies": ["logging", "asyncio", "playwright"],
                "critical": True
            },
            "BrowserService": {
                "module": "src.services.pacer.browser.browser_service", 
                "class": "BrowserService",
                "dependencies": ["playwright", "logging"],
                "critical": True
            },
            "DocketProcessingOrchestratorService": {
                "module": "src.services.pacer.docket_processing_orchestrator_service",
                "class": "PacerDocketProcessingOrchestratorService", 
                "dependencies": ["logging", "asyncio"],
                "critical": True
            },
            "ConfigurationService": {
                "module": "src.services.pacer.configuration_service",
                "class": "PacerConfigurationService",
                "dependencies": ["json", "pathlib"],
                "critical": True
            },
            "RelevanceService": {
                "module": "src.services.pacer.relevance_service",
                "class": "RelevanceService",
                "dependencies": ["logging"],
                "critical": True
            },
            "FileManagementService": {
                "module": "src.services.pacer.file_management_service",
                "class": "PacerFileManagementService",
                "dependencies": ["pathlib", "logging"],
                "critical": False
            },
            "NavigationService": {
                "module": "src.services.pacer.navigation_service",
                "class": "PacerNavigationService",
                "dependencies": ["playwright", "logging"],
                "critical": False
            },
            "CaseProcessingService": {
                "module": "src.services.pacer.case_processing_service",
                "class": "PacerCaseProcessingService", 
                "dependencies": ["logging"],
                "critical": False
            },
            "DownloadOrchestrationService": {
                "module": "src.services.pacer.download_orchestration_service",
                "class": "PacerDownloadOrchestrationService",
                "dependencies": ["asyncio", "logging"],
                "critical": False
            },
            "HtmlProcessingService": {
                "module": "src.services.pacer.html_processing_service",
                "class": "PacerHtmlProcessingService",
                "dependencies": ["logging"],
                "critical": False
            }
        }
    
    def _define_external_dependencies(self) -> Dict[str, Dict[str, Any]]:
        """Define external dependencies and their checks."""
        return {
            "playwright": {
                "import_path": "playwright.async_api",
                "check_method": "check_playwright",
                "critical": True
            },
            "dependency_injector": {
                "import_path": "dependency_injector",
                "check_method": "check_dependency_injector", 
                "critical": True
            },
            "pydantic": {
                "import_path": "pydantic",
                "check_method": "check_pydantic",
                "critical": True
            },
            "boto3": {
                "import_path": "boto3",
                "check_method": "check_aws_sdk",
                "critical": False
            },
            "asyncio": {
                "import_path": "asyncio",
                "check_method": "check_asyncio",
                "critical": True
            },
            "pathlib": {
                "import_path": "pathlib",
                "check_method": "check_pathlib",
                "critical": True
            }
        }
    
    async def check_all_services(self) -> Dict[str, ServiceHealth]:
        """Check health of all PACER services with performance monitoring and state tracking."""
        async with monitor_async_performance("service_health_check_all", "monitoring"):
            self.state_tracker.set_state(
                self.monitor_service_name,
                ServiceState.RUNNING,
                metadata={"operation": "health_check_all"}
            )
            
            self.logger.info("🏥 HEALTH CHECK: Starting comprehensive service health check")
            
            results = {}
            
            # Check external dependencies first
            dependency_results = await self._check_dependencies()
            
            # Check individual services
            for service_name, service_config in self.pacer_services.items():
                self.logger.info(f"🔍 CHECKING: {service_name}")
                
                async with monitor_async_performance(f"service_health_check_{service_name}", "monitoring"):
                    start_time = time.time()
                    try:
                        health = await self._check_service_health(service_name, service_config, dependency_results)
                        response_time = (time.time() - start_time) * 1000
                        health.response_time_ms = response_time
                        
                        # Map health status to service state
                        service_state = self._map_health_to_service_state(health.status)
                        health.service_state = service_state
                        
                        # Update service state in tracker
                        self.state_tracker.set_state(
                            service_name,
                            service_state,
                            error_message=health.error_message,
                            metadata={
                                "health_status": health.status.value,
                                "response_time_ms": response_time,
                                "dependencies_healthy": health.dependencies_healthy
                            }
                        )
                        
                    except Exception as e:
                        response_time = (time.time() - start_time) * 1000
                        health = ServiceHealth(
                            service_name=service_name,
                            status=HealthStatus.CRITICAL,
                            response_time_ms=response_time,
                            error_message=str(e),
                            dependencies_healthy=False,
                            last_check=datetime.now().isoformat(),
                            service_state=ServiceState.FAILED
                        )
                        
                        # Update service state for failed health check
                        self.state_tracker.set_state(
                            service_name,
                            ServiceState.FAILED,
                            error_message=str(e),
                            metadata={"health_check_failed": True, "response_time_ms": response_time}
                        )
                
                results[service_name] = health
                
                # Update health history
                if service_name not in self.health_history:
                    self.health_history[service_name] = []
                self.health_history[service_name].append(health)
                
                # Keep only last 10 health checks
                self.health_history[service_name] = self.health_history[service_name][-10:]
            
            # Update monitor state with summary
            healthy_count = sum(1 for h in results.values() if h.status == HealthStatus.HEALTHY)
            total_count = len(results)
            
            self.state_tracker.set_state(
                self.monitor_service_name,
                ServiceState.RUNNING,
                metadata={
                    "operation": "health_check_complete",
                    "services_checked": total_count,
                    "healthy_services": healthy_count,
                    "health_percentage": (healthy_count / total_count) * 100 if total_count > 0 else 0
                }
            )
            
            self.logger.info("✅ HEALTH CHECK: Service health check completed")
            return results
    
    def _map_health_to_service_state(self, health_status: HealthStatus) -> ServiceState:
        """Map health status to service state."""
        mapping = {
            HealthStatus.HEALTHY: ServiceState.RUNNING,
            HealthStatus.WARNING: ServiceState.RUNNING,  # Running but with warnings
            HealthStatus.CRITICAL: ServiceState.ERROR,
            HealthStatus.UNKNOWN: ServiceState.CREATED
        }
        return mapping.get(health_status, ServiceState.UNKNOWN)
    
    async def _check_dependencies(self) -> Dict[str, DependencyCheck]:
        """Check external dependencies."""
        self.logger.info("🔗 CHECKING: External dependencies")
        
        results = {}
        
        for dep_name, dep_config in self.external_dependencies.items():
            try:
                # Try to import the module
                import_path = dep_config["import_path"]
                
                try:
                    if "." in import_path:
                        module_parts = import_path.split(".")
                        module = __import__(import_path)
                        for part in module_parts[1:]:
                            module = getattr(module, part)
                    else:
                        module = __import__(import_path)
                    
                    # Run specific check if available
                    check_method = dep_config.get("check_method")
                    if check_method and hasattr(self, check_method):
                        additional_check = getattr(self, check_method)
                        await additional_check(module)
                    
                    results[dep_name] = DependencyCheck(
                        dependency_name=dep_name,
                        is_available=True,
                        check_type="import"
                    )
                    
                except ImportError as e:
                    results[dep_name] = DependencyCheck(
                        dependency_name=dep_name,
                        is_available=False,
                        error_message=f"Import failed: {e}",
                        check_type="import"
                    )
                
            except Exception as e:
                results[dep_name] = DependencyCheck(
                    dependency_name=dep_name,
                    is_available=False,
                    error_message=f"Check failed: {e}",
                    check_type="import"
                )
        
        return results
    
    async def _check_service_health(self, service_name: str, service_config: Dict[str, Any], 
                                  dependency_results: Dict[str, DependencyCheck]) -> ServiceHealth:
        """Check health of a specific service."""
        
        # Check service dependencies
        service_deps = service_config.get("dependencies", [])
        deps_healthy = all(
            dependency_results.get(dep, DependencyCheck(dep, False)).is_available 
            for dep in service_deps
        )
        
        if not deps_healthy:
            return ServiceHealth(
                service_name=service_name,
                status=HealthStatus.CRITICAL,
                response_time_ms=0,
                error_message="Service dependencies not available",
                dependencies_healthy=False,
                last_check=datetime.now().isoformat()
            )
        
        # Try to import and validate the service
        try:
            module_path = service_config["module"]
            class_name = service_config["class"]
            
            # Import the module
            spec = importlib.util.spec_from_file_location(
                module_path.replace(".", "_"),
                self.project_root / (module_path.replace(".", "/") + ".py")
            )
            
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # Check if class exists
                if hasattr(module, class_name):
                    service_class = getattr(module, class_name)
                    
                    # Basic validation - can we inspect the class?
                    import inspect
                    if inspect.isclass(service_class):
                        status = HealthStatus.HEALTHY
                        error_msg = None
                    else:
                        status = HealthStatus.WARNING
                        error_msg = "Service class validation failed"
                else:
                    status = HealthStatus.CRITICAL
                    error_msg = f"Class {class_name} not found in module"
            else:
                status = HealthStatus.CRITICAL
                error_msg = f"Module {module_path} not found"
                
        except Exception as e:
            status = HealthStatus.CRITICAL
            error_msg = f"Service import failed: {e}"
        
        return ServiceHealth(
            service_name=service_name,
            status=status,
            response_time_ms=0,  # Will be set by caller
            error_message=error_msg,
            dependencies_healthy=deps_healthy,
            last_check=datetime.now().isoformat()
        )
    
    async def check_playwright(self, module) -> None:
        """Check Playwright-specific functionality."""
        try:
            from playwright.async_api import async_playwright
            # This is just a validation that we can import the async playwright
            pass
        except Exception as e:
            raise Exception(f"Playwright validation failed: {e}")
    
    async def check_dependency_injector(self, module) -> None:
        """Check dependency injector functionality."""
        try:
            from dependency_injector import containers, providers
            # Validate basic DI functionality
            pass
        except Exception as e:
            raise Exception(f"Dependency injector validation failed: {e}")
    
    async def check_pydantic(self, module) -> None:
        """Check Pydantic functionality."""
        try:
            from pydantic import BaseModel
            # Basic validation
            pass
        except Exception as e:
            raise Exception(f"Pydantic validation failed: {e}")
    
    async def check_aws_sdk(self, module) -> None:
        """Check AWS SDK functionality."""
        try:
            import boto3
            # Try to create a session (doesn't require credentials)
            session = boto3.Session()
        except Exception as e:
            raise Exception(f"AWS SDK validation failed: {e}")
    
    async def check_asyncio(self, module) -> None:
        """Check asyncio functionality."""
        try:
            # Test basic asyncio functionality
            await asyncio.sleep(0.001)
        except Exception as e:
            raise Exception(f"Asyncio validation failed: {e}")
    
    async def check_pathlib(self, module) -> None:
        """Check pathlib functionality."""
        try:
            from pathlib import Path
            # Basic path validation
            test_path = Path(".")
            test_path.exists()
        except Exception as e:
            raise Exception(f"Pathlib validation failed: {e}")
    
    def get_circuit_breaker_status(self, service_name: str) -> Dict[str, Any]:
        """Get circuit breaker status for a service."""
        if service_name not in self.circuit_breakers:
            return {
                "state": "closed",
                "failure_count": 0,
                "last_failure": None,
                "next_retry": None
            }
        
        return self.circuit_breakers[service_name]
    
    def update_circuit_breaker(self, service_name: str, is_healthy: bool) -> None:
        """Update circuit breaker state based on health check."""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = {
                "state": "closed",
                "failure_count": 0,
                "last_failure": None,
                "next_retry": None
            }
        
        breaker = self.circuit_breakers[service_name]
        
        if is_healthy:
            # Reset on success
            breaker["state"] = "closed"
            breaker["failure_count"] = 0
            breaker["last_failure"] = None
            breaker["next_retry"] = None
        else:
            # Increment failure count
            breaker["failure_count"] += 1
            breaker["last_failure"] = datetime.now().isoformat()
            
            # Open circuit after 3 failures
            if breaker["failure_count"] >= 3:
                breaker["state"] = "open"
                # Set retry time (30 seconds from now)
                retry_time = datetime.now() + timedelta(seconds=30)
                breaker["next_retry"] = retry_time.isoformat()
    
    def generate_health_report(self, health_results: Dict[str, ServiceHealth]) -> str:
        """Generate a comprehensive health report."""
        
        # Calculate summary statistics
        total_services = len(health_results)
        healthy_services = sum(1 for h in health_results.values() if h.status == HealthStatus.HEALTHY)
        warning_services = sum(1 for h in health_results.values() if h.status == HealthStatus.WARNING)
        critical_services = sum(1 for h in health_results.values() if h.status == HealthStatus.CRITICAL)
        
        # Calculate average response time
        response_times = [h.response_time_ms for h in health_results.values() if h.response_time_ms > 0]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        report_lines = [
            "=" * 80,
            "PACER SERVICES HEALTH DASHBOARD",
            "=" * 80,
            f"Generated: {datetime.now().isoformat()}",
            f"Total services checked: {total_services}",
            ""
        ]
        
        # Summary section
        report_lines.extend([
            "HEALTH SUMMARY:",
            f"  ✅ Healthy: {healthy_services} ({(healthy_services/total_services)*100:.1f}%)",
            f"  ⚠️ Warning: {warning_services} ({(warning_services/total_services)*100:.1f}%)",
            f"  ❌ Critical: {critical_services} ({(critical_services/total_services)*100:.1f}%)",
            f"  📊 Avg Response Time: {avg_response_time:.1f}ms",
            ""
        ])
        
        # Critical services first
        critical_list = [name for name, health in health_results.items() if health.status == HealthStatus.CRITICAL]
        if critical_list:
            report_lines.extend([
                "🚨 CRITICAL SERVICES:",
                *[f"  ❌ {name}: {health_results[name].error_message}" for name in critical_list],
                ""
            ])
        
        # Warning services
        warning_list = [name for name, health in health_results.items() if health.status == HealthStatus.WARNING]
        if warning_list:
            report_lines.extend([
                "⚠️ WARNING SERVICES:",
                *[f"  ⚠️ {name}: {health_results[name].error_message or 'No specific error'}" for name in warning_list],
                ""
            ])
        
        # Detailed service status
        report_lines.extend([
            "DETAILED SERVICE STATUS:",
            ""
        ])
        
        for service_name, health in sorted(health_results.items()):
            status_icon = {
                HealthStatus.HEALTHY: "✅",
                HealthStatus.WARNING: "⚠️", 
                HealthStatus.CRITICAL: "❌",
                HealthStatus.UNKNOWN: "❓"
            }.get(health.status, "❓")
            
            report_lines.append(f"{status_icon} {service_name}")
            report_lines.append(f"    Status: {health.status.value}")
            report_lines.append(f"    Response Time: {health.response_time_ms:.1f}ms")
            report_lines.append(f"    Dependencies: {'✅' if health.dependencies_healthy else '❌'}")
            
            if health.error_message:
                report_lines.append(f"    Error: {health.error_message}")
            
            # Circuit breaker status
            breaker_status = self.get_circuit_breaker_status(service_name)
            if breaker_status["state"] != "closed":
                report_lines.append(f"    Circuit Breaker: {breaker_status['state']} (failures: {breaker_status['failure_count']})")
            
            report_lines.append("")
        
        return "\n".join(report_lines)
    
    def export_health_json(self, health_results: Dict[str, ServiceHealth], output_path: str) -> None:
        """Export health results to JSON."""
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_services": len(health_results),
                "healthy": sum(1 for h in health_results.values() if h.status == HealthStatus.HEALTHY),
                "warning": sum(1 for h in health_results.values() if h.status == HealthStatus.WARNING),
                "critical": sum(1 for h in health_results.values() if h.status == HealthStatus.CRITICAL)
            },
            "services": {name: asdict(health) for name, health in health_results.items()},
            "circuit_breakers": self.circuit_breakers
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        self.logger.info(f"📊 EXPORTED: Health data to {output_path}")


async def main():
    """Main CLI interface for service health monitoring."""
    parser = argparse.ArgumentParser(description="PACER Service Health Dashboard")
    parser.add_argument("--project-root", help="Project root directory")
    parser.add_argument("--output", help="Output report file")
    parser.add_argument("--json", help="Export health data to JSON file")
    parser.add_argument("--watch", action="store_true", help="Continuous monitoring mode")
    parser.add_argument("--interval", type=int, default=60, help="Watch interval in seconds")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    try:
        # Create logger that implements LoggerProtocol
        import logging
        
        class CLILogger:
            def __init__(self):
                self._logger = logging.getLogger(__name__)
                
            def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.debug(message, extra=extra)
                
            def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.info(message, extra=extra)
                
            def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.warning(message, extra=extra)
                
            def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.error(message, extra=extra)
                
            def exception(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.exception(message, extra=extra)
        
        cli_logger = CLILogger()
        monitor = ServiceHealthMonitor(args.project_root, logger=cli_logger)
        
        if args.watch:
            # Continuous monitoring mode
            print(f"🔄 Starting continuous health monitoring (interval: {args.interval}s)")
            print("Press Ctrl+C to stop")
            
            while True:
                try:
                    health_results = await monitor.check_all_services()
                    
                    # Update circuit breakers
                    for service_name, health in health_results.items():
                        is_healthy = health.status == HealthStatus.HEALTHY
                        monitor.update_circuit_breaker(service_name, is_healthy)
                    
                    # Display summary
                    healthy_count = sum(1 for h in health_results.values() if h.status == HealthStatus.HEALTHY)
                    total_count = len(health_results)
                    
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] Health: {healthy_count}/{total_count} services healthy")
                    
                    # Alert on critical services
                    critical_services = [name for name, health in health_results.items() 
                                       if health.status == HealthStatus.CRITICAL]
                    if critical_services:
                        print(f"🚨 CRITICAL: {', '.join(critical_services)}")
                    
                    await asyncio.sleep(args.interval)
                    
                except KeyboardInterrupt:
                    break
        else:
            # Single health check
            health_results = await monitor.check_all_services()
            
            # Update circuit breakers
            for service_name, health in health_results.items():
                is_healthy = health.status == HealthStatus.HEALTHY
                monitor.update_circuit_breaker(service_name, is_healthy)
            
            # Generate report
            report = monitor.generate_health_report(health_results)
            
            # Output report
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"Report saved to: {args.output}")
            else:
                print(report)
            
            # Export JSON if requested
            if args.json:
                monitor.export_health_json(health_results, args.json)
            
            # Exit with appropriate code
            critical_count = sum(1 for h in health_results.values() if h.status == HealthStatus.CRITICAL)
            sys.exit(0 if critical_count == 0 else 1)
        
    except KeyboardInterrupt:
        print("\nHealth monitoring stopped by user")
        sys.exit(0)
    except Exception as e:
        logging.error(f"Service health check failed: {e}")
        sys.exit(3)


if __name__ == "__main__":
    asyncio.run(main())