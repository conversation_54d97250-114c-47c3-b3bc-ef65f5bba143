#!/usr/bin/env python3
"""
Script that returns ALL dockets as a list for GROUP 3 and INVALID validation.
"""
import os
import re
import argparse
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.table import Table

console = Console()

def extract_court_docket(json_path):
    """Extract court_id and docket_num from JSON file."""
    import json
    try:
        with open(json_path, 'r') as f:
            data = json.load(f)
            court_id = data.get('court_id')
            docket_num = data.get('docket_num')
            if court_id and docket_num:
                return {"court_id": court_id, "docket_num": docket_num}
    except Exception:
        pass
    return None

def main():
    parser = argparse.ArgumentParser(
        description="Returns all dockets for validation purposes"
    )
    parser.add_argument(
        "--date",
        default=datetime.now().strftime("%Y%m%d"),
        help="Date in YYYYMMDD format (default: today)"
    )
    parser.add_argument(
        "--format",
        choices=["json", "table", "csv"],
        default="json",
        help="Output format (default: json)"
    )
    
    args = parser.parse_args()
    
    # Support for configurable data directory
    data_base = os.environ.get('LEXGENIUS_DATA_DIR', 'data')
    dockets_dir = Path(data_base) / args.date / "dockets"
    
    if not dockets_dir.exists():
        console.print(f"[red]Error: Directory {dockets_dir} does not exist[/red]")
        return
    
    all_dockets = []
    
    with console.status(f"[bold green]Scanning dockets in {dockets_dir}..."):
        for json_file in dockets_dir.glob("*.json"):
            result = extract_court_docket(json_file)
            if result:
                all_dockets.append(result)
    
    # Sort by court_id then docket_num
    all_dockets.sort(key=lambda x: (x["court_id"], x["docket_num"]))
    
    console.print(f"[green]Found {len(all_dockets)} dockets[/green]")
    
    # Output based on format
    if args.format == "json":
        import json
        print(json.dumps(all_dockets, indent=2))
    elif args.format == "table":
        table = Table(title=f"Dockets for {args.date}")
        table.add_column("Court ID", style="cyan")
        table.add_column("Docket Number", style="magenta")
        
        for docket in all_dockets:
            table.add_row(docket["court_id"], docket["docket_num"])
        
        console.print(table)
    elif args.format == "csv":
        print("court_id,docket_num")
        for docket in all_dockets:
            print(f"{docket['court_id']},{docket['docket_num']}")

if __name__ == "__main__":
    main()