#!/bin/bash
# Development Environment Setup Script for LexGenius
# Usage: ./scripts/dev_setup.sh [--skip-env] [--skip-hooks] [--help]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default options
SKIP_ENV=false
SKIP_HOOKS=false
SHOW_HELP=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --skip-env)
      SKIP_ENV=true
      shift
      ;;
    --skip-hooks)
      SKIP_HOOKS=true
      shift
      ;;
    --help|-h)
      SHOW_HELP=true
      shift
      ;;
    *)
      echo -e "${RED}Unknown option: $1${NC}"
      SHOW_HELP=true
      shift
      ;;
  esac
done

# Show help
if [ "$SHOW_HELP" = true ]; then
  echo -e "${BLUE}LexGenius Development Environment Setup${NC}"
  echo ""
  echo "Usage: ./scripts/dev_setup.sh [OPTIONS]"
  echo ""
  echo "Options:"
  echo "  --skip-env    Skip conda environment creation/update"
  echo "  --skip-hooks  Skip pre-commit hooks installation"
  echo "  --help, -h    Show this help message"
  echo ""
  echo "This script will:"
  echo "  1. Create/update the lexgenius conda environment"
  echo "  2. Install development dependencies"
  echo "  3. Set up pre-commit hooks"
  echo "  4. Validate the development environment"
  exit 0
fi

echo -e "${BLUE}🚀 LexGenius Development Environment Setup${NC}"
echo "========================================"

# Function to check if command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Function to print status
print_status() {
  echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
  echo -e "${RED}✗${NC} $1"
}

# Check prerequisites
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

if ! command_exists conda && ! command_exists mamba; then
  print_error "Neither conda nor mamba found. Please install Miniconda or Mambaforge."
  exit 1
fi

if command_exists mamba; then
  CONDA_CMD="mamba"
  print_status "Found mamba (faster conda alternative)"
else
  CONDA_CMD="conda"
  print_status "Found conda"
fi

# Get project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Environment setup
if [ "$SKIP_ENV" = false ]; then
  echo ""
  echo -e "${BLUE}🐍 Setting up conda environment...${NC}"
  
  # Check if environment exists
  if $CONDA_CMD env list | grep -q "lexgenius"; then
    echo "Updating existing lexgenius environment..."
    $CONDA_CMD env update -f environment.yml
  else
    echo "Creating new lexgenius environment..."
    $CONDA_CMD env create -f environment.yml
  fi
  
  print_status "Conda environment configured"
else
  print_warning "Skipping conda environment setup"
fi

# Activate environment for the rest of the script
echo ""
echo -e "${BLUE}🔧 Activating environment and installing dev tools...${NC}"

# Source conda and activate environment
eval "$($CONDA_CMD shell.bash hook)"
$CONDA_CMD activate lexgenius

# Verify we're in the right environment
if [[ "$CONDA_DEFAULT_ENV" != "lexgenius" ]]; then
  print_error "Failed to activate lexgenius environment"
  exit 1
fi

print_status "Activated lexgenius environment"

# Install development dependencies with pip (if not already installed)
echo "Installing/updating development dependencies..."
pip install --upgrade \
  pre-commit \
  black \
  isort \
  ruff \
  bandit \
  safety \
  pydocstyle \
  detect-secrets \
  yamllint \
  commitizen \
  pytest-xdist \
  pytest-mock

print_status "Development dependencies installed"

# Pre-commit hooks setup
if [ "$SKIP_HOOKS" = false ]; then
  echo ""
  echo -e "${BLUE}🪝 Setting up pre-commit hooks...${NC}"
  
  # Install pre-commit hooks
  pre-commit install
  pre-commit install --hook-type commit-msg
  
  # Initialize secrets baseline if it doesn't exist
  if [ ! -f .secrets.baseline ]; then
    echo "Creating secrets baseline..."
    detect-secrets scan --baseline .secrets.baseline
  fi
  
  print_status "Pre-commit hooks installed"
  
  # Optional: Run pre-commit on all files (can be slow)
  echo ""
  read -p "Run pre-commit on all files? This may take a while but ensures everything is properly formatted. (y/N): " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Running pre-commit on all files..."
    pre-commit run --all-files || {
      print_warning "Pre-commit found issues. Run 'pre-commit run --all-files' again to see details."
    }
  fi
else
  print_warning "Skipping pre-commit hooks setup"
fi

# Environment validation
echo ""
echo -e "${BLUE}✅ Validating development environment...${NC}"

# Check Python version
python_version=$(python --version)
print_status "Python: $python_version"

# Check key packages
echo "Checking key development tools:"
packages=("pytest" "black" "isort" "ruff" "mypy" "pre-commit")
for package in "${packages[@]}"; do
  if pip show "$package" >/dev/null 2>&1; then
    version=$(pip show "$package" | grep Version | cut -d' ' -f2)
    print_status "$package: $version"
  else
    print_error "$package: Not installed"
  fi
done

# Check git hooks
if [ -f .git/hooks/pre-commit ] && [ -f .git/hooks/commit-msg ]; then
  print_status "Git hooks: Installed"
else
  print_warning "Git hooks: Not properly installed"
fi

# Test basic functionality
echo ""
echo -e "${BLUE}🧪 Testing basic functionality...${NC}"

# Test pytest
if python -m pytest tests/ --collect-only >/dev/null 2>&1; then
  print_status "Pytest: Can discover tests"
else
  print_warning "Pytest: Issues with test discovery"
fi

# Test black
if echo "x=1" | black --check - >/dev/null 2>&1; then
  print_status "Black: Working"
else
  print_warning "Black: Issues detected"
fi

# Test mypy
if mypy --version >/dev/null 2>&1; then
  print_status "MyPy: Working"
else
  print_warning "MyPy: Issues detected"
fi

# Final summary
echo ""
echo -e "${GREEN}🎉 Development environment setup complete!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Make sure you're in the lexgenius conda environment: conda activate lexgenius"
echo "2. Run tests: python run_tests.py"
echo "3. Check code formatting: pre-commit run --all-files"
echo "4. Start coding! All commits will automatically run quality checks."
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo "  pytest tests/                 # Run tests"
echo "  black src/                    # Format code"
echo "  isort src/                    # Sort imports" 
echo "  ruff check src/               # Lint code"
echo "  mypy src/                     # Type checking"
echo "  pre-commit run --all-files    # Run all quality checks"
echo ""
echo -e "${YELLOW}Note:${NC} Pre-commit hooks are now active and will run automatically on git commit."
echo "If you need to skip hooks temporarily, use: git commit --no-verify"