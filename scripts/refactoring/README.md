# LexGenius Refactoring Automation

This directory contains scripts and prompts for executing Phase I, II, and III of the LexGenius refactoring project in parallel.

## Overview

The refactoring is divided into three phases that can be executed in parallel:

- **Phase I**: Foundation Strengthening - Utility migration and factory patterns
- **Phase II**: Type-Safe Dependency Injection - Protocol interfaces and configuration
- **Phase III**: Performance Monitoring and Lifecycle Management - Monitoring and health checks

## Quick Start

### Run All Phases in Parallel

#### Option 1: Single Working Directory
```bash
# Execute all phases with live execution
python scripts/refactoring/execute_phases_parallel.py

# Dry run to see what would be executed
python scripts/refactoring/execute_phases_parallel.py --dry-run

# Execute specific phases only
python scripts/refactoring/execute_phases_parallel.py --phases 1,3
```

#### Option 2: Git Worktrees (Recommended for Isolation)
```bash
# Execute all phases in separate git worktrees
python scripts/refactoring/execute_phases_parallel_worktrees.py

# Dry run with worktrees
python scripts/refactoring/execute_phases_parallel_worktrees.py --dry-run

# Execute specific phases with worktrees
python scripts/refactoring/execute_phases_parallel_worktrees.py --phases 1,2

# Skip automatic merging (for manual review)
python scripts/refactoring/execute_phases_parallel_worktrees.py --skip-merge
```

### Validate Phase Completion

```bash
# Validate Phase I completion
python scripts/refactoring/validate_phase_1.py

# Validate Phase II completion  
python scripts/refactoring/validate_phase_2.py

# Validate Phase III completion
python scripts/refactoring/validate_phase_3.py
```

## Files

### Execution Scripts

- **`execute_phases_parallel.py`** - Main parallel execution script with Rich progress bars
- **`execute_phases_parallel_worktrees.py`** - Enhanced version using git worktrees for isolation
- **`validate_phase_1.py`** - Comprehensive Phase I validation
- **`validate_phase_2.py`** - Comprehensive Phase II validation
- **`validate_phase_3.py`** - Comprehensive Phase III validation

### LLM Prompts

- **`prompts/phase_1_foundation_prompt.md`** - Detailed Phase I implementation guide
- **`prompts/phase_2_dependency_injection_prompt.md`** - Detailed Phase II implementation guide
- **`prompts/phase_3_performance_monitoring_prompt.md`** - Detailed Phase III implementation guide

## Phase Breakdown

### Phase I: Foundation Strengthening

**Duration**: ~2.5 hours | **Tasks**: 6 | **Dependencies**: Can run independently

Key Tasks:
- Delete redundant `src/core/html/` directory
- Migrate `async_decorators.py` to `AsyncDecoratorService`
- Migrate `cleanup_utils.py` to `ResourceCleanupService`
- Migrate `performance_monitor.py` to `PerformanceMonitorService`
- Implement `EnhancedServiceFactory` with type safety
- Create `ServiceRegistry` for dependency management

**Target Locations**:
```
src/services/utils/
├── async_decorator_service.py
├── resource_cleanup_service.py
└── performance_monitor_service.py

src/infrastructure/
├── factories/enhanced_service_factory.py
└── registry/service_registry.py
```

### Phase II: Type-Safe Dependency Injection

**Duration**: ~2 hours | **Tasks**: 5 | **Dependencies**: Can run in parallel with Phase I

Key Tasks:
- Implement `StorageProtocol` with async methods
- Implement `LoggerProtocol` with standardized methods  
- Add runtime type checking with `@runtime_checkable`
- Create type-safe `ServiceConfig` dataclasses
- Enhance configuration injection with protocol validation

**Target Locations**:
```
src/infrastructure/
├── protocols/
│   ├── storage_protocol.py
│   └── logger_protocol.py
├── validation/type_checker.py
├── config/service_config.py
└── injection/config_injector.py
```

### Phase III: Performance Monitoring and Lifecycle Management

**Duration**: ~2.5 hours | **Tasks**: 6 | **Dependencies**: Can run in parallel with others

Key Tasks:
- Create `PerformanceMetrics` dataclass with comprehensive metrics
- Implement `PerformanceMonitoringService` optimized for Mac M4
- Add async operation decorators for automatic monitoring
- Implement `ServiceState` enum and tracking
- Create `LifecycleManager` for service coordination
- Add health check system with Rich console integration

**Target Locations**:
```
src/infrastructure/
├── monitoring/performance_metrics.py
└── lifecycle/service_state.py

src/services/
├── monitoring/performance_monitoring_service.py
└── lifecycle/lifecycle_manager.py
```

## Usage Examples

### Using the Parallel Execution Script

```bash
# Full execution with all phases
python scripts/refactoring/execute_phases_parallel.py

# Dry run to see execution plan
python scripts/refactoring/execute_phases_parallel.py --dry-run

# Execute only Phase I and II
python scripts/refactoring/execute_phases_parallel.py --phases 1,2 

# Verbose logging
python scripts/refactoring/execute_phases_parallel.py --verbose
```

### Validation Workflow

```bash
# Run validation for completed phase
python scripts/refactoring/validate_phase_1.py

# Check validation reports
ls reports/validation/
```

### Integration with LLM Tools

Each phase prompt is designed for use with LLM tools like Claude Code:

1. Copy the appropriate prompt from `prompts/`
2. Provide the prompt to your LLM tool
3. Execute the generated code
4. Run validation script to verify completion

## Output and Reporting

### Execution Reports

The parallel execution script provides:
- Real-time progress bars for each phase
- Rich formatted console output
- Error handling and recovery
- Final execution summary

### Validation Reports

Each validation script generates:
- Comprehensive JSON reports in `reports/validation/`
- Rich formatted console tables
- Pass/fail status for each check
- Detailed error information for failures

### Sample Output

```
🚀 Phase Execution Dashboard

Phase I   | ✅ 6/6 tasks | 2.3s | Foundation
Phase II  | ✅ 5/5 tasks | 1.8s | Dependency Injection  
Phase III | ✅ 6/6 tasks | 2.1s | Performance Monitoring

✅ All phases completed successfully!
```

## Success Criteria

### Phase I Complete When:
- [ ] `src/core/html/` directory removed
- [ ] All utility services extend `ComponentImplementation`
- [ ] Factory patterns implemented with type safety
- [ ] Service registry operational
- [ ] All tests passing
- [ ] No imports point to old locations

### Phase II Complete When:
- [ ] All protocols marked `@runtime_checkable`
- [ ] Type-safe configuration dataclasses implemented
- [ ] Dependency injection enhanced with protocol validation
- [ ] Runtime type checking functional
- [ ] MyPy type checking passes

### Phase III Complete When:
- [ ] Performance monitoring with Mac M4 optimization
- [ ] Lifecycle manager with dependency resolution
- [ ] Health check system operational
- [ ] Rich console integration working
- [ ] Graceful shutdown procedures implemented
- [ ] Performance reports exportable

## Error Handling

The scripts include comprehensive error handling:

- **Dependency Validation**: Checks for missing dependencies before execution
- **Rollback Capability**: Can revert changes if validation fails
- **Partial Execution**: Continues with other phases if one fails
- **Rich Error Display**: Clear error messages with context

## Integration Notes

- All phases maintain backward compatibility during migration
- Services can be migrated incrementally without breaking functionality
- Feature flags may be used for gradual rollout
- Enhanced patterns from later phases can be applied to earlier migrated services

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed via `conda activate lexgenius`
2. **Permission Errors**: Run with appropriate file system permissions
3. **Test Failures**: Run `python run_tests.py` to identify specific issues
4. **Type Checking**: Run `mypy src/` to identify type issues

### Getting Help

- Check validation reports in `reports/validation/`
- Review error logs in script output
- Use `--dry-run` mode to preview changes
- Run individual validation scripts to isolate issues

## Contributing

When modifying these scripts:

1. Follow Rich console formatting patterns
2. Maintain comprehensive error handling
3. Update validation scripts for new requirements
4. Test with both dry-run and live execution modes
5. Ensure backward compatibility during migrations