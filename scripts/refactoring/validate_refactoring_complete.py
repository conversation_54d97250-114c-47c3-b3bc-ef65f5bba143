#!/usr/bin/env python3
"""
Comprehensive Refactoring Validation Script

Validates that all phases completed successfully and generates tests for new infrastructure.
"""

import asyncio
import sys
import subprocess
from pathlib import Path
from typing import Dict, List, Set, Optional
from dataclasses import dataclass
import json

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

@dataclass
class ValidationResult:
    component: str
    passed: bool
    details: str
    severity: str = "info"  # info, warning, error

class RefactoringValidator:
    """Validates refactoring completion and generates tests."""
    
    def __init__(self, console: Console):
        self.console = console
        self.project_root = project_root
        self.results: List[ValidationResult] = []
        
    def validate_infrastructure_files(self) -> List[ValidationResult]:
        """Validate that all expected infrastructure files were created."""
        expected_files = {
            "src/infrastructure/protocols/storage_protocol.py": "Storage Protocol",
            "src/infrastructure/protocols/logger_protocol.py": "Logger Protocol", 
            "src/infrastructure/config/service_configs.py": "Service Configuration",
            "src/infrastructure/config/injection.py": "Configuration Injection",
            "src/infrastructure/monitoring/performance_metrics.py": "Performance Metrics",
            "src/infrastructure/monitoring/service_states.py": "Service States",
            "src/services/infrastructure/async_decorator_service.py": "Async Decorators"
        }
        
        results = []
        for file_path, component in expected_files.items():
            full_path = self.project_root / file_path
            if full_path.exists():
                # Check if file has substantial content
                with open(full_path, 'r') as f:
                    content = f.read()
                if len(content) > 100:  # Basic content check
                    results.append(ValidationResult(
                        component=component,
                        passed=True,
                        details=f"✅ Created with {len(content)} characters"
                    ))
                else:
                    results.append(ValidationResult(
                        component=component,
                        passed=False,
                        details="❌ File exists but appears empty",
                        severity="warning"
                    ))
            else:
                results.append(ValidationResult(
                    component=component,
                    passed=False,
                    details="❌ File not found",
                    severity="error"
                ))
        
        return results
    
    def validate_imports(self) -> List[ValidationResult]:
        """Validate that new files can be imported without errors."""
        import_tests = [
            ("src.infrastructure.protocols.storage_protocol", "StorageProtocol"),
            ("src.infrastructure.protocols.logger_protocol", "LoggerProtocol"),
            ("src.infrastructure.config.service_configs", "ServiceConfig"),
            ("src.infrastructure.config.injection", "ConfigInjector"),
            ("src.infrastructure.monitoring.performance_metrics", "PerformanceMetrics"),
            ("src.infrastructure.monitoring.service_states", "ServiceState"),
            ("src.services.infrastructure.async_decorator_service", "AsyncDecoratorService")
        ]
        
        results = []
        for module_path, class_name in import_tests:
            try:
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                results.append(ValidationResult(
                    component=f"Import {class_name}",
                    passed=True,
                    details=f"✅ Successfully imported {class_name}"
                ))
            except ImportError as e:
                results.append(ValidationResult(
                    component=f"Import {class_name}",
                    passed=False,
                    details=f"❌ Import failed: {e}",
                    severity="error"
                ))
            except AttributeError as e:
                results.append(ValidationResult(
                    component=f"Import {class_name}",
                    passed=False,
                    details=f"❌ Class not found: {e}",
                    severity="error"
                ))
        
        return results
    
    def validate_protocol_compliance(self) -> List[ValidationResult]:
        """Validate that protocols are properly defined with @runtime_checkable."""
        results = []
        
        try:
            from src.infrastructure.protocols.storage_protocol import StorageProtocol
            import inspect
            
            # Check if it's a Protocol
            if hasattr(StorageProtocol, '__protocol_attrs__'):
                results.append(ValidationResult(
                    component="StorageProtocol Structure",
                    passed=True,
                    details="✅ Properly defined as Protocol"
                ))
            else:
                results.append(ValidationResult(
                    component="StorageProtocol Structure",
                    passed=False,
                    details="❌ Not properly defined as Protocol",
                    severity="warning"
                ))
                
        except Exception as e:
            results.append(ValidationResult(
                component="StorageProtocol Validation",
                passed=False,
                details=f"❌ Validation failed: {e}",
                severity="error"
            ))
        
        return results
    
    def generate_test_files(self) -> List[ValidationResult]:
        """Generate test files for new infrastructure components."""
        results = []
        test_dir = self.project_root / "tests" / "unit" / "infrastructure"
        test_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate test for StorageProtocol
        storage_test = '''
import pytest
from unittest.mock import AsyncMock
from src.infrastructure.protocols.storage_protocol import StorageProtocol

class MockStorage:
    """Mock implementation of StorageProtocol for testing."""
    
    async def get(self, key: str):
        return f"value_for_{key}"
    
    async def put(self, key: str, value):
        return None
    
    async def delete(self, key: str):
        return True
    
    async def list_keys(self, prefix: str = ""):
        return [f"{prefix}key1", f"{prefix}key2"]
    
    async def exists(self, key: str):
        return True

@pytest.mark.asyncio
async def test_storage_protocol_compliance():
    """Test that MockStorage implements StorageProtocol."""
    storage = MockStorage()
    
    # Test all protocol methods
    assert await storage.get("test_key") == "value_for_test_key"
    assert await storage.put("test_key", "test_value") is None
    assert await storage.delete("test_key") is True
    assert await storage.exists("test_key") is True
    
    keys = await storage.list_keys("prefix_")
    assert "prefix_key1" in keys
    assert "prefix_key2" in keys

@pytest.mark.asyncio
async def test_storage_protocol_runtime_check():
    """Test runtime protocol checking."""
    storage = MockStorage()
    assert isinstance(storage, StorageProtocol)
'''
        
        test_file = test_dir / "test_storage_protocol.py"
        with open(test_file, 'w') as f:
            f.write(storage_test.strip())
        
        results.append(ValidationResult(
            component="Storage Protocol Tests",
            passed=True,
            details=f"✅ Generated test file: {test_file}"
        ))
        
        # Generate test for PerformanceMetrics
        metrics_test = '''
import pytest
import time
from src.infrastructure.monitoring.performance_metrics import PerformanceMetrics, ServiceHealthStatus

def test_performance_metrics_creation():
    """Test PerformanceMetrics dataclass creation."""
    metrics = PerformanceMetrics(service_name="test_service")
    
    assert metrics.service_name == "test_service"
    assert metrics.start_time is not None
    assert metrics.end_time is None
    assert metrics.success is True
    assert metrics.operations_count == 0

def test_performance_metrics_finish():
    """Test finishing a performance metrics operation."""
    metrics = PerformanceMetrics(service_name="test_service")
    
    # Simulate some work
    time.sleep(0.01)
    
    # Finish the operation
    metrics.finish(success=True)
    
    assert metrics.end_time is not None
    assert metrics.duration_seconds is not None
    assert metrics.duration_seconds > 0
    assert metrics.success is True

def test_performance_metrics_failure():
    """Test metrics for failed operations."""
    metrics = PerformanceMetrics(service_name="test_service")
    
    metrics.finish(success=False, error_message="Test error")
    
    assert metrics.success is False
    assert metrics.error_message == "Test error"

def test_service_health_status():
    """Test ServiceHealthStatus dataclass."""
    health = ServiceHealthStatus(service_name="test_service")
    
    assert health.service_name == "test_service"
    assert health.healthy is True
    assert health.error_count == 0
    assert health.uptime_seconds == 0.0
'''
        
        metrics_test_file = test_dir / "test_performance_metrics.py"
        with open(metrics_test_file, 'w') as f:
            f.write(metrics_test.strip())
        
        results.append(ValidationResult(
            component="Performance Metrics Tests",
            passed=True,
            details=f"✅ Generated test file: {metrics_test_file}"
        ))
        
        # Generate test for ServiceState
        states_test = '''
import pytest
from datetime import datetime
from src.infrastructure.monitoring.service_states import ServiceState, ServiceStateTracker, ServiceStateInfo

def test_service_state_enum():
    """Test ServiceState enum values."""
    assert ServiceState.CREATED
    assert ServiceState.RUNNING
    assert ServiceState.STOPPED
    assert ServiceState.ERROR

def test_service_state_tracker():
    """Test ServiceStateTracker functionality."""
    tracker = ServiceStateTracker()
    
    # Set initial state
    tracker.set_state("test_service", ServiceState.CREATED)
    
    # Check state
    state_info = tracker.get_state("test_service")
    assert state_info is not None
    assert state_info.service_name == "test_service"
    assert state_info.current_state == ServiceState.CREATED
    assert state_info.previous_state is None
    
    # Transition to running
    tracker.set_state("test_service", ServiceState.RUNNING)
    
    # Check updated state
    state_info = tracker.get_state("test_service")
    assert state_info.current_state == ServiceState.RUNNING
    assert state_info.previous_state == ServiceState.CREATED

def test_service_health_check():
    """Test service health checking."""
    tracker = ServiceStateTracker()
    
    # Service in running state should be healthy
    tracker.set_state("healthy_service", ServiceState.RUNNING)
    assert tracker.is_healthy("healthy_service") is True
    
    # Service in error state should not be healthy
    tracker.set_state("unhealthy_service", ServiceState.ERROR)
    assert tracker.is_healthy("unhealthy_service") is False
    
    # Unknown service should not be healthy
    assert tracker.is_healthy("unknown_service") is False

def test_services_in_state():
    """Test getting services by state."""
    tracker = ServiceStateTracker()
    
    tracker.set_state("service1", ServiceState.RUNNING)
    tracker.set_state("service2", ServiceState.RUNNING)
    tracker.set_state("service3", ServiceState.ERROR)
    
    running_services = tracker.get_services_in_state(ServiceState.RUNNING)
    assert "service1" in running_services
    assert "service2" in running_services
    assert "service3" not in running_services
    
    error_services = tracker.get_services_in_state(ServiceState.ERROR)
    assert "service3" in error_services
'''
        
        states_test_file = test_dir / "test_service_states.py"
        with open(states_test_file, 'w') as f:
            f.write(states_test.strip())
        
        results.append(ValidationResult(
            component="Service States Tests",
            passed=True,
            details=f"✅ Generated test file: {states_test_file}"
        ))
        
        return results
    
    def run_generated_tests(self) -> List[ValidationResult]:
        """Run the generated tests to validate they work."""
        results = []
        test_files = [
            "tests/unit/infrastructure/test_storage_protocol.py",
            "tests/unit/infrastructure/test_performance_metrics.py", 
            "tests/unit/infrastructure/test_service_states.py"
        ]
        
        for test_file in test_files:
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pytest", test_file, "-v"
                ], cwd=self.project_root, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    results.append(ValidationResult(
                        component=f"Test Execution: {Path(test_file).name}",
                        passed=True,
                        details="✅ All tests passed"
                    ))
                else:
                    results.append(ValidationResult(
                        component=f"Test Execution: {Path(test_file).name}",
                        passed=False,
                        details=f"❌ Tests failed:\n{result.stdout}\n{result.stderr}",
                        severity="error"
                    ))
            except subprocess.TimeoutExpired:
                results.append(ValidationResult(
                    component=f"Test Execution: {Path(test_file).name}",
                    passed=False,
                    details="❌ Test execution timed out",
                    severity="warning"
                ))
            except Exception as e:
                results.append(ValidationResult(
                    component=f"Test Execution: {Path(test_file).name}",
                    passed=False,
                    details=f"❌ Test execution error: {e}",
                    severity="error"
                ))
        
        return results
    
    def generate_integration_tests(self) -> List[ValidationResult]:
        """Generate integration tests that use multiple components together."""
        results = []
        integration_dir = self.project_root / "tests" / "integration" / "infrastructure"
        integration_dir.mkdir(parents=True, exist_ok=True)
        
        integration_test = '''
import pytest
import asyncio
from src.infrastructure.monitoring.performance_metrics import PerformanceMetrics
from src.infrastructure.monitoring.service_states import ServiceStateTracker, ServiceState
from src.services.infrastructure.async_decorator_service import AsyncDecoratorService

@pytest.mark.asyncio
async def test_infrastructure_integration():
    """Test integration between monitoring and service components."""
    
    # Initialize components
    state_tracker = ServiceStateTracker()
    decorator_service = AsyncDecoratorService()
    
    # Set up service state
    service_name = "test_integration_service"
    state_tracker.set_state(service_name, ServiceState.INITIALIZING)
    
    # Create performance metrics
    metrics = PerformanceMetrics(service_name=service_name)
    
    # Simulate service starting
    state_tracker.set_state(service_name, ServiceState.RUNNING)
    
    # Test async decorator functionality
    @decorator_service.timeout(1.0)
    async def test_operation():
        await asyncio.sleep(0.1)
        return "success"
    
    # Execute operation
    result = await test_operation()
    assert result == "success"
    
    # Finish metrics
    metrics.finish(success=True)
    
    # Verify final state
    assert state_tracker.is_healthy(service_name)
    assert metrics.success is True
    assert metrics.duration_seconds is not None

@pytest.mark.asyncio
async def test_error_handling_integration():
    """Test error handling across infrastructure components."""
    
    state_tracker = ServiceStateTracker()
    decorator_service = AsyncDecoratorService()
    service_name = "error_test_service"
    
    # Start service
    state_tracker.set_state(service_name, ServiceState.RUNNING)
    metrics = PerformanceMetrics(service_name=service_name)
    
    # Test retry decorator
    attempt_count = 0
    
    @decorator_service.retry(max_attempts=3, delay=0.01)
    async def failing_operation():
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            raise ValueError("Simulated failure")
        return "finally_success"
    
    # Should succeed after retries
    result = await failing_operation()
    assert result == "finally_success"
    assert attempt_count == 3
    
    # Test timeout handling
    @decorator_service.timeout(0.05)
    async def slow_operation():
        await asyncio.sleep(0.1)
        return "should_timeout"
    
    with pytest.raises(asyncio.TimeoutError):
        await slow_operation()
    
    # Update state to error and finish metrics
    state_tracker.set_state(service_name, ServiceState.ERROR, error_message="Timeout occurred")
    metrics.finish(success=False, error_message="Operation timed out")
    
    # Verify error state
    assert not state_tracker.is_healthy(service_name)
    assert metrics.success is False
'''
        
        integration_file = integration_dir / "test_infrastructure_integration.py"
        with open(integration_file, 'w') as f:
            f.write(integration_test.strip())
        
        results.append(ValidationResult(
            component="Infrastructure Integration Tests",
            passed=True,
            details=f"✅ Generated integration test: {integration_file}"
        ))
        
        return results
    
    async def validate_all(self) -> Dict[str, List[ValidationResult]]:
        """Run all validation checks."""
        validation_steps = {
            "Infrastructure Files": self.validate_infrastructure_files,
            "Import Validation": self.validate_imports,
            "Protocol Compliance": self.validate_protocol_compliance,
            "Test Generation": self.generate_test_files,
            "Integration Tests": self.generate_integration_tests,
            "Test Execution": self.run_generated_tests
        }
        
        all_results = {}
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            for step_name, step_func in validation_steps.items():
                task = progress.add_task(f"Running {step_name}...", total=None)
                
                try:
                    results = step_func()
                    all_results[step_name] = results
                    self.results.extend(results)
                    
                    passed_count = sum(1 for r in results if r.passed)
                    progress.update(task, description=f"✅ {step_name}: {passed_count}/{len(results)} passed")
                    
                except Exception as e:
                    error_result = ValidationResult(
                        component=step_name,
                        passed=False,
                        details=f"❌ Validation step failed: {e}",
                        severity="error"
                    )
                    all_results[step_name] = [error_result]
                    self.results.append(error_result)
                    progress.update(task, description=f"❌ {step_name}: Failed")
        
        return all_results
    
    def display_results(self, results: Dict[str, List[ValidationResult]]):
        """Display validation results in a formatted table."""
        
        # Summary statistics
        total_checks = len(self.results)
        passed_checks = sum(1 for r in self.results if r.passed)
        failed_checks = total_checks - passed_checks
        
        # Create summary panel
        summary = f"""
🔍 **Validation Summary**
  
📊 **Statistics:**
  • Total Checks: {total_checks}
  • ✅ Passed: {passed_checks}
  • ❌ Failed: {failed_checks}
  • Success Rate: {(passed_checks/total_checks)*100:.1f}%
"""
        
        self.console.print(Panel(summary.strip(), title="Refactoring Validation Results", border_style="blue"))
        
        # Detailed results table
        table = Table(title="Detailed Validation Results")
        table.add_column("Category", style="cyan", no_wrap=True)
        table.add_column("Component", style="magenta")
        table.add_column("Status", style="green")
        table.add_column("Details", style="white")
        
        for category, category_results in results.items():
            for i, result in enumerate(category_results):
                status_color = "green" if result.passed else "red"
                status_icon = "✅" if result.passed else "❌"
                
                category_display = category if i == 0 else ""
                
                table.add_row(
                    category_display,
                    result.component,
                    f"[{status_color}]{status_icon}[/{status_color}]",
                    result.details[:100] + "..." if len(result.details) > 100 else result.details
                )
        
        self.console.print(table)
        
        # Show errors and warnings
        errors = [r for r in self.results if not r.passed and r.severity == "error"]
        warnings = [r for r in self.results if not r.passed and r.severity == "warning"]
        
        if errors:
            self.console.print(f"\n[red]❌ {len(errors)} Critical Errors Found:[/red]")
            for error in errors:
                self.console.print(f"  • {error.component}: {error.details}")
        
        if warnings:
            self.console.print(f"\n[yellow]⚠️  {len(warnings)} Warnings Found:[/yellow]")
            for warning in warnings:
                self.console.print(f"  • {warning.component}: {warning.details}")

@click.command()
@click.option("--generate-tests", is_flag=True, help="Generate test files for new infrastructure")
@click.option("--run-tests", is_flag=True, help="Run generated tests after validation")
@click.option("--verbose", "-v", is_flag=True, help="Show detailed output")
def main(generate_tests: bool, run_tests: bool, verbose: bool):
    """Validate refactoring completion and optionally generate tests."""
    console = Console()
    
    console.print(Panel(
        "🔍 **Refactoring Validation Script**\n\n"
        "This script validates that the refactoring phases completed successfully\n"
        "and generates comprehensive tests for the new infrastructure.",
        title="Validation Starting",
        border_style="blue"
    ))
    
    validator = RefactoringValidator(console)
    
    # Run validation
    try:
        results = asyncio.run(validator.validate_all())
        
        # Display results
        validator.display_results(results)
        
        # Final recommendation
        passed_count = sum(1 for r in validator.results if r.passed)
        total_count = len(validator.results)
        success_rate = (passed_count / total_count) * 100
        
        if success_rate >= 90:
            console.print(f"\n[green]🎉 Excellent! Refactoring validation passed with {success_rate:.1f}% success rate.[/green]")
            console.print("[green]✅ Your infrastructure is ready for production use.[/green]")
        elif success_rate >= 75:
            console.print(f"\n[yellow]⚠️  Good progress! {success_rate:.1f}% success rate with some issues to address.[/yellow]")
            console.print("[yellow]🔧 Review the warnings and errors above.[/yellow]")
        else:
            console.print(f"\n[red]❌ Significant issues found. Only {success_rate:.1f}% success rate.[/red]")
            console.print("[red]🚨 Please address the critical errors before proceeding.[/red]")
            return 1
        
        return 0
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Validation interrupted by user[/yellow]")
        return 130
    except Exception as e:
        console.print(f"\n[red]Unexpected error during validation: {e}[/red]")
        return 1

if __name__ == "__main__":
    sys.exit(main())