#!/usr/bin/env python3
"""
Phase 3: Performance Metrics Implementation
Creates PerformanceMetrics dataclass for monitoring service performance.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 3 Performance Metrics task."""
    print("Implementing PerformanceMetrics dataclass...")
    
    # Create the metrics file
    metrics_content = '''
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from datetime import datetime
import time

@dataclass
class PerformanceMetrics:
    """Performance metrics for service monitoring."""
    service_name: str
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    duration_seconds: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    operations_count: int = 0
    
    def finish(self, success: bool = True, error_message: Optional[str] = None):
        """Mark the operation as finished."""
        self.end_time = time.time()
        self.duration_seconds = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return {
            'service_name': self.service_name,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration_seconds': self.duration_seconds,
            'success': self.success,
            'error_message': self.error_message,
            'memory_usage_mb': self.memory_usage_mb,
            'cpu_usage_percent': self.cpu_usage_percent,
            'operations_count': self.operations_count
        }

@dataclass 
class ServiceHealthStatus:
    """Health status for a service."""
    service_name: str
    healthy: bool = True
    last_check: datetime = field(default_factory=datetime.utcnow)
    error_count: int = 0
    uptime_seconds: float = 0.0
    recent_metrics: List[PerformanceMetrics] = field(default_factory=list)
'''
    
    metrics_file = project_root / "src" / "infrastructure" / "monitoring" / "performance_metrics.py"
    metrics_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(metrics_file, 'w') as f:
        f.write(metrics_content.strip())
    
    print(f"✅ Created PerformanceMetrics at {metrics_file}")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)