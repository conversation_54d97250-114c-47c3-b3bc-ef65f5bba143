#!/usr/bin/env python3
"""
Phase 1: Delete Redundant HTML Directory
Removes the redundant src/core/html/ directory that was migrated to services.
"""

import sys
import shutil
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 1 HTML directory deletion task."""
    print("Deleting redundant src/core/html/ directory...")
    
    html_dir = project_root / "src" / "core" / "html"
    
    if html_dir.exists():
        # Move to archive instead of deleting completely
        archive_dir = project_root / "archive" / "legacy_html"
        archive_dir.mkdir(parents=True, exist_ok=True)
        
        shutil.move(str(html_dir), str(archive_dir / "html"))
        print(f"✅ Moved {html_dir} to {archive_dir / 'html'}")
    else:
        print(f"✅ Directory {html_dir} does not exist (already cleaned)")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)