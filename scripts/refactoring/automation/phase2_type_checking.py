#!/usr/bin/env python3
"""
Phase 2: Runtime Type Checking
Implements @runtime_checkable protocols for better type safety.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 2 Runtime Type Checking task."""
    print("Implementing @runtime_checkable protocols...")
    
    # Update the storage protocol to be runtime checkable
    storage_protocol_file = project_root / "src" / "infrastructure" / "protocols" / "storage_protocol.py"
    
    if storage_protocol_file.exists():
        with open(storage_protocol_file, 'r') as f:
            content = f.read()
        
        # Add runtime_checkable decorator
        updated_content = content.replace(
            'from typing import Protocol, Any, Optional, Dict, List',
            'from typing import Protocol, runtime_checkable, Any, Optional, Dict, List'
        ).replace(
            'class StorageProtocol(Protocol):',
            '@runtime_checkable\nclass StorageProtocol(Protocol):'
        )
        
        with open(storage_protocol_file, 'w') as f:
            f.write(updated_content)
        
        print(f"✅ Updated StorageProtocol with @runtime_checkable")
    
    # Update the logger protocol to be runtime checkable
    logger_protocol_file = project_root / "src" / "infrastructure" / "protocols" / "logger_protocol.py"
    
    if logger_protocol_file.exists():
        with open(logger_protocol_file, 'r') as f:
            content = f.read()
        
        # Add runtime_checkable decorator
        updated_content = content.replace(
            'from typing import Protocol, Any, Optional, Dict',
            'from typing import Protocol, runtime_checkable, Any, Optional, Dict'
        ).replace(
            'class LoggerProtocol(Protocol):',
            '@runtime_checkable\nclass LoggerProtocol(Protocol):'
        )
        
        with open(logger_protocol_file, 'w') as f:
            f.write(updated_content)
        
        print(f"✅ Updated LoggerProtocol with @runtime_checkable")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)