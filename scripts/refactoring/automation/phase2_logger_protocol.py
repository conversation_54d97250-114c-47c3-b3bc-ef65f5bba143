#!/usr/bin/env python3
"""
Phase 2: Logger Protocol Implementation
Creates LoggerProtocol with standardized logging methods.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 2 Logger Protocol task."""
    print("Creating LoggerProtocol with standardized methods...")
    
    # Create the protocol file
    protocol_content = '''
from typing import Protocol, Any, Optional, Dict
from abc import abstractmethod

class LoggerProtocol(Protocol):
    """Protocol for standardized logging operations."""
    
    @abstractmethod
    def info(self, message: str, **kwargs: Any) -> None:
        """Log info message."""
        ...
    
    @abstractmethod 
    def error(self, message: str, **kwargs: Any) -> None:
        """Log error message."""
        ...
    
    @abstractmethod
    def warning(self, message: str, **kwargs: Any) -> None:
        """Log warning message."""
        ...
    
    @abstractmethod
    def debug(self, message: str, **kwargs: Any) -> None:
        """Log debug message."""
        ...
    
    @abstractmethod
    def exception(self, message: str, **kwargs: Any) -> None:
        """Log exception with traceback."""
        ...
'''
    
    protocol_file = project_root / "src" / "infrastructure" / "protocols" / "logger_protocol.py"
    protocol_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(protocol_file, 'w') as f:
        f.write(protocol_content.strip())
    
    print(f"✅ Created LoggerProtocol at {protocol_file}")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)