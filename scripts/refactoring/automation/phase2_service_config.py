#!/usr/bin/env python3
"""
Phase 2: Service Configuration Classes
Creates type-safe ServiceConfig dataclasses for dependency injection.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 2 Service Config task."""
    print("Creating type-safe ServiceConfig dataclasses...")
    
    # Create the config classes file
    config_content = '''
from dataclasses import dataclass
from typing import Any, Optional, Dict
from pydantic import BaseModel

@dataclass
class ServiceConfig:
    """Base configuration for all services."""
    service_name: str
    enabled: bool = True
    timeout_seconds: int = 30
    retry_attempts: int = 3
    log_level: str = "INFO"
    
class DatabaseConfig(BaseModel):
    """Database service configuration."""
    host: str = "localhost"
    port: int = 5432
    database: str = "lexgenius"
    username: Optional[str] = None
    password: Optional[str] = None
    pool_size: int = 10
    
class CacheConfig(BaseModel):
    """Cache service configuration."""
    host: str = "localhost"
    port: int = 6379
    ttl_seconds: int = 3600
    max_connections: int = 100
    
class AIServiceConfig(BaseModel):
    """AI service configuration."""
    model_name: str = "gpt-4"
    api_key: Optional[str] = None
    max_tokens: int = 4000
    temperature: float = 0.7
'''
    
    config_file = project_root / "src" / "infrastructure" / "config" / "service_configs.py"
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_file, 'w') as f:
        f.write(config_content.strip())
    
    print(f"✅ Created ServiceConfig classes at {config_file}")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)