#!/usr/bin/env python3
"""
Phase 1: Migrate Async Decorators
Migrates async_decorators.py to AsyncDecoratorService in the service architecture.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 1 async decorators migration."""
    print("Migrating async_decorators.py to AsyncDecoratorService...")
    
    # Create the service file
    service_content = '''
import asyncio
import functools
import logging
from typing import Any, Callable, Optional, TypeVar
from src.infrastructure.patterns.component_base import AsyncServiceBase

F = TypeVar('F', bound=Callable[..., Any])

class AsyncDecoratorService(AsyncServiceBase):
    """Service for managing async operation decorators."""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
    
    def retry(self, max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
        """Decorator for retrying async operations."""
        def decorator(func: F) -> F:
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                last_exception = None
                current_delay = delay
                
                for attempt in range(max_attempts):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        if attempt < max_attempts - 1:
                            self.logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {current_delay}s...")
                            await asyncio.sleep(current_delay)
                            current_delay *= backoff
                        else:
                            self.logger.error(f"All {max_attempts} attempts failed")
                
                raise last_exception
            return wrapper
        return decorator
    
    def timeout(self, seconds: float):
        """Decorator for adding timeout to async operations."""
        def decorator(func: F) -> F:
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                try:
                    return await asyncio.wait_for(func(*args, **kwargs), timeout=seconds)
                except asyncio.TimeoutError:
                    self.logger.error(f"Operation {func.__name__} timed out after {seconds}s")
                    raise
            return wrapper
        return decorator
    
    async def _execute_action(self, data: Any) -> Any:
        """Required by AsyncServiceBase - not used for decorator service."""
        return data
'''
    
    service_file = project_root / "src" / "services" / "infrastructure" / "async_decorator_service.py"
    service_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(service_file, 'w') as f:
        f.write(service_content.strip())
    
    print(f"✅ Created AsyncDecoratorService at {service_file}")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)