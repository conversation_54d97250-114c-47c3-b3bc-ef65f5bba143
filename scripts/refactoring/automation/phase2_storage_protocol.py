#!/usr/bin/env python3
"""
Phase 2: Storage Protocol Implementation
Creates StorageProtocol with async methods for type-safe storage abstraction.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 2 Storage Protocol task."""
    print("Creating StorageProtocol with async methods...")
    
    # Create the protocol file
    protocol_content = '''
from typing import Protocol, Any, Optional, Dict, List
from abc import abstractmethod

class StorageProtocol(Protocol):
    """Protocol for async storage operations."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get item by key."""
        ...
    
    @abstractmethod 
    async def put(self, key: str, value: Any) -> None:
        """Store item with key."""
        ...
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete item by key."""
        ...
    
    @abstractmethod
    async def list_keys(self, prefix: str = "") -> List[str]:
        """List keys with optional prefix."""
        ...
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        ...
'''
    
    protocol_file = project_root / "src" / "infrastructure" / "protocols" / "storage_protocol.py"
    protocol_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(protocol_file, 'w') as f:
        f.write(protocol_content.strip())
    
    print(f"✅ Created StorageProtocol at {protocol_file}")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)