#!/usr/bin/env python3
"""
Phase 2: Configuration Injection
Implements type-safe configuration injection for services.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 2 Configuration Injection task."""
    print("Implementing type-safe configuration injection...")
    
    # Create config injection utility
    injection_content = '''
from typing import TypeVar, Type, Dict, Any
from functools import wraps
from src.infrastructure.config.service_configs import ServiceConfig

T = TypeVar('T', bound=ServiceConfig)

class ConfigInjector:
    """Type-safe configuration injection utility."""
    
    def __init__(self):
        self._configs: Dict[str, Any] = {}
    
    def register_config(self, config_class: Type[T], config_instance: T) -> None:
        """Register a configuration instance."""
        self._configs[config_class.__name__] = config_instance
    
    def get_config(self, config_class: Type[T]) -> T:
        """Get a configuration instance by type."""
        config_name = config_class.__name__
        if config_name not in self._configs:
            raise ValueError(f"Configuration {config_name} not registered")
        return self._configs[config_name]
    
    def inject_config(self, config_class: Type[T]):
        """Decorator to inject configuration into service methods."""
        def decorator(func):
            @wraps(func)
            def wrapper(self, *args, **kwargs):
                config = self.get_config(config_class)
                return func(self, config, *args, **kwargs)
            return wrapper
        return decorator

# Global injector instance
config_injector = ConfigInjector()
'''
    
    injection_file = project_root / "src" / "infrastructure" / "config" / "injection.py"
    injection_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(injection_file, 'w') as f:
        f.write(injection_content.strip())
    
    print(f"✅ Created configuration injection system at {injection_file}")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)