#!/usr/bin/env python3
"""
Phase 3: Service State Implementation
Creates ServiceState enum and tracking for service lifecycle management.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Implementation for Phase 3 Service States task."""
    print("Creating ServiceState enum and tracking...")
    
    # Create the states file
    states_content = '''
from enum import Enum, auto
from dataclasses import dataclass, field
from typing import Dict, Optional, Set
from datetime import datetime
import logging

class ServiceState(Enum):
    """Enum representing the state of a service."""
    CREATED = auto()
    INITIALIZING = auto()
    READY = auto()
    RUNNING = auto()
    PAUSING = auto()
    PAUSED = auto()
    STOPPING = auto()
    STOPPED = auto()
    ERROR = auto()
    FAILED = auto()

@dataclass
class ServiceStateInfo:
    """Information about a service's current state."""
    service_name: str
    current_state: ServiceState
    previous_state: Optional[ServiceState] = None
    state_entered_at: datetime = field(default_factory=datetime.utcnow)
    error_message: Optional[str] = None
    metadata: Dict = field(default_factory=dict)

class ServiceStateTracker:
    """Tracks state changes for services."""
    
    def __init__(self):
        self._states: Dict[str, ServiceStateInfo] = {}
        self._state_history: Dict[str, list] = {}
        self.logger = logging.getLogger(__name__)
    
    def set_state(self, service_name: str, new_state: ServiceState, 
                  error_message: Optional[str] = None, **metadata):
        """Set the state for a service."""
        current_info = self._states.get(service_name)
        previous_state = current_info.current_state if current_info else None
        
        state_info = ServiceStateInfo(
            service_name=service_name,
            current_state=new_state,
            previous_state=previous_state,
            error_message=error_message,
            metadata=metadata
        )
        
        self._states[service_name] = state_info
        
        # Add to history
        if service_name not in self._state_history:
            self._state_history[service_name] = []
        self._state_history[service_name].append(state_info)
        
        self.logger.info(f"Service {service_name}: {previous_state} -> {new_state}")
    
    def get_state(self, service_name: str) -> Optional[ServiceStateInfo]:
        """Get current state info for a service."""
        return self._states.get(service_name)
    
    def get_services_in_state(self, state: ServiceState) -> Set[str]:
        """Get all services currently in the specified state."""
        return {
            name for name, info in self._states.items() 
            if info.current_state == state
        }
    
    def is_healthy(self, service_name: str) -> bool:
        """Check if a service is in a healthy state."""
        info = self.get_state(service_name)
        if not info:
            return False
        return info.current_state in {ServiceState.READY, ServiceState.RUNNING}
'''
    
    states_file = project_root / "src" / "infrastructure" / "monitoring" / "service_states.py"
    states_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(states_file, 'w') as f:
        f.write(states_content.strip())
    
    print(f"✅ Created ServiceState tracking at {states_file}")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)