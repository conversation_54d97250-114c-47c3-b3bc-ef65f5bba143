#!/usr/bin/env python3
"""
Parallel Phase Execution Script for LexGenius Refactoring

This script executes Phase I, II, and III refactoring tasks in parallel using
multiprocessing with proper error handling and progress monitoring.

Usage:
    python scripts/refactoring/execute_phases_parallel.py [--dry-run] [--phases 1,2,3]
"""

import asyncio
import concurrent.futures
import logging
import multiprocessing as mp
import sys
import time
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import click
from rich.console import Console
from rich.logging import RichHandler
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TextColumn,
    TimeElapsedColumn,
)
from rich.table import Table

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class PhaseStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class PhaseResult:
    phase_id: str
    status: PhaseStatus
    duration: float
    tasks_completed: int
    tasks_failed: int
    error_message: Optional[str] = None


@dataclass
class PhaseTask:
    task_id: str
    description: str
    estimated_duration: int  # minutes
    dependencies: List[str]  # task IDs that must complete first


class PhaseExecutor:
    """Manages parallel execution of refactoring phases."""
    
    def __init__(self, console: Console, dry_run: bool = False):
        self.console = console
        self.dry_run = dry_run
        self.results: Dict[str, PhaseResult] = {}
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format="%(message)s",
            datefmt="[%X]",
            handlers=[RichHandler(console=console, rich_tracebacks=True)]
        )
        self.logger = logging.getLogger("phase_executor")

    def get_phase_tasks(self) -> Dict[str, List[PhaseTask]]:
        """Define all tasks for each phase."""
        return {
            "phase_1": [
                PhaseTask(
                    "delete_redundant_html",
                    "Delete redundant src/core/html/ directory",
                    estimated_duration=5,
                    dependencies=[]
                ),
                PhaseTask(
                    "migrate_async_decorators",
                    "Migrate async_decorators.py to AsyncDecoratorService",
                    estimated_duration=20,
                    dependencies=[]
                ),
                PhaseTask(
                    "migrate_cleanup_utils",
                    "Migrate cleanup_utils.py to ResourceCleanupService",
                    estimated_duration=25,
                    dependencies=[]
                ),
                PhaseTask(
                    "migrate_performance_monitor",
                    "Migrate performance_monitor.py to PerformanceMonitorService",
                    estimated_duration=30,
                    dependencies=[]
                ),
                PhaseTask(
                    "enhance_factory_patterns",
                    "Implement type-safe service factories",
                    estimated_duration=35,
                    dependencies=["migrate_async_decorators"]
                ),
                PhaseTask(
                    "implement_service_registry",
                    "Create lightweight service registry",
                    estimated_duration=40,
                    dependencies=["enhance_factory_patterns"]
                ),
            ],
            "phase_2": [
                PhaseTask(
                    "implement_storage_protocol",
                    "Create StorageProtocol with async methods",
                    estimated_duration=20,
                    dependencies=[]
                ),
                PhaseTask(
                    "implement_logger_protocol",
                    "Create LoggerProtocol with standardized methods",
                    estimated_duration=15,
                    dependencies=[]
                ),
                PhaseTask(
                    "add_runtime_type_checking",
                    "Implement @runtime_checkable protocols",
                    estimated_duration=25,
                    dependencies=["implement_storage_protocol", "implement_logger_protocol"]
                ),
                PhaseTask(
                    "create_service_config_classes",
                    "Create type-safe ServiceConfig dataclasses",
                    estimated_duration=30,
                    dependencies=[]
                ),
                PhaseTask(
                    "enhance_config_injection",
                    "Implement type-safe configuration injection",
                    estimated_duration=35,
                    dependencies=["create_service_config_classes", "add_runtime_type_checking"]
                ),
            ],
            "phase_3": [
                PhaseTask(
                    "create_performance_metrics",
                    "Implement PerformanceMetrics dataclass",
                    estimated_duration=15,
                    dependencies=[]
                ),
                PhaseTask(
                    "implement_monitoring_service",
                    "Create PerformanceMonitoringService",
                    estimated_duration=40,
                    dependencies=["create_performance_metrics"]
                ),
                PhaseTask(
                    "add_monitoring_decorators",
                    "Create async operation decorators for monitoring",
                    estimated_duration=25,
                    dependencies=["implement_monitoring_service"]
                ),
                PhaseTask(
                    "implement_service_states",
                    "Create ServiceState enum and tracking",
                    estimated_duration=20,
                    dependencies=[]
                ),
                PhaseTask(
                    "create_lifecycle_manager",
                    "Implement LifecycleManager for service coordination",
                    estimated_duration=45,
                    dependencies=["implement_service_states"]
                ),
                PhaseTask(
                    "add_health_checks",
                    "Implement health check system",
                    estimated_duration=30,
                    dependencies=["create_lifecycle_manager"]
                ),
            ]
        }

    async def execute_phase_task(self, phase_id: str, task: PhaseTask) -> Tuple[str, bool, str]:
        """Execute a single phase task."""
        if self.dry_run:
            await asyncio.sleep(0.1)  # Simulate work
            return task.task_id, True, f"[DRY RUN] {task.description}"
        
        try:
            # Here you would call the actual LLM prompts or automation scripts
            # For now, we'll simulate the work
            self.logger.info(f"Executing: {task.description}")
            
            # Import and execute the specific task based on task_id
            success = await self._execute_specific_task(phase_id, task)
            
            return task.task_id, success, f"Completed: {task.description}"
            
        except Exception as e:
            return task.task_id, False, f"Failed: {task.description} - {str(e)}"

    async def _execute_specific_task(self, phase_id: str, task: PhaseTask) -> bool:
        """Execute the specific task logic."""
        # This is where you'd integrate with your LLM prompts or automation
        # For now, simulate different execution times
        execution_times = {
            "delete_redundant_html": 0.5,
            "migrate_async_decorators": 2.0,
            "migrate_cleanup_utils": 2.5,
            "implement_storage_protocol": 1.5,
            "create_performance_metrics": 1.0,
        }
        
        await asyncio.sleep(execution_times.get(task.task_id, 1.0))
        return True  # Simulate success

    async def execute_phase(self, phase_id: str, tasks: List[PhaseTask]) -> PhaseResult:
        """Execute all tasks in a phase, respecting dependencies."""
        start_time = time.time()
        completed_tasks = set()
        failed_tasks = set()
        
        with Progress(
            SpinnerColumn(),
            TextColumn(f"[bold blue]{phase_id.upper()}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeElapsedColumn(),
            console=self.console,
        ) as progress:
            
            task_progress = progress.add_task(f"Phase {phase_id[-1]} Tasks", total=len(tasks))
            
            # Execute tasks in dependency order
            remaining_tasks = tasks.copy()
            
            while remaining_tasks:
                # Find tasks with satisfied dependencies
                ready_tasks = [
                    task for task in remaining_tasks
                    if all(dep in completed_tasks for dep in task.dependencies)
                ]
                
                if not ready_tasks:
                    # Check if we're stuck due to failed dependencies
                    blocked_by_failed = any(
                        any(dep in failed_tasks for dep in task.dependencies)
                        for task in remaining_tasks
                    )
                    if blocked_by_failed:
                        # Skip remaining tasks blocked by failures
                        for task in remaining_tasks:
                            failed_tasks.add(task.task_id)
                        break
                    else:
                        # This shouldn't happen with proper dependency definition
                        self.logger.error(f"Circular dependency detected in {phase_id}")
                        break
                
                # Execute ready tasks in parallel
                task_futures = [
                    self.execute_phase_task(phase_id, task)
                    for task in ready_tasks
                ]
                
                results = await asyncio.gather(*task_futures, return_exceptions=True)
                
                for task, result in zip(ready_tasks, results):
                    if isinstance(result, Exception):
                        failed_tasks.add(task.task_id)
                        self.logger.error(f"Task {task.task_id} failed: {result}")
                    else:
                        task_id, success, message = result
                        if success:
                            completed_tasks.add(task_id)
                            self.logger.info(message)
                        else:
                            failed_tasks.add(task_id)
                            self.logger.error(message)
                    
                    remaining_tasks.remove(task)
                    progress.update(task_progress, advance=1)
        
        duration = time.time() - start_time
        
        return PhaseResult(
            phase_id=phase_id,
            status=PhaseStatus.COMPLETED if not failed_tasks else PhaseStatus.FAILED,
            duration=duration,
            tasks_completed=len(completed_tasks),
            tasks_failed=len(failed_tasks),
            error_message=f"Failed tasks: {failed_tasks}" if failed_tasks else None
        )

    async def execute_phases_parallel(self, phase_ids: List[str]) -> Dict[str, PhaseResult]:
        """Execute multiple phases in parallel."""
        all_tasks = self.get_phase_tasks()
        
        self.logger.info(f"Starting parallel execution of phases: {phase_ids}")
        
        # Create phase execution coroutines
        phase_coroutines = [
            self.execute_phase(phase_id, all_tasks[phase_id])
            for phase_id in phase_ids
            if phase_id in all_tasks
        ]
        
        # Execute all phases in parallel
        results = await asyncio.gather(*phase_coroutines, return_exceptions=True)
        
        # Process results
        for phase_id, result in zip(phase_ids, results):
            if isinstance(result, Exception):
                self.results[phase_id] = PhaseResult(
                    phase_id=phase_id,
                    status=PhaseStatus.FAILED,
                    duration=0.0,
                    tasks_completed=0,
                    tasks_failed=0,
                    error_message=str(result)
                )
            else:
                self.results[phase_id] = result
        
        return self.results

    def display_results(self):
        """Display execution results in a formatted table."""
        table = Table(title="Phase Execution Results")
        
        table.add_column("Phase", style="cyan", no_wrap=True)
        table.add_column("Status", style="magenta")
        table.add_column("Duration", style="green")
        table.add_column("Tasks Completed", style="blue")
        table.add_column("Tasks Failed", style="red")
        table.add_column("Notes", style="yellow")
        
        for phase_id, result in self.results.items():
            status_color = {
                PhaseStatus.COMPLETED: "green",
                PhaseStatus.FAILED: "red",
                PhaseStatus.RUNNING: "yellow",
                PhaseStatus.PENDING: "blue"
            }.get(result.status, "white")
            
            table.add_row(
                phase_id.replace("_", " ").title(),
                f"[{status_color}]{result.status.value}[/{status_color}]",
                f"{result.duration:.2f}s",
                str(result.tasks_completed),
                str(result.tasks_failed),
                result.error_message or "✅"
            )
        
        self.console.print(table)


@click.command()
@click.option("--dry-run", is_flag=True, help="Run in dry-run mode without making changes")
@click.option("--phases", default="1,2,3", help="Comma-separated list of phases to run (1,2,3)")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
def main(dry_run: bool, phases: str, verbose: bool):
    """Execute refactoring phases in parallel."""
    console = Console()
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Parse phase selection
    try:
        phase_numbers = [int(p.strip()) for p in phases.split(",")]
        phase_ids = [f"phase_{p}" for p in phase_numbers if 1 <= p <= 3]
    except (ValueError, AttributeError):
        console.print("[red]Error: Invalid phase selection. Use format: 1,2,3[/red]")
        sys.exit(1)
    
    if not phase_ids:
        console.print("[red]Error: No valid phases selected. Choose from: 1, 2, 3[/red]")
        sys.exit(1)
    
    executor = PhaseExecutor(console, dry_run)
    
    # Show execution plan
    console.print(f"\n[bold blue]Execution Plan[/bold blue]")
    console.print(f"Phases: {', '.join(phase_ids)}")
    console.print(f"Mode: {'DRY RUN' if dry_run else 'LIVE EXECUTION'}")
    console.print(f"Parallelization: {'Enabled' if len(phase_ids) > 1 else 'Single Phase'}")
    
    if not dry_run:
        console.print("\n[yellow]⚠️  This will make changes to your codebase![/yellow]")
        if not click.confirm("Continue with live execution?"):
            console.print("Execution cancelled.")
            sys.exit(0)
    
    # Execute phases
    try:
        start_time = time.time()
        results = asyncio.run(executor.execute_phases_parallel(phase_ids))
        total_time = time.time() - start_time
        
        # Display results
        console.print(f"\n[bold green]Execution Complete[/bold green] ({total_time:.2f}s total)")
        executor.display_results()
        
        # Summary
        completed_phases = sum(1 for r in results.values() if r.status == PhaseStatus.COMPLETED)
        failed_phases = sum(1 for r in results.values() if r.status == PhaseStatus.FAILED)
        
        if failed_phases > 0:
            console.print(f"\n[red]❌ {failed_phases} phase(s) failed[/red]")
            sys.exit(1)
        else:
            console.print(f"\n[green]✅ All {completed_phases} phase(s) completed successfully[/green]")
    
    except KeyboardInterrupt:
        console.print("\n[yellow]Execution interrupted by user[/yellow]")
        sys.exit(130)
    except Exception as e:
        console.print(f"\n[red]Unexpected error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()