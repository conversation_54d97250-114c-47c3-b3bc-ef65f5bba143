#!/usr/bin/env python3
"""
Phase III Validation Script

Validates that Phase III refactoring has been completed correctly:
- Performance monitoring service implemented
- Lifecycle management system functional
- Health check system operational
- Rich console integration working
- Mac M4 optimizations in place
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import importlib.util
import subprocess
import json
import time
import psutil

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

console = Console()

class ValidationResult:
    def __init__(self, name: str, passed: bool, message: str, details: Optional[Dict] = None):
        self.name = name
        self.passed = passed
        self.message = message
        self.details = details or {}

class PhaseThreeValidator:
    def __init__(self):
        self.results: List[ValidationResult] = []
        self.project_root = project_root
    
    def validate_monitoring_structure(self) -> ValidationResult:
        """Validate monitoring directory structure and files."""
        try:
            monitoring_path = self.project_root / "src" / "infrastructure" / "monitoring"
            services_monitoring_path = self.project_root / "src" / "services" / "monitoring"
            lifecycle_path = self.project_root / "src" / "infrastructure" / "lifecycle"
            services_lifecycle_path = self.project_root / "src" / "services" / "lifecycle"
            
            expected_files = {
                "infrastructure/monitoring": ["performance_metrics.py"],
                "services/monitoring": ["performance_monitoring_service.py"],
                "infrastructure/lifecycle": ["service_state.py"],
                "services/lifecycle": ["lifecycle_manager.py"]
            }
            
            missing_files = []
            existing_files = []
            
            for dir_path, files in expected_files.items():
                full_dir_path = self.project_root / "src" / dir_path
                for file_name in files:
                    file_path = full_dir_path / file_name
                    if file_path.exists():
                        existing_files.append(f"{dir_path}/{file_name}")
                    else:
                        missing_files.append(f"{dir_path}/{file_name}")
            
            passed = len(missing_files) == 0
            
            details = {
                "existing_files": existing_files,
                "missing_files": missing_files,
                "expected_file_count": sum(len(files) for files in expected_files.values())
            }
            
            message = "Monitoring structure validation " + ("passed" if passed else "failed")
            return ValidationResult("monitoring_structure", passed, message, details)
            
        except Exception as e:
            return ValidationResult("monitoring_structure", False, f"Error: {e}")
    
    def validate_performance_metrics(self) -> ValidationResult:
        """Validate PerformanceMetrics dataclass implementation."""
        try:
            try:
                from src.infrastructure.monitoring.performance_metrics import (
                    PerformanceMetrics, AggregatedMetrics, PerformanceLevel, MetricType
                )
                
                # Check that PerformanceMetrics is a dataclass
                is_dataclass = hasattr(PerformanceMetrics, '__dataclass_fields__')
                
                # Check required fields
                required_fields = ['operation_name', 'start_time', 'end_time', 'duration']
                has_required_fields = all(field in PerformanceMetrics.__dataclass_fields__ for field in required_fields)
                
                # Check enums exist
                has_enums = all(hasattr(enum_class, 'value') for enum_class in [PerformanceLevel, MetricType])
                
                # Check methods exist
                expected_methods = ['performance_level', 'finalize', 'add_custom_metric', 'to_dict']
                has_methods = all(hasattr(PerformanceMetrics, method) for method in expected_methods)
                
                passed = is_dataclass and has_required_fields and has_enums and has_methods
                details = {
                    "is_dataclass": is_dataclass,
                    "has_required_fields": has_required_fields,
                    "has_enums": has_enums,
                    "has_methods": has_methods,
                    "missing_fields": [f for f in required_fields if f not in (PerformanceMetrics.__dataclass_fields__ if is_dataclass else {})],
                    "missing_methods": [m for m in expected_methods if not hasattr(PerformanceMetrics, m)]
                }
                
                message = f"Performance metrics validation {'passed' if passed else 'failed'}"
                return ValidationResult("performance_metrics", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("performance_metrics", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("performance_metrics", False, f"Error: {e}")
    
    def validate_monitoring_service(self) -> ValidationResult:
        """Validate PerformanceMonitoringService implementation."""
        try:
            try:
                from src.services.monitoring.performance_monitoring_service import PerformanceMonitoringService
                
                # Check inheritance
                base_classes = [base.__name__ for base in PerformanceMonitoringService.__bases__]
                extends_base = 'AsyncServiceBase' in base_classes or 'ComponentImplementation' in base_classes
                
                # Check required methods
                expected_methods = [
                    'start_operation_monitoring',
                    'stop_operation_monitoring', 
                    'monitor_operation',
                    'create_performance_decorator',
                    'get_aggregated_metrics',
                    'display_live_metrics'
                ]
                
                has_methods = all(hasattr(PerformanceMonitoringService, method) for method in expected_methods)
                
                # Check async methods
                async_methods = [m for m in expected_methods if asyncio.iscoroutinefunction(getattr(PerformanceMonitoringService, m, None))]
                
                passed = extends_base and has_methods
                details = {
                    "extends_base": extends_base,
                    "has_methods": has_methods,
                    "base_classes": base_classes,
                    "async_methods": async_methods,
                    "missing_methods": [m for m in expected_methods if not hasattr(PerformanceMonitoringService, m)]
                }
                
                message = f"Monitoring service validation {'passed' if passed else 'failed'}"
                return ValidationResult("monitoring_service", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("monitoring_service", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("monitoring_service", False, f"Error: {e}")
    
    def validate_service_state(self) -> ValidationResult:
        """Validate ServiceState and lifecycle components."""
        try:
            try:
                from src.infrastructure.lifecycle.service_state import (
                    ServiceState, ServiceStateTransition, ServiceHealthCheck, ServiceStateTracker
                )
                
                # Check ServiceState enum
                is_enum = hasattr(ServiceState, '__members__')
                expected_states = ['CREATED', 'RUNNING', 'STOPPED', 'FAILED', 'DEGRADED']
                has_expected_states = all(hasattr(ServiceState, state) for state in expected_states)
                
                # Check dataclasses
                is_transition_dataclass = hasattr(ServiceStateTransition, '__dataclass_fields__')
                is_health_dataclass = hasattr(ServiceHealthCheck, '__dataclass_fields__')
                
                # Check ServiceStateTracker methods
                tracker_methods = ['set_state', 'get_state', 'get_transitions', 'record_health_check', 'is_healthy']
                has_tracker_methods = all(hasattr(ServiceStateTracker, method) for method in tracker_methods)
                
                passed = is_enum and has_expected_states and is_transition_dataclass and is_health_dataclass and has_tracker_methods
                details = {
                    "is_enum": is_enum,
                    "has_expected_states": has_expected_states,
                    "is_transition_dataclass": is_transition_dataclass,
                    "is_health_dataclass": is_health_dataclass,
                    "has_tracker_methods": has_tracker_methods,
                    "missing_states": [s for s in expected_states if not hasattr(ServiceState, s)],
                    "missing_tracker_methods": [m for m in tracker_methods if not hasattr(ServiceStateTracker, m)]
                }
                
                message = f"Service state validation {'passed' if passed else 'failed'}"
                return ValidationResult("service_state", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("service_state", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("service_state", False, f"Error: {e}")
    
    def validate_lifecycle_manager(self) -> ValidationResult:
        """Validate LifecycleManager implementation."""
        try:
            try:
                from src.services.lifecycle.lifecycle_manager import LifecycleManager
                
                # Check inheritance
                base_classes = [base.__name__ for base in LifecycleManager.__bases__]
                extends_base = 'AsyncServiceBase' in base_classes or 'ComponentImplementation' in base_classes
                
                # Check required methods
                expected_methods = [
                    'register_service',
                    'start_all_services',
                    'start_service',
                    'stop_service',
                    'shutdown_all_services',
                    'managed_lifecycle',
                    'get_service_status'
                ]
                
                has_methods = all(hasattr(LifecycleManager, method) for method in expected_methods)
                
                # Check for async context manager
                has_context_manager = hasattr(LifecycleManager, 'managed_lifecycle')
                
                passed = extends_base and has_methods and has_context_manager
                details = {
                    "extends_base": extends_base,
                    "has_methods": has_methods,
                    "has_context_manager": has_context_manager,
                    "base_classes": base_classes,
                    "missing_methods": [m for m in expected_methods if not hasattr(LifecycleManager, m)]
                }
                
                message = f"Lifecycle manager validation {'passed' if passed else 'failed'}"
                return ValidationResult("lifecycle_manager", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("lifecycle_manager", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("lifecycle_manager", False, f"Error: {e}")
    
    async def validate_performance_decorator(self) -> ValidationResult:
        """Validate performance monitoring decorator functionality."""
        try:
            try:
                from src.services.monitoring.performance_monitoring_service import PerformanceMonitoringService
                
                # Create service instance
                monitoring_service = PerformanceMonitoringService()
                
                # Test decorator creation
                decorator = monitoring_service.create_performance_decorator("test_operation")
                
                # Test decorator on a simple async function
                @decorator
                async def test_async_function():
                    await asyncio.sleep(0.01)
                    return "test_result"
                
                # Test decorator on a sync function  
                @decorator
                def test_sync_function():
                    time.sleep(0.01)
                    return "sync_result"
                
                # Execute decorated functions
                start_time = time.time()
                async_result = await test_async_function()
                sync_result = test_sync_function()
                execution_time = time.time() - start_time
                
                # Validate results
                async_success = async_result == "test_result"
                sync_success = sync_result == "sync_result"
                reasonable_time = execution_time < 1.0  # Should complete quickly
                
                passed = async_success and sync_success and reasonable_time
                details = {
                    "async_success": async_success,
                    "sync_success": sync_success,
                    "execution_time": execution_time,
                    "reasonable_time": reasonable_time,
                    "decorator_created": decorator is not None
                }
                
                message = f"Performance decorator validation {'passed' if passed else 'failed'}"
                return ValidationResult("performance_decorator", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("performance_decorator", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("performance_decorator", False, f"Error: {e}")
    
    def validate_mac_optimization(self) -> ValidationResult:
        """Validate Mac M4 specific optimizations."""
        try:
            # Check CPU count (M4 should have 8+ cores)
            cpu_count = psutil.cpu_count()
            
            # Check memory (M4 should have significant RAM)
            memory_gb = psutil.virtual_memory().total / (1024**3)
            
            # Check if we're on macOS
            import platform
            is_mac = platform.system() == "Darwin"
            
            # Look for parallel processing patterns in monitoring service
            try:
                from src.services.monitoring.performance_monitoring_service import PerformanceMonitoringService
                import inspect
                
                source = inspect.getsource(PerformanceMonitoringService)
                has_async_patterns = "async with" in source and "await" in source
                has_performance_focus = "cpu_percent" in source and "memory_info" in source
                
            except ImportError:
                has_async_patterns = False
                has_performance_focus = False
            
            mac_optimized = is_mac and cpu_count >= 4 and memory_gb >= 8
            code_optimized = has_async_patterns and has_performance_focus
            
            passed = mac_optimized and code_optimized
            details = {
                "is_mac": is_mac,
                "cpu_count": cpu_count,
                "memory_gb": memory_gb,
                "mac_optimized": mac_optimized,
                "has_async_patterns": has_async_patterns,
                "has_performance_focus": has_performance_focus,
                "code_optimized": code_optimized
            }
            
            message = f"Mac M4 optimization validation {'passed' if passed else 'failed'}"
            return ValidationResult("mac_optimization", passed, message, details)
            
        except Exception as e:
            return ValidationResult("mac_optimization", False, f"Error: {e}")
    
    def validate_rich_integration(self) -> ValidationResult:
        """Validate Rich console integration."""
        try:
            try:
                from src.services.monitoring.performance_monitoring_service import PerformanceMonitoringService
                from src.services.lifecycle.lifecycle_manager import LifecycleManager
                import inspect
                
                # Check for Rich imports and usage
                monitoring_source = inspect.getsource(PerformanceMonitoringService)
                lifecycle_source = inspect.getsource(LifecycleManager)
                
                rich_imports = [
                    "from rich.console import Console",
                    "from rich.table import Table",
                    "from rich.progress import Progress",
                    "from rich.panel import Panel"
                ]
                
                monitoring_has_rich = any(imp in monitoring_source for imp in rich_imports)
                lifecycle_has_rich = any(imp in lifecycle_source for imp in rich_imports)
                
                # Check for console usage
                has_console_usage = "console.print" in monitoring_source or "console.print" in lifecycle_source
                has_progress_bars = "Progress(" in monitoring_source or "Progress(" in lifecycle_source
                
                passed = monitoring_has_rich and lifecycle_has_rich and has_console_usage and has_progress_bars
                details = {
                    "monitoring_has_rich": monitoring_has_rich,
                    "lifecycle_has_rich": lifecycle_has_rich,
                    "has_console_usage": has_console_usage,
                    "has_progress_bars": has_progress_bars
                }
                
                message = f"Rich integration validation {'passed' if passed else 'failed'}"
                return ValidationResult("rich_integration", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("rich_integration", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("rich_integration", False, f"Error: {e}")
    
    def validate_monitoring_tests(self) -> ValidationResult:
        """Validate that monitoring and lifecycle tests exist and pass."""
        try:
            # Check if tests exist
            test_paths = [
                "tests/unit/infrastructure/monitoring/",
                "tests/unit/infrastructure/lifecycle/", 
                "tests/unit/services/monitoring/",
                "tests/unit/services/lifecycle/"
            ]
            
            existing_test_paths = [path for path in test_paths if (self.project_root / path).exists()]
            
            if not existing_test_paths:
                return ValidationResult("monitoring_tests", False, "No monitoring/lifecycle tests found", {
                    "searched_paths": test_paths
                })
            
            # Run tests
            test_results = []
            for test_path in existing_test_paths:
                result = subprocess.run([
                    sys.executable, "-m", "pytest", 
                    test_path,
                    "-v", "--tb=short"
                ], capture_output=True, text=True, cwd=self.project_root)
                
                test_results.append({
                    "path": test_path,
                    "return_code": result.returncode,
                    "passed": result.returncode == 0
                })
            
            all_passed = all(result["passed"] for result in test_results)
            
            details = {
                "existing_test_paths": existing_test_paths,
                "test_results": test_results,
                "all_passed": all_passed
            }
            
            message = f"Monitoring tests validation {'passed' if all_passed else 'failed'}"
            return ValidationResult("monitoring_tests", all_passed, message, details)
            
        except Exception as e:
            return ValidationResult("monitoring_tests", False, f"Error running tests: {e}")
    
    async def run_all_validations(self) -> None:
        """Run all validation checks."""
        validations = [
            ("Monitoring Structure", self.validate_monitoring_structure),
            ("Performance Metrics", self.validate_performance_metrics),
            ("Monitoring Service", self.validate_monitoring_service),
            ("Service State", self.validate_service_state),
            ("Lifecycle Manager", self.validate_lifecycle_manager),
            ("Performance Decorator", self.validate_performance_decorator),
            ("Mac M4 Optimization", self.validate_mac_optimization),
            ("Rich Integration", self.validate_rich_integration),
            ("Monitoring Tests", self.validate_monitoring_tests)
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            for name, validation_func in validations:
                task = progress.add_task(f"Validating {name}...", total=None)
                
                try:
                    if asyncio.iscoroutinefunction(validation_func):
                        result = await validation_func()
                    else:
                        result = validation_func()
                    
                    self.results.append(result)
                    
                    status = "✅" if result.passed else "❌"
                    progress.update(task, description=f"{status} {name}")
                    
                except Exception as e:
                    error_result = ValidationResult(name.lower().replace(" ", "_"), False, f"Error: {e}")
                    self.results.append(error_result)
                    progress.update(task, description=f"❌ {name} (Error)")
                
                await asyncio.sleep(0.1)
    
    def display_results(self) -> None:
        """Display validation results in a formatted table."""
        table = Table(title="🔍 Phase III Validation Results")
        
        table.add_column("Check", style="cyan", no_wrap=True)
        table.add_column("Status", style="magenta")
        table.add_column("Message", style="green")
        table.add_column("Details", style="yellow")
        
        for result in self.results:
            status = "✅ PASS" if result.passed else "❌ FAIL"
            status_color = "green" if result.passed else "red"
            
            details_str = ""
            if result.details:
                if 'missing_files' in result.details and result.details['missing_files']:
                    details_str += f"Missing: {len(result.details['missing_files'])} files\n"
                if 'missing_methods' in result.details and result.details['missing_methods']:
                    details_str += f"Missing methods: {', '.join(result.details['missing_methods'])}\n"
                if 'cpu_count' in result.details:
                    details_str += f"CPU cores: {result.details['cpu_count']}\n"
                if 'memory_gb' in result.details and result.details['memory_gb']:
                    details_str += f"Memory: {result.details['memory_gb']:.1f}GB\n"
                if 'execution_time' in result.details:
                    details_str += f"Exec time: {result.details['execution_time']:.3f}s\n"
            
            table.add_row(
                result.name.replace("_", " ").title(),
                f"[{status_color}]{status}[/{status_color}]",
                result.message,
                details_str.strip() or "✓"
            )
        
        console.print(table)
        
        # Summary
        passed_count = sum(1 for r in self.results if r.passed)
        total_count = len(self.results)
        
        if passed_count == total_count:
            console.print(Panel(
                f"[bold green]🎉 Phase III validation PASSED![/bold green]\n"
                f"All {total_count} checks completed successfully.\n"
                f"Performance monitoring and lifecycle management are ready!",
                title="Validation Summary",
                border_style="green"
            ))
        else:
            failed_count = total_count - passed_count
            console.print(Panel(
                f"[bold red]❌ Phase III validation FAILED![/bold red]\n"
                f"{failed_count} out of {total_count} checks failed.\n"
                f"Please review the issues above before deployment.",
                title="Validation Summary",
                border_style="red"
            ))
    
    def save_report(self, filepath: Path) -> None:
        """Save validation report to JSON file."""
        report = {
            "timestamp": "2025-07-01T00:00:00Z",
            "phase": "Phase III",
            "total_checks": len(self.results),
            "passed_checks": sum(1 for r in self.results if r.passed),
            "failed_checks": sum(1 for r in self.results if not r.passed),
            "system_info": {
                "cpu_count": psutil.cpu_count(),
                "memory_gb": psutil.virtual_memory().total / (1024**3),
                "platform": __import__("platform").system()
            },
            "results": [
                {
                    "name": r.name,
                    "passed": r.passed,
                    "message": r.message,
                    "details": r.details
                }
                for r in self.results
            ]
        }
        
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2)
        
        console.print(f"[green]📄 Validation report saved to {filepath}[/green]")

async def main():
    """Main validation function."""
    console.print("[bold blue]🔍 Phase III Validation Starting...[/bold blue]\n")
    
    validator = PhaseThreeValidator()
    await validator.run_all_validations()
    
    console.print("\n" + "="*50)
    validator.display_results()
    
    # Save report
    report_path = Path("reports/validation/phase_3_validation_report.json")
    validator.save_report(report_path)
    
    # Exit with appropriate code
    all_passed = all(r.passed for r in validator.results)
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    asyncio.run(main())