#!/usr/bin/env python3
"""
Phase II Validation Script

Validates that Phase II refactoring has been completed correctly:
- Protocol-based interfaces implemented
- Runtime type checking functional
- Type-safe configuration classes created
- Dependency injection enhanced
- All protocols marked with @runtime_checkable
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Protocol, get_type_hints
import importlib.util
import subprocess
import json
import inspect

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

console = Console()

class ValidationResult:
    def __init__(self, name: str, passed: bool, message: str, details: Optional[Dict] = None):
        self.name = name
        self.passed = passed
        self.message = message
        self.details = details or {}

class PhaseTwoValidator:
    def __init__(self):
        self.results: List[ValidationResult] = []
        self.project_root = project_root
    
    def validate_protocol_structure(self) -> ValidationResult:
        """Validate protocol directory structure and files."""
        try:
            protocols_path = self.project_root / "src" / "infrastructure" / "protocols"
            
            expected_protocols = [
                "storage_protocol.py",
                "logger_protocol.py"
            ]
            
            protocols_exist = protocols_path.exists() and all(
                (protocols_path / protocol).exists() for protocol in expected_protocols
            )
            
            # Check infrastructure validation and config directories
            validation_path = self.project_root / "src" / "infrastructure" / "validation"
            config_path = self.project_root / "src" / "infrastructure" / "config"
            injection_path = self.project_root / "src" / "infrastructure" / "injection"
            
            expected_files = {
                "validation": ["type_checker.py"],
                "config": ["service_config.py"],
                "injection": ["config_injector.py"]
            }
            
            all_infrastructure_exists = all([
                validation_path.exists(),
                config_path.exists(),
                injection_path.exists()
            ])
            
            missing_files = []
            for dir_name, files in expected_files.items():
                dir_path = self.project_root / "src" / "infrastructure" / dir_name
                for file_name in files:
                    if not (dir_path / file_name).exists():
                        missing_files.append(f"{dir_name}/{file_name}")
            
            passed = protocols_exist and all_infrastructure_exists and len(missing_files) == 0
            
            details = {
                "protocols_exist": protocols_exist,
                "infrastructure_exists": all_infrastructure_exists,
                "missing_protocols": [p for p in expected_protocols if not (protocols_path / p).exists()],
                "missing_files": missing_files
            }
            
            message = "Protocol structure validation " + ("passed" if passed else "failed")
            return ValidationResult("protocol_structure", passed, message, details)
            
        except Exception as e:
            return ValidationResult("protocol_structure", False, f"Error: {e}")
    
    def validate_protocol_implementations(self) -> ValidationResult:
        """Validate that protocols are properly implemented."""
        try:
            protocols_to_check = [
                "src.infrastructure.protocols.storage_protocol",
                "src.infrastructure.protocols.logger_protocol"
            ]
            
            valid_protocols = []
            invalid_protocols = []
            runtime_checkable_protocols = []
            
            for protocol_module in protocols_to_check:
                try:
                    module = importlib.import_module(protocol_module)
                    
                    # Look for protocol classes
                    protocol_classes = [
                        getattr(module, name) for name in dir(module)
                        if (name.endswith('Protocol') and 
                            hasattr(getattr(module, name), '__bases__') and
                            not name.startswith('_'))
                    ]
                    
                    for protocol_class in protocol_classes:
                        # Check if it's a Protocol
                        if Protocol in protocol_class.__bases__:
                            valid_protocols.append(f"{protocol_module}.{protocol_class.__name__}")
                            
                            # Check if it's runtime_checkable
                            if hasattr(protocol_class, '_is_runtime_protocol'):
                                runtime_checkable_protocols.append(f"{protocol_module}.{protocol_class.__name__}")
                        else:
                            invalid_protocols.append(f"{protocol_module}.{protocol_class.__name__}")
                            
                except ImportError as e:
                    invalid_protocols.append(f"{protocol_module} (import error: {e})")
            
            passed = len(invalid_protocols) == 0 and len(valid_protocols) > 0
            details = {
                "valid_protocols": valid_protocols,
                "invalid_protocols": invalid_protocols,
                "runtime_checkable_protocols": runtime_checkable_protocols
            }
            
            message = f"Protocol implementation validation {'passed' if passed else 'failed'}"
            return ValidationResult("protocol_implementations", passed, message, details)
            
        except Exception as e:
            return ValidationResult("protocol_implementations", False, f"Error: {e}")
    
    def validate_type_checker(self) -> ValidationResult:
        """Validate runtime type checker implementation."""
        try:
            try:
                from src.infrastructure.validation.type_checker import RuntimeTypeChecker, TypeValidationError
                
                # Check that RuntimeTypeChecker has expected methods
                expected_methods = ['validate_protocol_implementation', 'create_type_checking_decorator']
                has_methods = all(hasattr(RuntimeTypeChecker, method) for method in expected_methods)
                
                # Check that TypeValidationError exists
                has_exception = issubclass(TypeValidationError, Exception)
                
                passed = has_methods and has_exception
                details = {
                    "has_methods": has_methods,
                    "has_exception": has_exception,
                    "missing_methods": [m for m in expected_methods if not hasattr(RuntimeTypeChecker, m)]
                }
                
                message = f"Type checker validation {'passed' if passed else 'failed'}"
                return ValidationResult("type_checker", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("type_checker", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("type_checker", False, f"Error: {e}")
    
    def validate_service_config(self) -> ValidationResult:
        """Validate service configuration dataclasses."""
        try:
            try:
                from src.infrastructure.config.service_config import (
                    BaseServiceConfig, StorageServiceConfig, DatabaseServiceConfig,
                    S3ServiceConfig, AIServiceConfig, BrowserServiceConfig, ServiceConfigRegistry
                )
                
                # Check that all config classes are dataclasses
                config_classes = [
                    BaseServiceConfig, StorageServiceConfig, DatabaseServiceConfig,
                    S3ServiceConfig, AIServiceConfig, BrowserServiceConfig
                ]
                
                valid_dataclasses = []
                invalid_dataclasses = []
                
                for config_class in config_classes:
                    if hasattr(config_class, '__dataclass_fields__'):
                        valid_dataclasses.append(config_class.__name__)
                    else:
                        invalid_dataclasses.append(config_class.__name__)
                
                # Check ServiceConfigRegistry methods
                registry_methods = ['register_config', 'get_config', 'get_typed_config', 'validate_all_configs']
                has_registry_methods = all(hasattr(ServiceConfigRegistry, method) for method in registry_methods)
                
                passed = len(invalid_dataclasses) == 0 and has_registry_methods
                details = {
                    "valid_dataclasses": valid_dataclasses,
                    "invalid_dataclasses": invalid_dataclasses,
                    "has_registry_methods": has_registry_methods
                }
                
                message = f"Service config validation {'passed' if passed else 'failed'}"
                return ValidationResult("service_config", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("service_config", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("service_config", False, f"Error: {e}")
    
    def validate_config_injector(self) -> ValidationResult:
        """Validate configuration injection system."""
        try:
            try:
                from src.infrastructure.injection.config_injector import (
                    TypeSafeConfigInjector, DependencyInjector, ConfigurationError
                )
                
                # Check TypeSafeConfigInjector methods
                injector_methods = ['inject_config', 'load_environment_overrides', 'load_file_overrides']
                has_injector_methods = all(hasattr(TypeSafeConfigInjector, method) for method in injector_methods)
                
                # Check DependencyInjector methods
                dependency_methods = ['register_dependency', 'get_dependency', 'get_by_protocol', 'inject_dependencies']
                has_dependency_methods = all(hasattr(DependencyInjector, method) for method in dependency_methods)
                
                # Check ConfigurationError exists
                has_exception = issubclass(ConfigurationError, Exception)
                
                passed = has_injector_methods and has_dependency_methods and has_exception
                details = {
                    "has_injector_methods": has_injector_methods,
                    "has_dependency_methods": has_dependency_methods,
                    "has_exception": has_exception
                }
                
                message = f"Config injector validation {'passed' if passed else 'failed'}"
                return ValidationResult("config_injector", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("config_injector", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("config_injector", False, f"Error: {e}")
    
    def validate_protocol_tests(self) -> ValidationResult:
        """Validate that protocol tests exist and pass."""
        try:
            # Check if protocol tests exist
            test_paths = [
                "tests/unit/infrastructure/protocols/",
                "tests/unit/infrastructure/validation/",
                "tests/unit/infrastructure/config/"
            ]
            
            tests_exist = any((self.project_root / path).exists() for path in test_paths)
            
            if not tests_exist:
                return ValidationResult("protocol_tests", False, "No protocol tests found", {
                    "searched_paths": test_paths
                })
            
            # Run tests
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                "tests/unit/infrastructure/",
                "-v", "--tb=short"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            passed = result.returncode == 0
            details = {
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "tests_exist": tests_exist
            }
            
            message = f"Protocol tests validation {'passed' if passed else 'failed'}"
            return ValidationResult("protocol_tests", passed, message, details)
            
        except Exception as e:
            return ValidationResult("protocol_tests", False, f"Error running tests: {e}")
    
    def validate_type_checking_mypy(self) -> ValidationResult:
        """Validate mypy type checking on infrastructure."""
        try:
            # Run mypy on infrastructure
            result = subprocess.run([
                sys.executable, "-m", "mypy", 
                "src/infrastructure/",
                "--ignore-missing-imports",
                "--strict-optional"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            passed = result.returncode == 0
            details = {
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            message = f"Infrastructure type checking {'passed' if passed else 'failed'}"
            return ValidationResult("type_checking_mypy", passed, message, details)
            
        except Exception as e:
            return ValidationResult("type_checking_mypy", False, f"Error running mypy: {e}")
    
    def validate_protocol_method_signatures(self) -> ValidationResult:
        """Validate that protocols have proper async method signatures."""
        try:
            try:
                from src.infrastructure.protocols.storage_protocol import StorageProtocol
                from src.infrastructure.protocols.logger_protocol import LoggerProtocol
                
                validation_results = {}
                
                # Check StorageProtocol methods
                storage_methods = ['save', 'load', 'exists', 'delete', 'list_keys', 'batch_save', 'batch_load']
                storage_async_methods = []
                
                for method_name in storage_methods:
                    if hasattr(StorageProtocol, method_name):
                        method = getattr(StorageProtocol, method_name)
                        if inspect.iscoroutinefunction(method) or (hasattr(method, '__annotations__') and 
                                                                   'return' in method.__annotations__ and 
                                                                   'Awaitable' in str(method.__annotations__['return'])):
                            storage_async_methods.append(method_name)
                
                validation_results['storage_protocol'] = {
                    'expected_methods': storage_methods,
                    'async_methods': storage_async_methods,
                    'all_async': len(storage_async_methods) >= len(storage_methods) - 2  # Allow some sync methods
                }
                
                # Check LoggerProtocol methods
                logger_methods = ['debug', 'info', 'warning', 'error', 'critical', 'log']
                logger_sync_methods = []
                
                for method_name in logger_methods:
                    if hasattr(LoggerProtocol, method_name):
                        method = getattr(LoggerProtocol, method_name)
                        if not inspect.iscoroutinefunction(method):
                            logger_sync_methods.append(method_name)
                
                validation_results['logger_protocol'] = {
                    'expected_methods': logger_methods,
                    'sync_methods': logger_sync_methods,
                    'properly_sync': len(logger_sync_methods) >= len(logger_methods) - 2  # Allow some variation
                }
                
                passed = (validation_results['storage_protocol']['all_async'] and 
                         validation_results['logger_protocol']['properly_sync'])
                
                message = f"Protocol method signatures {'passed' if passed else 'failed'}"
                return ValidationResult("protocol_method_signatures", passed, message, validation_results)
                
            except ImportError as e:
                return ValidationResult("protocol_method_signatures", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("protocol_method_signatures", False, f"Error: {e}")
    
    async def run_all_validations(self) -> None:
        """Run all validation checks."""
        validations = [
            ("Protocol Structure", self.validate_protocol_structure),
            ("Protocol Implementations", self.validate_protocol_implementations),
            ("Type Checker", self.validate_type_checker),
            ("Service Config", self.validate_service_config),
            ("Config Injector", self.validate_config_injector),
            ("Protocol Tests", self.validate_protocol_tests),
            ("Type Checking (MyPy)", self.validate_type_checking_mypy),
            ("Protocol Method Signatures", self.validate_protocol_method_signatures)
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            for name, validation_func in validations:
                task = progress.add_task(f"Validating {name}...", total=None)
                
                try:
                    result = validation_func()
                    self.results.append(result)
                    
                    status = "✅" if result.passed else "❌"
                    progress.update(task, description=f"{status} {name}")
                    
                except Exception as e:
                    error_result = ValidationResult(name.lower().replace(" ", "_"), False, f"Error: {e}")
                    self.results.append(error_result)
                    progress.update(task, description=f"❌ {name} (Error)")
                
                await asyncio.sleep(0.1)
    
    def display_results(self) -> None:
        """Display validation results in a formatted table."""
        table = Table(title="🔍 Phase II Validation Results")
        
        table.add_column("Check", style="cyan", no_wrap=True)
        table.add_column("Status", style="magenta")
        table.add_column("Message", style="green")
        table.add_column("Details", style="yellow")
        
        for result in self.results:
            status = "✅ PASS" if result.passed else "❌ FAIL"
            status_color = "green" if result.passed else "red"
            
            details_str = ""
            if result.details:
                if 'missing_protocols' in result.details and result.details['missing_protocols']:
                    details_str += f"Missing protocols: {', '.join(result.details['missing_protocols'])}\n"
                if 'invalid_protocols' in result.details and result.details['invalid_protocols']:
                    details_str += f"Invalid: {', '.join(result.details['invalid_protocols'])}\n"
                if 'return_code' in result.details and result.details['return_code'] != 0:
                    details_str += f"Exit code: {result.details['return_code']}\n"
                if 'runtime_checkable_protocols' in result.details:
                    count = len(result.details['runtime_checkable_protocols'])
                    details_str += f"Runtime checkable: {count}\n"
            
            table.add_row(
                result.name.replace("_", " ").title(),
                f"[{status_color}]{status}[/{status_color}]",
                result.message,
                details_str.strip() or "✓"
            )
        
        console.print(table)
        
        # Summary
        passed_count = sum(1 for r in self.results if r.passed)
        total_count = len(self.results)
        
        if passed_count == total_count:
            console.print(Panel(
                f"[bold green]🎉 Phase II validation PASSED![/bold green]\n"
                f"All {total_count} checks completed successfully.",
                title="Validation Summary",
                border_style="green"
            ))
        else:
            failed_count = total_count - passed_count
            console.print(Panel(
                f"[bold red]❌ Phase II validation FAILED![/bold red]\n"
                f"{failed_count} out of {total_count} checks failed.\n"
                f"Please review the issues above before proceeding to Phase III.",
                title="Validation Summary",
                border_style="red"
            ))
    
    def save_report(self, filepath: Path) -> None:
        """Save validation report to JSON file."""
        report = {
            "timestamp": "2025-07-01T00:00:00Z",
            "phase": "Phase II",
            "total_checks": len(self.results),
            "passed_checks": sum(1 for r in self.results if r.passed),
            "failed_checks": sum(1 for r in self.results if not r.passed),
            "results": [
                {
                    "name": r.name,
                    "passed": r.passed,
                    "message": r.message,
                    "details": r.details
                }
                for r in self.results
            ]
        }
        
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2)
        
        console.print(f"[green]📄 Validation report saved to {filepath}[/green]")

async def main():
    """Main validation function."""
    console.print("[bold blue]🔍 Phase II Validation Starting...[/bold blue]\n")
    
    validator = PhaseTwoValidator()
    await validator.run_all_validations()
    
    console.print("\n" + "="*50)
    validator.display_results()
    
    # Save report
    report_path = Path("reports/validation/phase_2_validation_report.json")
    validator.save_report(report_path)
    
    # Exit with appropriate code
    all_passed = all(r.passed for r in validator.results)
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    asyncio.run(main())