# Phase II: Type-Safe Dependency Injection - LLM Execution Prompt

## Context
You are helping with Phase II of the LexGenius refactoring project. This phase focuses on implementing protocol-based interfaces with runtime type checking and enhancing configuration injection with type safety.

## Project Architecture
- **Base Classes**: Services extend `AsyncServiceBase` or `ComponentImplementation`
- **Protocols**: Use `@runtime_checkable` protocols for interface definitions
- **Configuration**: Pydantic models in `src/config_models/`
- **Type Safety**: Full type hints with mypy compliance

## Phase II Objectives

### 2.A Enhanced Protocol-Based Interfaces

#### Task: Implement StorageProtocol with Async Methods
**Priority**: High
**Estimated Duration**: 20 minutes

**Target Location**: `src/infrastructure/protocols/storage_protocol.py`

**Implementation**:
```python
from typing import Protocol, runtime_checkable, Any, Optional, Dict, List
from abc import abstractmethod

@runtime_checkable
class StorageProtocol(Protocol):
    """Protocol for all storage implementations."""
    
    @abstractmethod
    async def save(self, key: str, data: Any, **kwargs) -> bool:
        """Save data to storage with the given key."""
        ...
    
    @abstractmethod
    async def load(self, key: str, **kwargs) -> Optional[Any]:
        """Load data from storage by key."""
        ...
    
    @abstractmethod
    async def exists(self, key: str, **kwargs) -> bool:
        """Check if key exists in storage."""
        ...
    
    @abstractmethod
    async def delete(self, key: str, **kwargs) -> bool:
        """Delete data from storage by key."""
        ...
    
    @abstractmethod
    async def list_keys(self, prefix: str = "", **kwargs) -> List[str]:
        """List all keys with optional prefix filter."""
        ...
    
    @abstractmethod
    async def batch_save(self, items: Dict[str, Any], **kwargs) -> Dict[str, bool]:
        """Save multiple items in batch operation."""
        ...
    
    @abstractmethod
    async def batch_load(self, keys: List[str], **kwargs) -> Dict[str, Any]:
        """Load multiple items in batch operation."""
        ...

@runtime_checkable  
class AsyncFileStorageProtocol(StorageProtocol, Protocol):
    """Extended protocol for file-based storage."""
    
    @abstractmethod
    async def save_file(self, key: str, file_path: str, **kwargs) -> bool:
        """Save file to storage."""
        ...
    
    @abstractmethod
    async def download_file(self, key: str, local_path: str, **kwargs) -> bool:
        """Download file from storage to local path."""
        ...

@runtime_checkable
class AsyncDatabaseStorageProtocol(StorageProtocol, Protocol):
    """Extended protocol for database storage."""
    
    @abstractmethod
    async def query(self, query: str, params: Dict[str, Any] = None, **kwargs) -> List[Dict[str, Any]]:
        """Execute query against database storage."""
        ...
    
    @abstractmethod
    async def execute(self, command: str, params: Dict[str, Any] = None, **kwargs) -> bool:
        """Execute command against database storage."""
        ...
```

#### Task: Implement LoggerProtocol with Standardized Methods
**Priority**: High
**Estimated Duration**: 15 minutes

**Target Location**: `src/infrastructure/protocols/logger_protocol.py`

**Implementation**:
```python
from typing import Protocol, runtime_checkable, Any, Optional, Dict
from abc import abstractmethod
from enum import Enum

class LogLevel(Enum):
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@runtime_checkable
class LoggerProtocol(Protocol):
    """Protocol for all logging implementations."""
    
    @abstractmethod
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        ...
    
    @abstractmethod
    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        ...
    
    @abstractmethod
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        ...
    
    @abstractmethod
    def error(self, message: str, **kwargs) -> None:
        """Log error message."""
        ...
    
    @abstractmethod
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message."""
        ...
    
    @abstractmethod
    def log(self, level: LogLevel, message: str, **kwargs) -> None:
        """Log message at specified level."""
        ...

@runtime_checkable
class StructuredLoggerProtocol(LoggerProtocol, Protocol):
    """Extended protocol for structured logging."""
    
    @abstractmethod
    def log_with_context(self, level: LogLevel, message: str, context: Dict[str, Any], **kwargs) -> None:
        """Log message with structured context data."""
        ...
    
    @abstractmethod
    def log_performance(self, operation: str, duration: float, context: Dict[str, Any] = None, **kwargs) -> None:
        """Log performance metrics."""
        ...
    
    @abstractmethod
    def log_error_with_traceback(self, message: str, exception: Exception, context: Dict[str, Any] = None, **kwargs) -> None:
        """Log error with full traceback and context."""
        ...
```

#### Task: Add Runtime Type Checking
**Priority**: High
**Estimated Duration**: 25 minutes

**Target Location**: `src/infrastructure/validation/type_checker.py`

**Implementation**:
```python
from typing import Any, Type, Protocol, get_type_hints, get_origin, get_args
import inspect
from functools import wraps

class TypeValidationError(Exception):
    """Raised when type validation fails."""
    pass

class RuntimeTypeChecker:
    """Utility for runtime type checking of protocol implementations."""
    
    @staticmethod
    def validate_protocol_implementation(instance: Any, protocol: Type[Protocol]) -> bool:
        """Validate that instance properly implements protocol."""
        if not isinstance(instance, protocol):
            return False
        
        # Check that all required methods are implemented
        protocol_methods = [
            name for name, method in inspect.getmembers(protocol, predicate=inspect.isfunction)
            if not name.startswith('_')
        ]
        
        instance_methods = [
            name for name, method in inspect.getmembers(instance, predicate=inspect.ismethod)
            if not name.startswith('_')
        ]
        
        missing_methods = set(protocol_methods) - set(instance_methods)
        if missing_methods:
            raise TypeValidationError(
                f"Instance {type(instance).__name__} missing required methods: {missing_methods}"
            )
        
        return True
    
    @staticmethod
    def create_type_checking_decorator(protocol: Type[Protocol]):
        """Create decorator that validates protocol implementation at runtime."""
        def decorator(cls):
            original_init = cls.__init__
            
            @wraps(original_init)
            def validated_init(self, *args, **kwargs):
                original_init(self, *args, **kwargs)
                RuntimeTypeChecker.validate_protocol_implementation(self, protocol)
            
            cls.__init__ = validated_init
            return cls
        
        return decorator

# Decorators for common protocols
def validate_storage_protocol(cls):
    """Decorator to validate StorageProtocol implementation."""
    return RuntimeTypeChecker.create_type_checking_decorator(StorageProtocol)(cls)

def validate_logger_protocol(cls):
    """Decorator to validate LoggerProtocol implementation."""
    return RuntimeTypeChecker.create_type_checking_decorator(LoggerProtocol)(cls)
```

### 2.B Configuration Injection Enhancement

#### Task: Create Type-Safe ServiceConfig Dataclasses
**Priority**: High
**Estimated Duration**: 30 minutes

**Target Location**: `src/infrastructure/config/service_config.py`

**Implementation**:
```python
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Type
from pathlib import Path
import os

@dataclass
class BaseServiceConfig:
    """Base configuration for all services."""
    service_name: str
    enabled: bool = True
    log_level: str = "INFO"
    timeout_seconds: int = 30
    retry_attempts: int = 3
    environment: str = field(default_factory=lambda: os.getenv("ENVIRONMENT", "development"))

@dataclass
class StorageServiceConfig(BaseServiceConfig):
    """Configuration for storage services."""
    connection_string: Optional[str] = None
    max_connections: int = 10
    connection_timeout: int = 5
    retry_on_failure: bool = True
    backup_enabled: bool = True
    encryption_enabled: bool = False

@dataclass
class DatabaseServiceConfig(StorageServiceConfig):
    """Configuration for database services."""
    database_name: str = ""
    table_prefix: str = ""
    migration_enabled: bool = True
    connection_pool_size: int = 5
    query_timeout: int = 30

@dataclass
class S3ServiceConfig(StorageServiceConfig):
    """Configuration for S3 storage services."""
    bucket_name: str = ""
    region: str = "us-east-1"
    access_key_id: Optional[str] = None
    secret_access_key: Optional[str] = None
    endpoint_url: Optional[str] = None
    use_ssl: bool = True

@dataclass
class AIServiceConfig(BaseServiceConfig):
    """Configuration for AI services."""
    api_key: Optional[str] = None
    model_name: str = "gpt-4"
    max_tokens: int = 4000
    temperature: float = 0.7
    rate_limit_rpm: int = 60
    rate_limit_tpm: int = 90000

@dataclass
class BrowserServiceConfig(BaseServiceConfig):
    """Configuration for browser automation services."""
    headless: bool = True
    download_path: str = "/tmp/downloads"
    page_timeout: int = 30000
    navigation_timeout: int = 30000
    max_concurrent_pages: int = 5
    user_agent: Optional[str] = None

@dataclass
class ServiceConfigRegistry:
    """Registry for managing service configurations."""
    configs: Dict[str, BaseServiceConfig] = field(default_factory=dict)
    
    def register_config(self, service_name: str, config: BaseServiceConfig) -> None:
        """Register configuration for a service."""
        config.service_name = service_name
        self.configs[service_name] = config
    
    def get_config(self, service_name: str) -> Optional[BaseServiceConfig]:
        """Get configuration for a service."""
        return self.configs.get(service_name)
    
    def get_typed_config(self, service_name: str, config_type: Type[BaseServiceConfig]) -> Optional[BaseServiceConfig]:
        """Get configuration with type validation."""
        config = self.get_config(service_name)
        if config and isinstance(config, config_type):
            return config
        return None
    
    def validate_all_configs(self) -> Dict[str, List[str]]:
        """Validate all registered configurations."""
        validation_errors = {}
        
        for service_name, config in self.configs.items():
            errors = []
            
            # Basic validation
            if not config.service_name:
                errors.append("service_name is required")
            if config.timeout_seconds <= 0:
                errors.append("timeout_seconds must be positive")
            if config.retry_attempts < 0:
                errors.append("retry_attempts must be non-negative")
            
            # Type-specific validation
            if isinstance(config, DatabaseServiceConfig):
                if not config.database_name:
                    errors.append("database_name is required for database services")
            elif isinstance(config, S3ServiceConfig):
                if not config.bucket_name:
                    errors.append("bucket_name is required for S3 services")
            elif isinstance(config, AIServiceConfig):
                if not config.api_key:
                    errors.append("api_key is required for AI services")
            
            if errors:
                validation_errors[service_name] = errors
        
        return validation_errors
```

#### Task: Enhance Configuration Injection
**Priority**: High
**Estimated Duration**: 35 minutes

**Target Location**: `src/infrastructure/injection/config_injector.py`

**Implementation**:
```python
from typing import Dict, Any, Type, TypeVar, Generic, Optional, get_type_hints
from dataclasses import fields, is_dataclass
import os
import json
from pathlib import Path

from src.infrastructure.config.service_config import BaseServiceConfig, ServiceConfigRegistry
from src.infrastructure.protocols.storage_protocol import StorageProtocol
from src.infrastructure.protocols.logger_protocol import LoggerProtocol

T = TypeVar('T', bound=BaseServiceConfig)

class ConfigurationError(Exception):
    """Raised when configuration injection fails."""
    pass

class TypeSafeConfigInjector(Generic[T]):
    """Type-safe configuration injector for services."""
    
    def __init__(self, config_registry: ServiceConfigRegistry):
        self.config_registry = config_registry
        self._environment_overrides: Dict[str, Any] = {}
        self._file_overrides: Dict[str, Any] = {}
        
    def load_environment_overrides(self, prefix: str = "LEXGENIUS_") -> None:
        """Load configuration overrides from environment variables."""
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower()
                self._environment_overrides[config_key] = self._parse_env_value(value)
    
    def load_file_overrides(self, config_file: Path) -> None:
        """Load configuration overrides from JSON file."""
        if config_file.exists():
            with open(config_file, 'r') as f:
                self._file_overrides = json.load(f)
    
    def _parse_env_value(self, value: str) -> Any:
        """Parse environment variable value to appropriate type."""
        # Try boolean
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Try integer
        try:
            return int(value)
        except ValueError:
            pass
        
        # Try float
        try:
            return float(value)
        except ValueError:
            pass
        
        # Return as string
        return value
    
    def inject_config(self, service_name: str, config_class: Type[T]) -> T:
        """Inject configuration for a service with type safety."""
        # Get base config from registry
        base_config = self.config_registry.get_config(service_name)
        
        if base_config and not isinstance(base_config, config_class):
            raise ConfigurationError(
                f"Config type mismatch for {service_name}: "
                f"expected {config_class.__name__}, got {type(base_config).__name__}"
            )
        
        # Create new config instance with overrides
        config_dict = {}
        
        if base_config:
            # Start with base config values
            if is_dataclass(base_config):
                config_dict = {field.name: getattr(base_config, field.name) for field in fields(base_config)}
        
        # Apply file overrides
        service_file_overrides = self._file_overrides.get(service_name, {})
        config_dict.update(service_file_overrides)
        
        # Apply environment overrides
        service_env_key = service_name.lower().replace('-', '_')
        for key, value in self._environment_overrides.items():
            if key.startswith(service_env_key + '_'):
                config_key = key[len(service_env_key) + 1:]
                config_dict[config_key] = value
        
        # Validate required fields
        self._validate_required_fields(config_class, config_dict)
        
        # Create and return typed config instance
        try:
            return config_class(**config_dict)
        except TypeError as e:
            raise ConfigurationError(f"Failed to create config for {service_name}: {e}")
    
    def _validate_required_fields(self, config_class: Type[T], config_dict: Dict[str, Any]) -> None:
        """Validate that all required fields are present."""
        if not is_dataclass(config_class):
            return
        
        required_fields = [
            field.name for field in fields(config_class)
            if field.default == field.default_factory and field.default_factory == field.default_factory  # No default value
        ]
        
        missing_fields = [field for field in required_fields if field not in config_dict]
        if missing_fields:
            raise ConfigurationError(f"Missing required config fields: {missing_fields}")

class DependencyInjector:
    """Enhanced dependency injector with protocol validation."""
    
    def __init__(self):
        self._dependencies: Dict[str, Any] = {}
        self._protocol_mappings: Dict[Type, str] = {}
    
    def register_dependency(self, name: str, instance: Any, protocol: Optional[Type] = None) -> None:
        """Register a dependency with optional protocol validation."""
        if protocol:
            if not isinstance(instance, protocol):
                raise ConfigurationError(f"Instance does not implement required protocol {protocol.__name__}")
            self._protocol_mappings[protocol] = name
        
        self._dependencies[name] = instance
    
    def get_dependency(self, name: str) -> Any:
        """Get dependency by name."""
        if name not in self._dependencies:
            raise ConfigurationError(f"Dependency '{name}' not registered")
        return self._dependencies[name]
    
    def get_by_protocol(self, protocol: Type) -> Any:
        """Get dependency by protocol type."""
        if protocol not in self._protocol_mappings:
            raise ConfigurationError(f"No dependency registered for protocol {protocol.__name__}")
        
        name = self._protocol_mappings[protocol]
        return self.get_dependency(name)
    
    def inject_dependencies(self, service_class: Type, **kwargs) -> Any:
        """Create service instance with dependency injection."""
        # Get type hints to understand what dependencies are needed
        type_hints = get_type_hints(service_class.__init__)
        
        # Inject dependencies based on type hints
        injected_kwargs = kwargs.copy()
        
        for param_name, param_type in type_hints.items():
            if param_name in ['self', 'return']:
                continue
            
            # Skip if already provided
            if param_name in injected_kwargs:
                continue
            
            # Try to inject by protocol
            try:
                if hasattr(param_type, '__origin__') and param_type.__origin__ is not None:
                    # Handle generic types, unions, etc.
                    continue
                
                dependency = self.get_by_protocol(param_type)
                injected_kwargs[param_name] = dependency
            except ConfigurationError:
                # Try to inject by name
                try:
                    dependency = self.get_dependency(param_name)
                    injected_kwargs[param_name] = dependency
                except ConfigurationError:
                    # Dependency not available, skip
                    continue
        
        return service_class(**injected_kwargs)
```

## Testing Requirements

Create comprehensive tests for all protocol implementations:

```python
# Example test structure
import pytest
from src.infrastructure.protocols.storage_protocol import StorageProtocol
from src.infrastructure.validation.type_checker import RuntimeTypeChecker

class MockStorageImplementation:
    async def save(self, key: str, data: Any, **kwargs) -> bool:
        return True
    
    async def load(self, key: str, **kwargs) -> Optional[Any]:
        return {"test": "data"}
    
    # ... implement all protocol methods

class TestStorageProtocol:
    def test_protocol_validation_success(self):
        impl = MockStorageImplementation()
        assert RuntimeTypeChecker.validate_protocol_implementation(impl, StorageProtocol)
    
    def test_protocol_validation_failure(self):
        class IncompleteImplementation:
            async def save(self, key: str, data: Any, **kwargs) -> bool:
                return True
            # Missing other required methods
        
        impl = IncompleteImplementation()
        with pytest.raises(TypeValidationError):
            RuntimeTypeChecker.validate_protocol_implementation(impl, StorageProtocol)
```

## Success Criteria

### Phase II Completion Checklist
- [ ] `StorageProtocol` implemented with all async methods
- [ ] `LoggerProtocol` implemented with standardized logging
- [ ] Runtime type checking system implemented
- [ ] Type-safe service configuration dataclasses created
- [ ] Configuration injection system enhanced with type safety
- [ ] Dependency injector enhanced with protocol validation
- [ ] All protocols marked with `@runtime_checkable`
- [ ] Comprehensive test coverage for all protocols
- [ ] Type checking passes with mypy
- [ ] Integration tests validate protocol implementations

### Post-Migration Validation
```bash
# Run these commands to validate migration
python -m mypy src/infrastructure/protocols/     # Type checking passes
python -m mypy src/infrastructure/config/       # Config type checking passes
pytest tests/infrastructure/ -v                 # Infrastructure tests pass
python scripts/refactoring/validate_phase_2.py  # Custom validation
```

## Integration Notes

- All existing services should be updated to use new protocol interfaces
- Configuration injection should be backward compatible
- Protocol validation can be enabled gradually with feature flags
- Dependencies should be registered in main application startup
- Type safety should not impact runtime performance significantly