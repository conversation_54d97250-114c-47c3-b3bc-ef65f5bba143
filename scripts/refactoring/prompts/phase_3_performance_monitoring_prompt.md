# Phase III: Performance Monitoring and Lifecycle Management - LLM Execution Prompt

## Context
You are helping with Phase III of the LexGenius refactoring project. This phase focuses on implementing comprehensive performance monitoring and enhanced service lifecycle management for local development and production environments.

## Project Architecture
- **Mac M4/128GB Optimization**: Leverage parallel processing and high memory capacity
- **Rich Console**: Use rich formatting and progress bars for all output
- **Async Architecture**: All monitoring should be non-blocking and asynchronous
- **Service Base Classes**: Extend existing `AsyncServiceBase` patterns

## Phase III Objectives

### 3.A Performance Monitoring Service

#### Task: Create PerformanceMetrics Dataclass
**Priority**: High
**Estimated Duration**: 15 minutes

**Target Location**: `src/infrastructure/monitoring/performance_metrics.py`

**Implementation**:
```python
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum
import psutil
import asyncio
from pathlib import Path

class MetricType(Enum):
    EXECUTION = "execution"
    MEMORY = "memory" 
    CPU = "cpu"
    IO = "io"
    NETWORK = "network"
    CUSTOM = "custom"

class PerformanceLevel(Enum):
    EXCELLENT = "excellent"  # < 1s, < 10% CPU
    GOOD = "good"           # < 5s, < 25% CPU  
    ACCEPTABLE = "acceptable" # < 15s, < 50% CPU
    POOR = "poor"           # < 30s, < 75% CPU
    CRITICAL = "critical"   # > 30s, > 75% CPU

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics for operations."""
    operation_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None  # seconds
    
    # System metrics
    memory_start: Optional[float] = None  # MB
    memory_end: Optional[float] = None    # MB
    memory_peak: Optional[float] = None   # MB
    memory_delta: Optional[float] = None  # MB
    
    cpu_percent_avg: Optional[float] = None
    cpu_percent_peak: Optional[float] = None
    
    # I/O metrics
    disk_read_bytes: Optional[int] = None
    disk_write_bytes: Optional[int] = None
    network_sent_bytes: Optional[int] = None
    network_recv_bytes: Optional[int] = None
    
    # Custom metrics
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    
    # Context and metadata
    service_name: Optional[str] = None
    thread_id: Optional[int] = None
    process_id: Optional[int] = None
    correlation_id: Optional[str] = None
    
    # Result tracking
    success: bool = True
    error_message: Optional[str] = None
    warning_count: int = 0
    
    @property
    def performance_level(self) -> PerformanceLevel:
        """Calculate performance level based on metrics."""
        if not self.duration or not self.cpu_percent_avg:
            return PerformanceLevel.ACCEPTABLE
        
        if self.duration < 1.0 and self.cpu_percent_avg < 10:
            return PerformanceLevel.EXCELLENT
        elif self.duration < 5.0 and self.cpu_percent_avg < 25:
            return PerformanceLevel.GOOD
        elif self.duration < 15.0 and self.cpu_percent_avg < 50:
            return PerformanceLevel.ACCEPTABLE
        elif self.duration < 30.0 and self.cpu_percent_avg < 75:
            return PerformanceLevel.POOR
        else:
            return PerformanceLevel.CRITICAL
    
    def finalize(self) -> None:
        """Finalize metrics when operation completes."""
        if self.end_time:
            self.duration = (self.end_time - self.start_time).total_seconds()
        
        if self.memory_start and self.memory_end:
            self.memory_delta = self.memory_end - self.memory_start
    
    def add_custom_metric(self, name: str, value: Any) -> None:
        """Add custom metric."""
        self.custom_metrics[name] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        result = {}
        for field_obj in self.__dataclass_fields__.values():
            value = getattr(self, field_obj.name)
            if isinstance(value, datetime):
                result[field_obj.name] = value.isoformat()
            elif isinstance(value, (PerformanceLevel, MetricType)):
                result[field_obj.name] = value.value
            else:
                result[field_obj.name] = value
        return result

@dataclass
class AggregatedMetrics:
    """Aggregated performance metrics for analysis."""
    operation_name: str
    total_executions: int
    successful_executions: int
    failed_executions: int
    
    avg_duration: float
    min_duration: float
    max_duration: float
    p95_duration: float
    p99_duration: float
    
    avg_memory_usage: float
    peak_memory_usage: float
    avg_cpu_usage: float
    peak_cpu_usage: float
    
    first_execution: datetime
    last_execution: datetime
    
    performance_distribution: Dict[PerformanceLevel, int] = field(default_factory=dict)
```

#### Task: Implement PerformanceMonitoringService
**Priority**: High
**Estimated Duration**: 40 minutes

**Target Location**: `src/services/monitoring/performance_monitoring_service.py`

**Implementation**:
```python
import asyncio
import psutil
import time
from collections import defaultdict, deque
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, AsyncGenerator
import uuid
from pathlib import Path
import json

from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.live import Live
from rich.panel import Panel

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.monitoring.performance_metrics import (
    PerformanceMetrics, AggregatedMetrics, PerformanceLevel, MetricType
)

class PerformanceMonitoringService(AsyncServiceBase):
    """Comprehensive performance monitoring service optimized for Mac M4."""
    
    def __init__(self):
        super().__init__()
        self.console = Console()
        self._metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._active_operations: Dict[str, PerformanceMetrics] = {}
        self._thresholds: Dict[str, Dict[str, float]] = {}
        self._monitoring_enabled = True
        self._background_task: Optional[asyncio.Task] = None
        self._alerts_enabled = True
        
        # System monitoring
        self._system_metrics_interval = 5.0  # seconds
        self._process = psutil.Process()
        
        # Performance tracking
        self._operation_counts: Dict[str, int] = defaultdict(int)
        self._slow_operations: List[PerformanceMetrics] = []
        
    async def _execute_action(self, data: Any) -> Any:
        """Execute monitoring action."""
        if isinstance(data, dict) and 'action' in data:
            action = data['action']
            if action == 'start_operation':
                return await self.start_operation_monitoring(
                    data['operation_name'],
                    data.get('service_name'),
                    data.get('correlation_id')
                )
            elif action == 'stop_operation':
                return await self.stop_operation_monitoring(
                    data['operation_id'],
                    data.get('success', True),
                    data.get('error_message')
                )
        return None
    
    async def start_operation_monitoring(
        self, 
        operation_name: str, 
        service_name: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> str:
        """Start monitoring an operation."""
        operation_id = str(uuid.uuid4())
        
        # Capture initial system state
        memory_info = self._process.memory_info()
        cpu_percent = self._process.cpu_percent()
        io_counters = self._process.io_counters()
        
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            start_time=datetime.now(),
            service_name=service_name,
            correlation_id=correlation_id,
            memory_start=memory_info.rss / 1024 / 1024,  # MB
            cpu_percent_avg=cpu_percent,
            disk_read_bytes=io_counters.read_bytes,
            disk_write_bytes=io_counters.write_bytes,
            process_id=self._process.pid,
            thread_id=asyncio.current_task().get_name() if asyncio.current_task() else None
        )
        
        self._active_operations[operation_id] = metrics
        self._operation_counts[operation_name] += 1
        
        return operation_id
    
    async def stop_operation_monitoring(
        self, 
        operation_id: str, 
        success: bool = True,
        error_message: Optional[str] = None
    ) -> Optional[PerformanceMetrics]:
        """Stop monitoring an operation."""
        if operation_id not in self._active_operations:
            return None
        
        metrics = self._active_operations.pop(operation_id)
        
        # Capture final system state
        memory_info = self._process.memory_info()
        cpu_percent = self._process.cpu_percent()
        io_counters = self._process.io_counters()
        
        metrics.end_time = datetime.now()
        metrics.memory_end = memory_info.rss / 1024 / 1024  # MB
        metrics.success = success
        metrics.error_message = error_message
        
        # Calculate deltas
        if metrics.disk_read_bytes:
            metrics.disk_read_bytes = io_counters.read_bytes - metrics.disk_read_bytes
            metrics.disk_write_bytes = io_counters.write_bytes - metrics.disk_write_bytes
        
        metrics.finalize()
        
        # Store in history
        self._metrics_history[metrics.operation_name].append(metrics)
        
        # Check for slow operations
        if metrics.performance_level in [PerformanceLevel.POOR, PerformanceLevel.CRITICAL]:
            self._slow_operations.append(metrics)
            if self._alerts_enabled:
                await self._alert_slow_operation(metrics)
        
        return metrics
    
    @asynccontextmanager
    async def monitor_operation(
        self, 
        operation_name: str, 
        service_name: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """Context manager for monitoring operations."""
        operation_id = await self.start_operation_monitoring(operation_name, service_name)
        try:
            yield operation_id
            await self.stop_operation_monitoring(operation_id, success=True)
        except Exception as e:
            await self.stop_operation_monitoring(operation_id, success=False, error_message=str(e))
            raise
    
    def create_performance_decorator(self, operation_name: Optional[str] = None):
        """Create decorator for automatic performance monitoring."""
        def decorator(func: Callable) -> Callable:
            actual_operation_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper(*args, **kwargs):
                    async with self.monitor_operation(actual_operation_name):
                        return await func(*args, **kwargs)
                return async_wrapper
            else:
                def sync_wrapper(*args, **kwargs):
                    # For sync functions, we'll track them but won't use context manager
                    start_time = time.time()
                    try:
                        result = func(*args, **kwargs)
                        duration = time.time() - start_time
                        # Log sync operation performance
                        self.logger.info(f"Sync operation {actual_operation_name} completed in {duration:.2f}s")
                        return result
                    except Exception as e:
                        duration = time.time() - start_time
                        self.logger.error(f"Sync operation {actual_operation_name} failed after {duration:.2f}s: {e}")
                        raise
                return sync_wrapper
        
        return decorator
    
    async def get_aggregated_metrics(self, operation_name: str) -> Optional[AggregatedMetrics]:
        """Get aggregated metrics for an operation."""
        if operation_name not in self._metrics_history:
            return None
        
        metrics_list = list(self._metrics_history[operation_name])
        if not metrics_list:
            return None
        
        durations = [m.duration for m in metrics_list if m.duration]
        memory_usage = [m.memory_delta for m in metrics_list if m.memory_delta]
        cpu_usage = [m.cpu_percent_avg for m in metrics_list if m.cpu_percent_avg]
        
        successful = sum(1 for m in metrics_list if m.success)
        failed = len(metrics_list) - successful
        
        # Calculate percentiles
        durations.sort()
        p95_idx = int(len(durations) * 0.95)
        p99_idx = int(len(durations) * 0.99)
        
        # Performance level distribution
        perf_distribution = defaultdict(int)
        for metrics in metrics_list:
            perf_distribution[metrics.performance_level] += 1
        
        return AggregatedMetrics(
            operation_name=operation_name,
            total_executions=len(metrics_list),
            successful_executions=successful,
            failed_executions=failed,
            avg_duration=sum(durations) / len(durations) if durations else 0,
            min_duration=min(durations) if durations else 0,
            max_duration=max(durations) if durations else 0,
            p95_duration=durations[p95_idx] if durations else 0,
            p99_duration=durations[p99_idx] if durations else 0,
            avg_memory_usage=sum(memory_usage) / len(memory_usage) if memory_usage else 0,
            peak_memory_usage=max(memory_usage) if memory_usage else 0,
            avg_cpu_usage=sum(cpu_usage) / len(cpu_usage) if cpu_usage else 0,
            peak_cpu_usage=max(cpu_usage) if cpu_usage else 0,
            first_execution=min(m.start_time for m in metrics_list),
            last_execution=max(m.start_time for m in metrics_list),
            performance_distribution=dict(perf_distribution)
        )
    
    async def display_live_metrics(self, duration: int = 60) -> None:
        """Display live performance metrics using Rich."""
        start_time = time.time()
        
        with Live(self._create_metrics_table(), console=self.console, refresh_per_second=2) as live:
            while time.time() - start_time < duration:
                live.update(self._create_metrics_table())
                await asyncio.sleep(0.5)
    
    def _create_metrics_table(self) -> Panel:
        """Create Rich table for metrics display."""
        table = Table(title="🚀 Performance Monitoring Dashboard")
        
        table.add_column("Operation", style="cyan", no_wrap=True)
        table.add_column("Count", style="magenta")
        table.add_column("Avg Duration", style="green")
        table.add_column("Success Rate", style="blue")
        table.add_column("Performance", style="yellow")
        table.add_column("Active", style="red")
        
        for operation_name, count in self._operation_counts.items():
            if operation_name in self._metrics_history:
                metrics_list = list(self._metrics_history[operation_name])
                if metrics_list:
                    avg_duration = sum(m.duration for m in metrics_list if m.duration) / len(metrics_list)
                    success_rate = sum(1 for m in metrics_list if m.success) / len(metrics_list) * 100
                    
                    # Get performance level of most recent operation
                    recent_perf = metrics_list[-1].performance_level if metrics_list else PerformanceLevel.ACCEPTABLE
                    
                    # Count active operations
                    active_count = sum(1 for m in self._active_operations.values() if m.operation_name == operation_name)
                    
                    table.add_row(
                        operation_name,
                        str(count),
                        f"{avg_duration:.2f}s",
                        f"{success_rate:.1f}%",
                        recent_perf.value,
                        str(active_count) if active_count > 0 else "0"
                    )
        
        return Panel(table, title="Performance Metrics", border_style="blue")
    
    async def _alert_slow_operation(self, metrics: PerformanceMetrics) -> None:
        """Alert on slow operations."""
        self.console.print(
            f"[yellow]⚠️  Slow Operation Alert[/yellow]\n"
            f"Operation: {metrics.operation_name}\n"
            f"Duration: {metrics.duration:.2f}s\n"
            f"Performance Level: {metrics.performance_level.value}\n"
            f"Memory Delta: {metrics.memory_delta:.2f}MB\n"
            f"Service: {metrics.service_name or 'Unknown'}"
        )
    
    async def save_metrics_report(self, filepath: Path) -> None:
        """Save comprehensive metrics report to file."""
        report = {
            "generated_at": datetime.now().isoformat(),
            "system_info": {
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
                "platform": "Mac M4"
            },
            "operation_counts": dict(self._operation_counts),
            "aggregated_metrics": {},
            "slow_operations": [m.to_dict() for m in self._slow_operations[-50:]]  # Last 50 slow ops
        }
        
        # Add aggregated metrics
        for operation_name in self._metrics_history.keys():
            agg_metrics = await self.get_aggregated_metrics(operation_name)
            if agg_metrics:
                report["aggregated_metrics"][operation_name] = {
                    "total_executions": agg_metrics.total_executions,
                    "success_rate": agg_metrics.successful_executions / agg_metrics.total_executions,
                    "avg_duration": agg_metrics.avg_duration,
                    "p95_duration": agg_metrics.p95_duration,
                    "performance_distribution": {k.value: v for k, v in agg_metrics.performance_distribution.items()}
                }
        
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.console.print(f"[green]✅ Metrics report saved to {filepath}[/green]")
```

### 3.B Enhanced Lifecycle Management

#### Task: Implement ServiceState Enum and Tracking
**Priority**: High
**Estimated Duration**: 20 minutes

**Target Location**: `src/infrastructure/lifecycle/service_state.py`

**Implementation**:
```python
from enum import Enum, auto
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any, List
import asyncio

class ServiceState(Enum):
    """Service lifecycle states."""
    CREATED = auto()
    INITIALIZING = auto()
    INITIALIZED = auto()
    STARTING = auto()
    RUNNING = auto()
    STOPPING = auto()
    STOPPED = auto()
    FAILED = auto()
    DEGRADED = auto()
    MAINTENANCE = auto()

@dataclass
class ServiceStateTransition:
    """Record of service state transitions."""
    from_state: ServiceState
    to_state: ServiceState
    timestamp: datetime
    reason: Optional[str] = None
    metadata: Dict[str, Any] = None

@dataclass
class ServiceHealthCheck:
    """Health check result for a service."""
    service_name: str
    timestamp: datetime
    healthy: bool
    response_time_ms: float
    details: Dict[str, Any] = None
    error_message: Optional[str] = None

class ServiceStateTracker:
    """Tracks service states and transitions."""
    
    def __init__(self):
        self._states: Dict[str, ServiceState] = {}
        self._transitions: Dict[str, List[ServiceStateTransition]] = {}
        self._health_checks: Dict[str, List[ServiceHealthCheck]] = {}
        self._lock = asyncio.Lock()
    
    async def set_state(self, service_name: str, new_state: ServiceState, reason: Optional[str] = None) -> None:
        """Set service state and record transition."""
        async with self._lock:
            old_state = self._states.get(service_name, ServiceState.CREATED)
            
            if old_state != new_state:
                transition = ServiceStateTransition(
                    from_state=old_state,
                    to_state=new_state,
                    timestamp=datetime.now(),
                    reason=reason
                )
                
                if service_name not in self._transitions:
                    self._transitions[service_name] = []
                self._transitions[service_name].append(transition)
                
                self._states[service_name] = new_state
    
    def get_state(self, service_name: str) -> ServiceState:
        """Get current service state."""
        return self._states.get(service_name, ServiceState.CREATED)
    
    def get_transitions(self, service_name: str) -> List[ServiceStateTransition]:
        """Get all state transitions for a service."""
        return self._transitions.get(service_name, [])
    
    async def record_health_check(self, health_check: ServiceHealthCheck) -> None:
        """Record health check result."""
        async with self._lock:
            if health_check.service_name not in self._health_checks:
                self._health_checks[health_check.service_name] = []
            
            # Keep only last 100 health checks per service
            health_history = self._health_checks[health_check.service_name]
            health_history.append(health_check)
            if len(health_history) > 100:
                health_history.pop(0)
    
    def get_recent_health_checks(self, service_name: str, count: int = 10) -> List[ServiceHealthCheck]:
        """Get recent health checks for a service."""
        return self._health_checks.get(service_name, [])[-count:]
    
    def is_healthy(self, service_name: str) -> bool:
        """Check if service is currently healthy."""
        recent_checks = self.get_recent_health_checks(service_name, 1)
        if not recent_checks:
            return self.get_state(service_name) == ServiceState.RUNNING
        
        return recent_checks[0].healthy
```

#### Task: Create LifecycleManager for Service Coordination
**Priority**: High  
**Estimated Duration**: 45 minutes

**Target Location**: `src/services/lifecycle/lifecycle_manager.py`

**Implementation**:
```python
import asyncio
from typing import Dict, Set, List, Optional, Callable, Any
from collections import defaultdict, deque
import signal
import sys
from contextlib import asynccontextmanager

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.lifecycle.service_state import (
    ServiceState, ServiceStateTracker, ServiceHealthCheck
)

class ServiceDependencyError(Exception):
    """Raised when service dependencies cannot be resolved."""
    pass

class LifecycleManager(AsyncServiceBase):
    """Manages service lifecycle with dependency resolution and health monitoring."""
    
    def __init__(self):
        super().__init__()
        self.console = Console()
        self._services: Dict[str, Any] = {}
        self._dependencies: Dict[str, Set[str]] = defaultdict(set)
        self._dependents: Dict[str, Set[str]] = defaultdict(set)
        self._state_tracker = ServiceStateTracker()
        self._health_check_tasks: Dict[str, asyncio.Task] = {}
        self._shutdown_event = asyncio.Event()
        self._startup_order: List[str] = []
        self._shutdown_order: List[str] = []
        
        # Health check configuration
        self._health_check_interval = 30  # seconds
        self._health_check_timeout = 5    # seconds
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        try:
            signal.signal(signal.SIGTERM, self._signal_handler)
            signal.signal(signal.SIGINT, self._signal_handler)
        except ValueError:
            # Signal handlers can only be set in main thread
            pass
    
    def _signal_handler(self, signum: int, frame) -> None:
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown")
        asyncio.create_task(self.shutdown_all_services())
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute lifecycle management action."""
        if isinstance(data, dict) and 'action' in data:
            action = data['action']
            if action == 'register_service':
                return self.register_service(
                    data['service_name'],
                    data['service_instance'],
                    data.get('dependencies', [])
                )
            elif action == 'start_service':
                return await self.start_service(data['service_name'])
            elif action == 'stop_service':
                return await self.stop_service(data['service_name'])
        return None
    
    def register_service(
        self, 
        service_name: str, 
        service_instance: Any, 
        dependencies: List[str] = None
    ) -> None:
        """Register a service with its dependencies."""
        self._services[service_name] = service_instance
        
        if dependencies:
            self._dependencies[service_name] = set(dependencies)
            for dep in dependencies:
                self._dependents[dep].add(service_name)
        
        self.logger.info(f"Registered service: {service_name}")
    
    def _resolve_startup_order(self) -> List[str]:
        """Resolve service startup order based on dependencies."""
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(service_name: str) -> None:
            if service_name in temp_visited:
                raise ServiceDependencyError(f"Circular dependency detected involving {service_name}")
            
            if service_name not in visited:
                temp_visited.add(service_name)
                
                for dependency in self._dependencies.get(service_name, []):
                    if dependency not in self._services:
                        raise ServiceDependencyError(f"Dependency {dependency} not registered for {service_name}")
                    visit(dependency)
                
                temp_visited.remove(service_name)
                visited.add(service_name)
                order.append(service_name)
        
        for service_name in self._services:
            if service_name not in visited:
                visit(service_name)
        
        return order
    
    async def start_all_services(self) -> None:
        """Start all registered services in dependency order."""
        self._startup_order = self._resolve_startup_order()
        self._shutdown_order = list(reversed(self._startup_order))
        
        self.console.print("[bold blue]🚀 Starting all services...[/bold blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            
            for service_name in self._startup_order:
                task = progress.add_task(f"Starting {service_name}...", total=None)
                
                try:
                    await self.start_service(service_name)
                    progress.update(task, description=f"✅ {service_name} started")
                except Exception as e:
                    progress.update(task, description=f"❌ {service_name} failed: {e}")
                    raise
                
                await asyncio.sleep(0.1)  # Brief pause for visual effect
        
        self.console.print("[bold green]✅ All services started successfully![/bold green]")
        
        # Start health monitoring
        await self._start_health_monitoring()
    
    async def start_service(self, service_name: str) -> None:
        """Start a specific service."""
        if service_name not in self._services:
            raise ValueError(f"Service {service_name} not registered")
        
        service = self._services[service_name]
        
        # Check if dependencies are running
        for dep_name in self._dependencies.get(service_name, []):
            dep_state = self._state_tracker.get_state(dep_name)
            if dep_state != ServiceState.RUNNING:
                raise ServiceDependencyError(f"Dependency {dep_name} is not running (state: {dep_state})")
        
        await self._state_tracker.set_state(service_name, ServiceState.STARTING)
        
        try:
            # Start the service
            if hasattr(service, 'start'):
                await service.start()
            elif hasattr(service, '__aenter__'):
                await service.__aenter__()
            
            await self._state_tracker.set_state(service_name, ServiceState.RUNNING)
            self.logger.info(f"Started service: {service_name}")
            
        except Exception as e:
            await self._state_tracker.set_state(service_name, ServiceState.FAILED, str(e))
            self.logger.error(f"Failed to start service {service_name}: {e}")
            raise
    
    async def stop_service(self, service_name: str) -> None:
        """Stop a specific service."""
        if service_name not in self._services:
            return
        
        service = self._services[service_name]
        current_state = self._state_tracker.get_state(service_name)
        
        if current_state in [ServiceState.STOPPED, ServiceState.FAILED]:
            return
        
        await self._state_tracker.set_state(service_name, ServiceState.STOPPING)
        
        try:
            # Stop health monitoring for this service
            if service_name in self._health_check_tasks:
                self._health_check_tasks[service_name].cancel()
                del self._health_check_tasks[service_name]
            
            # Stop the service
            if hasattr(service, 'stop'):
                await service.stop()
            elif hasattr(service, '__aexit__'):
                await service.__aexit__(None, None, None)
            
            await self._state_tracker.set_state(service_name, ServiceState.STOPPED)
            self.logger.info(f"Stopped service: {service_name}")
            
        except Exception as e:
            await self._state_tracker.set_state(service_name, ServiceState.FAILED, str(e))
            self.logger.error(f"Failed to stop service {service_name}: {e}")
            raise
    
    async def shutdown_all_services(self) -> None:
        """Shutdown all services in reverse dependency order."""
        self.console.print("[bold yellow]🛑 Shutting down all services...[/bold yellow]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            
            for service_name in self._shutdown_order:
                task = progress.add_task(f"Stopping {service_name}...", total=None)
                
                try:
                    await self.stop_service(service_name)
                    progress.update(task, description=f"✅ {service_name} stopped")
                except Exception as e:
                    progress.update(task, description=f"⚠️ {service_name} error: {e}")
                    # Continue with shutdown even if individual services fail
                
                await asyncio.sleep(0.1)
        
        self._shutdown_event.set()
        self.console.print("[bold green]✅ All services shut down[/bold green]")
    
    async def _start_health_monitoring(self) -> None:
        """Start health check monitoring for all services."""
        for service_name in self._services:
            task = asyncio.create_task(self._health_check_loop(service_name))
            self._health_check_tasks[service_name] = task
    
    async def _health_check_loop(self, service_name: str) -> None:
        """Continuous health check loop for a service."""
        while not self._shutdown_event.is_set():
            try:
                health_check = await self._perform_health_check(service_name)
                await self._state_tracker.record_health_check(health_check)
                
                # Update service state based on health
                if not health_check.healthy:
                    current_state = self._state_tracker.get_state(service_name)
                    if current_state == ServiceState.RUNNING:
                        await self._state_tracker.set_state(service_name, ServiceState.DEGRADED, "Health check failed")
                
            except Exception as e:
                self.logger.error(f"Health check error for {service_name}: {e}")
            
            await asyncio.sleep(self._health_check_interval)
    
    async def _perform_health_check(self, service_name: str) -> ServiceHealthCheck:
        """Perform health check for a service."""
        service = self._services[service_name]
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Try to call health check method if available
            if hasattr(service, 'health_check'):
                healthy = await asyncio.wait_for(
                    service.health_check(),
                    timeout=self._health_check_timeout
                )
            else:
                # Basic health check - just verify service is running
                healthy = self._state_tracker.get_state(service_name) == ServiceState.RUNNING
            
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            return ServiceHealthCheck(
                service_name=service_name,
                timestamp=datetime.now(),
                healthy=healthy,
                response_time_ms=response_time
            )
            
        except asyncio.TimeoutError:
            response_time = self._health_check_timeout * 1000
            return ServiceHealthCheck(
                service_name=service_name,
                timestamp=datetime.now(),
                healthy=False,
                response_time_ms=response_time,
                error_message="Health check timeout"
            )
        except Exception as e:
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000
            return ServiceHealthCheck(
                service_name=service_name,
                timestamp=datetime.now(),
                healthy=False,
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    @asynccontextmanager
    async def managed_lifecycle(self):
        """Context manager for complete service lifecycle."""
        try:
            await self.start_all_services()
            yield self
        finally:
            await self.shutdown_all_services()
    
    def get_service_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all services."""
        status = {}
        
        for service_name in self._services:
            state = self._state_tracker.get_state(service_name)
            recent_health = self._state_tracker.get_recent_health_checks(service_name, 1)
            
            status[service_name] = {
                "state": state.name,
                "healthy": self._state_tracker.is_healthy(service_name),
                "dependencies": list(self._dependencies.get(service_name, [])),
                "dependents": list(self._dependents.get(service_name, [])),
                "last_health_check": recent_health[0].timestamp.isoformat() if recent_health else None
            }
        
        return status
```

## Success Criteria

### Phase III Completion Checklist
- [ ] PerformanceMetrics dataclass with comprehensive metrics tracking
- [ ] PerformanceMonitoringService with Mac M4 optimization
- [ ] Async operation decorators for automatic monitoring
- [ ] ServiceState enum with complete lifecycle states
- [ ] LifecycleManager with dependency resolution
- [ ] Health check system with configurable intervals
- [ ] Rich console integration for live metrics display
- [ ] Graceful shutdown with proper dependency ordering
- [ ] Performance reports with JSON export
- [ ] Signal handler for graceful shutdown

### Performance Benchmarks
- Monitoring overhead < 5% of operation time
- Health check response time < 100ms
- Memory usage tracking accuracy within 1MB
- Service startup time < 30 seconds for full stack
- Shutdown time < 10 seconds with proper cleanup

## Integration Examples

### Using Performance Monitoring
```python
# Decorator usage
@performance_monitor.create_performance_decorator("data_processing")
async def process_data(data):
    # Your code here
    return processed_data

# Context manager usage
async with performance_monitor.monitor_operation("file_upload"):
    await upload_file_to_s3(file_path)

# Service integration
lifecycle_manager.register_service("data_processor", data_processor, ["storage", "logger"])
async with lifecycle_manager.managed_lifecycle():
    # All services are running with health monitoring
    await run_application()
```

This comprehensive implementation provides production-ready performance monitoring and lifecycle management optimized for the Mac M4 architecture with rich visual feedback and robust error handling.