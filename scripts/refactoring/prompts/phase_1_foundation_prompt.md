# Phase I: Foundation Strengthening - LLM Execution Prompt

## Context
You are helping with Phase I of the LexGenius refactoring project. This phase focuses on migrating utility functions to standardized service architecture and implementing enhanced dependency injection patterns.

## Project Architecture
- **Service Architecture**: All services extend `AsyncServiceBase` from `src/infrastructure/patterns/component_base.py`
- **Error Handling**: Use `ComponentImplementation` base class for standardized error handling
- **Dependency Injection**: Factory pattern via `MainServiceFactory`
- **Async Patterns**: All I/O operations should be asynchronous

## Phase I Objectives

### 1.A ComponentImplementation Migration for Utils

#### Task: Delete Redundant HTML Components
**Priority**: High
**Estimated Duration**: 5 minutes

```bash
# Current state: Redundant functionality exists
src/core/html/          # ← DELETE THIS (duplicates src/services/html/)
src/services/html/      # ← KEEP THIS (active services)
```

**Actions Required**:
1. Verify `src/services/html/` contains all functionality from `src/core/html/`
2. Check for any imports pointing to `src/core/html/` and update them
3. Delete the entire `src/core/html/` directory
4. Run tests to ensure no breakage

#### Task: Migrate async_decorators.py to AsyncDecoratorService
**Priority**: High  
**Estimated Duration**: 20 minutes

**Current Location**: `src/lib/utils/async_decorators.py`
**Target Location**: `src/services/utils/async_decorator_service.py`

**Migration Pattern**:
```python
# FROM: Utility functions
def async_retry(retries: int = 3):
    def decorator(func):
        # implementation
    return decorator

# TO: Service with ComponentImplementation
from src.infrastructure.patterns.component_base import ComponentImplementation

class AsyncDecoratorService(ComponentImplementation):
    async def _execute_action(self, data: Any) -> Any:
        # Implement decorator factory methods
        return processed_data
    
    def create_retry_decorator(self, retries: int = 3) -> Callable:
        """Create retry decorator for async functions."""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                for attempt in range(retries):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        if attempt == retries - 1:
                            raise
                        await asyncio.sleep(2 ** attempt)
            return wrapper
        return decorator
```

**Key Requirements**:
- Preserve all existing decorator functionality
- Add proper error handling and logging
- Maintain async/await patterns
- Add type hints throughout
- Create factory methods for decorators

#### Task: Migrate cleanup_utils.py to ResourceCleanupService  
**Priority**: High
**Estimated Duration**: 25 minutes

**Current Location**: `src/lib/utils/cleanup_utils.py`
**Target Location**: `src/services/utils/resource_cleanup_service.py`

**Migration Pattern**:
```python
from src.infrastructure.patterns.component_base import ComponentImplementation

class ResourceCleanupService(ComponentImplementation):
    async def _execute_action(self, data: Any) -> Any:
        return await self._cleanup_resources(data)
    
    async def cleanup_browser_contexts(self, contexts: List[BrowserContext]) -> None:
        """Clean up browser contexts with proper error handling."""
        
    async def cleanup_temporary_files(self, file_paths: List[Path]) -> None:
        """Clean up temporary files and directories."""
        
    async def cleanup_async_resources(self, resources: List[Any]) -> None:
        """Generic async resource cleanup."""
```

#### Task: Migrate performance_monitor.py to PerformanceMonitorService
**Priority**: High
**Estimated Duration**: 30 minutes

**Current Location**: `src/lib/utils/performance_monitor.py`  
**Target Location**: `src/services/utils/performance_monitor_service.py`

**Migration Pattern**:
```python
from dataclasses import dataclass
from src.infrastructure.patterns.component_base import ComponentImplementation

@dataclass
class PerformanceMetrics:
    operation_name: str
    duration: float
    memory_used: float
    cpu_percent: float
    timestamp: datetime

class PerformanceMonitorService(ComponentImplementation):
    async def _execute_action(self, data: Any) -> PerformanceMetrics:
        return await self._monitor_operation(data)
    
    def create_performance_decorator(self) -> Callable:
        """Create decorator for automatic performance monitoring."""
```

### 1.B Factory Pattern Enhancement

#### Task: Implement Type-Safe Service Factories
**Priority**: High
**Estimated Duration**: 35 minutes

**Target Location**: `src/infrastructure/factories/enhanced_service_factory.py`

**Implementation**:
```python
from typing import TypeVar, Generic, Protocol, Type
from abc import ABC, abstractmethod

T = TypeVar('T')

class ServiceFactoryProtocol(Protocol, Generic[T]):
    async def create(self, config: Any) -> T:
        """Create service instance with proper configuration."""
        ...

class EnhancedServiceFactory(Generic[T]):
    def __init__(self, service_class: Type[T]):
        self._service_class = service_class
        self._dependencies: Dict[str, Any] = {}
    
    def with_dependency(self, name: str, dependency: Any) -> 'EnhancedServiceFactory[T]':
        """Add dependency for service creation."""
        self._dependencies[name] = dependency
        return self
    
    async def create(self, config: Any = None) -> T:
        """Create service with resolved dependencies."""
        return await self._service_class.create_with_dependencies(
            config=config,
            dependencies=self._dependencies
        )
```

### 1.C Service Registry Implementation

#### Task: Create Lightweight Service Registry
**Priority**: High
**Estimated Duration**: 40 minutes

**Target Location**: `src/infrastructure/registry/service_registry.py`

**Implementation**:
```python
from typing import Dict, Type, TypeVar, Optional, Any
from enum import Enum

T = TypeVar('T')

class ServiceLifecycle(Enum):
    SINGLETON = "singleton"
    FACTORY = "factory"
    SCOPED = "scoped"

class ServiceRegistry:
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Any] = {}
        self._singletons: Dict[str, Any] = {}
    
    def register_factory(self, service_name: str, factory: ServiceFactoryProtocol[T]) -> None:
        """Register a service factory."""
        
    def register_singleton(self, service_name: str, instance: T) -> None:
        """Register a singleton service instance."""
        
    async def get_service(self, service_name: str) -> Optional[Any]:
        """Get service instance with proper lifecycle management."""
        
    async def create_service_graph(self) -> Dict[str, Any]:
        """Create complete service dependency graph."""
```

## Testing Requirements

For each migrated service, create comprehensive tests:

```python
# Example test structure
import pytest
from src.services.utils.async_decorator_service import AsyncDecoratorService

class TestAsyncDecoratorService:
    @pytest.fixture
    async def service(self):
        return AsyncDecoratorService()
    
    async def test_retry_decorator_success(self, service):
        # Test successful operation
        
    async def test_retry_decorator_failure(self, service):
        # Test retry logic with eventual failure
        
    async def test_retry_decorator_recovery(self, service):
        # Test recovery after transient failures
```

## Success Criteria

### Phase I Completion Checklist
- [ ] `src/core/html/` directory deleted (verified no functionality loss)
- [ ] `AsyncDecoratorService` created with all decorator functionality
- [ ] `ResourceCleanupService` created with proper async cleanup
- [ ] `PerformanceMonitorService` created with metrics collection
- [ ] `EnhancedServiceFactory` implemented with type safety
- [ ] `ServiceRegistry` implemented with lifecycle management
- [ ] All migrated services extend appropriate base classes
- [ ] Comprehensive test coverage (>90%) for all new services
- [ ] All imports updated to point to new service locations
- [ ] No breaking changes to existing functionality

### Post-Migration Validation
```bash
# Run these commands to validate migration
python run_tests.py --parallel                    # All tests pass
pytest tests/ -m unit -v                         # Unit tests pass
python -m mypy src/services/utils/                # Type checking passes
python scripts/refactoring/validate_phase_1.py   # Custom validation
```

## Error Handling Patterns

All services should implement consistent error handling:

```python
async def _execute_action(self, data: Any) -> Any:
    try:
        return await self._process_data(data)
    except ValidationError as e:
        self.logger.error(f"Validation failed: {e}")
        raise ServiceValidationError(f"Invalid data: {e}")
    except Exception as e:
        self.logger.error(f"Unexpected error: {e}")
        raise ServiceExecutionError(f"Operation failed: {e}")
```

## Performance Considerations

- Use async/await throughout for I/O operations
- Implement proper resource cleanup in `__aenter__`/`__aexit__`
- Add performance monitoring decorators to critical paths
- Optimize for Mac M4/128GB architecture with parallel processing

## Integration Notes

- All services must be compatible with existing `MainServiceFactory`
- Preserve backward compatibility during migration
- Update CLAUDE.md with new service locations
- Services should be registered in the main service registry
- Feature flags may be used for gradual rollout if needed