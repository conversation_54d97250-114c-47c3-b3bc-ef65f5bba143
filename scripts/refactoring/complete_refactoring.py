#!/usr/bin/env python3
"""
Refactoring Completion Script

Completes all remaining refactoring tasks from Phases 1 and 3.
Implements the missing components following established patterns.
"""

import asyncio
import sys
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, MofNCompleteColumn
from rich.panel import Panel
from rich.table import Table

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

@dataclass
class CompletionTask:
    task_id: str
    description: str
    phase: str
    file_path: str
    content: str
    dependencies: List[str] = None

class RefactoringCompleter:
    """Completes remaining refactoring tasks."""
    
    def __init__(self, console: Console):
        self.console = console
        self.project_root = project_root
        self.completed_tasks = []
        self.failed_tasks = []
    
    def get_completion_tasks(self) -> List[CompletionTask]:
        """Define all remaining tasks to complete the refactoring."""
        return [
            # Phase 1 - Missing Tasks
            CompletionTask(
                task_id="phase1_cleanup_service",
                description="Create ResourceCleanupService",
                phase="Phase 1",
                file_path="src/services/infrastructure/resource_cleanup_service.py",
                content='''
import asyncio
import gc
import logging
import psutil
import weakref
from typing import Set, Any, Optional, Callable, List
from contextlib import asynccontextmanager
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.monitoring.performance_metrics import PerformanceMetrics

class ResourceCleanupService(AsyncServiceBase):
    """Service for managing resource cleanup and memory optimization."""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self._cleanup_handlers: Set[Callable] = set()
        self._active_resources: Set[weakref.ref] = set()
        self._cleanup_interval = 300  # 5 minutes
        self._cleanup_task: Optional[asyncio.Task] = None
        self._metrics = PerformanceMetrics(service_name="ResourceCleanupService")
    
    async def start(self):
        """Start the cleanup service."""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            self.logger.info("Resource cleanup service started")
    
    async def stop(self):
        """Stop the cleanup service."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
            self.logger.info("Resource cleanup service stopped")
    
    def register_cleanup_handler(self, handler: Callable):
        """Register a cleanup handler function."""
        self._cleanup_handlers.add(handler)
        self.logger.debug(f"Registered cleanup handler: {handler.__name__}")
    
    def unregister_cleanup_handler(self, handler: Callable):
        """Unregister a cleanup handler function."""
        self._cleanup_handlers.discard(handler)
        self.logger.debug(f"Unregistered cleanup handler: {handler.__name__}")
    
    def track_resource(self, resource: Any):
        """Track a resource for automatic cleanup."""
        if hasattr(resource, '__del__') or hasattr(resource, 'close'):
            ref = weakref.ref(resource, self._resource_finalized)
            self._active_resources.add(ref)
    
    def _resource_finalized(self, ref: weakref.ref):
        """Called when a tracked resource is finalized."""
        self._active_resources.discard(ref)
    
    async def force_cleanup(self) -> Dict[str, Any]:
        """Force immediate cleanup and return statistics."""
        self.logger.info("Starting forced cleanup")
        cleanup_stats = {
            'handlers_executed': 0,
            'memory_before': self._get_memory_usage(),
            'gc_collected': 0,
            'active_resources_before': len(self._active_resources)
        }
        
        # Execute all cleanup handlers
        for handler in self._cleanup_handlers.copy():
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler()
                else:
                    handler()
                cleanup_stats['handlers_executed'] += 1
            except Exception as e:
                self.logger.error(f"Cleanup handler {handler.__name__} failed: {e}")
        
        # Force garbage collection
        cleanup_stats['gc_collected'] = gc.collect()
        
        # Update metrics
        cleanup_stats['memory_after'] = self._get_memory_usage()
        cleanup_stats['memory_freed'] = cleanup_stats['memory_before'] - cleanup_stats['memory_after']
        cleanup_stats['active_resources_after'] = len(self._active_resources)
        
        self.logger.info(f"Cleanup completed: {cleanup_stats}")
        return cleanup_stats
    
    async def _periodic_cleanup(self):
        """Periodic cleanup task."""
        while True:
            try:
                await asyncio.sleep(self._cleanup_interval)
                await self.force_cleanup()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Periodic cleanup failed: {e}")
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    @asynccontextmanager
    async def cleanup_context(self, description: str = "operation"):
        """Context manager that ensures cleanup after operation."""
        self.logger.debug(f"Starting cleanup context: {description}")
        initial_memory = self._get_memory_usage()
        
        try:
            yield
        finally:
            # Cleanup after operation
            await self.force_cleanup()
            final_memory = self._get_memory_usage()
            memory_change = final_memory - initial_memory
            
            if memory_change > 0:
                self.logger.warning(f"Memory increased by {memory_change:.1f}MB during {description}")
            else:
                self.logger.debug(f"Memory freed: {abs(memory_change):.1f}MB during {description}")
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute cleanup action."""
        if isinstance(data, dict) and data.get('action') == 'cleanup':
            return await self.force_cleanup()
        return data

# Global cleanup service instance
cleanup_service = ResourceCleanupService()
'''
            ),
            
            CompletionTask(
                task_id="phase1_performance_monitor_service",
                description="Create PerformanceMonitorService",
                phase="Phase 1", 
                file_path="src/services/infrastructure/performance_monitor_service.py",
                content='''
import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.monitoring.performance_metrics import PerformanceMetrics

@dataclass
class OperationMetrics:
    """Metrics for a specific operation."""
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class PerformanceMonitorService(AsyncServiceBase):
    """Service for monitoring and tracking performance metrics."""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self._active_operations: Dict[str, OperationMetrics] = {}
        self._completed_operations: List[OperationMetrics] = []
        self._max_history = 1000
        self._performance_thresholds: Dict[str, float] = {}
        self._alert_handlers: List[Callable] = []
    
    def set_performance_threshold(self, operation_name: str, threshold_seconds: float):
        """Set performance threshold for an operation."""
        self._performance_thresholds[operation_name] = threshold_seconds
        self.logger.info(f"Set performance threshold for {operation_name}: {threshold_seconds}s")
    
    def add_alert_handler(self, handler: Callable[[OperationMetrics], None]):
        """Add handler for performance alerts."""
        self._alert_handlers.append(handler)
    
    @asynccontextmanager
    async def monitor_operation(self, operation_name: str, **metadata):
        """Context manager to monitor an operation's performance."""
        operation_id = f"{operation_name}_{int(time.time() * 1000)}"
        metrics = OperationMetrics(
            operation_name=operation_name,
            start_time=time.time(),
            metadata=metadata
        )
        
        self._active_operations[operation_id] = metrics
        self.logger.debug(f"Started monitoring operation: {operation_name}")
        
        try:
            yield metrics
            metrics.success = True
        except Exception as e:
            metrics.success = False
            metrics.error_message = str(e)
            self.logger.error(f"Operation {operation_name} failed: {e}")
            raise
        finally:
            # Finalize metrics
            metrics.end_time = time.time()
            metrics.duration = metrics.end_time - metrics.start_time
            
            # Move to completed operations
            self._active_operations.pop(operation_id, None)
            self._completed_operations.append(metrics)
            
            # Trim history if needed
            if len(self._completed_operations) > self._max_history:
                self._completed_operations = self._completed_operations[-self._max_history:]
            
            # Check thresholds and trigger alerts
            await self._check_performance_threshold(metrics)
            
            self.logger.info(
                f"Operation {operation_name} completed in {metrics.duration:.3f}s "
                f"(success: {metrics.success})"
            )
    
    async def _check_performance_threshold(self, metrics: OperationMetrics):
        """Check if operation exceeded performance threshold."""
        threshold = self._performance_thresholds.get(metrics.operation_name)
        if threshold and metrics.duration and metrics.duration > threshold:
            self.logger.warning(
                f"Performance threshold exceeded for {metrics.operation_name}: "
                f"{metrics.duration:.3f}s > {threshold}s"
            )
            
            # Trigger alert handlers
            for handler in self._alert_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(metrics)
                    else:
                        handler(metrics)
                except Exception as e:
                    self.logger.error(f"Alert handler failed: {e}")
    
    def get_operation_stats(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """Get statistics for operations."""
        operations = self._completed_operations
        if operation_name:
            operations = [op for op in operations if op.operation_name == operation_name]
        
        if not operations:
            return {"message": "No operations found"}
        
        durations = [op.duration for op in operations if op.duration is not None]
        successes = [op for op in operations if op.success]
        
        stats = {
            "total_operations": len(operations),
            "successful_operations": len(successes),
            "failed_operations": len(operations) - len(successes),
            "success_rate": len(successes) / len(operations) * 100 if operations else 0
        }
        
        if durations:
            stats.update({
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "total_duration": sum(durations)
            })
        
        return stats
    
    def get_active_operations(self) -> List[Dict[str, Any]]:
        """Get currently active operations."""
        current_time = time.time()
        return [
            {
                "operation_name": metrics.operation_name,
                "elapsed_time": current_time - metrics.start_time,
                "metadata": metrics.metadata
            }
            for metrics in self._active_operations.values()
        ]
    
    def get_recent_operations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent completed operations."""
        recent = self._completed_operations[-limit:] if self._completed_operations else []
        return [
            {
                "operation_name": op.operation_name,
                "duration": op.duration,
                "success": op.success,
                "error_message": op.error_message,
                "metadata": op.metadata
            }
            for op in reversed(recent)
        ]
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute performance monitoring action."""
        if isinstance(data, dict):
            action = data.get('action')
            if action == 'get_stats':
                return self.get_operation_stats(data.get('operation_name'))
            elif action == 'get_active':
                return self.get_active_operations()
            elif action == 'get_recent':
                return self.get_recent_operations(data.get('limit', 10))
        return data

# Global performance monitor instance
performance_monitor = PerformanceMonitorService()
'''
            ),
            
            CompletionTask(
                task_id="phase1_service_factory",
                description="Create TypeSafeServiceFactory",
                phase="Phase 1",
                file_path="src/infrastructure/factories/type_safe_factory.py",
                content='''
import logging
from typing import TypeVar, Type, Dict, Any, Optional, Protocol, runtime_checkable
from dataclasses import dataclass
from src.infrastructure.config.service_configs import ServiceConfig
from src.infrastructure.monitoring.service_states import ServiceStateTracker, ServiceState

T = TypeVar('T')
S = TypeVar('S', bound=ServiceConfig)

@runtime_checkable
class ServiceProtocol(Protocol):
    """Protocol that all services should implement."""
    
    async def start(self) -> None:
        """Start the service."""
        ...
    
    async def stop(self) -> None:
        """Stop the service."""
        ...
    
    def is_healthy(self) -> bool:
        """Check if service is healthy."""
        ...

@dataclass
class ServiceRegistration:
    """Registration information for a service."""
    service_class: Type[T]
    config_class: Type[S]
    singleton: bool = True
    dependencies: list = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

class TypeSafeServiceFactory:
    """Type-safe factory for creating and managing services."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._registrations: Dict[str, ServiceRegistration] = {}
        self._instances: Dict[str, Any] = {}
        self._configs: Dict[str, ServiceConfig] = {}
        self.state_tracker = ServiceStateTracker()
    
    def register_service(
        self, 
        service_class: Type[T], 
        config_class: Type[S],
        service_name: Optional[str] = None,
        singleton: bool = True,
        dependencies: Optional[list] = None
    ) -> None:
        """Register a service with the factory."""
        name = service_name or service_class.__name__
        
        registration = ServiceRegistration(
            service_class=service_class,
            config_class=config_class,
            singleton=singleton,
            dependencies=dependencies or []
        )
        
        self._registrations[name] = registration
        self.logger.info(f"Registered service: {name}")
    
    def register_config(self, service_name: str, config: ServiceConfig) -> None:
        """Register configuration for a service."""
        self._configs[service_name] = config
        self.logger.debug(f"Registered config for service: {service_name}")
    
    async def create_service(self, service_name: str, **kwargs) -> T:
        """Create a service instance with type safety."""
        if service_name not in self._registrations:
            raise ValueError(f"Service {service_name} is not registered")
        
        registration = self._registrations[service_name]
        
        # Check if singleton and already exists
        if registration.singleton and service_name in self._instances:
            return self._instances[service_name]
        
        # Set initial state
        self.state_tracker.set_state(service_name, ServiceState.INITIALIZING)
        
        try:
            # Get configuration
            config = self._configs.get(service_name)
            if config is None:
                # Create default config
                config = registration.config_class()
                self._configs[service_name] = config
            
            # Validate config type
            if not isinstance(config, registration.config_class):
                raise TypeError(
                    f"Config for {service_name} must be of type {registration.config_class.__name__}"
                )
            
            # Create dependencies first
            dependencies = {}
            for dep_name in registration.dependencies:
                dependencies[dep_name] = await self.create_service(dep_name)
            
            # Create service instance
            if dependencies:
                service = registration.service_class(config=config, dependencies=dependencies, **kwargs)
            else:
                service = registration.service_class(config=config, **kwargs)
            
            # Validate service implements protocol
            if not isinstance(service, ServiceProtocol):
                self.logger.warning(f"Service {service_name} does not implement ServiceProtocol")
            
            # Store instance if singleton
            if registration.singleton:
                self._instances[service_name] = service
            
            # Update state
            self.state_tracker.set_state(service_name, ServiceState.READY)
            
            self.logger.info(f"Created service: {service_name}")
            return service
            
        except Exception as e:
            self.state_tracker.set_state(service_name, ServiceState.FAILED, error_message=str(e))
            self.logger.error(f"Failed to create service {service_name}: {e}")
            raise
    
    async def get_service(self, service_name: str) -> Optional[T]:
        """Get an existing service instance."""
        if service_name in self._instances:
            return self._instances[service_name]
        
        # Try to create if registered
        if service_name in self._registrations:
            return await self.create_service(service_name)
        
        return None
    
    async def start_service(self, service_name: str) -> None:
        """Start a service."""
        service = await self.get_service(service_name)
        if service and hasattr(service, 'start'):
            self.state_tracker.set_state(service_name, ServiceState.RUNNING)
            await service.start()
            self.logger.info(f"Started service: {service_name}")
    
    async def stop_service(self, service_name: str) -> None:
        """Stop a service."""
        service = await self.get_service(service_name)
        if service and hasattr(service, 'stop'):
            self.state_tracker.set_state(service_name, ServiceState.STOPPING)
            await service.stop()
            self.state_tracker.set_state(service_name, ServiceState.STOPPED)
            self.logger.info(f"Stopped service: {service_name}")
    
    def list_services(self) -> Dict[str, Dict[str, Any]]:
        """List all registered services and their status."""
        services = {}
        for name, registration in self._registrations.items():
            state_info = self.state_tracker.get_state(name)
            services[name] = {
                "class": registration.service_class.__name__,
                "config_class": registration.config_class.__name__,
                "singleton": registration.singleton,
                "dependencies": registration.dependencies,
                "state": state_info.current_state.name if state_info else "UNKNOWN",
                "has_instance": name in self._instances
            }
        return services
    
    async def shutdown_all(self) -> None:
        """Shutdown all services."""
        for service_name in list(self._instances.keys()):
            try:
                await self.stop_service(service_name)
            except Exception as e:
                self.logger.error(f"Error stopping service {service_name}: {e}")
        
        self._instances.clear()
        self.logger.info("All services shut down")

# Global factory instance
service_factory = TypeSafeServiceFactory()
'''
            ),
            
            CompletionTask(
                task_id="phase1_service_registry",
                description="Create ServiceRegistry",
                phase="Phase 1",
                file_path="src/infrastructure/registry/service_registry.py", 
                content='''
import asyncio
import logging
from typing import Dict, Set, Optional, Any, Callable, List
from dataclasses import dataclass, field
from datetime import datetime
from src.infrastructure.monitoring.service_states import ServiceStateTracker, ServiceState
from src.infrastructure.factories.type_safe_factory import ServiceProtocol

@dataclass
class ServiceInfo:
    """Information about a registered service."""
    name: str
    service_type: str
    version: str = "1.0.0"
    description: str = ""
    dependencies: List[str] = field(default_factory=list)
    tags: Set[str] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)
    registered_at: datetime = field(default_factory=datetime.utcnow)
    last_health_check: Optional[datetime] = None
    is_healthy: bool = True

@dataclass 
class ServiceDiscoveryEvent:
    """Event for service discovery notifications."""
    event_type: str  # 'registered', 'unregistered', 'health_changed'
    service_name: str
    service_info: ServiceInfo
    timestamp: datetime = field(default_factory=datetime.utcnow)

class ServiceRegistry:
    """Lightweight service registry for discovery and health monitoring."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._services: Dict[str, ServiceInfo] = {}
        self._service_instances: Dict[str, Any] = {}
        self._event_handlers: List[Callable[[ServiceDiscoveryEvent], None]] = []
        self.state_tracker = ServiceStateTracker()
        self._health_check_interval = 60  # seconds
        self._health_check_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """Start the service registry."""
        if not self._health_check_task:
            self._health_check_task = asyncio.create_task(self._periodic_health_checks())
            self.logger.info("Service registry started")
    
    async def stop(self):
        """Stop the service registry."""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
            self.logger.info("Service registry stopped")
    
    def register_service(
        self,
        name: str,
        service_instance: Any,
        service_type: str = "generic",
        version: str = "1.0.0",
        description: str = "",
        dependencies: Optional[List[str]] = None,
        tags: Optional[Set[str]] = None,
        **metadata
    ) -> ServiceInfo:
        """Register a service with the registry."""
        
        service_info = ServiceInfo(
            name=name,
            service_type=service_type,
            version=version,
            description=description,
            dependencies=dependencies or [],
            tags=tags or set(),
            metadata=metadata
        )
        
        self._services[name] = service_info
        self._service_instances[name] = service_instance
        self.state_tracker.set_state(name, ServiceState.READY)
        
        # Emit registration event
        event = ServiceDiscoveryEvent(
            event_type="registered",
            service_name=name,
            service_info=service_info
        )
        self._emit_event(event)
        
        self.logger.info(f"Registered service: {name} ({service_type} v{version})")
        return service_info
    
    def unregister_service(self, name: str) -> bool:
        """Unregister a service from the registry."""
        if name not in self._services:
            return False
        
        service_info = self._services.pop(name)
        self._service_instances.pop(name, None)
        self.state_tracker.set_state(name, ServiceState.STOPPED)
        
        # Emit unregistration event
        event = ServiceDiscoveryEvent(
            event_type="unregistered",
            service_name=name,
            service_info=service_info
        )
        self._emit_event(event)
        
        self.logger.info(f"Unregistered service: {name}")
        return True
    
    def get_service(self, name: str) -> Optional[Any]:
        """Get a service instance by name."""
        return self._service_instances.get(name)
    
    def get_service_info(self, name: str) -> Optional[ServiceInfo]:
        """Get service information by name."""
        return self._services.get(name)
    
    def list_services(
        self,
        service_type: Optional[str] = None,
        tags: Optional[Set[str]] = None,
        healthy_only: bool = False
    ) -> List[ServiceInfo]:
        """List services matching the criteria."""
        services = list(self._services.values())
        
        if service_type:
            services = [s for s in services if s.service_type == service_type]
        
        if tags:
            services = [s for s in services if tags.issubset(s.tags)]
        
        if healthy_only:
            services = [s for s in services if s.is_healthy]
        
        return services
    
    def find_services_by_dependency(self, dependency: str) -> List[ServiceInfo]:
        """Find services that depend on a specific service."""
        return [
            service_info for service_info in self._services.values()
            if dependency in service_info.dependencies
        ]
    
    def get_dependency_graph(self) -> Dict[str, List[str]]:
        """Get the service dependency graph."""
        return {
            name: service_info.dependencies
            for name, service_info in self._services.items()
        }
    
    def validate_dependencies(self) -> List[str]:
        """Validate that all service dependencies are satisfied."""
        missing_deps = []
        
        for service_name, service_info in self._services.items():
            for dep in service_info.dependencies:
                if dep not in self._services:
                    missing_deps.append(f"{service_name} depends on missing service: {dep}")
        
        return missing_deps
    
    async def check_service_health(self, name: str) -> bool:
        """Check the health of a specific service."""
        service = self._service_instances.get(name)
        service_info = self._services.get(name)
        
        if not service or not service_info:
            return False
        
        try:
            # Check if service implements health check
            if hasattr(service, 'is_healthy'):
                is_healthy = service.is_healthy()
                if asyncio.iscoroutine(is_healthy):
                    is_healthy = await is_healthy
            elif isinstance(service, ServiceProtocol):
                is_healthy = service.is_healthy()
            else:
                # Default: assume healthy if no exceptions
                is_healthy = True
            
            # Update health status
            if service_info.is_healthy != is_healthy:
                service_info.is_healthy = is_healthy
                event = ServiceDiscoveryEvent(
                    event_type="health_changed",
                    service_name=name,
                    service_info=service_info
                )
                self._emit_event(event)
            
            service_info.last_health_check = datetime.utcnow()
            return is_healthy
            
        except Exception as e:
            self.logger.error(f"Health check failed for service {name}: {e}")
            service_info.is_healthy = False
            service_info.last_health_check = datetime.utcnow()
            return False
    
    async def _periodic_health_checks(self):
        """Perform periodic health checks on all services."""
        while True:
            try:
                await asyncio.sleep(self._health_check_interval)
                
                for service_name in list(self._services.keys()):
                    try:
                        await self.check_service_health(service_name)
                    except Exception as e:
                        self.logger.error(f"Error checking health of {service_name}: {e}")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Periodic health check failed: {e}")
    
    def add_event_handler(self, handler: Callable[[ServiceDiscoveryEvent], None]):
        """Add an event handler for service discovery events."""
        self._event_handlers.append(handler)
    
    def remove_event_handler(self, handler: Callable[[ServiceDiscoveryEvent], None]):
        """Remove an event handler."""
        if handler in self._event_handlers:
            self._event_handlers.remove(handler)
    
    def _emit_event(self, event: ServiceDiscoveryEvent):
        """Emit a service discovery event to all handlers."""
        for handler in self._event_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    asyncio.create_task(handler(event))
                else:
                    handler(event)
            except Exception as e:
                self.logger.error(f"Event handler failed: {e}")
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        healthy_services = sum(1 for s in self._services.values() if s.is_healthy)
        
        return {
            "total_services": len(self._services),
            "healthy_services": healthy_services,
            "unhealthy_services": len(self._services) - healthy_services,
            "service_types": list(set(s.service_type for s in self._services.values())),
            "last_health_check_interval": self._health_check_interval
        }

# Global registry instance
service_registry = ServiceRegistry()
'''
            ),
            
            # Phase 3 - Missing Tasks
            CompletionTask(
                task_id="phase3_monitoring_service",
                description="Create PerformanceMonitoringService",
                phase="Phase 3",
                file_path="src/services/monitoring/performance_monitoring_service.py",
                content='''
import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from src.infrastructure.patterns.component_base import AsyncServiceBase
from src.infrastructure.monitoring.performance_metrics import PerformanceMetrics, ServiceHealthStatus
from src.infrastructure.monitoring.service_states import ServiceStateTracker, ServiceState

@dataclass
class PerformanceAlert:
    """Performance alert definition."""
    metric_name: str
    threshold: float
    comparison: str  # 'gt', 'lt', 'eq'
    message: str
    severity: str = "warning"  # warning, error, critical
    
@dataclass
class MonitoringConfig:
    """Configuration for performance monitoring."""
    collection_interval: int = 30  # seconds
    retention_period: int = 3600  # seconds (1 hour)
    alert_cooldown: int = 300  # seconds (5 minutes)
    max_metrics_history: int = 1000

class PerformanceMonitoringService(AsyncServiceBase):
    """Comprehensive performance monitoring service."""
    
    def __init__(self, config: Optional[MonitoringConfig] = None):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.config = config or MonitoringConfig()
        
        # Storage
        self._metrics_history: List[PerformanceMetrics] = []
        self._service_health: Dict[str, ServiceHealthStatus] = {}
        self._active_alerts: Dict[str, PerformanceAlert] = {}
        self._alert_history: List[Dict[str, Any]] = []
        self._last_alert_time: Dict[str, datetime] = {}
        
        # Monitoring
        self._collection_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._alert_handlers: List[Callable] = []
        self.state_tracker = ServiceStateTracker()
        
        # Metrics
        self._start_time = time.time()
        self._metrics_collected = 0
        self._alerts_triggered = 0
    
    async def start(self):
        """Start the monitoring service."""
        if not self._collection_task:
            self._collection_task = asyncio.create_task(self._collection_loop())
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            self.logger.info("Performance monitoring service started")
    
    async def stop(self):
        """Stop the monitoring service."""
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
            self._collection_task = None
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
        
        self.logger.info("Performance monitoring service stopped")
    
    def register_service(self, service_name: str, service_instance: Any = None):
        """Register a service for monitoring."""
        health_status = ServiceHealthStatus(
            service_name=service_name,
            healthy=True,
            last_check=datetime.utcnow()
        )
        self._service_health[service_name] = health_status
        self.logger.info(f"Registered service for monitoring: {service_name}")
    
    def record_metric(self, metrics: PerformanceMetrics):
        """Record a performance metric."""
        self._metrics_history.append(metrics)
        self._metrics_collected += 1
        
        # Update service health if applicable
        if metrics.service_name in self._service_health:
            health = self._service_health[metrics.service_name]
            health.last_check = datetime.utcnow()
            
            if not metrics.success:
                health.error_count += 1
                health.healthy = health.error_count < 5  # Simple threshold
        
        # Check alerts
        asyncio.create_task(self._check_alerts(metrics))
        
        self.logger.debug(f"Recorded metric for {metrics.service_name}: {metrics.duration_seconds}s")
    
    def add_alert(self, alert: PerformanceAlert):
        """Add a performance alert."""
        alert_id = f"{alert.metric_name}_{alert.comparison}_{alert.threshold}"
        self._active_alerts[alert_id] = alert
        self.logger.info(f"Added performance alert: {alert.message}")
    
    def remove_alert(self, metric_name: str):
        """Remove alerts for a metric."""
        to_remove = [
            alert_id for alert_id, alert in self._active_alerts.items()
            if alert.metric_name == metric_name
        ]
        for alert_id in to_remove:
            del self._active_alerts[alert_id]
        self.logger.info(f"Removed alerts for metric: {metric_name}")
    
    def add_alert_handler(self, handler: Callable):
        """Add handler for alert notifications."""
        self._alert_handlers.append(handler)
    
    async def _check_alerts(self, metrics: PerformanceMetrics):
        """Check if metrics trigger any alerts."""
        current_time = datetime.utcnow()
        
        for alert_id, alert in self._active_alerts.items():
            # Check cooldown
            last_alert = self._last_alert_time.get(alert_id)
            if last_alert and (current_time - last_alert).seconds < self.config.alert_cooldown:
                continue
            
            # Check if metric matches alert criteria
            triggered = False
            metric_value = None
            
            if alert.metric_name == "duration" and metrics.duration_seconds:
                metric_value = metrics.duration_seconds
                if alert.comparison == "gt" and metric_value > alert.threshold:
                    triggered = True
                elif alert.comparison == "lt" and metric_value < alert.threshold:
                    triggered = True
            elif alert.metric_name == "error_rate":
                # Calculate recent error rate
                recent_metrics = self.get_recent_metrics(metrics.service_name, minutes=5)
                if recent_metrics:
                    error_count = sum(1 for m in recent_metrics if not m.success)
                    metric_value = error_count / len(recent_metrics) * 100
                    if alert.comparison == "gt" and metric_value > alert.threshold:
                        triggered = True
            
            if triggered:
                await self._trigger_alert(alert, metrics, metric_value)
                self._last_alert_time[alert_id] = current_time
    
    async def _trigger_alert(self, alert: PerformanceAlert, metrics: PerformanceMetrics, value: Any):
        """Trigger an alert."""
        alert_data = {
            "alert": alert,
            "metrics": metrics,
            "value": value,
            "timestamp": datetime.utcnow(),
            "severity": alert.severity
        }
        
        self._alert_history.append(alert_data)
        self._alerts_triggered += 1
        
        self.logger.warning(
            f"PERFORMANCE ALERT: {alert.message} "
            f"(Service: {metrics.service_name}, Value: {value}, Threshold: {alert.threshold})"
        )
        
        # Notify handlers
        for handler in self._alert_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(alert_data)
                else:
                    handler(alert_data)
            except Exception as e:
                self.logger.error(f"Alert handler failed: {e}")
    
    def get_recent_metrics(self, service_name: str, minutes: int = 10) -> List[PerformanceMetrics]:
        """Get recent metrics for a service."""
        cutoff_time = time.time() - (minutes * 60)
        return [
            m for m in self._metrics_history
            if m.service_name == service_name and m.start_time >= cutoff_time
        ]
    
    def get_service_health(self, service_name: str) -> Optional[ServiceHealthStatus]:
        """Get health status for a service."""
        return self._service_health.get(service_name)
    
    def get_all_service_health(self) -> Dict[str, ServiceHealthStatus]:
        """Get health status for all services."""
        return self._service_health.copy()
    
    def get_performance_summary(self, service_name: Optional[str] = None) -> Dict[str, Any]:
        """Get performance summary."""
        metrics = self._metrics_history
        if service_name:
            metrics = [m for m in metrics if m.service_name == service_name]
        
        if not metrics:
            return {"message": "No metrics available"}
        
        # Calculate statistics
        successful = [m for m in metrics if m.success]
        durations = [m.duration_seconds for m in metrics if m.duration_seconds is not None]
        
        summary = {
            "total_operations": len(metrics),
            "successful_operations": len(successful),
            "failed_operations": len(metrics) - len(successful),
            "success_rate": len(successful) / len(metrics) * 100 if metrics else 0,
            "alerts_triggered": self._alerts_triggered,
            "monitoring_uptime": time.time() - self._start_time
        }
        
        if durations:
            summary.update({
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "p95_duration": sorted(durations)[int(len(durations) * 0.95)] if len(durations) > 20 else max(durations)
            })
        
        return summary
    
    async def _collection_loop(self):
        """Main collection loop."""
        while True:
            try:
                await asyncio.sleep(self.config.collection_interval)
                await self._collect_system_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Collection loop error: {e}")
    
    async def _cleanup_loop(self):
        """Cleanup old metrics."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._cleanup_old_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Cleanup loop error: {e}")
    
    async def _collect_system_metrics(self):
        """Collect system-level metrics."""
        # This would collect CPU, memory, etc.
        # For now, just record that collection happened
        system_metrics = PerformanceMetrics(
            service_name="system",
            operations_count=self._metrics_collected
        )
        system_metrics.finish(success=True)
        self.record_metric(system_metrics)
    
    async def _cleanup_old_metrics(self):
        """Remove old metrics to prevent memory buildup."""
        cutoff_time = time.time() - self.config.retention_period
        
        # Remove old metrics
        old_count = len(self._metrics_history)
        self._metrics_history = [
            m for m in self._metrics_history
            if m.start_time >= cutoff_time
        ]
        
        # Limit total count
        if len(self._metrics_history) > self.config.max_metrics_history:
            self._metrics_history = self._metrics_history[-self.config.max_metrics_history:]
        
        removed = old_count - len(self._metrics_history)
        if removed > 0:
            self.logger.debug(f"Cleaned up {removed} old metrics")
        
        # Cleanup alert history
        alert_cutoff = datetime.utcnow() - timedelta(hours=24)
        old_alert_count = len(self._alert_history)
        self._alert_history = [
            alert for alert in self._alert_history
            if alert["timestamp"] >= alert_cutoff
        ]
        
        alert_removed = old_alert_count - len(self._alert_history)
        if alert_removed > 0:
            self.logger.debug(f"Cleaned up {alert_removed} old alerts")
    
    async def _execute_action(self, data: Any) -> Any:
        """Execute monitoring action."""
        if isinstance(data, dict):
            action = data.get("action")
            if action == "get_summary":
                return self.get_performance_summary(data.get("service_name"))
            elif action == "get_health":
                service_name = data.get("service_name")
                if service_name:
                    return self.get_service_health(service_name)
                else:
                    return self.get_all_service_health()
        return data

# Global monitoring service instance
monitoring_service = PerformanceMonitoringService()
'''
            ),
            
            CompletionTask(
                task_id="phase3_monitoring_decorators",
                description="Create Monitoring Decorators",
                phase="Phase 3",
                file_path="src/infrastructure/decorators/monitoring_decorators.py",
                content='''
import asyncio
import functools
import time
import logging
from typing import Any, Callable, Optional, TypeVar, Dict
from src.infrastructure.monitoring.performance_metrics import PerformanceMetrics
from src.services.monitoring.performance_monitoring_service import monitoring_service

F = TypeVar('F', bound=Callable[..., Any])

def monitor_performance(
    operation_name: Optional[str] = None,
    track_memory: bool = False,
    alert_threshold: Optional[float] = None
):
    """Decorator to monitor performance of async operations."""
    def decorator(func: F) -> F:
        op_name = operation_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            metrics = PerformanceMetrics(service_name=op_name)
            
            try:
                # Track memory if requested
                if track_memory:
                    import psutil
                    process = psutil.Process()
                    metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
                
                # Execute the function
                result = await func(*args, **kwargs)
                metrics.finish(success=True)
                
                # Check alert threshold
                if alert_threshold and metrics.duration_seconds and metrics.duration_seconds > alert_threshold:
                    logger = logging.getLogger(__name__)
                    logger.warning(
                        f"Performance threshold exceeded for {op_name}: "
                        f"{metrics.duration_seconds:.3f}s > {alert_threshold}s"
                    )
                
                return result
                
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                # Record metrics
                monitoring_service.record_metric(metrics)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            metrics = PerformanceMetrics(service_name=op_name)
            
            try:
                if track_memory:
                    import psutil
                    process = psutil.Process()
                    metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
                
                result = func(*args, **kwargs)
                metrics.finish(success=True)
                
                if alert_threshold and metrics.duration_seconds and metrics.duration_seconds > alert_threshold:
                    logger = logging.getLogger(__name__)
                    logger.warning(
                        f"Performance threshold exceeded for {op_name}: "
                        f"{metrics.duration_seconds:.3f}s > {alert_threshold}s"
                    )
                
                return result
                
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                monitoring_service.record_metric(metrics)
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def track_operation_count(operation_name: Optional[str] = None):
    """Decorator to track the number of times an operation is called."""
    def decorator(func: F) -> F:
        op_name = operation_name or f"{func.__module__}.{func.__name__}"
        call_count = 0
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            metrics = PerformanceMetrics(service_name=op_name)
            metrics.operations_count = call_count
            
            try:
                result = await func(*args, **kwargs)
                metrics.finish(success=True)
                return result
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                monitoring_service.record_metric(metrics)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            metrics = PerformanceMetrics(service_name=op_name)
            metrics.operations_count = call_count
            
            try:
                result = func(*args, **kwargs)
                metrics.finish(success=True)
                return result
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                monitoring_service.record_metric(metrics)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def monitor_resource_usage(
    cpu_threshold: Optional[float] = None,
    memory_threshold: Optional[float] = None
):
    """Decorator to monitor CPU and memory usage during operation."""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            import psutil
            process = psutil.Process()
            
            # Get initial readings
            cpu_start = process.cpu_percent()
            memory_start = process.memory_info().rss / 1024 / 1024
            
            metrics = PerformanceMetrics(service_name=f"{func.__module__}.{func.__name__}")
            
            try:
                result = await func(*args, **kwargs)
                
                # Get final readings
                cpu_end = process.cpu_percent()
                memory_end = process.memory_info().rss / 1024 / 1024
                
                metrics.cpu_usage_percent = cpu_end
                metrics.memory_usage_mb = memory_end
                
                # Check thresholds
                logger = logging.getLogger(__name__)
                if cpu_threshold and cpu_end > cpu_threshold:
                    logger.warning(f"CPU usage threshold exceeded: {cpu_end}% > {cpu_threshold}%")
                
                if memory_threshold and (memory_end - memory_start) > memory_threshold:
                    logger.warning(
                        f"Memory usage increase threshold exceeded: "
                        f"{memory_end - memory_start:.1f}MB > {memory_threshold}MB"
                    )
                
                metrics.finish(success=True)
                return result
                
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                monitoring_service.record_metric(metrics)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            import psutil
            process = psutil.Process()
            
            cpu_start = process.cpu_percent()
            memory_start = process.memory_info().rss / 1024 / 1024
            
            metrics = PerformanceMetrics(service_name=f"{func.__module__}.{func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                
                cpu_end = process.cpu_percent()
                memory_end = process.memory_info().rss / 1024 / 1024
                
                metrics.cpu_usage_percent = cpu_end
                metrics.memory_usage_mb = memory_end
                
                logger = logging.getLogger(__name__)
                if cpu_threshold and cpu_end > cpu_threshold:
                    logger.warning(f"CPU usage threshold exceeded: {cpu_end}% > {cpu_threshold}%")
                
                if memory_threshold and (memory_end - memory_start) > memory_threshold:
                    logger.warning(
                        f"Memory usage increase threshold exceeded: "
                        f"{memory_end - memory_start:.1f}MB > {memory_threshold}MB"
                    )
                
                metrics.finish(success=True)
                return result
                
            except Exception as e:
                metrics.finish(success=False, error_message=str(e))
                raise
            finally:
                monitoring_service.record_metric(metrics)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class PerformanceMonitor:
    """Context manager for monitoring performance of code blocks."""
    
    def __init__(self, operation_name: str, track_memory: bool = False):
        self.operation_name = operation_name
        self.track_memory = track_memory
        self.metrics: Optional[PerformanceMetrics] = None
    
    def __enter__(self):
        self.metrics = PerformanceMetrics(service_name=self.operation_name)
        
        if self.track_memory:
            import psutil
            process = psutil.Process()
            self.metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
        
        return self.metrics
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.metrics:
            success = exc_type is None
            error_message = str(exc_val) if exc_val else None
            self.metrics.finish(success=success, error_message=error_message)
            monitoring_service.record_metric(self.metrics)
    
    async def __aenter__(self):
        return self.__enter__()
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.__exit__(exc_type, exc_val, exc_tb)

# Convenience functions
def create_performance_monitor(operation_name: str, track_memory: bool = False) -> PerformanceMonitor:
    """Create a performance monitor context manager."""
    return PerformanceMonitor(operation_name, track_memory)

# Example usage patterns
"""
# Decorator usage:
@monitor_performance("database_query", alert_threshold=2.0)
async def query_database():
    pass

@track_operation_count("api_calls")
async def api_call():
    pass

@monitor_resource_usage(cpu_threshold=80.0, memory_threshold=100.0)
async def heavy_computation():
    pass

# Context manager usage:
async with create_performance_monitor("complex_operation", track_memory=True) as monitor:
    # Do complex work
    monitor.operations_count = 42
"""
'''
            ),
            
            CompletionTask(
                task_id="phase3_lifecycle_manager",
                description="Create LifecycleManager",
                phase="Phase 3",
                file_path="src/infrastructure/lifecycle/lifecycle_manager.py",
                content='''
import asyncio
import logging
from typing import Dict, List, Set, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from src.infrastructure.monitoring.service_states import ServiceStateTracker, ServiceState, ServiceStateInfo
from src.infrastructure.factories.type_safe_factory import ServiceProtocol

class LifecyclePhase(Enum):
    """Lifecycle phases for service coordination."""
    STARTUP = "startup"
    READY = "ready" 
    SHUTDOWN = "shutdown"
    RECOVERY = "recovery"

@dataclass
class LifecycleEvent:
    """Event during service lifecycle."""
    phase: LifecyclePhase
    service_name: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ServiceDependency:
    """Dependency relationship between services."""
    service_name: str
    depends_on: str
    dependency_type: str = "hard"  # hard, soft
    startup_delay: float = 0.0  # seconds to wait after dependency starts

class LifecycleManager:
    """Manages service lifecycle and coordination."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.state_tracker = ServiceStateTracker()
        
        # Service management
        self._services: Dict[str, Any] = {}
        self._dependencies: List[ServiceDependency] = []
        self._startup_order: List[str] = []
        self._shutdown_order: List[str] = []
        
        # Lifecycle tracking
        self._current_phase = LifecyclePhase.READY
        self._lifecycle_events: List[LifecycleEvent] = []
        self._phase_handlers: Dict[LifecyclePhase, List[Callable]] = {
            phase: [] for phase in LifecyclePhase
        }
        
        # Recovery and health
        self._failed_services: Set[str] = set()
        self._recovery_attempts: Dict[str, int] = {}
        self._max_recovery_attempts = 3
        self._recovery_delay = 30.0  # seconds
        
        # Tasks
        self._health_monitor_task: Optional[asyncio.Task] = None
        self._recovery_task: Optional[asyncio.Task] = None
    
    def register_service(self, name: str, service: Any, dependencies: Optional[List[str]] = None):
        """Register a service with the lifecycle manager."""
        self._services[name] = service
        
        # Add dependencies
        if dependencies:
            for dep in dependencies:
                self._dependencies.append(ServiceDependency(
                    service_name=name,
                    depends_on=dep
                ))
        
        # Update startup/shutdown order
        self._calculate_startup_order()
        
        self.state_tracker.set_state(name, ServiceState.CREATED)
        self.logger.info(f"Registered service: {name}")
    
    def add_dependency(self, service_name: str, depends_on: str, dependency_type: str = "hard", startup_delay: float = 0.0):
        """Add a dependency relationship."""
        dependency = ServiceDependency(
            service_name=service_name,
            depends_on=depends_on,
            dependency_type=dependency_type,
            startup_delay=startup_delay
        )
        self._dependencies.append(dependency)
        self._calculate_startup_order()
        
        self.logger.info(f"Added dependency: {service_name} depends on {depends_on} ({dependency_type})")
    
    def _calculate_startup_order(self):
        """Calculate the order for starting services based on dependencies."""
        # Simple topological sort
        in_degree = {name: 0 for name in self._services.keys()}
        graph = {name: [] for name in self._services.keys()}
        
        # Build dependency graph
        for dep in self._dependencies:
            if dep.service_name in self._services and dep.depends_on in self._services:
                graph[dep.depends_on].append(dep.service_name)
                in_degree[dep.service_name] += 1
        
        # Topological sort
        queue = [name for name, degree in in_degree.items() if degree == 0]
        startup_order = []
        
        while queue:
            current = queue.pop(0)
            startup_order.append(current)
            
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        self._startup_order = startup_order
        self._shutdown_order = startup_order[::-1]  # Reverse for shutdown
        
        self.logger.debug(f"Calculated startup order: {self._startup_order}")
    
    async def start_all_services(self) -> bool:
        """Start all services in dependency order."""
        self._current_phase = LifecyclePhase.STARTUP
        await self._emit_phase_event(LifecyclePhase.STARTUP, "lifecycle_manager", True)
        
        success = True
        started_services = []
        
        try:
            for service_name in self._startup_order:
                if await self._start_service(service_name):
                    started_services.append(service_name)
                else:
                    success = False
                    self.logger.error(f"Failed to start service: {service_name}")
                    break
            
            if success:
                self._current_phase = LifecyclePhase.READY
                await self._emit_phase_event(LifecyclePhase.READY, "lifecycle_manager", True)
                
                # Start health monitoring
                if not self._health_monitor_task:
                    self._health_monitor_task = asyncio.create_task(self._health_monitor_loop())
                
                self.logger.info("All services started successfully")
            else:
                # Cleanup partially started services
                await self._cleanup_failed_startup(started_services)
                
        except Exception as e:
            self.logger.error(f"Error during service startup: {e}")
            await self._cleanup_failed_startup(started_services)
            success = False
        
        return success
    
    async def _start_service(self, service_name: str) -> bool:
        """Start a single service."""
        service = self._services.get(service_name)
        if not service:
            return False
        
        try:
            # Check dependencies first
            if not await self._check_dependencies(service_name):
                return False
            
            # Apply startup delay if specified
            for dep in self._dependencies:
                if dep.service_name == service_name and dep.startup_delay > 0:
                    await asyncio.sleep(dep.startup_delay)
                    break
            
            self.state_tracker.set_state(service_name, ServiceState.INITIALIZING)
            
            # Start the service
            if hasattr(service, 'start'):
                if asyncio.iscoroutinefunction(service.start):
                    await service.start()
                else:
                    service.start()
            
            self.state_tracker.set_state(service_name, ServiceState.RUNNING)
            await self._emit_lifecycle_event(LifecyclePhase.STARTUP, service_name, True)
            
            self.logger.info(f"Started service: {service_name}")
            return True
            
        except Exception as e:
            self.state_tracker.set_state(service_name, ServiceState.FAILED, error_message=str(e))
            await self._emit_lifecycle_event(LifecyclePhase.STARTUP, service_name, False, str(e))
            self.logger.error(f"Failed to start service {service_name}: {e}")
            return False
    
    async def _check_dependencies(self, service_name: str) -> bool:
        """Check if all dependencies of a service are running."""
        for dep in self._dependencies:
            if dep.service_name == service_name:
                dep_state = self.state_tracker.get_state(dep.depends_on)
                if not dep_state or dep_state.current_state != ServiceState.RUNNING:
                    if dep.dependency_type == "hard":
                        self.logger.error(f"Hard dependency {dep.depends_on} not running for {service_name}")
                        return False
                    else:
                        self.logger.warning(f"Soft dependency {dep.depends_on} not running for {service_name}")
        return True
    
    async def stop_all_services(self) -> bool:
        """Stop all services in reverse dependency order."""
        self._current_phase = LifecyclePhase.SHUTDOWN
        await self._emit_phase_event(LifecyclePhase.SHUTDOWN, "lifecycle_manager", True)
        
        # Stop health monitoring
        if self._health_monitor_task:
            self._health_monitor_task.cancel()
            try:
                await self._health_monitor_task
            except asyncio.CancelledError:
                pass
            self._health_monitor_task = None
        
        success = True
        
        for service_name in self._shutdown_order:
            if not await self._stop_service(service_name):
                success = False
                self.logger.error(f"Failed to stop service: {service_name}")
        
        if success:
            self.logger.info("All services stopped successfully")
        
        return success
    
    async def _stop_service(self, service_name: str) -> bool:
        """Stop a single service."""
        service = self._services.get(service_name)
        if not service:
            return True
        
        try:
            self.state_tracker.set_state(service_name, ServiceState.STOPPING)
            
            if hasattr(service, 'stop'):
                if asyncio.iscoroutinefunction(service.stop):
                    await service.stop()
                else:
                    service.stop()
            
            self.state_tracker.set_state(service_name, ServiceState.STOPPED)
            await self._emit_lifecycle_event(LifecyclePhase.SHUTDOWN, service_name, True)
            
            self.logger.info(f"Stopped service: {service_name}")
            return True
            
        except Exception as e:
            self.state_tracker.set_state(service_name, ServiceState.ERROR, error_message=str(e))
            await self._emit_lifecycle_event(LifecyclePhase.SHUTDOWN, service_name, False, str(e))
            self.logger.error(f"Failed to stop service {service_name}: {e}")
            return False
    
    async def restart_service(self, service_name: str) -> bool:
        """Restart a specific service."""
        self.logger.info(f"Restarting service: {service_name}")
        
        # Stop the service
        if not await self._stop_service(service_name):
            return False
        
        # Wait a moment
        await asyncio.sleep(1.0)
        
        # Start the service
        return await self._start_service(service_name)
    
    async def _health_monitor_loop(self):
        """Monitor service health and trigger recovery if needed."""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                for service_name, service in self._services.items():
                    if await self._check_service_health(service_name, service):
                        # Service is healthy, remove from failed set
                        self._failed_services.discard(service_name)
                    else:
                        # Service is unhealthy
                        if service_name not in self._failed_services:
                            self._failed_services.add(service_name)
                            self.logger.warning(f"Service health check failed: {service_name}")
                            
                            # Trigger recovery
                            asyncio.create_task(self._recover_service(service_name))
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Health monitor error: {e}")
    
    async def _check_service_health(self, service_name: str, service: Any) -> bool:
        """Check if a service is healthy."""
        try:
            if hasattr(service, 'is_healthy'):
                is_healthy = service.is_healthy()
                if asyncio.iscoroutine(is_healthy):
                    is_healthy = await is_healthy
                return is_healthy
            elif isinstance(service, ServiceProtocol):
                return service.is_healthy()
            else:
                # If no health check method, assume healthy if state is running
                state = self.state_tracker.get_state(service_name)
                return state and state.current_state == ServiceState.RUNNING
        except Exception as e:
            self.logger.error(f"Health check error for {service_name}: {e}")
            return False
    
    async def _recover_service(self, service_name: str):
        """Attempt to recover a failed service."""
        attempts = self._recovery_attempts.get(service_name, 0)
        
        if attempts >= self._max_recovery_attempts:
            self.logger.error(f"Max recovery attempts reached for {service_name}")
            return
        
        self._current_phase = LifecyclePhase.RECOVERY
        self._recovery_attempts[service_name] = attempts + 1
        
        self.logger.info(f"Attempting recovery for {service_name} (attempt {attempts + 1})")
        
        # Wait before recovery attempt
        await asyncio.sleep(self._recovery_delay * attempts)  # Exponential backoff
        
        # Attempt restart
        if await self.restart_service(service_name):
            self.logger.info(f"Successfully recovered service: {service_name}")
            self._recovery_attempts[service_name] = 0  # Reset counter
            await self._emit_lifecycle_event(LifecyclePhase.RECOVERY, service_name, True)
        else:
            self.logger.error(f"Failed to recover service: {service_name}")
            await self._emit_lifecycle_event(LifecyclePhase.RECOVERY, service_name, False)
        
        if self._current_phase == LifecyclePhase.RECOVERY:
            self._current_phase = LifecyclePhase.READY
    
    async def _cleanup_failed_startup(self, started_services: List[str]):
        """Cleanup services that were started before a failure."""
        self.logger.info("Cleaning up after failed startup")
        
        for service_name in reversed(started_services):
            try:
                await self._stop_service(service_name)
            except Exception as e:
                self.logger.error(f"Error during cleanup of {service_name}: {e}")
    
    def add_phase_handler(self, phase: LifecyclePhase, handler: Callable):
        """Add a handler for lifecycle phase events."""
        self._phase_handlers[phase].append(handler)
    
    async def _emit_lifecycle_event(self, phase: LifecyclePhase, service_name: str, success: bool, error_message: Optional[str] = None):
        """Emit a lifecycle event."""
        event = LifecycleEvent(
            phase=phase,
            service_name=service_name,
            success=success,
            error_message=error_message
        )
        self._lifecycle_events.append(event)
        
        # Trigger handlers
        for handler in self._phase_handlers[phase]:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
            except Exception as e:
                self.logger.error(f"Lifecycle event handler failed: {e}")
    
    async def _emit_phase_event(self, phase: LifecyclePhase, service_name: str, success: bool):
        """Emit a phase change event."""
        await self._emit_lifecycle_event(phase, service_name, success)
    
    def get_service_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all managed services."""
        status = {}
        for service_name in self._services.keys():
            state_info = self.state_tracker.get_state(service_name)
            status[service_name] = {
                "state": state_info.current_state.name if state_info else "UNKNOWN",
                "healthy": service_name not in self._failed_services,
                "recovery_attempts": self._recovery_attempts.get(service_name, 0),
                "dependencies": [
                    dep.depends_on for dep in self._dependencies
                    if dep.service_name == service_name
                ]
            }
        return status
    
    def get_lifecycle_events(self, limit: int = 50) -> List[LifecycleEvent]:
        """Get recent lifecycle events."""
        return self._lifecycle_events[-limit:] if self._lifecycle_events else []

# Global lifecycle manager instance
lifecycle_manager = LifecycleManager()
'''
            ),
            
            CompletionTask(
                task_id="phase3_health_checks",
                description="Create Health Check System",
                phase="Phase 3",
                file_path="src/infrastructure/health/health_check_system.py",
                content='''
import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from src.infrastructure.monitoring.service_states import ServiceStateTracker, ServiceState

class HealthStatus(Enum):
    """Health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """Individual health check definition."""
    name: str
    check_function: Callable
    interval: float = 60.0  # seconds
    timeout: float = 10.0   # seconds
    retries: int = 2
    critical: bool = False
    enabled: bool = True
    tags: Set[str] = field(default_factory=set)

@dataclass
class HealthCheckResult:
    """Result of a health check."""
    check_name: str
    status: HealthStatus
    message: str = ""
    timestamp: datetime = field(default_factory=datetime.utcnow)
    duration: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None

@dataclass
class SystemHealthReport:
    """Overall system health report."""
    overall_status: HealthStatus
    timestamp: datetime = field(default_factory=datetime.utcnow)
    total_checks: int = 0
    healthy_checks: int = 0
    warning_checks: int = 0
    critical_checks: int = 0
    unknown_checks: int = 0
    check_results: List[HealthCheckResult] = field(default_factory=list)
    uptime_seconds: float = 0.0

class HealthCheckSystem:
    """Comprehensive health check system."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Health checks
        self._health_checks: Dict[str, HealthCheck] = {}
        self._check_results: Dict[str, HealthCheckResult] = {}
        self._check_history: Dict[str, List[HealthCheckResult]] = {}
        self._max_history_per_check = 100
        
        # Scheduling
        self._check_tasks: Dict[str, asyncio.Task] = {}
        self._running = False
        self._start_time = time.time()
        
        # Notifications
        self._status_change_handlers: List[Callable] = []
        self._critical_handlers: List[Callable] = []
        
        # System monitoring
        self.state_tracker = ServiceStateTracker()
        self._last_status = HealthStatus.UNKNOWN
    
    async def start(self):
        """Start the health check system."""
        if self._running:
            return
        
        self._running = True
        self._start_time = time.time()
        
        # Start all enabled health checks
        for check_name, health_check in self._health_checks.items():
            if health_check.enabled:
                await self._start_health_check(check_name)
        
        self.logger.info("Health check system started")
    
    async def stop(self):
        """Stop the health check system."""
        if not self._running:
            return
        
        self._running = False
        
        # Cancel all running tasks
        for task in self._check_tasks.values():
            task.cancel()
        
        # Wait for all tasks to complete
        if self._check_tasks:
            await asyncio.gather(*self._check_tasks.values(), return_exceptions=True)
        
        self._check_tasks.clear()
        self.logger.info("Health check system stopped")
    
    def register_health_check(self, health_check: HealthCheck):
        """Register a new health check."""
        self._health_checks[health_check.name] = health_check
        self._check_history[health_check.name] = []
        
        # Start the check if system is running
        if self._running and health_check.enabled:
            asyncio.create_task(self._start_health_check(health_check.name))
        
        self.logger.info(f"Registered health check: {health_check.name}")
    
    def unregister_health_check(self, check_name: str):
        """Unregister a health check."""
        if check_name in self._health_checks:
            # Stop the check if running
            if check_name in self._check_tasks:
                self._check_tasks[check_name].cancel()
                del self._check_tasks[check_name]
            
            # Clean up
            del self._health_checks[check_name]
            self._check_results.pop(check_name, None)
            self._check_history.pop(check_name, None)
            
            self.logger.info(f"Unregistered health check: {check_name}")
    
    async def _start_health_check(self, check_name: str):
        """Start a specific health check task."""
        if check_name in self._check_tasks:
            # Already running
            return
        
        health_check = self._health_checks[check_name]
        task = asyncio.create_task(self._health_check_loop(health_check))
        self._check_tasks[check_name] = task
        
        self.logger.debug(f"Started health check task: {check_name}")
    
    async def _health_check_loop(self, health_check: HealthCheck):
        """Main loop for a health check."""
        while self._running:
            try:
                # Execute the health check
                result = await self._execute_health_check(health_check)
                
                # Store result
                self._check_results[health_check.name] = result
                self._check_history[health_check.name].append(result)
                
                # Trim history
                if len(self._check_history[health_check.name]) > self._max_history_per_check:
                    self._check_history[health_check.name] = self._check_history[health_check.name][-self._max_history_per_check:]
                
                # Handle status changes and notifications
                await self._handle_check_result(result)
                
                # Wait for next check
                await asyncio.sleep(health_check.interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Health check loop error for {health_check.name}: {e}")
                await asyncio.sleep(health_check.interval)
    
    async def _execute_health_check(self, health_check: HealthCheck) -> HealthCheckResult:
        """Execute a single health check with retries."""
        last_error = None
        
        for attempt in range(health_check.retries + 1):
            try:
                start_time = time.time()
                
                # Execute the check function with timeout
                if asyncio.iscoroutinefunction(health_check.check_function):
                    result = await asyncio.wait_for(
                        health_check.check_function(),
                        timeout=health_check.timeout
                    )
                else:
                    result = await asyncio.wait_for(
                        asyncio.get_event_loop().run_in_executor(
                            None, health_check.check_function
                        ),
                        timeout=health_check.timeout
                    )
                
                duration = time.time() - start_time
                
                # Process result
                if isinstance(result, HealthCheckResult):
                    result.duration = duration
                    return result
                elif isinstance(result, dict):
                    return HealthCheckResult(
                        check_name=health_check.name,
                        status=HealthStatus(result.get('status', 'healthy')),
                        message=result.get('message', ''),
                        duration=duration,
                        metadata=result.get('metadata', {})
                    )
                elif isinstance(result, bool):
                    return HealthCheckResult(
                        check_name=health_check.name,
                        status=HealthStatus.HEALTHY if result else HealthStatus.CRITICAL,
                        message="OK" if result else "Check failed",
                        duration=duration
                    )
                else:
                    return HealthCheckResult(
                        check_name=health_check.name,
                        status=HealthStatus.HEALTHY,
                        message=str(result),
                        duration=duration
                    )
                    
            except asyncio.TimeoutError:
                last_error = f"Health check timed out after {health_check.timeout}s"
                if attempt < health_check.retries:
                    await asyncio.sleep(1.0)  # Brief delay before retry
                    continue
            except Exception as e:
                last_error = str(e)
                if attempt < health_check.retries:
                    await asyncio.sleep(1.0)
                    continue
        
        # All retries failed
        return HealthCheckResult(
            check_name=health_check.name,
            status=HealthStatus.CRITICAL if health_check.critical else HealthStatus.WARNING,
            message="Health check failed",
            error=last_error
        )
    
    async def _handle_check_result(self, result: HealthCheckResult):
        """Handle the result of a health check."""
        # Log significant status changes
        if result.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
            self.logger.warning(f"Health check {result.check_name}: {result.status.value} - {result.message}")
        
        # Trigger critical handlers for critical status
        if result.status == HealthStatus.CRITICAL:
            for handler in self._critical_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(result)
                    else:
                        handler(result)
                except Exception as e:
                    self.logger.error(f"Critical handler failed: {e}")
        
        # Check for system-wide status changes
        current_system_status = self._calculate_overall_status()
        if current_system_status != self._last_status:
            self._last_status = current_system_status
            
            # Notify status change handlers
            for handler in self._status_change_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(current_system_status, result)
                    else:
                        handler(current_system_status, result)
                except Exception as e:
                    self.logger.error(f"Status change handler failed: {e}")
    
    def _calculate_overall_status(self) -> HealthStatus:
        """Calculate overall system health status."""
        if not self._check_results:
            return HealthStatus.UNKNOWN
        
        statuses = [result.status for result in self._check_results.values()]
        
        # If any critical, system is critical
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        
        # If any warning, system has warnings
        if HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        
        # If any unknown, system status is unknown
        if HealthStatus.UNKNOWN in statuses:
            return HealthStatus.UNKNOWN
        
        # All healthy
        return HealthStatus.HEALTHY
    
    async def run_check_once(self, check_name: str) -> HealthCheckResult:
        """Run a specific health check once, immediately."""
        if check_name not in self._health_checks:
            raise ValueError(f"Health check not found: {check_name}")
        
        health_check = self._health_checks[check_name]
        result = await self._execute_health_check(health_check)
        
        # Update stored results
        self._check_results[check_name] = result
        self._check_history[check_name].append(result)
        
        return result
    
    def get_health_report(self) -> SystemHealthReport:
        """Get comprehensive system health report."""
        results = list(self._check_results.values())
        
        # Count statuses
        status_counts = {
            HealthStatus.HEALTHY: 0,
            HealthStatus.WARNING: 0,
            HealthStatus.CRITICAL: 0,
            HealthStatus.UNKNOWN: 0
        }
        
        for result in results:
            status_counts[result.status] += 1
        
        return SystemHealthReport(
            overall_status=self._calculate_overall_status(),
            total_checks=len(results),
            healthy_checks=status_counts[HealthStatus.HEALTHY],
            warning_checks=status_counts[HealthStatus.WARNING],
            critical_checks=status_counts[HealthStatus.CRITICAL],
            unknown_checks=status_counts[HealthStatus.UNKNOWN],
            check_results=results,
            uptime_seconds=time.time() - self._start_time
        )
    
    def get_check_result(self, check_name: str) -> Optional[HealthCheckResult]:
        """Get the latest result for a specific check."""
        return self._check_results.get(check_name)
    
    def get_check_history(self, check_name: str, limit: int = 10) -> List[HealthCheckResult]:
        """Get history for a specific check."""
        history = self._check_history.get(check_name, [])
        return history[-limit:] if history else []
    
    def add_status_change_handler(self, handler: Callable):
        """Add handler for system status changes."""
        self._status_change_handlers.append(handler)
    
    def add_critical_handler(self, handler: Callable):
        """Add handler for critical health check failures."""
        self._critical_handlers.append(handler)
    
    def list_health_checks(self) -> List[Dict[str, Any]]:
        """List all registered health checks."""
        return [
            {
                "name": check.name,
                "interval": check.interval,
                "timeout": check.timeout,
                "critical": check.critical,
                "enabled": check.enabled,
                "tags": list(check.tags),
                "last_result": self._check_results.get(check.name)
            }
            for check in self._health_checks.values()
        ]

# Predefined health checks
def create_memory_health_check(threshold_mb: float = 1000.0) -> HealthCheck:
    """Create a memory usage health check."""
    async def check_memory():
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb > threshold_mb:
                return {
                    'status': 'warning',
                    'message': f'Memory usage high: {memory_mb:.1f}MB > {threshold_mb}MB',
                    'metadata': {'memory_mb': memory_mb, 'threshold_mb': threshold_mb}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'Memory usage normal: {memory_mb:.1f}MB',
                    'metadata': {'memory_mb': memory_mb}
                }
        except Exception as e:
            return {
                'status': 'unknown',
                'message': f'Failed to check memory: {e}'
            }
    
    return HealthCheck(
        name="memory_usage",
        check_function=check_memory,
        interval=60.0,
        tags={"system", "memory"}
    )

def create_disk_health_check(path: str = "/", threshold_percent: float = 90.0) -> HealthCheck:
    """Create a disk usage health check."""
    async def check_disk():
        try:
            import shutil
            total, used, free = shutil.disk_usage(path)
            used_percent = (used / total) * 100
            
            if used_percent > threshold_percent:
                return {
                    'status': 'critical',
                    'message': f'Disk usage critical: {used_percent:.1f}% > {threshold_percent}%',
                    'metadata': {
                        'used_percent': used_percent,
                        'threshold_percent': threshold_percent,
                        'free_gb': free / (1024**3)
                    }
                }
            elif used_percent > threshold_percent * 0.8:
                return {
                    'status': 'warning',
                    'message': f'Disk usage high: {used_percent:.1f}%',
                    'metadata': {'used_percent': used_percent}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'Disk usage normal: {used_percent:.1f}%',
                    'metadata': {'used_percent': used_percent}
                }
        except Exception as e:
            return {
                'status': 'unknown',
                'message': f'Failed to check disk: {e}'
            }
    
    return HealthCheck(
        name=f"disk_usage_{path.replace('/', '_')}",
        check_function=check_disk,
        interval=300.0,  # Check every 5 minutes
        critical=True,
        tags={"system", "disk"}
    )

# Global health check system instance
health_check_system = HealthCheckSystem()
'''
            )
        ]
    
    async def complete_refactoring(self) -> Dict[str, Any]:
        """Complete all remaining refactoring tasks."""
        completion_stats = {
            'total_tasks': len(self.get_completion_tasks()),
            'completed_tasks': 0,
            'failed_tasks': 0,
            'created_files': [],
            'errors': []
        }
        
        tasks = self.get_completion_tasks()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            MofNCompleteColumn(),
            console=self.console
        ) as progress:
            
            overall_task = progress.add_task("Completing refactoring...", total=len(tasks))
            
            for task in tasks:
                task_progress = progress.add_task(f"Creating {task.description}...", total=None)
                
                try:
                    # Create the file
                    file_path = self.project_root / task.file_path
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    with open(file_path, 'w') as f:
                        f.write(task.content.strip())
                    
                    completion_stats['completed_tasks'] += 1
                    completion_stats['created_files'].append(str(file_path))
                    self.completed_tasks.append(task)
                    
                    progress.update(task_progress, description=f"✅ Created {task.description}")
                    self.console.print(f"✅ {task.phase}: {task.description}")
                    
                except Exception as e:
                    completion_stats['failed_tasks'] += 1
                    completion_stats['errors'].append(f"{task.description}: {str(e)}")
                    self.failed_tasks.append(task)
                    
                    progress.update(task_progress, description=f"❌ Failed {task.description}")
                    self.console.print(f"❌ {task.phase}: {task.description} - {e}")
                
                progress.update(overall_task, advance=1)
        
        return completion_stats
    
    async def run_completion_tests(self) -> Dict[str, Any]:
        """Run tests on completed components."""
        test_results = {
            'import_tests': 0,
            'import_failures': 0,
            'validation_tests': 0,
            'validation_failures': 0
        }
        
        # Test imports
        import_tests = [
            ("src.services.infrastructure.resource_cleanup_service", "ResourceCleanupService"),
            ("src.services.infrastructure.performance_monitor_service", "PerformanceMonitorService"),
            ("src.infrastructure.factories.type_safe_factory", "TypeSafeServiceFactory"),
            ("src.infrastructure.registry.service_registry", "ServiceRegistry"),
            ("src.services.monitoring.performance_monitoring_service", "PerformanceMonitoringService"),
            ("src.infrastructure.decorators.monitoring_decorators", "monitor_performance"),
            ("src.infrastructure.lifecycle.lifecycle_manager", "LifecycleManager"),
            ("src.infrastructure.health.health_check_system", "HealthCheckSystem")
        ]
        
        for module_path, class_name in import_tests:
            try:
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                test_results['import_tests'] += 1
                self.console.print(f"✅ Import test passed: {class_name}")
            except Exception as e:
                test_results['import_failures'] += 1
                self.console.print(f"❌ Import test failed: {class_name} - {e}")
        
        return test_results

@click.command()
@click.option("--dry-run", is_flag=True, help="Show what would be done without creating files")
@click.option("--test", is_flag=True, help="Run tests after completion")
@click.option("--verbose", "-v", is_flag=True, help="Show detailed output")
def main(dry_run: bool, test: bool, verbose: bool):
    """Complete the remaining refactoring tasks."""
    console = Console()
    
    console.print(Panel(
        "🚀 **Refactoring Completion Script**\n\n"
        "This script completes all remaining tasks from Phases 1 and 3\n"
        "to finish the service architecture refactoring.",
        title="Completing Refactoring",
        border_style="green"
    ))
    
    completer = RefactoringCompleter(console)
    
    if dry_run:
        console.print("\n[yellow]🔍 DRY RUN - Showing what would be created:[/yellow]")
        tasks = completer.get_completion_tasks()
        
        table = Table(title="Refactoring Completion Plan")
        table.add_column("Phase", style="cyan")
        table.add_column("Task", style="magenta")
        table.add_column("File Path", style="green")
        
        for task in tasks:
            table.add_row(task.phase, task.description, task.file_path)
        
        console.print(table)
        console.print(f"\n[blue]Total: {len(tasks)} tasks to complete[/blue]")
        return 0
    
    # Execute completion
    try:
        results = asyncio.run(completer.complete_refactoring())
        
        # Show results
        console.print(f"\n[green]✅ Refactoring completion finished![/green]")
        console.print(f"📊 **Summary:**")
        console.print(f"  • Total tasks: {results['total_tasks']}")
        console.print(f"  • ✅ Completed: {results['completed_tasks']}")
        console.print(f"  • ❌ Failed: {results['failed_tasks']}")
        console.print(f"  • 📁 Files created: {len(results['created_files'])}")
        
        if results['errors']:
            console.print(f"\n[red]❌ Errors encountered:[/red]")
            for error in results['errors']:
                console.print(f"  • {error}")
        
        # Run tests if requested
        if test and results['completed_tasks'] > 0:
            console.print(f"\n[blue]🧪 Running completion tests...[/blue]")
            test_results = asyncio.run(completer.run_completion_tests())
            
            console.print(f"📊 **Test Results:**")
            console.print(f"  • Import tests passed: {test_results['import_tests']}")
            console.print(f"  • Import tests failed: {test_results['import_failures']}")
        
        # Success rate
        success_rate = (results['completed_tasks'] / results['total_tasks']) * 100 if results['total_tasks'] > 0 else 0
        
        if success_rate >= 90:
            console.print(f"\n[green]🎉 Excellent! {success_rate:.1f}% success rate.[/green]")
            console.print("[green]✅ Refactoring is now complete![/green]")
            return 0
        elif success_rate >= 75:
            console.print(f"\n[yellow]⚠️  Good progress: {success_rate:.1f}% completed with some issues.[/yellow]")
            return 1
        else:
            console.print(f"\n[red]❌ Significant issues: Only {success_rate:.1f}% completed.[/red]")
            return 1
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Completion interrupted by user[/yellow]")
        return 130
    except Exception as e:
        console.print(f"\n[red]Unexpected error: {e}[/red]")
        return 1

if __name__ == "__main__":
    sys.exit(main())