#!/usr/bin/env python3
"""
Parallel Phase Execution Script with Git Worktrees

This script executes Phase I, II, and III refactoring tasks in parallel using
separate git worktrees for each phase to avoid conflicts.

Usage:
    python scripts/refactoring/execute_phases_parallel_worktrees.py [--dry-run] [--phases 1,2,3]
"""

import asyncio
import concurrent.futures
import logging
import multiprocessing as mp
import sys
import time
import shutil
import subprocess
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import click
from rich.console import Console
from rich.logging import RichHandler
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TextColumn,
    TimeElapsedColumn,
)
from rich.table import Table
from rich.panel import Panel

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class PhaseStatus(Enum):
    PENDING = "pending"
    PREPARING = "preparing"
    RUNNING = "running"
    MERGING = "merging"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class PhaseResult:
    phase_id: str
    status: PhaseStatus
    duration: float
    tasks_completed: int
    tasks_failed: int
    worktree_path: Optional[Path] = None
    branch_name: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class PhaseTask:
    task_id: str
    description: str
    estimated_duration: int  # minutes
    dependencies: List[str]  # task IDs that must complete first
    script_path: Optional[str] = None  # Path to automation script


class WorktreePhaseExecutor:
    """Manages parallel execution of refactoring phases using git worktrees."""
    
    def __init__(self, console: Console, dry_run: bool = False):
        self.console = console
        self.dry_run = dry_run
        self.results: Dict[str, PhaseResult] = {}
        self.project_root = project_root
        self.worktrees_root = project_root / "worktrees"
        self.current_branch = None
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format="%(message)s",
            datefmt="[%X]",
            handlers=[RichHandler(console=console, rich_tracebacks=True)]
        )
        self.logger = logging.getLogger("worktree_executor")

    def get_phase_tasks(self) -> Dict[str, List[PhaseTask]]:
        """Define all tasks for each phase with automation scripts."""
        return {
            "phase_1": [
                PhaseTask(
                    "delete_redundant_html",
                    "Delete redundant src/core/html/ directory",
                    estimated_duration=5,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase1_delete_html.py"
                ),
                PhaseTask(
                    "migrate_async_decorators",
                    "Migrate async_decorators.py to AsyncDecoratorService",
                    estimated_duration=20,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase1_migrate_decorators.py"
                ),
                PhaseTask(
                    "migrate_cleanup_utils",
                    "Migrate cleanup_utils.py to ResourceCleanupService",
                    estimated_duration=25,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase1_migrate_cleanup.py"
                ),
                PhaseTask(
                    "migrate_performance_monitor",
                    "Migrate performance_monitor.py to PerformanceMonitorService",
                    estimated_duration=30,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase1_migrate_monitor.py"
                ),
                PhaseTask(
                    "enhance_factory_patterns",
                    "Implement type-safe service factories",
                    estimated_duration=35,
                    dependencies=["migrate_async_decorators"],
                    script_path="scripts/refactoring/automation/phase1_factory_patterns.py"
                ),
                PhaseTask(
                    "implement_service_registry",
                    "Create lightweight service registry",
                    estimated_duration=40,
                    dependencies=["enhance_factory_patterns"],
                    script_path="scripts/refactoring/automation/phase1_service_registry.py"
                ),
            ],
            "phase_2": [
                PhaseTask(
                    "implement_storage_protocol",
                    "Create StorageProtocol with async methods",
                    estimated_duration=20,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase2_storage_protocol.py"
                ),
                PhaseTask(
                    "implement_logger_protocol",
                    "Create LoggerProtocol with standardized methods",
                    estimated_duration=15,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase2_logger_protocol.py"
                ),
                PhaseTask(
                    "add_runtime_type_checking",
                    "Implement @runtime_checkable protocols",
                    estimated_duration=25,
                    dependencies=["implement_storage_protocol", "implement_logger_protocol"],
                    script_path="scripts/refactoring/automation/phase2_type_checking.py"
                ),
                PhaseTask(
                    "create_service_config_classes",
                    "Create type-safe ServiceConfig dataclasses",
                    estimated_duration=30,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase2_service_config.py"
                ),
                PhaseTask(
                    "enhance_config_injection",
                    "Implement type-safe configuration injection",
                    estimated_duration=35,
                    dependencies=["create_service_config_classes", "add_runtime_type_checking"],
                    script_path="scripts/refactoring/automation/phase2_config_injection.py"
                ),
            ],
            "phase_3": [
                PhaseTask(
                    "create_performance_metrics",
                    "Implement PerformanceMetrics dataclass",
                    estimated_duration=15,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase3_performance_metrics.py"
                ),
                PhaseTask(
                    "implement_monitoring_service",
                    "Create PerformanceMonitoringService",
                    estimated_duration=40,
                    dependencies=["create_performance_metrics"],
                    script_path="scripts/refactoring/automation/phase3_monitoring_service.py"
                ),
                PhaseTask(
                    "add_monitoring_decorators",
                    "Create async operation decorators for monitoring",
                    estimated_duration=25,
                    dependencies=["implement_monitoring_service"],
                    script_path="scripts/refactoring/automation/phase3_decorators.py"
                ),
                PhaseTask(
                    "implement_service_states",
                    "Create ServiceState enum and tracking",
                    estimated_duration=20,
                    dependencies=[],
                    script_path="scripts/refactoring/automation/phase3_service_states.py"
                ),
                PhaseTask(
                    "create_lifecycle_manager",
                    "Implement LifecycleManager for service coordination",
                    estimated_duration=45,
                    dependencies=["implement_service_states"],
                    script_path="scripts/refactoring/automation/phase3_lifecycle_manager.py"
                ),
                PhaseTask(
                    "add_health_checks",
                    "Implement health check system",
                    estimated_duration=30,
                    dependencies=["create_lifecycle_manager"],
                    script_path="scripts/refactoring/automation/phase3_health_checks.py"
                ),
            ]
        }

    def get_current_branch(self) -> str:
        """Get the current git branch."""
        if self.current_branch is None:
            result = subprocess.run(
                ["git", "branch", "--show-current"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            self.current_branch = result.stdout.strip()
        return self.current_branch

    def create_worktree(self, phase_id: str) -> Tuple[Path, str]:
        """Create a git worktree for a phase."""
        branch_name = f"refactor/{phase_id.replace('_', '-')}"
        worktree_path = self.worktrees_root / phase_id
        
        # Clean up existing worktree if it exists
        if worktree_path.exists():
            self.cleanup_worktree(phase_id)
        
        # Ensure worktrees directory exists
        self.worktrees_root.mkdir(exist_ok=True)
        
        # Get current branch before any operations
        original_branch = self.get_current_branch()
        
        # Special handling: if we're trying to create a worktree for the current branch,
        # use the current directory instead of creating a worktree
        if branch_name == original_branch:
            self.logger.info(f"Using current directory for {phase_id} (already on {branch_name})")
            return self.project_root, branch_name
        
        # Check if branch already exists
        branch_check = subprocess.run([
            "git", "branch", "--list", branch_name
        ], cwd=self.project_root, capture_output=True, text=True)
        
        # Create new branch only if it doesn't exist
        if not branch_check.stdout.strip():
            subprocess.run([
                "git", "checkout", "-b", branch_name
            ], cwd=self.project_root, check=True)
            
            # Switch back to original branch
            subprocess.run([
                "git", "checkout", original_branch
            ], cwd=self.project_root, check=True)
        
        # Create worktree
        subprocess.run([
            "git", "worktree", "add", str(worktree_path), branch_name
        ], cwd=self.project_root, check=True)
        
        return worktree_path, branch_name

    def cleanup_worktree(self, phase_id: str) -> None:
        """Clean up a git worktree."""
        worktree_path = self.worktrees_root / phase_id
        branch_name = f"refactor/{phase_id.replace('_', '-')}"
        
        if worktree_path.exists():
            # Remove worktree
            subprocess.run([
                "git", "worktree", "remove", str(worktree_path), "--force"
            ], cwd=self.project_root, capture_output=True)
            
            # Delete branch if it exists
            subprocess.run([
                "git", "branch", "-D", branch_name
            ], cwd=self.project_root, capture_output=True)

    async def execute_phase_task(self, phase_id: str, task: PhaseTask, worktree_path: Path) -> Tuple[str, bool, str]:
        """Execute a single phase task in its worktree."""
        if self.dry_run:
            await asyncio.sleep(0.1)  # Simulate work
            return task.task_id, True, f"[DRY RUN] {task.description}"
        
        try:
            self.logger.info(f"[{phase_id}] Executing: {task.description}")
            
            # Execute the specific automation script if available
            if task.script_path and (self.project_root / task.script_path).exists():
                result = subprocess.run([
                    sys.executable, task.script_path
                ], cwd=worktree_path, capture_output=True, text=True, timeout=300)
                
                success = result.returncode == 0
                if not success:
                    return task.task_id, False, f"Script failed: {result.stderr}"
            else:
                # Fallback to LLM prompt execution
                success = await self._execute_with_llm_prompt(phase_id, task, worktree_path)
            
            return task.task_id, success, f"Completed: {task.description}"
            
        except subprocess.TimeoutExpired:
            return task.task_id, False, f"Timeout: {task.description}"
        except Exception as e:
            return task.task_id, False, f"Failed: {task.description} - {str(e)}"

    async def _execute_with_llm_prompt(self, phase_id: str, task: PhaseTask, worktree_path: Path) -> bool:
        """Execute task using LLM prompt (placeholder for actual LLM integration)."""
        # This would integrate with your LLM tool of choice
        # For now, simulate work based on task complexity
        execution_time = task.estimated_duration * 60 / 100  # Scale down for demo
        await asyncio.sleep(execution_time)
        return True

    async def execute_phase(self, phase_id: str, tasks: List[PhaseTask]) -> PhaseResult:
        """Execute all tasks in a phase within its worktree."""
        start_time = time.time()
        completed_tasks = set()
        failed_tasks = set()
        
        # Create worktree for this phase
        try:
            worktree_path, branch_name = self.create_worktree(phase_id)
            self.logger.info(f"Created worktree for {phase_id}: {worktree_path}")
        except Exception as e:
            return PhaseResult(
                phase_id=phase_id,
                status=PhaseStatus.FAILED,
                duration=0.0,
                tasks_completed=0,
                tasks_failed=len(tasks),
                error_message=f"Failed to create worktree: {e}"
            )
        
        with Progress(
            SpinnerColumn(),
            TextColumn(f"[bold blue]{phase_id.upper()}"),
            BarColumn(),
            MofNCompleteColumn(),
            TimeElapsedColumn(),
            console=self.console,
        ) as progress:
            
            task_progress = progress.add_task(f"Phase {phase_id[-1]} Tasks", total=len(tasks))
            
            # Execute tasks in dependency order
            remaining_tasks = tasks.copy()
            
            while remaining_tasks:
                # Find tasks with satisfied dependencies
                ready_tasks = [
                    task for task in remaining_tasks
                    if all(dep in completed_tasks for dep in task.dependencies)
                ]
                
                if not ready_tasks:
                    # Check if we're stuck due to failed dependencies
                    blocked_by_failed = any(
                        any(dep in failed_tasks for dep in task.dependencies)
                        for task in remaining_tasks
                    )
                    if blocked_by_failed:
                        # Skip remaining tasks blocked by failures
                        for task in remaining_tasks:
                            failed_tasks.add(task.task_id)
                        break
                    else:
                        self.logger.error(f"Circular dependency detected in {phase_id}")
                        break
                
                # Execute ready tasks in parallel
                task_futures = [
                    self.execute_phase_task(phase_id, task, worktree_path)
                    for task in ready_tasks
                ]
                
                results = await asyncio.gather(*task_futures, return_exceptions=True)
                
                for task, result in zip(ready_tasks, results):
                    if isinstance(result, Exception):
                        failed_tasks.add(task.task_id)
                        self.logger.error(f"[{phase_id}] Task {task.task_id} failed: {result}")
                    else:
                        task_id, success, message = result
                        if success:
                            completed_tasks.add(task_id)
                            self.logger.info(f"[{phase_id}] {message}")
                        else:
                            failed_tasks.add(task_id)
                            self.logger.error(f"[{phase_id}] {message}")
                    
                    remaining_tasks.remove(task)
                    progress.update(task_progress, advance=1)
        
        duration = time.time() - start_time
        
        return PhaseResult(
            phase_id=phase_id,
            status=PhaseStatus.COMPLETED if not failed_tasks else PhaseStatus.FAILED,
            duration=duration,
            tasks_completed=len(completed_tasks),
            tasks_failed=len(failed_tasks),
            worktree_path=worktree_path,
            branch_name=branch_name,
            error_message=f"Failed tasks: {failed_tasks}" if failed_tasks else None
        )

    async def merge_phase_results(self, phase_results: List[PhaseResult]) -> bool:
        """Merge successful phase results back to main branch."""
        successful_phases = [r for r in phase_results if r.status == PhaseStatus.COMPLETED]
        
        if not successful_phases:
            self.console.print("[red]No successful phases to merge[/red]")
            return False
        
        original_branch = self.get_current_branch()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            
            for phase_result in successful_phases:
                task = progress.add_task(f"Merging {phase_result.phase_id}...", total=None)
                
                try:
                    # Check if we're already in a worktree and handle appropriately
                    worktree_check = subprocess.run([
                        "git", "worktree", "list"
                    ], cwd=self.project_root, capture_output=True, text=True)
                    
                    # For merging, we need to work from the main directory, not the worktree
                    if str(phase_result.worktree_path) in worktree_check.stdout:
                        # Commit any pending changes in the worktree
                        subprocess.run([
                            "git", "add", "."
                        ], cwd=phase_result.worktree_path, capture_output=True)
                        
                        subprocess.run([
                            "git", "commit", "-m", f"feat: complete {phase_result.phase_id} refactoring"
                        ], cwd=phase_result.worktree_path, capture_output=True)
                        
                        # Switch to original branch from main directory and merge
                        subprocess.run([
                            "git", "checkout", original_branch
                        ], cwd=self.project_root, capture_output=True)
                        
                        # Merge the phase branch
                        merge_result = subprocess.run([
                            "git", "merge", phase_result.branch_name, "--no-ff", 
                            "-m", f"Merge {phase_result.phase_id} refactoring"
                        ], cwd=self.project_root, capture_output=True, text=True)
                        
                        if merge_result.returncode != 0:
                            self.logger.error(f"Merge conflict in {phase_result.phase_id}: {merge_result.stderr}")
                            continue
                    else:
                        self.logger.warning(f"Worktree for {phase_result.phase_id} not found, skipping merge")
                        continue
                    
                    progress.update(task, description=f"✅ Merged {phase_result.phase_id}")
                    
                except subprocess.CalledProcessError as e:
                    self.logger.error(f"Failed to merge {phase_result.phase_id}: {e}")
                    progress.update(task, description=f"❌ Failed {phase_result.phase_id}")
        
        return True

    async def cleanup_worktrees(self, phase_results: List[PhaseResult]) -> None:
        """Clean up all worktrees after execution."""
        for phase_result in phase_results:
            if phase_result.worktree_path:
                try:
                    self.cleanup_worktree(phase_result.phase_id)
                    self.logger.info(f"Cleaned up worktree for {phase_result.phase_id}")
                except Exception as e:
                    self.logger.error(f"Failed to cleanup {phase_result.phase_id}: {e}")

    async def execute_phases_parallel(self, phase_ids: List[str]) -> Dict[str, PhaseResult]:
        """Execute multiple phases in parallel using worktrees."""
        all_tasks = self.get_phase_tasks()
        
        self.logger.info(f"Starting parallel execution of phases: {phase_ids}")
        
        # Execute phases sequentially to avoid Rich display conflicts
        # TODO: Implement proper multi-display handling for true parallelism
        results = []
        for phase_id in phase_ids:
            if phase_id in all_tasks:
                try:
                    result = await self.execute_phase(phase_id, all_tasks[phase_id])
                    results.append(result)
                except Exception as e:
                    result = PhaseResult(
                        phase_id=phase_id,
                        status=PhaseStatus.FAILED,
                        duration=0.0,
                        tasks_completed=0,
                        tasks_failed=0,
                        error_message=str(e)
                    )
                    results.append(result)
        
        # Process results
        phase_results = results
        for result in phase_results:
            self.results[result.phase_id] = result
        
        # Merge successful results
        if not self.dry_run:
            await self.merge_phase_results(phase_results)
        
        # Cleanup worktrees
        await self.cleanup_worktrees(phase_results)
        
        return self.results

    def display_results(self):
        """Display execution results in a formatted table."""
        table = Table(title="Phase Execution Results (with Worktrees)")
        
        table.add_column("Phase", style="cyan", no_wrap=True)
        table.add_column("Status", style="magenta")
        table.add_column("Duration", style="green")
        table.add_column("Tasks Completed", style="blue")
        table.add_column("Tasks Failed", style="red")
        table.add_column("Worktree", style="yellow")
        table.add_column("Notes", style="white")
        
        for phase_id, result in self.results.items():
            status_color = {
                PhaseStatus.COMPLETED: "green",
                PhaseStatus.FAILED: "red",
                PhaseStatus.RUNNING: "yellow",
                PhaseStatus.PENDING: "blue"
            }.get(result.status, "white")
            
            worktree_info = "N/A"
            if result.worktree_path:
                worktree_info = result.worktree_path.name
            
            table.add_row(
                phase_id.replace("_", " ").title(),
                f"[{status_color}]{result.status.value}[/{status_color}]",
                f"{result.duration:.2f}s",
                str(result.tasks_completed),
                str(result.tasks_failed),
                worktree_info,
                result.error_message or "✅"
            )
        
        self.console.print(table)


@click.command()
@click.option("--dry-run", is_flag=True, help="Run in dry-run mode without making changes")
@click.option("--phases", default="1,2,3", help="Comma-separated list of phases to run (1,2,3)")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
@click.option("--skip-merge", is_flag=True, help="Skip merging results (keep worktrees)")
def main(dry_run: bool, phases: str, verbose: bool, skip_merge: bool):
    """Execute refactoring phases in parallel using git worktrees."""
    console = Console()
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Parse phase selection
    try:
        phase_numbers = [int(p.strip()) for p in phases.split(",")]
        phase_ids = [f"phase_{p}" for p in phase_numbers if 1 <= p <= 3]
    except (ValueError, AttributeError):
        console.print("[red]Error: Invalid phase selection. Use format: 1,2,3[/red]")
        sys.exit(1)
    
    if not phase_ids:
        console.print("[red]Error: No valid phases selected. Choose from: 1, 2, 3[/red]")
        sys.exit(1)
    
    executor = WorktreePhaseExecutor(console, dry_run)
    
    # Show execution plan
    console.print(f"\n[bold blue]Execution Plan (Git Worktrees)[/bold blue]")
    console.print(f"Phases: {', '.join(phase_ids)}")
    console.print(f"Mode: {'DRY RUN' if dry_run else 'LIVE EXECUTION'}")
    console.print(f"Parallelization: Git worktrees in {executor.worktrees_root}")
    console.print(f"Merge: {'Disabled' if skip_merge else 'Enabled'}")
    
    if not dry_run:
        console.print("\n[yellow]⚠️  This will create git worktrees and branches![/yellow]")
        if not click.confirm("Continue with worktree execution?"):
            console.print("Execution cancelled.")
            sys.exit(0)
    
    # Execute phases
    try:
        start_time = time.time()
        results = asyncio.run(executor.execute_phases_parallel(phase_ids))
        total_time = time.time() - start_time
        
        # Display results
        console.print(f"\n[bold green]Execution Complete[/bold green] ({total_time:.2f}s total)")
        executor.display_results()
        
        # Summary
        completed_phases = sum(1 for r in results.values() if r.status == PhaseStatus.COMPLETED)
        failed_phases = sum(1 for r in results.values() if r.status == PhaseStatus.FAILED)
        
        if failed_phases > 0:
            console.print(f"\n[red]❌ {failed_phases} phase(s) failed[/red]")
            sys.exit(1)
        else:
            console.print(f"\n[green]✅ All {completed_phases} phase(s) completed successfully[/green]")
    
    except KeyboardInterrupt:
        console.print("\n[yellow]Execution interrupted by user[/yellow]")
        # Cleanup any created worktrees
        try:
            asyncio.run(executor.cleanup_worktrees([
                PhaseResult(phase_id, PhaseStatus.FAILED, 0, 0, 0) 
                for phase_id in phase_ids
            ]))
        except:
            pass
        sys.exit(130)
    except Exception as e:
        console.print(f"\n[red]Unexpected error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()