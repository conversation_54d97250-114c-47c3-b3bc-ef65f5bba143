#!/usr/bin/env python3
"""
Phase I Validation Script

Validates that Phase I refactoring has been completed correctly:
- Redundant HTML components removed
- Utility services migrated to ComponentImplementation
- Factory patterns implemented
- Service registry created
- All tests passing
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import importlib.util
import subprocess
import json

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

console = Console()

class ValidationResult:
    def __init__(self, name: str, passed: bool, message: str, details: Optional[Dict] = None):
        self.name = name
        self.passed = passed
        self.message = message
        self.details = details or {}

class PhaseOneValidator:
    def __init__(self):
        self.results: List[ValidationResult] = []
        self.project_root = project_root
    
    def validate_directory_structure(self) -> ValidationResult:
        """Validate expected directory structure changes."""
        try:
            # Check that redundant HTML directory is removed
            redundant_html_path = self.project_root / "src" / "core" / "html"
            html_removed = not redundant_html_path.exists()
            
            # Check that services directory has expected structure
            services_path = self.project_root / "src" / "services"
            utils_services_path = services_path / "utils"
            
            expected_services = [
                "async_decorator_service.py",
                "resource_cleanup_service.py", 
                "performance_monitor_service.py"
            ]
            
            services_exist = utils_services_path.exists() and all(
                (utils_services_path / service).exists() for service in expected_services
            )
            
            # Check infrastructure directories
            infrastructure_path = self.project_root / "src" / "infrastructure"
            expected_infrastructure = [
                "factories",
                "registry"
            ]
            
            infrastructure_exists = infrastructure_path.exists() and all(
                (infrastructure_path / infra).exists() for infra in expected_infrastructure
            )
            
            passed = html_removed and services_exist and infrastructure_exists
            
            details = {
                "redundant_html_removed": html_removed,
                "services_created": services_exist,
                "infrastructure_created": infrastructure_exists,
                "missing_services": [s for s in expected_services if not (utils_services_path / s).exists()],
                "missing_infrastructure": [i for i in expected_infrastructure if not (infrastructure_path / i).exists()]
            }
            
            message = "Directory structure validation " + ("passed" if passed else "failed")
            return ValidationResult("directory_structure", passed, message, details)
            
        except Exception as e:
            return ValidationResult("directory_structure", False, f"Error: {e}")
    
    def validate_service_implementations(self) -> ValidationResult:
        """Validate that services extend ComponentImplementation."""
        try:
            services_to_check = [
                "src.services.utils.async_decorator_service",
                "src.services.utils.resource_cleanup_service",
                "src.services.utils.performance_monitor_service"
            ]
            
            valid_services = []
            invalid_services = []
            
            for service_module in services_to_check:
                try:
                    # Import the module
                    module = importlib.import_module(service_module)
                    
                    # Look for service classes
                    service_classes = [
                        getattr(module, name) for name in dir(module)
                        if (name.endswith('Service') and 
                            hasattr(getattr(module, name), '__bases__') and
                            not name.startswith('_'))
                    ]
                    
                    for service_class in service_classes:
                        # Check if it extends ComponentImplementation or AsyncServiceBase
                        base_names = [base.__name__ for base in service_class.__bases__]
                        if 'ComponentImplementation' in base_names or 'AsyncServiceBase' in base_names:
                            valid_services.append(f"{service_module}.{service_class.__name__}")
                        else:
                            invalid_services.append(f"{service_module}.{service_class.__name__}")
                            
                except ImportError as e:
                    invalid_services.append(f"{service_module} (import error: {e})")
            
            passed = len(invalid_services) == 0
            details = {
                "valid_services": valid_services,
                "invalid_services": invalid_services
            }
            
            message = f"Service implementation validation {'passed' if passed else 'failed'}"
            return ValidationResult("service_implementations", passed, message, details)
            
        except Exception as e:
            return ValidationResult("service_implementations", False, f"Error: {e}")
    
    def validate_factory_patterns(self) -> ValidationResult:
        """Validate factory pattern implementation."""
        try:
            factory_files = [
                "src/infrastructure/factories/enhanced_service_factory.py",
                "src/infrastructure/registry/service_registry.py"
            ]
            
            files_exist = all((self.project_root / f).exists() for f in factory_files)
            
            if not files_exist:
                missing_files = [f for f in factory_files if not (self.project_root / f).exists()]
                return ValidationResult(
                    "factory_patterns", 
                    False, 
                    "Factory pattern files missing",
                    {"missing_files": missing_files}
                )
            
            # Try to import factory modules
            try:
                from src.infrastructure.factories.enhanced_service_factory import EnhancedServiceFactory
                from src.infrastructure.registry.service_registry import ServiceRegistry
                
                # Basic validation that classes exist and have expected methods
                factory_valid = hasattr(EnhancedServiceFactory, 'create') and hasattr(EnhancedServiceFactory, 'with_dependency')
                registry_valid = hasattr(ServiceRegistry, 'register_factory') and hasattr(ServiceRegistry, 'get_service')
                
                passed = factory_valid and registry_valid
                details = {
                    "factory_valid": factory_valid,
                    "registry_valid": registry_valid
                }
                
                message = f"Factory patterns validation {'passed' if passed else 'failed'}"
                return ValidationResult("factory_patterns", passed, message, details)
                
            except ImportError as e:
                return ValidationResult("factory_patterns", False, f"Import error: {e}")
                
        except Exception as e:
            return ValidationResult("factory_patterns", False, f"Error: {e}")
    
    def validate_tests(self) -> ValidationResult:
        """Validate that tests pass for migrated services."""
        try:
            # Run pytest on utils services
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                "tests/unit/services/utils/",
                "-v", "--tb=short"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            passed = result.returncode == 0
            details = {
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            message = f"Tests validation {'passed' if passed else 'failed'}"
            return ValidationResult("tests", passed, message, details)
            
        except Exception as e:
            return ValidationResult("tests", False, f"Error running tests: {e}")
    
    def validate_imports(self) -> ValidationResult:
        """Validate no imports point to old locations."""
        try:
            # Search for imports that might point to old locations
            old_import_patterns = [
                "from src.core.html",
                "import src.core.html",
                "from src.lib.utils.async_decorators",
                "from src.lib.utils.cleanup_utils",
                "from src.lib.utils.performance_monitor"
            ]
            
            problematic_files = []
            
            # Search through Python files
            for py_file in self.project_root.rglob("*.py"):
                if "/__pycache__/" in str(py_file) or "/archive/" in str(py_file):
                    continue
                
                try:
                    content = py_file.read_text()
                    for pattern in old_import_patterns:
                        if pattern in content:
                            problematic_files.append({
                                "file": str(py_file.relative_to(self.project_root)),
                                "pattern": pattern
                            })
                except Exception:
                    continue
            
            passed = len(problematic_files) == 0
            details = {"problematic_imports": problematic_files}
            
            message = f"Import validation {'passed' if passed else 'failed'}"
            return ValidationResult("imports", passed, message, details)
            
        except Exception as e:
            return ValidationResult("imports", False, f"Error: {e}")
    
    def validate_type_checking(self) -> ValidationResult:
        """Validate mypy type checking on migrated services."""
        try:
            # Run mypy on utils services
            result = subprocess.run([
                sys.executable, "-m", "mypy", 
                "src/services/utils/",
                "--ignore-missing-imports"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            passed = result.returncode == 0
            details = {
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            message = f"Type checking validation {'passed' if passed else 'failed'}"
            return ValidationResult("type_checking", passed, message, details)
            
        except Exception as e:
            return ValidationResult("type_checking", False, f"Error running mypy: {e}")
    
    async def run_all_validations(self) -> None:
        """Run all validation checks."""
        validations = [
            ("Directory Structure", self.validate_directory_structure),
            ("Service Implementations", self.validate_service_implementations),
            ("Factory Patterns", self.validate_factory_patterns),
            ("Tests", self.validate_tests),
            ("Imports", self.validate_imports),
            ("Type Checking", self.validate_type_checking)
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            for name, validation_func in validations:
                task = progress.add_task(f"Validating {name}...", total=None)
                
                try:
                    result = validation_func()
                    self.results.append(result)
                    
                    status = "✅" if result.passed else "❌"
                    progress.update(task, description=f"{status} {name}")
                    
                except Exception as e:
                    error_result = ValidationResult(name.lower().replace(" ", "_"), False, f"Error: {e}")
                    self.results.append(error_result)
                    progress.update(task, description=f"❌ {name} (Error)")
                
                await asyncio.sleep(0.1)
    
    def display_results(self) -> None:
        """Display validation results in a formatted table."""
        table = Table(title="🔍 Phase I Validation Results")
        
        table.add_column("Check", style="cyan", no_wrap=True)
        table.add_column("Status", style="magenta")
        table.add_column("Message", style="green")
        table.add_column("Details", style="yellow")
        
        for result in self.results:
            status = "✅ PASS" if result.passed else "❌ FAIL"
            status_color = "green" if result.passed else "red"
            
            details_str = ""
            if result.details:
                if 'missing_services' in result.details and result.details['missing_services']:
                    details_str += f"Missing: {', '.join(result.details['missing_services'])}\n"
                if 'invalid_services' in result.details and result.details['invalid_services']:
                    details_str += f"Invalid: {', '.join(result.details['invalid_services'])}\n"
                if 'problematic_imports' in result.details and result.details['problematic_imports']:
                    details_str += f"Bad imports: {len(result.details['problematic_imports'])}\n"
                if 'return_code' in result.details and result.details['return_code'] != 0:
                    details_str += f"Exit code: {result.details['return_code']}\n"
            
            table.add_row(
                result.name.replace("_", " ").title(),
                f"[{status_color}]{status}[/{status_color}]",
                result.message,
                details_str.strip() or "✓"
            )
        
        console.print(table)
        
        # Summary
        passed_count = sum(1 for r in self.results if r.passed)
        total_count = len(self.results)
        
        if passed_count == total_count:
            console.print(Panel(
                f"[bold green]🎉 Phase I validation PASSED![/bold green]\n"
                f"All {total_count} checks completed successfully.",
                title="Validation Summary",
                border_style="green"
            ))
        else:
            failed_count = total_count - passed_count
            console.print(Panel(
                f"[bold red]❌ Phase I validation FAILED![/bold red]\n"
                f"{failed_count} out of {total_count} checks failed.\n"
                f"Please review the issues above before proceeding to Phase II.",
                title="Validation Summary",
                border_style="red"
            ))
    
    def save_report(self, filepath: Path) -> None:
        """Save validation report to JSON file."""
        report = {
            "timestamp": "2025-07-01T00:00:00Z",
            "phase": "Phase I",
            "total_checks": len(self.results),
            "passed_checks": sum(1 for r in self.results if r.passed),
            "failed_checks": sum(1 for r in self.results if not r.passed),
            "results": [
                {
                    "name": r.name,
                    "passed": r.passed,
                    "message": r.message,
                    "details": r.details
                }
                for r in self.results
            ]
        }
        
        filepath.parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2)
        
        console.print(f"[green]📄 Validation report saved to {filepath}[/green]")

async def main():
    """Main validation function."""
    console.print("[bold blue]🔍 Phase I Validation Starting...[/bold blue]\n")
    
    validator = PhaseOneValidator()
    await validator.run_all_validations()
    
    console.print("\n" + "="*50)
    validator.display_results()
    
    # Save report
    report_path = Path("reports/validation/phase_1_validation_report.json")
    validator.save_report(report_path)
    
    # Exit with appropriate code
    all_passed = all(r.passed for r in validator.results)
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    asyncio.run(main())