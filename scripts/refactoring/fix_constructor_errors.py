#!/usr/bin/env python3
"""
Fix Constructor Errors

Fixes all AsyncServiceBase constructor issues in the newly created services.
"""

import sys
from pathlib import Path
from typing import List, Tuple

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def get_fixes() -> List[Tuple[str, str, str]]:
    """Get all the constructor fixes needed."""
    return [
        # (file_path, old_constructor, new_constructor)
        (
            "src/services/infrastructure/resource_cleanup_service.py",
            """    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)""",
            """    def __init__(self, logger=None, config=None):
        import logging as std_logging
        logger = logger or std_logging.getLogger(__name__)
        super().__init__(logger, config)"""
        ),
        (
            "src/services/infrastructure/performance_monitor_service.py", 
            """    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)""",
            """    def __init__(self, logger=None, config=None):
        import logging as std_logging
        logger = logger or std_logging.getLogger(__name__)
        super().__init__(logger, config)"""
        ),
        (
            "src/services/monitoring/performance_monitoring_service.py",
            """    def __init__(self, config: Optional[MonitoringConfig] = None):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.config = config or MonitoringConfig()""",
            """    def __init__(self, config: Optional[MonitoringConfig] = None, logger=None):
        import logging as std_logging
        logger = logger or std_logging.getLogger(__name__)
        super().__init__(logger, {})
        self.config = config or MonitoringConfig()"""
        ),
        (
            "src/infrastructure/decorators/monitoring_decorators.py",
            """from src.services.monitoring.performance_monitoring_service import monitoring_service""",
            """# Import will be handled dynamically to avoid circular imports"""
        )
    ]

def apply_fixes():
    """Apply all the constructor fixes."""
    fixes = get_fixes()
    success_count = 0
    
    for file_path, old_code, new_code in fixes:
        full_path = project_root / file_path
        
        if not full_path.exists():
            print(f"❌ File not found: {file_path}")
            continue
        
        try:
            # Read the file
            with open(full_path, 'r') as f:
                content = f.read()
            
            # Apply the fix
            if old_code in content:
                content = content.replace(old_code, new_code)
                
                # Write back
                with open(full_path, 'w') as f:
                    f.write(content)
                
                print(f"✅ Fixed: {file_path}")
                success_count += 1
            else:
                print(f"⚠️  Pattern not found in: {file_path}")
                
        except Exception as e:
            print(f"❌ Error fixing {file_path}: {e}")
    
    print(f"\n📊 Applied {success_count}/{len(fixes)} fixes")

def fix_import_issues():
    """Fix import and circular dependency issues."""
    
    # Fix the Dict import in resource_cleanup_service.py
    cleanup_service = project_root / "src/services/infrastructure/resource_cleanup_service.py"
    if cleanup_service.exists():
        with open(cleanup_service, 'r') as f:
            content = f.read()
        
        if "from typing import Set, Any, Optional, Callable, List" in content:
            content = content.replace(
                "from typing import Set, Any, Optional, Callable, List",
                "from typing import Set, Any, Optional, Callable, List, Dict"
            )
            
            with open(cleanup_service, 'w') as f:
                f.write(content)
            print("✅ Fixed Dict import in resource_cleanup_service.py")
    
    # Fix the monitoring decorators circular import
    decorators_file = project_root / "src/infrastructure/decorators/monitoring_decorators.py"
    if decorators_file.exists():
        with open(decorators_file, 'r') as f:
            content = f.read()
        
        # Replace the problematic import with dynamic import
        old_import = "from src.services.monitoring.performance_monitoring_service import monitoring_service"
        new_import = """# Dynamic import to avoid circular dependency
def get_monitoring_service():
    try:
        from src.services.monitoring.performance_monitoring_service import monitoring_service
        return monitoring_service
    except ImportError:
        # Fallback for testing
        class MockMonitoringService:
            def record_metric(self, metrics): pass
        return MockMonitoringService()

monitoring_service = get_monitoring_service()"""
        
        if old_import in content:
            content = content.replace(old_import, new_import)
            
            with open(decorators_file, 'w') as f:
                f.write(content)
            print("✅ Fixed circular import in monitoring_decorators.py")

def create_simple_test_file():
    """Create a simple test file to verify the fixes."""
    test_content = '''
import pytest
import logging

def test_resource_cleanup_service_import():
    """Test that ResourceCleanupService can be imported and instantiated."""
    try:
        from src.services.infrastructure.resource_cleanup_service import ResourceCleanupService
        logger = logging.getLogger("test")
        service = ResourceCleanupService(logger=logger)
        assert service is not None
        print("✅ ResourceCleanupService import test passed")
    except Exception as e:
        print(f"❌ ResourceCleanupService import test failed: {e}")
        raise

def test_performance_monitor_service_import():
    """Test that PerformanceMonitorService can be imported and instantiated."""
    try:
        from src.services.infrastructure.performance_monitor_service import PerformanceMonitorService
        logger = logging.getLogger("test")
        service = PerformanceMonitorService(logger=logger)
        assert service is not None
        print("✅ PerformanceMonitorService import test passed")
    except Exception as e:
        print(f"❌ PerformanceMonitorService import test failed: {e}")
        raise

def test_type_safe_factory_import():
    """Test that TypeSafeServiceFactory can be imported."""
    try:
        from src.infrastructure.factories.type_safe_factory import TypeSafeServiceFactory
        factory = TypeSafeServiceFactory()
        assert factory is not None
        print("✅ TypeSafeServiceFactory import test passed")
    except Exception as e:
        print(f"❌ TypeSafeServiceFactory import test failed: {e}")
        raise

def test_service_registry_import():
    """Test that ServiceRegistry can be imported."""
    try:
        from src.infrastructure.registry.service_registry import ServiceRegistry
        registry = ServiceRegistry()
        assert registry is not None
        print("✅ ServiceRegistry import test passed")
    except Exception as e:
        print(f"❌ ServiceRegistry import test failed: {e}")
        raise

def test_performance_monitoring_service_import():
    """Test that PerformanceMonitoringService can be imported."""
    try:
        from src.services.monitoring.performance_monitoring_service import PerformanceMonitoringService
        logger = logging.getLogger("test")
        service = PerformanceMonitoringService(logger=logger)
        assert service is not None
        print("✅ PerformanceMonitoringService import test passed")
    except Exception as e:
        print(f"❌ PerformanceMonitoringService import test failed: {e}")
        raise

def test_lifecycle_manager_import():
    """Test that LifecycleManager can be imported."""
    try:
        from src.infrastructure.lifecycle.lifecycle_manager import LifecycleManager
        manager = LifecycleManager()
        assert manager is not None
        print("✅ LifecycleManager import test passed")
    except Exception as e:
        print(f"❌ LifecycleManager import test failed: {e}")
        raise

def test_health_check_system_import():
    """Test that HealthCheckSystem can be imported."""
    try:
        from src.infrastructure.health.health_check_system import HealthCheckSystem
        system = HealthCheckSystem()
        assert system is not None
        print("✅ HealthCheckSystem import test passed")
    except Exception as e:
        print(f"❌ HealthCheckSystem import test failed: {e}")
        raise

if __name__ == "__main__":
    # Run all tests
    tests = [
        test_resource_cleanup_service_import,
        test_performance_monitor_service_import,
        test_type_safe_factory_import,
        test_service_registry_import,
        test_performance_monitoring_service_import,
        test_lifecycle_manager_import,
        test_health_check_system_import
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            failed += 1
            print(f"Test {test.__name__} failed: {e}")
    
    print(f"\\n📊 Test Results: {passed} passed, {failed} failed")
    exit(0 if failed == 0 else 1)
'''
    
    test_file = project_root / "tests/unit/infrastructure/test_new_services.py"
    test_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(test_file, 'w') as f:
        f.write(test_content.strip())
    
    print(f"✅ Created test file: {test_file}")

if __name__ == "__main__":
    print("🔧 Fixing constructor errors...")
    apply_fixes()
    
    print("\\n🔧 Fixing import issues...")
    fix_import_issues()
    
    print("\\n🧪 Creating test file...")
    create_simple_test_file()
    
    print("\\n✅ All fixes applied!")