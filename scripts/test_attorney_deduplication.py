#!/usr/bin/env python3
"""
Test script to demonstrate attorney deduplication functionality.

This script simulates the HTML processing with your example case data
showing duplicate attorneys across multiple plaintiffs.
"""

def test_attorney_deduplication():
    """Test attorney deduplication with the provided example data."""
    
    # Simulate the parsed plaintiffs data from your example
    # Each plaintiff has the same 4 attorneys
    plaintiffs_data = [
        {
            "name": "<PERSON><PERSON><PERSON>",
            "attorneys": [
                {
                    "attorney_name": "<PERSON>",
                    "law_firm": "Environmental Litigation Group PC",
                    "phone": "************",
                    "email": "<EMAIL>"
                },
                {
                    "attorney_name": "<PERSON>", 
                    "law_firm": "Environmental Litigation Group PC",
                    "phone": "************",
                    "email": "<EMAIL>"
                },
                {
                    "attorney_name": "<PERSON>",
                    "law_firm": "Environmental Litigation Group PC", 
                    "phone": "************",
                    "email": "<EMAIL>"
                },
                {
                    "attorney_name": "<PERSON><PERSON>",
                    "law_firm": "ENVIRONMENTAL LITIGATION GROUP, P.C.",
                    "phone": "************", 
                    "email": "<EMAIL>"
                }
            ]
        },
        {
            "name": "<PERSON>",
            "attorneys": [
                {
                    "attorney_name": "<PERSON> A Anderson",
                    "law_firm": "Environmental Litigation Group PC"
                },
                {
                    "attorney_name": "Gregory A Cade",
                    "law_firm": "Environmental Litigation Group PC"
                },
                {
                    "attorney_name": "Kevin B Mc<PERSON>ie", 
                    "law_firm": "Environmental Litigation Group PC"
                },
                {
                    "attorney_name": "Yahn Eric Olson",
                    "law_firm": "ENVIRONMENTAL LITIGATION GROUP, P.C."
                }
            ]
        },
        {
            "name": "Michael Wayne Anderson", 
            "attorneys": [
                {
                    "attorney_name": "Gary A Anderson",
                    "law_firm": "Environmental Litigation Group PC"
                },
                {
                    "attorney_name": "Gregory A Cade",
                    "law_firm": "Environmental Litigation Group PC"
                },
                {
                    "attorney_name": "Kevin B McKie",
                    "law_firm": "Environmental Litigation Group PC"
                },
                {
                    "attorney_name": "Yahn Eric Olson", 
                    "law_firm": "ENVIRONMENTAL LITIGATION GROUP, P.C."
                }
            ]
        },
        {
            "name": "Nancy Kay Appleby",
            "attorneys": [
                {
                    "attorney_name": "Gary A Anderson",
                    "law_firm": "Environmental Litigation Group PC"
                },
                {
                    "attorney_name": "Gregory A Cade",
                    "law_firm": "Environmental Litigation Group PC" 
                },
                {
                    "attorney_name": "Kevin B McKie",
                    "law_firm": "Environmental Litigation Group PC"
                },
                {
                    "attorney_name": "Yahn Eric Olson",
                    "law_firm": "ENVIRONMENTAL LITIGATION GROUP, P.C."
                }
            ]
        }
    ]
    
    print("=== Attorney Deduplication Test ===")
    print(f"Input: {len(plaintiffs_data)} plaintiffs with duplicate attorneys")
    
    # Collect all attorneys from all plaintiffs (simulating current behavior)
    all_attorneys = []
    for plaintiff in plaintiffs_data:
        all_attorneys.extend(plaintiff.get('attorneys', []))
    
    print(f"Total attorneys before deduplication: {len(all_attorneys)}")
    
    # Apply deduplication logic from HTML processing service
    unique_attorneys = []
    seen_attorneys = set()
    
    for attorney in all_attorneys:
        if isinstance(attorney, dict):
            # Create unique key based on attorney name and law firm (case-insensitive)
            attorney_key = (
                attorney.get('attorney_name', '').strip().lower(),
                attorney.get('law_firm', '').strip().lower()
            )
            
            if attorney_key not in seen_attorneys and attorney_key != ('', ''):
                seen_attorneys.add(attorney_key)
                unique_attorneys.append(attorney)
    
    print(f"Unique attorneys after deduplication: {len(unique_attorneys)}")
    print("\nUnique attorneys found:")
    for i, attorney in enumerate(unique_attorneys, 1):
        print(f"  {i}. {attorney.get('attorney_name')} - {attorney.get('law_firm')}")
    
    # Test field consistency (plaintiffs -> plaintiff conversion)
    print(f"\n=== Field Consistency Test ===")
    case_details = {"plaintiffs": plaintiffs_data}
    
    # Extract names only from plaintiff objects
    plaintiff_names = []
    for p in case_details['plaintiffs']:
        if isinstance(p, dict):
            name = p.get('name')
            if name and name.strip():
                plaintiff_names.append(name.strip())
    
    # Deduplicate while preserving order
    seen = set()
    case_details['plaintiff'] = [x for x in plaintiff_names if not (x in seen or seen.add(x))]
    
    # Remove plural field
    del case_details['plaintiffs']
    
    print(f"Plaintiffs converted to plaintiff field: {case_details['plaintiff']}")
    
    print(f"\n=== Summary ===")
    print(f"✅ Reduced {len(all_attorneys)} duplicate attorneys to {len(unique_attorneys)} unique attorneys")
    print(f"✅ Converted plaintiffs (plural) to plaintiff (singular) with {len(case_details['plaintiff'])} names")
    print(f"✅ Attorney deduplication working correctly!")

if __name__ == "__main__":
    test_attorney_deduplication()