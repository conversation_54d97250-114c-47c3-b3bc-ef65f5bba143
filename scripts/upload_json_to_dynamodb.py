#!/usr/bin/env python
"""
Upload all JSON files from a given date directory to DynamoDB.

Usage:
    python src/scripts/upload_json_to_dynamodb.py --date 20250617
    python src/scripts/upload_json_to_dynamodb.py --date 20250617 --dry-run
"""

import asyncio
import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import argparse
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from dotenv import load_dotenv
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.logging import RichHandler

# Load environment variables
load_dotenv()

# Import DynamoDB infrastructure
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository

# Setup logging with rich
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    handlers=[RichHandler(rich_tracebacks=True)]
)
logger = logging.getLogger(__name__)
console = Console()


class DynamoDBUploader:
    """Handles uploading JSON files to DynamoDB."""
    
    def __init__(self, date_str: str, dry_run: bool = False):
        self.date_str = date_str
        self.dry_run = dry_run
        self.storage = None
        self.pacer_repo = None
        self.stats = {
            'total_files': 0,
            'successful_uploads': 0,
            'failed_uploads': 0,
            'skipped_files': 0,
            'errors': []
        }
        
        # Determine data directory
        project_root = Path(__file__).parent.parent.parent
        self.data_dir = project_root / 'data' / date_str / 'dockets'
        
        if not self.data_dir.exists():
            raise ValueError(f"Data directory does not exist: {self.data_dir}")
    
    async def initialize(self):
        """Initialize DynamoDB connection."""
        self.storage = AsyncDynamoDBStorage({
            'aws_region': os.environ.get('AWS_REGION', 'us-west-2'),
            'aws_access_key': os.environ.get('AWS_ACCESS_KEY'),
            'aws_secret_key': os.environ.get('AWS_SECRET_KEY')
        })
        await self.storage.__aenter__()
        self.pacer_repo = PacerRepository(self.storage)
        logger.info("✅ DynamoDB connection initialized")
    
    async def cleanup(self):
        """Clean up DynamoDB connection."""
        if self.storage:
            await self.storage.__aexit__(None, None, None)
    
    def load_json_file(self, file_path: Path) -> Dict[str, Any]:
        """Load and validate JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Validate required fields
            required_fields = ['court_id', 'docket_num', 'filing_date']
            missing_fields = [field for field in required_fields if field not in data or not data[field]]
            
            if missing_fields:
                raise ValueError(f"Missing required fields: {missing_fields}")
            
            # Ensure added_on is set to the date from directory if not present
            if 'added_on' not in data or not data['added_on']:
                data['added_on'] = self.date_str
                logger.debug(f"Set added_on to {self.date_str} for {file_path.name}")
            
            # CRITICAL CHECK: Verify added_on is not being overwritten with filing_date
            if data.get('added_on') != self.date_str:
                logger.error(f"❌ CRITICAL: added_on ({data.get('added_on')}) does not match expected date ({self.date_str}) for {file_path.name}")
                # Force correct the value
                data['added_on'] = self.date_str
                logger.info(f"✅ CORRECTED: Set added_on to {self.date_str} for {file_path.name}")
            
            return data
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON: {e}")
        except Exception as e:
            raise ValueError(f"Error loading file: {e}")
    
    async def upload_file(self, file_path: Path) -> bool:
        """Upload a single JSON file to DynamoDB."""
        try:
            # Load JSON data
            data = self.load_json_file(file_path)
            
            # Log what we're uploading with more detail
            filing_date = data.get('filing_date')
            added_on = data.get('added_on')
            logger.info(f"Uploading {file_path.name}: court_id={data.get('court_id')}, "
                        f"docket_num={data.get('docket_num')}, filing_date={filing_date}, "
                        f"added_on={added_on}")
            
            if self.dry_run:
                logger.info(f"[DRY RUN] Would upload: {file_path.name}")
                return True
            
            # Upload to DynamoDB
            success = await self.pacer_repo.add_or_update_record(data)
            
            if success:
                logger.debug(f"✅ Successfully uploaded: {file_path.name}")
                return True
            else:
                logger.error(f"❌ Failed to upload: {file_path.name}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error uploading {file_path.name}: {e}")
            self.stats['errors'].append({
                'file': file_path.name,
                'error': str(e)
            })
            return False
    
    async def upload_all_files(self):
        """Upload all JSON files from the date directory."""
        # Get all JSON files
        json_files = list(self.data_dir.glob("*.json"))
        self.stats['total_files'] = len(json_files)
        
        if not json_files:
            logger.warning(f"No JSON files found in {self.data_dir}")
            return
        
        console.print(f"\n📁 Found [bold green]{len(json_files)}[/bold green] JSON files in {self.data_dir}")
        
        # Process files with progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            task = progress.add_task(
                f"[cyan]Uploading files to DynamoDB...", 
                total=len(json_files)
            )
            
            # Process in batches  
            batch_size = 25  # Increased batch size for faster processing
            for i in range(0, len(json_files), batch_size):
                batch = json_files[i:i + batch_size]
                
                # Upload batch concurrently
                results = await asyncio.gather(
                    *[self.upload_file(file_path) for file_path in batch],
                    return_exceptions=True
                )
                
                # Update stats
                for j, result in enumerate(results):
                    if isinstance(result, Exception):
                        self.stats['failed_uploads'] += 1
                        self.stats['errors'].append({
                            'file': batch[j].name,
                            'error': str(result)
                        })
                    elif result:
                        self.stats['successful_uploads'] += 1
                    else:
                        self.stats['failed_uploads'] += 1
                
                progress.update(task, advance=len(batch))
    
    def print_summary(self):
        """Print upload summary."""
        console.print("\n📊 Upload Summary:")
        console.print(f"  Total files: [bold]{self.stats['total_files']}[/bold]")
        console.print(f"  ✅ Successful uploads: [bold green]{self.stats['successful_uploads']}[/bold green]")
        console.print(f"  ❌ Failed uploads: [bold red]{self.stats['failed_uploads']}[/bold red]")
        console.print(f"  ⏭️  Skipped files: [bold yellow]{self.stats['skipped_files']}[/bold yellow]")
        
        if self.stats['errors']:
            console.print(f"\n❌ Errors ({len(self.stats['errors'])}):")
            for error in self.stats['errors'][:10]:  # Show first 10 errors
                console.print(f"  - {error['file']}: {error['error']}")
            if len(self.stats['errors']) > 10:
                console.print(f"  ... and {len(self.stats['errors']) - 10} more errors")
    
    async def verify_uploads(self, sample_size: int = None):
        """Verify uploads by querying DynamoDB."""
        json_files = list(self.data_dir.glob("*.json"))
        if sample_size:
            json_files = json_files[:sample_size]
            console.print(f"\n🔍 Verifying uploads (sample size: {sample_size})...")
        else:
            console.print(f"\n🔍 Verifying ALL {len(json_files)} uploads...")
        verified = 0
        
        for file_path in json_files:
            try:
                data = self.load_json_file(file_path)
                court_id = data.get('court_id')
                docket_num = data.get('docket_num')
                
                if court_id and docket_num:
                    records = await self.pacer_repo.query_by_court_and_docket(court_id, docket_num)
                    if records:
                        verified += 1
                        logger.info(f"✅ Verified: {court_id} {docket_num} (added_on: {records[0].get('added_on')})")
                    else:
                        logger.warning(f"❌ Not found: {court_id} {docket_num}")
            except Exception as e:
                logger.error(f"Error verifying {file_path.name}: {e}")
        
        console.print(f"\n✅ Verified {verified}/{len(json_files)} uploads")


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Upload JSON files to DynamoDB")
    parser.add_argument('--date', required=True, help='Date in YYYYMMDD format (e.g., 20250617)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be uploaded without actually uploading')
    parser.add_argument('--verify', action='store_true', help='Verify uploads after completion')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate date format
    try:
        datetime.strptime(args.date, '%Y%m%d')
    except ValueError:
        console.print("[bold red]Error:[/bold red] Date must be in YYYYMMDD format")
        sys.exit(1)
    
    # Create uploader
    try:
        uploader = DynamoDBUploader(args.date, dry_run=args.dry_run)
    except ValueError as e:
        console.print(f"[bold red]Error:[/bold red] {e}")
        sys.exit(1)
    
    # Run upload
    try:
        console.print(f"\n🚀 Starting DynamoDB upload for date: [bold cyan]{args.date}[/bold cyan]")
        if args.dry_run:
            console.print("[bold yellow]DRY RUN MODE - No actual uploads will be performed[/bold yellow]")
        
        await uploader.initialize()
        await uploader.upload_all_files()
        
        if args.verify and not args.dry_run:
            await uploader.verify_uploads()
        
        uploader.print_summary()
        
    except KeyboardInterrupt:
        console.print("\n[bold red]Upload interrupted by user[/bold red]")
    except Exception as e:
        console.print(f"\n[bold red]Fatal error:[/bold red] {e}")
        logger.exception("Fatal error during upload")
        sys.exit(1)
    finally:
        await uploader.cleanup()


if __name__ == "__main__":
    asyncio.run(main())