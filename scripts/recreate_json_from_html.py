#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to recreate JSON files from HTML files using HTMLCaseParser and HTMLDataUpdater.

Usage:
    python scripts/recreate_json_from_html.py --date YYYYMMDD
"""

import argparse
import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Optional

from rich.console import Console
from rich.logging import RichHandler
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.html.html_case_parser import HTMLCaseParser
from src.utils.docket_utils import normalize_docket_number

# Setup logging with rich handler
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    handlers=[RichHandler(rich_tracebacks=True)]
)
logger = logging.getLogger(__name__)
console = Console()


def extract_court_and_docket_from_filename(html_filename: str) -> tuple[Optional[str], Optional[str]]:
    """
    Extract court_id and docket_num from HTML filename.
    
    Expected format: {court_id}_{YY}_{NNNNN}_{versus_text}.html
    
    Args:
        html_filename: The HTML filename
        
    Returns:
        Tuple of (court_id, docket_num) or (None, None) if extraction fails
    """
    try:
        # Remove .html extension
        base_name = html_filename.replace('.html', '')
        
        # Split by underscore
        parts = base_name.split('_')
        
        if len(parts) >= 3:
            court_id = parts[0]
            yy = parts[1]
            nnnnn = parts[2]
            
            # Reconstruct docket number in format N:YY-XX-NNNNN
            # We'll assume 'cv' as case type since it's most common
            # This could be improved by looking at the HTML content
            docket_num = f"1:{yy}-cv-{nnnnn}"
            
            return court_id, docket_num
        else:
            logger.warning(f"Could not parse filename format: {html_filename}")
            return None, None
            
    except Exception as e:
        logger.error(f"Error extracting court/docket from filename {html_filename}: {e}")
        return None, None


def process_html_file(html_path: str, output_dir: str) -> bool:
    """
    Process a single HTML file and create corresponding JSON file.
    
    Args:
        html_path: Path to the HTML file
        output_dir: Directory to save the JSON file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Read HTML content
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Parse HTML using HTMLCaseParser
        parser = HTMLCaseParser(html_content)
        parsed_data = parser.parse()
        
        # Extract base filename
        html_filename = os.path.basename(html_path)
        base_filename = html_filename.replace('.html', '')
        
        # Extract court_id and docket_num from filename
        court_id, docket_num = extract_court_and_docket_from_filename(html_filename)
        
        # Build the JSON data structure
        json_data = {
            'original_filename': base_filename,
            'new_filename': base_filename,
            'base_filename': base_filename,
            'html_only': True,  # Mark as recreated from HTML
            'processing_notes': 'Recreated from HTML file'
        }
        
        # Add court and docket info if extracted
        if court_id:
            json_data['court_id'] = court_id
        if docket_num:
            json_data['docket_num'] = normalize_docket_number(docket_num)
        
        # Extract case info
        case_info = parsed_data.get('case_info', {})
        if case_info:
            # Add case info fields
            json_data['versus'] = case_info.get('versus', '')
            json_data['assigned_to'] = case_info.get('assigned_to', '')
            json_data['referred_to'] = case_info.get('referred_to', '')
            json_data['date_filed'] = case_info.get('date_filed', '')
            json_data['jury_demand'] = case_info.get('jury_demand', '')
            json_data['nos'] = case_info.get('nos', '')
            json_data['jurisdiction'] = case_info.get('jurisdiction', '')
            json_data['demand'] = case_info.get('demand', '')
            json_data['lead_case'] = case_info.get('lead_case', '')
            json_data['cause'] = case_info.get('cause', '')
            json_data['case_in_other_court'] = case_info.get('case_in_other_court', '')
            json_data['flags'] = case_info.get('flags', [])
            json_data['court_name'] = case_info.get('court_name', '')
            json_data['office'] = case_info.get('office', '')
            
            # Override docket_num if found in case_info
            if case_info.get('docket_num'):
                json_data['docket_num'] = case_info.get('docket_num')
        
        # Extract plaintiffs
        plaintiffs = parsed_data.get('plaintiffs', [])
        if plaintiffs:
            plaintiff_names = []
            for p in plaintiffs:
                if isinstance(p, dict) and p.get('name'):
                    plaintiff_names.append(p.get('name'))
            # Remove duplicates while preserving order
            seen = set()
            json_data['plaintiff'] = [x for x in plaintiff_names if not (x in seen or seen.add(x))]
        
        # Extract defendants
        defendants = parsed_data.get('defendants', [])
        if defendants:
            # Ensure defendants are strings and deduplicate
            seen_defendants = set()
            json_data['defendant'] = []
            for d in defendants:
                if isinstance(d, str) and d.strip():
                    d_lower = d.strip().lower()
                    if d_lower not in seen_defendants:
                        seen_defendants.add(d_lower)
                        json_data['defendant'].append(d.strip())
        
        # Extract attorneys
        attorneys = parsed_data.get('attorney', [])
        if attorneys:
            json_data['attorney'] = attorneys
        
        # Check if removal case
        is_removal = parser.is_removal()
        if is_removal:
            json_data['is_removal'] = True
            json_data['removal_date'] = is_removal.get('removal_date', '')
        
        # Get initial filing date
        filing_info = parser.get_initial_filing_date()
        if filing_info:
            json_data['initial_filing_date'] = filing_info.get('initial_filing_date', '')
            # Override date_filed if we have initial_filing_date
            if filing_info.get('initial_filing_date'):
                json_data['date_filed'] = filing_info.get('initial_filing_date')
        
        # Process transfer info if case_in_other_court exists
        if json_data.get('case_in_other_court'):
            case_info = json_data.get('case_in_other_court', '')
            if ',' in case_info:
                transferor_docket_num = case_info.split(',')[1].strip()
                transferor_court_name = case_info.split(',')[0].strip()
                
                json_data['transferor_court_docket_num'] = transferor_docket_num
                json_data['transferor_court'] = transferor_court_name
        
        # Clean up null values
        NULL_CONDITIONS = ['', 'NA', None, 'Pro Se', 'PRO SE', "None", []]
        json_data = {key: value for key, value in json_data.items() if value not in NULL_CONDITIONS}
        
        # Create output path
        json_filename = base_filename + '.json'
        json_path = os.path.join(output_dir, json_filename)
        
        # Save JSON file
        os.makedirs(output_dir, exist_ok=True)
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        logger.debug(f"Successfully created {json_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error processing {html_path}: {e}")
        return False


def main():
    """Main function to process HTML files for a given date."""
    parser = argparse.ArgumentParser(description='Recreate JSON files from HTML files')
    parser.add_argument('--date', required=True, help='Date in YYYYMMDD format')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be processed without creating files')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate date format
    if len(args.date) != 8 or not args.date.isdigit():
        console.print("[red]Error: Date must be in YYYYMMDD format[/red]")
        sys.exit(1)
    
    # Construct paths
    date_str = args.date  # YYYYMMDD format
    
    dockets_dir = f"data/{date_str}/dockets"
    
    # Check if directory exists
    if not os.path.exists(dockets_dir):
        console.print(f"[red]Error: Directory {dockets_dir} does not exist[/red]")
        sys.exit(1)
    
    # Find all HTML files
    html_files = []
    for filename in os.listdir(dockets_dir):
        if filename.endswith('.html'):
            html_files.append(os.path.join(dockets_dir, filename))
    
    if not html_files:
        console.print(f"[yellow]No HTML files found in {dockets_dir}[/yellow]")
        return
    
    console.print(f"[green]Found {len(html_files)} HTML files to process[/green]")
    
    if args.dry_run:
        console.print("\n[yellow]DRY RUN - No files will be created[/yellow]")
        console.print("\nFiles that would be processed:")
        for html_file in html_files[:10]:  # Show first 10
            console.print(f"  • {os.path.basename(html_file)}")
        if len(html_files) > 10:
            console.print(f"  ... and {len(html_files) - 10} more")
        return
    
    # Process files with progress bar
    success_count = 0
    error_count = 0
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeRemainingColumn(),
        console=console
    ) as progress:
        task = progress.add_task("Processing HTML files...", total=len(html_files))
        
        for html_file in html_files:
            if process_html_file(html_file, dockets_dir):
                success_count += 1
            else:
                error_count += 1
            
            progress.update(task, advance=1)
    
    # Summary
    console.print(f"\n[green]✓ Successfully processed: {success_count} files[/green]")
    if error_count > 0:
        console.print(f"[red]✗ Errors: {error_count} files[/red]")
    
    console.print(f"\n[blue]JSON files saved to: {dockets_dir}[/blue]")


if __name__ == "__main__":
    main()