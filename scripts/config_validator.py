#!/usr/bin/env python3
"""
Configuration Resilience Tool for PACER Services
Addresses critical configuration cascade failures identified in troubleshooting analysis.
Provides validation, fallback mechanisms, and resilient configuration loading.
Uses existing infrastructure for consistent logging.
"""
import json
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import argparse

# Use existing infrastructure
from src.infrastructure.protocols.logger import LoggerProtocol


@dataclass
class ConfigValidationResult:
    """Result of configuration validation."""
    is_valid: bool
    file_path: str
    errors: List[str]
    warnings: List[str]
    fallback_used: bool = False
    fallback_path: Optional[str] = None


@dataclass
class ConfigDependency:
    """Configuration dependency definition."""
    file_path: str
    required: bool
    critical_for: List[str]  # Services that require this config
    fallback_paths: List[str]
    validation_schema: Optional[Dict[str, Any]] = None


class ConfigurationValidator:
    """
    Configuration validation and resilience manager.
    Addresses configuration cascade failures in PACER services.
    Uses LoggerProtocol for consistent logging.
    """
    
    def __init__(self, logger: Optional[LoggerProtocol] = None):
        self.logger = logger or self._create_default_logger()
        self.validation_results: List[ConfigValidationResult] = []
        
        # Define critical configuration dependencies
        self.critical_configs = self._define_critical_configs()
    
    def _create_default_logger(self) -> LoggerProtocol:
        """Create a default logger that implements LoggerProtocol."""
        import logging
        
        class DefaultLogger:
            def __init__(self):
                self._logger = logging.getLogger(__name__)
                
            def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.debug(message, extra=extra)
                
            def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.info(message, extra=extra)
                
            def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.warning(message, extra=extra)
                
            def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.error(message, extra=extra)
                
            def exception(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.exception(message, extra=extra)
        
        return DefaultLogger()
    
    def _define_critical_configs(self) -> List[ConfigDependency]:
        """Define critical configuration files and their dependencies."""
        return [
            ConfigDependency(
                file_path="config/ignore_download.json",
                required=True,
                critical_for=["PacerConfigurationService", "RelevanceService", "PacerOrchestratorService"],
                fallback_paths=[
                    "config/ignore_download.default.json",
                    "data/config/ignore_download.json"
                ],
                validation_schema={
                    "type": "object",
                    "required": ["courts", "cases"]
                }
            ),
            ConfigDependency(
                file_path="config/paths_config.json",
                required=True,
                critical_for=["PacerConfigurationService", "PacerFileManagementService"],
                fallback_paths=[
                    "config/paths_config.default.json",
                    "data/config/paths_config.json"
                ],
                validation_schema={
                    "type": "object",
                    "required": ["data_dir", "relevant_defendants_relative_path"]
                }
            ),
            ConfigDependency(
                file_path="config/relevant_defendants.json",
                required=True,
                critical_for=["RelevanceService", "PacerCaseClassificationService"],
                fallback_paths=[
                    "config/relevant_defendants.default.json",
                    "data/config/relevant_defendants.json"
                ],
                validation_schema={
                    "type": "object",
                    "required": ["defendants"]
                }
            ),
            ConfigDependency(
                file_path="config/scraper.yml",
                required=False,
                critical_for=["PacerOrchestratorService"],
                fallback_paths=[
                    "config/scraper.default.yml",
                    "config/default.yml"
                ]
            ),
            ConfigDependency(
                file_path="config/transform.yml",
                required=False,
                critical_for=["TransformationServices"],
                fallback_paths=[
                    "config/transform.default.yml",
                    "config/default.yml"
                ]
            )
        ]
    
    def validate_all_configs(self, project_root: Optional[str] = None) -> Tuple[bool, List[ConfigValidationResult]]:
        """
        Validate all critical configuration files.
        
        Args:
            project_root: Root directory to search for configs (defaults to current directory)
            
        Returns:
            Tuple of (all_valid, validation_results)
        """
        if project_root:
            project_path = Path(project_root)
        else:
            project_path = Path.cwd()
        
        self.validation_results.clear()
        all_valid = True
        
        self.logger.info("🔧 CONFIG VALIDATION: Starting validation of critical configurations")
        self.logger.info(f"   Project root: {project_path.absolute()}")
        
        for config_dep in self.critical_configs:
            result = self._validate_config_file(project_path, config_dep)
            self.validation_results.append(result)
            
            if not result.is_valid and config_dep.required:
                all_valid = False
        
        # Log summary
        total_configs = len(self.validation_results)
        valid_configs = sum(1 for r in self.validation_results if r.is_valid)
        failed_configs = total_configs - valid_configs
        
        if all_valid:
            self.logger.info(f"✅ CONFIG VALIDATION: All {total_configs} critical configs valid")
        else:
            self.logger.error(f"❌ CONFIG VALIDATION: {failed_configs}/{total_configs} configs failed")
        
        return all_valid, self.validation_results
    
    def _validate_config_file(self, project_path: Path, config_dep: ConfigDependency) -> ConfigValidationResult:
        """Validate a single configuration file with fallback support."""
        primary_path = project_path / config_dep.file_path
        
        self.logger.info(f"🔍 VALIDATING: {config_dep.file_path}")
        
        # Try primary path first
        if primary_path.exists():
            result = self._validate_file_content(str(primary_path), config_dep)
            if result.is_valid:
                self.logger.info(f"✅ VALID: {config_dep.file_path}")
                return result
            else:
                self.logger.warning(f"⚠️ INVALID: {config_dep.file_path} - {', '.join(result.errors)}")
        else:
            self.logger.warning(f"⚠️ MISSING: {config_dep.file_path}")
        
        # Try fallback paths
        for fallback_rel_path in config_dep.fallback_paths:
            fallback_path = project_path / fallback_rel_path
            
            if fallback_path.exists():
                self.logger.info(f"🔄 TRYING FALLBACK: {fallback_rel_path}")
                result = self._validate_file_content(str(fallback_path), config_dep)
                result.fallback_used = True
                result.fallback_path = str(fallback_path)
                
                if result.is_valid:
                    self.logger.info(f"✅ FALLBACK SUCCESS: Using {fallback_rel_path}")
                    return result
                else:
                    self.logger.warning(f"⚠️ FALLBACK INVALID: {fallback_rel_path}")
        
        # All attempts failed
        errors = [f"Primary file not found: {primary_path}"]
        if config_dep.fallback_paths:
            errors.append(f"No valid fallback found from: {config_dep.fallback_paths}")
        
        return ConfigValidationResult(
            is_valid=False,
            file_path=config_dep.file_path,
            errors=errors,
            warnings=[],
            fallback_used=False
        )
    
    def _validate_file_content(self, file_path: str, config_dep: ConfigDependency) -> ConfigValidationResult:
        """Validate the content of a configuration file."""
        errors = []
        warnings = []
        
        try:
            # Read file
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    content = json.load(f)
                elif file_path.endswith(('.yml', '.yaml')):
                    try:
                        import yaml
                        content = yaml.safe_load(f)
                    except ImportError:
                        warnings.append("PyYAML not available for YAML validation")
                        return ConfigValidationResult(
                            is_valid=True,  # Assume valid if we can't validate
                            file_path=file_path,
                            errors=errors,
                            warnings=warnings
                        )
                else:
                    # Unknown format, assume valid
                    return ConfigValidationResult(
                        is_valid=True,
                        file_path=file_path,
                        errors=errors,
                        warnings=warnings
                    )
            
            # Basic schema validation if provided
            if config_dep.validation_schema:
                schema_errors = self._validate_schema(content, config_dep.validation_schema)
                errors.extend(schema_errors)
            
            # File-specific validation
            if "ignore_download.json" in file_path:
                errors.extend(self._validate_ignore_download(content))
            elif "paths_config.json" in file_path:
                errors.extend(self._validate_paths_config(content))
            elif "relevant_defendants.json" in file_path:
                errors.extend(self._validate_relevant_defendants(content))
            
            return ConfigValidationResult(
                is_valid=len(errors) == 0,
                file_path=file_path,
                errors=errors,
                warnings=warnings
            )
            
        except json.JSONDecodeError as e:
            errors.append(f"Invalid JSON: {e}")
        except Exception as e:
            errors.append(f"Validation error: {e}")
        
        return ConfigValidationResult(
            is_valid=False,
            file_path=file_path,
            errors=errors,
            warnings=warnings
        )
    
    def _validate_schema(self, content: Any, schema: Dict[str, Any]) -> List[str]:
        """Basic schema validation."""
        errors = []
        
        if schema.get("type") == "object" and not isinstance(content, dict):
            errors.append("Expected object/dictionary")
        
        required_fields = schema.get("required", [])
        if isinstance(content, dict):
            for field in required_fields:
                if field not in content:
                    errors.append(f"Missing required field: {field}")
        
        return errors
    
    def _validate_ignore_download(self, content: Dict[str, Any]) -> List[str]:
        """Validate ignore_download.json specific content."""
        errors = []
        
        if "courts" not in content:
            errors.append("Missing 'courts' section")
        elif not isinstance(content["courts"], dict):
            errors.append("'courts' must be an object")
        
        if "cases" not in content:
            errors.append("Missing 'cases' section")
        elif not isinstance(content["cases"], dict):
            errors.append("'cases' must be an object")
        
        return errors
    
    def _validate_paths_config(self, content: Dict[str, Any]) -> List[str]:
        """Validate paths_config.json specific content."""
        errors = []
        
        required_paths = ["data_dir", "relevant_defendants_relative_path"]
        for path_key in required_paths:
            if path_key not in content:
                errors.append(f"Missing path configuration: {path_key}")
            elif not isinstance(content[path_key], str):
                errors.append(f"Path must be string: {path_key}")
        
        return errors
    
    def _validate_relevant_defendants(self, content: Dict[str, Any]) -> List[str]:
        """Validate relevant_defendants.json specific content."""
        errors = []
        
        if "defendants" not in content:
            errors.append("Missing 'defendants' section")
        elif not isinstance(content["defendants"], list):
            errors.append("'defendants' must be an array")
        elif len(content["defendants"]) == 0:
            errors.append("'defendants' array cannot be empty")
        
        return errors
    
    def create_fallback_configs(self, project_root: Optional[str] = None) -> int:
        """Create default fallback configuration files."""
        if project_root:
            project_path = Path(project_root)
        else:
            project_path = Path.cwd()
        
        created_count = 0
        
        self.logger.info("🔧 CREATING FALLBACK CONFIGS")
        
        # Create ignore_download.default.json
        ignore_download_default = {
            "courts": {},
            "cases": {},
            "_metadata": {
                "created_by": "config_validator.py",
                "created_at": datetime.now().isoformat(),
                "purpose": "Fallback configuration for ignore_download functionality"
            }
        }
        
        fallback_path = project_path / "config" / "ignore_download.default.json"
        if self._create_fallback_file(fallback_path, ignore_download_default):
            created_count += 1
        
        # Create paths_config.default.json
        paths_config_default = {
            "data_dir": "data",
            "relevant_defendants_relative_path": "config/relevant_defendants.json",
            "_metadata": {
                "created_by": "config_validator.py",
                "created_at": datetime.now().isoformat(),
                "purpose": "Fallback configuration for paths"
            }
        }
        
        fallback_path = project_path / "config" / "paths_config.default.json"
        if self._create_fallback_file(fallback_path, paths_config_default):
            created_count += 1
        
        # Create relevant_defendants.default.json
        defendants_default = {
            "defendants": [
                "Johnson & Johnson",
                "Pfizer",
                "Abbott",
                "Merck"
            ],
            "_metadata": {
                "created_by": "config_validator.py",
                "created_at": datetime.now().isoformat(),
                "purpose": "Fallback configuration for relevant defendants"
            }
        }
        
        fallback_path = project_path / "config" / "relevant_defendants.default.json"
        if self._create_fallback_file(fallback_path, defendants_default):
            created_count += 1
        
        self.logger.info(f"✅ FALLBACK CONFIGS: Created {created_count} fallback files")
        return created_count
    
    def _create_fallback_file(self, file_path: Path, content: Dict[str, Any]) -> bool:
        """Create a fallback configuration file."""
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            if not file_path.exists():
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(content, f, indent=2, ensure_ascii=False)
                self.logger.info(f"✅ CREATED: {file_path}")
                return True
            else:
                self.logger.info(f"⏭️ EXISTS: {file_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ FAILED TO CREATE: {file_path} - {e}")
            return False
    
    def generate_report(self) -> str:
        """Generate a detailed validation report."""
        report_lines = [
            "=" * 80,
            "PACER CONFIGURATION VALIDATION REPORT",
            "=" * 80,
            f"Generated: {datetime.now().isoformat()}",
            f"Total configurations checked: {len(self.validation_results)}",
            ""
        ]
        
        # Summary
        valid_count = sum(1 for r in self.validation_results if r.is_valid)
        invalid_count = len(self.validation_results) - valid_count
        fallback_count = sum(1 for r in self.validation_results if r.fallback_used)
        
        report_lines.extend([
            "SUMMARY:",
            f"  ✅ Valid configurations: {valid_count}",
            f"  ❌ Invalid configurations: {invalid_count}",
            f"  🔄 Using fallbacks: {fallback_count}",
            ""
        ])
        
        # Detailed results
        report_lines.append("DETAILED RESULTS:")
        for result in self.validation_results:
            status = "✅ VALID" if result.is_valid else "❌ INVALID"
            report_lines.append(f"  {status}: {result.file_path}")
            
            if result.fallback_used:
                report_lines.append(f"    🔄 Fallback: {result.fallback_path}")
            
            if result.errors:
                for error in result.errors:
                    report_lines.append(f"    ❌ Error: {error}")
            
            if result.warnings:
                for warning in result.warnings:
                    report_lines.append(f"    ⚠️ Warning: {warning}")
            
            report_lines.append("")
        
        return "\n".join(report_lines)


def main():
    """Main CLI interface for configuration validation."""
    parser = argparse.ArgumentParser(description="PACER Configuration Validator")
    parser.add_argument("--project-root", help="Project root directory")
    parser.add_argument("--create-fallbacks", action="store_true", 
                       help="Create default fallback configuration files")
    parser.add_argument("--report", help="Output report to file")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create logger that implements LoggerProtocol
    import logging
    
    class CLILogger:
        def __init__(self):
            self._logger = logging.getLogger(__name__)
            
        def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
            self._logger.debug(message, extra=extra)
            
        def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
            self._logger.info(message, extra=extra)
            
        def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
            self._logger.warning(message, extra=extra)
            
        def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
            self._logger.error(message, extra=extra)
            
        def exception(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
            self._logger.exception(message, extra=extra)
    
    cli_logger = CLILogger()
    validator = ConfigurationValidator(logger=cli_logger)
    
    try:
        # Create fallback configs if requested
        if args.create_fallbacks:
            created_count = validator.create_fallback_configs(args.project_root)
            print(f"Created {created_count} fallback configuration files")
        
        # Validate configurations
        all_valid, results = validator.validate_all_configs(args.project_root)
        
        # Generate report
        report = validator.generate_report()
        
        # Output report
        if args.report:
            with open(args.report, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"Report saved to: {args.report}")
        else:
            print(report)
        
        # Exit with appropriate code
        sys.exit(0 if all_valid else 1)
        
    except KeyboardInterrupt:
        print("\nValidation interrupted by user")
        sys.exit(2)
    except Exception as e:
        logging.error(f"Configuration validation failed: {e}")
        sys.exit(3)


if __name__ == "__main__":
    main()