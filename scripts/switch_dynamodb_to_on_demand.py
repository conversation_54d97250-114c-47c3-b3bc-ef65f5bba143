#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to switch DynamoDB tables to on-demand billing mode.
This eliminates throttling issues for variable workloads.
"""
import boto3
import sys
import time
from typing import List, Optional


def switch_table_to_on_demand(table_name: str, region: str = 'us-west-2') -> bool:
    """
    Switch a DynamoDB table to on-demand billing mode.
    
    Args:
        table_name: Name of the DynamoDB table
        region: AWS region (default: us-west-2)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        client = boto3.client('dynamodb', region_name=region)
        
        # First, describe the table to check current billing mode
        response = client.describe_table(TableName=table_name)
        current_billing = response['Table'].get('BillingModeSummary', {}).get('BillingMode', 'PROVISIONED')
        
        if current_billing == 'PAY_PER_REQUEST':
            print(f"✓ Table '{table_name}' is already in on-demand mode")
            return True
        
        # Get current capacity for reporting
        provisioned = response['Table'].get('ProvisionedThroughput', {})
        read_capacity = provisioned.get('ReadCapacityUnits', 'N/A')
        write_capacity = provisioned.get('WriteCapacityUnits', 'N/A')
        
        print(f"Current provisioned capacity for '{table_name}':")
        print(f"  - Read: {read_capacity} units")
        print(f"  - Write: {write_capacity} units")
        print(f"\nSwitching '{table_name}' to on-demand billing mode...")
        
        # Update table to on-demand
        client.update_table(
            TableName=table_name,
            BillingMode='PAY_PER_REQUEST'
        )
        
        # Wait for table to become active
        waiter = client.get_waiter('table_exists')
        print("Waiting for table update to complete...")
        
        # Poll table status
        for i in range(30):  # Max 5 minutes
            time.sleep(10)
            response = client.describe_table(TableName=table_name)
            status = response['Table']['TableStatus']
            billing = response['Table'].get('BillingModeSummary', {}).get('BillingMode', 'PROVISIONED')
            
            if status == 'ACTIVE' and billing == 'PAY_PER_REQUEST':
                print(f"✓ Successfully switched '{table_name}' to on-demand mode!")
                return True
            elif status == 'UPDATING':
                print(f"  Status: {status} (updating billing mode...)")
            else:
                print(f"  Status: {status}, Billing: {billing}")
        
        print(f"✗ Timeout waiting for table update")
        return False
        
    except Exception as e:
        print(f"✗ Error switching table '{table_name}' to on-demand: {e}")
        return False


def main(tables: Optional[List[str]] = None):
    """
    Main function to switch tables to on-demand mode.
    
    Args:
        tables: List of table names to switch. If None, uses default tables.
    """
    if not tables:
        # Default LexGenius tables
        tables = ['Pacer', 'FBAdArchive', 'LawFirms', 'FBImageHash', 'DistrictCourts']
    
    print("=== DynamoDB On-Demand Billing Mode Switch ===\n")
    print("This script will switch the following tables to on-demand billing mode:")
    for table in tables:
        print(f"  - {table}")
    
    print("\nOn-demand billing benefits:")
    print("  ✓ No throttling errors")
    print("  ✓ Handles traffic spikes automatically")
    print("  ✓ Pay only for actual usage")
    print("  ✓ No capacity planning required")
    
    response = input("\nProceed with switching to on-demand? (yes/no): ")
    if response.lower() not in ['yes', 'y']:
        print("Operation cancelled.")
        return
    
    print("\n" + "="*50 + "\n")
    
    success_count = 0
    for table in tables:
        if switch_table_to_on_demand(table):
            success_count += 1
        print("-" * 50)
    
    print(f"\n=== Summary ===")
    print(f"Successfully switched {success_count}/{len(tables)} tables to on-demand mode.")
    
    if success_count < len(tables):
        print("\n⚠️  Some tables failed to switch. Please check the errors above.")
        sys.exit(1)
    else:
        print("\n✓ All tables successfully switched to on-demand mode!")
        print("\nNote: The application will automatically handle the new billing mode.")
        print("No code changes are required.")


if __name__ == "__main__":
    # Allow specifying tables as command line arguments
    if len(sys.argv) > 1:
        main(sys.argv[1:])
    else:
        main()