#!/usr/bin/env python3
"""
Download Failure Recovery Tool for PACER Services
Addresses download failure complexity identified in troubleshooting analysis.
Provides systematic analysis, recovery, and retry mechanisms for failed downloads.
Uses existing infrastructure for consistent logging.
"""
import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import argparse
import re
from enum import Enum

# Use existing infrastructure
from src.infrastructure.protocols.logger import LoggerProtocol


class FailureCategory(Enum):
    """Categories of download failures."""
    NETWORK_TIMEOUT = "network_timeout"
    AUTHENTICATION = "authentication"
    FILE_NOT_FOUND = "file_not_found"
    BROWSER_CONTEXT = "browser_context"
    PERMISSION_DENIED = "permission_denied"
    SYSTEM_ERROR = "system_error"
    ARTIFACT_MISSING = "artifact_missing"
    JSON_PROCESSING = "json_processing"
    UNKNOWN = "unknown"


@dataclass
class DownloadFailure:
    """Information about a download failure."""
    court_id: str
    docket_num: str
    file_path: str
    error_message: str
    timestamp: str
    category: FailureCategory
    retry_count: int = 0
    artifact_exists: bool = False
    json_exists: bool = False
    can_retry: bool = True
    priority_score: int = 0


@dataclass
class RecoveryStats:
    """Statistics from recovery operations."""
    total_failures: int
    analyzed_failures: int
    recoverable_failures: int
    successful_recoveries: int
    permanent_failures: int
    skipped_ignore_download: int


class DownloadFailureAnalyzer:
    """
    Analyzes download failures and provides recovery strategies.
    Addresses the dual verification pattern complexity in PACER services.
    """
    
    def __init__(self, project_root: Optional[str] = None, logger: Optional[LoggerProtocol] = None):
        self.logger = logger or self._create_default_logger()
        self.project_root = Path(project_root) if project_root else Path.cwd()
        
        # Data directory structure
        self.data_dir = self.project_root / "data"
        
        # Failure patterns for categorization
        self.failure_patterns = self._define_failure_patterns()
        
        # Load ignore_download configuration
        self.ignore_download_config = self._load_ignore_download_config()
    
    def _create_default_logger(self) -> LoggerProtocol:
        """Create a default logger that implements LoggerProtocol."""
        import logging
        
        class DefaultLogger:
            def __init__(self):
                self._logger = logging.getLogger(__name__)
                
            def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.debug(message, extra=extra)
                
            def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.info(message, extra=extra)
                
            def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.warning(message, extra=extra)
                
            def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.error(message, extra=extra)
                
            def exception(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.exception(message, extra=extra)
        
        return DefaultLogger()
    
    def _define_failure_patterns(self) -> Dict[FailureCategory, List[str]]:
        """Define patterns for categorizing failures."""
        return {
            FailureCategory.NETWORK_TIMEOUT: [
                "timeout", "connection", "network", "timed out", "connect failed"
            ],
            FailureCategory.AUTHENTICATION: [
                "authentication", "login", "credentials", "unauthorized", "403"
            ],
            FailureCategory.FILE_NOT_FOUND: [
                "not found", "404", "file not found", "does not exist"
            ],
            FailureCategory.BROWSER_CONTEXT: [
                "target closed", "context closed", "browser", "playwright", "page closed"
            ],
            FailureCategory.PERMISSION_DENIED: [
                "permission denied", "access denied", "forbidden", "401"
            ],
            FailureCategory.SYSTEM_ERROR: [
                "system error", "internal error", "500", "server error"
            ],
            FailureCategory.ARTIFACT_MISSING: [
                "artifact", "missing file", "no such file"
            ],
            FailureCategory.JSON_PROCESSING: [
                "json", "decode", "parsing", "invalid format"
            ]
        }
    
    def _load_ignore_download_config(self) -> Dict[str, Any]:
        """Load ignore_download configuration with fallback."""
        config_paths = [
            self.project_root / "config" / "ignore_download.json",
            self.project_root / "config" / "ignore_download.default.json",
            self.project_root / "data" / "config" / "ignore_download.json"
        ]
        
        for config_path in config_paths:
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    self.logger.info(f"🔧 LOADED CONFIG: {config_path}")
                    return config
                except Exception as e:
                    self.logger.warning(f"⚠️ CONFIG ERROR: {config_path} - {e}")
        
        self.logger.warning("⚠️ NO CONFIG: Using empty ignore_download config")
        return {"courts": {}, "cases": {}}
    
    def categorize_failure(self, error_message: str) -> FailureCategory:
        """Categorize a failure based on error message."""
        error_lower = error_message.lower()
        
        for category, patterns in self.failure_patterns.items():
            for pattern in patterns:
                if pattern in error_lower:
                    return category
        
        return FailureCategory.UNKNOWN
    
    def analyze_failure_logs(self, log_file_paths: List[str]) -> List[DownloadFailure]:
        """Analyze log files to extract download failures."""
        failures = []
        
        self.logger.info(f"🔍 ANALYZING: {len(log_file_paths)} log files")
        
        for log_path in log_file_paths:
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                
                # Extract failures using patterns
                extracted_failures = self._extract_failures_from_log(log_content, log_path)
                failures.extend(extracted_failures)
                
                self.logger.info(f"📋 EXTRACTED: {len(extracted_failures)} failures from {log_path}")
                
            except Exception as e:
                self.logger.error(f"❌ LOG READ ERROR: {log_path} - {e}")
        
        # Deduplicate and enrich failures
        unique_failures = self._deduplicate_failures(failures)
        enriched_failures = self._enrich_failures(unique_failures)
        
        self.logger.info(f"📊 ANALYSIS COMPLETE: {len(enriched_failures)} unique failures")
        return enriched_failures
    
    def _extract_failures_from_log(self, log_content: str, log_path: str) -> List[DownloadFailure]:
        """Extract failure information from log content."""
        failures = []
        
        # Pattern for PACER download failures
        failure_patterns = [
            r'ERROR.*\[([A-Z]+)\].*docket[_\s]*num[:\s]*([^\s]+).*error[:\s]*(.+)',
            r'FAILED.*court[_\s]*id[:\s]*([A-Z]+).*docket[:\s]*([^\s]+).*error[:\s]*(.+)',
            r'Download.*failed.*\[([A-Z]+)\].*([^\s]+).*error[:\s]*(.+)',
        ]
        
        for pattern in failure_patterns:
            matches = re.finditer(pattern, log_content, re.IGNORECASE | re.MULTILINE)
            
            for match in matches:
                try:
                    court_id = match.group(1).upper()
                    docket_num = match.group(2)
                    error_message = match.group(3).strip()
                    
                    # Extract timestamp (look for nearby timestamp)
                    timestamp = self._extract_timestamp_near_match(log_content, match.start())
                    
                    failure = DownloadFailure(
                        court_id=court_id,
                        docket_num=docket_num,
                        file_path=log_path,
                        error_message=error_message,
                        timestamp=timestamp,
                        category=self.categorize_failure(error_message)
                    )
                    
                    failures.append(failure)
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ PARSE ERROR: {e}")
        
        return failures
    
    def _extract_timestamp_near_match(self, content: str, match_position: int) -> str:
        """Extract timestamp near a regex match position."""
        # Look for timestamp patterns around the match
        start_search = max(0, match_position - 200)
        end_search = min(len(content), match_position + 200)
        search_area = content[start_search:end_search]
        
        # Common log timestamp patterns
        timestamp_patterns = [
            r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',
            r'\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}'
        ]
        
        for pattern in timestamp_patterns:
            match = re.search(pattern, search_area)
            if match:
                return match.group(0)
        
        return datetime.now().isoformat()
    
    def _deduplicate_failures(self, failures: List[DownloadFailure]) -> List[DownloadFailure]:
        """Remove duplicate failures based on court_id and docket_num."""
        seen = set()
        unique_failures = []
        
        for failure in failures:
            key = (failure.court_id, failure.docket_num)
            if key not in seen:
                seen.add(key)
                unique_failures.append(failure)
            else:
                # Update retry count for duplicates
                for existing in unique_failures:
                    if existing.court_id == failure.court_id and existing.docket_num == failure.docket_num:
                        existing.retry_count += 1
                        break
        
        return unique_failures
    
    def _enrich_failures(self, failures: List[DownloadFailure]) -> List[DownloadFailure]:
        """Enrich failures with additional metadata."""
        for failure in failures:
            # Check if artifacts exist (PDF/ZIP files)
            failure.artifact_exists = self._check_artifact_exists(failure.court_id, failure.docket_num)
            
            # Check if JSON metadata exists
            failure.json_exists = self._check_json_exists(failure.court_id, failure.docket_num)
            
            # Determine if retry is possible
            failure.can_retry = self._can_retry_failure(failure)
            
            # Calculate priority score
            failure.priority_score = self._calculate_priority_score(failure)
        
        return failures
    
    def _check_artifact_exists(self, court_id: str, docket_num: str) -> bool:
        """Check if artifact files (PDF/ZIP) exist for a case."""
        # Search in data directory structure: data/YYYY/MM/DD/court_id/
        for year_dir in self.data_dir.glob("20*"):
            for month_dir in year_dir.glob("*"):
                for day_dir in month_dir.glob("*"):
                    court_dir = day_dir / court_id
                    if court_dir.exists():
                        # Look for files matching the docket number
                        artifacts = list(court_dir.glob(f"*{docket_num}*.pdf")) + \
                                  list(court_dir.glob(f"*{docket_num}*.zip"))
                        if artifacts:
                            return True
        return False
    
    def _check_json_exists(self, court_id: str, docket_num: str) -> bool:
        """Check if JSON metadata exists for a case."""
        for year_dir in self.data_dir.glob("20*"):
            for month_dir in year_dir.glob("*"):
                for day_dir in month_dir.glob("*"):
                    court_dir = day_dir / court_id
                    if court_dir.exists():
                        json_files = list(court_dir.glob(f"*{docket_num}*.json"))
                        if json_files:
                            return True
        return False
    
    def _can_retry_failure(self, failure: DownloadFailure) -> bool:
        """Determine if a failure can be retried."""
        # Check ignore_download configuration
        if self._is_ignore_download_case(failure.court_id, failure.docket_num):
            return False
        
        # Check failure category
        non_retryable_categories = {
            FailureCategory.FILE_NOT_FOUND,
            FailureCategory.PERMISSION_DENIED
        }
        
        if failure.category in non_retryable_categories:
            return False
        
        # Check retry count
        if failure.retry_count > 3:
            return False
        
        # If artifact exists, may not need retry
        if failure.artifact_exists:
            return False
        
        return True
    
    def _is_ignore_download_case(self, court_id: str, docket_num: str) -> bool:
        """Check if case is in ignore_download configuration."""
        courts_config = self.ignore_download_config.get("courts", {})
        court_cases = courts_config.get(court_id, {})
        
        if isinstance(court_cases, dict):
            return docket_num in court_cases
        elif isinstance(court_cases, list):
            return docket_num in court_cases
        
        # Check global cases
        global_cases = self.ignore_download_config.get("cases", {})
        return docket_num in global_cases
    
    def _calculate_priority_score(self, failure: DownloadFailure) -> int:
        """Calculate priority score for recovery (higher = more important)."""
        score = 0
        
        # Base score by category
        category_scores = {
            FailureCategory.NETWORK_TIMEOUT: 80,
            FailureCategory.BROWSER_CONTEXT: 70,
            FailureCategory.AUTHENTICATION: 60,
            FailureCategory.SYSTEM_ERROR: 50,
            FailureCategory.ARTIFACT_MISSING: 40,
            FailureCategory.JSON_PROCESSING: 30,
            FailureCategory.FILE_NOT_FOUND: 20,
            FailureCategory.PERMISSION_DENIED: 10,
            FailureCategory.UNKNOWN: 25
        }
        
        score += category_scores.get(failure.category, 25)
        
        # Adjust for retry count (fewer retries = higher priority)
        score += max(0, 20 - (failure.retry_count * 5))
        
        # Boost if no artifact exists
        if not failure.artifact_exists:
            score += 30
        
        # Reduce if JSON exists (partial success)
        if failure.json_exists:
            score -= 10
        
        return max(0, score)
    
    def generate_recovery_plan(self, failures: List[DownloadFailure]) -> Dict[str, Any]:
        """Generate a comprehensive recovery plan."""
        recoverable_failures = [f for f in failures if f.can_retry]
        
        # Sort by priority score
        recoverable_failures.sort(key=lambda x: x.priority_score, reverse=True)
        
        # Group by category
        by_category = {}
        for failure in recoverable_failures:
            category = failure.category.value
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(failure)
        
        # Generate statistics
        stats = RecoveryStats(
            total_failures=len(failures),
            analyzed_failures=len(failures),
            recoverable_failures=len(recoverable_failures),
            successful_recoveries=0,
            permanent_failures=len([f for f in failures if not f.can_retry]),
            skipped_ignore_download=len([f for f in failures if self._is_ignore_download_case(f.court_id, f.docket_num)])
        )
        
        return {
            "recovery_plan": {
                "total_failures": len(failures),
                "recoverable_failures": len(recoverable_failures),
                "by_category": {k: len(v) for k, v in by_category.items()},
                "top_priority": recoverable_failures[:10] if recoverable_failures else []
            },
            "statistics": asdict(stats),
            "recommendations": self._generate_recommendations(by_category),
            "recovery_commands": self._generate_recovery_commands(recoverable_failures)
        }
    
    def _generate_recommendations(self, failures_by_category: Dict[str, List[DownloadFailure]]) -> List[str]:
        """Generate actionable recommendations based on failure patterns."""
        recommendations = []
        
        for category, failures in failures_by_category.items():
            count = len(failures)
            
            if category == FailureCategory.NETWORK_TIMEOUT.value and count > 5:
                recommendations.append(
                    f"🔧 NETWORK: {count} timeout failures - Consider increasing timeout values in config"
                )
            
            if category == FailureCategory.BROWSER_CONTEXT.value and count > 3:
                recommendations.append(
                    f"🔧 BROWSER: {count} context failures - Implement browser context monitoring"
                )
            
            if category == FailureCategory.AUTHENTICATION.value and count > 2:
                recommendations.append(
                    f"🔧 AUTH: {count} authentication failures - Check PACER credentials"
                )
            
            if category == FailureCategory.ARTIFACT_MISSING.value and count > 10:
                recommendations.append(
                    f"🔧 ARTIFACTS: {count} missing artifacts - Run artifact verification sweep"
                )
        
        if not recommendations:
            recommendations.append("✅ No systematic issues detected in failure patterns")
        
        return recommendations
    
    def _generate_recovery_commands(self, recoverable_failures: List[DownloadFailure]) -> List[str]:
        """Generate specific recovery commands."""
        commands = []
        
        # Group by court for batch processing
        by_court = {}
        for failure in recoverable_failures:
            if failure.court_id not in by_court:
                by_court[failure.court_id] = []
            by_court[failure.court_id].append(failure.docket_num)
        
        for court_id, docket_nums in by_court.items():
            if len(docket_nums) == 1:
                commands.append(
                    f"python src/main.py --params config/scrape.yml --court {court_id} --case {docket_nums[0]} --reprocess-failed"
                )
            else:
                docket_list = ",".join(docket_nums[:5])  # Limit to 5 per command
                commands.append(
                    f"python src/main.py --params config/scrape.yml --court {court_id} --cases {docket_list} --reprocess-failed"
                )
        
        return commands[:20]  # Limit to 20 commands
    
    def export_failures_csv(self, failures: List[DownloadFailure], output_path: str) -> None:
        """Export failures to CSV for analysis."""
        import csv
        
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'court_id', 'docket_num', 'error_message', 'timestamp', 
                'category', 'retry_count', 'artifact_exists', 'json_exists',
                'can_retry', 'priority_score'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for failure in failures:
                writer.writerow({
                    'court_id': failure.court_id,
                    'docket_num': failure.docket_num,
                    'error_message': failure.error_message,
                    'timestamp': failure.timestamp,
                    'category': failure.category.value,
                    'retry_count': failure.retry_count,
                    'artifact_exists': failure.artifact_exists,
                    'json_exists': failure.json_exists,
                    'can_retry': failure.can_retry,
                    'priority_score': failure.priority_score
                })
        
        self.logger.info(f"📊 EXPORTED: {len(failures)} failures to {output_path}")


def main():
    """Main CLI interface for download failure recovery."""
    parser = argparse.ArgumentParser(description="PACER Download Failure Recovery Tool")
    parser.add_argument("--project-root", help="Project root directory")
    parser.add_argument("--log-files", nargs="+", help="Log files to analyze")
    parser.add_argument("--log-dir", help="Directory containing log files")
    parser.add_argument("--output", help="Output report file")
    parser.add_argument("--csv", help="Export failures to CSV file")
    parser.add_argument("--top", type=int, default=20, help="Show top N failures")
    parser.add_argument("--category", choices=[c.value for c in FailureCategory], 
                       help="Filter by failure category")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    try:
        # Create logger that implements LoggerProtocol
        import logging
        
        class CLILogger:
            def __init__(self):
                self._logger = logging.getLogger(__name__)
                
            def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.debug(message, extra=extra)
                
            def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.info(message, extra=extra)
                
            def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.warning(message, extra=extra)
                
            def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.error(message, extra=extra)
                
            def exception(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
                self._logger.exception(message, extra=extra)
        
        cli_logger = CLILogger()
        analyzer = DownloadFailureAnalyzer(args.project_root, logger=cli_logger)
        
        # Determine log files to analyze
        log_files = []
        if args.log_files:
            log_files.extend(args.log_files)
        elif args.log_dir:
            log_dir = Path(args.log_dir)
            log_files.extend([str(f) for f in log_dir.glob("*.log")])
        else:
            # Default log locations
            default_log_paths = [
                "logs/scraper.log",
                "logs/pacer.log", 
                "logs/application.log"
            ]
            log_files = [f for f in default_log_paths if Path(f).exists()]
        
        if not log_files:
            print("No log files found to analyze")
            sys.exit(1)
        
        # Analyze failures
        failures = analyzer.analyze_failure_logs(log_files)
        
        # Filter by category if specified
        if args.category:
            failures = [f for f in failures if f.category.value == args.category]
        
        # Generate recovery plan
        recovery_plan = analyzer.generate_recovery_plan(failures)
        
        # Display results
        print("=" * 80)
        print("PACER DOWNLOAD FAILURE ANALYSIS REPORT")
        print("=" * 80)
        print(f"Generated: {datetime.now().isoformat()}")
        print(f"Log files analyzed: {len(log_files)}")
        print()
        
        stats = recovery_plan["statistics"]
        print("SUMMARY:")
        print(f"  📋 Total failures: {stats['total_failures']}")
        print(f"  🔄 Recoverable: {stats['recoverable_failures']}")
        print(f"  ❌ Permanent: {stats['permanent_failures']}")
        print(f"  ⏭️ Ignored (config): {stats['skipped_ignore_download']}")
        print()
        
        print("FAILURE CATEGORIES:")
        for category, count in recovery_plan["recovery_plan"]["by_category"].items():
            print(f"  {category}: {count}")
        print()
        
        print("RECOMMENDATIONS:")
        for rec in recovery_plan["recommendations"]:
            print(f"  {rec}")
        print()
        
        print(f"TOP {args.top} PRIORITY FAILURES:")
        top_failures = recovery_plan["recovery_plan"]["top_priority"][:args.top]
        for i, failure_dict in enumerate(top_failures, 1):
            failure = DownloadFailure(**failure_dict)
            print(f"  {i:2d}. [{failure.court_id}] {failure.docket_num} - {failure.category.value} (score: {failure.priority_score})")
        print()
        
        print("RECOVERY COMMANDS:")
        for cmd in recovery_plan["recovery_commands"][:10]:
            print(f"  {cmd}")
        print()
        
        # Export CSV if requested
        if args.csv:
            analyzer.export_failures_csv(failures, args.csv)
        
        # Save full report if requested
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(recovery_plan, f, indent=2, default=str)
            print(f"Full report saved to: {args.output}")
        
        # Exit with appropriate code
        recoverable_count = recovery_plan["statistics"]["recoverable_failures"]
        sys.exit(0 if recoverable_count == 0 else 1)
        
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user")
        sys.exit(2)
    except Exception as e:
        logging.error(f"Download failure analysis failed: {e}")
        sys.exit(3)


if __name__ == "__main__":
    main()