#!/usr/bin/env python3
"""
Process Review Cases Script

This script processes review cases collected during PACER scraping. It loads cases from
the review_cases_all.json file for a specific date, filters them based on various criteria,
and allows the user to review and select which cases to keep. The final list is saved to
review_cases_final.json.

Usage:
    python -m src.scripts.process_review_cases --date YYYYMMDD
    python -m src.scripts.process_review_cases --log-level INFO --date YYYYMMDD
    python -m src.scripts.process_review_cases --date YYYYMMDD --dicts
    python -m src.scripts.process_review_cases --date YYYYMMDD --filter-date 20240101

Arguments:
    --date: Date in YYYYMMDD format (e.g., 20240601 for June 1, 2024)
    --log-level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    --dicts: If present, save the final cases as a list of dictionaries instead of list of lists.
    --filter-date: Filter out cases with filing dates earlier than this date (YYYYMMDD format)
"""

import logging
import os
import json
import hashlib
from datetime import datetime
import sys
import argparse
import asyncio

# Add the project root to the Python path if running as a script
if __name__ == "__main__":
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from rich.console import Console
from rich.progress import track
from rich.table import Table
from rich.panel import Panel
from rich.syntax import Syntax
from rich.pretty import Pretty
from rich.text import Text
from rich.columns import Columns

console = Console()
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
DATA_PATH = os.path.join(PROJECT_ROOT, 'data')


def get_valid_date(date_str):
    """Validate date format and return the logs directory path."""
    if not date_str:
        console.print("[red]Error: Date is required. Use --date YYYYMMDD[/red]")
        sys.exit(1)

    try:
        # Validate date format
        datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
        console.print(f"[red]Error: Invalid date format '{date_str}'. Use YYYYMMDD format.[/red]")
        sys.exit(1)

    # Construct the logs directory path
    logs_path = os.path.join(DATA_PATH, date_str, 'logs')
    return logs_path


def setup_logging(log_level):
    """Setup logging configuration."""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        console.print(f"[red]Invalid log level: {log_level}[/red]")
        sys.exit(1)

    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_json_files(directory):
    """Load and combine all JSON files from the directory, flattening nested structures."""
    all_cases = []
    case_metadata = {}  # Track original structure for each case
    
    # Try new filename first, then legacy filename
    file_path = os.path.join(directory, "review_cases_all.json")
    legacy_file_path = os.path.join(directory, "irrelevant_cases_all.json")
    
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                data = json.load(f)
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            # Check if this is a grouped structure with review_cases array
                            if 'review_cases' in item and isinstance(item['review_cases'], list):
                                court_id = item.get('court_id')
                                # Extract individual cases from the nested array
                                for case in item['review_cases']:
                                    if isinstance(case, dict):
                                        # Mark this case as originally grouped
                                        case_key = f"{case.get('court_id')}:{case.get('docket_num')}"
                                        case_metadata[case_key] = {'was_grouped': True, 'group_court_id': court_id}
                                        all_cases.append(case)
                            else:
                                # This is a flat case structure
                                case_key = f"{item.get('court_id')}:{item.get('docket_num')}"
                                case_metadata[case_key] = {'was_grouped': False}
                                all_cases.append(item)
                else:
                    all_cases.append(data)  # Should ideally be a list, but handle single object
        elif os.path.exists(legacy_file_path):
            console.print(f"[yellow]Using legacy file: {legacy_file_path}[/yellow]")
            with open(legacy_file_path, 'r') as f:
                data = json.load(f)
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            case_key = f"{item.get('court_id')}:{item.get('docket_num')}"
                            case_metadata[case_key] = {'was_grouped': False}
                            all_cases.append(item)
                else:
                    all_cases.append(data)
        else:
            console.print(f"[red]File not found: {file_path} or {legacy_file_path}[/red]")
    except json.JSONDecodeError:
        console.print(f"[red]Error reading JSON file. Skipping...[/red]")
    except Exception as exc:
        console.print(f"[red]Unexpected error reading file: {str(exc)}[/red]")

    return all_cases, case_metadata


def deduplicate_cases(cases):
    """Remove duplicate cases based on court_id and docket_num, then remove truly identical cases."""
    
    # First pass: Remove duplicates based on court_id and docket_num (case-insensitive)
    seen_keys = set()
    unique_by_key = []
    for case in cases:
        if isinstance(case, dict):
            court_id = str(case.get('court_id', '')).lower()
            docket_num = str(case.get('docket_num', '')).lower()
            key = (court_id, docket_num)
            if key not in seen_keys:
                seen_keys.add(key)
                unique_by_key.append(case)
        else:
            # Handle non-dict cases (shouldn't happen, but be safe)
            unique_by_key.append(case)
    
    # Second pass: Remove truly identical cases (all fields match)
    seen_hashes = set()
    unique_cases = []
    for case in unique_by_key:
        if isinstance(case, dict):
            # Create a hash of all the case data to identify truly identical cases
            case_str = json.dumps(case, sort_keys=True, default=str)
            case_hash = hashlib.md5(case_str.encode()).hexdigest()
            if case_hash not in seen_hashes:
                seen_hashes.add(case_hash)
                unique_cases.append(case)
        else:
            unique_cases.append(case)
    
    return unique_cases


def filter_excluded_keywords(cases):
    """Filter out cases containing excluded keywords in versus field."""
    excluded_keywords = [
        'kroger', 'elevator', 'amazon.com', 'walmart', 'wal-mart', 'target',
        'cvs', 'walgreens', "lowe's", 'united states marshall service',
        'fbi', 'federal bureau of investigation', 'quicktrip', 'albertsons',
        'norwegian cruise lines', 'carnival corporation', 'fiesta mart',
        'pilot travel centers', 'princess cruise lines', 'speedway', 'publix',
        'royal caribbean', 'johnson & johnson', "sam's east", "racetrac",
        'burlington coat', 'brookshire grocery', 'safeway', 'dgl group',
        'dollar general', 'royal caribean', "ollie's bargain outlet",
        'hillstone restaurant group', 'universal city studios', 'costco wholesale',
        'airbnb', 'home depot', 'associated wholesale grocers'
    ]
    
    # Additional names to filter out case-insensitively
    excluded_names = []
    
    filtered_cases = []
    for case in cases:
        if isinstance(case, dict):
            versus = str(case.get('versus', '')).lower()
            # Check both excluded keywords and excluded names
            if (not any(keyword.lower() in versus for keyword in excluded_keywords) and 
                not any(name.lower() in versus for name in excluded_names)):
                filtered_cases.append(case)
        else:
            filtered_cases.append(case)
    
    return filtered_cases


def filter_excluded_causes(cases):
    """Filter out cases containing excluded causes."""
    excluded_causes = [
        '23:134 P.I.- Auto Negligence',
        '23:134 PI- Auto Negligence',
        '23:134 Personal Injury- Auto Negligence',
        '23:134 Personal Injury - Auto Negligence'
    ]
    
    filtered_cases = []
    for case in cases:
        if isinstance(case, dict):
            cause = str(case.get('cause', ''))
            # Check if cause matches any excluded causes (case-insensitive)
            if not any(excluded_cause.lower() == cause.lower() for excluded_cause in excluded_causes):
                filtered_cases.append(case)
        else:
            filtered_cases.append(case)
    
    return filtered_cases


def filter_by_filing_date(cases, filter_date):
    """Filter out cases with filing dates earlier than the specified date.
    
    Args:
        cases: List of case dictionaries
        filter_date: Date string in YYYYMMDD format
    
    Returns:
        List of cases with filing dates on or after the filter date
    """
    if not filter_date:
        return cases
    
    filtered_cases = []
    for case in cases:
        if isinstance(case, dict):
            filing_date = case.get('filing_date', '')
            # Convert filing_date from MM/DD/YY format to YYYYMMDD if needed
            if filing_date:
                # Try to parse different date formats
                converted_date = None
                if '/' in filing_date:
                    # Handle MM/DD/YY format
                    try:
                        from datetime import datetime
                        parsed_date = datetime.strptime(filing_date, '%m/%d/%y')
                        converted_date = parsed_date.strftime('%Y%m%d')
                    except ValueError:
                        try:
                            # Try MM/DD/YYYY format
                            parsed_date = datetime.strptime(filing_date, '%m/%d/%Y')
                            converted_date = parsed_date.strftime('%Y%m%d')
                        except ValueError:
                            pass
                elif len(filing_date) == 8 and filing_date.isdigit():
                    # Already in YYYYMMDD format
                    converted_date = filing_date
                
                # Only include if filing date is >= filter date
                if converted_date and converted_date >= filter_date:
                    filtered_cases.append(case)
            else:
                # Exclude cases without filing dates when filter is applied
                pass
        else:
            filtered_cases.append(case)
    
    return filtered_cases


async def filter_existing_dockets(cases, pacer_repository):
    """Filter out cases that already exist in the database."""
    filtered_cases = []
    
    for case in track(cases, description="Checking existing dockets..."):
        if isinstance(case, dict):
            court_id = case.get('court_id')
            docket_num = case.get('docket_num')
            
            if court_id and docket_num:
                try:
                    results = await pacer_repository.check_docket_exists(court_id, docket_num)
                    if not results:
                        filtered_cases.append(case)
                except Exception as e:
                    console.print(f"[yellow]Warning: Could not check existence for {court_id}:{docket_num}: {e}[/yellow]")
                    filtered_cases.append(case)  # Include if we can't check
            else:
                filtered_cases.append(case)  # Include if missing required fields
        else:
            filtered_cases.append(case)
    
    return filtered_cases


def save_cases_with_structure(final_cases, case_metadata, output_path):
    """Save cases maintaining the original structure (grouped vs flat)."""
    output_data = []
    grouped_cases = {}  # court_id -> list of cases
    
    for case in final_cases:
        case_key = f"{case.get('court_id')}:{case.get('docket_num')}"
        metadata = case_metadata.get(case_key, {'was_grouped': False})
        
        if metadata['was_grouped']:
            # This case was originally in a grouped structure
            court_id = case.get('court_id')
            if court_id not in grouped_cases:
                grouped_cases[court_id] = []
            grouped_cases[court_id].append(case)
        else:
            # This case was originally flat
            output_data.append(case)
    
    # Add grouped cases to output
    for court_id, cases in grouped_cases.items():
        output_data.append({
            'court_id': court_id,
            'review_cases': cases
        })
    
    # Save to file
    with open(output_path, 'w') as f:
        json.dump(output_data, f, indent=4)


async def main(args, config):
    """Main function to process review cases for a specific date.

    This function processes review cases by:
    1. Validating the date and finding the corresponding logs directory
    2. Loading review cases from JSON files
    3. Removing duplicates and filtering by excluded keywords
    4. Checking if cases already exist in the database
    5. Allowing user to review and select cases to keep
    6. Saving the final list of cases (as dicts) to a JSON file

    Args:
        args: Command line arguments including date and log level
        config: Application configuration
    """
    date_path = get_valid_date(args.date)
    date_str = os.path.basename(os.path.dirname(date_path))  # YYYYMMDD
    console.print(f"[cyan]Processing review cases for date: {date_str}[/cyan]")

    if not os.path.isdir(date_path):
        console.print(f"[red]Logs directory not found: {date_path}[/red]")
        sys.exit(1)

    # Initialize async storage and repository using context manager
    logger = logging.getLogger(__name__)
    async with AsyncDynamoDBStorage(config, logger) as storage:
        pacer_repository = PacerRepository(storage)
        
        console.print("[cyan]Loading review cases from 'review_cases_all.json'...[/cyan]")
        all_cases, case_metadata = load_json_files(date_path)
        console.print(f"[green]Total cases loaded: {len(all_cases)}[/green]")
        if not all_cases:
            console.print("[yellow]No cases to process. Exiting.[/yellow]")
            return

        console.print("[cyan]Removing duplicates...[/cyan]")
        unique_cases = deduplicate_cases(all_cases)
        console.print(f"[green]Cases after deduplication: {len(unique_cases)}[/green]")

        console.print("[cyan]Filtering by excluded keywords...[/cyan]")
        filtered_by_keywords = filter_excluded_keywords(unique_cases)
        console.print(f"[green]Cases after keyword filtering: {len(filtered_by_keywords)}[/green]")

        console.print("[cyan]Filtering by excluded causes...[/cyan]")
        filtered_by_causes = filter_excluded_causes(filtered_by_keywords)
        console.print(f"[green]Cases after cause filtering: {len(filtered_by_causes)}[/green]")

        # Apply filing date filter if specified
        if args.filter_date:
            console.print(f"[cyan]Filtering by filing date >= {args.filter_date}...[/cyan]")
            filtered_by_date = filter_by_filing_date(filtered_by_causes, args.filter_date)
            console.print(f"[green]Cases after filing date filtering: {len(filtered_by_date)}[/green]")
        else:
            filtered_by_date = filtered_by_causes

        cases_to_review = await filter_existing_dockets(filtered_by_date, pacer_repository)
        console.print(f"[green]Cases after filtering existing dockets: {len(cases_to_review)}[/green]")

        if not cases_to_review:
            console.print("[yellow]No cases need review after filtering. Exiting.[/yellow]")
            return

        final_cases = []
        total_to_review = len(cases_to_review)

        for idx, case in enumerate(cases_to_review, 1):
            console.rule(f"[yellow]Reviewing Case {idx} of {total_to_review}[/yellow]")

            if isinstance(case, dict):
                # Create main case info panel
                case_info = []
                
                # Primary identifiers
                court_id = case.get('court_id', 'N/A')
                docket_num = case.get('docket_num', 'N/A')
                versus = case.get('versus', 'N/A')
                
                case_info.append(f"[bold green]Court ID:[/bold green] {court_id}")
                case_info.append(f"[bold green]Docket #:[/bold green] {docket_num}")
                case_info.append(f"[bold cyan]Case:[/bold cyan] {versus}")
                
                # Dates
                if 'filing_date' in case:
                    case_info.append(f"[yellow]Filing Date:[/yellow] {case['filing_date']}")
                if 'added_date_iso' in case:
                    case_info.append(f"[yellow]Added Date:[/yellow] {case['added_date_iso']}")
                
                # Create main info panel
                info_panel = Panel(
                    "\n".join(case_info),
                    title="[bold]Case Information[/bold]",
                    border_style="blue"
                )
                console.print(info_panel)
                
                # Review reason - highlight this prominently
                if '_reason_review' in case and case['_reason_review']:
                    reason_panel = Panel(
                        Text(case['_reason_review'], style="bold yellow"),
                        title="[bold red]Reason for Review[/bold red]",
                        border_style="red",
                        padding=(1, 2)
                    )
                    console.print(reason_panel)
                
                # Handle complex fields
                complex_fields = []
                
                # Defendants
                if 'defendants' in case and case['defendants']:
                    if isinstance(case['defendants'], list):
                        defendant_list = []
                        for d in case['defendants']:
                            if isinstance(d, dict) and 'name' in d:
                                defendant_list.append(f"• {d['name']}")
                            else:
                                defendant_list.append(f"• {str(d)}")
                        if defendant_list:
                            complex_fields.append(Panel(
                                "\n".join(defendant_list),
                                title="Defendants",
                                border_style="cyan"
                            ))
                
                # Attorneys
                if 'attorneys' in case and case['attorneys']:
                    if isinstance(case['attorneys'], list):
                        attorney_list = [f"• {str(a)}" for a in case['attorneys']]
                        complex_fields.append(Panel(
                            "\n".join(attorney_list),
                            title="Attorneys",
                            border_style="cyan"
                        ))
                
                # Display complex fields in columns
                if complex_fields:
                    console.print(Columns(complex_fields, equal=True, expand=True))
                
                # Other important fields
                other_info = []
                important_fields = ['cause', 'nos', 'law_firm', 'jurisdiction', 'docket_link']
                for field in important_fields:
                    if field in case and case[field]:
                        field_name = field.replace('_', ' ').title()
                        other_info.append(f"[cyan]{field_name}:[/cyan] {case[field]}")
                
                if other_info:
                    console.print(Panel(
                        "\n".join(other_info),
                        title="Additional Information",
                        border_style="dim"
                    ))
                
                # Show any remaining fields as JSON (excluding already displayed ones)
                displayed_fields = {'court_id', 'docket_num', 'versus', 'filing_date', 'added_date_iso',
                                  '_reason_review', 'defendants', 'attorneys', 'cause', 'nos', 
                                  'law_firm', 'jurisdiction', 'docket_link', 'defendant'}
                
                remaining = {k: v for k, v in case.items() 
                           if k not in displayed_fields and v is not None and v != ''}
                
                if remaining:
                    console.print(Panel(
                        Pretty(remaining, expand_all=True),
                        title="Other Fields",
                        border_style="dim"
                    ))
            else:
                console.print(Panel(
                    "[red]Error: Case data is not in the expected dictionary format.[/red]",
                    border_style="red"
                ))

            # Add a visual separator before the prompt
            console.print()
            response = console.input("[bold yellow]➤ Keep this case? (Y/N/Q to quit) [N]: [/bold yellow]").strip().upper()
            if response == "Q":
                break
            elif response == "Y":
                # Check if this is a removal case that needs a link number
                if isinstance(case, dict) and (case.get('is_removal') or case.get('_gpt_removal_na')):
                    console.print("\n[cyan]This is a removal case.[/cyan]")
                    link_num = console.input("[yellow]Enter the attachment link number to download (e.g., 1, 2, 3): [/yellow]").strip()
                    
                    # Validate the input is a number
                    if link_num.isdigit():
                        case['is_removal_case'] = True
                        case['removal_link_number'] = link_num
                        console.print(f"[green]Link number {link_num} saved for this removal case.[/green]")
                    else:
                        console.print("[red]Invalid input. Link number must be a digit. Case will be saved without link number.[/red]")
                        case['is_removal_case'] = True
                        case['removal_link_number'] = None
                
                final_cases.append(case)

        # Default output filename reflects the dict structure
        output_filename = "review_cases_final.json"
        output_path = os.path.join(date_path, output_filename)

        if final_cases:
            save_response = console.input(
                f"\n[yellow]Save the {len(final_cases)} selected cases to '{output_filename}'? (Y/N) [Y]:[/yellow] ").strip().upper() or "Y"
            if save_response != "N":
                save_cases_with_structure(final_cases, case_metadata, output_path)
                console.print(f"[green]Selected cases saved to {output_path}[/green]")
                console.print(f"[green]Saved {len(final_cases)} cases.[/green]")
            else:
                console.print("[yellow]File save canceled.[/yellow]")
        else:
            console.print(
                f"[yellow]No cases were selected to keep. No output file generated as '{output_filename}'.[/yellow]")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process review cases for a specific date")
    parser.add_argument("--date", required=True, help="Date in YYYYMMDD format")
    parser.add_argument("--log-level", default="INFO", help="Logging level")
    parser.add_argument("--dicts", action="store_true", help="Save as list of dictionaries (default behavior)")
    parser.add_argument("--filter-date", help="Filter out cases with filing dates earlier than this date (YYYYMMDD format)")

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.log_level)
    
    # Validate filter-date format if provided
    if args.filter_date:
        try:
            datetime.strptime(args.filter_date, "%Y%m%d")
        except ValueError:
            console.print(f"[red]Error: Invalid filter-date format '{args.filter_date}'. Use YYYYMMDD format.[/red]")
            sys.exit(1)

    try:
        from src.config_models.loader import load_config

        effective_date_for_config = args.date if args.date else datetime.now().strftime("%Y%m%d")
        try:
            config_date_mmddyy = datetime.strptime(effective_date_for_config, "%Y%m%d").strftime("%m/%d/%y")
        except ValueError:
            logger.critical(f"Invalid effective date for config: {effective_date_for_config}")
            sys.exit(1)

        config = load_config('scraper', {'date': config_date_mmddyy})
    except ImportError:
        logger.critical("Failed to import load_config. Ensure src.lib.config is correct.")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"Failed to load configuration: {e}")
        sys.exit(1)

    asyncio.run(main(args, config))
