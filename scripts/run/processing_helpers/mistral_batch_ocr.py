#!/usr/bin/env python3
"""
ROBUST Mistral Batch OCR Script with comprehensive error handling and recovery

This script provides batch OCR processing of PDF files using Mistral's OCR API with:
- Automatic batch submission and monitoring
- Comprehensive error handling and recovery
- Cost tracking and billing reports
- File deduplication and skip logic
- Recovery of deleted files from existing jobs

Key Features:
1. Batch Processing: Automatically groups PDFs into optimal batch sizes
2. Cost Efficiency: $1 per 2000 pages processed
3. Recovery Mode: Can recover previously processed files without re-payment
4. Progress Tracking: SQLite database tracks all processed files
5. Billing Reports: Track costs across date ranges

Usage Examples:
--------------
# Process new PDFs for a date range
python mistral_batch_ocr.py --start-date 20241210 --end-date 20241212

# Recover deleted MD files from existing Mistral jobs (no additional cost)
python mistral_batch_ocr.py --start-date 20241210 --end-date 20241212 --recover-deleted

# Include previously processed files with missing MDs in new batch
python mistral_batch_ocr.py --start-date 20241210 --end-date 20241212 --include-deleted

# Check status of all batch jobs
python mistral_batch_ocr.py --status

# Show billing report
python mistral_batch_ocr.py --billing --billing-start 20241201 --billing-end 20241231

# Clean up old batches (delete from DB and optionally from API)
python mistral_batch_ocr.py --cleanup 7  # Keep last 7 days

# Recovery mode - check for pending/incomplete jobs
python mistral_batch_ocr.py --recover

Database Schema:
---------------
The script maintains a SQLite database (sqlite/mistral_ocr_tracker.db) with:
- batch_jobs: Tracks all submitted batch jobs
- batch_files: Maps individual PDFs to batch jobs
- completed_files: Records successfully processed PDFs
- billing_log: Tracks costs per job

Recovery Features:
-----------------
1. --recover-deleted: Recovers files that were previously processed but MD files deleted
   - Searches for files marked as completed in DB but missing MD files
   - Lists found files and asks for confirmation
   - Re-extracts OCR text ONLY from local cached results (no API downloads)
   - Does NOT delete any batch files or modify job status
   - No charges incurred - only uses existing local data
   
2. --include-deleted: Includes deleted files in normal processing
   - Adds previously processed files with missing MDs to new batches
   - Useful when you want to reprocess along with new files
   - This WILL incur charges as files are re-submitted to Mistral

Skip Logic:
----------
Files are skipped if:
1. MD file already exists (fastest check)
2. added_on == "00000000" in JSON metadata
3. Marked as completed in database

Environment Requirements:
-----------------------
- MISTRAL_API_KEY: Required for API access
- AWS credentials: Optional for S3 storage integration
- Python packages: mistralai, rich, sqlite3, PyMuPDF (optional)
"""

import argparse
import asyncio
import base64
import json
import logging
import os
import sys
import tempfile
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import sqlite3
import hashlib

from rich.console import Console
from rich.logging import RichHandler

# For PDF page counting
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# For now, disable the new service imports to avoid circular dependencies
# The script will fall back to direct Mistral client usage
MISTRAL_SERVICE_AVAILABLE = False
S3_ASYNC_AVAILABLE = False
MistralService = None
S3AsyncStorage = None

# Note: When circular import issues are resolved, uncomment this:
# try:
#     from src.services.ai.mistral_service import MistralService
#     from src.infrastructure.storage.s3_async import S3AsyncStorage
#     MISTRAL_SERVICE_AVAILABLE = True
#     S3_ASYNC_AVAILABLE = True
# except ImportError as e:
#     print(f"Warning: Could not import new services: {e}")
#     MISTRAL_SERVICE_AVAILABLE = False
#     S3_ASYNC_AVAILABLE = False
#     MistralService = None
#     S3AsyncStorage = None


class RobustBatchTracker:
    """Enhanced SQLite tracker with better error handling and recovery."""
    
    def __init__(self, db_path: str = "sqlite/mistral_ocr_tracker.db"):
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """Initialize database with robust schema."""
        # Ensure the sqlite directory exists
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Enhanced batch jobs table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS batch_jobs (
                    job_id TEXT PRIMARY KEY,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    submitted_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    total_files INTEGER,
                    processed_files INTEGER DEFAULT 0,
                    failed_files INTEGER DEFAULT 0,
                    total_pages INTEGER DEFAULT 0,
                    batch_file_path TEXT,
                    batch_file_hash TEXT,
                    metadata TEXT,
                    last_checked TIMESTAMP,
                    error_count INTEGER DEFAULT 0,
                    last_error TEXT,
                    estimated_cost REAL DEFAULT 0.0
                )
            """)
            
            # Check if we need to add new columns to existing tables
            # Get existing columns
            existing_columns = [row[1] for row in cursor.execute("PRAGMA table_info(batch_jobs)").fetchall()]
            
            # Add missing columns if needed
            if 'total_pages' not in existing_columns:
                cursor.execute("ALTER TABLE batch_jobs ADD COLUMN total_pages INTEGER DEFAULT 0")
                
            if 'estimated_cost' not in existing_columns:
                cursor.execute("ALTER TABLE batch_jobs ADD COLUMN estimated_cost REAL DEFAULT 0.0")
            
            # Files in batches
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS batch_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    job_id TEXT NOT NULL,
                    pdf_path TEXT NOT NULL,
                    custom_id TEXT NOT NULL,
                    file_hash TEXT,
                    status TEXT DEFAULT 'pending',
                    md_path TEXT,
                    processed_at TIMESTAMP,
                    error_message TEXT,
                    FOREIGN KEY (job_id) REFERENCES batch_jobs (job_id),
                    UNIQUE(job_id, pdf_path)
                )
            """)
            
            # Completed MD files
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS completed_files (
                    pdf_path TEXT PRIMARY KEY,
                    md_path TEXT NOT NULL,
                    file_hash TEXT,
                    processed_at TIMESTAMP NOT NULL,
                    job_id TEXT,
                    method TEXT
                )
            """)
            
            # Billing log table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS billing_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    job_id TEXT NOT NULL,
                    date DATE NOT NULL,
                    pages_processed INTEGER NOT NULL,
                    cost REAL NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    FOREIGN KEY (job_id) REFERENCES batch_jobs (job_id)
                )
            """)
            
            # Also check if batch_file_id exists in metadata (for older databases)
            # This is stored in the metadata JSON, so no schema change needed
            
            # Create indexes
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_job_status ON batch_jobs(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_batch_files_status ON batch_files(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_file_hash ON completed_files(file_hash)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_billing_date ON billing_log(date)")
            
            conn.commit()
    
    def add_batch_job(self, job_id: str, batch_file_path: str, metadata: dict) -> None:
        """Add a new batch job with enhanced tracking."""
        # Calculate hash of batch file
        file_hash = self._calculate_file_hash(batch_file_path)
        
        # Extract page count if available
        total_pages = metadata.get('total_pages', 0)
        estimated_cost = (total_pages / 2000.0) if total_pages > 0 else 0.0
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO batch_jobs 
                (job_id, status, created_at, submitted_at, batch_file_path, 
                 batch_file_hash, metadata, total_files, total_pages, estimated_cost)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                job_id, 
                "SUBMITTED", 
                datetime.now().isoformat(),
                datetime.now().isoformat(),
                batch_file_path,
                file_hash,
                json.dumps(metadata),
                metadata.get('total_files', 0),
                total_pages,
                estimated_cost
            ))
            conn.commit()
    
    def add_batch_files(self, job_id: str, files: List[Tuple[str, str]]) -> None:
        """Add files that are part of a batch."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            for pdf_path, custom_id in files:
                file_hash = self._calculate_file_hash(pdf_path) if Path(pdf_path).exists() else None
                cursor.execute("""
                    INSERT OR IGNORE INTO batch_files 
                    (job_id, pdf_path, custom_id, file_hash, status)
                    VALUES (?, ?, ?, ?, 'pending')
                """, (job_id, pdf_path, custom_id, file_hash))
            conn.commit()
    
    def is_file_completed(self, pdf_path: str) -> bool:
        """Check if a PDF has been successfully processed."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            result = cursor.execute("""
                SELECT 1 FROM completed_files WHERE pdf_path = ?
            """, (str(pdf_path),)).fetchone()
            return result is not None
    
    def is_filename_completed(self, pdf_path: str) -> bool:
        """Check if a PDF filename has been successfully processed (regardless of directory)."""
        pdf_filename = Path(pdf_path).name
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            result = cursor.execute("""
                SELECT pdf_path FROM completed_files WHERE pdf_path LIKE ?
            """, (f'%{pdf_filename}',)).fetchone()
            return result is not None
    
    def mark_file_completed(self, pdf_path: str, md_path: str, job_id: str = None, method: str = "batch") -> None:
        """Mark a file as successfully processed."""
        file_hash = self._calculate_file_hash(pdf_path) if Path(pdf_path).exists() else None
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO completed_files 
                (pdf_path, md_path, file_hash, processed_at, job_id, method)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (str(pdf_path), str(md_path), file_hash, datetime.now().isoformat(), job_id, method))
            
            # Update batch_files if job_id provided
            if job_id:
                cursor.execute("""
                    UPDATE batch_files 
                    SET status = 'completed', md_path = ?, processed_at = ?
                    WHERE job_id = ? AND pdf_path = ?
                """, (str(md_path), datetime.now().isoformat(), job_id, str(pdf_path)))
            
            conn.commit()
    
    def get_pending_jobs(self) -> List[Dict]:
        """Get all jobs that need attention."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            results = cursor.execute("""
                SELECT * FROM batch_jobs 
                WHERE status NOT IN ('COMPLETED', 'FAILED', 'CANCELLED')
                   OR (status = 'SUCCESS' AND processed_files < total_files)
                ORDER BY created_at DESC
            """).fetchall()
            return [dict(row) for row in results]
    
    def update_job_status(self, job_id: str, status: str, error_msg: str = None) -> None:
        """Update job status with error tracking."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if error_msg:
                cursor.execute("""
                    UPDATE batch_jobs 
                    SET status = ?, last_checked = ?, error_count = error_count + 1, last_error = ?
                    WHERE job_id = ?
                """, (status, datetime.now().isoformat(), error_msg, job_id))
            else:
                cursor.execute("""
                    UPDATE batch_jobs 
                    SET status = ?, last_checked = ?
                    WHERE job_id = ?
                """, (status, datetime.now().isoformat(), job_id))
            
            conn.commit()
    
    def _calculate_file_hash(self, file_path: str) -> Optional[str]:
        """Calculate SHA256 hash of a file."""
        try:
            if not Path(file_path).exists():
                return None
            
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception:
            return None
    
    def is_job_downloaded(self, job_id: str) -> bool:
        """Check if a job's results have been downloaded."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            result = cursor.execute("""
                SELECT status, processed_files, total_files 
                FROM batch_jobs 
                WHERE job_id = ? AND status = 'COMPLETED'
            """, (job_id,)).fetchone()
            
            if result:
                status, processed, total = result
                # Job is considered downloaded if it's completed and all files are accounted for
                return status == 'COMPLETED' and processed >= total
            return False
    
    def delete_old_jobs(self, before_date: datetime) -> Tuple[int, List[str]]:
        """Delete batch jobs created before the specified date.
        
        Returns:
            Tuple of (number of jobs deleted, list of deleted job IDs)
        """
        deleted_jobs = []
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get jobs to delete
            jobs_to_delete = cursor.execute("""
                SELECT job_id, batch_file_path FROM batch_jobs 
                WHERE created_at < ?
            """, (before_date.isoformat(),)).fetchall()
            
            for job_id, batch_file_path in jobs_to_delete:
                deleted_jobs.append(job_id)
                
                # Delete associated batch files
                cursor.execute("DELETE FROM batch_files WHERE job_id = ?", (job_id,))
                
                # Delete from completed files
                cursor.execute("DELETE FROM completed_files WHERE job_id = ?", (job_id,))
            
            # Delete the batch jobs
            cursor.execute("DELETE FROM batch_jobs WHERE created_at < ?", (before_date.isoformat(),))
            
            conn.commit()
            
        return len(deleted_jobs), deleted_jobs
    
    def log_billing(self, job_id: str, pages_processed: int) -> None:
        """Log billing information for a completed job."""
        cost = pages_processed / 2000.0  # $1 per 2000 pages
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO billing_log (job_id, date, pages_processed, cost, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                job_id,
                datetime.now().date().isoformat(),
                pages_processed,
                cost,
                datetime.now().isoformat()
            ))
            conn.commit()
    
    def get_billing_summary(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict:
        """Get billing summary for a date range."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if start_date and end_date:
                query = """
                    SELECT 
                        COUNT(DISTINCT job_id) as job_count,
                        SUM(pages_processed) as total_pages,
                        SUM(cost) as total_cost,
                        MIN(date) as first_date,
                        MAX(date) as last_date
                    FROM billing_log
                    WHERE date >= ? AND date <= ?
                """
                result = cursor.execute(query, (start_date, end_date)).fetchone()
            else:
                query = """
                    SELECT 
                        COUNT(DISTINCT job_id) as job_count,
                        SUM(pages_processed) as total_pages,
                        SUM(cost) as total_cost,
                        MIN(date) as first_date,
                        MAX(date) as last_date
                    FROM billing_log
                """
                result = cursor.execute(query).fetchone()
            
            if result:
                return dict(result)
            return {
                "job_count": 0,
                "total_pages": 0,
                "total_cost": 0.0,
                "first_date": None,
                "last_date": None
            }


class RobustMistralOCR:
    """Robust Mistral OCR processor with comprehensive error handling."""
    
    def __init__(self, data_dir: str = "data"):
        self.console = Console()
        self.data_dir = Path(data_dir)
        self.tracker = RobustBatchTracker()
        
        # Initialize Mistral service
        api_key = os.environ.get("MISTRAL_API_KEY")
        if not api_key:
            self.console.print("[red]Error: MISTRAL_API_KEY not set[/red]")
            sys.exit(1)
        
        # Initialize S3 storage and MistralService if available
        self.mistral_service = None
        self.client = None
        
        if MISTRAL_SERVICE_AVAILABLE and S3_ASYNC_AVAILABLE and MistralService and S3AsyncStorage:
            try:
                s3_storage = S3AsyncStorage(
                    bucket_name=os.environ.get("AWS_S3_BUCKET", "lexgenius-data"),
                    aws_access_key=os.environ.get("AWS_ACCESS_KEY_ID"),
                    aws_secret_key=os.environ.get("AWS_SECRET_ACCESS_KEY")
                )
                self.mistral_service = MistralService(api_key, s3_storage)
                self.console.print("[green]Using MistralService[/green]")
            except Exception as e:
                self.console.print(f"[yellow]Warning: Could not initialize MistralService: {e}[/yellow]")
                self.mistral_service = None
        
        # Fall back to direct Mistral client if service unavailable
        if self.mistral_service is None:
            try:
                # Use the new Mistral client API
                from mistralai import Mistral
                self.client = Mistral(api_key=api_key)
                self.console.print("[yellow]Using Mistral client fallback[/yellow]")
            except Exception as e:
                self.console.print(f"[red]Error: Could not initialize Mistral client: {e}[/red]")
                sys.exit(1)
        self.setup_logging()
    
    def setup_logging(self):
        """Setup comprehensive logging."""
        log_dir = self.data_dir / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger("robust_mistral_ocr")
        self.logger.setLevel(logging.DEBUG)
        
        # Detailed file logging
        file_handler = logging.FileHandler(log_dir / "robust_ocr.log")
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        
        # Console handler for warnings and above
        console_handler = RichHandler(console=self.console, show_time=False)
        console_handler.setLevel(logging.WARNING)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def recover_all_pending(self):
        """Recover ALL pending batch jobs and unprocessed files."""
        self.console.print("[bold blue]Checking for pending work...[/bold blue]")
        
        # 1. Check for any batch result files on disk
        self._process_orphan_batch_files()
        
        # 2. Check for pending jobs in tracker
        pending_jobs = self.tracker.get_pending_jobs()
        if pending_jobs:
            self.console.print(f"[yellow]Found {len(pending_jobs)} pending jobs in tracker[/yellow]")
            for job in pending_jobs:
                self._recover_job(job)
        
        # 3. Check Mistral API for any SUCCESS jobs
        if self.mistral_service or self.client:
            self._check_mistral_api_jobs()
    
    def _is_job_fully_processed(self, job_id: str) -> bool:
        """Check if a job has been fully downloaded and processed."""
        # Check in the tracker database
        if self.tracker.is_job_downloaded(job_id):
            return True
        
        # Also check if the results file exists in the processed directory
        processed_results_file = Path(f"processed_batch_results/batch_results_{job_id}.jsonl")
        return processed_results_file.exists()
    
    def _process_orphan_batch_files(self):
        """Process any batch result files not in tracker."""
        import glob
        batch_files = glob.glob("batch_results_*.jsonl")
        
        if batch_files:
            self.console.print(f"[yellow]Found {len(batch_files)} batch result files[/yellow]")
            
            for batch_file in batch_files:
                # Extract job_id from filename
                job_id = batch_file.replace("batch_results_", "").replace(".jsonl", "")
                
                # Process the file
                self._process_batch_results_file(Path(batch_file), job_id)
    
    def _check_mistral_api_jobs(self):
        """Check Mistral API for completed jobs."""
        try:
            self.console.print("[dim]Checking Mistral API for completed jobs...[/dim]")
            
            # Use direct HTTP API for batch jobs listing
            import requests
            api_key = os.environ.get("MISTRAL_API_KEY")
            headers = {"Authorization": f"Bearer {api_key}"}
            
            response = requests.get(
                "https://api.mistral.ai/v1/batch/jobs",
                headers=headers
            )
            
            if response.status_code != 200:
                raise Exception(f"Failed to list jobs: {response.status_code} {response.text}")
            
            jobs_data = response.json()
            jobs = jobs_data.get("data", [])
            
            for job in jobs:
                job_status = job.get("status")
                job_id = job.get("id")
                
                if job_status == "SUCCESS":
                    # Check if we've already fully processed this job
                    if self._is_job_fully_processed(job_id):
                        self.logger.debug(f"Job {job_id} already fully processed, skipping download")
                        continue
                    
                    # Always check if this job exists in our local database
                    # This prevents attempting to download jobs that were cleaned up
                    with sqlite3.connect(self.tracker.db_path) as conn:
                        cursor = conn.cursor()
                        local_job = cursor.execute(
                            "SELECT 1 FROM batch_jobs WHERE job_id = ?", 
                            (job_id,)
                        ).fetchone()
                        
                        # If job doesn't exist locally, skip it (it was probably cleaned up)
                        if not local_job:
                            self.logger.debug(f"Job {job_id} not in local database, skipping (use --recover-orphan-jobs to download)")
                            continue
                    
                    # Check if we've processed this job
                    job_results_file = Path(f"batch_results_{job_id}.jsonl")
                    processed_results_file = Path(f"processed_batch_results/batch_results_{job_id}.jsonl")
                    
                    if not job_results_file.exists() and not processed_results_file.exists():
                        self.console.print(f"[yellow]Downloading results for job {job_id[:12]}...[/yellow]")
                        self._download_and_process_job(job_id)
                        
        except Exception as e:
            self.logger.error(f"Error checking Mistral API: {e}")
    
    def _recover_job(self, job_info: Dict):
        """Recover a specific job from tracker."""
        job_id = job_info['job_id']
        status = job_info['status']
        
        self.console.print(f"[yellow]Recovering job {job_id[:12]} (status: {status})[/yellow]")
        
        try:
            # Try to get job status from API
            try:
                if self.mistral_service:
                    # Use asyncio for async service calls
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        job_status = loop.run_until_complete(self.mistral_service.get_batch_job_status(job_id))
                        if job_status:
                            actual_status = job_status['status']
                        else:
                            raise Exception("Failed to get job status")
                    finally:
                        loop.close()
                else:
                    # Use direct HTTP API for batch job status
                    import requests
                    api_key = os.environ.get("MISTRAL_API_KEY")
                    headers = {"Authorization": f"Bearer {api_key}"}
                    
                    response = requests.get(
                        f"https://api.mistral.ai/v1/batch/jobs/{job_id}",
                        headers=headers
                    )
                    
                    if response.status_code != 200:
                        raise Exception(f"Failed to get job status: {response.status_code} {response.text}")
                    
                    job_data = response.json()
                    actual_status = job_data["status"]
                
                if actual_status != status:
                    self.tracker.update_job_status(job_id, actual_status)
                    status = actual_status
                    
            except Exception as e:
                self.logger.warning(f"Could not get job status from API: {e}")
            
            # Handle based on status
            if status == "SUCCESS":
                self._download_and_process_job(job_id)
            elif status in ["QUEUED", "RUNNING"]:
                self._monitor_job(job_id)
            else:
                self.logger.info(f"Job {job_id} has status {status}, skipping")
                
        except Exception as e:
            self.logger.error(f"Error recovering job {job_id}: {e}")
            self.tracker.update_job_status(job_id, "ERROR", str(e))
    
    def _download_and_process_job(self, job_id: str):
        """Download and process results from a job."""
        results_file = Path(f"batch_results_{job_id}.jsonl")
        processed_results_file = Path(f"processed_batch_results/batch_results_{job_id}.jsonl")
        
        try:
            # Check if already fully processed
            if self._is_job_fully_processed(job_id):
                self.console.print(f"[green]Job {job_id[:12]} already fully processed, skipping[/green]")
                return
            
            # Check if results file exists (either in current dir or processed dir)
            if processed_results_file.exists():
                self.console.print(f"[yellow]Results already processed for job {job_id[:12]}[/yellow]")
                return
            
            # Download if not exists
            if not results_file.exists():
                try:
                    if self.mistral_service:
                        # Use asyncio for async service calls
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            job_status = loop.run_until_complete(self.mistral_service.get_batch_job_status(job_id))
                            if not job_status or not job_status.get('output_file'):
                                self.logger.error(f"No output file for job {job_id}")
                                self.tracker.update_job_status(job_id, "NO_OUTPUT")
                                return
                            
                            self.console.print(f"[blue]Downloading results for job {job_id[:12]}...[/blue]")
                            # Use MistralService to download results
                            results = loop.run_until_complete(self.mistral_service.download_batch_results(job_id))
                            if results:
                                # Convert results back to JSONL format for compatibility
                                results_content = "\n".join(json.dumps(result) for result in results)
                            else:
                                raise Exception("Failed to download batch results")
                        finally:
                            loop.close()
                    else:
                        # Use direct HTTP API for batch job retrieval
                        import requests
                        api_key = os.environ.get("MISTRAL_API_KEY")
                        headers = {"Authorization": f"Bearer {api_key}"}
                        
                        response = requests.get(
                            f"https://api.mistral.ai/v1/batch/jobs/{job_id}",
                            headers=headers
                        )
                        
                        if response.status_code != 200:
                            raise Exception(f"Failed to get job: {response.status_code} {response.text}")
                        
                        job_data = response.json()
                        
                        if not job_data.get('output_file'):
                            self.logger.error(f"No output file for job {job_id}")
                            self.tracker.update_job_status(job_id, "NO_OUTPUT")
                            return
                        
                        self.console.print(f"[blue]Downloading results for job {job_id[:12]}...[/blue]")
                        
                        # Download output file
                        response = requests.get(
                            f"https://api.mistral.ai/v1/files/{job_data['output_file']}/content",
                            headers=headers
                        )
                        
                        if response.status_code != 200:
                            raise Exception(f"Failed to download file: {response.status_code} {response.text}")
                        
                        results_content = response.text
                    
                    with open(results_file, "w") as f:
                        f.write(results_content)
                    
                    self.console.print(f"[green]Downloaded results to {results_file}[/green]")
                except Exception as e:
                    if "404" in str(e) or "No File matches" in str(e):
                        self.logger.warning(f"Output file no longer available for job {job_id}, skipping")
                        # Mark this job to not be retried
                        self.tracker.update_job_status(job_id, "FILE_DELETED")
                        return
                    else:
                        raise
            else:
                self.console.print(f"[yellow]Results file already exists for job {job_id[:12]}, processing...[/yellow]")
            
            # Process the results
            self._process_batch_results_file(results_file, job_id)
            
        except Exception as e:
            self.logger.error(f"Error downloading/processing job {job_id}: {e}")
            self.tracker.update_job_status(job_id, "ERROR", str(e))
    
    def _process_batch_results_file(self, results_file: Path, job_id: str):
        """Process a batch results file."""
        if not results_file.exists():
            return
        
        self.console.print(f"[blue]Processing results from {results_file}[/blue]")
        
        processed = 0
        failed = 0
        skipped = 0
        
        try:
            with open(results_file, 'r') as f:
                lines = f.readlines()
            
            total = len([l for l in lines if l.strip()])
            
            for i, line in enumerate(lines):
                if not line.strip():
                    continue
                
                if i % 50 == 0 and i > 0:
                    self.console.print(f"Progress: {i}/{total} - Processed: {processed}, Skipped: {skipped}")
                
                try:
                    result = json.loads(line)
                    custom_id = result.get("custom_id", "")
                    
                    if "_" not in custom_id:
                        failed += 1
                        continue
                    
                    parts = custom_id.split("_", 1)
                    filename_part = parts[1]
                    
                    # Find the PDF
                    pdf_found = False
                    for pdf_path in self.data_dir.rglob(f"{filename_part}.pdf"):
                        if "_bak" in str(pdf_path):
                            continue
                        
                        pdf_found = True
                        
                        # Check if already completed - also check if MD file exists
                        md_path = pdf_path.with_suffix(".md")
                        if self.tracker.is_file_completed(str(pdf_path)) or md_path.exists():
                            skipped += 1
                            break
                        
                        # Process the result
                        if self._process_single_result(result, pdf_path, job_id):
                            processed += 1
                        else:
                            failed += 1
                        break
                    
                    if not pdf_found:
                        self.logger.debug(f"PDF not found: {filename_part}")
                        failed += 1
                        
                except Exception as e:
                    self.logger.error(f"Error processing result: {e}")
                    failed += 1
            
            self.console.print(f"[green]Completed: Processed={processed}, Skipped={skipped}, Failed={failed}[/green]")
            
            # Update job status and file counts
            with sqlite3.connect(self.tracker.db_path) as conn:
                cursor = conn.cursor()
                
                # Get total pages from job metadata
                job_info = cursor.execute("""
                    SELECT metadata FROM batch_jobs WHERE job_id = ?
                """, (job_id,)).fetchone()
                
                total_pages = 0
                if job_info and job_info[0]:
                    try:
                        metadata = json.loads(job_info[0])
                        total_pages = metadata.get('total_pages', 0)
                    except:
                        pass
                
                cursor.execute("""
                    UPDATE batch_jobs 
                    SET status = 'COMPLETED', 
                        processed_files = ?,
                        failed_files = ?,
                        completed_at = ?
                    WHERE job_id = ?
                """, (processed + skipped, failed, datetime.now().isoformat(), job_id))
                conn.commit()
            
            # Log billing if we have page count
            if total_pages > 0:
                self.tracker.log_billing(job_id, total_pages)
                cost = total_pages / 2000.0
                self.console.print(f"[green]Billing logged: {total_pages} pages, ${cost:.4f}[/green]")
            
            # Delete uploaded file if metadata contains file ID
            self._cleanup_batch_file(job_id)
            
            # Archive the results file
            archive_dir = Path("processed_batch_results")
            archive_dir.mkdir(exist_ok=True)
            results_file.rename(archive_dir / results_file.name)
            
        except Exception as e:
            self.logger.error(f"Error processing results file: {e}")
    
    def _process_single_result(self, result: Dict, pdf_path: Path, job_id: str) -> bool:
        """Process a single OCR result."""
        try:
            md_path = pdf_path.with_suffix(".md")
            
            # Check if MD file already exists before processing
            if md_path.exists():
                self.logger.debug(f"MD file already exists for {pdf_path.name}, skipping")
                return True  # Return True since file is already processed
            
            # Extract response
            response = result.get("response", {})
            if not response or response.get("status_code") != 200:
                return False
            
            body = response.get("body", {})
            pages = body.get("pages", [])
            
            if not pages:
                return False
            
            # Extract text
            extracted_text = ""
            for page in pages:
                page_num = page.get("index", 0) + 1
                page_text = page.get("markdown", "")
                if page_text:
                    extracted_text += f"\n--- Page {page_num} ---\n"
                    extracted_text += page_text
                    extracted_text += "\n"
            
            if not extracted_text.strip():
                return False
            
            # Create MD file
            md_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(md_path, "w", encoding="utf-8") as f:
                f.write(f"# OCR Extracted Text - {pdf_path.name}\n\n")
                f.write(f"**Source PDF:** {pdf_path.name}\n")
                f.write(f"**Processed:** {datetime.now().isoformat()}\n")
                f.write(f"**Method:** Mistral Batch API\n")
                f.write(f"**Batch Job:** {job_id}\n")
                f.write(f"**Pages:** {len(pages)}\n\n")
                f.write("---\n\n")
                f.write(extracted_text)
            
            # Mark as completed
            self.tracker.mark_file_completed(str(pdf_path), str(md_path), job_id)
            
            self.logger.info(f"Created MD file: {md_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error processing result for {pdf_path}: {e}")
            return False
    
    def _monitor_job(self, job_id: str, timeout: int = 3600):
        """Monitor a job with timeout and retry logic."""
        start_time = time.time()
        retry_count = 0
        max_retries = 3
        
        while time.time() - start_time < timeout:
            try:
                if self.mistral_service:
                    # Use asyncio for async service calls
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        job_status = loop.run_until_complete(self.mistral_service.get_batch_job_status(job_id))
                        if job_status:
                            status = job_status['status']
                        else:
                            raise Exception("Failed to get job status")
                    finally:
                        loop.close()
                else:
                    # Use direct HTTP API for batch job status
                    import requests
                    api_key = os.environ.get("MISTRAL_API_KEY")
                    headers = {"Authorization": f"Bearer {api_key}"}
                    
                    response = requests.get(
                        f"https://api.mistral.ai/v1/batch/jobs/{job_id}",
                        headers=headers
                    )
                    
                    if response.status_code != 200:
                        raise Exception(f"Failed to get job status: {response.status_code} {response.text}")
                    
                    job_data = response.json()
                    status = job_data["status"]
                
                self.console.print(f"[yellow]Job {job_id[:12]}: {status}[/yellow]")
                self.tracker.update_job_status(job_id, status)
                
                if status == "SUCCESS":
                    self._download_and_process_job(job_id)
                    return True
                elif status in ["FAILED", "CANCELLED", "TIMEOUT_EXCEEDED"]:
                    self.logger.error(f"Job {job_id} ended with status: {status}")
                    return False
                
                retry_count = 0  # Reset retry count on success
                # Use longer polling interval for large batches
                time.sleep(30)
                
            except Exception as e:
                retry_count += 1
                self.logger.warning(f"Error monitoring job (retry {retry_count}/{max_retries}): {e}")
                
                if retry_count >= max_retries:
                    self.tracker.update_job_status(job_id, "ERROR", str(e))
                    return False
                
                time.sleep(10)  # Wait longer before retry
        
        self.logger.warning(f"Monitoring timeout for job {job_id}")
        return False
    
    def process_date_range(self, start_date: str, end_date: str, include_deleted: bool = False):
        """Process PDFs in a date range."""
        # First, always check for pending work
        self.recover_all_pending()
        
        # Then process new files
        dates = self._get_date_range(start_date, end_date)
        pdf_pairs = self._discover_unprocessed_pdfs(dates, include_deleted=include_deleted)
        
        if not pdf_pairs:
            self.console.print("[green]No new PDFs to process[/green]")
            return
        
        self.console.print(f"[blue]Found {len(pdf_pairs)} PDFs to process[/blue]")
        
        # Process in batches
        batch_size = 0
        batch_pdfs = []
        MAX_BATCH_SIZE_MB = 450
        
        for pdf_path, md_path in pdf_pairs:
            pdf_size_mb = pdf_path.stat().st_size / (1024 * 1024)
            estimated_size = pdf_size_mb * 1.43  # Base64 overhead
            
            if batch_size + estimated_size > MAX_BATCH_SIZE_MB and batch_pdfs:
                # Submit current batch
                self._submit_batch(batch_pdfs)
                batch_pdfs = []
                batch_size = 0
            
            batch_pdfs.append((pdf_path, md_path))
            batch_size += estimated_size
        
        # Submit final batch
        if batch_pdfs:
            self._submit_batch(batch_pdfs)
    
    def _discover_unprocessed_pdfs(self, dates: List[str], include_deleted: bool = False) -> List[Tuple[Path, Path]]:
        """Find PDFs that need processing.
        
        This method implements intelligent file discovery with multiple skip conditions:
        1. Skip if MD file exists (fastest check, no DB query needed)
        2. Skip if JSON metadata has added_on == "00000000" (indicates non-relevant file)
        3. Skip if marked as completed in database (unless include_deleted=True)
        
        The include_deleted parameter allows re-processing of files that were previously
        processed but have missing MD files. This is useful when you want to include
        them in a new batch submission (will incur costs).
        
        Args:
            dates: List of date strings to process (YYYYMMDD format)
            include_deleted: If True, include files that were previously processed but MD files are missing
            
        Returns:
            List of tuples (pdf_path, md_path) for files that need processing
        """
        pdf_pairs = []
        skipped_count = 0
        skipped_db_count = 0
        skipped_added_on_count = 0
        
        for date_str in dates:
            date_dir = self.data_dir / date_str / "dockets"
            if not date_dir.exists():
                continue
            
            for pdf_file in date_dir.rglob("*.pdf"):
                if "_bak" in str(pdf_file):
                    continue
                
                # Check if already processed - MD file check first (fastest)
                md_file = pdf_file.with_suffix(".md")
                
                # First check if MD file exists - if it does, skip immediately
                if md_file.exists():
                    skipped_count += 1
                    self.logger.debug(f"Skipping {pdf_file.name} - MD file already exists")
                    continue
                
                # Check if corresponding JSON file has added_on == "00000000" (skip early if so)
                json_file = pdf_file.with_suffix(".json")
                if json_file.exists():
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            json_data = json.load(f)
                            added_on = json_data.get('added_on', '')
                            if added_on == "00000000":
                                skipped_added_on_count += 1
                                self.logger.debug(f"Skipping {pdf_file.name} - added_on is 00000000")
                                continue
                    except (json.JSONDecodeError, Exception) as e:
                        self.logger.warning(f"Could not read JSON file {json_file}: {e}")
                        # Continue processing the PDF if JSON is malformed
                
                # Only check database if file passed other checks (more expensive operation)
                # Check both exact path and filename matches (for duplicates in other directories)
                is_completed = self.tracker.is_file_completed(str(pdf_file)) or self.tracker.is_filename_completed(str(pdf_file))
                
                if include_deleted and is_completed and not md_file.exists():
                    # This file was previously processed but MD is missing - include it for recovery
                    pdf_pairs.append((pdf_file, md_file))
                elif is_completed:
                    skipped_db_count += 1
                    self.logger.debug(f"Skipping {pdf_file.name} - marked as completed in database")
                    continue
                elif not is_completed:
                    # Only add if all checks pass
                    pdf_pairs.append((pdf_file, md_file))
        
        if skipped_count > 0:
            self.console.print(f"[yellow]Skipped {skipped_count} PDFs that already have MD files[/yellow]")
        if skipped_db_count > 0:
            self.console.print(f"[yellow]Skipped {skipped_db_count} PDFs already marked as completed in database[/yellow]")
        if skipped_added_on_count > 0:
            self.console.print(f"[yellow]Skipped {skipped_added_on_count} PDFs with added_on == '00000000'[/yellow]")
        
        return pdf_pairs
    
    def _submit_batch(self, batch_pdfs: List[Tuple[Path, Path]]):
        """Submit a batch with robust error handling."""
        if not batch_pdfs:
            return
        
        self.console.print(f"[blue]Submitting batch of {len(batch_pdfs)} PDFs using direct HTTP API[/blue]")
        
        try:
            # Create batch input file
            batch_requests = []
            file_mappings = []
            total_pages = 0
            
            for idx, (pdf_path, _) in enumerate(batch_pdfs):
                # Count pages
                page_count = self._count_pdf_pages(pdf_path)
                total_pages += page_count
                
                with open(pdf_path, "rb") as f:
                    pdf_data = f.read()
                
                pdf_base64 = base64.b64encode(pdf_data).decode("utf-8")
                custom_id = f"{idx}_{pdf_path.stem}"
                
                request = {
                    "custom_id": custom_id,
                    "method": "POST",
                    "url": "/v1/ocr",
                    "body": {
                        "model": "mistral-ocr-latest",
                        "document": {
                            "type": "document_url",
                            "document_url": f"data:application/pdf;base64,{pdf_base64}"
                        },
                        "include_image_base64": False
                    }
                }
                
                batch_requests.append(request)
                file_mappings.append((str(pdf_path), custom_id))
            
            # Write batch file
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False)
            for request in batch_requests:
                json.dump(request, temp_file)
                temp_file.write('\n')
            temp_file.close()
            
            # Upload file using direct HTTP API
            import requests
            
            api_key = os.environ.get("MISTRAL_API_KEY")
            headers = {
                "Authorization": f"Bearer {api_key}",
            }
            
            with open(temp_file.name, "rb") as f:
                files = {
                    "file": (f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jsonl", f, "application/jsonl"),
                    "purpose": (None, "batch")
                }
                
                response = requests.post(
                    "https://api.mistral.ai/v1/files",
                    headers=headers,
                    files=files
                )
                
                if response.status_code != 200:
                    raise Exception(f"File upload failed: {response.status_code} {response.text}")
                
                batch_file_data = response.json()
                batch_file_id = batch_file_data["id"]
            
            # Submit job using direct HTTP API
            job_data = {
                "model": "mistral-ocr-latest",
                "input_files": [batch_file_id],
                "endpoint": "/v1/ocr",
                "metadata": {
                    "total_files": str(len(batch_pdfs)),
                    "total_pages": str(total_pages),
                    "created_at": datetime.now().isoformat()
                }
            }
            
            response = requests.post(
                "https://api.mistral.ai/v1/batch/jobs",
                headers=headers,
                json=job_data
            )
            
            if response.status_code != 200:
                raise Exception(f"Batch job creation failed: {response.status_code} {response.text}")
            
            batch_job_data = response.json()
            job_id = batch_job_data["id"]
            
            estimated_cost = total_pages / 2000.0
            self.console.print(f"[green]Batch submitted: {job_id}[/green]")
            self.console.print(f"[yellow]Total pages: {total_pages} (Estimated cost: ${estimated_cost:.4f})[/yellow]")
            
            # Track in database
            metadata = {
                "total_files": len(batch_pdfs),
                "total_pages": total_pages,
                "batch_file_id": batch_file_id
            }
            self.tracker.add_batch_job(job_id, temp_file.name, metadata)
            self.tracker.add_batch_files(job_id, file_mappings)
            
            # Clean up temp file
            Path(temp_file.name).unlink()
            
            # Monitor with retry logic
            self._monitor_job(job_id)
            
        except Exception as e:
            self.logger.error(f"Error submitting batch: {e}")
            self.console.print(f"[red]Batch submission failed: {e}[/red]")
    
    def _count_pdf_pages(self, pdf_path: Path) -> int:
        """Count pages in a PDF file."""
        if not PYMUPDF_AVAILABLE:
            # If PyMuPDF not available, estimate based on file size
            # Average PDF has about 100KB per page
            size_kb = pdf_path.stat().st_size / 1024
            return max(1, int(size_kb / 100))
        
        try:
            with fitz.open(pdf_path) as pdf:
                return len(pdf)
        except Exception as e:
            self.logger.warning(f"Could not count pages for {pdf_path}: {e}")
            # Fallback to size estimation
            size_kb = pdf_path.stat().st_size / 1024
            return max(1, int(size_kb / 100))
    
    def _cleanup_batch_file(self, job_id: str) -> bool:
        """Delete the uploaded batch file from Mistral."""
        try:
            # Get batch file ID from job metadata
            with sqlite3.connect(self.tracker.db_path) as conn:
                cursor = conn.cursor()
                result = cursor.execute("""
                    SELECT metadata FROM batch_jobs WHERE job_id = ?
                """, (job_id,)).fetchone()
                
                if not result or not result[0]:
                    return False
                
                metadata = json.loads(result[0])
                batch_file_id = metadata.get('batch_file_id')
                
                if batch_file_id:
                    # Delete the file from Mistral
                    if self.mistral_service:
                        # Note: MistralService doesn't have file delete method yet
                        # Fall back to direct client
                        from mistralai import Mistral
                        temp_client = Mistral(api_key=os.environ.get("MISTRAL_API_KEY"))
                        temp_client.files.delete(file_id=batch_file_id)
                    else:
                        self.client.files.delete(file_id=batch_file_id)
                    self.logger.info(f"Deleted batch file {batch_file_id} for job {job_id}")
                    return True
                    
        except Exception as e:
            self.logger.warning(f"Could not delete batch file for job {job_id}: {e}")
        
        return False
    
    def cleanup_all_batch_files(self, force: bool = False):
        """Delete ALL files from Mistral API (handles pagination)."""
        self.console.print("[yellow]Cleaning up ALL files from Mistral API...[/yellow]")
        
        if not force:
            response = input("This will delete ALL files from your Mistral account. Continue? (yes/no): ")
            if response.lower() != 'yes':
                self.console.print("[red]Cancelled.[/red]")
                return
        
        total_deleted = 0
        total_failed = 0
        batch_num = 0
        
        try:
            # Keep deleting until no more files are found
            while True:
                batch_num += 1
                deleted_in_batch = 0
                
                try:
                    self.console.print(f"\n[dim]Fetching file batch {batch_num}...[/dim]")
                    
                    # List files - API returns max 100 at a time
                    if self.mistral_service:
                        # Note: MistralService doesn't have files.list method yet
                        # Fall back to direct client
                        from mistralai import Mistral
                        temp_client = Mistral(api_key=os.environ.get("MISTRAL_API_KEY"))
                        files_response = temp_client.files.list()
                    else:
                        files_response = self.client.files.list()
                    
                    if hasattr(files_response, 'data'):
                        files = files_response.data
                    else:
                        files = files_response
                    
                    if not files or len(files) == 0:
                        self.console.print("[green]No more files to delete![/green]")
                        break
                    
                    self.console.print(f"[yellow]Found {len(files)} files in batch {batch_num}[/yellow]")
                    
                    # Delete each file in this batch
                    for i, file in enumerate(files):
                        try:
                            file_id = file.id if hasattr(file, 'id') else file.get('id')
                            if file_id:
                                if self.mistral_service:
                                    # Fall back to direct client for file operations
                                    from mistralai import Mistral
                                    temp_client = Mistral(api_key=os.environ.get("MISTRAL_API_KEY"))
                                    temp_client.files.delete(file_id=file_id)
                                else:
                                    self.client.files.delete(file_id=file_id)
                                deleted_in_batch += 1
                                total_deleted += 1
                                
                                # Show progress every 10 files
                                if (i + 1) % 10 == 0:
                                    self.console.print(f"[dim]Deleted {i + 1}/{len(files)} files in this batch...[/dim]")
                                    
                        except Exception as e:
                            total_failed += 1
                            self.logger.warning(f"Failed to delete file: {e}")
                    
                    self.console.print(f"[green]Deleted {deleted_in_batch} files in batch {batch_num}[/green]")
                    
                    # If we deleted files, there might be more - wait a bit then continue
                    if deleted_in_batch > 0:
                        self.console.print("[dim]Waiting 1 second before next batch...[/dim]")
                        time.sleep(1)
                    else:
                        # No files deleted, we're done
                        break
                        
                except AttributeError as e:
                    # If list() doesn't exist, fall back to database method
                    if batch_num == 1:
                        self.console.print("[yellow]Files.list() not available, trying database method...[/yellow]")
                        
                        # Get all jobs with file IDs from database
                        with sqlite3.connect(self.tracker.db_path) as conn:
                            cursor = conn.cursor()
                            jobs = cursor.execute("""
                                SELECT job_id, metadata FROM batch_jobs 
                                WHERE metadata IS NOT NULL
                            """).fetchall()
                            
                            for job_id, metadata_str in jobs:
                                try:
                                    metadata = json.loads(metadata_str)
                                    batch_file_id = metadata.get('batch_file_id')
                                    
                                    if batch_file_id:
                                        try:
                                            if self.mistral_service:
                                                from mistralai import Mistral
                                                temp_client = Mistral(api_key=os.environ.get("MISTRAL_API_KEY"))
                                                temp_client.files.delete(file_id=batch_file_id)
                                            else:
                                                self.client.files.delete(file_id=batch_file_id)
                                            total_deleted += 1
                                            self.logger.info(f"Deleted file {batch_file_id}")
                                        except Exception as e:
                                            if "404" not in str(e):  # Ignore if file already deleted
                                                total_failed += 1
                                                self.logger.warning(f"Failed to delete {batch_file_id}: {e}")
                                                
                                except json.JSONDecodeError:
                                    continue
                    break
                    
                except Exception as e:
                    self.logger.error(f"Error in batch {batch_num}: {e}")
                    self.console.print(f"[red]Error in batch {batch_num}: {e}[/red]")
                    break
            
            # Try to list batch jobs as well
            try:
                batch_jobs = self.client.jobs.list()
                self.console.print(f"\n[dim]Note: {len(batch_jobs.data)} batch jobs still exist in API[/dim]")
            except:
                pass
                
        except Exception as e:
            self.logger.error(f"Error during file cleanup: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
        
        self.console.print(f"\n[bold]Final Cleanup Results:[/bold]")
        self.console.print(f"[green]Successfully deleted: {total_deleted} files[/green]")
        if total_failed > 0:
            self.console.print(f"[red]Failed to delete: {total_failed} files[/red]")
    
    def cancel_all_batch_jobs(self, force: bool = False):
        """Cancel ALL batch jobs in Mistral API."""
        self.console.print("[yellow]Cancelling ALL batch jobs in Mistral API...[/yellow]")
        
        if not force:
            response = input("This will cancel ALL batch jobs in your account. Continue? (yes/no): ")
            if response.lower() != 'yes':
                self.console.print("[red]Cancelled.[/red]")
                return
        
        total_cancelled = 0
        total_failed = 0
        
        try:
            # List all batch jobs
            self.console.print("[dim]Fetching all batch jobs...[/dim]")
            
            if self.mistral_service:
                # Fall back to direct client for batch jobs operations
                from mistralai import Mistral
                temp_client = Mistral(api_key=os.environ.get("MISTRAL_API_KEY"))
                jobs_response = temp_client.jobs.list()
            else:
                jobs_response = self.client.jobs.list()
            
            if hasattr(jobs_response, 'data'):
                jobs = jobs_response.data
            else:
                jobs = jobs_response
            
            self.console.print(f"[yellow]Found {len(jobs)} batch jobs[/yellow]")
            
            # Process each job
            for job in jobs:
                try:
                    job_id = job.id if hasattr(job, 'id') else job.get('id')
                    job_status = job.status if hasattr(job, 'status') else job.get('status', 'UNKNOWN')
                    
                    if job_status in ['QUEUED', 'RUNNING']:
                        # Cancel active jobs
                        if self.mistral_service:
                            from mistralai import Mistral
                            temp_client = Mistral(api_key=os.environ.get("MISTRAL_API_KEY"))
                            temp_client.jobs.cancel(job_id=job_id)
                        else:
                            self.client.jobs.cancel(job_id=job_id)
                        total_cancelled += 1
                        self.console.print(f"[green]Cancelled job: {job_id} (was {job_status})[/green]")
                    else:
                        # Job is already completed/failed/cancelled
                        self.console.print(f"[dim]Skipped job: {job_id} (status: {job_status})[/dim]")
                        
                except Exception as e:
                    total_failed += 1
                    self.logger.warning(f"Failed to cancel job: {e}")
                    self.console.print(f"[red]Failed to cancel job: {e}[/red]")
            
        except Exception as e:
            self.logger.error(f"Error during batch job cancellation: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
        
        self.console.print(f"\n[bold]Batch Job Cancellation Results:[/bold]")
        self.console.print(f"[green]Successfully cancelled: {total_cancelled} jobs[/green]")
        if total_failed > 0:
            self.console.print(f"[red]Failed to cancel: {total_failed} jobs[/red]")
        
        # Note about deletion
        self.console.print("\n[yellow]Note: Mistral API doesn't support deleting batch jobs.[/yellow]")
        self.console.print("[yellow]Cancelled jobs will remain in your history with 'CANCELLED' status.[/yellow]")
    
    def recover_deleted_files(self, start_date: str, end_date: str):
        """Recover files that were previously processed but MD files are missing.
        
        This method allows recovery of OCR results without re-submitting to Mistral API,
        saving costs. It works by:
        1. Finding PDFs marked as completed in DB but missing MD files
        2. Grouping them by their original batch job IDs
        3. Attempting to recover from:
           - Local cached result files (processed_batch_results/*.jsonl)
           - Unprocessed result files (batch_results_*.jsonl)
           - Mistral API if files still available
        
        Args:
            start_date: Start date in YYYYMMDD format
            end_date: End date in YYYYMMDD format
            
        Note:
            This is cost-free as it reuses existing OCR results.
            Files must have been previously processed for recovery to work.
        """
        self.console.print("[bold blue]Checking for deleted MD files that can be recovered from existing Mistral jobs...[/bold blue]")
        
        # Get date range
        dates = self._get_date_range(start_date, end_date)
        
        # Find files that are marked as completed but MD files are missing
        deleted_files = []
        files_by_job = {}  # Group by job_id for efficient recovery
        
        with sqlite3.connect(self.tracker.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            for date_str in dates:
                date_dir = self.data_dir / date_str / "dockets"
                if not date_dir.exists():
                    continue
                
                for pdf_file in date_dir.rglob("*.pdf"):
                    if "_bak" in str(pdf_file):
                        continue
                    
                    # Check if file is in completed_files table
                    result = cursor.execute("""
                        SELECT pdf_path, md_path, job_id, processed_at 
                        FROM completed_files 
                        WHERE pdf_path = ? OR pdf_path LIKE ?
                    """, (str(pdf_file), f"%{pdf_file.name}")).fetchone()
                    
                    if result:
                        md_path = Path(result['md_path'])
                        # Check if MD file is missing
                        if not md_path.exists():
                            deleted_files.append({
                                'pdf_path': pdf_file,
                                'md_path': md_path,
                                'job_id': result['job_id'],
                                'processed_at': result['processed_at']
                            })
                            
                            # Group by job_id for efficient recovery
                            job_id = result['job_id']
                            if job_id and job_id != 'None':
                                if job_id not in files_by_job:
                                    files_by_job[job_id] = []
                                files_by_job[job_id].append(pdf_file.name)
        
        if not deleted_files:
            self.console.print("[green]No deleted MD files found that can be recovered.[/green]")
            return
        
        # Show summary
        self.console.print(f"\n[yellow]Found {len(deleted_files)} deleted MD files that can be recovered:[/yellow]")
        self.console.print(f"[yellow]These files are from {len(files_by_job)} Mistral batch jobs[/yellow]\n")
        
        # List files
        for i, file_info in enumerate(deleted_files[:20]):  # Show first 20
            self.console.print(f"  {i+1}. {file_info['pdf_path'].name} (processed {file_info['processed_at'][:10]})")
        
        if len(deleted_files) > 20:
            self.console.print(f"  ... and {len(deleted_files) - 20} more files")
        
        # Ask for confirmation
        response = input(f"\nRecover these {len(deleted_files)} files from existing Mistral jobs? (yes/no): ")
        if response.lower() != 'yes':
            self.console.print("[red]Recovery cancelled.[/red]")
            return
        
        # Process recovery by job
        recovered_count = 0
        failed_count = 0
        
        for job_id, filenames in files_by_job.items():
            self.console.print(f"\n[blue]Recovering {len(filenames)} files from job {job_id[:12]}...[/blue]")
            
            # Check if we have the results file
            results_file = Path(f"batch_results_{job_id}.jsonl")
            processed_results_file = Path(f"processed_batch_results/batch_results_{job_id}.jsonl")
            
            if processed_results_file.exists():
                # Use processed file
                self._process_batch_results_file_recovery(processed_results_file, job_id, filenames)
                recovered_count += len(filenames)
            elif results_file.exists():
                # Use unprocessed file
                self._process_batch_results_file_recovery(results_file, job_id, filenames)
                recovered_count += len(filenames)
            else:
                # Don't download from API - only use local files to avoid charges
                self.console.print(f"[red]No local results file found for job {job_id[:12]}[/red]")
                self.console.print(f"[dim]Files needed: {', '.join(filenames[:3])}{'...' if len(filenames) > 3 else ''}[/dim]")
                failed_count += len(filenames)
        
        self.console.print(f"\n[bold]Recovery Complete:[/bold]")
        self.console.print(f"[green]Successfully recovered: {recovered_count} files[/green]")
        if failed_count > 0:
            self.console.print(f"[red]Failed to recover: {failed_count} files[/red]")

    def _process_batch_results_file_recovery(self, results_file: Path, job_id: str, target_filenames: List[str]):
        """Process a batch results file for recovery - only processes specific files and doesn't delete anything.
        
        Args:
            results_file: Path to the batch results JSONL file
            job_id: The Mistral job ID
            target_filenames: List of specific filenames to recover
        """
        if not results_file.exists():
            return
        
        self.console.print(f"[blue]Processing results from {results_file} (recovery mode)[/blue]")
        
        processed = 0
        failed = 0
        skipped = 0
        
        try:
            with open(results_file, 'r') as f:
                lines = f.readlines()
            
            total = len([l for l in lines if l.strip()])
            
            for i, line in enumerate(lines):
                if not line.strip():
                    continue
                
                try:
                    result = json.loads(line)
                    custom_id = result.get("custom_id", "")
                    
                    if "_" not in custom_id:
                        continue
                    
                    parts = custom_id.split("_", 1)
                    filename_part = parts[1]
                    
                    # Only process if this file is in our target list
                    if not any(target_name in filename_part for target_name in target_filenames):
                        continue
                    
                    # Find the PDF in the current date directory
                    pdf_found = False
                    for pdf_path in self.data_dir.rglob(f"*{filename_part}.pdf"):
                        if "_bak" in str(pdf_path):
                            continue
                        
                        pdf_found = True
                        
                        # Process the result
                        if self._process_single_result(result, pdf_path, job_id):
                            processed += 1
                            self.console.print(f"[green]Recovered: {pdf_path.name}[/green]")
                        else:
                            failed += 1
                        break
                    
                    if not pdf_found:
                        self.logger.debug(f"PDF not found in current date: {filename_part}")
                        
                except Exception as e:
                    self.logger.error(f"Error processing result: {e}")
                    failed += 1
            
            self.console.print(f"[green]Recovery processed: {processed} files[/green]")
            
            # DO NOT delete or archive the results file during recovery
            # DO NOT update job status - we're just recovering specific files
            
        except Exception as e:
            self.logger.error(f"Error processing results file for recovery: {e}")

    def _get_date_range(self, start_date: str, end_date: str) -> List[str]:
        """Generate date range."""
        try:
            start = datetime.strptime(start_date, "%Y%m%d")
            end = datetime.strptime(end_date, "%Y%m%d")
            
            dates = []
            current = start
            while current <= end:
                dates.append(current.strftime("%Y%m%d"))
                current += timedelta(days=1)
            
            return dates
        except ValueError:
            return []
    
    def show_batch_status(self):
        """Show status of all batch jobs."""
        from rich.table import Table
        
        table = Table(title="Batch Job Status")
        table.add_column("Job ID", style="cyan", no_wrap=True)
        table.add_column("Status", style="yellow")
        table.add_column("Created Date/Time", style="green", no_wrap=True)
        table.add_column("Files", style="magenta")
        table.add_column("Pages", style="blue")
        table.add_column("Cost", style="green")
        table.add_column("Processed", style="blue")
        table.add_column("Failed", style="red")
        
        with sqlite3.connect(self.tracker.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get all jobs
            jobs = cursor.execute("""
                SELECT job_id, status, created_at, total_files, 
                       processed_files, failed_files, completed_at,
                       total_pages, estimated_cost
                FROM batch_jobs
                ORDER BY created_at DESC
                LIMIT 100
            """).fetchall()
            
            total_cost = 0.0
            total_pages_all = 0
            
            for job in jobs:
                job_id_short = job['job_id'][:12] + "..."
                status = job['status']
                # Show full date and time
                if job['created_at']:
                    try:
                        # Parse ISO format and display in readable format
                        from datetime import datetime
                        dt = datetime.fromisoformat(job['created_at'].replace('Z', '+00:00'))
                        created = dt.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        created = job['created_at'][:19]  # Fallback to first 19 chars
                else:
                    created = "N/A"
                
                total = str(job['total_files']) if job['total_files'] else "0"
                processed = str(job['processed_files']) if job['processed_files'] else "0"
                failed = str(job['failed_files']) if job['failed_files'] else "0"
                pages = str(job['total_pages']) if job['total_pages'] else "0"
                cost = f"${job['estimated_cost']:.4f}" if job['estimated_cost'] else "$0.0000"
                
                if job['total_pages']:
                    total_pages_all += job['total_pages']
                if job['estimated_cost']:
                    total_cost += job['estimated_cost']
                
                # Color code status
                if status == "COMPLETED":
                    status = f"[green]{status}[/green]"
                elif status in ["FAILED", "ERROR"]:
                    status = f"[red]{status}[/red]"
                elif status in ["QUEUED", "RUNNING"]:
                    status = f"[yellow]{status}[/yellow]"
                
                table.add_row(job_id_short, status, created, total, pages, cost, processed, failed)
        
        self.console.print(table)
        
        # Show billing summary
        self.console.print(f"\n[bold]Total Pages Processed: {total_pages_all:,}[/bold]")
        self.console.print(f"[bold]Total Estimated Cost: ${total_cost:.4f}[/bold]")
        
        # Also check for orphan result files
        import glob
        orphan_files = glob.glob("batch_results_*.jsonl")
        if orphan_files:
            self.console.print(f"\n[yellow]Found {len(orphan_files)} unprocessed result files:[/yellow]")
            for f in orphan_files:
                self.console.print(f"  - {f}")
    
    def show_job_details(self, job_id: str):
        """Show detailed information about a specific job."""
        from rich.table import Table
        from rich.panel import Panel
        
        with sqlite3.connect(self.tracker.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get job details
            job = cursor.execute("""
                SELECT * FROM batch_jobs WHERE job_id = ?
            """, (job_id,)).fetchone()
            
            if not job:
                self.console.print(f"[red]Job {job_id} not found[/red]")
                return
            
            # Create info panel
            info_lines = []
            info_lines.append(f"[bold]Job ID:[/bold] {job['job_id']}")
            info_lines.append(f"[bold]Status:[/bold] {job['status']}")
            info_lines.append(f"[bold]Created:[/bold] {job['created_at']}")
            if job['submitted_at']:
                info_lines.append(f"[bold]Submitted:[/bold] {job['submitted_at']}")
            if job['completed_at']:
                info_lines.append(f"[bold]Completed:[/bold] {job['completed_at']}")
            info_lines.append(f"[bold]Total Files:[/bold] {job['total_files'] or 0}")
            info_lines.append(f"[bold]Total Pages:[/bold] {job['total_pages'] or 0}")
            info_lines.append(f"[bold]Estimated Cost:[/bold] ${job['estimated_cost'] or 0:.4f}")
            info_lines.append(f"[bold]Processed Files:[/bold] {job['processed_files'] or 0}")
            info_lines.append(f"[bold]Failed Files:[/bold] {job['failed_files'] or 0}")
            
            if job['last_error']:
                info_lines.append(f"[bold red]Last Error:[/bold red] {job['last_error']}")
            
            self.console.print(Panel("\n".join(info_lines), title="Job Details", border_style="blue"))
            
            # Show file details
            files = cursor.execute("""
                SELECT pdf_path, status, processed_at, error_message
                FROM batch_files
                WHERE job_id = ?
                ORDER BY pdf_path
            """, (job_id,)).fetchall()
            
            if files:
                file_table = Table(title="Files in Batch")
                file_table.add_column("PDF Path", style="cyan")
                file_table.add_column("Status", style="yellow")
                file_table.add_column("Processed At", style="green")
                
                for file in files:
                    status = file['status']
                    if status == 'completed':
                        status = f"[green]{status}[/green]"
                    elif status == 'failed':
                        status = f"[red]{status}[/red]"
                    
                    processed = file['processed_at'][:19] if file['processed_at'] else "N/A"
                    file_table.add_row(file['pdf_path'], status, processed)
                
                self.console.print(file_table)
    
    def show_billing_report(self, start_date: Optional[str] = None, end_date: Optional[str] = None):
        """Show billing report for a date range."""
        from rich.table import Table
        
        # Get billing summary
        summary = self.tracker.get_billing_summary(start_date, end_date)
        
        # Create summary table
        table = Table(title="Billing Summary")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="yellow")
        
        if summary['first_date'] and summary['last_date']:
            table.add_row("Date Range", f"{summary['first_date']} to {summary['last_date']}")
        else:
            table.add_row("Date Range", "All time")
        
        table.add_row("Total Jobs", str(summary['job_count']))
        table.add_row("Total Pages", f"{summary['total_pages']:,}" if summary['total_pages'] else "0")
        table.add_row("Total Cost", f"${summary['total_cost']:.4f}" if summary['total_cost'] else "$0.0000")
        
        if summary['total_pages'] and summary['job_count']:
            avg_pages = summary['total_pages'] / summary['job_count']
            table.add_row("Avg Pages/Job", f"{avg_pages:.1f}")
        
        self.console.print(table)
        
        # Show daily breakdown if date range is specified
        if start_date and end_date:
            self.console.print("\n[bold]Daily Breakdown:[/bold]")
            
            with sqlite3.connect(self.tracker.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                daily_data = cursor.execute("""
                    SELECT date, COUNT(DISTINCT job_id) as jobs, 
                           SUM(pages_processed) as pages, SUM(cost) as cost
                    FROM billing_log
                    WHERE date >= ? AND date <= ?
                    GROUP BY date
                    ORDER BY date DESC
                """, (start_date, end_date)).fetchall()
                
                if daily_data:
                    daily_table = Table()
                    daily_table.add_column("Date", style="green")
                    daily_table.add_column("Jobs", style="yellow")
                    daily_table.add_column("Pages", style="blue")
                    daily_table.add_column("Cost", style="green")
                    
                    for row in daily_data:
                        daily_table.add_row(
                            row['date'],
                            str(row['jobs']),
                            f"{row['pages']:,}" if row['pages'] else "0",
                            f"${row['cost']:.4f}" if row['cost'] else "$0.0000"
                        )
                    
                    self.console.print(daily_table)
    
    def cleanup_old_batches(self, days_to_keep: int = 0, delete_api_files: bool = True):
        """Delete batch jobs and their results created before specified days ago.
        
        Args:
            days_to_keep: Number of days to keep (0 = delete all before today)
            delete_api_files: Whether to also delete files from Mistral API
        """
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        if days_to_keep > 0:
            cutoff_date = cutoff_date - timedelta(days=days_to_keep)
        
        self.console.print(f"[yellow]Cleaning up batches created before {cutoff_date.strftime('%Y-%m-%d')}...[/yellow]")
        
        # First, delete API files if requested
        api_files_deleted = 0
        if delete_api_files:
            with sqlite3.connect(self.tracker.db_path) as conn:
                cursor = conn.cursor()
                old_jobs = cursor.execute("""
                    SELECT job_id FROM batch_jobs 
                    WHERE created_at < ?
                """, (cutoff_date.isoformat(),)).fetchall()
                
                for (job_id,) in old_jobs:
                    if self._cleanup_batch_file(job_id):
                        api_files_deleted += 1
        
        # Get list of jobs to delete from database
        num_deleted, deleted_job_ids = self.tracker.delete_old_jobs(cutoff_date)
        
        # Delete associated result files
        deleted_files = 0
        import glob
        
        for job_id in deleted_job_ids:
            # Check current directory
            result_file = Path(f"batch_results_{job_id}.jsonl")
            if result_file.exists():
                result_file.unlink()
                deleted_files += 1
                self.logger.info(f"Deleted result file: {result_file}")
            
            # Check processed directory
            processed_file = Path(f"processed_batch_results/batch_results_{job_id}.jsonl")
            if processed_file.exists():
                processed_file.unlink()
                deleted_files += 1
                self.logger.info(f"Deleted processed file: {processed_file}")
        
        # Also clean up any orphan batch files not in database
        all_batch_files = glob.glob("batch_results_*.jsonl") + glob.glob("processed_batch_results/batch_results_*.jsonl")
        
        for batch_file in all_batch_files:
            file_path = Path(batch_file)
            if file_path.stat().st_mtime < cutoff_date.timestamp():
                file_path.unlink()
                deleted_files += 1
                self.logger.info(f"Deleted orphan file: {batch_file}")
        
        self.console.print(f"[green]Cleanup complete:[/green]")
        self.console.print(f"  - Deleted {num_deleted} job records from database")
        self.console.print(f"  - Deleted {deleted_files} result files")
        if delete_api_files:
            self.console.print(f"  - Deleted {api_files_deleted} files from Mistral API")
        
        # Show remaining jobs
        self.show_batch_status()


def main():
    """Main entry point with robust argument handling."""
    parser = argparse.ArgumentParser(
        description="""Robust Mistral Batch OCR Script

This script processes PDF files using Mistral's OCR API with batch processing,
cost tracking, and recovery capabilities.

Recovery Features:
- Use --recover-deleted to recover previously processed files without re-payment
- Use --include-deleted to include deleted files in new batch processing

Examples:
  # Process new PDFs
  %(prog)s --start-date 20241210 --end-date 20241212
  
  # Recover deleted MD files (no cost)
  %(prog)s --start-date 20241210 --end-date 20241212 --recover-deleted
  
  # Check batch status
  %(prog)s --status
""",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--start-date", help="Start date (YYYYMMDD)")
    parser.add_argument("--end-date", help="End date (YYYYMMDD)")
    parser.add_argument("--recover", action="store_true", help="Only run recovery")
    parser.add_argument("--recover-deleted", action="store_true", 
                        help="Recover files that were previously processed but MD files are missing")
    parser.add_argument("--include-deleted", action="store_true",
                        help="Include previously processed files with missing MD files in normal processing")
    parser.add_argument("--data-dir", default="data", help="Data directory")
    parser.add_argument("--status", action="store_true", help="Show status of all batch jobs")
    parser.add_argument("--cleanup", type=int, nargs='?', const=0, metavar="DAYS",
                        help="Delete batches older than DAYS (default: 0 = before today)")
    parser.add_argument("--billing", action="store_true", help="Show billing report")
    parser.add_argument("--billing-start", help="Billing report start date (YYYYMMDD)")
    parser.add_argument("--billing-end", help="Billing report end date (YYYYMMDD)")
    parser.add_argument("--job-details", help="Show details for a specific job ID")
    parser.add_argument("--cleanup-api-files", action="store_true", 
                        help="Delete ALL uploaded batch files from Mistral API")
    parser.add_argument("--no-delete-api", action="store_true",
                        help="Skip deleting API files during cleanup")
    parser.add_argument("--cancel-all-jobs", action="store_true",
                        help="Cancel ALL batch jobs in Mistral API")
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = RobustMistralOCR(data_dir=args.data_dir)
    
    if args.cleanup_api_files:
        # Delete all API files
        processor.cleanup_all_batch_files()
    elif args.cancel_all_jobs:
        # Cancel all batch jobs
        processor.cancel_all_batch_jobs()
    elif args.cleanup is not None:
        # Run cleanup with API file deletion option
        delete_api = not args.no_delete_api
        processor.cleanup_old_batches(days_to_keep=args.cleanup, delete_api_files=delete_api)
    elif args.billing:
        # Show billing report
        if args.billing_start and args.billing_end:
            processor.show_billing_report(args.billing_start, args.billing_end)
        else:
            processor.show_billing_report()
    elif args.job_details:
        # Show details for specific job
        processor.show_job_details(args.job_details)
    elif args.status:
        # Show status of all jobs
        processor.show_batch_status()
    elif args.recover_deleted:
        # Recover deleted files from existing Mistral jobs
        if not args.start_date or not args.end_date:
            print("Error: Both --start-date and --end-date required for --recover-deleted")
            sys.exit(1)
        processor.recover_deleted_files(args.start_date, args.end_date)
    elif args.recover or (not args.start_date and not args.end_date):
        # Just run recovery
        processor.recover_all_pending()
    else:
        # Process date range (which includes recovery)
        if not args.start_date or not args.end_date:
            print("Error: Both --start-date and --end-date required")
            sys.exit(1)
        
        processor.process_date_range(args.start_date, args.end_date, include_deleted=args.include_deleted)


if __name__ == "__main__":
    main()