#!/usr/bin/env python3
"""Check what data exists in DynamoDB for debugging report generation."""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
import yaml

def load_and_validate_config(config_path, step_name):
    """Simple config loader for the check script."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


async def check_pacer_data(pacer_repo, dates_to_check):
    """Check PACER data for multiple dates."""
    print("\n🔍 Checking PACER data...")
    for date_str in dates_to_check:
        try:
            # Check by filing date
            filing_items = await pacer_repo.query_by_filing_date(date_str)
            print(f"  📅 FilingDate {date_str}: {len(filing_items)} items")
            
            # Check by added date
            added_items = await pacer_repo.query_by_added_on_range(date_str, date_str)
            print(f"  📅 AddedOn {date_str}: {len(added_items)} items")
            
            # Show sample if data exists
            if filing_items:
                sample = filing_items[0]
                print(f"     Sample filing: {sample.get('docket_num', 'N/A')} - {sample.get('title', 'N/A')}")
            if added_items:
                sample = added_items[0]
                print(f"     Sample added: {sample.get('docket_num', 'N/A')} - {sample.get('title', 'N/A')}")
                
        except Exception as e:
            print(f"  ❌ Error checking {date_str}: {e}")


async def check_fb_ads_data(fb_repo, dates_to_check):
    """Check Facebook ads data for multiple dates."""
    print("\n🔍 Checking Facebook Ads data...")
    for date_str in dates_to_check:
        try:
            items = await fb_repo.query_by_start_date(date_str)
            print(f"  📅 StartDate {date_str}: {len(items)} ads")
            
            if items:
                sample = items[0]
                print(f"     Sample ad: {sample.get('page_name', 'N/A')} - {sample.get('ad_archive_id', 'N/A')}")
                
        except Exception as e:
            print(f"  ❌ Error checking {date_str}: {e}")


async def main():
    """Main function to check database data."""
    # Load config
    config_dict = load_and_validate_config('config/report.yml', 'generate_report')
    
    # Generate dates to check
    base_date = datetime(2025, 6, 6)  # June 6, 2025
    dates_to_check = []
    
    # Check 7 days before and after the target date
    for i in range(-7, 8):
        check_date = base_date + timedelta(days=i)
        dates_to_check.append(check_date.strftime('%Y%m%d'))
    
    print(f"🗓️  Checking dates around {base_date.strftime('%Y-%m-%d')}...")
    print(f"   Dates: {dates_to_check}")
    
    # Initialize storage
    storage = AsyncDynamoDBStorage(config_dict)
    
    async with storage:
        # Initialize repositories
        pacer_repo = PacerRepository(storage)
        fb_repo = FBArchiveRepository(storage)
        
        # Check PACER data
        await check_pacer_data(pacer_repo, dates_to_check)
        
        # Check FB ads data
        await check_fb_ads_data(fb_repo, dates_to_check)
        
        # Also check a known date with data (if we know one)
        print("\n🔍 Checking known dates with data...")
        known_dates = ['20240606', '20250617']  # Add any known dates here
        
        for date_str in known_dates:
            print(f"\n  Checking {date_str}:")
            filing_items = await pacer_repo.query_by_filing_date(date_str)
            added_items = await pacer_repo.query_by_added_on_range(date_str, date_str)
            fb_items = await fb_repo.query_by_start_date(date_str)
            
            print(f"    PACER FilingDate: {len(filing_items)} items")
            print(f"    PACER AddedOn: {len(added_items)} items")
            print(f"    FB Ads: {len(fb_items)} items")


if __name__ == "__main__":
    asyncio.run(main())