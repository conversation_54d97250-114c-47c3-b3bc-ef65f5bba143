#!/usr/bin/env python3
"""Get docket info with base filename mapping grouped by court_id."""

import json
from pathlib import Path
from collections import defaultdict

data_dir = "data/20250617/dockets"
results = defaultdict(dict)

for json_file in Path(data_dir).glob("*.json"):
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
            court_id = data.get('court_id')
            docket_num = data.get('docket_num')
            if court_id and docket_num:
                # Get base filename without extension
                base_filename = json_file.stem
                results[court_id][docket_num] = base_filename
    except:
        pass

# Print grouped by court_id
for court_id in sorted(results.keys()):
    print(f"{court_id}:")
    for docket_num in sorted(results[court_id].keys()):
        print(f"  {docket_num} : {results[court_id][docket_num]}")
    print()