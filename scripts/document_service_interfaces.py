import ast
import importlib.util
from pathlib import Path
import json
import argparse
from typing import Dict, <PERSON>, Tuple, Any, Optional

def get_type_repr(node: Optional[ast.AST]) -> str:
    """Generate a string representation for a type annotation."""
    if node is None:
        return "Any"
    if isinstance(node, ast.Name):
        return node.id
    if isinstance(node, ast.Attribute):
        # Handle cases like "module.ClassName"
        return f"{get_type_repr(node.value)}.{node.attr}"
    if isinstance(node, ast.Subscript):
        # Handle cases like "List[str]" or "Dict[str, int]"
        slice_repr = get_type_repr(node.slice) if not isinstance(node.slice, ast.Tuple) else ", ".join(get_type_repr(elt) for elt in node.slice.elts)
        return f"{get_type_repr(node.value)}[{slice_repr}]"
    if isinstance(node, ast.Constant) and isinstance(node.value, str): # For string literals in annotations (Python 3.7+)
        return node.value
    return ast.unparse(node)

def analyze_service_constructor(service_path: Path) -> Dict[str, Any]:
    """Extract constructor parameters and their types"""
    content = service_path.read_text()
    tree = ast.parse(content)

    analysis_output: Dict[str, Any] = {
        "file_path": str(service_path),
        "class_name": None, # To store the name of the class
        "constructors": [],
        "dependencies": [],
        "errors": []
    }

    class_nodes = [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]

    # Prioritize classes ending with "Service", "Repository", "Client", "Handler", "Orchestrator"
    # This helps in cases where multiple classes might be in a file.
    priority_suffix = ("Service", "Repository", "Client", "Handler", "Orchestrator")
    sorted_class_nodes = sorted(
        class_nodes,
        key=lambda cn: not any(cn.name.endswith(suffix) for suffix in priority_suffix)
    )

    if not sorted_class_nodes:
        analysis_output["errors"].append(f"No class definitions found in {service_path}")
        return analysis_output

    # Analyze the first (highest priority) class definition
    # In many cases, service files contain one primary class.
    class_node = sorted_class_nodes[0]
    analysis_output["class_name"] = class_node.name

    constructor_details: Optional[Dict[str, Any]] = None
    for item in class_node.body:
        if isinstance(item, ast.FunctionDef) and item.name == "__init__":
            constructor_details = {
                "name": "__init__",
                "parameters": [],
                "required_parameters": [],
                "optional_parameters": [],
                "special_patterns": []
            }

            args = item.args
            num_defaults = len(args.defaults)
            num_pos_args = len(args.args)

            # Positional and keyword arguments
            for i, arg in enumerate(args.args):
                if arg.arg == "self":
                    continue

                param_info = {
                    "name": arg.arg,
                    "type": get_type_repr(arg.annotation) if arg.annotation else "Any",
                    "default": None
                }

                default_idx = i - (num_pos_args - num_defaults)
                if default_idx >= 0:
                    try:
                        param_info["default"] = ast.unparse(args.defaults[default_idx])
                    except Exception: # Handle complex default values that ast.unparse might struggle with
                        param_info["default"] = "Complex Default Value"
                    constructor_details["optional_parameters"].append(param_info["name"])
                else:
                    constructor_details["required_parameters"].append(param_info["name"])

                constructor_details["parameters"].append(param_info)

            # *args
            if args.vararg:
                vararg_info = {
                    "name": f"*{args.vararg.arg}",
                    "type": get_type_repr(args.vararg.annotation) if args.vararg.annotation else "Any",
                    "default": None
                }
                constructor_details["parameters"].append(vararg_info)
                constructor_details["special_patterns"].append(f"*{args.vararg.arg} present")

            # Keyword-only arguments (Python 3)
            for i, kwonly_arg in enumerate(args.kwonlyargs):
                param_info = {
                    "name": kwonly_arg.arg,
                    "type": get_type_repr(kwonly_arg.annotation) if kwonly_arg.annotation else "Any",
                    "default": None
                }
                if args.kw_defaults[i] is not None:
                    try:
                        param_info["default"] = ast.unparse(args.kw_defaults[i])
                    except Exception:
                        param_info["default"] = "Complex Default Value"
                    constructor_details["optional_parameters"].append(param_info["name"])
                else: # Required keyword-only argument
                    constructor_details["required_parameters"].append(param_info["name"])
                constructor_details["parameters"].append(param_info)

            # **kwargs
            if args.kwarg:
                kwarg_info = {
                    "name": f"**{args.kwarg.arg}",
                    "type": get_type_repr(args.kwarg.annotation) if args.kwarg.annotation else "Any",
                    "default": None
                }
                constructor_details["parameters"].append(kwarg_info)
                constructor_details["special_patterns"].append(f"**{args.kwarg.arg} present")

            analysis_output["constructors"].append(constructor_details)

            # Basic dependency mapping from constructor params
            current_deps = []
            for p in constructor_details["parameters"]:
                type_str = p["type"].lower()
                name_str = p["name"].lower().replace("*","") # remove * from *args and **kwargs

                # Heuristic for identifying dependencies
                is_dependency_type = any(keyword in type_str for keyword in ["service", "client", "repository", "handler", "manager", "storage", "session"])
                is_dependency_name = any(keyword in name_str for keyword in ["service", "client", "repository", "handler", "config", "logger", "db", "session", "storage", "event", "pacer", "archive"])

                if is_dependency_type or is_dependency_name:
                    # Avoid self-referential or overly generic types like 'str', 'int', 'bool' as dependencies unless part of a config object
                    if p["type"] not in ["Any", "str", "int", "float", "bool", "list", "dict", "tuple", "set", "Path"] or "config" in name_str:
                         current_deps.append({"name": name_str, "type": p["type"], "source": "__init__"})
            analysis_output["dependencies"].extend(current_deps)
            break # Found __init__ for this class

    if not constructor_details : # If no explicit __init__ was found in the primary class
         analysis_output["constructors"].append({
                "name": "__init__ (implicit or inherited)",
                "parameters": [],
                "required_parameters": [],
                "optional_parameters": [],
                "special_patterns": [f"No explicit __init__ found for class {class_node.name}. May use default or inherit."]
            })

    if not analysis_output["constructors"] and not analysis_output["errors"]:
         analysis_output["errors"].append(f"No __init__ method found in class {analysis_output['class_name']} or other parseable classes in {service_path}")

    return analysis_output

def map_service_dependencies(service_path: Path, constructor_analysis: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    Identify all dependencies a service requires.
    Currently, this relies on the dependencies extracted by `analyze_service_constructor`.
    Future enhancements:
    - Analyze factory methods (e.g., @staticmethod create(...))
    - Detect lazy-loaded dependencies (e.g., properties that initialize a dependency on first access)
    - Look for service locator patterns if used (though anti-pattern with DI)
    """
    dependencies = constructor_analysis.get("dependencies", [])

    # Placeholder for circular dependency risk:
    # True circular dependency detection requires a full graph of all services.
    # This function can only note potential self-references or obvious patterns if any.
    # For now, this is a high-level note.
    # constructor_analysis["circular_dependency_risks"] = "Requires full service graph analysis."

    return dependencies

def main():
    parser = argparse.ArgumentParser(description="Analyze Python service files to document interfaces and dependencies.")
    parser.add_argument("service_dir", type=str, help="Directory containing service Python files.")
    parser.add_argument("--output", type=str, default="service_documentation.json", help="Output file for the JSON documentation.")
    parser.add_argument("--glob-pattern", type=str, default="*.py", help="Glob pattern for service files (e.g., '*_service.py').")

    args = parser.parse_args()

    service_dir = Path(args.service_dir)
    if not service_dir.is_dir():
        print(f"Error: {service_dir} is not a valid directory.")
        return

    all_services_documentation = []

    # Use rglob for recursive search
    file_pattern = args.glob_pattern
    print(f"Searching for files matching '{file_pattern}' in '{service_dir}' and its subdirectories...")

    service_files = list(service_dir.rglob(file_pattern))

    if not service_files:
        print(f"No files found matching '{file_pattern}' in '{service_dir}'. Please check the path and pattern.")
        # Try a broader search if the specific one fails, to help user debug
        if file_pattern != "*.py":
            print("Trying with a generic '*.py' pattern...")
            service_files = list(service_dir.rglob("*.py"))
            if not service_files:
                 print(f"Still no .py files found in {service_dir}.")
                 return
            else:
                print(f"Found {len(service_files)} .py files. Consider refining your --glob-pattern.")


    print(f"Found {len(service_files)} files to analyze.")

    for py_file in service_files:
        if py_file.name == "__init__.py" and py_file.parent == service_dir : # Avoid analyzing top-level __init__.py if service_dir itself
            print(f"Skipping top-level __init__.py: {py_file}")
            continue
        print(f"Analyzing {py_file}...")
        try:
            constructor_info = analyze_service_constructor(py_file)
            # map_service_dependencies currently uses data from constructor_info
            # If it did its own parsing, it would be:
            # constructor_info["dependencies"].extend(map_service_dependencies(py_file, constructor_info))
            # but for now, analyze_service_constructor already populates 'dependencies'.

            all_services_documentation.append(constructor_info)
        except SyntaxError as se:
            print(f"SyntaxError analyzing {py_file}: {se}")
            all_services_documentation.append({
                "file_path": str(py_file),
                "class_name": "Unknown (Syntax Error)",
                "constructors": [],
                "dependencies": [],
                "errors": [f"SyntaxError: {se.msg} (line {se.lineno}, offset {se.offset})"]
            })
        except Exception as e:
            print(f"Error analyzing {py_file}: {e}")
            all_services_documentation.append({
                "file_path": str(py_file),
                "class_name": "Unknown (Error)",
                "constructors": [],
                "dependencies": [],
                "errors": [f"Failed to parse or analyze: {str(e)}"]
            })

    output_path = Path(args.output)
    try:
        with output_path.open("w") as f:
            json.dump(all_services_documentation, f, indent=2)
        print(f"\nDocumentation generated: {output_path.resolve()}")
    except Exception as e:
        print(f"\nError writing output to {output_path}: {e}")

    print(f"Analyzed {len(all_services_documentation)} files targeted by the pattern.")

if __name__ == "__main__":
    main()
