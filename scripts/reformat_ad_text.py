#!/usr/bin/env python3
"""
Script to reformat Facebook ad body text using Ollama Mistral.
Loads 100 items from local DynamoDB FBAdArchive and reformats text concurrently.
"""
import asyncio
import logging
import os
import sys
from typing import List, Dict, Any
import aiohttp
from aiohttp import ClientTimeout

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.config_models.base import WorkflowConfig
from src.config_models.features import FeatureFlags

# Rebuild the model after importing FeatureFlags
WorkflowConfig.model_rebuild()


class OllamaMistralClient:
    """Client for Ollama Mistral API."""
    
    def __init__(self, base_url: str = "http://localhost:11434", timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.model = "mistral:latest"
        self.timeout = timeout
        self._session = None
        self.logger = logging.getLogger(__name__)
    
    async def __aenter__(self):
        """Async context manager entry."""
        connector = aiohttp.TCPConnector(limit_per_host=10)
        self._session = aiohttp.ClientSession(
            connector=connector,
            timeout=ClientTimeout(total=self.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session:
            await self._session.close()
    
    async def reformat_text(self, text: str) -> str:
        """
        Reformat Facebook ad copy text using Mistral.
        
        Args:
            text: Original ad text
            
        Returns:
            Reformatted text
        """
        if not text or not text.strip():
            return text
        
        prompt = f"""Add proper line breaks to the following Facebook ad text where needed. DO NOT change any words, DO NOT add explanations, DO NOT add formatting like bullets or bold. ONLY add line breaks between sentences and sections where missing.

Original text:
{text}

Reformatted text:"""
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_ctx": 4096,
                "num_predict": 1024
            }
        }
        
        try:
            async with self._session.post(
                f"{self.base_url}/api/generate",
                json=payload
            ) as response:
                response.raise_for_status()
                data = await response.json()
                
                if 'response' in data:
                    return data['response'].strip()
                else:
                    self.logger.error(f"Unexpected response format: {data}")
                    return text  # Return original if reformatting fails
                    
        except Exception as e:
            self.logger.error(f"Error reformatting text: {e}")
            return text  # Return original if reformatting fails


class AdTextReformatter:
    """Main class for reformatting ad text."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('@cache/reformat_ad_text.log')
            ]
        )
    
    def load_ads_from_dynamodb(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Load ads from local DynamoDB using boto3 directly.
        
        Args:
            limit: Maximum number of ads to load
            
        Returns:
            List of ad records
        """
        import boto3
        from boto3.dynamodb.conditions import Attr
        
        try:
            print(f"Loading up to {limit} ads from DynamoDB...")
            
            # Connect directly to local DynamoDB
            dynamodb = boto3.resource('dynamodb', endpoint_url='http://localhost:8000', region_name='us-west-2')
            table = dynamodb.Table('FBAdArchive')
            
            # Scan with filter for records that have body text
            response = table.scan(
                FilterExpression=Attr('Body').exists(),
                Limit=limit
            )
            
            ads = []
            for item in response['Items']:
                # Convert DynamoDB item to our format
                ad = {
                    'ad_archive_id': item.get('AdArchiveID', ''),
                    'start_date': item.get('StartDate', ''),
                    'body': item.get('Body', ''),
                    'page_name': item.get('PageName', ''),
                    'law_firm': item.get('LawFirm', ''),
                    'page_id': item.get('PageID', '')
                }
                
                if ad['body']:  # Only include ads with body text
                    ads.append(ad)
            
            print(f"✅ Loaded {len(ads)} ads with body text from DynamoDB")
            self.logger.info(f"Loaded {len(ads)} ads with body text")
            return ads
            
        except Exception as e:
            self.logger.error(f"Error connecting to DynamoDB: {e}")
            print(f"\n❌ Error: Could not connect to local DynamoDB: {e}")
            print("Please ensure local DynamoDB is running")
            return []
    
    async def process_ad_batch(self, ads: List[Dict[str, Any]], ollama_client: OllamaMistralClient) -> List[Dict[str, Any]]:
        """
        Process a batch of ads concurrently with progress tracking.
        
        Args:
            ads: List of ad records
            ollama_client: Ollama client instance
            
        Returns:
            List of processed ads with reformatted text
        """
        from rich.progress import Progress, TaskID
        import time
        
        total_ads = len(ads)
        completed = 0
        processed_ads = []
        
        # Create progress tracking
        with Progress() as progress:
            task_id = progress.add_task("[green]Processing ads...", total=total_ads)
            
            # Create semaphore to limit concurrent requests (prevent overwhelming Ollama)
            semaphore = asyncio.Semaphore(10)  # Process 10 at a time
            
            async def process_with_progress(ad, index):
                nonlocal completed
                async with semaphore:
                    try:
                        result = await self.process_single_ad(ad, ollama_client, show_console=False)
                        completed += 1
                        progress.update(task_id, advance=1)
                        
                        # Print progress
                        percentage = (completed / total_ads) * 100
                        print(f"🚀 [{completed}/{total_ads}] ({percentage:.1f}%) Completed ad {ad.get('ad_archive_id', 'unknown')}")
                        
                        return result
                    except Exception as e:
                        completed += 1
                        progress.update(task_id, advance=1)
                        percentage = (completed / total_ads) * 100
                        print(f"❌ [{completed}/{total_ads}] ({percentage:.1f}%) Failed ad {ad.get('ad_archive_id', 'unknown')}: {e}")
                        return ad  # Return original ad if processing failed
            
            # Create tasks for all ads
            tasks = [process_with_progress(ad, i) for i, ad in enumerate(ads)]
            
            # Process all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Collect results
            for result in results:
                if not isinstance(result, Exception):
                    processed_ads.append(result)
        
        print(f"\n🎉 Processing complete! {len(processed_ads)}/{total_ads} ads successfully processed")
        return processed_ads
    
    async def process_single_ad(self, ad: Dict[str, Any], ollama_client: OllamaMistralClient, show_console: bool = True) -> Dict[str, Any]:
        """
        Process a single ad record.
        
        Args:
            ad: Ad record
            ollama_client: Ollama client instance
            
        Returns:
            Processed ad with reformatted text
        """
        ad_id = ad.get('ad_archive_id', 'unknown')
        original_body = ad.get('body', '')
        
        if not original_body:
            self.logger.warning(f"Ad {ad_id} has no body text")
            return ad
        
        if show_console:
            print(f"📝 Processing ad {ad_id} (body length: {len(original_body)})")
        self.logger.info(f"Processing ad {ad_id} (body length: {len(original_body)})")
        
        try:
            reformatted_body = await ollama_client.reformat_text(original_body)
            
            # Create new ad record with reformatted text
            processed_ad = ad.copy()
            processed_ad['body_original'] = original_body
            processed_ad['body_reformatted'] = reformatted_body
            
            # Log changes if text was modified
            if original_body != reformatted_body:
                if show_console:
                    print(f"✅ Ad {ad_id}: Text reformatted ({len(original_body)} → {len(reformatted_body)} chars)")
                    print(f"🔴 BEFORE: {original_body}")
                    print(f"🟢 AFTER:  {reformatted_body}")
                    print("-" * 80)
                self.logger.info(f"Ad {ad_id}: Text reformatted (original: {len(original_body)} chars, reformatted: {len(reformatted_body)} chars)")
            else:
                if show_console:
                    print(f"⚡ Ad {ad_id}: No reformatting needed")
                    print(f"📄 TEXT: {original_body}")
                    print("-" * 80)
                self.logger.info(f"Ad {ad_id}: No reformatting needed")
            
            return processed_ad
            
        except Exception as e:
            self.logger.error(f"Error processing ad {ad_id}: {e}")
            return ad
    
    async def save_results(self, processed_ads: List[Dict[str, Any]]):
        """
        Save results to a file.
        
        Args:
            processed_ads: List of processed ad records
        """
        import json
        from datetime import datetime
        
        # Create cache directory if it doesn't exist
        os.makedirs('@cache', exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"@cache/reformatted_ads_{timestamp}.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processed_ads, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Results saved to {output_file}")
            
            # Print summary
            total_ads = len(processed_ads)
            reformatted_count = sum(1 for ad in processed_ads if ad.get('body_reformatted') and ad.get('body_original') != ad.get('body_reformatted'))
            
            print(f"\n--- Summary ---")
            print(f"Total ads processed: {total_ads}")
            print(f"Ads reformatted: {reformatted_count}")
            print(f"Results saved to: {output_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving results: {e}")
    
    def create_sample_ads(self) -> List[Dict[str, Any]]:
        """Create sample ad data for testing when DynamoDB isn't available."""
        return [
            {
                "ad_archive_id": "sample_001",
                "start_date": "20250630",
                "body": "Are you suffering from mesothelioma?Contact our law firm today for a free consultation.We have recovered millions for our clients.",
                "page_name": "Sample Law Firm"
            },
            {
                "ad_archive_id": "sample_002", 
                "start_date": "20250630",
                "body": "ATTENTION:Roundup users may be entitled to compensation.Call now for your free case evaluation.No fees unless we win.",
                "page_name": "Another Law Firm"
            },
            {
                "ad_archive_id": "sample_003",
                "start_date": "20250630", 
                "body": "If you took Zantac and developed cancer,you may have a legal case.Our experienced attorneys are here to help.Free consultation available.",
                "page_name": "Legal Eagles"
            }
        ]

    async def run(self):
        """Main execution method."""
        print("Starting Facebook Ad Text Reformatter...")
        print("Using Ollama Mistral for text reformatting")
        
        try:
            # Load ads from DynamoDB
            ads = self.load_ads_from_dynamodb(100)
            
            if not ads:
                print("\nNo ads loaded from DynamoDB.")
                print("Using sample data for demonstration...")
                ads = self.create_sample_ads()
            
            # Process ads with Ollama
            async with OllamaMistralClient() as ollama_client:
                print(f"Processing {len(ads)} ads concurrently...")
                processed_ads = await self.process_ad_batch(ads, ollama_client)
            
            # Save results
            await self.save_results(processed_ads)
            
            print("Processing completed!")
            
        except Exception as e:
            self.logger.error(f"Error in main execution: {e}")
            print(f"Error: {e}")


async def main():
    """Main entry point."""
    reformatter = AdTextReformatter()
    await reformatter.run()


if __name__ == "__main__":
    asyncio.run(main())