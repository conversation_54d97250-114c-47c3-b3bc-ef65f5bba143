import argparse
import json
import os
import re

import nltk
import numpy as np
import pandas as pd
from nltk import ngrams
from nltk.corpus import stopwords
from nltk.tokenize import sent_tokenize, word_tokenize

import logging
from config import load_config

# nltk.download('punkt')
# nltk.download('stopwords')


class LitigationDataProcessor:
    def __init__(self, config, pacer_path, mdl_court_path, docket_entries_dir, mdl_num=None):
        self.pacer = pacer_path
        self.mdl_court = mdl_court_path
        self.docket_entries = docket_entries_dir
        self.mdl_num = mdl_num
        self.docket_dfs = {}
        self.pacer_df = None
        self.mdl_court_df = None
        self.stop_words = set(stopwords.words('english'))
        self.config = config
        self.logger = logging.getLogger(__name__)

    def load_data(self):
        self._load_docket_entries()
        self._load_pacer_data()
        self._load_mdl_court_data()

    def get_docket_entries_df(self):
        """
        Returns a DataFrame containing specific columns from docket entries.
        """
        columns = [
            'mdl_num',  # Added mdl_num to the list of columns
            'docketentry_date_filed',
            'docketentry_entry_number',
            'docketentry_description',
            'recapdocument_document_type',
            'recapdocument_description',
            'recapdocument_date_upload',
            'recapdocument_is_available',
            'recapdocument_filepath_local',
            'recapdocument_filepath_ia'
        ]

        all_docket_entries = pd.concat(self.docket_dfs.values(), ignore_index=True)
        return all_docket_entries[columns]

    def get_docketentry_description_counts(self):
        """
        Returns a DataFrame with value counts of categorized docketentry_description,
        grouped by mdl_num.
        """
        all_docket_entries = self.get_docket_entries_df()

        def categorize_description(description):
            if pd.isna(description):
                return np.nan
            words = description.split()
            category = []
            for word in words:
                if word.isupper() or word.isdigit() or (word.isupper() and word.endswith('.')):
                    category.append(word)
                else:
                    break
            return ' '.join(category)

        all_docket_entries['category'] = all_docket_entries['docketentry_description'].apply(categorize_description)

        # Group by mdl_num and category, then count
        category_counts = all_docket_entries.groupby(['mdl_num', 'category']).size().unstack(fill_value=0)

        # Add a total column
        category_counts['Total'] = category_counts.sum(axis=1)

        # Sort the DataFrame by the Total column in descending order
        category_counts = category_counts.sort_values('Total', ascending=False)

        return category_counts

    def _load_docket_entries(self):
        for file in os.listdir(self.docket_entries):
            if file.endswith('.csv'):
                current_mdl_num = file.split('.')[0]
                if self.mdl_num is None or current_mdl_num == self.mdl_num:
                    df = pd.read_csv(os.path.join(self.docket_entries, file))
                    df['mdl_num'] = current_mdl_num
                    self._preprocess_docket_df(df)
                    self.docket_dfs[current_mdl_num] = df

    def _load_pacer_data(self):
        with open(self.pacer, 'r') as f:
            pacer_data = json.load(f)
        self.pacer_df = pd.DataFrame(pacer_data)
        self._preprocess_source_df(self.pacer_df)

    def _load_mdl_court_data(self):
        with open(self.mdl_court, 'r') as f:
            mdl_court_data = json.load(f)
        if self.mdl_num:
            mdl_court_list = mdl_court_data.get(self.mdl_num, [])
        else:
            mdl_court_list = [item for sublist in mdl_court_data.values() for item in sublist]
        self.mdl_court_df = pd.DataFrame(mdl_court_list)
        self._preprocess_source_df(self.mdl_court_df)

    def _preprocess_docket_df(self, df):
        df['docketentry_date_filed'] = pd.to_datetime(df['docketentry_date_filed']).dt.strftime('%Y-%m-%d')
        df['docketentry_entry_number'] = df['docketentry_entry_number'].astype(str).str.split('.').str[0]
        df['recapdocument_attachment_number'] = df['recapdocument_attachment_number'].fillna('').astype(str)

    def _preprocess_source_df(self, df):
        df.replace({'NA': np.nan, 'null': np.nan, 'none': np.nan}, inplace=True)
        df['filing_date'] = pd.to_datetime(df['filing_date'], errors='coerce').dt.strftime('%Y-%m-%d')
        df['doc_num'] = df['doc_num'].astype(str)
        df['entry_num'] = df['doc_num'].str.split('-').str[0]
        df['attachment_num'] = df['doc_num'].str.split('-').str[1].fillna('')

    def generate_comparison_dataframes(self):
        pacer_comparison = self._generate_comparison(self.pacer_df, 'pacer')
        mdl_court_comparison = self._generate_comparison(self.mdl_court_df, 'mdl_court')
        return pd.DataFrame(pacer_comparison), pd.DataFrame(mdl_court_comparison)

    def _generate_comparison(self, source_df, source_name):
        comparison = []
        for mdl_num, docket_df in self.docket_dfs.items():
            subset = source_df[source_df['mdl_num'] == mdl_num]
            for _, row in subset.iterrows():
                comparison_row = row.to_dict()
                comparison_row['source'] = source_name
                comparison_row['match_status'] = self._get_match_status(row, docket_df)
                comparison.append(comparison_row)
        return comparison

    def _get_match_status(self, row, docket_df):
        if pd.isna(row['doc_num']) or pd.isna(row['filing_date']):
            return 'Invalid data'

        match = docket_df[
            (docket_df['docketentry_entry_number'] == row['entry_num']) &
            (docket_df['docketentry_date_filed'] == row['filing_date'])
            ]

        if match.empty:
            return 'Unmatched'

        if row['attachment_num']:
            if match['recapdocument_attachment_number'].eq(row['attachment_num']).any():
                return 'Matched'
            else:
                return 'Unmatched - different attachment'

        return 'Matched'

    @staticmethod
    def generate_summary(comparison_df):
        summary = comparison_df.groupby(['source', 'mdl_num'])['match_status'].value_counts().unstack(fill_value=0)
        summary['total'] = summary.sum(axis=1)
        summary['match_percentage'] = summary['Matched'] / summary['total'] * 100
        return summary

    @staticmethod
    def generate_unmatched_report(comparison_df):
        unmatched = comparison_df[
            comparison_df['match_status'].isin(['Unmatched', 'Unmatched - different attachment', 'Invalid data'])]
        return unmatched[['source', 'mdl_num', 'filing_date', 'doc_num', 'match_status', 'description']]

    def get_docketentry_description_counts_by_mdl(self):
        """
        Returns a dictionary of DataFrames with value counts of categorized docketentry_description,
        one DataFrame per MDL number.
        """
        all_docket_entries = self.get_docket_entries_df()

        def categorize_description(description):
            if pd.isna(description):
                return np.nan
            words = description.split()
            category = []
            for word in words:
                if word.isupper() or word.isdigit() or (word.isupper() and word.endswith('.')):
                    category.append(word)
                else:
                    break
            return ' '.join(category)

        all_docket_entries['category'] = all_docket_entries['docketentry_description'].apply(categorize_description)

        mdl_summaries = {}

        for mdl_num in all_docket_entries['mdl_num'].unique():
            mdl_data = all_docket_entries[all_docket_entries['mdl_num'] == mdl_num]
            category_counts = mdl_data['category'].value_counts()
            total = category_counts.sum()

            summary_df = pd.DataFrame({
                'Category': ['Total'] + category_counts.index.tolist(),
                'Count': [total] + category_counts.values.tolist()
            })

            mdl_summaries[mdl_num] = summary_df

        return mdl_summaries

    def categorize_description(self, description):
        if pd.isna(description):
            return np.nan
        words = description.split()
        category = []
        for word in words:
            if word.isupper() or word.isdigit() or (word.isupper() and word.endswith('.')):
                category.append(word)
            else:
                break
        return ' '.join(category)

    def extract_capitalized_parts(self, text):
        if not isinstance(text, str):
            self.logger.warning(f"Non-string value encountered: {text}")
            return ""

        # Split the text into sentences
        sentences = sent_tokenize(text)

        # Extract capitalized parts
        capitalized_parts = []

        # Process the first sentence
        first_sentence = sentences[0] if sentences else ""
        words = word_tokenize(first_sentence)
        initial_caps = []
        for word in words:
            if word.isupper() or word[0].isupper():
                initial_caps.append(word)
            else:
                break
        capitalized_parts.extend(initial_caps)

        # Process the rest of the sentences
        for sentence in sentences:
            # Find all fully capitalized words or sentences
            caps = re.findall(r'\b[A-Z][A-Z]+\b|\b[A-Z][a-z]+\b|[A-Z][A-Z\s]+[.]', sentence)
            capitalized_parts.extend(caps)

        return ' '.join(capitalized_parts)

    def generate_ngrams(self, text, n):
        extracted_text = self.extract_capitalized_parts(text)
        if not extracted_text:
            return []

        tokens = word_tokenize(extracted_text.lower())
        tokens = [token for token in tokens if token.isalnum() and token not in self.stop_words]

        n_grams = list(ngrams(tokens, n))
        return [' '.join(gram) for gram in n_grams]

    def generate_ngram_report(self, output_file, n_gram=1, top_n=10, only_filepaths=False):
        all_docket_entries = self.get_docket_entries_df()

        if only_filepaths:
            filtered_entries = all_docket_entries[
                all_docket_entries['recapdocument_filepath_local'].notna() |
                all_docket_entries['recapdocument_filepath_ia'].notna()
                ]
        else:
            filtered_entries = all_docket_entries

        # Generate n-grams
        filtered_entries['ngrams'] = filtered_entries['docketentry_description'].apply(
            lambda x: self.generate_ngrams(x, n_gram)
        )

        # Explode the n-grams into separate rows
        exploded_ngrams = filtered_entries.explode('ngrams')

        # Remove rows with empty n-grams
        exploded_ngrams = exploded_ngrams[exploded_ngrams['ngrams'].notna() & (exploded_ngrams['ngrams'] != '')]

        # Generate summary
        summary = exploded_ngrams['ngrams'].value_counts().reset_index()
        summary.columns = ['ngram', 'count']

        # Calculate percentage
        total_entries = len(filtered_entries)
        summary['percentage'] = (summary['count'] / total_entries) * 100

        # Sort by count in descending order
        summary = summary.sort_values('count', ascending=False)

        # Save full summary to CSV
        summary.to_csv(output_file, index=False)

        print(f"N-gram report saved to {output_file}")
        print(f"Total unique {n_gram}-grams: {len(summary)}")
        print(f"Total entries processed: {total_entries}")
        print(f"\nTop {top_n} {n_gram}-grams:")
        print(summary.head(top_n).to_string(index=False))

    def generate_filepath_report(self, output_file, summarize=False, n_gram=1):
        """
        Generates a CSV file for all docket entries where either
        recapdocument_filepath_local or recapdocument_filepath_ia is not null.
        Optionally summarizes the entries by n-grams of docketentry_description across all MDLs.

        :param output_file: str, the name of the output CSV file
        :param summarize: bool, whether to generate a summary by n-grams
        :param n_gram: int, the size of n-grams to generate (default is 1, which is equivalent to the previous categorization)
        """
        all_docket_entries = self.get_docket_entries_df()

        # Filter entries where either filepath is not null
        filtered_entries = all_docket_entries[
            all_docket_entries['recapdocument_filepath_local'].notna() |
            all_docket_entries['recapdocument_filepath_ia'].notna()
            ]

        if summarize:
            # Generate n-grams
            filtered_entries['ngrams'] = filtered_entries['docketentry_description'].apply(
                lambda x: self.generate_ngrams(x, n_gram)
            )

            # Explode the n-grams into separate rows
            exploded_ngrams = filtered_entries.explode('ngrams')

            # Remove rows with empty n-grams
            exploded_ngrams = exploded_ngrams[exploded_ngrams['ngrams'].notna() & (exploded_ngrams['ngrams'] != '')]

            # Generate summary across all MDLs
            summary = exploded_ngrams['ngrams'].value_counts().reset_index()
            summary.columns = ['ngram', 'count']

            # Calculate percentage
            total_entries = len(filtered_entries)
            summary['percentage'] = (summary['count'] / total_entries) * 100

            # Sort by count in descending order
            summary = summary.sort_values('count', ascending=False)

            # Save summary to CSV
            summary.to_csv(output_file, index=False)
            print(f"Filepath summary report with {n_gram}-grams saved to {output_file}")
            print(f"Total unique {n_gram}-grams: {len(summary)}")
            print(f"Total entries with filepaths: {total_entries}")
        else:
            # Sort by docketentry_date_filed for the full report
            filtered_entries = filtered_entries.sort_values('docketentry_date_filed')

            # Save full report to CSV
            filtered_entries.to_csv(output_file, index=False)
            print(f"Filepath report saved to {output_file}")
            print(f"Total entries with filepaths: {len(filtered_entries)}")

    # def generate_filepath_report(self, output_file, summarize=False, n_gram=1):
    #     """
    #     Generates a CSV file for all docket entries where either
    #     recapdocument_filepath_local or recapdocument_filepath_ia is not null.
    #     Optionally summarizes the entries by n-grams of docketentry_description across all MDLs.
    #
    #     :param output_file: str, the name of the output CSV file
    #     :param summarize: bool, whether to generate a summary by n-grams
    #     :param n_gram: int, the size of n-grams to generate (default is 1, which is equivalent to the previous categorization)
    #     """
    #     all_docket_entries = self.get_docket_entries_df()
    #
    #     # Filter entries where either filepath is not null
    #     filtered_entries = all_docket_entries[
    #         all_docket_entries['recapdocument_filepath_local'].notna() |
    #         all_docket_entries['recapdocument_filepath_ia'].notna()
    #         ]
    #
    #     if summarize:
    #         # Generate n-grams
    #         filtered_entries['ngrams'] = filtered_entries['docketentry_description'].apply(
    #             lambda x: self.generate_ngrams(x, n_gram)
    #         )
    #
    #         # Explode the n-grams into separate rows
    #         exploded_ngrams = filtered_entries.explode('ngrams')
    #
    #         # Remove rows with empty n-grams
    #         exploded_ngrams = exploded_ngrams[exploded_ngrams['ngrams'].notna() & (exploded_ngrams['ngrams'] != '')]
    #
    #         # Generate summary across all MDLs
    #         summary = exploded_ngrams['ngrams'].value_counts().reset_index()
    #         summary.columns = ['ngram', 'count']
    #
    #         # Calculate percentage
    #         total_entries = len(filtered_entries)
    #         summary['percentage'] = (summary['count'] / total_entries) * 100
    #
    #         # Sort by count in descending order
    #         summary = summary.sort_values('count', ascending=False)
    #
    #         # Save summary to CSV
    #         summary.to_csv(output_file, index=False)
    #         print(f"Filepath summary report with {n_gram}-grams saved to {output_file}")
    #         print(f"Total unique {n_gram}-grams: {len(summary)}")
    #         print(f"Total entries with filepaths: {total_entries}")
    #     else:
    #         # Sort by docketentry_date_filed for the full report
    #         filtered_entries = filtered_entries.sort_values('docketentry_date_filed')
    #
    #         # Save full report to CSV
    #         filtered_entries.to_csv(output_file, index=False)
    #         print(f"Filepath report saved to {output_file}")
    #         print(f"Total entries with filepaths: {len(filtered_entries)}")


def main(args):
    config = load_config('01/01/70')

    processor = LitigationDataProcessor(
        config=config,
        pacer_path='//combined_pacer_orders.json',
        mdl_court_path='/Users/<USER>/PycharmProjects/mt_competitive_analysis/src/lib/court_mdl_data/combined_mdl_orders.json',
        docket_entries_dir='/',
        mdl_num=args.mdl_num
    )

    processor.load_data()

    output_prefix = f"mdl_{args.mdl_num}_" if args.mdl_num else ""

    if args.docket_entries or args.all:
        docket_entries_df = processor.get_docket_entries_df()
        print("\nDocket Entries DataFrame:")
        print(docket_entries_df.head())
        docket_entries_df.to_csv(f'{output_prefix}docket_entries.csv', index=False)

    if args.description_counts or args.all:
        description_counts = processor.get_docketentry_description_counts()
        print("\nDocket Entry Description Counts by MDL:")
        print(description_counts.head(20))  # Show top 20 rows
        description_counts.to_csv(f'{output_prefix}docket_entry_categories_by_mdl.csv')

    if args.mdl_specific_counts or args.all:
        mdl_summaries = processor.get_docketentry_description_counts_by_mdl()
        for mdl_num, summary_df in mdl_summaries.items():
            if args.mdl_num is None or args.mdl_num == mdl_num:
                print(f"\nCategory Summary for MDL {mdl_num}:")
                print(summary_df.to_string(index=False))
                summary_df.to_csv(f'{output_prefix}mdl_{mdl_num}_category_summary.csv', index=False)

    if args.comparison or args.all:
        pacer_comparison, mdl_court_comparison = processor.generate_comparison_dataframes()
        all_comparison = pd.concat([pacer_comparison, mdl_court_comparison])

        summary = processor.generate_summary(all_comparison)
        print("\nSummary of Matches and Unmatched Items:")
        print(summary)
        summary.to_csv(f'{output_prefix}summary.csv')

        unmatched_report = processor.generate_unmatched_report(all_comparison)
        print("\nDetailed Report of Unmatched Items:")
        print(unmatched_report)
        unmatched_report.to_csv(f'{output_prefix}unmatched_report.csv', index=False)

        pacer_comparison.to_csv(f'{output_prefix}pacer_comparison.csv', index=False)
        mdl_court_comparison.to_csv(f'{output_prefix}mdl_court_comparison.csv', index=False)

    if args.ngram_report or args.all:
        processor.generate_ngram_report(
            f'{output_prefix}ngram_report_{args.ngram}gram{"_filepaths" if args.only_filepaths else ""}.csv',
            n_gram=args.ngram,
            top_n=args.top_n,
            only_filepaths=args.only_filepaths
        )

    if args.filepath_report or args.all:
        if args.summarize_filepath:
            processor.generate_filepath_report(f'{output_prefix}filepath_summary_{args.ngram}gram.csv',
                                               summarize=True, n_gram=args.ngram)
        else:
            processor.generate_filepath_report(f'{output_prefix}filepath_report.csv', summarize=False)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Process litigation data.')
    parser.add_argument('--mdl_num', type=str, help='Specific MDL number to process')
    parser.add_argument('-m', '--main', action='store_true', help='Run the main function')
    parser.add_argument('--docket_entries', action='store_true', help='Generate docket entries report')
    parser.add_argument('--description_counts', action='store_true', help='Generate description counts report')
    parser.add_argument('--mdl_specific_counts', action='store_true', help='Generate MDL-specific category counts')
    parser.add_argument('--comparison', action='store_true', help='Generate comparison report')
    parser.add_argument('--filepath_report', action='store_true', help='Generate report for entries with filepaths')
    parser.add_argument('--summarize_filepath', action='store_true', help='Summarize filepath report by n-grams')
    parser.add_argument('--ngram_report', action='store_true', help='Generate n-gram report')
    parser.add_argument('--ngram', type=int, default=1, help='Size of n-grams to generate (default: 1)')
    parser.add_argument('--top_n', type=int, default=10, help='Number of top n-grams to display (default: 10)')
    parser.add_argument('--only_filepaths', action='store_true', help='Only consider entries with filepaths')
    parser.add_argument('--all', action='store_true', help='Generate all reports')

    args = parser.parse_args()

    if args.main or not any([args.docket_entries, args.description_counts, args.mdl_specific_counts, args.comparison,
                             args.filepath_report, args.ngram_report, args.all]):
        main(args)
    else:
        main(args)
