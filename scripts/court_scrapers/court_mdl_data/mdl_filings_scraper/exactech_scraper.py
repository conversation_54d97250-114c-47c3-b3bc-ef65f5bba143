import re
import time  # Import for adding delay
import unicodedata
from pprint import pformat
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import Select  # For dropdown interaction
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options


from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor


class ExactechScraper:
    def __init__(self, config, url, mdl_num):
        self.url = url
        self.mdl_num = mdl_num
        self.root_url = f"https://www.exactechmdlfilings.com/mdl-{mdl_num}/"
        log_setup = LoggerSetup(config, 'ExactechScraperLogger', 'ExactechScraper.log')
        self.logger = log_setup.get_logger()
        self.pdf_extractor = PDFExtractor(config, '')  # Instantiate PDFExtractor
        self.driver = None

    def fetch_content(self, url):
        self.logger.info(f"Fetching content from URL: {url}")
        try:
            response = requests.get(url)
            response.raise_for_status()
            self.logger.info("Content fetched successfully.")
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    def setup_selenium(self):
        self.logger.info("Setting up Selenium WebDriver in visible mode...")

        # Set Chrome options for visible mode (remove headless option)
        chrome_options = Options()
        # Comment out the headless mode to see the browser window
        # chrome_options.add_argument("--headless")
        chrome_options.add_argument("--disable-gpu")  # Disable GPU acceleration
        chrome_options.add_argument("--no-sandbox")  # Required in some environments
        chrome_options.add_argument("--disable-dev-shm-usage")  # Overcome limited resource problems
        chrome_options.add_argument('--headless')

        # Set up the WebDriver with the options for visible mode
        self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        self.driver.get(self.url)

        # Wait for the dropdown to be present
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.NAME, "ctl00$ContentPlaceHolder1$ddlPageSize"))
        )
        self.logger.info("Selenium WebDriver setup complete in visible mode.")

    def select_dropdown_option(self):
        try:
            self.logger.info("Selecting '100' from the dropdown.")
            select = Select(self.driver.find_element(By.NAME, "ctl00$ContentPlaceHolder1$ddlPageSize"))
            select.select_by_value("100")
            time.sleep(2)
            # Wait for the table to reload completely after selecting "100"
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "ContentPlaceHolder1_gdvPTOrders"))
            )
            self.logger.info("Option '100' selected and page reloaded.")
        except Exception as e:
            self.logger.error(f"Error selecting from dropdown: {e}")

    def clean_description(self, text):
        """Remove non-breaking spaces and clean the text."""
        return text.replace('\xa0', ' ').strip()

    def parse_orders(self, soup):
        self.logger.info("Parsing orders from the HTML content.")
        # Find the correct table and extract data
        rows = soup.find_all("tr")
        results = []

        self.logger.info(f"Found {len(rows)} rows in the table.")

        for idx, row in enumerate(rows):
            self.logger.info(f"Processing row {idx + 1}/{len(rows)}.")
            try:
                # Extract the URL from the 2nd td (the link to the document)
                doc_element = row.find("td", {"class": "text-left align-top"}).find("a", href=True)
                if doc_element:
                    # Append the relative href to the base URL
                    url = urljoin("https://www.exactechmdlfilings.com/", doc_element['href'].strip())
                    doc_title = doc_element.text.strip()

                    # Extract filing date from the 4th td
                    filing_date = row.find_all("td")[3].text.strip()

                    self.logger.info(f"Document Title: {doc_title}, URL: {url}, Filing Date: {filing_date}")

                    result = {
                        "mdl_num": self.mdl_num,
                        "url": url,  # Full URL now includes base URL
                        "doc_title": doc_title,
                        "filing_date": filing_date,
                        "doc_num": "NA"
                    }

                    # Add this entry to results
                    results.append(result)

            except Exception as e:
                self.logger.error(f"Error parsing row {idx + 1}: {e}")

        self.logger.info(f"Parsing complete. {len(results)} results found.")
        return results

    def extract_doc_nums_and_dates(self, result):
        self.logger.info(f"Extracting document number and dates for: {result['url']}")

        if result['url'] == 'https://www.exactechmdlfilings.com/Docs/IndexDocs/Amended_Personal_Injury_Complaint.pdf':
            result['doc_num'] = '164'
            self.logger.info(f"Special case: Set Doc Num to 164 for URL: {result['url']}")
            return result

        # https://www.exactechmdlfilings.com/Docs/IndexDocs/First_Amended_Discovery_Case_Management_Order.pdf
        if result['url'] == 'https://www.exactechmdlfilings.com/Docs/IndexDocs/Amended_Personal_Injury_Complaint.pdf':
            result['doc_num'] = '204'
            self.logger.info(f"Special case: Set Doc Num to 204 for URL: {result['url']}")
            return result

        if 'pdf' in result['url']:
            # Extract text from the PDF using PyMuPDF
            try:
                pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])
                normalized_text = unicodedata.normalize('NFKD', pdf_text)
                cleaned_text = ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

                # Extract the document number using the specified regex
                if not result.get('doc_num') or result.get('doc_num') == 'NA':
                    doc_num_match = re.search(r'\s+[Ee]ntry\s+[Nn]umber\s+(\d+)\s+', cleaned_text)
                    if doc_num_match:
                        doc_num = doc_num_match.group(1)
                        result['doc_num'] = doc_num
                        self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")
                    else:
                        doc_num = self.pdf_extractor.extract_doc_num_with_tesseract(result['url'])
                        if doc_num:
                            result['doc_num'] = doc_num
                        self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")

                # Extract the filing date
                if not result.get('filing_date') or result['filing_date'] == 'NA':
                    filing_date = self.pdf_extractor.extract_filing_date_with_tesseract(result['url'])
                    if filing_date:
                        result['filing_date'] = filing_date
                        self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

            except Exception as e:
                self.logger.error(f"Error extracting document info for {result['url']}: {e}")

            return result

    def process_results(self, results):
        self.logger.info(f"Processing {len(results)} results.")
        for idx, result in enumerate(results):
            self.logger.info(f"Processing result {idx + 1}/{len(results)}.")
            self.extract_doc_nums_and_dates(result)
            self.logger.info(f"Processed result {idx + 1}: {pformat(result)}")  # Log after each iteration
            time.sleep(1 + 2 * time.time() % 1)  # Delay between 1 and 3 seconds
        return results

    def run(self):
        self.logger.info(f"Starting scraper for {self.url}")

        # Setup Selenium WebDriver
        self.setup_selenium()

        # Select 100 items per page from the dropdown
        self.select_dropdown_option()

        # Get the updated page source after selecting the dropdown
        page_source = self.driver.page_source

        # Parse the page with BeautifulSoup
        soup = BeautifulSoup(page_source, 'html.parser')

        if soup:
            # Now ensure all rows are being captured correctly
            results = self.parse_orders(soup)
            self.logger.info(f"Starting to process {len(results)} parsed orders.")
            processed_results = self.process_results(results)
            self.logger.info(f"Final processed results: {pformat(processed_results)}")
            return processed_results
        else:
            self.logger.error("Failed to fetch or parse the webpage.")
        return []


if __name__ == "__main__":
    config = load_config('01/01/1970')

    mdl_info = [
        ('exactech', "https://www.exactechmdlfilings.com/", '3044')
    ]

    for mdl in mdl_info:
        court_id, url, mdl_num = mdl

    scraper = ExactechScraper(config, url, mdl_num)
    results = scraper.run()
