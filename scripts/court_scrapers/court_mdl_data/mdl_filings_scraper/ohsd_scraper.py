import re

import requests
from bs4 import BeautifulSoup

from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor

from pprint import pformat

class OHSDScraper:
    def __init__(self, config, url, mdl_info):
        self.url = url
        self.mdl_num = mdl_info
        log_setup = LoggerSetup(config, 'OHSDScraperLogger', 'OHSDScraper.log')
        self.logger = log_setup.get_logger()
        self.pdf_extractor = PDFExtractor(config, '')  # Instantiate PDFExtractor

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    def parse_orders(self, soup):
        tbody = soup.find('tbody')
        if not tbody:
            self.logger.info("No <tbody> found in the HTML content.")
            return []

        results = []
        rows = tbody.find_all('tr')
        for row in rows:
            result = {
                "mdl_num": self.mdl_num
            }

            # Extract the URL and document title
            a_tag = row.find('a', href=True)
            if a_tag:
                href = a_tag['href'].strip()
                url = f"https://www.ohsd.uscourts.gov/{href}"
                doc_title = a_tag.get_text(strip=True)

                result['url'] = url
                result['doc_title'] = doc_title
                self.logger.info(f"Extracted URL: {url} and Document Title: {doc_title}")

                # Extract the document number (doc_num) from the PDF
                self.extract_doc_num(result)

            # Extract the filing date
            date_td = row.find('td', text=re.compile(r'\d{1,2}/\d{1,2}/\d{2,4}'))
            if date_td:
                filing_date = date_td.get_text(strip=True)
                result['filing_date'] = filing_date
                self.logger.info(f"Extracted Filing Date: {filing_date}")

            results.append(result)

        return results

    def extract_doc_num(self, result):
        if 'pdf' in result['url']:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])

            # Extract the document number using the specified regex for "Entry Number"
            doc_num_match = re.search(r'Doc #:\s+(\d+)\s+', pdf_text)
            if doc_num_match:
                doc_num = doc_num_match.group(1)
                if doc_num == '10/26/208':
                    doc_num = '10/26/2018'
                result['doc_num'] = doc_num
                self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")

    def run(self):
        all_results = []

        # Fetch and parse the content from the orders page
        soup = self.fetch_content(self.url)
        if soup:
            results = self.parse_orders(soup)
            all_results.extend(results)
        self.logger.info(pformat(all_results))
        return all_results


if __name__ == "__main__":
    config = load_config('01/01/1970')
    mdl_info = [
        ('ohsd', 'https://www.ohsd.uscourts.gov/select-orders-date-mdl-2846', '2846')
    ]
    for mdl in mdl_info:
        court_id, url, mdl_info = mdl
    scraper = OHSDScraper(config, url, mdl_info)
    results = scraper.run()
