import re
import unicodedata
from pprint import pformat
from urllib.parse import quote, urljoin

import requests
from bs4 import Beautiful<PERSON>oup

from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor


class MADScraper:
    def __init__(self, config, url, mdl_id):
        self.url = url
        self.soup = None
        log_setup = LoggerSetup(config, 'MADScraperLogger', 'MADScraper.log')
        self.logger = log_setup.get_logger()
        self.pdf_extractor = PDFExtractor(config, '')

    def fetch_content(self):
        try:
            response = requests.get(self.url)
            response.raise_for_status()  # Check for request errors
            self.soup = BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")

    def parse_cards(self):
        if self.soup is None:
            self.logger.error("No content to parse. Please fetch the content first.")
            return []

        cards = self.soup.find_all('div', class_='card')
        if not cards:
            self.logger.error("No 'card' divs found on the page.")
            return []

        all_orders = []
        for index, card in enumerate(cards, start=1):
            self.logger.info(f"\n--- Card {index} ---\n")
            orders = self.parse_card(card)
            all_orders.extend(orders)

        return all_orders

    def parse_card(self, card):
        orders = []

        # Parse the card header (tabs)
        card_header = card.find('div', class_='card-header')
        if card_header:
            tabs = card_header.find_all('a', class_='nav-link')
            for tab in tabs:
                tab_title = tab.text.strip()
                tab_link = tab['href']
                self.logger.info(f"Tab: {tab_title} | Link: {tab_link}")

        # Parse the card body (tab content)
        card_body = card.find('div', class_='card-body')
        if card_body:
            tab_content = card_body.find_all('div', class_='tab-pane')
            for content in tab_content:
                title = content.find('h4', class_='card-title')
                if title:
                    self.logger.info(f"Title: {title.text.strip()}")

                lead_case_number = content.find('p', class_='card-text')
                if lead_case_number and "Lead Case Number" in lead_case_number.text:
                    self.logger.info(f"Lead Case Number: {lead_case_number.text.strip()}")

                # Parse additional content
                if "orders" in content['id']:
                    self.logger.info("\nOrders, Opinions, and Procedures:")
                    orders.extend(self.parse_orders_list(content))

                elif "counsel" in content['id']:
                    self.logger.info("\nCounsel List:")
                    p_tags = content.find_all('p', class_='card-text')
                    for p in p_tags:
                        self.logger.info(p.text.strip())

                elif "upcoming" in content['id']:
                    self.logger.info("\nUpcoming Court Proceedings:")
                    upcoming_events = content.find_all('p', class_='card-text')
                    for event in upcoming_events:
                        self.logger.info(event.text.strip())

        # Parse the card footer (related links)
        card_footer = card.find('div', class_='card-footer')
        if card_footer:
            links = card_footer.find_all('a')
            for link in links:
                link_text = link.text.strip()
                link_href = link['href']
                self.logger.info(f"Link: {link_text} | URL: {link_href}")

        return orders

    def parse_orders_list(self, content):
        ul_lists = content.find_all('ul', class_='fa-ul')
        orders = []
        for ul in ul_lists:
            items = ul.find_all('li')
            for item in items:
                order_link = item.find('a')
                if order_link:
                    # Combine and clean the text within the <a> tag, removing excess whitespace
                    doc_title = " ".join(order_link.stripped_strings).split()
                    doc_title = " ".join(doc_title)

                    doc_title = " ".join(" ".join(order_link.stripped_strings).split())

                    # Create a fully qualified URL using urljoin to handle relative paths
                    base_url = "https://www.mad.uscourts.gov/caseinfo/"
                    full_url = urljoin(base_url, order_link['href'])

                    # Ensure the href is properly URL-encoded
                    encoded_url = quote(full_url, safe='/:')

                    # Extract the MDL number directly from the URL
                    mdl_num = self.extract_mdl_num_from_url(full_url)

                    # Append the order as a dictionary
                    orders.append({
                        "mdl_num": mdl_num,
                        "url": encoded_url,
                        "doc_title": doc_title,
                        "filing_date": "NA",
                        "doc_num": "NA"
                    })

                    self.logger.info(f"Order: {doc_title} | URL: {encoded_url} | MDL: {mdl_num}")

        return orders

    @staticmethod
    def extract_mdl_num_from_url(url):
        pattern = re.compile(r'/mdl/(\d{4})/')
        match = pattern.search(url)
        if match:
            return match.group(1)
        return "NA"

    def extract_doc_nums_and_dates(self, result):
        if 'pdf' in result['url']:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])
            normalized_text = unicodedata.normalize('NFKD', pdf_text)
            cleaned_text = ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

            # Extract the document number using the specified regex
            if not result.get('doc_num') or result.get('doc_num') == 'NA':
                doc_num_match = re.search(r'\s+[Dd]ocument\s+(\d+)\s+', cleaned_text)
                if doc_num_match:
                    doc_num = doc_num_match.group(1)
                    result['doc_num'] = doc_num.replace('()', '')
                    self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")

            # For other parts of your code
            if not result.get('filing_date') or result['filing_date'] == 'NA':
                filing_date = self.pdf_extractor.extract_filing_date()
                if filing_date != 'NA':
                    result['filing_date'] = filing_date
                    self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

            # The existing Tesseract fallback can remain as is
            if not result.get('filing_date') or result['filing_date'] == 'NA':
                filing_date = self.pdf_extractor.extract_filing_date_with_tesseract(result['url'])
                if filing_date != 'NA':
                    result['filing_date'] = filing_date
                    self.logger.info(f"TESS: Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")
            # TODO: This was prior to refactoring pdf_extracto to separate signature page, extract_filing_date
            # # Extract the filing date
            # if not result.get('filing_date') or result['filing_date'] == 'NA':
            #     filing_date = self.pdf_extractor.extract_filing_date(cleaned_text)
            #     if filing_date:
            #         result['filing_date'] = filing_date
            #         self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")
            #
            # if not result.get('filing_date') or result['filing_date'] == 'NA':
            #     filing_date = self.pdf_extractor.extract_filing_date_with_tesseract(result['url'])
            #     if filing_date:
            #         result['filing_date'] = filing_date
            #         self.logger.info(f"TESS: Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

            return result

    def add_doc_nums_and_dates(self, results):
        for i, result in enumerate(results):
            results[i] = self.extract_doc_nums_and_dates(result)
            self.logger.debug(results[i])

    def run(self):
        self.fetch_content()
        orders = self.parse_cards()
        self.add_doc_nums_and_dates(orders)
        self.logger.info(pformat(orders))
        return orders


if __name__ == '__main__':
    # Example usage
    config = load_config('01/01/1970')
    # url = 'https://www.mad.uscourts.gov/caseinfo/multi-district-litigation.htm'
    mdl_info = [
        ('mad', 'https://www.mad.uscourts.gov/caseinfo/multi-district-litigation.htm', None)
    ]
    for mdl in mdl_info:
        court_id, url, mdl_id = mdl
        scraper = MADScraper(config, url, mdl_id)
        scraper.run()
