import json
import re
from pprint import pformat
from urllib.parse import urljoin

import certifi
import requests
from bs4 import BeautifulSoup

from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor

class CANDScraper:
    def __init__(self, config, url, mdl_id):
        self.url = url
        self.mdl_num = mdl_id
        self.pdf_extractor = PDFExtractor(config,'')
        log_setup = LoggerSetup(config, 'CANDScraperLogger', 'CANDScraper.log')
        self.logger = log_setup.get_logger()

    @staticmethod
    def extract_filing_date(text):
        # Match dates in MM/DD/YY or MM/DD/YYYY format
        match = re.search(r'\b\d{1,2}/\d{1,2}/\d{2,4}\b', text)
        return match.group(0) if match else ''

    @staticmethod
    def clean_doc_title(text):
        # Replace non-breaking spaces (\xa0) with regular spaces and strip any leading/trailing whitespace
        text = text.replace('\xa0', ' ').strip()

        # Remove the specified phrases from the document title (update or customize as needed)
        doc_title = re.sub(
            r'\s*signed\s+by\s*(Chief\s*)?Judge\s+<PERSON>tine\s+G\.\s+Baker\s+on\s+\d{1,2}/\d{1,2}/\d{4}',
            '',
            text,
            flags=re.IGNORECASE
        ).strip()

        # Remove any trailing period or extra spaces left after the replacement
        doc_title = re.sub(r'\s*\.\s*$', '', doc_title)

        return doc_title

    @staticmethod
    def extract_doc_num(text):
        # Match a numeric value that may include a hyphen followed by digits, does not resemble a date format, and has no alphabetical characters
        if re.match(r'^\d+(-\d+)?$', text):  # Only digits or digits with an optional hyphen and more digits
            if not re.match(r'\b\d{1,2}/\d{1,2}/\d{2,4}\b', text):  # Exclude date-like formats
                return text
        return ''

    def scrape_table(self):
        try:
            # Fetch the webpage content
            response = requests.get(self.url, verify=certifi.where())
            response.raise_for_status()  # Check for HTTP errors
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching URL {self.url}: {e}")
            return []

        # Parse the HTML content
        soup = BeautifulSoup(response.text, 'html.parser')

        # Find all tables within the document
        extracted_data = []
        for table in soup.find_all('table'):
            rows = table.find_all('tr')
            for row in rows:  # No need to skip header if it may have data
                cells = row.find_all('td')
                if len(cells) >= 2:  # Ensure there are enough columns
                    doc_link_tag = None
                    doc_num = ''
                    filing_date = ''

                    # Extract PDF link and title
                    for cell in cells:
                        potential_link = cell.find('a', href=True)
                        if potential_link and '.pdf' in potential_link['href']:
                            doc_link_tag = potential_link
                            break

                    # Extract document number and filing date from the same row
                    for cell in cells:
                        text_content = cell.get_text(strip=True)
                        # Extract doc_num if not already found and if it's valid
                        if not doc_num:
                            doc_num_candidate = self.extract_doc_num(text_content)
                            if doc_num_candidate:
                                doc_num = doc_num_candidate
                        # Extract filing_date if not already found
                        if not filing_date:
                            filing_date = self.extract_filing_date(text_content)

                    if doc_link_tag:
                        # Extract the document details
                        doc_title = doc_link_tag.get_text(strip=True)
                        doc_title = self.clean_doc_title(doc_title)  # Clean the doc title
                        doc_url = urljoin(self.url, doc_link_tag['href'])

                        document_data = {
                            "mdl_num": self.mdl_num,
                            "doc_title": doc_title,
                            "url": doc_url,
                            "doc_num": doc_num,
                            "filing_date": filing_date
                        }
                        extracted_data.append(document_data)

        if not extracted_data:
            self.logger.error(f"No documents found at URL: {self.url}")

        return extracted_data

    def run(self):
        documents = self.scrape_table()
        if documents:
            self.logger.info(pformat(documents))
            return documents
        else:
            self.logger.info(f"No documents found for URL: {self.url} and MDL Number: {self.mdl_num}")


# Example usage
if __name__ == "__main__":
    config = load_config('01/01/1970')

    mdl_info = [
        ("cand", "https://www.cand.uscourts.gov/judges/breyer-charles-r-crb/ubermdl/", "3084"),
        ("cand", "https://www.cand.uscourts.gov/in-re-social-media-adolescent-addiction-personal-injury-products-liability-litigation-mdl-no-3047/", "3047"),
        ("cand", "https://www.cand.uscourts.gov/judges/chhabria-vince-vc/in-re-roundup-products-liability-litigation-mdl-no-2741/", "2741"),
        ("cand", "https://www.cand.uscourts.gov/in-re-baby-food-products-liability-litigation/", "3101")
    ]

    for mdl in mdl_info:
        scraper = CANDScraper(config, mdl[1], mdl[2])
        scraper.run()
