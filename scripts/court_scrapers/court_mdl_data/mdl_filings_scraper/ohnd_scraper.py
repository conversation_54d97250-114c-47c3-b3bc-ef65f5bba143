import re

import requests
from bs4 import BeautifulSoup

from color_logging import LoggerSetup
from config import load_config
from pprint import pformat

class OHNDScraper:
    def __init__(self, config, url, mdl_info):
        self.url = url
        self.mdl_num = mdl_info
        log_setup = LoggerSetup(config, 'OHNDScraperLogger', 'OHNDScraper.log')
        self.logger = log_setup.get_logger()

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    def parse_orders(self, soup):
        tbodies = soup.find_all('tbody')
        if len(tbodies) < 2:
            self.logger.info("Less than 2 <tbody> elements found in the HTML content.")
            return []

        tbody = tbodies[1]  # Select the second <tbody>
        results = []
        rows = tbody.find_all('tr')
        for row in rows:
            tds = row.find_all('td')
            if len(tds) != 3:
                self.logger.warning("Row does not contain exactly 3 <td> elements, skipping.")
                continue

            result = {
                "mdl_num": self.mdl_num
            }

            # Extract the filing date from the second <td>
            date_td = tds[1].get_text(strip=True)
            filing_date_match = re.search(r'\d{2}/\d{2}/\d{4}', date_td)
            if filing_date_match:
                result['filing_date'] = filing_date_match.group(0)
                self.logger.info(f"Extracted Filing Date: {result['filing_date']}")

            # Extract the document title from the third <td>
            doc_title = tds[2].get_text(strip=True)
            result['doc_title'] = doc_title
            self.logger.info(f"Extracted Document Title: {doc_title}")

            # Extract the URL and document number from the first <td>
            a_tag = tds[0].find('a', href=True)
            if a_tag:
                href = f"https://www.ohnd.uscourts.gov/{a_tag['href'].strip()}"
                url = href
                doc_num_match = re.search(r'Doc\s+#(\d+)', a_tag.get_text(strip=True))
                if not doc_num_match:
                    doc_num_match = re.search(r'Doc#\s+(\d+)', a_tag.get_text(strip=True))
                if doc_num_match:
                    result['doc_num'] = doc_num_match.group(1)
                else:
                    self.logger.warning(f"No doc number found in: {a_tag.get_text(strip=True)}")

                result['url'] = url
                # self.logger.info(f"Extracted URL: {url} and Doc Num: {result.get('doc_num')}")

            results.append(result)
        return results

    def run(self):
        all_results = []

        # Fetch and parse the content from the orders page
        soup = self.fetch_content(self.url)
        if soup:
            results = self.parse_orders(soup)
            all_results.extend(results)

        self.logger.info(pformat(all_results))
        return all_results


if __name__ == "__main__":
    config = load_config('01/01/1970')
    mdl_info = [
        ('ohnd', 'https://www.ohnd.uscourts.gov/mdl-3092', '3092')
    ]

    for mdl in mdl_info:
        court_id, url, mdl_name = mdl
        scraper = OHNDScraper(config, url, mdl_info)
        results = scraper.run()
