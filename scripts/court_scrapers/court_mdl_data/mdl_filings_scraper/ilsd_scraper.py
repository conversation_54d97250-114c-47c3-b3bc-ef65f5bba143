import io
import re
from urllib.parse import quote, unquote, urljoin
import unicodedata
import requests
from bs4 import BeautifulSoup
from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor
from pdfminer.high_level import extract_text  # Ensure this import is correct
from color_logging import LoggerSetup
from config import load_config
from pprint import pformat


class ILSDScraper:
    def __init__(self, config, url, mdl_id):
        self.url = url
        self.mdl_num = mdl_id
        log_setup = LoggerSetup(config, 'ILSDScraperLogger', 'ILSDScraper.log')
        self.logger = log_setup.get_logger()
        self.pdf_extractor = PDFExtractor(config, '')  # PDFExtractor setup with config

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    def extract_doc_nums_and_dates(self, result):
        if 'pdf' in result['url']:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])

            if pdf_text:
                # Normalize the text to remove invisible or non-standard characters
                normalized_text = unicodedata.normalize('NFKD', pdf_text)
                cleaned_text = ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

                # Extract the document number using the specified regex
                if 'doc_num' not in result or not result['doc_num']:
                    doc_num = self.pdf_extractor.extract_document_number(cleaned_text)
                    result['doc_num'] = doc_num
                    self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")

                # # Extract the filing date
                # if not result.get('filing_date') or result['filing_date'] == 'NA':
                #     filing_date = self.pdf_extractor.extract_filing_date(pdf_text)
                #     if filing_date:
                #         result['filing_date'] = filing_date
                #         self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

    def parse_docs(self, soup):
        target_tr = None
        for tr in soup.find_all('tr'):
            th_tags = tr.find_all('th')
            if (len(th_tags) == 3 and
                    th_tags[0].get_text(strip=True) == "Date Posted" and
                    th_tags[1].get_text(strip=True) == "Date Signed" and
                    th_tags[2].get_text(strip=True) == "Description"):
                target_tr = tr
                break

        if not target_tr:
            self.logger.info("No matching <tr> tag found.")
            return []

        results = []
        for row in target_tr.find_next_siblings('tr'):
            td_elements = row.find_all('td')
            if len(td_elements) == 3:
                posted_date = td_elements[0].get_text(strip=True).replace('-', '/')
                filing_date = td_elements[1].get_text(strip=True).replace('-', '/')
                a_tag = td_elements[2].find('a', href=True)

                if a_tag:
                    href = a_tag['href'].strip()
                    url = urljoin(self.url, quote(unquote(href)))
                    doc_title = a_tag.get_text(strip=True).replace('\xa0', ' ').strip()

                    result = {
                        "mdl_num": self.mdl_num,
                        "url": url,
                        "doc_title": doc_title,
                        "posted_date": posted_date,
                        "filing_date": filing_date
                    }

                    # # Extract doc_num and update the result
                    self.extract_doc_nums_and_dates(result)

                    self.logger.info(pformat(result))
                    results.append(result)

        return results

    def run(self):
        all_results = []
        soup = self.fetch_content(self.url)
        if soup:
            results = self.parse_docs(soup)
            all_results.extend(results)

        # Add hard-coded entries for mdl_num == '3004'
        if self.mdl_num == '3004':
            additional_docs = [
                {
                    "mdl_num": "3004",
                    "doc_title": "Plaintiff's Assessment Questionnaire (PAQ)",
                    "filing_date": "NA",
                    "doc_num": "NA",
                    "url": "https://www.ilsd.uscourts.gov/sites/ilsd/files/PlaintiffAssmntQuestionnaire.pdf"
                },
                {
                    "mdl_num": "3004",
                    "doc_title": "PAQ Authorizations",
                    "filing_date": "NA",
                    "doc_num": "NA",
                    "url": "https://www.ilsd.uscourts.gov/sites/ilsd/files/PAQAuthorizations.pdf"
                },
                {
                    "mdl_num": "3004",
                    "doc_title": "Amended Administrative Order 322",
                    "filing_date": "NA",
                    "doc_num": "NA",
                    "url": "https://www.ilsd.uscourts.gov/sites/ilsd/files/AdminOrder322FirstAmended.pdf"
                }
            ]
            all_results.extend(additional_docs)
            self.logger.info(pformat(additional_docs))
        self.logger.info(f"Final Processed result: {pformat(all_results)}")

        return all_results


if __name__ == "__main__":
    config = load_config('01/01/1970')
    mdl_info = [
        ('ilsd', 'https://www.ilsd.uscourts.gov/paraquat-products-liability-litigation', '3004')
    ]

    for mdl in mdl_info:
        court_id, url, mdl_id = mdl
        scraper = ILSDScraper(config,url, mdl_id)
        results = scraper.run()
