import re
import unicodedata
from pprint import pformat
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup

from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor


class LAEDScraper:
    def __init__(self, config, url, mdl_id):
        self.url = url
        self.mdl_num = mdl_id
        self.pdf_extractor = PDFExtractor(config, '')
        log_setup = LoggerSetup(config, 'LAEDScraperLogger', 'LAEDScraper.log')
        self.logger = log_setup.get_logger()

    def fetch_page(self):
        response = requests.get(self.url)
        if response.status_code == 200:
            return response.text
        else:
            raise Exception(f"Failed to fetch the page. Status code: {response.status_code}")

    def parse_date(self, date_str):
        """Extracts the date from the string in YYYY-MM-DD format and converts it to MM/DD/YYYY."""
        match = re.search(r"(\d{4})-(\d{1,2})-(\d{1,2})", date_str)
        if match:
            year, month, day = match.groups()
            return f"{month.zfill(2)}/{day.zfill(2)}/{year}"
        return None

    def extract_data(self, html_content):
        soup = BeautifulSoup(html_content, 'html.parser')
        documents = []

        for entry in soup.find_all("span", class_="file"):
            a_tag = entry.find("a")
            if a_tag and "title" in a_tag.attrs:
                relative_url = a_tag["href"].strip()
                url = urljoin(self.url, relative_url)
                doc_title = a_tag.get_text(strip=True)
                filing_date_raw = a_tag["title"][:10]  # Extract the date portion YYYY-MM-DD
                filing_date = self.parse_date(filing_date_raw)

                document = {
                    "mdl_num": self.mdl_num,
                    "url": url,
                    "doc_title": doc_title,
                    "filing_date": filing_date
                }

                document = self.extract_doc_nums_and_dates(document)

                # self.logger.info(pformat(document))
                documents.append(document)
        self.logger.info(pformat(documents))
        return documents

    def extract_doc_nums_and_dates(self, result):
        if 'pdf' in result['url']:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])
            normalized_text = unicodedata.normalize('NFKD', pdf_text)
            cleaned_text = ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

            # Extract the document number using the specified regex
            if not result.get('doc_num') or result.get('doc_num') == 'NA':
                doc_num_match = re.search(r'\s+[Dd]ocument\s+(\d+)\s+', cleaned_text)
                if doc_num_match:
                    doc_num = doc_num_match.group(1)
                    result['doc_num'] = doc_num.replace('()', '')
                    self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")
                else:
                    result['doc_num'] = "NA"  # Assign "NA" if no document number is found

            # Extract the filing date
            if not result.get('filing_date') or result['filing_date'] == 'NA':
                filing_date = self.pdf_extractor.extract_filing_date(cleaned_text)
                if filing_date:
                    result['filing_date'] = filing_date
                    self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

        return result

    def run(self):
        html_content = self.fetch_page()
        return self.extract_data(html_content)


if __name__ == "__main__":
    config = load_config('01/01/1970')
    mdl_info = [
        ('laed', 'https://www.laed.uscourts.gov/case-information/mdl-mass-class-action/taxotere-eye', '3023')
    ]
    for mdl in mdl_info:
        court_id, url, mdl_id = mdl
    scraper = LAEDScraper(config, url, mdl_id)
    documents = scraper.run()
