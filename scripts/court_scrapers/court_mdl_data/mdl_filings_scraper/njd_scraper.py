import re
import unicodedata
from pprint import pformat

import requests
from bs4 import BeautifulSoup

from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor


class NJDScraper:
    def __init__(self, config, url, mdl_info):
        self.url = url
        self.mdl_num = mdl_info
        self.logger = self.setup_logger(config)
        self.pdf_extractor = PDFExtractor(config, '')

    @staticmethod
    def setup_logger(config):
        log_setup = LoggerSetup(config, 'NJDScraperLogger', 'NJDScraper.log')
        return log_setup.get_logger()

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    @staticmethod
    def clean_description(text):
        """Remove non-breaking spaces and clean the text."""
        return text.replace('\xa0', ' ').strip()

    def parse_orders(self, soup):
        results = []

        field_items = soup.find_all('div', class_='field__item even')
        if not field_items:
            self.logger.info("No 'field__item even' div found in the HTML content.")
            return results

        for field_item in field_items:
            results.extend(self.parse_paragraphs(field_item))
            results.extend(self.parse_list_items(field_item))

        self.logger.info(pformat(results))
        return results

    def parse_paragraphs(self, field_item):
        results = []
        paragraphs = field_item.find_all('p')
        for paragraph in paragraphs:
            links = paragraph.find_all('a', href=True)
            for link in links:
                if link['href'].endswith('.pdf'):
                    result = self.create_result_dict(link, paragraph.text)
                    results.append(result)
        return results

    def parse_list_items(self, field_item):
        results = []
        ul_items = field_item.find_all('ul')
        for ul in ul_items:
            li_tags = ul.find_all('li')
            for li in li_tags:
                links = li.find_all('a', href=True)
                for link in links:
                    if link['href'].endswith('.pdf'):
                        result = self.create_result_dict(link, li.text)
                        results.append(result)
        return results

    def create_result_dict(self, link, text):
        url = f"https://www.njd.uscourts.gov{link['href']}"
        description = self.clean_description(link.text)

        result = dict(mdl_num=self.mdl_num, url=url, description=description)

        result['doc_num'] = self.extract_docket_number(text)
        result['filing_date'] = self.extract_filing_date(text)

        return result

    @staticmethod
    def extract_docket_number(text):
        match = re.search(r'Docket Number[:\s]+(\d+)', text)
        return match.group(1) if match else None

    @staticmethod
    def extract_filing_date(text):
        match = re.search(r'Date[:\s]+([0-9]{1,2}/[0-9]{1,2}/[0-9]{2,4})', text)
        return match.group(1) if match else None

    def extract_doc_nums(self, results):
        for result in results:
            # if result['doc_num'] and result['filing_date']:
            #     continue
            if 'pdf' in result['url']:
                pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])
                cleaned_text = self.clean_pdf_text(pdf_text)

                if not result.get('doc_num'):
                    result['doc_num'] = self.pdf_extractor.extract_doc_num_with_tesseract(result['url'])
                    if result['doc_num']:
                        self.logger.info(f"Extracted Doc Num: {result['doc_num']} from PDF at URL: {result['url']}")

                if not result.get('filing_date'):
                    result['filing_date'] = self.pdf_extractor.extract_filing_date(cleaned_text)
                    if result['filing_date']:
                        self.logger.info(
                            f"Extracted Filing Date: {result['filing_date']} from PDF at URL: {result['url']}")

                if not result.get('filing_date'):
                    result['filing_date'] = self.extract_filing_date_with_tesseract(result)

    @staticmethod
    def clean_pdf_text(text):
        normalized_text = unicodedata.normalize('NFKD', text)
        return ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

    def extract_filing_date_with_tesseract(self, result):
        self.logger.info(f"Using Tesseract OCR to extract filing date from PDF at URL: {result['url']}")
        filing_date = self.pdf_extractor.extract_filing_date_with_tesseract(result['url'])
        if filing_date:
            self.logger.info(f"Extracted Filing Date with Tesseract: {filing_date} from PDF at URL: {result['url']}")
        return filing_date

    def run(self):
        all_results = {}
        soup = self.fetch_content(self.url)
        if soup:
            results = self.parse_orders(soup)
            self.extract_doc_nums(results)

            # Aggregate results by mdl_num
            if self.mdl_num in all_results:
                all_results[self.mdl_num].extend(results)
            else:
                all_results[self.mdl_num] = results

        self.logger.info(pformat(all_results))
        return all_results.get(self.mdl_num, [])


if __name__ == "__main__":
    config = load_config('01/01/1970')

    mdl_info = [
        ('scd', 'https://www.njd.uscourts.gov/orders-opinions-valsartan-mdl-2875', '2875'),
        ('scd', 'https://www.njd.uscourts.gov/panel-orders-valsartan-mdl-2875', '2875'),
        ('scd', 'https://www.njd.uscourts.gov/allergan-biocell-textured-breast-implants-management-orders', '2921'),
        ('scd', 'https://www.njd.uscourts.gov/elmiron-case-management-orders', '2973')
    ]

    for mdl in mdl_info:
        court_id, url, mdl_id = mdl
        scraper = NJDScraper(config, url, mdl_id)
        results = scraper.run()
