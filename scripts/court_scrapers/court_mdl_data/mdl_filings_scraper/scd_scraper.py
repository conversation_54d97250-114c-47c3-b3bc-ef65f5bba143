import re
import unicodedata
from pprint import pformat
from urllib.parse import urljoin, quote

import requests
from bs4 import BeautifulSoup

from color_logging import LoggerSetup
from config import load_config
from pdf_extractor import PDFExtractor


class SCDScraper:
    def __init__(self, config, url, mdl_num):
        self.url = url
        self.mdl_num = mdl_num
        self.root_url = f"https://www.scd.uscourts.gov/mdl-{mdl_num}/"
        log_setup = LoggerSetup(config, 'SCDScraperLogger', 'SCDScraper.log')
        self.logger = log_setup.get_logger()
        self.pdf_extractor = PDFExtractor(config, '')  # Instantiate PDFExtractor

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    def clean_description(self, text):
        """Remove non-breaking spaces and clean the text."""
        return text.replace('\xa0', ' ').strip()

    def parse_orders(self, soup):
        links = soup.find_all('a', href=True)
        results = []

        for link in links:
            if link['href'].endswith('.pdf'):
                href = link['href'].strip()
                url = urljoin(self.root_url, quote(href))  # Construct URL and encode spaces
                doc_title = link.get('title', '').strip()

                result = {
                    "mdl_num": self.mdl_num,
                    "url": url,
                    "doc_title": doc_title,
                }

                # Extract doc_num and filing_date using PDFExtractor
                self.extract_doc_nums_and_dates(result)
                results.append(result)

        return results

    def extract_doc_nums_and_dates(self, result):
        if 'pdf' in result['url']:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(result['url'])
            normalized_text = unicodedata.normalize('NFKD', pdf_text)
            cleaned_text = ''.join(c for c in normalized_text if not unicodedata.category(c).startswith('C'))

            # Extract the document number using the specified regex
            if not result.get('doc_num') or result.get('doc_num') == 'NA':
                doc_num_match = re.search(r'\s+[Ee]ntry\s+[Nn]umber\s+(\d+)\s+', cleaned_text)
                if doc_num_match:
                    doc_num = doc_num_match.group(1)
                    result['doc_num'] = doc_num.replace('()', '')
                    self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")
                else:
                    doc_num = self.pdf_extractor.extract_doc_num_with_tesseract(result['url'])
                    if doc_num is not None:
                        result['doc_num'] = doc_num
                    self.logger.info(f"Extracted Doc Num: {doc_num} from PDF at URL: {result['url']}")

            # Extract the filing date
            if not result.get('filing_date') or result['filing_date'] == 'NA':
                filing_date = self.pdf_extractor.extract_filing_date_with_tesseract(result['url'])
                if filing_date:
                    result['filing_date'] = filing_date
                    self.logger.info(f"Extracted Filing Date: {filing_date} from PDF at URL: {result['url']}")

            return result

    def run(self):
        soup = self.fetch_content(self.url)
        if soup:
            results = self.parse_orders(soup)
            self.logger.info(f"{pformat(results)}")
            return results
        return []


if __name__ == "__main__":
    config = load_config('01/01/1970')

    mdl_info = [
        ('scd', "https://www.scd.uscourts.gov/mdl-2873/orders.asp", '2873')
    ]

    for mdl in mdl_info:
        court_id, url, mdl_info = mdl

    scraper = SCDScraper(config, url, mdl_info)
    results = scraper.run()

