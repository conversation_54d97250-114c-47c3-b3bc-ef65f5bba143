import re
from pprint import pformat
from urllib.parse import unquote, quote

import requests
from bs4 import BeautifulSoup

from color_logging import LoggerSetup
from pdf_extractor import PDFExtractor
from config import load_config

class AREDScraper:
    def __init__(self, config, url, mdl_id):
        self.url = url
        self.mdl_num = mdl_id
        log_setup = LoggerSetup(config, 'AREDScraperLogger', 'AREDScraper.log')
        self.logger = log_setup.get_logger()
        self.pdf_extractor = PDFExtractor(config, '')  # Instantiate PDFExtractor

    def fetch_content(self, url):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching the URL: {e}")
            return None

    def process_url(self, href):
        decoded_href = unquote(href)
        encoded_url = f"https://www.are.uscourts.gov{quote(decoded_href)}"
        return encoded_url

    @staticmethod
    def clean_doc_title(text):
        # Remove non-breaking spaces
        text = text.replace('\xa0', ' ').strip()

        # Remove the specified phrases from the document title
        doc_title = re.sub(
            r'\s*signed\s+by\s*(Chief\s*)?Judge\s+Kristine\s+G\.\s+Baker\s+on\s+\d{1,2}/\d{1,2}/\d{4}',
            '',
            text,
            flags=re.IGNORECASE
        ).strip()

        # Remove any trailing period or extra spaces left after the replacement
        doc_title = re.sub(r'\s*\.\s*$', '', doc_title)

        return doc_title

    def parse_doc_num(self, url):
        try:
            pdf_text = self.pdf_extractor.extract_text_from_pdf_pymupdf(url)
            doc_num_match = re.search(r'Document\s+(\d+)', pdf_text)
            if doc_num_match:
                return doc_num_match.group(1)
        except Exception as e:
            self.logger.error(f"Error fetching or reading PDF from {url}: {e}")
        return None

    def parse_orders(self, soup):
        key_documents_section = soup.find('h3', text='Key Documents')
        if not key_documents_section:
            self.logger.info("Key Documents section not found.")
            return []

        results = []
        li_tags = key_documents_section.find_next('ul').find_all('li')
        for li in li_tags:
            a_tag = li.find('a', href=True)
            if not a_tag:
                continue

            url = self.process_url(a_tag['href'])
            doc_title = self.clean_doc_title(a_tag.text)

            result = {
                "mdl_num": self.mdl_num,
                "url": url,
                "doc_title": doc_title
            }

            # Extract Filing Date if present
            date_match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', li.text)
            if date_match:
                result['filing_date'] = date_match.group(1)

            # Extract Doc Number from PDF
            if 'pdf' in url.lower():
                doc_num = self.parse_doc_num(url)
                if doc_num:
                    result['doc_num'] = doc_num

            results.append(result)

        return results

    def run(self):
        all_results = []
        soup = self.fetch_content(self.url)
        if soup:
            results = self.parse_orders(soup)
            all_results.extend(results)

        self.logger.info(f'All results: {pformat(all_results)}')
        return all_results

if __name__ == "__main__":
    config = load_config('01/01/1970')
    mdl_info = ('ared', 'https://www.are.uscourts.gov/multi-district-litigation-2949', '2949')

    scraper = AREDScraper(config, mdl_info)
    results = scraper.run()
