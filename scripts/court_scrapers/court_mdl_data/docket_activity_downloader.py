import json
import os
import random
import re
import time

import pandas as pd
from selenium import webdriver
from selenium.common.exceptions import TimeoutException, ElementClickInterceptedException, \
    WebDriverException
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from webdriver_manager.chrome import ChromeDriverManager

from color_logging import LoggerSetup
from config import load_config


class DocketActivityDownloader:
    def __init__(self, config):
        self.config = config
        self.download_dir = os.getcwd()
        print(self.download_dir)
        self.headless = True  # Set to True if you want headless mode

        # Initialize logger
        log_setup = LoggerSetup(config, 'DocketActivityDownloaderLogger', 'DocketActivityDownloader.log')
        self.logger = log_setup.get_logger()

        self.setup_chrome_options()
        self.driver = self.initialize_webdriver()

    def setup_chrome_options(self):
        self.chrome_options = webdriver.ChromeOptions()
        self.chrome_options.add_experimental_option("prefs", {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,  # Enable safe browsing
            "profile.default_content_settings.popups": 0,
            "plugins.always_open_pdf_externally": True,
        })
        self.chrome_options.add_argument("--disable-gpu")
        self.chrome_options.add_argument("--window-size=1400,1000")
        self.chrome_options.add_argument(
            "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

        if self.headless:
            self.chrome_options.add_argument('--headless')

    def initialize_webdriver(self):
        chromedriver_path = ChromeDriverManager().install()
        service = Service(chromedriver_path)
        return webdriver.Chrome(service=service, options=self.chrome_options)

    @staticmethod
    def extract_docket_num(url):
        match = re.search(r'/docket/(\d+)/', url)
        if match:
            return match.group(1)
        else:
            raise ValueError(f"Docket number not found in URL: {url}")

    def download_and_load_csv(self, mdl_dockets):
        updated_dockets = []

        for docket in mdl_dockets:
            court_id, url, mdl_num = docket[:3]
            docket_num = self.extract_docket_num(url)

            self.logger.info(f"Processing docket: {docket_num} from URL: {url}")

            retry_count = 0
            max_retries = 3

            while retry_count < max_retries:
                try:
                    self.driver.get(url)
                    time.sleep(random.uniform(2, 5))  # Add a random delay to mimic human behavior

                    download_button_xpath = "//*[@id='docket-entry-table']/div[1]/div[4]/a"
                    try:
                        download_button = WebDriverWait(self.driver, 30).until(
                            EC.visibility_of_element_located((By.XPATH, download_button_xpath))
                        )
                    except TimeoutException:
                        self.logger.error(f"Download button not found for docket {docket_num}")
                        retry_count += 1
                        continue

                    try:
                        WebDriverWait(self.driver, 30).until(
                            EC.element_to_be_clickable((By.XPATH, download_button_xpath))
                        )
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", download_button)
                    except TimeoutException:
                        self.logger.error(f"Download button not clickable for docket {docket_num}")
                        retry_count += 1
                        continue

                    try:
                        self.driver.execute_script("arguments[0].click();", download_button)
                    except ElementClickInterceptedException as e:
                        self.logger.error(
                            f"ElementClickInterceptedException occurred for docket {docket_num}: {str(e)}")
                        retry_count += 1
                        continue

                    try:
                        downloaded_file = self._wait_for_download(docket_num)
                        new_file_name = f"{mdl_num}.csv"  # Use the last value in the tuple as the new name

                        # Delete if file already exists
                        new_file_path = os.path.join(self.download_dir, new_file_name)
                        if os.path.exists(new_file_path):
                            os.remove(new_file_path)

                        os.rename(downloaded_file, new_file_path)
                        self.logger.info(f"Downloaded and renamed CSV for docket {docket_num} to {new_file_name}")

                        # Update the tuple with the new file path
                        updated_docket = list(docket)
                        updated_docket[2] = new_file_path  # Save the new file path as the third item
                        updated_dockets.append(tuple(updated_docket))

                        break  # Exit retry loop if successful
                    except TimeoutException:
                        self.logger.error(f"File download timeout for docket {docket_num}")
                        retry_count += 1
                    except FileNotFoundError as e:
                        self.logger.error(str(e))
                        retry_count += 1

                except Exception as e:
                    self.logger.error(f"An unexpected error occurred while processing docket {docket_num}: {str(e)}")
                    retry_count += 1

                if retry_count >= max_retries:
                    self.logger.error(f"Failed to process docket {docket_num} after {max_retries} retries.")
                    updated_dockets.append(docket)  # Add the original tuple if processing fails

        return updated_dockets

    def _wait_for_download(self, docket_num):
        WebDriverWait(self.driver, 120).until(
            lambda driver: any(
                docket_num in filename and filename.endswith('.csv')
                for filename in os.listdir(self.download_dir)
            )
        )

        for filename in os.listdir(self.download_dir):
            if docket_num in filename and filename.endswith('.csv'):
                return os.path.join(self.download_dir, filename)

        raise FileNotFoundError(f"File for docket {docket_num} not found in {self.download_dir}")

    def log_cookies(self):
        try:
            cookies = self.driver.get_cookies()
            self.logger.debug(f"Cookies set by the server: {cookies}")
        except WebDriverException as e:
            self.logger.error(f"Error retrieving cookies: {str(e)}")

    def close(self):
        self.driver.quit()


if __name__ == "__main__":
    config = load_config('01/01/1970')

    mdl_dockets = [
        ('cand',
         'https://www.courtlistener.com/docket/65407433/in-re-social-media-adolescent-addictionpersonal-injury-products-liability/',
         '3047'),
        ('cand',
         'https://www.courtlistener.com/docket/67856158/in-re-uber-technologies-inc-passenger-sexual-assault-litigation/',
         '3084'),
        ('cand', 'https://www.courtlistener.com/docket/68078148/in-re-future-motion-inc-products-liability-litigation/',
         '3087'),
        (
            'cand', 'https://www.courtlistener.com/docket/68437807/in-re-baby-food-products-liability-litigation/',
            '3101'),
        ('cand', 'https://www.courtlistener.com/docket/5981306/in-re-roundup-products-liability-litigation/', '2741'),
        ('scd',
         'https://www.courtlistener.com/docket/8408916/in-re-aqueous-film-forming-foams-products-liability-litigation-mdl-2873/',
         '2873'),
        ('ared',
         'https://www.courtlistener.com/docket/17453225/in-re-profemur-hip-implant-products-liability-litigation/',
         '2949'),
        ('azd',
         'https://www.courtlistener.com/docket/67678440/in-re-bard-implanted-port-catheter-products-liability-litigation/',
         '3081'),
        ('flsd', 'https://www.courtlistener.com/docket/16813256/in-re-zantac-ranitidine-products-liability-litigation/',
         '2924'),
        ('gand',
         'https://www.courtlistener.com/docket/6078886/in-re-ethicon-physiomesh-flexible-composite-hernia-mesh-products-liability/',
         '2782'),
        ('gand', 'https://www.courtlistener.com/docket/44788596/in-re-paragard-iud-products-liability-litigation/',
         '2974'),
        ('ilnd', 'https://www.courtlistener.com/docket/61690868/hall-v-abbott-laboratories/', '3026'),
        ('ilnd',
         'https://www.courtlistener.com/docket/64881611/recalled-abbott-infant-formula-products-liability-litigation/',
         '3037'),
        ('ilnd',
         'https://www.courtlistener.com/docket/66801859/in-re-hair-relaxer-marketing-sales-practices-and-products-liability/',
         '3060'),
        ('ilnd',
         'https://www.courtlistener.com/docket/67495438/in-re-tepezza-marketing-sales-practices-and-products-liability/',
         '3079'),
        ('ilsd',
         'https://www.courtlistener.com/docket/59988561/in-re-paraquat-products-liability-litigation-v-syngenta-crop-protection/',
         '3004'),
        ('laed',
         'https://www.courtlistener.com/docket/62995309/in-re-taxotere-docetaxel-eye-injury-products-liability-litigation/',
         '3023'),
        ('mad',
         'https://www.courtlistener.com/docket/6081579/in-re-stryker-lfit-v40-femoral-head-products-liability-litigation/',
         '2768'),
        ('mad',
         'https://www.courtlistener.com/docket/63367050/in-re-covidien-hernia-mesh-products-liability-litigation-no-ii/',
         '2775'),
        ('ncwd', 'https://www.courtlistener.com/docket/64875241/in-re-gardasil-products-liability-litigation/', '3036'),
        ('njd',
         'https://www.courtlistener.com/docket/14819816/valsartan-losartan-and-irbesartan-products-liability-litigation/',
         '2875'),
        ('njd',
         'https://www.courtlistener.com/docket/16684846/in-re-allergan-biocell-textured-breast-implant-products-liability/',
         '2921'),
        ('njd',
         'https://www.courtlistener.com/docket/18753355/elmiron-pentosan-polysulfate-sodium-products-liability-litigation/',
         '2973'),
        ('flsd', 'https://www.courtlistener.com/docket/60122193/in-re-tasigna-nilotinib-products-liability-litigation/',
         '3006'),
        ('nysd',
         'https://www.courtlistener.com/docket/7999065/in-re-zimmer-ml-taper-hip-prosthesis-or-ml-taper-hip-prosthesis-with/',
         '2859'),
        ('nysd',
         'https://www.courtlistener.com/docket/65408277/in-re-acetaminophen-asd-adhd-products-liability-litigation/',
         '3043'),
        ('nywd',
         'https://www.courtlistener.com/docket/16529692/in-re-fisher-price-rock-n-play-sleeper-marketing-sales-practices-and/',
         '2903'),
        ('ohnd',
         'https://www.courtlistener.com/docket/68224010/in-re-suboxone-buprenorphinenaloxone-film-products-liability-litigation/',
         '3092'),
        ('ohsd',
         'https://www.courtlistener.com/docket/7603829/in-re-davol-inccr-bard-inc-polypropylene-hernia-mesh-products/',
         '3094'),
        ('paed',
         'https://www.courtlistener.com/docket/68222905/glucagon-like-peptide-1-receptor-agonists-glp-1-ras-products-liability/',
         '3094'),
        ('pawd',
         'https://www.courtlistener.com/docket/60866823/philips-recalled-cpap-bi-level-pap-and-mechanical-ventilator-products/',
         '3014'),
        ('mnd',
         'https://www.courtlistener.com/docket/4514197/in-re-bair-hugger-forced-air-warming-devices-products-liability-litigation/?page=12',
         '2666')
    ]

    downloader = DocketActivityDownloader(config)
    downloader.download_and_load_csv(mdl_dockets)
    downloader.close()


