import json
import os

import pandas as pd

from config import load_config
from court_mdl_data.mdl_filings_scraper.ared_scraper import AREDScraper
from court_mdl_data.mdl_filings_scraper.azd_scraper import AZDScraper
from court_mdl_data.mdl_filings_scraper.cand_scraper import CAND<PERSON>craper
from court_mdl_data.mdl_filings_scraper.exactech_scraper import ExactechScraper
from court_mdl_data.mdl_filings_scraper.gand_scraper import GANDScraper
from court_mdl_data.mdl_filings_scraper.ilnd_scraper import ILNDScraper
from court_mdl_data.mdl_filings_scraper.ilsd_scraper import ILSDScraper
from court_mdl_data.mdl_filings_scraper.laed_scraper import LAEDScraper
from court_mdl_data.mdl_filings_scraper.mad_scraper import MADScraper
from court_mdl_data.mdl_filings_scraper.njd_scraper import NJDScraper
from court_mdl_data.mdl_filings_scraper.ohnd_scraper import OHND<PERSON><PERSON>raper
from court_mdl_data.mdl_filings_scraper.paed_scraper import PAEDScraper
from court_mdl_data.mdl_filings_scraper.scd_scraper import SCDScraper


class MDLScraperManager:
    def __init__(self, config_date):
        self.config = load_config(config_date)
        self.court_mdl_info = [
            ('ared', 'https://www.are.uscourts.gov/multi-district-litigation-2949', '2949'),
            ("azd", "https://www.azd.uscourts.gov/re-bard-implanted-port-catheter-products-liability-litigation",
             "3081"),
            ('cand', "https://www.cand.uscourts.gov/judges/breyer-charles-r-crb/ubermdl/", "3084"),
            ('cand',
             "https://www.cand.uscourts.gov/in-re-social-media-adolescent-addiction-personal-injury-products-liability-litigation-mdl-no-3047/",
             "3047"),
            ('cand', "https://www.cand.uscourts.gov/in-re-baby-food-products-liability-litigation/", "3101"),
            ('cand',
             "https://www.cand.uscourts.gov/judges/chhabria-vince-vc/in-re-roundup-products-liability-litigation-mdl-no-2741/",
             "2741"),
            ('gand', 'https://www.gand.uscourts.gov/17md2782/case-specific-forms', '2782'),
            ('gand', 'https://www.gand.uscourts.gov/17md2782/practice-procedure-orders', '2782'),
            ('gand', 'https://www.gand.uscourts.gov/17md2782/orders-and-opinions', '2782'),
            ('gand', 'https://www.gand.uscourts.gov/17md2782/misc-orders', '2782'),
            ('gand', 'https://www.gand.uscourts.gov/20md2974/practice-procedure-orders', '2974'),
            ('gand', 'https://www.gand.uscourts.gov/20md2974/orders-and-opinions', '2974'),
            ('ilnd', 'https://www.ilnd.uscourts.gov/mdl-details.aspx?91eSFtoI+ycFmA6482wQKA==', '3060'),
            ('ilnd', 'https://www.ilnd.uscourts.gov/mdl-details.aspx?fnu/YDnD0O7Y8SjpUhHmZA==', '3079'),
            ('ilnd', 'https://www.ilnd.uscourts.gov/mdl-details.aspx?Lbz1nwUsE4JWF/IQJN6GpA==', '3026'),
            ('ilnd', 'https://www.ilnd.uscourts.gov/mdl-details.aspx?Y/EMwl0+f9LcVNqgbUFRDw==', '3037'),
            ('ilsd', 'https://www.ilsd.uscourts.gov/paraquat-products-liability-litigation', '3004'),
            ('laed', 'https://www.laed.uscourts.gov/case-information/mdl-mass-class-action/taxotere-eye', '3023'),
            ('ohnd', 'https://www.ohnd.uscourts.gov/mdl-3092', '3092'),
            ('mad', 'https://www.mad.uscourts.gov/caseinfo/multi-district-litigation.htm', None),
            ('njd', 'https://www.njd.uscourts.gov/orders-opinions-valsartan-mdl-2875', '2875'),
            ('njd', 'https://www.njd.uscourts.gov/panel-orders-valsartan-mdl-2875', '2875'),
            ('njd', 'https://www.njd.uscourts.gov/allergan-biocell-textured-breast-implants-management-orders', '2921'),
            ('njd', 'https://www.njd.uscourts.gov/elmiron-case-management-orders', '2973'),
            ('paed', 'https://www.paed.uscourts.gov/mdl/mdl3094/orders', '3094'),
            ('scd', 'https://www.scd.uscourts.gov/mdl-2873/orders.asp', '2873'),
            ('ohsd', 'https://www.ohsd.uscourts.gov/select-orders-date-mdl-2846', '2846'),
            ('exactech', "https://www.exactechmdlfilings.com/", '3044')
        ]

        self.scrapers = {
            'ared': AREDScraper,
            'azd': AZDScraper,
            'cand': CANDScraper,
            'gand': GANDScraper,
            'ilnd': ILNDScraper,
            'ilsd': ILSDScraper,
            'laed': LAEDScraper,
            'mad': MADScraper,
            'njd': NJDScraper,
            'ohnd': OHNDScraper,
            'paed': PAEDScraper,
            'scd': SCDScraper,
            'exactech': ExactechScraper,
        }

        self.results = {}

    def run_scrapers(self):
        for court_key, url, mdl_id in self.court_mdl_info:
            print(f'Processing court {court_key}:{mdl_id}')
            scraper_class = self.scrapers[court_key]
            scraper_instance = scraper_class(self.config, url, mdl_id)
            run_results = scraper_instance.run() or []  # Ensure it's always a list

            if mdl_id in self.results:
                self.results[mdl_id].extend(run_results)
            else:
                self.results[mdl_id] = run_results

    def execute(self):
        self.run_scrapers()
        file_path = os.path.join(os.getcwd(), 'combined_mdl_orders.json')
        self.save_results(file_path)

    def save_results(self, file_path):
        with open(file_path, 'w') as json_file:
            json.dump(self.results, json_file, indent=4)
        print(f'Data has been successfully written to {file_path}')

    @staticmethod
    def load_results(file_path):
        with open(file_path, 'r') as json_file:
            data = json.load(json_file)
        return data

    @staticmethod
    def load_json_as_dataframe(file_path, key_column_name='mdl_id'):
        with open(file_path, 'r') as json_file:
            data = json.load(json_file)

        # Flatten the JSON structure by converting each key's value into a DataFrame and adding the key as a new column
        df_list = []
        for key, value in data.items():
            temp_df = pd.DataFrame(value)
            temp_df[key_column_name] = key  # Add the key as a new column
            df_list.append(temp_df)

        # Concatenate all the DataFrames into a single DataFrame
        df = pd.concat(df_list, ignore_index=True)

        return df


if __name__ == '__main__':
    manager = MDLScraperManager(config_date='01/01/1970')
    # manager.execute()
    file_path = os.path.join(os.getcwd(), 'combined_mdl_orders.json')
    data = manager.load_json_as_dataframe(file_path)
    # print('')
