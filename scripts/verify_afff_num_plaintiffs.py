#!/usr/bin/env python3
"""
Verify that AFFF cases (MDL 2873) have NumPlaintiffs field properly set in DynamoDB.
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from rich.console import Console
from rich.table import Table
from rich.progress import Progress


async def verify_afff_num_plaintiffs():
    """Verify NumPlaintiffs field for AFFF cases in DynamoDB."""
    console = Console()
    console.print("[bold blue]Verifying NumPlaintiffs field for AFFF cases (MDL 2873)...[/bold blue]")
    
    # Initialize storage and repository
    storage = AsyncDynamoDBStorage()
    pacer_repo = PacerRepository(storage)
    
    # Query for the last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    console.print(f"\nQuerying cases from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}...")
    
    # Collect all AFFF cases
    afff_cases = []
    cases_with_num_plaintiffs = 0
    cases_without_num_plaintiffs = 0
    
    with Progress() as progress:
        task = progress.add_task("Querying cases...", total=30)
        
        current_date = end_date
        while current_date >= start_date:
            filing_date = current_date.strftime('%Y%m%d')
            
            try:
                # Query by filing date
                daily_records = await pacer_repo.query_by_filing_date(filing_date)
                
                # Filter for AFFF cases (MDL 2873)
                for record in daily_records:
                    if str(record.get('mdl_num', '')) == '2873':
                        afff_cases.append(record)
                        
                        # Check if num_plaintiffs exists
                        if 'num_plaintiffs' in record and record['num_plaintiffs'] not in [None, '', 'NA']:
                            cases_with_num_plaintiffs += 1
                        else:
                            cases_without_num_plaintiffs += 1
                
            except Exception as e:
                console.print(f"[red]Error querying date {filing_date}: {e}[/red]")
            
            current_date -= timedelta(days=1)
            progress.update(task, advance=1)
    
    # Display results
    console.print(f"\n[bold]AFFF Cases Summary (MDL 2873):[/bold]")
    console.print(f"Total AFFF cases found: {len(afff_cases)}")
    console.print(f"Cases WITH num_plaintiffs: {cases_with_num_plaintiffs} ({cases_with_num_plaintiffs/len(afff_cases)*100:.1f}%)" if afff_cases else "No AFFF cases found")
    console.print(f"Cases WITHOUT num_plaintiffs: {cases_without_num_plaintiffs} ({cases_without_num_plaintiffs/len(afff_cases)*100:.1f}%)" if afff_cases else "")
    
    # Show sample of cases without num_plaintiffs
    if cases_without_num_plaintiffs > 0:
        console.print(f"\n[yellow]Sample of AFFF cases missing num_plaintiffs:[/yellow]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Filing Date", style="cyan")
        table.add_column("Docket Number", style="cyan")
        table.add_column("Court ID", style="cyan")
        table.add_column("Case Title", style="cyan", max_width=50)
        table.add_column("Plaintiffs Field", style="yellow")
        
        sample_count = min(10, cases_without_num_plaintiffs)
        shown = 0
        
        for case in afff_cases:
            if 'num_plaintiffs' not in case or case['num_plaintiffs'] in [None, '', 'NA']:
                plaintiffs_value = str(case.get('plaintiff', case.get('plaintiffs', 'N/A')))[:50]
                if plaintiffs_value and plaintiffs_value != 'N/A':
                    plaintiffs_value += "..."
                
                table.add_row(
                    case.get('filing_date', 'N/A'),
                    case.get('docket_num', 'N/A'),
                    case.get('court_id', 'N/A'),
                    case.get('case_title', case.get('versus', 'N/A'))[:50],
                    plaintiffs_value
                )
                
                shown += 1
                if shown >= sample_count:
                    break
        
        console.print(table)
        
        if cases_without_num_plaintiffs > sample_count:
            console.print(f"\n[dim]... and {cases_without_num_plaintiffs - sample_count} more cases[/dim]")
    
    # Show sample of cases with num_plaintiffs
    if cases_with_num_plaintiffs > 0:
        console.print(f"\n[green]Sample of AFFF cases with num_plaintiffs:[/green]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Filing Date", style="cyan")
        table.add_column("Docket Number", style="cyan")
        table.add_column("Court ID", style="cyan")
        table.add_column("Num Plaintiffs", style="green")
        table.add_column("Case Title", style="cyan", max_width=50)
        
        sample_count = min(5, cases_with_num_plaintiffs)
        shown = 0
        
        for case in afff_cases:
            if 'num_plaintiffs' in case and case['num_plaintiffs'] not in [None, '', 'NA']:
                table.add_row(
                    case.get('filing_date', 'N/A'),
                    case.get('docket_num', 'N/A'),
                    case.get('court_id', 'N/A'),
                    str(case['num_plaintiffs']),
                    case.get('case_title', case.get('versus', 'N/A'))[:50]
                )
                
                shown += 1
                if shown >= sample_count:
                    break
        
        console.print(table)
    
    # Provide recommendations
    if cases_without_num_plaintiffs > 0:
        console.print(f"\n[bold yellow]Recommendation:[/bold yellow]")
        console.print("To add num_plaintiffs to these cases, run:")
        console.print("[bold]python src/main.py --params config/transform.yml --force-reprocess[/bold]")
        console.print("\nThis will reprocess all cases and calculate num_plaintiffs for AFFF cases.")


if __name__ == "__main__":
    asyncio.run(verify_afff_num_plaintiffs())