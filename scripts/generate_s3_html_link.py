#!/usr/bin/env python3
"""
Script to generate missing S3 HTML links for docket JSON files.

Usage:
    python src/scripts/generate_s3_html_link.py --date YYYYMMDD [--dry-run]

This script:
- Iterates through each JSON file in data/YYYYMMDD/dockets/
- Checks if s3_html link is missing
- Generates the link using the pattern: {cdn_base_url}/{date}/html/{base_filename}.html
- Updates the JSON file with the new s3_html link (unless --dry-run)
"""

import argparse
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn
from rich.table import Table
from rich.logging import RichHandler

# Initialize Rich console
console = Console()

# Default CDN base URL - should always use this format
DEFAULT_CDN_BASE_URL = "https://cdn.lexgenius.ai"


def load_config() -> Dict:
    """Load configuration from various sources."""
    config = {
        'cdn_base_url': DEFAULT_CDN_BASE_URL
    }
    
    # Try to load from environment variable
    if 'CDN_BASE_URL' in os.environ:
        config['cdn_base_url'] = os.environ['CDN_BASE_URL'].rstrip('/')
    
    # Could also load from config files here if needed
    
    return config


def generate_s3_html_link(base_filename: str, iso_date: str, cdn_base_url: str) -> str:
    """
    Generate S3 HTML link following the pattern used in docket_processor.py.
    
    Args:
        base_filename: Base filename without extension
        iso_date: Date in YYYYMMDD format
        cdn_base_url: CDN base URL
        
    Returns:
        Generated S3 HTML link
    """
    return f"{cdn_base_url}/{iso_date}/html/{base_filename}.html"


def process_json_file(json_path: Path, iso_date: str, cdn_base_url: str, dry_run: bool) -> Optional[Dict]:
    """
    Process a single JSON file to check and potentially add s3_html link.
    
    Args:
        json_path: Path to JSON file
        iso_date: Date in YYYYMMDD format
        cdn_base_url: CDN base URL
        dry_run: If True, don't actually update files
        
    Returns:
        Dict with update info if link was missing, None otherwise
    """
    try:
        # Read JSON file
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Check if s3_html already exists
        if 's3_html' in data and data['s3_html']:
            return None
        
        # Generate s3_html link
        base_filename = json_path.stem
        s3_html_link = generate_s3_html_link(base_filename, iso_date, cdn_base_url)
        
        # Update data
        data['s3_html'] = s3_html_link
        
        # Write back if not dry run
        if not dry_run:
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        
        return {
            'filename': json_path.name,
            'base_filename': base_filename,
            's3_html_link': s3_html_link,
            'court_id': data.get('court_id', 'unknown'),
            'docket_num': data.get('docket_num', 'unknown')
        }
        
    except json.JSONDecodeError as e:
        console.print(f"[red]Error decoding JSON from {json_path}: {e}[/red]")
        return None
    except Exception as e:
        console.print(f"[red]Error processing {json_path}: {e}[/red]")
        return None


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description='Generate missing S3 HTML links for docket JSON files'
    )
    parser.add_argument(
        '--date',
        required=True,
        help='Date in YYYYMMDD format'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be updated without making changes'
    )
    parser.add_argument(
        '--cdn-base-url',
        help=f'CDN base URL (default: {DEFAULT_CDN_BASE_URL})'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Validate date format
    try:
        datetime.strptime(args.date, '%Y%m%d')
    except ValueError:
        console.print(f"[red]Invalid date format: {args.date}. Use YYYYMMDD format.[/red]")
        sys.exit(1)
    
    # Load configuration
    config = load_config()
    if args.cdn_base_url:
        config['cdn_base_url'] = args.cdn_base_url.rstrip('/')
    
    # Get base data directory from environment variable or use default
    base_data_dir = os.environ.get('LEXGENIUS_DATA_DIR', 'data')
    
    # Construct dockets directory path
    dockets_dir = Path(base_data_dir) / args.date / 'dockets'
    
    if not dockets_dir.exists():
        console.print(f"[red]Directory not found: {dockets_dir}[/red]")
        sys.exit(1)
    
    # Find all JSON files
    json_files = list(dockets_dir.glob('*.json'))
    
    if not json_files:
        console.print(f"[yellow]No JSON files found in {dockets_dir}[/yellow]")
        return
    
    console.print(f"\n[bold]Processing {len(json_files)} JSON files in {dockets_dir}[/bold]")
    console.print(f"CDN Base URL: {config['cdn_base_url']}")
    console.print(f"Mode: {'[yellow]DRY RUN[/yellow]' if args.dry_run else '[green]UPDATE[/green]'}\n")
    
    # Process files with progress bar
    updated_files = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeRemainingColumn(),
        console=console
    ) as progress:
        task = progress.add_task("Processing files...", total=len(json_files))
        
        for json_file in json_files:
            update_info = process_json_file(
                json_file,
                args.date,
                config['cdn_base_url'],
                args.dry_run
            )
            
            if update_info:
                updated_files.append(update_info)
            
            progress.update(task, advance=1)
    
    # Display results
    if updated_files:
        console.print(f"\n[bold]Found {len(updated_files)} files missing s3_html links:[/bold]\n")
        
        # Create table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Court ID", style="cyan")
        table.add_column("Docket Number", style="cyan")
        table.add_column("Filename", style="yellow")
        table.add_column("Generated S3 HTML Link", style="green")
        
        for update in updated_files:
            table.add_row(
                update['court_id'],
                update['docket_num'],
                update['filename'],
                update['s3_html_link']
            )
        
        console.print(table)
        
        if args.dry_run:
            console.print(f"\n[yellow]DRY RUN: No files were updated. Run without --dry-run to apply changes.[/yellow]")
        else:
            console.print(f"\n[green]Successfully updated {len(updated_files)} files with s3_html links.[/green]")
    else:
        console.print("\n[green]All files already have s3_html links. No updates needed.[/green]")
    
    # Summary
    console.print(f"\n[bold]Summary:[/bold]")
    console.print(f"  Total files processed: {len(json_files)}")
    console.print(f"  Files missing s3_html: {len(updated_files)}")
    console.print(f"  Files already complete: {len(json_files) - len(updated_files)}")


if __name__ == "__main__":
    main()