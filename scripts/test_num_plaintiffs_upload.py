#!/usr/bin/env python3
"""
Test script to verify num_plaintiffs field is properly uploaded to DynamoDB.
"""
import asyncio
import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.repositories.pacer_repository import PacerRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage


async def test_num_plaintiffs_upload():
    """Test uploading a record with num_plaintiffs field."""
    print("Testing num_plaintiffs upload to DynamoDB...")
    
    # Initialize storage and repository
    # Create a simple config object with the needed attributes
    class StorageConfig:
        def __init__(self):
            self.aws_region = 'us-west-2'
            self.dynamodb_endpoint = None
            self.dynamodb_max_retries = 15
            self.dynamodb_base_delay = 1.0
            self.dynamodb_max_delay = 300.0
    
    storage_config = StorageConfig()
    storage = AsyncDynamoDBStorage(storage_config)
    # Use async context manager to ensure proper initialization
    async with storage:
        pacer_repo = PacerRepository(storage)
        
        # Create a test record with num_plaintiffs field
        test_record = {
            'filing_date': '20250117',  # Today's date
            'docket_num': '2:25-cv-99999',  # Test docket number
            'court_id': 'test',
            'mdl_num': '2873',  # AFFF MDL
            'num_plaintiffs': '42',  # The field we're testing
            'case_title': 'Test Case for NumPlaintiffs Field',
            'date_filed': '2025-01-17',
            'added_on': datetime.now().strftime('%Y%m%d'),
            'is_test_record': True  # Flag to identify test records
        }
        
        print(f"\nTest record (snake_case):")
        for key, value in test_record.items():
            print(f"  {key}: {value}")
        
        # Test the field conversion
        converted = pacer_repo._convert_dict_to_pascal(test_record.copy())
        print(f"\nConverted record (PascalCase):")
        for key, value in converted.items():
            print(f"  {key}: {value}")
        
        # Verify num_plaintiffs was converted to NumPlaintiffs
        if 'NumPlaintiffs' in converted:
            print("\n✓ num_plaintiffs successfully converted to NumPlaintiffs")
        else:
            print("\n✗ ERROR: num_plaintiffs was not converted to NumPlaintiffs")
            print(f"  Available keys: {list(converted.keys())}")
            return
        
        # Test upload
        print("\nUploading test record to DynamoDB...")
        try:
            success = await pacer_repo.add_or_update_record(test_record)
            if success:
                print("✓ Upload successful!")
            else:
                print("✗ Upload failed!")
                return
        except Exception as e:
            print(f"✗ Upload error: {e}")
            return
        
        # Test retrieval
        print("\nRetrieving test record from DynamoDB...")
        try:
            retrieved = await pacer_repo.get_by_filing_date_and_docket('20250117', '2:25-cv-99999')
            if retrieved:
                print("✓ Record retrieved successfully!")
                print(f"\nRetrieved record (snake_case):")
                for key, value in retrieved.items():
                    if key in ['num_plaintiffs', 'mdl_num', 'case_title', 'is_test_record']:
                        print(f"  {key}: {value}")
                
                # Check if num_plaintiffs is present
                if 'num_plaintiffs' in retrieved:
                    print(f"\n✓ num_plaintiffs field present with value: {retrieved['num_plaintiffs']}")
                else:
                    print("\n✗ ERROR: num_plaintiffs field not found in retrieved record")
            else:
                print("✗ Failed to retrieve record")
        except Exception as e:
            print(f"✗ Retrieval error: {e}")
        
        # Clean up test record
        print("\nCleaning up test record...")
        try:
            # Delete the test record by updating it with a delete marker
            cleanup_record = {
                'filing_date': '20250117',
                'docket_num': '2:25-cv-99999',
                'deleted': True,
                'deleted_at': datetime.now().isoformat()
            }
            await pacer_repo.add_or_update_record(cleanup_record)
            print("✓ Test record marked for deletion")
        except Exception as e:
            print(f"✗ Cleanup error: {e}")


if __name__ == "__main__":
    asyncio.run(test_num_plaintiffs_upload())