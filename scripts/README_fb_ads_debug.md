# Facebook Ads Debug Tools

This directory contains debug tools for troubleshooting Facebook ads processing issues with rich formatting and detailed logging.

## Debug Scripts

### `debug_fb_ads_single_firm.py`

A comprehensive debug script that processes a single firm with rich console output and detailed file logging.

#### Features

- **Rich Console Output**: Beautiful, formatted terminal display with:
  - Color-coded status messages (✅ success, ❌ errors, ⚠️ warnings)
  - Progress bars and spinners
  - Structured panels and tables
  - Syntax highlighting for JSON responses

- **Detailed File Logging**: Complete debug information saved to log files:
  - All HTTP requests/responses with headers and bodies
  - Session state and token validation
  - API call timing and error details
  - Full stack traces for exceptions

- **API Request Interception**: Captures and displays:
  - GraphQL company search requests
  - Facebook Ads Library API calls
  - Raw request/response data
  - Error handling and retry attempts

#### Usage

```bash
# Process a specific firm ID with today's date
python src/scripts/debug_fb_ads_single_firm.py --firm-id *********

# Process with a specific date
python src/scripts/debug_fb_ads_single_firm.py --firm-id ********* --date 01/15/24

# Enable verbose output
python src/scripts/debug_fb_ads_single_firm.py --firm-id ********* --verbose
```

#### Output

The script provides:

1. **Console Output**: Real-time rich formatted display showing:
   ```
   ╭─ Facebook Ads Debug Session ─────────────────────────────╮
   │ Firm ID: *********                                      │
   │ Date: 01/15/24                                          │
   │ Log File: data/20240115/logs/debug_fb_ads.log          │
   │ Verbose: False                                          │
   ╰──────────────────────────────────────────────────────────╯
   ```

2. **Configuration Summary**: Table showing key settings
3. **API Request Details**: Tables with request/response information
4. **Results Summary**: Final status and recommendations

#### Log File

The debug log file (`data/YYYYMMDD/logs/debug_fb_ads.log`) contains:

- Timestamped entries with module and line numbers
- Complete HTTP request/response data
- Session tokens and validation results
- Error details and stack traces
- API response parsing and processing steps

### `fb_ads_debug_interceptor.py`

Supporting module that patches Facebook API client methods to capture detailed debugging information.

#### Features

- **Request Interception**: Captures all HTTP requests before they're sent
- **Response Analysis**: Processes and formats API responses
- **Error Tracking**: Detailed error logging with context
- **Rich Formatting**: Beautiful console output for debugging data

## Common Use Cases

### 1. Firm Not Found
```bash
python src/scripts/debug_fb_ads_single_firm.py --firm-id UNKNOWN_FIRM
```
This will show if the firm exists in the database and can be processed.

### 2. API Errors
```bash
python src/scripts/debug_fb_ads_single_firm.py --firm-id ********* --verbose
```
This will capture detailed API request/response data to diagnose API blocks, rate limits, or authentication issues.

### 3. Session Problems
The debug output will show:
- Session creation and validation steps
- Token availability and format
- Cookie management and expiration

### 4. Data Parsing Issues
The logs will contain:
- Raw API responses before parsing
- JSON structure analysis
- Field extraction and transformation steps

## Example Output

### Successful Processing
```
✅ Configuration loaded successfully
✅ Services initialized
🔍 COMPANY SEARCH INITIATED
📋 Company Search Request
   Company Name: Test Law Firm
   Endpoint: GraphQL Company Search

Company Search Results (1 found)
┏━━━━━━━━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━┳━━━━━━━━━━━━━┓
┃ ID        ┃ Name        ┃ Category ┃ Deleted ┃ Restricted ┃
┡━━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━╇━━━━━━━━━━━━━┩
│ ********* │ Test Law... │ Legal    │ False   │ False      │
└───────────┴─────────────┴──────────┴─────────┴────────────┘

📄 ADS PAGE FETCH INITIATED
📊 Ads Fetch Request
   Company ID: *********
   Date Range: 2024-01-01 → 2024-01-15
   Cursor: None

✅ Processing completed successfully
```

### Failed Processing
```
❌ Company Search Failed
   Error: GraphQL API returned errors

💥 Failure Summary
   Firm ID: *********
   Check the log file for error details and API responses
   Common issues: Invalid firm ID, API blocks, session problems
```

## Configuration

The debug script uses the same configuration as the main Facebook ads processing:

- Loads `config/fb_ads.yml`
- Respects proxy settings and API keys
- Uses the same session management and error handling

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're in the `lexgenius` conda environment
2. **Configuration Missing**: Verify `config/fb_ads.yml` exists
3. **API Keys**: Check `.env` file has required API keys
4. **AWS Credentials**: Verify AWS credentials for S3 and DynamoDB access

### Log Analysis

Check the log file for:
- `❌ CRITICAL:` messages indicating missing dependencies
- `HTTP Request failed:` for network issues
- `Session validation failed` for authentication problems
- `API error in ad payload` for Facebook API blocks

## Dependencies

- Rich library for formatted output
- Existing Facebook ads processing infrastructure
- Same configuration and credentials as main processing