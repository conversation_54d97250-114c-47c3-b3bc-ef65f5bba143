#!/usr/bin/env python3
"""
Validation script for duplicate detection changes in process_html_pipeline.py

This script runs the comprehensive test suite and provides a summary report
of the duplicate detection functionality.
"""

import sys
import subprocess
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent
TEST_FILE = PROJECT_ROOT / "tests" / "test_html_pipeline_duplicate_detection.py"

def run_tests():
    """Run the duplicate detection tests"""
    print("=" * 80)
    print("DUPLICATE DETECTION VALIDATION REPORT")
    print("=" * 80)
    print(f"Testing file: {TEST_FILE}")
    print()
    
    # Check if test file exists
    if not TEST_FILE.exists():
        print(f"ERROR: Test file not found: {TEST_FILE}")
        return False
    
    # Run the tests using pytest
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            str(TEST_FILE), 
            "-v", 
            "--asyncio-mode=auto",
            "--tb=short"
        ], capture_output=True, text=True, cwd=PROJECT_ROOT)
        
        print("TEST OUTPUT:")
        print("-" * 40)
        print(result.stdout)
        
        if result.stderr:
            print("\nERRORS/WARNINGS:")
            print("-" * 40)
            print(result.stderr)
        
        print("\nRETURN CODE:", result.returncode)
        
        if result.returncode == 0:
            print("\n✅ ALL TESTS PASSED!")
            print("\nDuplicate detection functionality validated:")
            print("✓ SQLite filtering happens before HTML parsing")
            print("✓ DynamoDB check occurs before scraping")
            print("✓ Dockets with court_id='njd' are properly filtered")
            print("✓ Pipeline skips scraping for existing dockets")
            print("✓ Error handling works when DynamoDB check fails")
            print("✓ Performance improvements confirmed (no unnecessary scraping)")
            print("✓ Async changes work correctly")
            print("✓ Johnson & Johnson case filtering works")
            print("✓ Docket number extraction and validation works")
            print("✓ DynamoDB initialization flow works correctly")
            return True
        else:
            print("\n❌ SOME TESTS FAILED!")
            print("Please review the test output above to identify issues.")
            return False
            
    except Exception as e:
        print(f"ERROR running tests: {e}")
        return False

def validate_test_coverage():
    """Validate that all required test scenarios are covered"""
    print("\n" + "=" * 80)
    print("TEST COVERAGE VALIDATION")
    print("=" * 80)
    
    required_tests = [
        "test_sqlite_filtering_before_html_parsing",
        "test_dynamodb_check_before_scraping", 
        "test_court_id_njd_filtering",
        "test_pipeline_skips_scraping_for_existing_dockets",
        "test_error_handling_dynamodb_check_failure",
        "test_performance_improvement_no_unnecessary_scraping",
        "test_async_changes_work_correctly",
        "test_case_filtering_johnson_and_johnson_only",
        "test_docket_number_extraction_and_validation",
        "test_integration_with_real_dynamodb_init_flow"
    ]
    
    try:
        with open(TEST_FILE, 'r') as f:
            test_content = f.read()
        
        missing_tests = []
        for test_name in required_tests:
            if test_name not in test_content:
                missing_tests.append(test_name)
        
        if missing_tests:
            print("❌ MISSING TESTS:")
            for test in missing_tests:
                print(f"  - {test}")
            return False
        else:
            print("✅ ALL REQUIRED TESTS ARE PRESENT")
            print(f"Total test methods: {len(required_tests)}")
            return True
            
    except Exception as e:
        print(f"ERROR validating test coverage: {e}")
        return False

def main():
    """Main validation function"""
    print("Starting duplicate detection validation...")
    
    # Validate test coverage first
    coverage_ok = validate_test_coverage()
    
    if not coverage_ok:
        print("\n❌ Test coverage validation failed!")
        return 1
    
    # Run the actual tests
    tests_ok = run_tests()
    
    if not tests_ok:
        print("\n❌ Test execution failed!")
        return 1
    
    print("\n" + "=" * 80)
    print("VALIDATION SUMMARY")
    print("=" * 80)
    print("✅ Test coverage: COMPLETE")
    print("✅ Test execution: SUCCESSFUL")
    print("✅ Duplicate detection functionality: VALIDATED")
    print("\nThe modified logic in process_html_pipeline.py (lines 245-270)")
    print("has been thoroughly tested and validated.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())