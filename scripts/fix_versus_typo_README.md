# Fix Versus Typo Script

This script fixes the `versIus` typo in JSON files and renames files from `Unknown_Case` to the correct case title.

## Usage

### Fix a single date
```bash
python scripts/fix_versus_typo.py 20250616 --dry-run
```

### Fix a date range
```bash
python scripts/fix_versus_typo.py 20250601 20250616 --dry-run
```

### Apply the fixes (remove --dry-run)
```bash
python scripts/fix_versus_typo.py 20250601 20250616
```

## What it does

1. **Fixes the typo**: Changes `"versIus"` to `"versus"` in JSON files
2. **Renames files**: Changes files named `Unknown_Case` to the correct case title based on the versus field
3. **Updates associated files**: Also renames corresponding `.pdf` and `.md` files

## The typo issue

Some JSON files have `"versIus"` instead of `"versus"` (with a capital I instead of lowercase i). This causes:
- Files to be named `Unknown_Case` instead of the actual case title
- The transformer rename operation to fail because it can't find the `versus` field

## Safety

- Use `--dry-run` first to see what will be changed
- The script preserves all data - it only changes the key name
- Original files are moved, not deleted