#!/usr/bin/env python3
"""
Script to rename 'docket_number' key to 'docket_num' in ILND JSON files.
If both keys exist, removes 'docket_number' and keeps 'docket_num'.
"""

import json
import os
import glob
from pathlib import Path


def process_json_file(file_path):
    """Process a single JSON file to rename docket_number to docket_num and remove workflow keys"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        modified = False
        
        # Check if 'docket_number' exists
        if 'docket_number' in data:
            docket_number_value = data['docket_number']
            
            # If 'docket_num' doesn't exist, rename the key
            if 'docket_num' not in data:
                data['docket_num'] = docket_number_value
                modified = True
            
            # Remove the old 'docket_number' key
            del data['docket_number']
            modified = True
        
        # Remove workflow status and execution keys
        keys_to_remove = [
            'workflow_status',
            'total_execution_time', 
            'step_summary'
        ]
        
        for key in keys_to_remove:
            if key in data:
                del data[key]
                modified = True
        
        # Save the modified file if changes were made
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")
        return False


def main():
    """Main function to process all ILND JSON files"""
    # Define the pattern for ILND JSON files
    data_dir = "data/20250818/dockets"
    pattern = os.path.join(data_dir, "ilnd*.json")
    
    # Find all matching files
    json_files = glob.glob(pattern)
    
    if not json_files:
        print(f"No ILND JSON files found in {data_dir}")
        return
    
    print(f"Found {len(json_files)} ILND JSON files to process...")
    
    processed_count = 0
    modified_count = 0
    
    for file_path in json_files:
        filename = os.path.basename(file_path)
        print(f"Processing: {filename}")
        
        if process_json_file(file_path):
            modified_count += 1
            print(f"  ✓ Modified {filename}")
        else:
            print(f"  - No changes needed for {filename}")
        
        processed_count += 1
    
    print(f"\nSummary:")
    print(f"  Files processed: {processed_count}")
    print(f"  Files modified: {modified_count}")
    print(f"  Files unchanged: {processed_count - modified_count}")


if __name__ == "__main__":
    main()