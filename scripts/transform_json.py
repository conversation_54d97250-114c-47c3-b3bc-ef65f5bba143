#!/usr/bin/env python3
"""
JSON Transformation CLI Script

Usage:
    python scripts/transform_json.py <url_or_file_path> [--html-url <html_url>]
    
Examples:
    # Transform JSON from URL
    python scripts/transform_json.py https://example.com/data.json
    
    # Transform local JSON file
    python scripts/transform_json.py data/sample.json
    
    # Transform with custom HTML URL for re-parsing
    python scripts/transform_json.py data/sample.json --html-url https://cdn.lexgenius.ai/file.html

This script fetches JSON data from a URL or file, applies transformation rules,
and displays the properly formatted result to console.
"""

import asyncio
import argparse
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import aiohttp

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.transformer.components.transformation.json_transformer import JSONTransformationService


# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Set to WARNING to reduce noise in console output
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def fetch_json_from_url(url: str) -> Dict[str, Any]:
    """
    Fetch JSON data from a URL.
    
    Args:
        url: URL to fetch JSON from
        
    Returns:
        Parsed JSON data
    """
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            if response.status != 200:
                raise Exception(f"Failed to fetch URL: HTTP {response.status}")
            
            content_type = response.headers.get('content-type', '')
            if 'application/json' in content_type:
                return await response.json()
            else:
                # Try to parse as JSON anyway
                text = await response.text()
                return json.loads(text)


def load_json_from_file(file_path: str) -> Dict[str, Any]:
    """
    Load JSON data from a file.
    
    Args:
        file_path: Path to JSON file
        
    Returns:
        Parsed JSON data
    """
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    with open(path, 'r') as f:
        return json.load(f)


async def transform_json(
    input_source: str,
    html_url: Optional[str] = None,
    verbose: bool = False
) -> Dict[str, Any]:
    """
    Transform JSON data from URL or file.
    
    Args:
        input_source: URL or file path to JSON data
        html_url: Optional HTML URL for re-parsing fields
        verbose: Enable verbose output
        
    Returns:
        Transformed JSON data
    """
    # Determine if input is URL or file
    if input_source.startswith(('http://', 'https://')):
        if verbose:
            print(f"📥 Fetching JSON from URL: {input_source}", file=sys.stderr)
        json_data = await fetch_json_from_url(input_source)
    else:
        if verbose:
            print(f"📂 Loading JSON from file: {input_source}", file=sys.stderr)
        json_data = load_json_from_file(input_source)
    
    # Extract HTML URL from JSON if not provided
    if not html_url:
        # Try to extract from html_s3_upload_ignore.s3_html
        if 'html_s3_upload_ignore' in json_data:
            html_url = json_data['html_s3_upload_ignore'].get('s3_html')
        # Or from s3_html directly
        elif 's3_html' in json_data:
            html_url = json_data['s3_html']
        
        if html_url and verbose:
            print(f"🔗 Using HTML URL from JSON: {html_url}", file=sys.stderr)
    
    # Initialize transformation service
    service = JSONTransformationService(logger=logger)
    
    if verbose:
        print("🔄 Applying transformation rules...", file=sys.stderr)
    
    # Transform the JSON
    transformed_data = await service.transform_json_data(
        raw_data=json_data,
        html_url=html_url
    )
    
    if verbose:
        print("✅ Transformation completed successfully!", file=sys.stderr)
    
    return transformed_data


def display_transformation_summary(
    original: Dict[str, Any],
    transformed: Dict[str, Any]
) -> None:
    """
    Display a summary of the transformation.
    
    Args:
        original: Original JSON data
        transformed: Transformed JSON data
    """
    print("\n" + "="*60, file=sys.stderr)
    print("TRANSFORMATION SUMMARY", file=sys.stderr)
    print("="*60, file=sys.stderr)
    
    # Count changes
    deleted_keys = set(original.keys()) - set(transformed.keys())
    added_keys = set(transformed.keys()) - set(original.keys())
    
    print(f"\n📊 Statistics:", file=sys.stderr)
    print(f"  • Original keys: {len(original)}", file=sys.stderr)
    print(f"  • Transformed keys: {len(transformed)}", file=sys.stderr)
    print(f"  • Deleted keys: {len(deleted_keys)}", file=sys.stderr)
    print(f"  • Added keys: {len(added_keys)}", file=sys.stderr)
    
    if deleted_keys and len(deleted_keys) <= 20:
        print(f"\n🗑️  Deleted keys:", file=sys.stderr)
        for key in sorted(deleted_keys):
            print(f"    - {key}", file=sys.stderr)
    
    # Check attorney changes
    orig_attorneys = original.get('attorney', [])
    trans_attorneys = transformed.get('attorney', [])
    if orig_attorneys != trans_attorneys:
        print(f"\n👥 Attorney changes:", file=sys.stderr)
        print(f"    Original: {len(orig_attorneys)} attorneys", file=sys.stderr)
        print(f"    Transformed: {len(trans_attorneys)} attorneys", file=sys.stderr)
    
    # Check HTML-parsed fields
    html_fields = ['jury_demand', 'is_mdl', 'lead_case']
    html_changes = []
    for field in html_fields:
        if field in transformed and original.get(field) != transformed.get(field):
            html_changes.append(field)
    
    if html_changes:
        print(f"\n🌐 HTML re-parsed fields:", file=sys.stderr)
        for field in html_changes:
            print(f"    - {field}: {original.get(field, 'N/A')} → {transformed.get(field)}", file=sys.stderr)
    
    print("\n" + "="*60, file=sys.stderr)


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description='Transform JSON data with specific cleaning and transformation rules'
    )
    parser.add_argument(
        'input',
        help='URL or file path to JSON data'
    )
    parser.add_argument(
        '--html-url',
        help='HTML URL for re-parsing fields (jury_demand, is_mdl, lead_case)'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    parser.add_argument(
        '--summary', '-s',
        action='store_true',
        help='Show transformation summary'
    )
    parser.add_argument(
        '--indent',
        type=int,
        default=2,
        help='JSON indentation (default: 2)'
    )
    parser.add_argument(
        '--compact', '-c',
        action='store_true',
        help='Output compact JSON (no indentation)'
    )
    
    args = parser.parse_args()
    
    try:
        # Load original data for comparison if summary requested
        original_data = None
        if args.summary:
            if args.input.startswith(('http://', 'https://')):
                original_data = await fetch_json_from_url(args.input)
            else:
                original_data = load_json_from_file(args.input)
        
        # Transform the JSON
        transformed_data = await transform_json(
            input_source=args.input,
            html_url=args.html_url,
            verbose=args.verbose
        )
        
        # Display summary if requested
        if args.summary and original_data:
            display_transformation_summary(original_data, transformed_data)
        
        # Output the transformed JSON
        if args.compact:
            print(json.dumps(transformed_data, separators=(',', ':'), default=str))
        else:
            print(json.dumps(transformed_data, indent=args.indent, default=str))
        
    except Exception as e:
        print(f"❌ Error: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())