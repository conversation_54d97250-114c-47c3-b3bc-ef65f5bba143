import os

def get_project_root():
    """
    Returns the project root path.
    
    This function tries several methods to obtain the project root path:
    1. Import from src.lib.config
    2. Load from environment variable
    3. Use fallback hardcoded path
    
    Returns:
        str: The project root path
    """
    try:
        from src.lib.config_adapter import PROJECT_ROOT
        return PROJECT_ROOT
    except ImportError:
        # If can't import, try to load from environment or use a fallback
        try:
            from dotenv import load_dotenv
            load_dotenv()
            return os.getenv('PROJECT_ROOT', os.path.expanduser('/'))
        except ImportError:
            return os.path.expanduser('/')