#!/usr/bin/env python3
"""
Extract court_id and docket_num from all docket JSON files for a given date.
Usage: python extract_dockets.py --date YYYYMMDD
"""

import argparse
import json
import os
import re
import sys
from pathlib import Path


def extract_court_docket_from_filename(filename):
    """
    Extract court_id and docket_num from filename.
    
    Args:
        filename: File name to extract from
        
    Returns:
        dict with court_id and docket_num or None if not found
    """
    # Pattern: court_id_YY_NNNNN (e.g., mad_23_12345)
    pattern = r'([a-z]+d?)_(\d{2})_(\d{5})'
    match = re.search(pattern, filename.lower())
    if match:
        court_id = match.group(1)
        docket_num = f"{match.group(2)}_{match.group(3)}"
        return {"court_id": court_id, "docket_num": docket_num}
    return None


def main():
    parser = argparse.ArgumentParser(description="Extract docket information from JSON files")
    parser.add_argument(
        "--date",
        required=True,
        help="Date in YYYYMMDD format"
    )
    
    args = parser.parse_args()
    
    # Validate date format
    if not re.match(r'^\d{8}$', args.date):
        print(json.dumps({"error": "Date must be in YYYYMMDD format"}))
        sys.exit(1)
    
    # Get base data directory from environment variable or use default
    base_data_dir = os.environ.get('LEXGENIUS_DATA_DIR', 'data')
    
    # Get dockets directory
    dockets_dir = Path(base_data_dir) / args.date / "dockets"
    
    if not dockets_dir.exists():
        print(json.dumps({"error": f"Directory not found: {dockets_dir}"}))
        sys.exit(1)
    
    # Extract all dockets
    all_dockets = []
    
    for json_file in sorted(dockets_dir.glob("*.json")):
        result = extract_court_docket_from_filename(json_file.name)
        if result:
            all_dockets.append(result)
    
    # Print as JSON array
    print(json.dumps(all_dockets))


if __name__ == "__main__":
    main()