#!/usr/bin/env python3
"""
Remove S3 links ending with .html from docket JSON files.

Usage:
    python src/scripts/remove_s3_link.py --date YYYYMMDD [--dry-run]
"""

import json
import argparse
import os
from pathlib import Path
from rich.console import Console
from rich.progress import track

console = Console()


def remove_html_s3_links(date: str, dry_run: bool = False):
    """Remove S3 links ending with .html from docket JSON files."""
    # Get base data directory from environment variable or use default
    base_data_dir = os.environ.get('LEXGENIUS_DATA_DIR', 'data')
    
    # Use YYYYMMDD format directly
    dockets_path = Path(base_data_dir) / date / "dockets"
    
    if not dockets_path.exists():
        console.print(f"[red]Directory not found: {dockets_path}[/red]")
        return
    
    # Find all JSON files
    json_files = list(dockets_path.glob("*.json"))
    
    if not json_files:
        console.print(f"[yellow]No JSON files found in {dockets_path}[/yellow]")
        return
    
    console.print(f"[green]Found {len(json_files)} JSON files to process[/green]")
    
    modified_count = 0
    
    for json_file in track(json_files, description="Processing JSON files"):
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            # Check if s3_link exists and ends with .html
            if 's3_link' in data and isinstance(data['s3_link'], str) and data['s3_link'].endswith('.html'):
                if dry_run:
                    console.print(f"[yellow]Would remove s3_link from {json_file.name}: {data['s3_link']}[/yellow]")
                else:
                    console.print(f"[blue]Removing s3_link from {json_file.name}: {data['s3_link']}[/blue]")
                    del data['s3_link']
                    
                    # Write updated JSON back to file
                    with open(json_file, 'w') as f:
                        json.dump(data, f, indent=2)
                
                modified_count += 1
                
        except json.JSONDecodeError as e:
            console.print(f"[red]Error reading {json_file.name}: {e}[/red]")
        except Exception as e:
            console.print(f"[red]Error processing {json_file.name}: {e}[/red]")
    
    if dry_run:
        console.print(f"\n[yellow]Dry run complete. Would modify {modified_count} files.[/yellow]")
    else:
        console.print(f"\n[green]Complete. Modified {modified_count} files.[/green]")


def main():
    parser = argparse.ArgumentParser(description="Remove S3 links ending with .html from docket JSON files")
    parser.add_argument('--date', required=True, help='Date in YYYYMMDD format')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')
    
    args = parser.parse_args()
    
    # Validate date format
    if len(args.date) != 8 or not args.date.isdigit():
        console.print("[red]Error: Date must be in YYYYMMDD format[/red]")
        return
    
    remove_html_s3_links(args.date, args.dry_run)


if __name__ == "__main__":
    main()