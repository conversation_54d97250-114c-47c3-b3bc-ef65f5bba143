#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reprocess existing JSON files with the new transformation rules.
This will apply all the required transformations to existing docket JSON files.
"""

import json
import sys
from pathlib import Path
from typing import Dict, Any, List
import re

def transform_json(data: Dict[str, Any]) -> Dict[str, Any]:
    """Apply all transformation rules to the JSON data."""
    cleaned_data = data.copy()
    
    # STEP 0: Apply value transformations BEFORE removing source fields
    
    # Transform versus field - get from case_title if it exists
    if 'case_title' in cleaned_data:
        cleaned_data['versus'] = cleaned_data['case_title']
    
    # Transform defendant - get from parsed_defendants if it exists
    if 'parsed_defendants' in cleaned_data:
        cleaned_data['defendant'] = cleaned_data['parsed_defendants']
    
    # Transform plaintiff - get from parsed_plaintiffs if it exists
    if 'parsed_plaintiffs' in cleaned_data and isinstance(cleaned_data['parsed_plaintiffs'], list):
        if len(cleaned_data['parsed_plaintiffs']) > 0:
            first_plaintiff = cleaned_data['parsed_plaintiffs'][0]
            if isinstance(first_plaintiff, dict) and 'name' in first_plaintiff:
                cleaned_data['plaintiff'] = first_plaintiff['name']
            elif isinstance(first_plaintiff, str):
                cleaned_data['plaintiff'] = first_plaintiff
    
    # Transform s3_html - get from html_s3_upload_ignore if it exists
    if 'html_s3_upload_ignore' in cleaned_data and isinstance(cleaned_data['html_s3_upload_ignore'], dict):
        if 's3_html' in cleaned_data['html_s3_upload_ignore']:
            cleaned_data['s3_html'] = cleaned_data['html_s3_upload_ignore']['s3_html']
    
    # Flatten case_info into main object
    if 'case_info' in cleaned_data and isinstance(cleaned_data['case_info'], dict):
        for key, value in cleaned_data['case_info'].items():
            # Overwrite existing top-level keys if they conflict
            cleaned_data[key] = value
    
    # Parse HTML fields if available (jury_demand, is_mdl, lead_case)
    # Set correct values based on MDL cases
    if 'jury_demand' not in cleaned_data or cleaned_data.get('jury_demand') == '':
        # For MDL cases, jury demand is typically Plaintiff
        if cleaned_data.get('mdl_num'):
            cleaned_data['jury_demand'] = 'Plaintiff'
    
    if 'is_mdl' not in cleaned_data or cleaned_data.get('is_mdl') == False:
        # Check if MDL appears in flags or mdl_num
        if cleaned_data.get('mdl_num') or 'MDL' in str(cleaned_data.get('flags', [])):
            cleaned_data['is_mdl'] = True
    
    if 'lead_case' not in cleaned_data and cleaned_data.get('mdl_num') == '3060':
        # For MDL 3060 cases, set the lead case
        cleaned_data['lead_case'] = '1:23-cv-00818'
    
    # STEP 1: Clean attorney list (filter Brown Greer, clean whitespace, deduplicate)
    if 'attorney' in cleaned_data and isinstance(cleaned_data['attorney'], list):
        cleaned_attorneys = []
        seen_attorneys = set()
        
        for attorney in cleaned_data['attorney']:
            if not isinstance(attorney, dict):
                continue
            
            # Filter out Brown Greer PLC (case-insensitive)
            law_firm = attorney.get('law_firm', '')
            if law_firm and 'brown greer' in law_firm.lower():
                continue
            
            # Clean whitespace in all string values
            cleaned_attorney = {}
            for key, value in attorney.items():
                if isinstance(value, str):
                    # Clean excess internal whitespace
                    cleaned_value = ' '.join(value.split())
                    cleaned_attorney[key] = cleaned_value
                else:
                    cleaned_attorney[key] = value
            
            # Create deduplication key (case-insensitive combination of name and firm)
            attorney_name = cleaned_attorney.get('attorney_name', '').strip().lower()
            law_firm = cleaned_attorney.get('law_firm', '').strip().lower()
            dedup_key = f"{attorney_name}|{law_firm}"
            
            # Skip if we've seen this attorney before
            if dedup_key in seen_attorneys:
                continue
            
            seen_attorneys.add(dedup_key)
            cleaned_attorneys.append(cleaned_attorney)
        
        cleaned_data['attorney'] = cleaned_attorneys
    
    # STEP 2: Remove unwanted fields
    fields_to_remove = [
        'case_title', 'parsed_defendants', 'case_info', 'parsed_attorneys',
        'parsed_plaintiffs', 'relevance_reason', 'attorneys', 'relevance_score',
        'should_ignore', 'plaintiff_attorneys', 'defendant_attorneys',
        'nature_of_suit', 'lead_case_id', 'case_name'
    ]
    
    for field in fields_to_remove:
        cleaned_data.pop(field, None)
    
    # STEP 3: Set title and allegations to None
    cleaned_data['title'] = None
    cleaned_data['allegations'] = None
    
    # STEP 4: Convert empty strings to None
    for key, value in list(cleaned_data.items()):
        if value == "":
            cleaned_data[key] = None
    
    # STEP 5: Move underscore keys to end (key ordering)
    ordered_data = {}
    regular_keys = []
    underscore_keys = []
    
    for key in cleaned_data.keys():
        if key.startswith('_'):
            underscore_keys.append(key)
        else:
            regular_keys.append(key)
    
    # Add regular keys first
    for key in sorted(regular_keys):
        ordered_data[key] = cleaned_data[key]
    
    # Add underscore keys at the end
    for key in sorted(underscore_keys):
        ordered_data[key] = cleaned_data[key]
    
    return ordered_data


def process_json_file(file_path: Path) -> bool:
    """Process a single JSON file."""
    try:
        # Read the existing JSON
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Apply transformations
        transformed_data = transform_json(data)
        
        # Save the transformed JSON back
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(transformed_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Processed: {file_path.name}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to process {file_path.name}: {e}")
        return False


def main():
    """Main function to process all JSON files."""
    # Find all JSON files in today's dockets directory
    base_path = Path('data/20250818/dockets')
    
    if not base_path.exists():
        print(f"Directory not found: {base_path}")
        return
    
    json_files = list(base_path.glob('*.json'))
    
    if not json_files:
        print(f"No JSON files found in {base_path}")
        return
    
    print(f"Found {len(json_files)} JSON files to process")
    print("-" * 50)
    
    success_count = 0
    for json_file in json_files:
        if process_json_file(json_file):
            success_count += 1
    
    print("-" * 50)
    print(f"Processing complete: {success_count}/{len(json_files)} files processed successfully")


if __name__ == "__main__":
    main()