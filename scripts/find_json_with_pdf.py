#!/usr/bin/env python3
"""
Script to find JSON files that have corresponding PDF files with the same base filename.
Iterates through date ranges and checks @data/{iso_date}/dockets/ directories.
"""

import argparse
import os
from datetime import datetime, timedelta
from pathlib import Path
import json


def parse_date(date_str):
    """Parse YYYYMMDD format date string."""
    try:
        return datetime.strptime(date_str, '%Y%m%d')
    except ValueError:
        raise argparse.ArgumentTypeError(f"Invalid date format: {date_str}. Use YYYYMMDD")


def get_date_range(start_date, end_date):
    """Generate all dates between start_date and end_date (inclusive)."""
    current = start_date
    while current <= end_date:
        yield current
        current += timedelta(days=1)


def find_json_with_pdf(data_dir, date_str):
    """Find JSON files that have corresponding PDF files in the given date directory."""
    date_dir = Path(data_dir) / date_str / "dockets"
    
    if not date_dir.exists():
        return []
    
    json_files_with_pdf = []
    
    # Get all JSON files in the directory
    json_files = list(date_dir.glob("*.json"))
    
    for json_file in json_files:
        # Get base filename without extension
        base_name = json_file.stem
        
        # Check if corresponding PDF exists
        pdf_file = date_dir / f"{base_name}.pdf"
        
        if pdf_file.exists():
            json_files_with_pdf.append(str(json_file))
    
    return json_files_with_pdf


def main():
    parser = argparse.ArgumentParser(
        description="Find JSON files with corresponding PDF files in date range"
    )
    parser.add_argument(
        "--start-date",
        type=parse_date,
        required=True,
        help="Start date in YYYYMMDD format"
    )
    parser.add_argument(
        "--end-date", 
        type=parse_date,
        required=True,
        help="End date in YYYYMMDD format"
    )
    parser.add_argument(
        "--data-dir",
        default="data",
        help="Base data directory (default: data)"
    )
    
    args = parser.parse_args()
    
    if args.start_date > args.end_date:
        print("Error: start-date must be before or equal to end-date")
        return 1
    
    # Iterate through each date in the range
    for date in get_date_range(args.start_date, args.end_date):
        date_str = date.strftime('%Y%m%d')
        
        json_files = find_json_with_pdf(args.data_dir, date_str)
        
        if json_files:
            print(f"# {date_str}")
            print("[\n" + ",\n".join(f'    "{file}"' for file in sorted(json_files)) + "\n]")
            print()
    
    return 0


if __name__ == "__main__":
    exit(main())