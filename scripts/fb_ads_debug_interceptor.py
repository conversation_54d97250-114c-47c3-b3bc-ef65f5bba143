"""
API request/response interceptor for Facebook ads debugging.
This module patches the FacebookAPIClient to capture detailed request/response data.
"""

import json
import logging
import time
from typing import Any, Dict, Optional
from functools import wraps

import requests
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table


class APIDebugInterceptor:
    """Intercepts and logs API requests/responses for debugging."""
    
    def __init__(self, console: Console, logger: logging.Logger):
        self.console = console
        self.logger = logger
        self.original_make_request = None
        self.original_search_company = None
        self.original_fetch_ads_page = None
        self.request_counter = 0
    
    def patch_api_client(self, api_client_class):
        """Patch the FacebookAPIClient with debugging capabilities."""
        # Store original methods
        self.original_make_request = api_client_class._make_request
        self.original_search_company = api_client_class.search_company_by_name
        self.original_fetch_ads_page = api_client_class.fetch_ads_page
        
        # Create bound methods that preserve the interceptor instance
        interceptor = self
        
        async def patched_make_request(api_client_self, method: str, url: str, **kwargs):
            return await interceptor._patched_make_request(api_client_self, method, url, **kwargs)
        
        async def patched_search_company(api_client_self, company_name: str):
            return await interceptor._patched_search_company(api_client_self, company_name)
        
        async def patched_fetch_ads_page(api_client_self, company_id: str, start_date: str, 
                                       end_date: str, forward_cursor=None):
            return await interceptor._patched_fetch_ads_page(api_client_self, company_id, start_date, end_date, forward_cursor)
        
        # Patch methods
        api_client_class._make_request = patched_make_request
        api_client_class.search_company_by_name = patched_search_company
        api_client_class.fetch_ads_page = patched_fetch_ads_page
        
        self.console.print("🔧 API client patched with debug interceptor", style="green")
    
    def unpatch_api_client(self, api_client_class):
        """Restore original API client methods."""
        if self.original_make_request:
            api_client_class._make_request = self.original_make_request
        if self.original_search_company:
            api_client_class.search_company_by_name = self.original_search_company
        if self.original_fetch_ads_page:
            api_client_class.fetch_ads_page = self.original_fetch_ads_page
        
        self.console.print("🔧 API client restored to original state", style="blue")
    
    async def _patched_make_request(self, api_client_self, method: str, url: str, **kwargs):
        """Patched version of _make_request with detailed logging."""
        self.request_counter += 1
        
        # Log request details
        self._log_request_details(method, url, kwargs)
        
        # Time the request
        start_time = time.time()
        
        # Call original method
        try:
            response = await self.original_make_request(api_client_self, method, url, **kwargs)
            end_time = time.time()
            
            # Log response details
            self._log_response_details(response, end_time - start_time)
            
            return response
            
        except Exception as e:
            end_time = time.time()
            self._log_request_error(e, end_time - start_time)
            raise
    
    async def _patched_search_company(self, api_client_self, company_name: str):
        """Patched version of search_company_by_name with detailed logging."""
        self.console.print(f"\n🔍 [bold]COMPANY SEARCH INITIATED[/bold]", style="cyan")
        
        search_panel = Panel(
            f"Company Name: [yellow]{company_name}[/yellow]\n"
            f"Endpoint: [cyan]GraphQL Company Search[/cyan]",
            title="📋 Company Search Request",
            border_style="cyan"
        )
        self.console.print(search_panel)
        
        # Call original method and capture result
        try:
            result = await self.original_search_company(api_client_self, company_name)
            
            # Log search results
            if result:
                results_table = Table(title=f"Company Search Results ({len(result)} found)")
                results_table.add_column("ID", style="cyan")
                results_table.add_column("Name", style="white")
                results_table.add_column("Category", style="yellow")
                results_table.add_column("Deleted", style="red")
                results_table.add_column("Restricted", style="magenta")
                
                for company in result[:5]:  # Show first 5 results
                    results_table.add_row(
                        str(company.get('id', 'N/A')),
                        company.get('name', 'N/A'),
                        company.get('category', 'N/A'),
                        str(company.get('pageIsDeleted', False)),
                        str(company.get('pageIsRestricted', False))
                    )
                
                self.console.print(results_table)
                
                if len(result) > 5:
                    self.console.print(f"... and {len(result) - 5} more results")
            else:
                self.console.print("❌ No companies found", style="red")
            
            return result
            
        except Exception as e:
            error_panel = Panel(
                f"[red]Error: {str(e)}[/red]",
                title="❌ Company Search Failed",
                border_style="red"
            )
            self.console.print(error_panel)
            raise
    
    async def _patched_fetch_ads_page(self, api_client_self, company_id: str, start_date: str, 
                               end_date: str, forward_cursor: Optional[str] = None):
        """Patched version of fetch_ads_page with detailed logging."""
        self.console.print(f"\n📄 [bold]ADS PAGE FETCH INITIATED[/bold]", style="blue")
        
        fetch_panel = Panel(
            f"Company ID: [yellow]{company_id}[/yellow]\n"
            f"Date Range: [cyan]{start_date} → {end_date}[/cyan]\n"
            f"Cursor: [dim]{forward_cursor[:50] + '...' if forward_cursor else 'None'}[/dim]",
            title="📊 Ads Fetch Request",
            border_style="blue"
        )
        self.console.print(fetch_panel)
        
        # Call original method and capture result
        try:
            result = await self.original_fetch_ads_page(
                api_client_self, company_id, start_date, end_date, forward_cursor
            )
            
            # Log fetch results
            if result and 'payload' in result:
                payload = result['payload']
                
                # Handle case where payload is None (valid response indicating no ads)
                if payload is None:
                    results_panel = Panel(
                        f"✅ [green]API call successful[/green]\n"
                        f"📄 [yellow]No ads found for this firm[/yellow]\n"
                        f"This is a valid response - the firm may not have any active ads\n"
                        f"or ads within the specified date range.",
                        title="📈 Ads Fetch Results (No Ads)",
                        border_style="yellow"
                    )
                    self.console.print(results_panel)
                    return result
                
                ads_count = len(payload.get('results', []))
                total_count = payload.get('totalCount', 0)
                has_next = bool(payload.get('forwardCursor'))
                
                results_panel = Panel(
                    f"✅ [green]Ads fetched successfully[/green]\n"
                    f"Ads in page: [yellow]{ads_count}[/yellow]\n"
                    f"Total available: [cyan]{total_count}[/cyan]\n"
                    f"Has next page: [{'green' if has_next else 'red'}]{has_next}[/{'green' if has_next else 'red'}]\n"
                    f"Next cursor: [dim]{payload.get('forwardCursor', 'None')[:50]}[/dim]",
                    title="📈 Ads Fetch Results",
                    border_style="green"
                )
                self.console.print(results_panel)
                
                # Show sample ad data
                if ads_count > 0:
                    sample_ad = payload['results'][0]
                    ad_table = Table(title="Sample Ad Data")
                    ad_table.add_column("Field", style="cyan")
                    ad_table.add_column("Value", style="white")
                    
                    sample_fields = ['adArchiveID', 'startDate', 'endDate', 'pageID', 'pageName']
                    for field in sample_fields:
                        if field in sample_ad:
                            value = str(sample_ad[field])
                            ad_table.add_row(field, value[:100] + '...' if len(value) > 100 else value)
                    
                    self.console.print(ad_table)
            else:
                error_panel = Panel(
                    "[red]❌ No valid payload received[/red]",
                    title="Ads Fetch Failed",
                    border_style="red"
                )
                self.console.print(error_panel)
            
            return result
            
        except Exception as e:
            error_panel = Panel(
                f"[red]Error: {str(e)}[/red]",
                title="❌ Ads Fetch Failed",
                border_style="red"
            )
            self.console.print(error_panel)
            raise
    
    def _log_request_details(self, method: str, url: str, kwargs: Dict[str, Any]):
        """Log detailed request information."""
        self.console.print(f"\n🌐 [bold]HTTP REQUEST #{self.request_counter}[/bold]", style="blue")
        
        # Create request details table
        request_table = Table(title=f"{method} Request Details")
        request_table.add_column("Property", style="cyan")
        request_table.add_column("Value", style="white")
        
        request_table.add_row("Method", method)
        request_table.add_row("URL", url)
        
        # Log headers
        headers = kwargs.get('headers', {})
        if headers:
            headers_str = json.dumps(dict(headers), indent=2)
            request_table.add_row("Headers", headers_str[:200] + '...' if len(headers_str) > 200 else headers_str)
        
        # Log parameters
        params = kwargs.get('params', {})
        if params:
            params_str = json.dumps(params, indent=2)
            request_table.add_row("Params", params_str[:200] + '...' if len(params_str) > 200 else params_str)
        
        # Log form data
        data = kwargs.get('data', {})
        if data:
            # Filter sensitive data
            filtered_data = {k: v for k, v in data.items() if 'token' not in k.lower() and 'key' not in k.lower()}
            data_str = json.dumps(filtered_data, indent=2)
            request_table.add_row("Form Data", data_str[:300] + '...' if len(data_str) > 300 else data_str)
        
        self.console.print(request_table)
        
        # Log to file with full details
        self.logger.debug(f"HTTP {method} {url}", extra={
            'request_details': {
                'method': method,
                'url': url,
                'headers': dict(headers) if headers else {},
                'params': params,
                'data': data,
                'kwargs': {k: v for k, v in kwargs.items() if k not in ['headers', 'params', 'data']}
            }
        })
    
    def _log_response_details(self, response: Optional[requests.Response], duration: float):
        """Log detailed response information."""
        if response is None:
            self.console.print("❌ No response received", style="red")
            return
        
        # Create response details table
        response_table = Table(title=f"Response Details ({duration:.2f}s)")
        response_table.add_column("Property", style="cyan")
        response_table.add_column("Value", style="white")
        
        response_table.add_row("Status Code", str(response.status_code))
        response_table.add_row("Duration", f"{duration:.2f}s")
        
        # Response headers
        content_type = response.headers.get('content-type', 'unknown')
        content_length = response.headers.get('content-length', 'unknown')
        response_table.add_row("Content-Type", content_type)
        response_table.add_row("Content-Length", content_length)
        
        self.console.print(response_table)
        
        # Show response content preview - ENHANCED for HTML/JavaScript responses
        try:
            response_text = response.text
            if response_text:
                # Save full response to file for detailed analysis
                if 'facebook.com' in response.url:
                    timestamp = time.strftime("%H%M%S")
                    response_file = f"data/20250715/logs/facebook_response_{timestamp}.html"
                    try:
                        import os
                        os.makedirs(os.path.dirname(response_file), exist_ok=True)
                        with open(response_file, 'w', encoding='utf-8') as f:
                            f.write(response_text)
                        self.console.print(f"💾 Full response saved to: {response_file}", style="cyan")
                    except Exception as save_error:
                        self.console.print(f"⚠️  Could not save response file: {save_error}", style="yellow")
                
                # Try to parse as JSON for pretty printing
                try:
                    if 'json' in content_type or response_text.strip().startswith(('{', '[')):
                        # Remove Facebook security prefix if present
                        clean_text = response_text
                        if clean_text.startswith("for (;;);"):
                            clean_text = clean_text[len("for (;;);"):]
                        
                        response_json = json.loads(clean_text)
                        
                        # Pretty print JSON with syntax highlighting - SHOW MORE CONTENT
                        json_str = json.dumps(response_json, indent=2)
                        
                        # For Facebook ads responses, show more content
                        if 'payload' in response_json:
                            payload = response_json.get('payload')
                            if payload is None:
                                self.console.print("🔍 PAYLOAD IS NULL - This indicates no ads found", style="yellow")
                            else:
                                self.console.print(f"🔍 PAYLOAD TYPE: {type(payload)}", style="cyan")
                                if isinstance(payload, dict):
                                    self.console.print(f"🔍 PAYLOAD KEYS: {list(payload.keys())}", style="cyan")
                        
                        # Show more of the response for debugging
                        display_length = 3000 if 'facebook.com' in response.url else 1000
                        if len(json_str) > display_length:
                            json_str = json_str[:display_length] + f"\n... (truncated, full response saved to file)"
                        
                        syntax = Syntax(json_str, "json", theme="monokai", line_numbers=True)
                        response_panel = Panel(
                            syntax,
                            title="📄 Response Content (JSON)",
                            border_style="green"
                        )
                        self.console.print(response_panel)
                    else:
                        # Plain text/HTML response - show more for HTML
                        if 'html' in content_type:
                            preview_length = 2000
                            title = "📄 Response Content (HTML)"
                        else:
                            preview_length = 500
                            title = "📄 Response Content (Text)"
                            
                        preview = response_text[:preview_length]
                        if len(response_text) > preview_length:
                            preview += f"\n... (truncated from {len(response_text)} chars, full response saved to file)"
                        
                        text_panel = Panel(
                            preview,
                            title=title,
                            border_style="yellow"
                        )
                        self.console.print(text_panel)
                        
                except json.JSONDecodeError:
                    # Not JSON, show as text/HTML
                    if 'html' in content_type:
                        preview_length = 2000
                        title = "📄 Response Content (HTML - Not JSON)"
                    else:
                        preview_length = 500
                        title = "📄 Response Content (Raw)"
                        
                    preview = response_text[:preview_length]
                    if len(response_text) > preview_length:
                        preview += f"\n... (truncated from {len(response_text)} chars, full response saved to file)"
                    
                    text_panel = Panel(
                        preview,
                        title=title,
                        border_style="yellow"
                    )
                    self.console.print(text_panel)
        except Exception as e:
            self.console.print(f"⚠️  Could not read response content: {e}", style="yellow")
        
        # Log to file
        self.logger.debug(f"HTTP Response {response.status_code}", extra={
            'response_details': {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'duration': duration,
                'content_length': len(response.text) if hasattr(response, 'text') else 0,
                'content_preview': response.text[:1000] if hasattr(response, 'text') else None
            }
        })
    
    def _log_request_error(self, error: Exception, duration: float):
        """Log request error details."""
        error_panel = Panel(
            f"[red]Error: {str(error)}[/red]\n"
            f"Duration: {duration:.2f}s\n"
            f"Type: {type(error).__name__}",
            title="❌ Request Failed",
            border_style="red"
        )
        self.console.print(error_panel)
        
        # Log to file
        self.logger.error(f"HTTP Request failed: {error}", extra={
            'error_details': {
                'error_type': type(error).__name__,
                'error_message': str(error),
                'duration': duration
            }
        })


def apply_debug_patches(console: Console, logger: logging.Logger):
    """Apply debug patches to Facebook API classes."""
    try:
        from src.services.fb_ads.api_client import FacebookAPIClient
        
        interceptor = APIDebugInterceptor(console, logger)
        interceptor.patch_api_client(FacebookAPIClient)
        
        return interceptor
        
    except ImportError as e:
        console.print(f"⚠️  Could not import FacebookAPIClient for patching: {e}", style="yellow")
        logger.warning(f"Debug patching failed: {e}")
        return None