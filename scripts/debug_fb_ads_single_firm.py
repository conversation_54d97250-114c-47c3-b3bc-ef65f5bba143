#!/usr/bin/env python3
"""
Debug script for single firm Facebook ads processing with Rich formatting and file logging.
This script provides detailed debugging information for troubleshooting FB ads processing issues.
"""

import argparse
import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict

import yaml
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
from rich.table import Table
from rich.tree import Tree

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config_models.base import WorkflowConfig
from src.factories.main_factory import MainServiceFactory
from src.utils.date import DateUtils
from scripts.fb_ads_debug_interceptor import apply_debug_patches


class FBAdsDebugLogger:
    """Enhanced logger that outputs to both Rich console and file."""

    def __init__(self, console: Console, log_file_path: str):
        self.console = console
        self.log_file_path = log_file_path

        # Ensure log directory exists
        os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

        # Set up file logging
        self.file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
        self.file_formatter = logging.Formatter(
            '[%(asctime)s.%(msecs)03d] [%(levelname)s] [%(name)s:%(lineno)d] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.file_handler.setFormatter(self.file_formatter)

        # Configure root logger for file output
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        root_logger.addHandler(self.file_handler)

        # Set FB ads modules to DEBUG level
        fb_modules = [
            'src.services.fb_ads',
            'src.services.fb_ads.api_client',
            'src.services.fb_ads.session_manager',
            'src.services.fb_ads.workflow_service',
            'src.services.fb_ads.orchestrator',
        ]

        for module in fb_modules:
            logger = logging.getLogger(module)
            logger.setLevel(logging.DEBUG)

    def info(self, message: str, extra_data: Dict[str, Any] = None):
        """Log info message to both console and file."""
        self.console.print(f"ℹ️  {message}", style="blue")
        logging.info(f"{message} {json.dumps(extra_data, default=str) if extra_data else ''}")

    def success(self, message: str, extra_data: Dict[str, Any] = None):
        """Log success message to both console and file."""
        self.console.print(f"✅ {message}", style="green")
        logging.info(f"SUCCESS: {message} {json.dumps(extra_data, default=str) if extra_data else ''}")

    def warning(self, message: str, extra_data: Dict[str, Any] = None):
        """Log warning message to both console and file."""
        self.console.print(f"⚠️  {message}", style="yellow")
        logging.warning(f"{message} {json.dumps(extra_data, default=str) if extra_data else ''}")

    def error(self, message: str, extra_data: Dict[str, Any] = None):
        """Log error message to both console and file."""
        self.console.print(f"❌ {message}", style="red")
        logging.error(f"{message} {json.dumps(extra_data, default=str) if extra_data else ''}")

    def debug(self, message: str, extra_data: Dict[str, Any] = None):
        """Log debug message to both console and file."""
        if extra_data:
            self.console.print(f"🔍 {message}", style="dim cyan")
            # Pretty print structured data
            if isinstance(extra_data, dict) and any(k in extra_data for k in ['url', 'status_code', 'response']):
                # This looks like API request/response data
                self._log_api_data(extra_data)
        else:
            self.console.print(f"🔍 {message}", style="dim cyan")
        logging.debug(f"{message} {json.dumps(extra_data, default=str) if extra_data else ''}")

    def _log_api_data(self, data: Dict[str, Any]):
        """Log API request/response data in a formatted way."""
        if 'url' in data:
            table = Table(title="API Request Details")
            table.add_column("Field", style="cyan")
            table.add_column("Value", style="white")

            for key, value in data.items():
                if key == 'response' and isinstance(value, str):
                    # Truncate long responses for console
                    display_value = value[:200] + "..." if len(value) > 200 else value
                    table.add_row(key, display_value)
                else:
                    table.add_row(key, str(value))

            self.console.print(table)


class FBAdsSingleFirmDebugger:
    """Main debugger class for single firm Facebook ads processing."""

    def __init__(self, firm_id: str, date_str: str = None, verbose: bool = False):
        self.firm_id = firm_id
        self.date_str = date_str or datetime.now().strftime('%m/%d/%y')
        self.verbose = verbose

        # Set up Rich console
        self.console = Console()

        # Set up logging
        iso_date = DateUtils.date_to_iso(self.date_str)
        log_dir = f"data/{iso_date}/logs"
        log_file = f"{log_dir}/debug_fb_ads.log"
        self.logger = FBAdsDebugLogger(self.console, log_file)

        # Apply debug patches
        self.interceptor = apply_debug_patches(self.console, logging.getLogger())

        # Display header
        self._show_header(log_file)

    def _show_header(self, log_file: str):
        """Display the debug session header."""
        header_panel = Panel.fit(
            f"[bold blue]Facebook Ads Debug Session[/bold blue]\n"
            f"Firm ID: [yellow]{self.firm_id}[/yellow]\n"
            f"Date: [yellow]{self.date_str}[/yellow]\n"
            f"Log File: [cyan]{log_file}[/cyan]\n"
            f"Verbose: [yellow]{self.verbose}[/yellow]",
            title="🔍 Debug Configuration",
            border_style="blue"
        )
        self.console.print(header_panel)
        self.console.print()

    async def run_debug_session(self):
        """Run the complete debug session."""
        try:
            # Step 1: Load and validate configuration
            config = await self._load_config()

            # Step 2: Initialize services
            factory = await self._initialize_services(config)

            # Step 3: Debug single firm processing
            await self._debug_single_firm(factory)

        except Exception as e:
            self.logger.error(f"Debug session failed: {e}")
            import traceback
            self.console.print(Panel(
                traceback.format_exc(),
                title="❌ Exception Details",
                border_style="red"
            ))
            sys.exit(1)

    async def _load_config(self) -> WorkflowConfig:
        """Load and validate FB ads configuration."""
        self.logger.info("Loading Facebook ads configuration...")

        # Load fb_ads.yml
        config_path = project_root / "config" / "fb_ads.yml"
        if not config_path.exists():
            self.logger.error(f"Configuration file not found: {config_path}")
            sys.exit(1)

        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)

        # Add required fields for WorkflowConfig validation
        config_data['name'] = 'fb_ads_debug'

        # Override date if provided
        if self.date_str:
            config_data['date'] = self.date_str
            config_data['iso_date'] = DateUtils.date_to_iso(self.date_str)

        # Enable debug settings
        config_data['verbose'] = True
        config_data['development'] = config_data.get('development', {})
        config_data['development']['enable_debug_output'] = True

        # Create WorkflowConfig
        try:
            config = WorkflowConfig(**config_data)
            self.logger.success("Configuration loaded successfully")

            # Show configuration summary
            config_table = Table(title="Configuration Summary")
            config_table.add_column("Setting", style="cyan")
            config_table.add_column("Value", style="white")

            key_settings = [
                ('date', config_data.get('date')),
                ('fb_ads', config_data.get('fb_ads')),
                ('headless', config_data.get('headless')),
                ('use_proxy', config_data.get('use_proxy')),
                ('mobile_proxy', config_data.get('mobile_proxy')),
                ('testing', config_data.get('testing')),
                ('verbose', config_data.get('verbose')),
            ]

            for key, value in key_settings:
                config_table.add_row(key, str(value))

            self.console.print(config_table)
            self.console.print()

            return config

        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            raise

    async def _initialize_services(self, config: WorkflowConfig) -> MainServiceFactory:
        """Initialize services with dependency injection."""
        self.logger.info("Initializing services with dependency injection...")

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            TimeElapsedColumn(),
            console=self.console
        ) as progress:

            task = progress.add_task("Initializing DI container...", total=None)

            try:
                factory = MainServiceFactory(config)
                await factory.__aenter__()

                progress.update(task, description="✅ Services initialized")

                self.logger.success("Dependency injection container created")
                return factory

            except Exception as e:
                progress.update(task, description="❌ Service initialization failed")
                self.logger.error(f"Service initialization failed: {e}")
                raise

    async def _debug_single_firm(self, factory: MainServiceFactory):
        """Debug single firm processing with detailed logging."""
        self.logger.info(f"Starting debug session for firm ID: {self.firm_id}")

        # Create debug workflow tree
        workflow_tree = Tree("🔍 FB Ads Debug Workflow")
        session_node = workflow_tree.add("1. Session Management")
        search_node = workflow_tree.add("2. Company Search")
        fetch_node = workflow_tree.add("3. Ads Fetching")
        process_node = workflow_tree.add("4. Data Processing")

        self.console.print(workflow_tree)
        self.console.print()

        try:
            # Use the existing CLI approach to run single firm processing
            self.logger.info("Accessing FB ads container from factory...")

            # Get the container's FB ads orchestrator
            fb_ads_orchestrator = factory._container.fb_ads.facebook_ads_orchestrator()

            self.logger.info("Running single firm scrape via orchestrator...")

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                TimeElapsedColumn(),
                console=self.console
            ) as progress:

                task = progress.add_task(f"Processing firm {self.firm_id}...", total=None)

                try:
                    # Use the same method as the CLI
                    result = await fb_ads_orchestrator.run_single_firm_scrape(self.firm_id)

                    if result:
                        progress.update(task, description="✅ Firm processing completed")
                        self.logger.success(f"Single firm processing completed successfully for {self.firm_id}")

                        # Show results summary
                        results_panel = Panel(
                            f"[green]✅ Processing completed successfully[/green]\n"
                            f"Firm ID: {self.firm_id}\n"
                            f"Check the log file for detailed API interactions and responses",
                            title="🎉 Results Summary",
                            border_style="green"
                        )
                        self.console.print(results_panel)

                    else:
                        progress.update(task, description="❌ Firm processing failed")
                        self.logger.error(f"Single firm processing failed for {self.firm_id}")

                        # Show failure summary
                        failure_panel = Panel(
                            f"[red]❌ Processing failed[/red]\n"
                            f"Firm ID: {self.firm_id}\n"
                            f"Check the log file for error details and API responses\n"
                            f"Common issues: Invalid firm ID, API blocks, session problems",
                            title="💥 Failure Summary",
                            border_style="red"
                        )
                        self.console.print(failure_panel)

                except Exception as e:
                    progress.update(task, description="💥 Exception occurred")
                    self.logger.error(f"Exception during firm processing: {e}")
                    import traceback
                    self.logger.debug("Full traceback", {"traceback": traceback.format_exc()})

                    # Show exception details
                    exception_panel = Panel(
                        f"[red]Exception: {str(e)}[/red]\n"
                        f"Type: {type(e).__name__}\n"
                        f"Check the log file for full traceback and debug details",
                        title="💥 Exception Details",
                        border_style="red"
                    )
                    self.console.print(exception_panel)
                    raise

        except Exception as e:
            self.logger.error(f"Debug session failed: {e}")
            raise
        finally:
            # Cleanup
            await factory.__aexit__(None, None, None)


async def main():
    """Main entry point for the debug script."""
    parser = argparse.ArgumentParser(
        description="Debug Facebook ads processing for a single firm with Rich formatting"
    )
    parser.add_argument(
        '--firm-id',
        required=True,
        help='Firm ID to debug (e.g., "123456789")'
    )
    parser.add_argument(
        '--date',
        help='Date in MM/DD/YY format (default: today)'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose debug output'
    )

    args = parser.parse_args()

    # Create and run debugger
    debugger = FBAdsSingleFirmDebugger(
        firm_id=args.firm_id,
        date_str=args.date,
        verbose=args.verbose
    )

    await debugger.run_debug_session()


if __name__ == "__main__":
    asyncio.run(main())
