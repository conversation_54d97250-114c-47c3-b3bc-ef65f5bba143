#!/usr/bin/env python3
"""
Force Report Update Script

This script forces CloudFront cache invalidation when configuration changes 
are made (e.g., updating section titles).

Usage:
    python src/scripts/force_report_update.py [--date YYYYMMDD] [--weekly]
"""

import argparse
import asyncio
import logging
import os
import sys
from datetime import datetime, date
from typing import Optional

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from dotenv import load_dotenv
from rich.console import Console
from rich.prompt import Confirm
from rich.logging import RichHandler

# Load environment variables
load_dotenv()

try:
    from src.infrastructure.messaging.cloudfront import CloudFrontService
except ImportError as e:
    print(f"Error importing CloudFrontService: {e}")
    print("Make sure you're in the correct conda environment: conda activate lexgenius")
    sys.exit(1)

console = Console()


async def force_cache_invalidation(iso_date: str, is_weekly: bool = False) -> bool:
    """Force CloudFront cache invalidation for all report-related paths."""
    
    # Get CloudFront configuration from environment
    distribution_id = os.environ.get('CLOUDFRONT_DISTRIBUTION_ID')
    aws_access_key = os.environ.get('AWS_ACCESS_KEY') or os.environ.get('AWS_ACCESS_KEY_ID')
    aws_secret_key = os.environ.get('AWS_SECRET_KEY') or os.environ.get('AWS_SECRET_ACCESS_KEY')
    aws_region = os.environ.get('AWS_REGION', 'us-east-1')
    
    # Check for missing credentials
    missing_vars = []
    if not distribution_id:
        missing_vars.append("CLOUDFRONT_DISTRIBUTION_ID")
    if not aws_access_key:
        missing_vars.append("AWS_ACCESS_KEY or AWS_ACCESS_KEY_ID")
    if not aws_secret_key:
        missing_vars.append("AWS_SECRET_KEY or AWS_SECRET_ACCESS_KEY")
    
    if missing_vars:
        console.print(f"[red]Error: Missing required environment variables:[/red]")
        for var in missing_vars:
            console.print(f"  - {var}")
        console.print("\n[yellow]Please set these in your .env file or environment[/yellow]")
        return False
    
    try:
        async with CloudFrontService(
            distribution_id=distribution_id,
            aws_access_key=aws_access_key,
            aws_secret_key=aws_secret_key,
            aws_region=aws_region
        ) as cf_service:
            
            # Create comprehensive list of paths to invalidate
            paths = [
                f'/{iso_date}/index.html',
                f'/{iso_date}/index-weekly.html' if is_weekly else None,
                f'/{iso_date}/*',  # All files for the date
                '/index.html',  # Root index
                '/assets/*',  # All assets
                '/*'  # Nuclear option - invalidate everything
            ]
            
            # Filter out None values
            paths = [p for p in paths if p is not None]
            
            console.print(f"\n[cyan]Paths to invalidate:[/cyan]")
            for path in paths:
                console.print(f"  - {path}")
            
            # Ask for confirmation for nuclear option
            if '/*' in paths:
                console.print("\n[yellow]Warning: This will invalidate ALL cached content![/yellow]")
                if not Confirm.ask("Do you want to continue?"):
                    console.print("[red]Invalidation cancelled[/red]")
                    return False
            
            console.print(f"\n[green]Invalidating {len(paths)} paths...[/green]")
            success = await cf_service.invalidate_paths(paths)
            
            if success:
                console.print("[green]✓ Cache invalidation completed successfully![/green]")
            else:
                console.print("[red]✗ Cache invalidation failed![/red]")
            
            return success
            
    except Exception as e:
        console.print(f"[red]Error during cache invalidation: {e}[/red]")
        logging.error(f"Cache invalidation error: {e}", exc_info=True)
        return False




async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Force CloudFront cache invalidation for reports')
    parser.add_argument('--date', type=str, help='Date in YYYYMMDD format (default: today)')
    parser.add_argument('--weekly', action='store_true', help='Include weekly report paths')
    parser.add_argument('--no-confirm', action='store_true', help='Skip confirmation prompts')
    
    args = parser.parse_args()
    
    # Setup logging with Rich handler
    logging.basicConfig(
        level=logging.INFO,
        format='%(message)s',
        handlers=[RichHandler(rich_tracebacks=True)]
    )
    
    # Get date
    if args.date:
        try:
            # Validate date format
            datetime.strptime(args.date, '%Y%m%d')
            iso_date = args.date
        except ValueError:
            console.print(f"[red]Invalid date format: {args.date}[/red]")
            console.print("[yellow]Please use YYYYMMDD format (e.g., 20250620)[/yellow]")
            return False
    else:
        iso_date = date.today().strftime('%Y%m%d')
    
    console.print(f"\n[bold]CloudFront Cache Invalidation[/bold]")
    console.print(f"Date: [cyan]{iso_date}[/cyan]")
    console.print(f"Type: [cyan]{'Weekly Report' if args.weekly else 'Daily Report'}[/cyan]")
    
    # Set global no-confirm flag for nuclear option
    if args.no_confirm:
        global Confirm
        Confirm.ask = lambda *args, **kwargs: True
    
    # Invalidate cache
    success = await force_cache_invalidation(iso_date, args.weekly)
    
    if success:
        console.print("\n[green]✓ Cache invalidation completed![/green]")
        console.print("\n[bold]To regenerate the report, run:[/bold]")
        if args.weekly:
            console.print(f"[cyan]python src/main.py --params config/weekly_report.yml --date {iso_date}[/cyan]")
        else:
            console.print(f"[cyan]python src/main.py --params config/report.yml --date {iso_date}[/cyan]")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)