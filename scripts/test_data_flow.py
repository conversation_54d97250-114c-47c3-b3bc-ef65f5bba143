#!/usr/bin/env python3
"""Test data flow in new reports service."""

import asyncio
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
from src.services.reports.data_loader_service import ReportsDataLoaderService
from src.services.reports.processing_service import ReportsProcessingService
from src.services.reports.config_service import ReportsConfigService
import yaml

async def main():
    # Load config
    with open('config/report.yml', 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # Add missing bucket_name from environment
    config_dict['bucket_name'] = os.getenv('S3_BUCKET', 'lexgenius-dockets')
    
    print("=" * 80)
    print("Testing data flow for report generation")
    print("=" * 80)
    
    # Initialize config service
    config_service = ReportsConfigService(config_dict, is_weekly=False)
    config = config_service.get_config()
    
    print(f"\n1. Configuration:")
    print(f"   Date from config: {config.report_date_str}")
    print(f"   ISO date: {config.iso_date}")
    print(f"   Seven days ago: {config.seven_days_ago}")
    
    # Initialize storage
    storage = AsyncDynamoDBStorage(config_dict)
    
    async with storage:
        # Test direct repository queries
        print(f"\n2. Direct Repository Queries:")
        pacer_repo = PacerRepository(storage)
        fb_repo = FBArchiveRepository(storage)
        
        # Check PACER data
        filing_items = await pacer_repo.query_by_filing_date(config.iso_date)
        added_items = await pacer_repo.query_by_added_on_range(config.iso_date, config.iso_date)
        print(f"   PACER FilingDate {config.iso_date}: {len(filing_items)} items")
        print(f"   PACER AddedOn {config.iso_date}: {len(added_items)} items")
        
        # Check FB data
        fb_items = await fb_repo.query_by_start_date(config.iso_date)
        print(f"   FB Ads StartDate {config.iso_date}: {len(fb_items)} items")
        
        # Test data loader service
        print(f"\n3. Data Loader Service:")
        from src.utils.law_firm import LawFirmNameHandler
        law_firm_handler = LawFirmNameHandler()
        data_loader = ReportsDataLoaderService(config, pacer_repo, fb_repo, law_firm_handler)
        
        # Load PACER data
        docket_df = await data_loader.load_pacer_dataframe(is_weekly=False)
        print(f"   Docket DataFrame shape: {docket_df.shape}")
        if not docket_df.empty:
            print(f"   Docket columns: {list(docket_df.columns)[:10]}...")
            print(f"   First docket: {docket_df.iloc[0]['docket_num']} - {docket_df.iloc[0]['title']}")
        
        # Load FB ads data
        ad_df = await data_loader.load_ad_dataframe()
        print(f"   Ad DataFrame shape: {ad_df.shape}")
        if not ad_df.empty:
            print(f"   Ad columns: {list(ad_df.columns)[:10]}...")
            print(f"   First ad: {ad_df.iloc[0]['page_name']} - {ad_df.iloc[0]['ad_archive_id']}")
        
        # Test processing service
        print(f"\n4. Processing Service:")
        processing_service = ReportsProcessingService(config, pacer_repo)
        
        # Process all data through the service
        processed_data = await processing_service.process_all_data(
            docket_df=docket_df,
            ad_df=ad_df,
            is_weekly=False
        )
        
        print(f"   Litigation titles: {len(processed_data.get('grouped_litigation', {}))}")
        print(f"   Law firm groupings: {len(processed_data.get('firm_groupings', {}))}")
        print(f"   Ad summaries: {len(processed_data.get('filtered_ad_summaries', []))}")

if __name__ == "__main__":
    asyncio.run(main())