#!/usr/bin/env python3
"""Quick check for June 6, 2025 data."""

import asyncio
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.repositories.pacer_repository import PacerRepository
from src.repositories.fb_archive_repository import FBArchiveRepository
import yaml

async def main():
    # Load config
    with open('config/report.yml', 'r') as f:
        config_dict = yaml.safe_load(f)
    
    # The target date
    target_date = "20250606"
    
    print(f"Checking for data on {target_date}...")
    
    # Initialize storage
    storage = AsyncDynamoDBStorage(config_dict)
    
    async with storage:
        # Check PACER data by FilingDate
        pacer_repo = PacerRepository(storage)
        filing_items = await pacer_repo.query_by_filing_date(target_date)
        print(f"PACER FilingDate {target_date}: {len(filing_items)} items")
        
        # Check PACER data by AddedOn (this is what daily reports use!)
        added_items = await pacer_repo.query_by_added_on_range(target_date, target_date)
        print(f"PACER AddedOn {target_date}: {len(added_items)} items")
        
        # Check FB ads data  
        fb_repo = FBArchiveRepository(storage)
        fb_items = await fb_repo.query_by_start_date(target_date)
        print(f"FB Ads StartDate {target_date}: {len(fb_items)} items")
        
        # Also check June 17 (today's date) since that might be what AddedOn is
        today_date = "20250617"
        print(f"\nChecking AddedOn for today ({today_date})...")
        added_today = await pacer_repo.query_by_added_on_range(today_date, today_date)
        print(f"PACER AddedOn {today_date}: {len(added_today)} items")

if __name__ == "__main__":
    asyncio.run(main())