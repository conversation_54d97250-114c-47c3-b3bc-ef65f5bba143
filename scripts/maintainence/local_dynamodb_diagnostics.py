#!/usr/bin/env python3

from typing import Dict, Any

import boto3
from botocore.exceptions import ClientError
from tabulate import tabulate


class DynamoDBDiagnostics:
    def __init__(self, endpoint_url: str = "http://localhost:8000"):
        self.dynamodb = boto3.resource('dynamodb', endpoint_url=endpoint_url)
        self.client = boto3.client('dynamodb', endpoint_url=endpoint_url)

    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get detailed info about a specific table."""
        try:
            table = self.dynamodb.Table(table_name)
            description = self.client.describe_table(TableName=table_name)['Table']

            # Get item count and size
            metrics = {
                'Item count': description.get('ItemCount', 0),
                'Table size (bytes)': description.get('TableSizeBytes', 0),
                'Status': description.get('TableStatus', 'Unknown'),
                'Creation Date': description.get('CreationDateTime', '').strftime('%Y-%m-%d %H:%M:%S UTC')
                if description.get('CreationDateTime') else 'Unknown'
            }

            # Get primary key info
            key_schema = description.get('KeySchema', [])
            primary_key = {item['AttributeName']: item['KeyType'] for item in key_schema}

            # Get GSI info
            gsis = description.get('GlobalSecondaryIndexes', [])
            gsi_info = {}
            for gsi in gsis:
                gsi_info[gsi['IndexName']] = {
                    'Key Schema': {item['AttributeName']: item['KeyType'] for item in gsi['KeySchema']},
                    'Status': gsi.get('IndexStatus', 'Unknown'),
                    'Size (bytes)': gsi.get('IndexSizeBytes', 0),
                    'Item count': gsi.get('ItemCount', 0)
                }

            return {
                'Table Metrics': metrics,
                'Primary Key Schema': primary_key,
                'Global Secondary Indexes': gsi_info,
                'Attribute Definitions': description.get('AttributeDefinitions', [])
            }

        except ClientError as e:
            return {'Error': f"Failed to get table info: {str(e)}"}

    def print_diagnostics(self):
        """Print diagnostic information about all tables."""
        try:
            print("\n=== Local DynamoDB Diagnostics ===\n")

            # Get all tables
            tables = self.client.list_tables()['TableNames']
            if not tables:
                print("No tables found in local DynamoDB.")
                return

            print(f"Found {len(tables)} tables:\n")

            for table_name in tables:
                print(f"\n{'=' * 20} {table_name} {'=' * 20}")
                info = self.get_table_info(table_name)

                # Print table metrics
                print("\nTable Metrics:")
                metrics_data = [[k, v] for k, v in info['Table Metrics'].items()]
                print(tabulate(metrics_data, tablefmt='simple'))

                # Print primary key schema
                print("\nPrimary Key Schema:")
                pk_data = [[k, v] for k, v in info['Primary Key Schema'].items()]
                print(tabulate(pk_data, tablefmt='simple'))

                # Print attribute definitions
                print("\nAttribute Definitions:")
                attr_data = [[attr['AttributeName'], attr['AttributeType']]
                             for attr in info['Attribute Definitions']]
                print(tabulate(attr_data, headers=['Name', 'Type'], tablefmt='simple'))

                # Print GSI information
                if info['Global Secondary Indexes']:
                    print("\nGlobal Secondary Indexes:")
                    for index_name, index_info in info['Global Secondary Indexes'].items():
                        print(f"\n  {index_name}:")
                        gsi_data = []
                        # Key schema
                        for key, key_type in index_info['Key Schema'].items():
                            gsi_data.append(['Key Schema', f"{key} ({key_type})"])
                        # Other metrics
                        for key, value in index_info.items():
                            if key != 'Key Schema':
                                gsi_data.append([key, value])
                        print(tabulate(gsi_data, tablefmt='simple'))

                print("\n" + "=" * 50)

        except Exception as e:
            print(f"Error getting diagnostics: {str(e)}")


if __name__ == "__main__":
    # Get info for a specific table
    diagnostics = DynamoDBDiagnostics()
    info = diagnostics.get_table_info('FBAdArchive')

    # Or print all diagnostics
    diagnostics.print_diagnostics()
