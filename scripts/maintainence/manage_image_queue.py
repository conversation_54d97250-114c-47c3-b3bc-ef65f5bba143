#!/usr/bin/env python3
"""
Consolidated Image Queue Management Script

This script consolidates all image queue processing functionality:
- Check queue status and statistics
- Process images using vision models
- Calculate perceptual hashes (PHash)
- Upload to DynamoDB (local/AWS/both)
- Generate AI-enhanced summaries with image text
- Export results
- Reset/rebuild queue states

Usage examples:
    # Check queue status
    python manage_image_queue.py status --date 20250528

    # Process images with vision model
    python manage_image_queue.py process --date 20250528

    # Calculate PHash for processed items
    python manage_image_queue.py calculate-phash --date 20250528

    # Upload to DynamoDB
    python manage_image_queue.py upload --date 20250528 --target both

    # Upload with AI summary generation
    python manage_image_queue.py upload --date 20250528 --target aws --generate-summaries

    # Test summary generation (dry run with 25 items)
    python manage_image_queue.py test-summaries --date 20250528 --target aws --limit 25

    # Upload with summary generation and SQLite cleanup
    python manage_image_queue.py upload --date 20250528 --target both --generate-summaries --clear-sqlite

    # Dry run to see what would be updated
    python manage_image_queue.py upload --date 20250528 --target aws --dry-run --generate-summaries --test-limit 10

    # Full pipeline: process + calculate phash + upload
    python manage_image_queue.py full-pipeline --date 20250528 --target both
"""

import argparse
import asyncio
import logging
import os
import time
from datetime import UTC, datetime
from typing import Any

from colorama import init
from rich.console import Console
from rich.progress import (
    BarColumn,
    MofNCompleteColumn,
    Progress,
    SpinnerColumn,
    TimeRemainingColumn,
)
from rich.table import Table

# Initialize colorama
init(autoreset=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%H:%M:%S",
)
logger = logging.getLogger(__name__)

# Initialize console
console = Console()

# Library imports
try:
    from src.config_models.loader import load_config
    from src.factories.main_factory import MainServiceFactory
    from src.infrastructure.external.deepseek_client import DeepSeekClient
    from src.infrastructure.external.llava_client import (
        LlavaClient as LlavaImageExtractor,
    )
    from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
    from src.infrastructure.storage.s3_async import (
        S3_ACCESS_DENIED_MARKER,
        S3AsyncStorage,
    )
    from src.repositories.fb_archive_repository import FBArchiveRepository
    from src.repositories.fb_image_hash_repository import FBImageHashRepository
    from src.services.ai.deepseek_service import DeepSeekService
    from src.services.fb_ads.image_utils import calculate_image_hash
    from src.services.fb_ads.local_image_queue import LocalImageQueue
except ImportError as e:
    console.print(f"[red]Error importing required libraries: {e}[/red]")
    exit(1)


class ImageQueueManager:
    """Consolidated manager for all image queue operations using DI containers."""

    def __init__(
        self, queue_dir: str = "./data/image_queue", config_date: str = "01/01/70"
    ):
        """Initialize the queue manager."""
        self.queue_dir = queue_dir
        self.config_date = config_date
        self.factory = None
        self.queue = None
        self.s3_manager = None
        self._initialized = False

    async def _ensure_initialized(self):
        """Initialize the container-based services if not already done."""
        if self._initialized:
            return

        try:
            # Create a basic config using the proper config system
            from src.config_models.scraper import ScraperConfig

            config_dict = {
                "name": "image_queue_manager",  # Required by WorkflowConfig
                "date": self.config_date,
                "headless": True,  # Required for factory
                "fb_ads": True,  # Boolean, not dict - required by WorkflowConfig
                "aws": {
                    "bucket_name": "lexgenius-dockets",
                    "region": "us-west-2",
                },
            }

            # Create proper config object
            config = ScraperConfig(**config_dict)

            # Initialize the main service factory with container-based DI
            self.factory = MainServiceFactory(config)
            await self.factory.__aenter__()

            # Get services from the container
            self.queue = self.factory._container.fb_ads.local_image_queue()
            self.s3_manager = self.factory._container.storage.s3_async_storage()

            self._initialized = True
            console.print("[green]✅ Container-based services initialized[/green]")

        except Exception as e:
            console.print(f"[red]❌ Failed to initialize container services: {e}[/red]")
            logger.error(f"Container initialization error: {e}")
            raise

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_initialized()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with proper cleanup."""
        if self.factory:
            try:
                await self.factory.__aexit__(exc_type, exc_val, exc_tb)
            except Exception as e:
                logger.warning(f"Error during factory cleanup: {e}")
        self._initialized = False

    async def get_queue_status(self, date: str | None = None) -> dict[str, Any]:
        """Get comprehensive queue status."""
        await self._ensure_initialized()
        with self.queue._get_connection() as conn:
            if date:
                # Status for specific date
                query = """
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                        SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                        SUM(CASE WHEN status = 'skipped' THEN 1 ELSE 0 END) as skipped,
                        SUM(CASE WHEN phash IS NOT NULL AND phash != '' THEN 1 ELSE 0 END) as has_phash,
                        SUM(CASE WHEN image_text IS NOT NULL AND image_text != '' THEN 1 ELSE 0 END) as has_text
                    FROM image_queue
                    WHERE scrape_date = ?
                """
                result = conn.execute(query, [date]).fetchone()
                return {
                    "date": date,
                    "total": result[0] or 0,
                    "pending": result[1] or 0,
                    "processing": result[2] or 0,
                    "processed": result[3] or 0,
                    "failed": result[4] or 0,
                    "skipped": result[5] or 0,
                    "has_phash": result[6] or 0,
                    "has_text": result[7] or 0,
                    "needs_phash": (result[3] or 0) - (result[6] or 0),
                }
            else:
                # Summary across all dates
                query = """
                    SELECT
                        scrape_date,
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed,
                        SUM(CASE WHEN phash IS NOT NULL AND phash != '' THEN 1 ELSE 0 END) as has_phash
                    FROM image_queue
                    GROUP BY scrape_date
                    ORDER BY scrape_date DESC
                """
                cursor = conn.execute(query)
                dates = []
                for row in cursor:
                    dates.append(
                        {
                            "date": row[0],
                            "total": row[1],
                            "processed": row[2],
                            "has_phash": row[3],
                            "needs_phash": row[2] - row[3],
                        }
                    )
                return {"dates": dates}

    async def display_status(self, date: str | None = None):
        """Display queue status in a formatted table."""
        status = await self.get_queue_status(date)

        if date:
            # Single date status
            table = Table(title=f"Queue Status for {date}")
            table.add_column("Metric", style="cyan")
            table.add_column("Count", justify="right")

            table.add_row("Total Items", str(status["total"]))
            table.add_row("Pending", f"[yellow]{status['pending']}[/yellow]")
            table.add_row("Processing", f"[yellow]{status['processing']}[/yellow]")
            table.add_row("Processed", f"[green]{status['processed']}[/green]")
            table.add_row("Failed", f"[red]{status['failed']}[/red]")
            table.add_row("Skipped", f"[yellow]{status['skipped']}[/yellow]")
            table.add_row("Has PHash", f"[green]{status['has_phash']}[/green]")
            table.add_row("Has Text", f"[green]{status['has_text']}[/green]")
            table.add_row("Needs PHash", f"[yellow]{status['needs_phash']}[/yellow]")

            console.print(table)

            # Check for stuck processing items
            stuck_count = self.queue.count_stuck_processing(date)
            if stuck_count > 0:
                console.print(
                    f"[yellow]⚠️  {stuck_count} items are stuck in processing status.[/yellow]"
                )
                console.print(
                    "[yellow]Use 'reset-processing' command to reset them.[/yellow]"
                )
        else:
            # All dates summary
            table = Table(title="Queue Summary by Date")
            table.add_column("Date", style="cyan")
            table.add_column("Total", justify="right")
            table.add_column("Processed", justify="right", style="green")
            table.add_column("Has PHash", justify="right", style="green")
            table.add_column("Needs PHash", justify="right", style="yellow")

            for date_info in status["dates"]:
                table.add_row(
                    date_info["date"],
                    str(date_info["total"]),
                    str(date_info["processed"]),
                    str(date_info["has_phash"]),
                    str(date_info["needs_phash"]),
                )

            console.print(table)

    async def process_images(
        self,
        date: str | None = None,
        batch_size: int = 100,
        concurrency: int = 6,
        upload_to_aws: bool = False,
        upload_to_local: bool = False,
        upload_to_both: bool = False,
        continuous: bool = False,
        timeout_minutes: int = 10,
    ) -> dict[str, Any]:
        """Process images using vision model."""
        await self._ensure_initialized()

        console.print("[bold cyan]Processing Images with Vision Model[/bold cyan]")
        if continuous:
            console.print(
                f"[yellow]Continuous mode enabled - will poll for new items with {timeout_minutes} minute timeout[/yellow]"
            )

        console.print(
            f"[green]S3 storage ready for bucket: {self.s3_manager.bucket_name}[/green]"
        )

        # Get vision model from container via AI orchestrator
        model_name = "llama3.2-vision:11b-instruct-q4_K_M"
        ai_orchestrator = self.factory._container.fb_ads.ai_orchestrator()

        # Create LlavaClient directly since it's not in the container
        from src.infrastructure.external.llava_client import LlavaClient

        llava_extractor = LlavaClient(
            base_url="http://localhost:11434",
            model=model_name,
            timeout=180,
            config=self.factory._container.config(),
            logger=self.factory._container.core.logger(),
        )

        console.print(
            f"[green]Vision model ready: {model_name} (OCR-optimized)[/green]"
        )

        # Auto-adjust concurrency for mllama models (they don't support parallel requests)
        if "llama" in model_name.lower() and "vision" in model_name.lower():
            if concurrency > 1:
                console.print(
                    f"[yellow]⚠️  Detected mllama model '{model_name}' - reducing concurrency from {concurrency} to 1[/yellow]"
                )
                console.print(
                    "[yellow]   (mllama models don't support parallel requests)[/yellow]"
                )
                concurrency = 1
            else:
                console.print(
                    "[green]✅ Using concurrency=1 (optimal for mllama model)[/green]"
                )
        else:
            console.print(
                f"[green]✅ Using concurrency={concurrency} for vision model[/green]"
            )

        # Check for stuck processing items
        stuck_count = self.queue.count_stuck_processing(date)
        if stuck_count > 0:
            console.print(
                f"[yellow]Found {stuck_count} items stuck in processing status[/yellow]"
            )
            reset_count = self.queue.reset_processing_items(date)
            console.print(
                f"[green]Automatically reset {reset_count} stuck items to pending[/green]"
            )

        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrency)

        total_stats = {"processed": 0, "failed": 0, "skipped": 0, "uploaded": 0}

        all_processed_items = []
        last_item_time = time.time()
        timeout_seconds = timeout_minutes * 60

        # Use LlavaClient as async context manager
        async with llava_extractor:
            # Process in batches
            while True:
                # Get next batch
                pending_items = self.queue.get_pending(
                    limit=batch_size, scrape_date=date
                )

                if not pending_items:
                    if continuous:
                        # Check if we've exceeded the timeout
                        elapsed = time.time() - last_item_time
                        if elapsed >= timeout_seconds:
                            console.print(
                                f"[yellow]No new items for {timeout_minutes} minutes - exiting[/yellow]"
                            )
                            break

                        # Wait before checking again
                        remaining = int(timeout_seconds - elapsed)
                        console.print(
                            f"[dim]No pending items - waiting... ({remaining}s until timeout)[/dim]"
                        )
                        await asyncio.sleep(30)  # Check every 30 seconds
                        continue
                    else:
                        # Not in continuous mode - exit immediately
                        break

                # Reset the timer since we found items
                last_item_time = time.time()

                # Mark items as processing
                image_hashes = [item["image_hash"] for item in pending_items]
                self.queue.mark_processing(image_hashes)

                # Process batch
                tasks = []
                for item in pending_items:
                    task = self._process_single_image(item, llava_extractor, semaphore)
                    tasks.append(task)

                # Wait for all tasks to complete
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Count results
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Task exception: {result}")
                        item = pending_items[i]
                        self.queue.mark_failed(
                            item["image_hash"], f"Task exception: {str(result)}"
                        )
                        total_stats["failed"] += 1
                    elif result and result["status"] == "processed":
                        total_stats["processed"] += 1
                        all_processed_items.append(result)
                    elif result and result["status"] == "skipped":
                        total_stats["skipped"] += 1
                    else:
                        total_stats["failed"] += 1

                console.print(
                    f"Batch complete - Processed: {total_stats['processed']}, Failed: {total_stats['failed']}, Skipped: {total_stats['skipped']}"
                )

        # Upload if requested
        if (upload_to_aws or upload_to_local or upload_to_both) and all_processed_items:
            targets = []
            if upload_to_both:
                targets = ["local", "aws"]
            elif upload_to_local:
                targets = ["local"]
            elif upload_to_aws:
                targets = ["aws"]

            if targets:
                console.print(
                    f"[cyan]Uploading {len(all_processed_items)} items to {', '.join(targets)} DynamoDB...[/cyan]"
                )
                upload_stats = await self._upload_to_dynamodb(
                    all_processed_items,
                    targets,
                    dry_run=False,
                    generate_summaries=False,
                    clear_sqlite=False,
                )
                total_stats["uploaded"] = upload_stats["total_success"]

        # Cleanup handled by container lifecycle management
        return total_stats

    async def _process_single_image(
        self,
        item: dict[str, Any],
        llava_extractor: LlavaImageExtractor,
        semaphore: asyncio.Semaphore,
    ) -> dict[str, Any] | None:
        """Process a single image."""
        async with semaphore:
            start_time = time.time()
            image_hash = item["image_hash"]
            s3_path = item["s3_path"]

            try:
                # Fetch image from S3
                image_data = await self.s3_manager.download_content(s3_path)

                if image_data == S3_ACCESS_DENIED_MARKER:
                    logger.warning(f"S3 Access Denied (403) for {image_hash}")
                    self.queue.mark_skipped(image_hash, "403 Forbidden")
                    return {"status": "skipped", "image_hash": image_hash}

                elif image_data is None:
                    logger.warning(f"S3 key not found: {s3_path}")
                    self.queue.mark_skipped(image_hash, "S3 key not found")
                    return {"status": "skipped", "image_hash": image_hash}

                elif isinstance(image_data, bytes):
                    # Calculate PHash
                    phash = None
                    try:
                        phash_obj = calculate_image_hash(image_data)
                        if phash_obj:
                            phash = str(phash_obj)
                            logger.debug(f"Calculated PHash: {phash} for {image_hash}")
                    except Exception as e:
                        logger.error(f"Failed to calculate PHash for {image_hash}: {e}")

                    # Process with vision model
                    extracted_text = await llava_extractor.extract_text_from_image(
                        image_data
                    )

                    if extracted_text:
                        processing_time_ms = int((time.time() - start_time) * 1000)
                        logger.info(
                            f"Processed {image_hash} - PHash: {phash}, Time: {processing_time_ms}ms"
                        )
                        self.queue.mark_processed(
                            image_hash,
                            extracted_text.strip(),
                            processing_time_ms,
                            phash=phash,
                        )

                        return {
                            "status": "processed",
                            "image_hash": image_hash,
                            "phash": phash,
                            "image_text": extracted_text.strip(),
                            "ad_archive_id": item["ad_archive_id"],
                            "creative_id": item.get("creative_id"),
                            "start_date": item["start_date"],
                            "s3_path": item["s3_path"],
                        }
                    else:
                        logger.warning(
                            f"Vision model returned empty result for {image_hash}"
                        )
                        self.queue.mark_failed(image_hash, "Empty vision model result")
                        return {"status": "failed", "image_hash": image_hash}

            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}"
                logger.error(f"Error processing {image_hash}: {error_msg}")
                self.queue.mark_failed(image_hash, error_msg)
                return {"status": "failed", "image_hash": image_hash}

    async def calculate_phash_for_processed_items(
        self, date: str | None = None, limit: int | None = None
    ) -> dict[str, Any]:
        """Calculate PHash for items that have been processed but lack PHash."""
        await self._ensure_initialized()

        console.print("[bold cyan]Calculating PHash for Processed Items[/bold cyan]")
        console.print(
            f"[green]S3 storage ready for bucket: {self.s3_manager.bucket_name}[/green]"
        )

        # Get items needing PHash
        with self.queue._get_connection() as conn:
            query = """
                SELECT image_hash, image_text, ad_archive_id, creative_id,
                       start_date, s3_path, scrape_date
                FROM image_queue
                WHERE status = 'processed'
                AND image_text IS NOT NULL
                AND (phash IS NULL OR phash = '')
            """
            params = []
            if date:
                query += " AND scrape_date = ?"
                params.append(date)
            query += " ORDER BY ad_archive_id"
            if limit:
                query += f" LIMIT {limit}"

            cursor = conn.execute(query, params)
            items = [dict(row) for row in cursor]

        if not items:
            console.print("[yellow]No items found needing PHash calculation[/yellow]")
            return {"calculated": 0, "failed": 0}

        console.print(
            f"[green]Found {len(items)} items needing PHash calculation[/green]"
        )

        stats = {"calculated": 0, "failed": 0}

        with Progress(
            SpinnerColumn(),
            "[progress.description]{task.description}",
            BarColumn(),
            "[progress.percentage]{task.percentage:>3.0f}%",
            TimeRemainingColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Calculating PHash values", total=len(items))

            for item in items:
                try:
                    # Download image from S3
                    image_data = await self.s3_manager.download_content(item["s3_path"])

                    if image_data and isinstance(image_data, bytes):
                        # Calculate perceptual hash
                        phash_obj = calculate_image_hash(image_data)
                        if phash_obj:
                            phash = str(phash_obj)

                            # Update SQLite with the calculated phash
                            with self.queue._get_connection() as conn:
                                conn.execute(
                                    "UPDATE image_queue SET phash = ? WHERE image_hash = ?",
                                    (phash, item["image_hash"]),
                                )

                            stats["calculated"] += 1
                            logger.info(
                                f"Calculated PHash for {item['ad_archive_id']}: {phash}"
                            )
                        else:
                            logger.warning(
                                f"Failed to calculate PHash for {item['ad_archive_id']}"
                            )
                            stats["failed"] += 1
                    else:
                        logger.warning(
                            f"Failed to download image for {item['ad_archive_id']}"
                        )
                        stats["failed"] += 1

                except Exception as e:
                    logger.error(f"Error processing {item['ad_archive_id']}: {e}")
                    stats["failed"] += 1

                progress.update(task, advance=1)

        console.print(
            f"[green]Successfully calculated {stats['calculated']} PHash values[/green]"
        )
        if stats["failed"] > 0:
            console.print(
                f"[red]Failed to calculate {stats['failed']} PHash values[/red]"
            )

        # Cleanup handled by container lifecycle management
        return stats

    async def _upload_to_dynamodb(
        self,
        items: list[dict[str, Any]],
        targets: list[str],
        dry_run: bool = False,
        generate_summaries: bool = False,
        clear_sqlite: bool = False,
    ) -> dict[str, Any]:
        """Upload items to DynamoDB."""
        import time

        from rich.panel import Panel

        upload_stats = {
            "fbimage_hash": {"success": 0, "failed": 0},
            "fbad_archive": {"success": 0, "failed": 0},
            "summaries_generated": 0,
            "summaries_failed": 0,
            "sqlite_cleared": 0,
            "total_success": 0,
            "total_failed": 0,
        }

        # Initialize DeepSeek client for summary generation if requested
        deepseek_client = None
        if generate_summaries:
            try:
                deepseek_client = DeepSeekClient(
                    config={
                        "temperature": 0.1,
                        "max_tokens": 4000,
                        "timeout": 300,
                        "openrouter_api_key": os.getenv("OPENROUTER_API_KEY"),
                    },
                    logger=logger,
                )
                await deepseek_client.__aenter__()
                console.print(
                    "[green]DeepSeek client initialized for summary generation[/green]"
                )
            except Exception as e:
                logger.error(f"Failed to initialize DeepSeek client: {e}")
                console.print(f"[red]Failed to initialize DeepSeek client: {e}[/red]")
                generate_summaries = False

        # Initialize repositories for each target
        repositories = []

        for target in targets:
            try:
                # Initialize AsyncDynamoDBStorage
                if target == "local":
                    config = {
                        "endpoint_url": "http://localhost:8000",
                        "region_name": "us-west-2",
                    }
                else:
                    config = {"region_name": "us-west-2"}

                storage = AsyncDynamoDBStorage(config, logger)

                # Initialize storage session
                await storage.__aenter__()

                # Initialize repositories
                fb_image_hash_repo = FBImageHashRepository(storage, logger)
                fb_archive_repo = FBArchiveRepository(storage, logger)

                repositories.append(
                    (target, storage, fb_image_hash_repo, fb_archive_repo)
                )

            except Exception as e:
                logger.error(f"[{target}] Error initializing repositories: {e}")

        if not repositories:
            logger.error("No valid DynamoDB targets available")
            return upload_stats

        # Process items in batches of 100 with 10 parallel threads for summary generation
        if generate_summaries and deepseek_client:
            console.print(
                f"[cyan]Processing {len(items)} items in batches of 100 with 10 parallel threads...[/cyan]"
            )

            # Create semaphore for concurrency control (limit concurrent AI calls)
            semaphore = asyncio.Semaphore(10)  # Process 10 summaries concurrently
            batch_size = 100

            async def process_single_item_summary(item):
                async with semaphore:
                    try:
                        # Skip if no phash
                        if not item.get("phash"):
                            logger.warning(
                                f"Skipping {item['ad_archive_id']} - no PHash"
                            )
                            return {
                                "item": item,
                                "updated_summary": None,
                                "error": "no_phash",
                            }

                        # Get original FBAdArchive record with auto-sync capability
                        original_record = await self._get_fbadarchive_record(
                            item["ad_archive_id"],
                            item["start_date"],
                            repositories[0][3],  # Primary FB archive repository
                            repositories,  # All repositories for fallback/sync
                        )

                        if not original_record:
                            logger.error(
                                f"RECORD_NOT_FOUND: AdArchiveID={item['ad_archive_id']}, StartDate={item['start_date']}, Reason='FBAdArchive record not found in DynamoDB'"
                            )
                            return {
                                "item": item,
                                "updated_summary": None,
                                "error": "no_original_record",
                            }

                        # Check if summary was updated today (skip if already processed today)
                        from datetime import datetime

                        summary_updated = original_record.get(
                            "summary_updated"
                        ) or original_record.get("SummaryUpdated")
                        if summary_updated:
                            try:
                                # Parse the timestamp
                                if isinstance(summary_updated, str):
                                    updated_date = datetime.fromisoformat(
                                        summary_updated.replace("Z", "+00:00")
                                    )
                                else:
                                    updated_date = summary_updated

                                # Check if updated today
                                today = datetime.now(UTC).date()
                                if updated_date.date() == today:
                                    logger.info(
                                        f"SKIPPING_TODAY_UPDATE: AdArchiveID={item['ad_archive_id']} - Summary already updated today ({updated_date.date()}), skipping AI processing"
                                    )
                                    return {
                                        "item": item,
                                        "updated_summary": None,
                                        "original_record": original_record,
                                        "error": "updated_today_skipped",
                                    }
                            except (ValueError, TypeError) as e:
                                logger.warning(
                                    f"Could not parse summary_updated timestamp for {item['ad_archive_id']}: {e}"
                                )

                        # Check if current summary is standardized (skip AI processing)
                        current_summary = original_record.get("summary", "").strip()
                        standardized_litigations = [
                            "Video Game Addiction Investigation",
                            "Video Game Addiction Lawsuit",
                            "Depo-Provera Brain Tumor Investigation",
                            "Depo Provera Brain Tumor Investigation",
                            "McLaren Health Data Breach Investigation",
                            "Roundup Cancer Product Liability Lawsuit",
                            "Roundup Cancer Investigation",
                            "Talc Mesothelioma Investigation",
                            "Talc Cosmetics Mesothelioma Investigation",
                            "PFAS Water Contamination Investigation",
                            "Hernia Mesh Complication Investigation",
                            "Hernia Mesh Product Liability",
                            "Oxbryta Sickle Cell Investigation",
                            "Oxbryta Adverse Effects Investigation",
                            "Hardy Water Contamination Investigation",
                            "LexisNexis Data Breach Investigation",
                            "LexisNexis Risk Solutions Data Breach Investigation",
                            "Lakeland Behavioral Sexual Abuse Investigation",
                            "Lakeland Behavioral Child Abuse Lawsuit",
                            "Western Dental Investigation",
                            "Western Dental Appointment Investigation",
                            "Atrium Health HER2 Misdiagnosis Investigation",
                            "Atrium Health HER2 Investigation",
                            "Atrium Health Breast Cancer Misdiagnosis",
                        ]

                        if current_summary in standardized_litigations:
                            logger.info(
                                f"SKIPPING_STANDARDIZED: AdArchiveID={item['ad_archive_id']}, Summary='{current_summary}' - Already standardized, skipping AI processing"
                            )
                            return {
                                "item": item,
                                "updated_summary": None,
                                "original_record": original_record,
                                "error": "standardized_summary_skipped",
                            }

                        # Generate new summary with image text
                        updated_summary = await self._generate_summary_with_image_text(
                            deepseek_client, original_record, item["image_text"]
                        )

                        if updated_summary:
                            # Log successful summary generation
                            original_summary = original_record.get("summary", "N/A")
                            logger.info(
                                f"SUMMARY_SUCCESS: AdArchiveID={item['ad_archive_id']}, Original='{original_summary}', Updated='{updated_summary}'"
                            )

                            return {
                                "item": item,
                                "updated_summary": updated_summary,
                                "original_record": original_record,
                                "error": None,
                            }
                        else:
                            # Log 'NA' or failed summary generation
                            original_summary = original_record.get("summary", "N/A")
                            title = original_record.get("title", "")
                            logger.warning(
                                f"SUMMARY_NA_OR_FAILED: AdArchiveID={item['ad_archive_id']}, Title='{title}', Original='{original_summary}', Reason='AI returned NA or empty'"
                            )

                            return {
                                "item": item,
                                "updated_summary": None,
                                "error": "summary_failed",
                            }

                    except Exception as e:
                        logger.error(
                            f"SUMMARY_EXCEPTION: AdArchiveID={item['ad_archive_id']}, Error='{str(e)}', Type='{type(e).__name__}'"
                        )

                        return {"item": item, "updated_summary": None, "error": str(e)}

            # Process items in batches with progress bar
            processed_items = []
            total_batches = (len(items) + batch_size - 1) // batch_size

            console.print(
                Panel.fit(
                    "🤖 AI Summary Generation Phase",
                    style="bold cyan",
                    border_style="cyan",
                )
            )

            # Use Rich Live for real-time updates
            progress = Progress(
                SpinnerColumn(),
                "[progress.description]{task.description}",
                BarColumn(bar_width=40),
                "[progress.percentage]{task.percentage:>3.0f}%",
                console=console,
                transient=False,
            )

            with progress:
                start_time = time.time()

                # Initialize real-time tracking variables
                success_count = 0
                failed_count = 0
                completed_count = 0

                # Detailed statistics tracking
                stats_breakdown = {
                    "new_summaries": 0,
                    "standardized_skipped": 0,
                    "today_skipped": 0,
                    "other_failed": 0,
                }

                # Track individual items with real-time updates
                main_task = progress.add_task(
                    "[cyan]🤖 Processing AI summaries...",
                    total=len(items),  # Total individual items
                )

                # Process all items with real-time progress updates using as_completed
                summary_tasks = [process_single_item_summary(item) for item in items]
                batch_results = []

                # Track progress as each task completes in real-time
                for coro in asyncio.as_completed(summary_tasks):
                    try:
                        result = await coro
                        batch_results.append(result)
                        completed_count += 1

                        # Determine if this was a success or failure
                        if isinstance(result, dict) and result.get("error") is None:
                            if result.get("updated_summary"):
                                success_count += 1  # Actually generated new summary
                                stats_breakdown["new_summaries"] += 1
                            else:
                                failed_count += 1  # Other cases without summary
                                stats_breakdown["other_failed"] += 1
                        elif (
                            isinstance(result, dict)
                            and result.get("error") == "standardized_summary_skipped"
                        ):
                            success_count += (
                                1  # Skipped standardized summary (counts as success)
                            )
                            stats_breakdown["standardized_skipped"] += 1
                        elif (
                            isinstance(result, dict)
                            and result.get("error") == "updated_today_skipped"
                        ):
                            success_count += (
                                1  # Skipped today's update (counts as success)
                            )
                            stats_breakdown["today_skipped"] += 1
                        else:
                            failed_count += 1
                            stats_breakdown["other_failed"] += 1

                        # Calculate real-time metrics
                        elapsed_time = time.time() - start_time
                        throughput = (
                            completed_count / elapsed_time if elapsed_time > 0 else 0
                        )
                        success_rate = (
                            (success_count / completed_count * 100)
                            if completed_count > 0
                            else 0
                        )
                        fail_rate = (
                            (failed_count / completed_count * 100)
                            if completed_count > 0
                            else 0
                        )

                        # Update progress bar with advance and custom description
                        progress.update(
                            main_task,
                            advance=1,
                            description=f"[cyan]🤖 Processing AI summaries... [green]✅{success_count} ({success_rate:.1f}%) [red]❌{failed_count} ({fail_rate:.1f}%) [yellow]📊{throughput:.1f}/sec",
                        )

                    except Exception as e:
                        batch_results.append(e)
                        completed_count += 1
                        failed_count += 1

                        # Calculate real-time metrics for exception
                        elapsed_time = time.time() - start_time
                        throughput = (
                            completed_count / elapsed_time if elapsed_time > 0 else 0
                        )
                        success_rate = (
                            (success_count / completed_count * 100)
                            if completed_count > 0
                            else 0
                        )
                        fail_rate = (
                            (failed_count / completed_count * 100)
                            if completed_count > 0
                            else 0
                        )

                        # Update progress bar with advance and custom description
                        progress.update(
                            main_task,
                            advance=1,
                            description=f"[cyan]🤖 Processing AI summaries... [green]✅{success_count} ({success_rate:.1f}%) [red]❌{failed_count} ({fail_rate:.1f}%) [yellow]📊{throughput:.1f}/sec",
                        )

                # Use the real-time counts for final stats
                upload_stats["summaries_generated"] = success_count
                upload_stats["summaries_failed"] = failed_count

                # Process all results to build processed_items list
                for result in batch_results:
                    if isinstance(result, dict):
                        if result["error"] is None and result["updated_summary"]:
                            processed_items.append(result)  # Generated new summary
                        elif result["error"] == "standardized_summary_skipped":
                            processed_items.append(
                                result
                            )  # Skipped standardized summary but still process PHash
                        elif result["error"] == "updated_today_skipped":
                            processed_items.append(
                                result
                            )  # Skipped today's update but still process PHash
                        else:
                            # Still add to processed_items for PHash update (without summary)
                            if result["error"] not in ["no_phash"]:
                                processed_items.append(result)

                # Final progress update with throughput
                total_processed = progress.tasks[main_task].completed
                elapsed_time = time.time() - start_time
                throughput = total_processed / elapsed_time if elapsed_time > 0 else 0

                progress.update(main_task, throughput=throughput)

            # Enhanced summary statistics with breakdown
            from rich.table import Table

            stats_table = Table(title="📊 Summary Processing Breakdown", style="bold")
            stats_table.add_column("Category", style="cyan")
            stats_table.add_column("Count", style="green", justify="right")
            stats_table.add_column("Percentage", style="yellow", justify="right")

            total_items = len(items)
            stats_table.add_row(
                "🆕 New Summaries Generated",
                str(stats_breakdown["new_summaries"]),
                (
                    f"{(stats_breakdown['new_summaries'] / total_items * 100):.1f}%"
                    if total_items > 0
                    else "0%"
                ),
            )
            stats_table.add_row(
                "⏭️ Standardized Summaries Skipped",
                str(stats_breakdown["standardized_skipped"]),
                (
                    f"{(stats_breakdown['standardized_skipped'] / total_items * 100):.1f}%"
                    if total_items > 0
                    else "0%"
                ),
            )
            stats_table.add_row(
                "📅 Today's Updates Skipped",
                str(stats_breakdown["today_skipped"]),
                (
                    f"{(stats_breakdown['today_skipped'] / total_items * 100):.1f}%"
                    if total_items > 0
                    else "0%"
                ),
            )
            stats_table.add_row(
                "❌ Failed/Other",
                str(stats_breakdown["other_failed"]),
                (
                    f"{(stats_breakdown['other_failed'] / total_items * 100):.1f}%"
                    if total_items > 0
                    else "0%"
                ),
            )
            stats_table.add_row(
                "🏆 Total Success Rate",
                str(success_count),
                (
                    f"{(success_count / total_items * 100):.1f}%"
                    if total_items > 0
                    else "0%"
                ),
            )

            console.print(stats_table)

            console.print(
                f"[bold green]✅ Parallel summary generation complete: {upload_stats['summaries_generated']} processed successfully[/bold green]"
            )

            # Log final summary statistics
            total_processed = (
                upload_stats["summaries_generated"] + upload_stats["summaries_failed"]
            )
            success_rate = (
                (upload_stats["summaries_generated"] / total_processed * 100)
                if total_processed > 0
                else 0
            )
            logger.info(
                f"SUMMARY_STATS: Total={total_processed}, Success={upload_stats['summaries_generated']}, Failed={upload_stats['summaries_failed']}, SuccessRate={success_rate:.1f}%, NewSummaries={stats_breakdown['new_summaries']}, StandardizedSkipped={stats_breakdown['standardized_skipped']}, TodaySkipped={stats_breakdown['today_skipped']}"
            )

        else:
            # No summary generation, just process items for PHash updates
            processed_items = [
                {"item": item, "updated_summary": None, "error": None}
                for item in items
                if item.get("phash")
            ]

        # Now handle the upload/update phase for all processed items in parallel
        console.print(
            Panel.fit(
                "📤 DynamoDB Upload Phase", style="bold green", border_style="green"
            )
        )

        if dry_run:
            # Enhanced dry-run simulation with progress bar
            console.print(
                f"[bold yellow][DRY RUN] Simulating upload of {len(processed_items)} items to DynamoDB...[/bold yellow]"
            )

            upload_batch_size = 100
            total_upload_batches = (
                len(processed_items) + upload_batch_size - 1
            ) // upload_batch_size

            with Progress(
                SpinnerColumn(),
                "[progress.description]{task.description}",
                BarColumn(bar_width=40),
                MofNCompleteColumn(),
                "[progress.percentage]{task.percentage:>5.1f}%",
                "•",
                "[bold green]📊 {task.fields[fbimage_success]:,}",
                "•",
                "[bold green]📁 {task.fields[fbarchive_success]:,}",
                "•",
                "[bold yellow]🗑️ {task.fields[sqlite_cleared]:,}",
                "•",
                TimeRemainingColumn(),
                console=console,
                transient=False,
            ) as progress:
                upload_task = progress.add_task(
                    "[yellow]🎨 [DRY RUN] Simulating uploads...",
                    total=len(processed_items),  # Track individual items
                    fbimage_success=0,
                    fbarchive_success=0,
                    sqlite_cleared=0,
                    current_batch=0,
                    total_batches=total_upload_batches,
                )

                for batch_idx in range(0, len(processed_items), upload_batch_size):
                    batch_items = processed_items[
                        batch_idx : batch_idx + upload_batch_size
                    ]

                    # Simulate upload delay
                    await asyncio.sleep(0.1)

                    # Count items for dry run simulation
                    batch_fbimage = len(batch_items)
                    batch_fbarchive = len(batch_items)
                    batch_sqlite = len(batch_items) if clear_sqlite else 0

                    # Update stats
                    upload_stats["fbimage_hash"]["success"] += batch_fbimage
                    upload_stats["fbad_archive"]["success"] += batch_fbarchive
                    upload_stats["sqlite_cleared"] += batch_sqlite

                    # Update progress bar
                    current_batch_num = (batch_idx // upload_batch_size) + 1

                    progress.update(
                        upload_task,
                        advance=len(batch_items),  # Advance by number of items in batch
                        fbimage_success=upload_stats["fbimage_hash"]["success"],
                        fbarchive_success=upload_stats["fbad_archive"]["success"],
                        sqlite_cleared=upload_stats["sqlite_cleared"],
                        current_batch=current_batch_num,
                    )

                    # Show some sample items
                    if batch_idx < 200:  # Only show first few batches to avoid spam
                        for result in batch_items[:3]:  # Show first 3 items per batch
                            item = result["item"]
                            console.print(
                                f"[dim][DRY RUN] Would update PHash for {item['ad_archive_id']}[/dim]"
                            )
        else:
            # Parallel upload processing with progress bar
            console.print(
                f"[cyan]Uploading {len(processed_items)} items to DynamoDB in batches of 100 with 10 parallel threads...[/cyan]"
            )

            # Create semaphore for upload concurrency control
            upload_semaphore = asyncio.Semaphore(10)  # Allow 10 concurrent uploads
            upload_batch_size = 100

            async def upload_single_item(result):
                async with upload_semaphore:
                    item = result["item"]
                    updated_summary = result.get("updated_summary")
                    upload_result = {
                        "fbimage_hash": {"success": 0, "failed": 0},
                        "fbad_archive": {"success": 0, "failed": 0},
                        "sqlite_cleared": 0,
                    }

                    try:
                        # Update each target
                        for (
                            target_name,
                            storage,
                            fb_image_hash_repo,
                            fb_archive_repo,
                        ) in repositories:
                            try:
                                # Create or update FBImageHash record
                                fb_image_record = {
                                    "p_hash": item["phash"],
                                    "ad_archive_id": item["ad_archive_id"],
                                    "ad_creative_id": item.get("creative_id", ""),
                                    "image_text": item["image_text"],
                                    "s3_image_key": item.get("s3_path", ""),
                                    "last_updated": datetime.utcnow().isoformat(),
                                }

                                logger.info(
                                    f"[{target_name}] ATTEMPTING FBImageHash save for AdArchiveID: {item['ad_archive_id']}, PHash: {item['phash']}"
                                )
                                hash_result = (
                                    await fb_image_hash_repo.add_or_update_record(
                                        fb_image_record
                                    )
                                )
                                if hash_result:
                                    upload_result["fbimage_hash"]["success"] += 1
                                    logger.info(
                                        f"[{target_name}] SUCCESS FBImageHash save for AdArchiveID: {item['ad_archive_id']}"
                                    )
                                else:
                                    upload_result["fbimage_hash"]["failed"] += 1
                                    logger.error(
                                        f"[{target_name}] FAILED FBImageHash save for AdArchiveID: {item['ad_archive_id']}"
                                    )

                                # Update FBAdArchive with PHash and optionally Summary
                                try:
                                    # Prepare updates - always include timestamp
                                    updates = {
                                        "p_hash": item["phash"],
                                        "summary_updated": datetime.utcnow().isoformat(),  # Always update timestamp
                                    }

                                    # Add summary update if generated
                                    if updated_summary:
                                        updates["summary"] = updated_summary
                                        logger.info(
                                            f"[{target_name}] Including summary update for AdArchiveID: {item['ad_archive_id']}"
                                        )
                                    else:
                                        logger.info(
                                            f"[{target_name}] Updating timestamp only (no new summary) for AdArchiveID: {item['ad_archive_id']}"
                                        )

                                    logger.info(
                                        f"[{target_name}] ATTEMPTING FBAdArchive update for AdArchiveID: {item['ad_archive_id']}, StartDate: {item['start_date']}, Updates: {list(updates.keys())}"
                                    )

                                    # Update the record
                                    success = await fb_archive_repo.update_attributes(
                                        item["ad_archive_id"],
                                        item["start_date"],
                                        updates,
                                    )

                                    if success:
                                        upload_result["fbad_archive"]["success"] += 1
                                        logger.info(
                                            f"[{target_name}] SUCCESS FBAdArchive update for AdArchiveID: {item['ad_archive_id']}, StartDate: {item['start_date']}"
                                        )
                                    else:
                                        upload_result["fbad_archive"]["failed"] += 1
                                        logger.error(
                                            f"[{target_name}] FAILED FBAdArchive update for AdArchiveID: {item['ad_archive_id']}, StartDate: {item['start_date']}"
                                        )

                                except Exception as e:
                                    logger.error(
                                        f"[{target_name}] EXCEPTION updating FBAdArchive for AdArchiveID: {item['ad_archive_id']}, StartDate: {item['start_date']}: {str(e)}"
                                    )
                                    upload_result["fbad_archive"]["failed"] += 1

                            except Exception as e:
                                logger.error(
                                    f"[{target_name}] EXCEPTION processing {item['ad_archive_id']}: {e}"
                                )
                                upload_result["fbimage_hash"]["failed"] += 1
                                upload_result["fbad_archive"]["failed"] += 1

                    except Exception as e:
                        logger.error(
                            f"CRITICAL UPLOAD ERROR for {item['ad_archive_id']}: {e}"
                        )
                        upload_result["fbimage_hash"]["failed"] += 1
                        upload_result["fbad_archive"]["failed"] += 1

                    # Clear from SQLite if requested and upload successful
                    if clear_sqlite and upload_result["fbimage_hash"]["success"] > 0:
                        try:
                            with self.queue._get_connection() as conn:
                                conn.execute(
                                    "DELETE FROM image_queue WHERE image_hash = ?",
                                    (item["image_hash"],),
                                )
                            upload_result["sqlite_cleared"] = 1
                            logger.info(
                                f"SUCCESS SQLite cleared record for {item['image_hash']}"
                            )
                        except Exception as e:
                            logger.error(
                                f"FAILED SQLite clear for {item['image_hash']}: {e}"
                            )
                    elif clear_sqlite:
                        logger.warning(
                            f"SKIPPED SQLite clear for {item['image_hash']} due to upload failure"
                        )

                    return upload_result

            # Process uploads in batches with progress bar
            total_upload_batches = (
                len(processed_items) + upload_batch_size - 1
            ) // upload_batch_size

            with Progress(
                SpinnerColumn(),
                "[progress.description]{task.description}",
                BarColumn(bar_width=40),
                "[progress.percentage]{task.percentage:>3.0f}%",
                console=console,
                transient=False,
            ) as progress:
                upload_start_time = time.time()

                upload_task = progress.add_task(
                    "[cyan]🚀 Uploading to DynamoDB...",
                    total=len(processed_items),  # Track individual items
                )

                for batch_idx in range(0, len(processed_items), upload_batch_size):
                    batch_items = processed_items[
                        batch_idx : batch_idx + upload_batch_size
                    ]

                    # Process upload batch in parallel
                    upload_tasks = [
                        upload_single_item(result) for result in batch_items
                    ]
                    batch_upload_results = await asyncio.gather(
                        *upload_tasks, return_exceptions=True
                    )

                    # Aggregate batch results
                    batch_fbimage_success = 0
                    batch_fbarchive_success = 0
                    batch_sqlite_cleared = 0

                    for upload_result in batch_upload_results:
                        if isinstance(upload_result, Exception):
                            logger.error(f"Upload task exception: {upload_result}")
                            upload_stats["fbimage_hash"]["failed"] += 1
                            upload_stats["fbad_archive"]["failed"] += 1
                        elif isinstance(upload_result, dict):
                            upload_stats["fbimage_hash"]["success"] += upload_result[
                                "fbimage_hash"
                            ]["success"]
                            upload_stats["fbimage_hash"]["failed"] += upload_result[
                                "fbimage_hash"
                            ]["failed"]
                            upload_stats["fbad_archive"]["success"] += upload_result[
                                "fbad_archive"
                            ]["success"]
                            upload_stats["fbad_archive"]["failed"] += upload_result[
                                "fbad_archive"
                            ]["failed"]
                            upload_stats["sqlite_cleared"] += upload_result[
                                "sqlite_cleared"
                            ]

                            batch_fbimage_success += upload_result["fbimage_hash"][
                                "success"
                            ]
                            batch_fbarchive_success += upload_result["fbad_archive"][
                                "success"
                            ]
                            batch_sqlite_cleared += upload_result["sqlite_cleared"]

                    # Calculate real-time metrics for display
                    current_fbimage = upload_stats["fbimage_hash"]["success"]
                    current_fbarchive = upload_stats["fbad_archive"]["success"]
                    current_sqlite = upload_stats["sqlite_cleared"]

                    # Calculate rates and throughput
                    processed_batches = batch_idx // upload_batch_size + 1
                    total_items_processed = min(
                        processed_batches * upload_batch_size, len(processed_items)
                    )
                    fbimage_rate = (
                        (current_fbimage / total_items_processed * 100)
                        if total_items_processed > 0
                        else 0
                    )
                    fbarchive_rate = (
                        (current_fbarchive / total_items_processed * 100)
                        if total_items_processed > 0
                        else 0
                    )

                    upload_elapsed = time.time() - upload_start_time
                    upload_throughput = (
                        total_items_processed / upload_elapsed
                        if upload_elapsed > 0
                        else 0
                    )

                    current_batch_num = (batch_idx // upload_batch_size) + 1

                    # Update progress with dynamic description showing metrics
                    progress.update(
                        upload_task,
                        advance=len(batch_items),
                        description=f"[cyan]🚀 Uploading to DynamoDB... [green]📊{current_fbimage} ({fbimage_rate:.1f}%) [green]📁{current_fbarchive} ({fbarchive_rate:.1f}%) [yellow]🗑️{current_sqlite} [cyan]⚡{upload_throughput:.1f}/sec",
                    )

                    # Add explicit logging for upload progress
                    logger.info(
                        f"UPLOAD_BATCH_COMPLETE: Batch {current_batch_num}/{total_upload_batches}, FBImageHash: {current_fbimage}, FBAdArchive: {current_fbarchive}, SQLite: {current_sqlite}, Throughput: {upload_throughput:.1f}/sec"
                    )

            # Rich summary panel
            from rich.table import Table

            summary_table = Table(title="📊 Upload Results Summary", style="bold")
            summary_table.add_column("Operation", style="cyan")
            summary_table.add_column("Success", style="green", justify="right")
            summary_table.add_column("Failed", style="red", justify="right")
            summary_table.add_column("Rate", style="yellow", justify="right")

            total_items = len(processed_items)
            fbimage_final_rate = (
                (upload_stats["fbimage_hash"]["success"] / total_items * 100)
                if total_items > 0
                else 0
            )
            fbarchive_final_rate = (
                (upload_stats["fbad_archive"]["success"] / total_items * 100)
                if total_items > 0
                else 0
            )

            summary_table.add_row(
                "📊 FBImageHash",
                str(upload_stats["fbimage_hash"]["success"]),
                str(upload_stats["fbimage_hash"]["failed"]),
                f"{fbimage_final_rate:.1f}%",
            )
            summary_table.add_row(
                "📁 FBAdArchive",
                str(upload_stats["fbad_archive"]["success"]),
                str(upload_stats["fbad_archive"]["failed"]),
                f"{fbarchive_final_rate:.1f}%",
            )
            summary_table.add_row(
                "🗑️ SQLite Cleared",
                str(upload_stats["sqlite_cleared"]),
                "0",
                "100.0%" if upload_stats["sqlite_cleared"] > 0 else "0.0%",
            )

            console.print(Panel.fit(summary_table, style="green bold"))

        upload_stats["total_success"] = (
            upload_stats["fbimage_hash"]["success"]
            + upload_stats["fbad_archive"]["success"]
        )
        upload_stats["total_failed"] = (
            upload_stats["fbimage_hash"]["failed"]
            + upload_stats["fbad_archive"]["failed"]
        )

        # Cleanup DeepSeek client
        if deepseek_client:
            try:
                await deepseek_client.__aexit__(None, None, None)
            except Exception as e:
                logger.warning(f"Error closing DeepSeek client: {e}")

        # Cleanup storage sessions
        for target_name, storage, _, _ in repositories:
            try:
                await storage.__aexit__(None, None, None)
            except Exception as e:
                logger.warning(f"Error closing {target_name} storage session: {e}")

        return upload_stats

    async def _get_fbadarchive_record(
        self,
        ad_archive_id: str,
        start_date: str,
        primary_repo: FBArchiveRepository,
        all_repositories: list | None = None,
    ) -> dict[str, Any] | None:
        """Retrieve original FBAdArchive record with automatic local-to-AWS sync."""
        try:
            # First, try the primary repository (usually AWS)
            record = await primary_repo.get_by_key(ad_archive_id, start_date)

            if record:
                console.print(
                    f"[green]✅ Found record in primary DynamoDB: {ad_archive_id}[/green]"
                )
                return record

            # If not found in primary and we have multiple repositories, try local
            if all_repositories and len(all_repositories) > 1:
                console.print(
                    "[yellow]⚠️  Record not found in primary DynamoDB, checking local...[/yellow]"
                )

                # Try to find a local repository (endpoint with localhost)
                local_repo = None
                aws_repo = None

                for target_name, storage, _, fb_repo in all_repositories:
                    storage_config = getattr(
                        storage.config, "dynamodb_endpoint", None
                    ) or getattr(storage.config, "endpoint_url", None)
                    if storage_config and "localhost" in str(storage_config):
                        local_repo = fb_repo
                        console.print(
                            "[cyan]🔍 Checking local DynamoDB repository...[/cyan]"
                        )
                    elif target_name == "aws" or not storage_config:
                        aws_repo = fb_repo

                # Try local repository if available
                if local_repo:
                    local_record = await local_repo.get_by_key(
                        ad_archive_id, start_date
                    )

                    if local_record:
                        console.print(
                            f"[green]✅ Found record in local DynamoDB: {ad_archive_id}[/green]"
                        )

                        # Auto-sync to AWS if we have AWS repo and record is only in local
                        if aws_repo and aws_repo != primary_repo:
                            try:
                                console.print(
                                    "[cyan]🔄 Auto-syncing record to AWS DynamoDB...[/cyan]"
                                )
                                await aws_repo.put_item(local_record)
                                console.print(
                                    f"[green]✅ Successfully synced {ad_archive_id} to AWS DynamoDB[/green]"
                                )
                                logger.info(
                                    f"AUTO_SYNC_SUCCESS: AdArchiveID={ad_archive_id}, StartDate={start_date}, Direction=local->aws"
                                )
                            except Exception as sync_error:
                                console.print(
                                    f"[red]❌ Failed to sync to AWS: {str(sync_error)}[/red]"
                                )
                                logger.error(
                                    f"AUTO_SYNC_FAILED: AdArchiveID={ad_archive_id}, Error={str(sync_error)}"
                                )

                        return local_record

            # Record not found in any repository
            console.print(
                f"[red]❌ Record not found in any DynamoDB: {ad_archive_id}[/red]"
            )
            logger.warning(
                f"FBAdArchive record not found for {ad_archive_id}, {start_date}"
            )
            return None

        except Exception as e:
            console.print(
                f"[red]💥 Error retrieving FBAdArchive record: {str(e)}[/red]"
            )
            logger.error(f"Error retrieving FBAdArchive record: {e}")
            return None

    async def _generate_summary_with_image_text(
        self,
        deepseek_client: DeepSeekClient,
        original_record: dict[str, Any],
        image_text: str,
    ) -> str | None:
        """Generate enhanced summary using original ad data plus image text."""
        try:
            # Prepare input data for summary generation
            ad_data = {
                "title": original_record.get("title", ""),
                "body": original_record.get("body", ""),
                "caption": original_record.get("caption", ""),
                "link_description": original_record.get("link_description", ""),
                "cta_text": original_record.get("cta_text", ""),
                "link_url": original_record.get("link_url", ""),
                "page_name": original_record.get("page_name", ""),
                "law_firm": original_record.get("law_firm", ""),
                "original_summary": original_record.get("summary", ""),
                "image_text": image_text,
            }

            # Load proper prompt files
            system_prompt = self._load_prompt_file("fb_ads/ad_summary/system.md")
            user_prompt_template = self._load_prompt_file("fb_ads/ad_summary/user.md")

            # Format user prompt with ad data
            user_prompt = user_prompt_template.format(
                title=ad_data["title"],
                body=ad_data["body"],
                link_description=ad_data["link_description"],
                law_firm_name=ad_data["law_firm"],
                image_text=ad_data["image_text"],
            )

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ]

            # Generate summary using DeepSeek
            response = await deepseek_client._chat_completion(
                messages=messages, temperature=0.1, max_tokens=500, json_mode=True
            )

            if response:
                # Parse JSON response and extract summary
                if isinstance(response, dict):
                    summary = response.get("summary", "")
                elif isinstance(response, str):
                    try:
                        import json

                        parsed = json.loads(response)
                        summary = parsed.get("summary", "")
                    except json.JSONDecodeError:
                        logger.error(f"Failed to parse JSON response: {response}")
                        return None
                else:
                    logger.error(f"Unexpected response type: {type(response)}")
                    return None

                # Return the summary if valid, otherwise None
                if summary and summary != "NA":
                    return summary.strip()
                else:
                    logger.info(
                        f"DeepSeek returned 'NA' or empty summary for ad {ad_data.get('title', 'unknown')}"
                    )
                    return None
            else:
                logger.warning("DeepSeek returned empty response")
                return None

        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return None

    async def upload_processed_items(
        self,
        date: str | None = None,
        targets: list[str] = ["aws"],
        limit: int | None = None,
        show_details: bool = False,
        dry_run: bool = False,
        generate_summaries: bool = False,
        clear_sqlite: bool = False,
    ) -> dict[str, Any]:
        """Upload processed items with PHash to DynamoDB."""
        if dry_run:
            console.print(
                f"[bold yellow]🎨 [DRY RUN] Would upload to {', '.join(targets)} DynamoDB[/bold yellow]"
            )
        else:
            console.print(
                f"[bold cyan]🚀 Uploading to {', '.join(targets)} DynamoDB[/bold cyan]"
            )

        console.print(
            "[bold yellow]📝 Note: FBAdArchive updates will only modify existing items (no new items will be created)[/bold yellow]"
        )

        if generate_summaries:
            console.print(
                "[bold cyan]🤖 AI summary generation enabled - will enhance summaries with image text[/bold cyan]"
            )

        if clear_sqlite:
            console.print(
                "[bold yellow]🧽 SQLite cleanup enabled - will remove processed items after successful upload[/bold yellow]"
            )

        # Get processed items with PHash
        with self.queue._get_connection() as conn:
            query = """
                SELECT image_hash, image_text, ad_archive_id, creative_id,
                       start_date, s3_path, phash
                FROM image_queue
                WHERE status = 'processed'
                AND image_text IS NOT NULL
                AND phash IS NOT NULL
                AND phash != ''
            """
            params = []
            if date:
                query += " AND scrape_date = ?"
                params.append(date)
            query += " ORDER BY ad_archive_id"
            if limit:
                query += f" LIMIT {limit}"

            cursor = conn.execute(query, params)
            items = [dict(row) for row in cursor]

        if not items:
            console.print(
                "[bold yellow]⚠️ No processed items found with PHash[/bold yellow]"
            )
            return {"uploaded": 0, "failed": 0}

        console.print(f"[bold green]📋 Found {len(items)} items to upload[/bold green]")

        # Upload using async method directly
        upload_stats = await self._upload_to_dynamodb(
            items,
            targets,
            dry_run=dry_run,
            generate_summaries=generate_summaries,
            clear_sqlite=clear_sqlite,
        )

        # Enhanced rich summary display
        from rich.panel import Panel

        console.print(
            Panel.fit(
                "🎉 Upload Pipeline Complete!", style="bold green", border_style="green"
            )
        )

        # Create enhanced summary table
        final_table = Table(
            title="📈 Final Results",
            style="bold",
            show_header=True,
            header_style="bold magenta",
        )
        final_table.add_column("📊 Table", style="cyan", width=15)
        final_table.add_column("✅ Success", justify="right", style="green", width=10)
        final_table.add_column("❌ Failed", justify="right", style="red", width=10)
        final_table.add_column("📊 Rate", justify="right", style="yellow", width=10)

        total_processed = len(items) if "items" in locals() else 1
        fbimage_rate = (
            (upload_stats["fbimage_hash"]["success"] / total_processed * 100)
            if total_processed > 0
            else 0
        )
        fbarchive_rate = (
            (upload_stats["fbad_archive"]["success"] / total_processed * 100)
            if total_processed > 0
            else 0
        )

        final_table.add_row(
            "FBImageHash",
            str(upload_stats["fbimage_hash"]["success"]),
            str(upload_stats["fbimage_hash"]["failed"]),
            f"{fbimage_rate:.1f}%",
        )
        final_table.add_row(
            "FBAdArchive",
            str(upload_stats["fbad_archive"]["success"]),
            str(upload_stats["fbad_archive"]["failed"]),
            f"{fbarchive_rate:.1f}%",
        )

        console.print(Panel.fit(final_table, style="bold blue"))

        # Additional summary stats
        if generate_summaries:
            console.print(
                f"[bold cyan]🤖 Summaries Generated: {upload_stats['summaries_generated']}[/bold cyan]"
            )
            console.print(
                f"[bold red]❌ Summary Failures: {upload_stats['summaries_failed']}[/bold red]"
            )

        if clear_sqlite:
            console.print(
                f"[bold green]🧽 SQLite Records Cleared: {upload_stats['sqlite_cleared']}[/bold green]"
            )

        return {
            "uploaded": upload_stats["total_success"],
            "failed": upload_stats["total_failed"],
            "summaries_generated": upload_stats.get("summaries_generated", 0),
            "summaries_failed": upload_stats.get("summaries_failed", 0),
        }

    async def reset_processing_items(self, date: str | None = None) -> int:
        """Reset items stuck in processing status."""
        await self._ensure_initialized()
        reset_count = self.queue.reset_processing_items(date)
        console.print(
            f"[green]Reset {reset_count} stuck processing items to pending[/green]"
        )
        return reset_count

    async def reset_failed_items(self, date: str | None = None) -> int:
        """Reset failed items to pending status."""
        await self._ensure_initialized()
        reset_count = self.queue.reset_failed_items(date)
        console.print(f"[green]Reset {reset_count} failed items to pending[/green]")
        return reset_count

    async def rebuild_summary(self, date: str | None = None) -> int:
        """Rebuild summary table from actual queue counts."""
        await self._ensure_initialized()
        rebuilt_count = self.queue.rebuild_summary(date)
        console.print(f"[green]Rebuilt summary for {rebuilt_count} dates[/green]")
        return rebuilt_count

    async def export_results(self, date: str, export_path: str | None = None) -> bool:
        """Export processed results to JSON."""
        await self._ensure_initialized()
        if not export_path:
            export_path = f"./exports/image_text_{date}.json"

        if self.queue.export_results(date, export_path):
            console.print(f"[green]Exported results to {export_path}[/green]")
            return True
        else:
            console.print("[red]Export failed[/red]")
            return False

    def _load_prompt_file(self, prompt_path: str) -> str:
        """Load prompt content from file."""
        from pathlib import Path

        try:
            # Get the path relative to the script location
            script_dir = Path(__file__).parent.parent.parent  # Go up to project root
            full_path = script_dir / "src" / "config" / "prompts" / prompt_path

            if not full_path.exists():
                raise FileNotFoundError(f"Prompt file not found: {full_path}")

            with open(full_path, encoding="utf-8") as f:
                content = f.read().strip()

            if not content:
                raise ValueError(f"Prompt file is empty: {full_path}")

            return content

        except Exception as e:
            logger.error(f"Error loading prompt {prompt_path}: {e}")
            raise


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Consolidated Image Queue Management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    # Commands
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Status command
    status_parser = subparsers.add_parser("status", help="Show queue status")
    status_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")

    # Process command
    process_parser = subparsers.add_parser(
        "process", help="Process images with vision model"
    )
    process_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")
    process_parser.add_argument(
        "--batch-size", type=int, default=100, help="Batch size (default: 100)"
    )
    process_parser.add_argument(
        "--concurrency", type=int, default=6, help="Concurrent requests (default: 6)"
    )
    process_parser.add_argument(
        "--upload-to-aws", action="store_true", help="Upload results to AWS"
    )
    process_parser.add_argument(
        "--upload-to-local",
        action="store_true",
        help="Upload results to local DynamoDB",
    )
    process_parser.add_argument(
        "--upload-to-both", action="store_true", help="Upload to both local and AWS"
    )
    process_parser.add_argument(
        "--continuous", action="store_true", help="Continue polling for new items"
    )
    process_parser.add_argument(
        "--timeout",
        type=int,
        default=10,
        help="Timeout in minutes for continuous mode (default: 10)",
    )

    # Calculate PHash command
    phash_parser = subparsers.add_parser(
        "calculate-phash", help="Calculate PHash for processed items"
    )
    phash_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")
    phash_parser.add_argument("--limit", type=int, help="Limit number of items")

    # Upload command
    upload_parser = subparsers.add_parser(
        "upload", help="Upload processed items to DynamoDB"
    )
    upload_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")
    upload_parser.add_argument(
        "--target",
        choices=["local", "aws", "both"],
        default="aws",
        help="Upload target",
    )
    upload_parser.add_argument("--limit", type=int, help="Limit number of items")
    upload_parser.add_argument(
        "--show-details", action="store_true", help="Show detailed results"
    )
    upload_parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be updated without making changes",
    )
    upload_parser.add_argument(
        "--generate-summaries",
        action="store_true",
        help="Generate AI-enhanced summaries with image text",
    )
    upload_parser.add_argument(
        "--clear-sqlite",
        action="store_true",
        help="Clear SQLite records after successful upload",
    )
    upload_parser.add_argument(
        "--test-limit", type=int, default=25, help="Limit for testing (default: 25)"
    )

    # Full pipeline command
    pipeline_parser = subparsers.add_parser(
        "full-pipeline", help="Run full pipeline: process + phash + upload"
    )
    pipeline_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")
    pipeline_parser.add_argument(
        "--target",
        choices=["local", "aws", "both"],
        default="both",
        help="Upload target",
    )
    pipeline_parser.add_argument(
        "--batch-size", type=int, default=100, help="Batch size"
    )
    pipeline_parser.add_argument(
        "--concurrency", type=int, default=6, help="Concurrent requests"
    )
    pipeline_parser.add_argument(
        "--clear-sqlite",
        action="store_true",
        help="Clear SQLite records after successful upload",
    )

    # Reset commands
    reset_parser = subparsers.add_parser(
        "reset-processing", help="Reset stuck processing items"
    )
    reset_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")

    reset_failed_parser = subparsers.add_parser(
        "reset-failed", help="Reset failed items"
    )
    reset_failed_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")

    # Rebuild summary
    rebuild_parser = subparsers.add_parser(
        "rebuild-summary", help="Rebuild summary table"
    )
    rebuild_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")

    # Test summaries command
    test_parser = subparsers.add_parser(
        "test-summaries", help="Test summary generation with comparison (dry-run only)"
    )
    test_parser.add_argument("--date", help="Filter by scrape date (YYYYMMDD)")
    test_parser.add_argument(
        "--target",
        choices=["local", "aws"],
        default="aws",
        help="DynamoDB target for testing",
    )
    test_parser.add_argument(
        "--limit", type=int, default=25, help="Number of items to test (default: 25)"
    )

    # Export command
    export_parser = subparsers.add_parser("export", help="Export results to JSON")
    export_parser.add_argument("--date", required=True, help="Scrape date (YYYYMMDD)")
    export_parser.add_argument("--output", help="Output file path")

    # Common options
    parser.add_argument(
        "--queue-dir", default="./data/image_queue", help="Queue directory path"
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Convert date to config format if provided
    config_date = "01/01/70"  # Default
    if hasattr(args, "date") and args.date:
        try:
            date_obj = datetime.strptime(args.date, "%Y%m%d")
            config_date = date_obj.strftime("%m/%d/%y")
        except ValueError:
            console.print(
                f"[red]Invalid date format: {args.date}. Expected YYYYMMDD[/red]"
            )
            return

    # Initialize manager with async context manager
    async with ImageQueueManager(args.queue_dir, config_date) as manager:
        # Execute command
        if args.command == "status":
            await manager.display_status(args.date)

        elif args.command == "process":
            stats = await manager.process_images(
                date=args.date,
                batch_size=args.batch_size,
                concurrency=args.concurrency,
                upload_to_aws=args.upload_to_aws,
                upload_to_local=args.upload_to_local,
                upload_to_both=args.upload_to_both,
                continuous=args.continuous,
                timeout_minutes=args.timeout,
            )
            console.print("[bold green]Processing Complete![/bold green]")
            console.print(
                f"Processed: {stats['processed']}, Failed: {stats['failed']}, Skipped: {stats['skipped']}"
            )
            if stats.get("uploaded"):
                console.print(f"Uploaded: {stats['uploaded']}")

        elif args.command == "calculate-phash":
            stats = await manager.calculate_phash_for_processed_items(
                args.date, args.limit
            )

        elif args.command == "upload":
            targets = [args.target] if args.target != "both" else ["local", "aws"]

            # Use test limit if dry run is enabled and no explicit limit set
            limit = args.limit
            if args.dry_run and not limit:
                limit = args.test_limit
                console.print(
                    f"[yellow]Using test limit of {limit} items for dry run[/yellow]"
                )

            stats = await manager.upload_processed_items(
                date=args.date,
                targets=targets,
                limit=limit,
                show_details=args.show_details,
                dry_run=args.dry_run,
                generate_summaries=args.generate_summaries,
                clear_sqlite=args.clear_sqlite,
            )

        elif args.command == "full-pipeline":
            console.print("[bold cyan]Running Full Pipeline[/bold cyan]")

            # Step 1: Process images
            console.print("[cyan]Step 1: Processing images with vision model[/cyan]")
            process_stats = await manager.process_images(
                date=args.date, batch_size=args.batch_size, concurrency=args.concurrency
            )

            # Step 2: Calculate PHash for any items missing it
            console.print("[cyan]Step 2: Calculating PHash for processed items[/cyan]")
            phash_stats = await manager.calculate_phash_for_processed_items(args.date)

            # Step 3: Upload to DynamoDB
            console.print("[cyan]Step 3: Uploading to DynamoDB[/cyan]")
            targets = [args.target] if args.target != "both" else ["local", "aws"]
            upload_stats = await manager.upload_processed_items(
                args.date, targets, clear_sqlite=args.clear_sqlite
            )

            # Summary
            console.print("[bold green]Pipeline Complete![/bold green]")
            console.print(f"Images Processed: {process_stats['processed']}")
            console.print(f"PHash Calculated: {phash_stats['calculated']}")
            console.print(f"Items Uploaded: {upload_stats['uploaded']}")

        elif args.command == "reset-processing":
            await manager.reset_processing_items(args.date)

        elif args.command == "reset-failed":
            await manager.reset_failed_items(args.date)

        elif args.command == "rebuild-summary":
            await manager.rebuild_summary(args.date)

        elif args.command == "test-summaries":
            targets = [args.target]
            console.print("[bold cyan]Testing Summary Generation (Dry Run)[/bold cyan]")
            console.print(
                f"[yellow]Testing {args.limit} items - no changes will be made[/yellow]"
            )

            stats = await manager.upload_processed_items(
                date=args.date,
                targets=targets,
                limit=args.limit,
                show_details=True,
                dry_run=True,
                generate_summaries=True,
                clear_sqlite=False,
            )

            console.print("[bold green]Summary Test Complete![/bold green]")
            console.print(f"Summaries Generated: {stats.get('summaries_generated', 0)}")
            console.print(f"Summary Failures: {stats.get('summaries_failed', 0)}")

        elif args.command == "export":
            await manager.export_results(args.date, args.output)

        else:
            parser.print_help()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("[yellow]Interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        logger.exception("Unhandled exception")
