#!/usr/bin/env python3

import os
import ast
from collections import defaultdict

# Configuration
SRC_ROOT_DIR_NAME = "../../src"  # The name of the root directory to analyze


def get_module_fqn(file_path_abs, src_root_abs_path, src_root_name):
    """
    Calculates the fully qualified name (FQN) of a Python module.
    e.g., /path/to/project/src/foo/bar.py -> src.foo.bar
          /path/to/project/src/foo/__init__.py -> src.foo
          /path/to/project/src/__init__.py -> src
    """
    if not file_path_abs.startswith(src_root_abs_path):
        return None

    relative_path = os.path.relpath(file_path_abs, src_root_abs_path)

    path_parts = list(relative_path.split(os.sep))

    if not path_parts:  # Should not happen with valid relative_path
        return None

    if path_parts[-1] == "__init__.py":
        path_parts.pop()
        if not path_parts:  # This was src/__init__.py
            return src_root_name
    else:
        # Remove .py extension
        if path_parts[-1].endswith(".py"):
            path_parts[-1] = path_parts[-1][:-3]
        else:  # Not a python file, though caught by file_name.endswith(".py") earlier
            return None

    return ".".join([src_root_name] + path_parts)


class ImportVisitor(ast.NodeVisitor):
    def __init__(self, current_module_fqn, src_root_name, all_project_modules):
        self.current_module_fqn = current_module_fqn
        self.src_root_name = src_root_name
        self.all_project_modules = all_project_modules
        self.imported_modules = set()

    def _resolve_and_add_import(self, level, module_name_str_from_ast):
        """
        Resolves an import statement to an FQN and adds it if it's a project module.
        level: 0 for absolute, 1 for '.', 2 for '..' etc.
        module_name_str_from_ast: The module name string from the AST node.
            - For `import foo.bar`: "foo.bar"
            - For `from .foo import baz`: "foo"
            - For `from ..foo import baz`: "foo"
            - For `from foo.bar import baz`: "foo.bar"
            - For `from . import foo`: "foo" (when called appropriately from visit_ImportFrom)
        """
        resolved_fqn = None

        if level == 0:  # Absolute import
            if not module_name_str_from_ast: return

            # Case 1: Import is already FQN like "src.module.name"
            if module_name_str_from_ast.startswith(self.src_root_name + "."):
                resolved_fqn = module_name_str_from_ast
            # Case 2: Import is like "module.name" (implicitly under "src") or "src" itself
            elif module_name_str_from_ast == self.src_root_name:
                resolved_fqn = self.src_root_name
            else:
                # Assumed to be like "lib.utils" meaning "src.lib.utils"
                resolved_fqn = f"{self.src_root_name}.{module_name_str_from_ast}"

        else:  # Relative import (level >= 1)
            if not module_name_str_from_ast: return

            current_parts = self.current_module_fqn.split('.')

            # Determine the base path for the relative import
            # For current 'src.pkg.mod', level 1 ('.') base is 'src.pkg' -> parts ['src', 'pkg']
            # For current 'src.pkg.mod', level 2 ('..') base is 'src' -> parts ['src']
            # For current 'src.pkg' (from __init__), level 1 ('.') base is 'src' -> parts ['src']

            # Number of levels to go up from current module's containing package.
            # level 1 ('.') means 0 levels up from containing package. Base is current_parts[:-1]
            # level 2 ('..') means 1 level up. Base is current_parts[:-2]
            # So, base is current_parts[:-level]
            if level > len(current_parts) - 1 and not (self.current_module_fqn == self.src_root_name and level == 1):
                # Trying to go above src_root_name, or invalid relative import from src_root_name itself
                # e.g. from 'src.foo', 'from ..x' (level 2). current_parts=['src','foo']. Base is current_parts[:-2] = []. Invalid.
                # e.g. from 'src', 'from ..x' (level 2). current_parts=['src']. Base is current_parts[:-2]. Invalid.
                if not (self.current_module_fqn != self.src_root_name and len(current_parts) - 1 == level - 1 and
                        current_parts[0] == self.src_root_name):
                    # This condition allows from ..x in src.foo to resolve to src.x
                    # current_parts = [src, foo], level = 2. current_parts[:-level] -> []
                    # This should be handled by the fact that resolved_fqn won't start with src_root_name.
                    return

            base_path_parts = current_parts[:-level]
            imported_name_parts = module_name_str_from_ast.split('.')

            if not base_path_parts and self.src_root_name not in imported_name_parts[0]:
                # If base_path_parts is empty, it means the import resolves outside src, unless
                # imported_name_parts itself forms a valid FQN starting with src_root_name
                # (e.g. from .. import src from src.foo.bar -> base_parts=[], imported_name_parts=['src'])
                if not (len(imported_name_parts) > 0 and imported_name_parts[0] == self.src_root_name):
                    return  # Resolved outside project scope

            resolved_fqn = ".".join(base_path_parts + imported_name_parts)

        if resolved_fqn and resolved_fqn in self.all_project_modules and resolved_fqn != self.current_module_fqn:
            self.imported_modules.add(resolved_fqn)

    def visit_Import(self, node):
        for alias in node.names:
            # alias.name is the full module path, e.g., "os.path" or "my_package.my_module"
            self._resolve_and_add_import(level=0, module_name_str_from_ast=alias.name)
        self.generic_visit(node)

    def visit_ImportFrom(self, node):
        # node.module is the "from" part.
        # e.g., "from .foo import bar": node.module="foo", node.level=1
        # e.g., "from ..foo import bar": node.module="foo", node.level=2
        # e.g., "from foo import bar": node.module="foo", node.level=0
        # e.g., "from . import foo": node.module=None, node.level=1, names=[alias(name="foo")]

        if node.module:
            # e.g. from .MODULE import ... or from ..MODULE import ... or from MODULE import ...
            # Here, node.module is the name of the module being imported from.
            self._resolve_and_add_import(level=node.level, module_name_str_from_ast=node.module)
        else:
            # from . import NAME1, NAME2 ... (node.module is None)
            # Each 'NAME' in 'node.names' is a module/package relative to the current package.
            for alias in node.names:
                self._resolve_and_add_import(level=node.level, module_name_str_from_ast=alias.name)
        self.generic_visit(node)


def parse_imports_for_module(file_path, current_module_fqn, src_root_name, all_project_modules):
    """Parses a Python file and returns a set of FQNs of imported project modules."""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        tree = ast.parse(content, filename=file_path)

        visitor = ImportVisitor(current_module_fqn, src_root_name, all_project_modules)
        visitor.visit(tree)
        return visitor.imported_modules
    except SyntaxError as e:
        print(f"Syntax error parsing {file_path}: {e}")
        return set()
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return set()


def find_cycles_dfs(graph):
    """Finds all elementary cycles in a directed graph using DFS."""
    all_cycles = []

    # path: nodes in the current DFS path
    # visiting: nodes currently in the recursion stack for this DFS traversal
    # visited_globally: nodes whose subgraphs have been completely explored
    path = []
    visiting = set()
    visited_globally = set()

    ordered_nodes = sorted(list(graph.keys()))

    for node in ordered_nodes:
        if node not in visited_globally:
            _dfs_cycle_util(node, graph, path, visiting, visited_globally, all_cycles)

    return all_cycles


def _dfs_cycle_util(u, graph, path, visiting, visited_globally, all_cycles):
    path.append(u)
    visiting.add(u)

    if u in graph:  # Check if u has outgoing edges
        # Sort neighbors for deterministic cycle reporting
        for v in sorted(list(graph[u])):
            if v in visiting:  # Cycle detected
                try:
                    cycle_start_index = path.index(v)
                    # Found cycle: path[cycle_start_index:] + [v] to close loop
                    all_cycles.append(path[cycle_start_index:] + [v])
                except ValueError:  # Should not happen
                    pass
            elif v not in visited_globally:
                # Only recurse if v hasn't been fully explored from another entry point.
                # If v is in visited_globally, any cycles involving v would have been found.
                _dfs_cycle_util(v, graph, path, visiting, visited_globally, all_cycles)

    path.pop()
    visiting.remove(u)
    # Mark as globally visited only after exploring all paths from it in this DFS tree
    visited_globally.add(u)


def build_dependency_graph(src_root_dir_name):
    """Builds the module dependency graph for the project."""
    graph = defaultdict(set)
    all_project_modules = set()
    module_file_map = {}

    src_root_abs_path = os.path.abspath(src_root_dir_name)
    if not os.path.isdir(src_root_abs_path):
        print(f"Error: Source directory '{src_root_dir_name}' not found at '{src_root_abs_path}'.")
        return None, None

    # First pass: identify all modules in the project
    for root, dirs, files in os.walk(src_root_abs_path, topdown=True):
        # Prune __pycache__ directories
        if "__pycache__" in dirs:
            dirs.remove("__pycache__")

        for file_name in files:
            if file_name.endswith(".py"):
                file_path = os.path.join(root, file_name)
                module_fqn = get_module_fqn(file_path, src_root_abs_path, src_root_dir_name)
                if module_fqn:
                    all_project_modules.add(module_fqn)
                    module_file_map[module_fqn] = file_path
                    # Initialize all nodes in graph keys, even if they import nothing relevant
                    _ = graph[module_fqn]

    if not all_project_modules:
        print(f"No Python modules found in '{src_root_dir_name}'.")
        return None, None

    # Second pass: parse imports and build graph edges
    sorted_modules_to_parse = sorted(list(all_project_modules))  # Process in a defined order
    for module_fqn in sorted_modules_to_parse:
        file_path = module_file_map[module_fqn]
        imported_fqns = parse_imports_for_module(file_path, module_fqn, src_root_dir_name, all_project_modules)
        if imported_fqns:
            graph[module_fqn].update(imported_fqns)

    return graph, all_project_modules


def main():
    print(f"Analyzing '{SRC_ROOT_DIR_NAME}' for circular dependencies...\n")

    dependency_graph, all_modules = build_dependency_graph(SRC_ROOT_DIR_NAME)

    if dependency_graph is None:
        return

    # Optional: Print the graph for debugging
    # print("Dependency Graph:")
    # for mod in sorted(dependency_graph.keys()):
    #     deps = sorted(list(dependency_graph[mod]))
    #     if deps:
    #         print(f"  {mod} -> {deps}")
    # print("\n")

    cycles = find_cycles_dfs(dependency_graph)

    if cycles:
        print("Circular dependencies found:")
        unique_cycles_canonical = set()

        for cycle_path in cycles:
            # cycle_path is [N1, N2, ..., Nk, N1]
            # actual cycle nodes are cycle_path[:-1]
            cycle_nodes = cycle_path[:-1]
            if not cycle_nodes: continue

            # Create canonical representation (smallest node first, then sequence)
            min_node = min(cycle_nodes)
            min_idx = -1
            for i, node in enumerate(cycle_nodes):  # Find first occurrence of min_node
                if node == min_node:
                    min_idx = i
                    break

            canonical_cycle_tuple = tuple(cycle_nodes[min_idx:] + cycle_nodes[:min_idx])

            if canonical_cycle_tuple not in unique_cycles_canonical:
                unique_cycles_canonical.add(canonical_cycle_tuple)
                # Print the originally detected path for this canonical cycle
                print(f"  {' -> '.join(cycle_path)}")

        if not unique_cycles_canonical:  # Handles cases where cycles might be empty or trivial after processing
            print(f"\nNo complex circular dependencies found in '{SRC_ROOT_DIR_NAME}'.")

    else:
        print(f"No circular dependencies found in '{SRC_ROOT_DIR_NAME}'.")


if __name__ == "__main__":
    main()