#!/bin/bash

# ANSI color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}Hardcoded Path Replacement Script${NC}"
echo -e "${BLUE}============================================${NC}"
echo -e "This script will search for and replace hardcoded paths:"
echo -e "  ${YELLOW}/Users/<USER>/PycharmProjects/lexgenius${NC}"
echo -e "with dynamically resolved project root variables.${NC}"
echo

# First run in dry-run mode to see what changes would be made
echo -e "${YELLOW}=== DRY RUN MODE - NO CHANGES WILL BE MADE ===${NC}"
echo

echo -e "${GREEN}Checking hardcoded paths in src/scripts...${NC}"
python3 src/scripts/fix_hardcoded_paths.py --directory "src/scripts" --dry-run
echo

echo -e "${GREEN}Checking hardcoded paths in src/lib...${NC}"
python3 src/scripts/fix_hardcoded_paths.py --directory "src/lib" --dry-run
echo

echo -e "${GREEN}Checking hardcoded paths in src/data...${NC}"
python3 src/scripts/fix_hardcoded_paths.py --directory "src/data" --dry-run
echo

echo -e "${GREEN}Checking hardcoded paths in src/ root Python files...${NC}"
python3 src/scripts/fix_hardcoded_paths.py --directory "src" --extensions ".py" --dry-run
echo

echo -e "${YELLOW}=== DRY RUN COMPLETE ===${NC}"
echo

# Ask user if they want to proceed with actual changes
read -p "Do you want to proceed with these changes? (y/n): " answer
if [ "$answer" != "y" ]; then
    echo -e "${RED}Operation cancelled.${NC}"
    exit 0
fi

echo
echo -e "${YELLOW}=== MAKING ACTUAL CHANGES ===${NC}"
echo

# Run fix_hardcoded_paths.py for different directories
echo -e "${GREEN}Fixing hardcoded paths in src/scripts...${NC}"
python3 src/scripts/fix_hardcoded_paths.py --directory "src/scripts"
echo

echo -e "${GREEN}Fixing hardcoded paths in src/lib...${NC}"
python3 src/scripts/fix_hardcoded_paths.py --directory "src/lib"
echo

echo -e "${GREEN}Fixing hardcoded paths in src/data...${NC}"
python3 src/scripts/fix_hardcoded_paths.py --directory "src/data"
echo

echo -e "${GREEN}Fixing hardcoded paths in src/ root Python files...${NC}"
python3 src/scripts/fix_hardcoded_paths.py --directory "src" --extensions ".py"
echo

echo -e "${BLUE}============================================${NC}"
echo -e "${GREEN}All done! Make sure to run some tests to verify the changes.${NC}"
echo -e "${BLUE}============================================${NC}"