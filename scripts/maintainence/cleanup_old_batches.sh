#!/bin/bash

# Script to clean up old Mistral batch jobs

echo "Mistral Batch Cleanup Options:"
echo "=============================="
echo "1. Delete all batches created before today (including API files)"
echo "2. Keep last 7 days of batches (including API files)" 
echo "3. Keep last 30 days of batches (including API files)"
echo "4. Delete all batches before today (local files only)"
echo "5. Delete ALL uploaded files from Mistral API"
echo "6. Cancel ALL batch jobs in Mistral API"
echo "7. Show current batch status"
echo "8. Custom number of days to keep"

read -p "Select option (1-8): " option

case $option in
    1)
        echo "Deleting all batches created before today (including API files)..."
        python src/scripts/mistral_batch_ocr.py --cleanup 0
        ;;
    2)
        echo "Keeping last 7 days of batches (including API files)..."
        python src/scripts/mistral_batch_ocr.py --cleanup 7
        ;;
    3)
        echo "Keeping last 30 days of batches (including API files)..."
        python src/scripts/mistral_batch_ocr.py --cleanup 30
        ;;
    4)
        echo "Deleting all batches before today (local files only)..."
        python src/scripts/mistral_batch_ocr.py --cleanup 0 --no-delete-api
        ;;
    5)
        echo "WARNING: This will delete ALL uploaded files from Mistral API!"
        python src/scripts/mistral_batch_ocr.py --cleanup-api-files
        ;;
    6)
        echo "WARNING: This will cancel ALL batch jobs in Mistral API!"
        python src/scripts/mistral_batch_ocr.py --cancel-all-jobs
        ;;
    7)
        echo "Showing current batch status..."
        python src/scripts/mistral_batch_ocr.py --status
        ;;
    8)
        read -p "Enter number of days to keep: " days
        read -p "Also delete API files? (y/n): " delete_api
        if [ "$delete_api" = "y" ]; then
            echo "Keeping last $days days of batches (including API files)..."
            python src/scripts/mistral_batch_ocr.py --cleanup $days
        else
            echo "Keeping last $days days of batches (local files only)..."
            python src/scripts/mistral_batch_ocr.py --cleanup $days --no-delete-api
        fi
        ;;
    *)
        echo "Invalid option"
        exit 1
        ;;
esac