#!/bin/bash

# Run mypy and capture its output, including stderr
mypy_output=$(mypy src/lib 2>&1)

error_found=0
current_filename=""

# Process each line of mypy output
while IFS= read -r line; do
  if [[ "$line" == *": error:"* ]] && [[ "$line" == *"Cannot find module"* ]]; then
    error_found=1
    filename=$(echo "$line" | awk -F':' '{print $1}')
    error_message=$(echo "$line" | awk -F':' '{print $2":"$3":"$4":"$5":"$6":"$7":"$8":"$9":"$10}')

    filename=$(echo -e "$filename")
    error_message=$(echo -e "$error_message")

    # If filename changes, print a header for the new file
    if [[ "$filename" != "$current_filename" ]]; then
      if [[ -n "$current_filename" ]]; then
        echo ""  # Add blank line between files
      fi
      echo "File: $filename"
      current_filename="$filename"
    fi
    echo "  $error_message" # Print error message indented

  fi
done <<< "$mypy_output"

if [[ $error_found -eq 0 ]]; then
  echo "No import errors found by mypy."
elif [[ -n "$current_filename" ]]; then
  echo "" # Add blank line at the end if errors were printed
fi