#!/usr/bin/env python3
"""
Clear Mistral OCR tracker database tables
"""

import sqlite3
import sys
from pathlib import Path

def clear_database(db_path="sqlite/mistral_ocr_tracker.db"):
    """Clear all data from Mistral OCR tracker database."""
    
    if not Path(db_path).exists():
        print(f"Database {db_path} does not exist.")
        return
    
    # Confirm with user
    response = input(f"This will DELETE ALL DATA from {db_path}. Continue? (yes/no): ")
    if response.lower() != 'yes':
        print("Cancelled.")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Get table names
            tables = cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table'"
            ).fetchall()
            
            # Clear each table
            for table in tables:
                table_name = table[0]
                if table_name != 'sqlite_sequence':  # Skip system table
                    cursor.execute(f"DELETE FROM {table_name}")
                    print(f"Cleared table: {table_name}")
            
            # Reset auto-increment counters
            cursor.execute("DELETE FROM sqlite_sequence")
            
            conn.commit()
            
        print("\nDatabase cleared successfully!")
        
    except Exception as e:
        print(f"Error clearing database: {e}")
        sys.exit(1)

def show_table_counts(db_path="sqlite/mistral_ocr_tracker.db"):
    """Show record counts for all tables."""
    
    if not Path(db_path).exists():
        print(f"Database {db_path} does not exist.")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Get table names
            tables = cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table'"
            ).fetchall()
            
            print("\nTable Record Counts:")
            print("-" * 40)
            
            total_records = 0
            for table in tables:
                table_name = table[0]
                if table_name != 'sqlite_sequence':
                    count = cursor.execute(f"SELECT COUNT(*) FROM {table_name}").fetchone()[0]
                    print(f"{table_name:<20} {count:>10} records")
                    total_records += count
            
            print("-" * 40)
            print(f"{'Total':<20} {total_records:>10} records")
            
    except Exception as e:
        print(f"Error reading database: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Clear Mistral OCR tracker database")
    parser.add_argument("--db", default="sqlite/mistral_ocr_tracker.db", 
                        help="Path to database file")
    parser.add_argument("--show", action="store_true", 
                        help="Show table counts without clearing")
    
    args = parser.parse_args()
    
    if args.show:
        show_table_counts(args.db)
    else:
        show_table_counts(args.db)
        print()
        clear_database(args.db)