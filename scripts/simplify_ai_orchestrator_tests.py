#!/usr/bin/env python3
"""
Simplify AI orchestrator tests to avoid complex DI override issues.
"""
from pathlib import Path


def simplify_tests():
    """Simplify the AI orchestrator tests."""
    test_file = Path('tests/unit/services/ai/test_ai_orchestrator.py')
    
    if not test_file.exists():
        print(f"❌ {test_file} not found")
        return
    
    # Read the current content
    content = test_file.read_text()
    
    # Rewrite the fixture to not use override_providers
    new_fixture = '''@pytest.fixture
async def ai_orchestrator(mock_deepseek, mock_gpt4, mock_llava, mock_s3, mock_logger):
    """AI orchestrator fixture using DI container."""
    # Create AI orchestrator directly with mock services
    from types import SimpleNamespace
    config = SimpleNamespace(is_test_environment=True)
    
    orchestrator = AIOrchestrator(
        logger=mock_logger,
        config=config,
        deepseek=mock_deepseek,
        gpt4=mock_gpt4,
        llava=mock_llava,
        s3_storage=mock_s3
    )
    
    # Initialize the service
    await orchestrator.initialize()
    
    yield orchestrator'''
    
    # Find the fixture and replace it
    import re
    pattern = r'@pytest\.fixture\s+async def ai_orchestrator.*?yield orchestrator'
    content = re.sub(pattern, new_fixture, content, flags=re.DOTALL)
    
    # Write the simplified content
    test_file.write_text(content)
    print(f"✅ Simplified AI orchestrator test fixture in {test_file}")


def main():
    """Main function."""
    print("Simplifying AI orchestrator tests...")
    print("=" * 60)
    
    simplify_tests()
    
    print("=" * 60)
    print("✅ AI orchestrator tests simplified")


if __name__ == '__main__':
    main()