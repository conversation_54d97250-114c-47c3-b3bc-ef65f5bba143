#!/usr/bin/env python3
"""
Script to match S3 HTML files to docket JSON files.
Compares files in data/YYYYMMDD/dockets/ with S3 objects in YYYYMMDD/html/.
"""

import argparse
import asyncio
import json
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.infrastructure.storage.s3_async import S3AsyncStorage

console = Console()


def extract_case_identifier(filename: str) -> str:
    """
    Extract case identifier in format {court_id}_YY_NNNNN from filename.

    Args:
        filename: File name to extract from

    Returns:
        Case identifier or empty string if not found
    """
    # Pattern: court_id_YY_NNNNN (e.g., mad_23_12345)
    pattern = r'([a-z]+d?)_(\d{2})_(\d{5})'
    match = re.search(pattern, filename.lower())
    if match:
        return f"{match.group(1)}_{match.group(2)}_{match.group(3)}"
    return ""


async def get_s3_html_files(s3_storage: S3AsyncStorage, date_str: str, silent: bool = False) -> List[str]:
    """
    Get list of HTML files from S3 for given date.

    Args:
        s3_storage: S3 storage instance
        date_str: Date in YYYYMMDD format

    Returns:
        List of S3 object keys
    """
    prefix = f"{date_str}/html/"

    if silent:
        # No progress display in silent mode
        try:
            objects = await s3_storage.list_objects(prefix)
            html_files = [obj for obj in objects if obj.endswith('.html')]
            return html_files
        except Exception as e:
            return []
    else:
        with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
        ) as progress:
            task = progress.add_task(f"Fetching S3 objects from {prefix}...", total=None)

            try:
                objects = await s3_storage.list_objects(prefix)
                progress.update(task, completed=True)

                # Filter only HTML files
                html_files = [obj for obj in objects if obj.endswith('.html')]

                console.print(f"[green]Found {len(html_files)} HTML files in S3[/green]")
                return html_files

            except Exception as e:
                console.print(f"[red]Error fetching S3 objects: {e}[/red]")
                return []


def get_local_json_files(date_str: str, silent: bool = False) -> List[str]:
    """
    Get list of JSON files from local dockets directory.

    Args:
        date_str: Date in YYYYMMDD format

    Returns:
        List of JSON file paths
    """
    dockets_dir = Path(f"data/{date_str}/dockets")

    if not dockets_dir.exists():
        if not silent:
            console.print(f"[yellow]Directory not found: {dockets_dir}[/yellow]")
        return []

    json_files = list(dockets_dir.glob("*.json"))
    if not silent:
        console.print(f"[green]Found {len(json_files)} JSON files in {dockets_dir}[/green]")

    return [str(f) for f in json_files]


def match_files(json_files: List[str], s3_files: List[str]) -> Tuple[Dict[str, str], List[str], List[str]]:
    """
    Match JSON files to S3 HTML files based on case identifier.

    Args:
        json_files: List of local JSON file paths
        s3_files: List of S3 object keys

    Returns:
        Tuple of (matches dict, unmatched json files, unmatched s3 files)
    """
    # Extract identifiers
    json_map = {}
    for json_file in json_files:
        identifier = extract_case_identifier(os.path.basename(json_file))
        if identifier:
            json_map[identifier] = json_file

    s3_map = {}
    for s3_file in s3_files:
        identifier = extract_case_identifier(os.path.basename(s3_file))
        if identifier:
            s3_map[identifier] = s3_file

    # Find matches
    matches = {}
    matched_identifiers = set()

    for identifier, json_file in json_map.items():
        if identifier in s3_map:
            matches[json_file] = s3_map[identifier]
            matched_identifiers.add(identifier)

    # Find unmatched files
    unmatched_json = [f for id, f in json_map.items() if id not in matched_identifiers]
    unmatched_s3 = [f for id, f in s3_map.items() if id not in matched_identifiers]

    return matches, unmatched_json, unmatched_s3


def extract_court_docket_from_filename(filename: str) -> Tuple[str, str]:
    """
    Extract court_id and docket_num from filename.
    
    Args:
        filename: File name to extract from
        
    Returns:
        Tuple of (court_id, docket_num) or ("", "") if not found
    """
    # Pattern: court_id_YY_NNNNN (e.g., mad_23_12345)
    pattern = r'([a-z]+d?)_(\d{2})_(\d{5})'
    match = re.search(pattern, filename.lower())
    if match:
        court_id = match.group(1)
        docket_num = f"{match.group(2)}-{match.group(3)}"
        return court_id, docket_num
    return "", ""


def display_results_validate(matches: Dict[str, str], unmatched_json: List[str], unmatched_s3: List[str]) -> List[Dict[str, str]]:
    """
    Display results in the format requested for validation.
    Extracts court_id and docket_num for unmatched files.
    Returns the list of unmatched files.
    """
    unmatched_list = []
    
    # Process unmatched JSON files
    for f in unmatched_json:
        court_id, docket_num = extract_court_docket_from_filename(os.path.basename(f))
        if court_id and docket_num:
            unmatched_list.append({"court_id": court_id, "docket_num": docket_num})
    
    # Process unmatched S3 files
    for f in unmatched_s3:
        court_id, docket_num = extract_court_docket_from_filename(os.path.basename(f))
        if court_id and docket_num:
            unmatched_list.append({"court_id": court_id, "docket_num": docket_num})
    
    # FORCE PRINT - no console, no rich, just plain print
    import sys
    sys.stdout.write(json.dumps(unmatched_list))
    sys.stdout.write('\n')
    sys.stdout.flush()
    
    return unmatched_list


def display_results(matches: Dict[str, str], unmatched_json: List[str], unmatched_s3: List[str]):
    """
    Display matching results in formatted tables.

    Args:
        matches: Dictionary of JSON file to S3 file matches
        unmatched_json: List of unmatched JSON files
        unmatched_s3: List of unmatched S3 files
    """
    console.print("\n[bold cyan]═══ MATCHING RESULTS ═══[/bold cyan]\n")

    # Display matches
    if matches:
        console.print(f"[bold green]✓ Found {len(matches)} matches:[/bold green]\n")

        for json_file, s3_file in sorted(matches.items()):
            console.print(f"[green]JSON:[/green] {os.path.basename(json_file)}")
            console.print(f"[blue]S3:  [/blue] {s3_file}\n")
    else:
        console.print("[yellow]No matches found[/yellow]\n")

    # Display unmatched files
    if unmatched_json or unmatched_s3:
        console.print("\n[bold yellow]⚠ Unmatched Files:[/bold yellow]\n")

        if unmatched_json:
            console.print(f"[yellow]JSON files without S3 match ({len(unmatched_json)}):[/yellow]")
            for f in sorted(unmatched_json):
                console.print(f"  - {os.path.basename(f)}")
            console.print()

        if unmatched_s3:
            console.print(f"[yellow]S3 files without JSON match ({len(unmatched_s3)}):[/yellow]")
            for f in sorted(unmatched_s3):
                console.print(f"  - {f}")

    # Summary
    console.print("\n[bold]Summary:[/bold]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Type", style="cyan")
    table.add_column("Count", justify="right")

    table.add_row("Matches", str(len(matches)))
    table.add_row("Unmatched JSON", str(len(unmatched_json)))
    table.add_row("Unmatched S3", str(len(unmatched_s3)))
    table.add_row("Total JSON", str(len(matches) + len(unmatched_json)))
    table.add_row("Total S3", str(len(matches) + len(unmatched_s3)))

    console.print(table)


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Match S3 HTML files to docket JSON files")
    parser.add_argument(
        "--date",
        required=True,
        help="Date in YYYYMMDD format"
    )
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Show unmatched files in JSON format for validation"
    )

    args = parser.parse_args()

    # Validate date format
    if not re.match(r'^\d{8}$', args.date):
        if not args.validate:
            console.print("[red]Error: Date must be in YYYYMMDD format[/red]")
        else:
            print(json.dumps({"error": "Date must be in YYYYMMDD format"}))
        sys.exit(1)

    if not args.validate:
        console.print(f"\n[bold]Matching S3 files to dockets for date: {args.date}[/bold]\n")

    # Load environment variables from project root
    from dotenv import load_dotenv
    project_root = Path(__file__).parent.parent.parent
    env_path = project_root / '.env'
    load_dotenv(env_path)

    # Get AWS credentials - try multiple possible env var names
    aws_access_key = (
        os.getenv('AWS_ACCESS_KEY_ID') or 
        os.getenv('LEXGENIUS_AWS_ACCESS_KEY_ID') or
        os.getenv('AWS_ACCESS_KEY')
    )
    aws_secret_key = (
        os.getenv('AWS_SECRET_ACCESS_KEY') or 
        os.getenv('LEXGENIUS_AWS_SECRET_ACCESS_KEY') or
        os.getenv('AWS_SECRET_KEY')
    )
    bucket_name = (
        os.getenv('AWS_S3_BUCKET') or 
        os.getenv('LEXGENIUS_AWS_S3_BUCKET') or
        os.getenv('S3_BUCKET_NAME') or
        'lexgeniuswebsite'
    )
    aws_region = (
        os.getenv('AWS_REGION') or 
        os.getenv('LEXGENIUS_AWS_REGION') or
        os.getenv('AWS_DEFAULT_REGION') or
        'us-west-2'
    )

    if not aws_access_key or not aws_secret_key:
        if not args.validate:
            console.print("[red]Error: AWS credentials not found in environment[/red]")
        else:
            print(json.dumps({"error": "AWS credentials not found in environment"}))
        sys.exit(1)

    # Initialize S3 storage
    async with S3AsyncStorage(
            bucket_name=bucket_name,
            aws_access_key=aws_access_key,
            aws_secret_key=aws_secret_key,
            aws_region=aws_region
    ) as s3_storage:
        # Get S3 files
        s3_files = await get_s3_html_files(s3_storage, args.date, silent=args.validate)

        # Get local JSON files
        json_files = get_local_json_files(args.date, silent=args.validate)

        if not json_files and not s3_files:
            if not args.validate:
                console.print("[yellow]No files found to match[/yellow]")
            return []

        # Match files
        matches, unmatched_json, unmatched_s3 = match_files(json_files, s3_files)

        # Display results
        if args.validate:
            return display_results_validate(matches, unmatched_json, unmatched_s3)
        else:
            display_results(matches, unmatched_json, unmatched_s3)
            return []


if __name__ == "__main__":
    result = asyncio.run(main())
    if result:
        # Exit with the result as JSON for programmatic use
        sys.exit(0)