[{"file_path": "src/services/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/transformer/docket_file_manager.py", "constructor_info": [{"class_name": "DocketFileManager", "parameters": [{"name": "download_dir", "type": "str", "default": null}, {"name": "file_handler", "type": null, "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["download_dir", "file_handler"], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DocketFileManager", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/docket_validator.py", "constructor_info": [{"class_name": "DocketValidator", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DocketValidator", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/error_handler.py", "constructor_info": [{"class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["file_handler"], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/data_transformer.py", "constructor_info": [{"class_name": "TransformationJob", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "Reporter", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": []}, {"class_name": "DataTransformer", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}, {"name": "openai_client", "type": "Optional[Any]", "default": "None"}, {"name": "deepseek_service", "type": "Optional[Any]", "default": "None"}, {"name": "mistral_service", "type": "Optional[Any]", "default": "None"}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config", "logger"], "optional_parameters": ["openai_client", "deepseek_service", "mistral_service", "shutdown_event"], "special_initialization_patterns": []}, {"class_name": "StorageConfig", "parameters": [{"name": "config", "type": null, "default": null}], "required_parameters": ["config"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "TransformationJob", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "Reporter", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "DataTransformer", "dependencies": [{"name": "config", "type": "Dict", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}, {"name": "openai_client", "type": "Optional[Any]", "optional": true}, {"name": "deepseek_service", "type": "Optional[Any]", "optional": true}, {"name": "mistral_service", "type": "Optional[Any]", "optional": true}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "StorageConfig", "dependencies": [{"name": "config", "type": null, "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/docket_data_cleaner.py", "constructor_info": [{"class_name": "DocketDataCleaner", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DocketDataCleaner", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/docket_processor.py", "constructor_info": [{"class_name": "DocketProcessor", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "llm_client", "type": "Any", "default": "None"}, {"name": "law_firm_processor", "type": "Any", "default": "None"}, {"name": "transfer_handler", "type": "Any", "default": "None"}, {"name": "html_data_updater", "type": "Any", "default": "None"}, {"name": "validator", "type": "Any", "default": "None"}, {"name": "file_handler", "type": "Any", "default": "None"}, {"name": "pdf_processor", "type": "Optional[Any]", "default": "None"}, {"name": "gpt_client", "type": "Optional[Any]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["llm_client", "law_firm_processor", "transfer_handler", "html_data_updater", "validator", "file_handler", "pdf_processor", "gpt_client", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DocketProcessor", "dependencies": [{"name": "config", "type": "Dict", "optional": false}, {"name": "llm_client", "type": "Any", "optional": true}, {"name": "gpt_client", "type": "Optional[Any]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/mdl_processor.py", "constructor_info": [{"class_name": "MDLProcessor", "parameters": [{"name": "mdl_litigations", "type": "pd.DataFrame", "default": null}, {"name": "mdl_path", "type": "str", "default": null}, {"name": "file_handler", "type": null, "default": null}, {"name": "gpt", "type": null, "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "litigation_classifier", "type": null, "default": "None"}, {"name": "pdf_cache", "type": null, "default": "None"}, {"name": "download_dir", "type": "Optional[str]", "default": "None"}, {"name": "district_court_db", "type": null, "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["mdl_litigations", "mdl_path", "file_handler", "gpt"], "optional_parameters": ["config", "litigation_classifier", "pdf_cache", "download_dir", "district_court_db", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "MDLProcessor", "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/uploader.py", "constructor_info": [{"class_name": "Uploader", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "s3_manager", "type": "S3AsyncStorage", "default": null}, {"name": "pacer_db", "type": "PacerRepository", "default": null}, {"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config", "s3_manager", "pacer_db", "file_handler"], "optional_parameters": ["logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "Uploader", "dependencies": [{"name": "config", "type": "Dict", "optional": false}, {"name": "pacer_db", "type": "PacerRepository", "optional": false}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/mdl_description_manager.py", "constructor_info": [{"class_name": "MDLDescriptionManager", "parameters": [{"name": "file_handler", "type": null, "default": null}, {"name": "gpt_client", "type": null, "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "download_dir", "type": "Optional[str]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["file_handler", "gpt_client"], "optional_parameters": ["config", "download_dir", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "MDLDescriptionManager", "dependencies": [{"name": "gpt_client", "type": null, "optional": false}, {"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/mdl_processor_original.py", "constructor_info": [{"class_name": "MDLProcessor", "parameters": [{"name": "mdl_litigations", "type": "pd.DataFrame", "default": null}, {"name": "mdl_path", "type": "str", "default": null}, {"name": "file_handler", "type": null, "default": null}, {"name": "gpt", "type": null, "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "litigation_classifier", "type": null, "default": "None"}, {"name": "pdf_cache", "type": null, "default": "None"}, {"name": "download_dir", "type": "Optional[str]", "default": "None"}, {"name": "district_court_db", "type": null, "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["mdl_litigations", "mdl_path", "file_handler", "gpt"], "optional_parameters": ["config", "litigation_classifier", "pdf_cache", "download_dir", "district_court_db", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "MDLProcessor", "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/specialized_workflows.py", "constructor_info": [{"class_name": "SpecializedWorkflows", "parameters": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "default": null}, {"name": "docket_processor", "type": null, "default": null}, {"name": "mdl_processor", "type": null, "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "config", "type": "Optional[Dict]", "default": "None"}], "required_parameters": ["file_handler", "docket_processor", "mdl_processor"], "optional_parameters": ["logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "SpecializedWorkflows", "dependencies": [{"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}, {"name": "config", "type": "Optional[Dict]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/file_operations.py", "constructor_info": [{"class_name": "FileOperationsManager", "parameters": [{"name": "file_handler", "type": "<PERSON><PERSON><PERSON><PERSON>", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["file_handler"], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FileOperationsManager", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/docket_text_handler.py", "constructor_info": [{"class_name": "DocketTextHandler", "parameters": [{"name": "file_handler", "type": "Any", "default": null}, {"name": "pdf_processor", "type": "Optional[Any]", "default": "None"}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["file_handler"], "optional_parameters": ["pdf_processor", "config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DocketTextHandler", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/litigation_classifier.py", "constructor_info": [{"class_name": "LitigationRule", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "LitigationClassifier", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "LitigationRule", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "LitigationClassifier", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/data_processing_engine.py", "constructor_info": [{"class_name": "DataProcessingEngine", "parameters": [{"name": "docket_processor", "type": "DocketProcessor", "default": null}, {"name": "mdl_processor", "type": "MDLProcessor", "default": null}, {"name": "llm_client", "type": null, "default": null}, {"name": "logger", "type": null, "default": null}, {"name": "config", "type": null, "default": null}, {"name": "html_integration_service", "type": null, "default": "None"}], "required_parameters": ["docket_processor", "mdl_processor", "llm_client", "logger", "config"], "optional_parameters": ["html_integration_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DataProcessingEngine", "dependencies": [{"name": "llm_client", "type": null, "optional": false}, {"name": "logger", "type": null, "optional": false}, {"name": "config", "type": null, "optional": false}, {"name": "html_integration_service", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/law_firm_integration.py", "constructor_info": [{"class_name": "LawFirmIntegration", "parameters": [{"name": "law_firm_processor", "type": "Optional[Any]", "default": "None"}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["law_firm_processor", "config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "LawFirmIntegration", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/law_firm_processor.py", "constructor_info": [{"class_name": "LawFirmProcessor", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "s3_storage", "type": "Optional[Any]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger", "s3_storage"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "LawFirmProcessor", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/mdl_persistence_manager.py", "constructor_info": [{"class_name": "MDLPersistenceManager", "parameters": [{"name": "mdl_path", "type": "str", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["mdl_path"], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "MDLPersistenceManager", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/docket_llm_engine.py", "constructor_info": [{"class_name": "DocketLLMEngine", "parameters": [{"name": "llm_client", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["llm_client"], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DocketLLMEngine", "dependencies": [{"name": "llm_client", "type": "Any", "optional": false}, {"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/mdl_lookup_manager.py", "constructor_info": [{"class_name": "MDLLookupManager", "parameters": [{"name": "district_court_db", "type": null, "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["district_court_db"], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "MDLLookupManager", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/config.py", "constructor_info": [{"class_name": "ScraperConfig", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "AppConfig", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "DataTransformerConfig", "parameters": [{"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["logger"], "special_initialization_patterns": []}, {"class_name": "Config", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}], "dependency_info": [{"class_name": "ScraperConfig", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "AppConfig", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "DataTransformerConfig", "dependencies": [{"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "Config", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/transfer_handler.py", "constructor_info": [{"class_name": "TransferHandler", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "pacer_db", "type": "PacerRepository", "default": null}, {"name": "district_court_db", "type": "DistrictCourtsRepository", "default": null}, {"name": "mdl_processor", "type": "MDLProcessor", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config", "pacer_db", "district_court_db", "mdl_processor"], "optional_parameters": ["logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "TransferHandler", "dependencies": [{"name": "config", "type": "Dict", "optional": false}, {"name": "pacer_db", "type": "PacerRepository", "optional": false}, {"name": "district_court_db", "type": "DistrictCourtsRepository", "optional": false}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/file_handler.py", "constructor_info": [{"class_name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "s3_manager", "type": "Optional[Any]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["s3_manager", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "<PERSON><PERSON><PERSON><PERSON>", "dependencies": [{"name": "config", "type": "Dict", "optional": false}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/html_integration_service.py", "constructor_info": [{"class_name": "TransformerHTMLIntegrationService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "TransformerHTMLIntegrationService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/afff_calculator.py", "constructor_info": [{"class_name": "AfffCalculator", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "AfffCalculator", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/mdl_data_processor.py", "constructor_info": [{"class_name": "MDLDataProcessor", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "MDLDataProcessor", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/component_factory.py", "constructor_info": [{"class_name": "ComponentFactory", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "openai_client", "type": "Optional[GPTClient]", "default": "None"}, {"name": "deepseek_service", "type": "Optional[DeepSeekService]", "default": "None"}, {"name": "mistral_service", "type": "Optional[MistralService]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["logger", "openai_client", "deepseek_service", "mistral_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ComponentFactory", "dependencies": [{"name": "config", "type": "Dict", "optional": false}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}, {"name": "openai_client", "type": "Optional[GPTClient]", "optional": true}, {"name": "deepseek_service", "type": "Optional[DeepSeekService]", "optional": true}, {"name": "mistral_service", "type": "Optional[MistralService]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/docket_html_processor.py", "constructor_info": [{"class_name": "DocketHTMLProcessor", "parameters": [{"name": "html_data_updater", "type": "Any", "default": null}, {"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "html_processing_service", "type": "Optional[Any]", "default": "None"}], "required_parameters": ["html_data_updater"], "optional_parameters": ["config", "logger", "html_processing_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DocketHTMLProcessor", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}, {"name": "html_processing_service", "type": "Optional[Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/court_data_processor.py", "constructor_info": [{"class_name": "CourtDataProcessor", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "CourtDataProcessor", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/transformer/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/transformer/cached_pdf_data.py", "constructor_info": [{"class_name": "CachedPdfData", "parameters": [{"name": "config", "type": "Optional[Dict]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "litigation_classifier", "type": "Optional[LitigationClassifier]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger", "litigation_classifier"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "CachedPdfData", "dependencies": [{"name": "config", "type": "Optional[Dict]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/orchestration/main_orchestrator.py", "constructor_info": [{"class_name": "MainOrchestrator", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "factory", "type": "MainServiceFactory", "default": null}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config", "factory"], "optional_parameters": ["shutdown_event"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "MainOrchestrator", "dependencies": [{"name": "config", "type": "WorkflowConfig", "optional": false}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/orchestration/processing_orchestrator.py", "constructor_info": [{"class_name": "ProcessingOrchestrator", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["shutdown_event"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ProcessingOrchestrator", "dependencies": [{"name": "config", "type": "WorkflowConfig", "optional": false}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/orchestration/scraping_orchestrator.py", "constructor_info": [{"class_name": "ScrapingOrchestrator", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "pacer_service", "type": "PacerOrchestratorService", "default": null}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config", "pacer_service"], "optional_parameters": ["shutdown_event"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ScrapingOrchestrator", "dependencies": [{"name": "config", "type": "WorkflowConfig", "optional": false}, {"name": "pacer_service", "type": "PacerOrchestratorService", "optional": false}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/orchestration/fb_ads_orchestrator_cli.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/orchestration/upload_orchestrator.py", "constructor_info": [{"class_name": "UploadOrchestrator", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "s3_storage_client", "type": "Optional[S3AsyncStorage]", "default": "None"}, {"name": "dynamo_storage_client", "type": "Optional[AsyncDynamoDBStorage]", "default": "None"}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["s3_storage_client", "dynamo_storage_client", "shutdown_event"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "UploadOrchestrator", "dependencies": [{"name": "config", "type": "WorkflowConfig", "optional": false}, {"name": "s3_storage_client", "type": "Optional[S3AsyncStorage]", "optional": true}, {"name": "dynamo_storage_client", "type": "Optional[AsyncDynamoDBStorage]", "optional": true}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/orchestration/fb_ads_orchestrator.py", "constructor_info": [{"class_name": "FbAdsOrchestrator", "parameters": [{"name": "config", "type": "WorkflowConfig", "default": null}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["shutdown_event"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FbAdsOrchestrator", "dependencies": [{"name": "config", "type": "WorkflowConfig", "optional": false}, {"name": "shutdown_event", "type": "Optional[asyncio.Event]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/orchestration/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/infrastructure/resource_cleanup_service.py", "constructor_info": [{"class_name": "ResourceCleanupService", "parameters": [{"name": "logger", "type": null, "default": "None"}, {"name": "config", "type": null, "default": "None"}], "required_parameters": [], "optional_parameters": ["logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ResourceCleanupService", "dependencies": [{"name": "logger", "type": null, "optional": true}, {"name": "config", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/infrastructure/performance_monitor_service.py", "constructor_info": [{"class_name": "OperationMetrics", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "PerformanceMonitorService", "parameters": [{"name": "logger", "type": null, "default": "None"}, {"name": "config", "type": null, "default": "None"}], "required_parameters": [], "optional_parameters": ["logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "OperationMetrics", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "PerformanceMonitorService", "dependencies": [{"name": "logger", "type": null, "optional": true}, {"name": "config", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/uploader/upload_service.py", "constructor_info": [{"class_name": "UploadService", "parameters": [{"name": "bucket_name", "type": "str", "default": null}, {"name": "s3_client", "type": null, "default": "None"}, {"name": "dynamodb_client", "type": null, "default": "None"}, {"name": "region_name", "type": "str", "default": "'us-east-1'"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["bucket_name"], "optional_parameters": ["s3_client", "dynamodb_client", "region_name", "logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "UploadService", "dependencies": [{"name": "s3_client", "type": null, "optional": true}, {"name": "dynamodb_client", "type": null, "optional": true}, {"name": "logger", "type": "Optional[LoggerProtocol]", "optional": true}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/uploader/s3_upload_service.py", "constructor_info": [{"class_name": "S3UploadService", "parameters": [{"name": "bucket_name", "type": "str", "default": null}, {"name": "cloudfront_distribution_id", "type": "Optional[str]", "default": "None"}, {"name": "s3_client", "type": null, "default": "None"}, {"name": "region_name", "type": "str", "default": "'us-east-1'"}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["bucket_name"], "optional_parameters": ["cloudfront_distribution_id", "s3_client", "region_name", "logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "S3UploadService", "dependencies": [{"name": "s3_client", "type": null, "optional": true}, {"name": "logger", "type": "Optional[LoggerProtocol]", "optional": true}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/uploader/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/district_courts/query_service.py", "constructor_info": [{"class_name": "DistrictCourtsQueryService", "parameters": [{"name": "repository", "type": "DistrictCourtsRepository", "default": null}], "required_parameters": ["repository"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DistrictCourtsQueryService", "dependencies": [{"name": "repository", "type": "DistrictCourtsRepository", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/ad_df_processor_service.py", "constructor_info": [{"class_name": "AdDataFrameProcessorService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "AdDataFrameProcessorService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/processing_service.py", "constructor_info": [{"class_name": "ReportsProcessingService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ReportsProcessingService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/config_service.py", "constructor_info": [{"class_name": "ReportsConfigService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}, {"class_name": "ReportsConfig", "parameters": [{"name": "config_dict", "type": "Dict[str, Any]", "default": null}, {"name": "is_weekly_report", "type": "bool", "default": "False"}], "required_parameters": ["config_dict"], "optional_parameters": ["is_weekly_report"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ReportsConfigService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "ReportsConfig", "dependencies": [{"name": "config_dict", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/rendering_service.py", "constructor_info": [{"class_name": "ReportsRenderingService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ReportsRenderingService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/publishing_service.py", "constructor_info": [{"class_name": "ReportsPublishingService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ReportsPublishingService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/data_loader_service.py", "constructor_info": [{"class_name": "ReportsDataLoaderService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ReportsDataLoaderService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/reports_orchestrator_service.py", "constructor_info": [{"class_name": "ReportsOrchestratorService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ReportsOrchestratorService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/ad_page_generator_service.py", "constructor_info": [{"class_name": "AdPageGeneratorService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "AdPageGeneratorService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/reports/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/monitoring/performance_monitoring_service.py", "constructor_info": [{"class_name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "MonitoringConfig", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "PerformanceMonitoringService", "parameters": [{"name": "config", "type": "Optional[MonitoringConfig]", "default": "None"}, {"name": "logger", "type": null, "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "<PERSON><PERSON><PERSON><PERSON>", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "MonitoringConfig", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "PerformanceMonitoringService", "dependencies": [{"name": "config", "type": "Optional[MonitoringConfig]", "optional": true}, {"name": "logger", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_archive/query_service.py", "constructor_info": [{"class_name": "FBArchiveQueryService", "parameters": [{"name": "repository", "type": "FBArchiveRepository", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FBArchiveQueryService", "dependencies": [{"name": "repository", "type": "FBArchiveRepository", "optional": false}, {"name": "logger", "type": "Optional[LoggerProtocol]", "optional": true}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_archive/delete_service.py", "constructor_info": [{"class_name": "FBArchiveDeleteService", "parameters": [{"name": "repository", "type": "FBArchiveRepository", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FBArchiveDeleteService", "dependencies": [{"name": "repository", "type": "FBArchiveRepository", "optional": false}, {"name": "logger", "type": "Optional[LoggerProtocol]", "optional": true}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_archive/update_service.py", "constructor_info": [{"class_name": "FBArchiveUpdateService", "parameters": [{"name": "repository", "type": "FBArchiveRepository", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}], "required_parameters": ["repository", "config"], "optional_parameters": ["logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FBArchiveUpdateService", "dependencies": [{"name": "repository", "type": "FBArchiveRepository", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "Optional[LoggerProtocol]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_archive/data_conversion_service.py", "constructor_info": [{"class_name": "FBArchiveDataConversionService", "parameters": [{"name": "repository", "type": "FBArchiveRepository", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FBArchiveDataConversionService", "dependencies": [{"name": "repository", "type": "FBArchiveRepository", "optional": false}, {"name": "logger", "type": "Optional[LoggerProtocol]", "optional": true}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/ai/deepseek_service.py", "constructor_info": [{"class_name": "DeepSeekService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DeepSeekService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/ai/ai_orchestrator.py", "constructor_info": [{"class_name": "AIOrchestrator", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "AIOrchestrator", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/ai/prompt_manager.py", "constructor_info": [{"class_name": "PromptManager", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}, {"class_name": "PromptTemplates", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}], "dependency_info": [{"class_name": "PromptManager", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "PromptTemplates", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/ai/mistral_service.py", "constructor_info": [{"class_name": "MistralService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "MistralService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/ai/batch_processor.py", "constructor_info": [{"class_name": "BatchProcessor", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "BatchProcessor", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/ai/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/fb_ads/logging_setup.py", "constructor_info": [{"class_name": "FBAdsLogger", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": [], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FBAdsLogger", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/ad_db_service.py", "constructor_info": [{"class_name": "AdDBService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "AdDBService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/ad_processing_service.py", "constructor_info": [{"class_name": "AdProcessingService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "AdProcessingService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/bandwidth_logger.py", "constructor_info": [{"class_name": "BandwidthLogger", "parameters": [{"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "logger", "type": "logging.Logger", "default": "None"}], "required_parameters": [], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "BandwidthLogger", "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}, {"name": "logger", "type": "logging.Logger", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/ner_rule_analyzer.py", "constructor_info": [{"class_name": "NerRuleAnalyzer", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "session", "type": "aiohttp.ClientSession", "default": null}, {"name": "ner_model_name", "type": "str", "default": "'en_core_web_trf'"}, {"name": "spacy_pipe_batch_size", "type": "int", "default": "64"}, {"name": "use_local_dynamodb", "type": "bool", "default": "False"}, {"name": "dynamodb_scan_workers", "type": "Optional[int]", "default": "None"}, {"name": "ner_processing_workers", "type": "Optional[int]", "default": "None"}], "required_parameters": ["config", "session"], "optional_parameters": ["ner_model_name", "spacy_pipe_batch_size", "use_local_dynamodb", "dynamodb_scan_workers", "ner_processing_workers"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "NerRuleAnalyzer", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/workflow_service.py", "constructor_info": [{"class_name": "WorkflowService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "WorkflowService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/processing_tracker.py", "constructor_info": [{"class_name": "ProcessingTracker", "parameters": [{"name": "file_path", "type": "str", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "logger", "type": "logging.Logger", "default": "None"}], "required_parameters": ["file_path"], "optional_parameters": ["config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ProcessingTracker", "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}, {"name": "logger", "type": "logging.Logger", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/local_image_queue.py", "constructor_info": [{"class_name": "LocalImageQueue", "parameters": [{"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}, {"name": "data_dir", "type": "str", "default": "'./data/image_queue'"}], "required_parameters": [], "optional_parameters": ["logger", "config", "data_dir"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "LocalImageQueue", "dependencies": [{"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}, {"name": "config", "type": "Dict[str, Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/disk_cache.py", "constructor_info": [{"class_name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "cache_file", "type": "Union[str, Path]", "default": null}, {"name": "max_memory_mb", "type": "int", "default": "1000"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "logger", "type": "logging.Logger", "default": "None"}], "required_parameters": ["cache_file"], "optional_parameters": ["max_memory_mb", "config", "logger"], "special_initialization_patterns": []}, {"class_name": "EmbeddingDiskCache", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "NERDiskCache", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}], "dependency_info": [{"class_name": "<PERSON><PERSON><PERSON><PERSON>", "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}, {"name": "logger", "type": "logging.Logger", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "EmbeddingDiskCache", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "NERDiskCache", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/interactive_service.py", "constructor_info": [{"class_name": "InteractiveService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "InteractiveService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/session_manager.py", "constructor_info": [{"class_name": "SSLAdapter", "parameters": [{"name": "ciphers", "type": "str", "default": "CIPHERS_SET_1"}], "required_parameters": [], "optional_parameters": ["ciphers"], "special_initialization_patterns": []}, {"class_name": "FacebookSessionManager", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "SSLAdapter", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "FacebookSessionManager", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/ad_ner_processor.py", "constructor_info": [{"class_name": "AdNerProcessor", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "session", "type": "aiohttp.ClientSession", "default": null}, {"name": "text_fields", "type": "List[str]", "default": "['Title', 'Body', 'Summary', 'ImageText']"}, {"name": "ner_model_name", "type": "str", "default": "'en_core_web_lg'"}, {"name": "spacy_pipe_batch_size", "type": "int", "default": "64"}, {"name": "use_local_dynamodb", "type": "bool", "default": "False"}, {"name": "dynamodb_scan_workers", "type": "Optional[int]", "default": "None"}, {"name": "ner_processing_workers", "type": "Optional[int]", "default": "None"}, {"name": "cluster_min_k", "type": "int", "default": "5"}, {"name": "cluster_max_k", "type": "int", "default": "50"}, {"name": "cluster_k_step", "type": "int", "default": "5"}, {"name": "cluster_output_enabled", "type": "bool", "default": "True"}], "required_parameters": ["config", "session"], "optional_parameters": ["text_fields", "ner_model_name", "spacy_pipe_batch_size", "use_local_dynamodb", "dynamodb_scan_workers", "ner_processing_workers", "cluster_min_k", "cluster_max_k", "cluster_k_step", "cluster_output_enabled"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "AdNerProcessor", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/data_validation_service.py", "constructor_info": [{"class_name": "DataValidationService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": null, "default": null}, {"name": "processing_tracker", "type": null, "default": null}], "required_parameters": ["config", "logger", "processing_tracker"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DataValidationService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": null, "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/failed_firms_manager.py", "constructor_info": [{"class_name": "FailedFirmsManager", "parameters": [{"name": "data_dir", "type": "str", "default": null}, {"name": "logger", "type": "LoggerProtocol", "default": null}], "required_parameters": ["data_dir", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FailedFirmsManager", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/classifier.py", "constructor_info": [{"class_name": "DecimalEncoder", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "LegalAdAnalyzer", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "base_path", "type": "str", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "base_path", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DecimalEncoder", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "LegalAdAnalyzer", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/orchestrator.py", "constructor_info": [{"class_name": "FacebookAdsOrchestrator", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "session", "type": "aiohttp.ClientSession", "default": null}], "required_parameters": ["config", "session"], "optional_parameters": [], "special_initialization_patterns": []}, {"class_name": "OpenAIWrapper", "parameters": [{"name": "api_key", "type": null, "default": null}], "required_parameters": ["api_key"], "optional_parameters": [], "special_initialization_patterns": []}, {"class_name": "DeepSeekWrapper", "parameters": [{"name": "api_key", "type": null, "default": null}, {"name": "logger", "type": null, "default": null}, {"name": "config", "type": null, "default": null}], "required_parameters": ["api_key", "logger", "config"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FacebookAdsOrchestrator", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "OpenAIWrapper", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "DeepSeekWrapper", "dependencies": [{"name": "logger", "type": null, "optional": false}, {"name": "config", "type": null, "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/processor.py", "constructor_info": [{"class_name": "AdProcessor", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "image_handler", "type": "ImageHandler", "default": null}, {"name": "ai_integrator", "type": "'AIOrchestrator'", "default": null}, {"name": "current_process_date", "type": "str", "default": null}, {"name": "fb_ad_db", "type": "Any", "default": "None"}, {"name": "s3_manager", "type": "Any", "default": "None"}, {"name": "logger", "type": "logging.Logger", "default": "None"}], "required_parameters": ["config", "image_handler", "ai_integrator", "current_process_date"], "optional_parameters": ["fb_ad_db", "s3_manager", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "AdProcessor", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/image_handler.py", "constructor_info": [{"class_name": "ImageHandler", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "s3_manager", "type": "S3AsyncStorage", "default": null}, {"name": "session_manager", "type": "FacebookSessionManager", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "s3_manager", "session_manager", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ImageHandler", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/error_handling_service.py", "constructor_info": [{"class_name": "ErrorHandlingService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}, {"name": "processing_tracker", "type": null, "default": null}], "required_parameters": ["config", "logger", "processing_tracker"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ErrorHandlingService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/concurrent_workflow_service.py", "constructor_info": [{"class_name": "ConcurrentWorkflowService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ConcurrentWorkflowService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/api_client.py", "constructor_info": [{"class_name": "FacebookAPIClient", "parameters": [{"name": "session_manager", "type": "'FacebookSessionManager'", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["session_manager", "config", "logger"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FacebookAPIClient", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/categorizer.py", "constructor_info": [{"class_name": "DeepSeekServiceUnavailableError", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "DeepSeekLLMResponseError", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "DeepSeekConfigurationError", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "DeepSeekFreeTierRateLimitError", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "GPTServiceUnavailableError", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "GPTRequestException", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "GPTRetryableError", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "FBAdCategorizer", "parameters": [{"name": "config", "type": null, "default": null}, {"name": "session", "type": "aiohttp.ClientSession", "default": null}, {"name": "deepseek_service", "type": "DeepSeekService", "default": null}, {"name": "use_local", "type": null, "default": "False"}, {"name": "scan_workers", "type": "Optional[int]", "default": "None"}, {"name": "update_workers", "type": "Optional[int]", "default": "None"}, {"name": "gpt_client", "type": "Optional[Any]", "default": "None"}], "required_parameters": ["config", "session", "deepseek_service"], "optional_parameters": ["use_local", "scan_workers", "update_workers", "gpt_client"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DeepSeekServiceUnavailableError", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "DeepSeekLLMResponseError", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "DeepSeekConfigurationError", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "DeepSeekFreeTierRateLimitError", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "GPTServiceUnavailableError", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "GPTRequestException", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "GPTRetryableError", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "FBAdCategorizer", "dependencies": [{"name": "config", "type": null, "optional": false}, {"name": "deepseek_service", "type": "DeepSeekService", "optional": false}, {"name": "gpt_client", "type": "Optional[Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/image_utils.py", "constructor_info": [{"class_name": "FBImageHashService", "parameters": [{"name": "repository", "type": "FBImageHashRepository", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["config"], "special_initialization_patterns": []}, {"class_name": "FBImageHashManager", "parameters": [{"name": "table_name", "type": "str", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}, {"name": "region_name", "type": "str", "default": "None"}, {"name": "use_local", "type": "bool", "default": "False"}, {"name": "local_port", "type": "int", "default": "8000"}, {"name": "remove_empty_str", "type": "bool", "default": "True"}], "required_parameters": ["table_name"], "optional_parameters": ["config", "region_name", "use_local", "local_port", "remove_empty_str"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "FBImageHashService", "dependencies": [{"name": "repository", "type": "FBImageHashRepository", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "FBImageHashManager", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/html/html_service_factory.py", "constructor_info": [{"class_name": "HtmlServiceFactory", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}], "dependency_info": [{"class_name": "HtmlServiceFactory", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/html/case_parser_service.py", "constructor_info": [{"class_name": "CaseParserService", "parameters": [{"name": "html_content", "type": "Optional[str]", "default": "None"}], "required_parameters": [], "optional_parameters": ["html_content"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "CaseParserService", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/html/data_updater_service.py", "constructor_info": [{"class_name": "DataUpdaterService", "parameters": [{"name": "config", "type": "Dict", "default": null}, {"name": "s3_manager", "type": "'S3AsyncStorage'", "default": null}, {"name": "pacer_db", "type": "'PacerRepository'", "default": null}], "required_parameters": ["config", "s3_manager", "pacer_db"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DataUpdaterService", "dependencies": [{"name": "config", "type": "Dict", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/html/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/law_firms/query_service.py", "constructor_info": [{"class_name": "LawFirmsQueryService", "parameters": [{"name": "repository", "type": "LawFirmsRepository", "default": null}], "required_parameters": ["repository"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "LawFirmsQueryService", "dependencies": [{"name": "repository", "type": "LawFirmsRepository", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer_dockets/query_service.py", "constructor_info": [{"class_name": "PacerDocketsQueryService", "parameters": [{"name": "repository", "type": "PacerDocketsRepository", "default": null}, {"name": "logger", "type": "Optional[LoggerProtocol]", "default": "None"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["repository"], "optional_parameters": ["logger", "config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerDocketsQueryService", "dependencies": [{"name": "repository", "type": "PacerDocketsRepository", "optional": false}, {"name": "logger", "type": "Optional[LoggerProtocol]", "optional": true}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/document/pdf_processor_service.py", "constructor_info": [{"class_name": "PdfProcessorService", "parameters": [{"name": "hugging_face_api_key", "type": "Optional[str]", "default": "None"}], "required_parameters": [], "optional_parameters": ["hugging_face_api_key"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PdfProcessorService", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/document/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/pacer/download_orchestration_service.py", "constructor_info": [{"class_name": "PacerDownloadOrchestrationService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "file_operations_service", "type": "Optional[PacerFileOperationsService]", "default": "None"}, {"name": "navigator", "type": "Optional[Any]", "default": "None"}, {"name": "navigation_service", "type": "Optional[Any]", "default": "None"}, {"name": "s3_manager", "type": "Optional[S3AsyncStorage]", "default": "None"}, {"name": "stability_config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "iso_date", "type": "Optional[str]", "default": "None"}, {"name": "file_management_service", "type": "Optional[Any]", "default": "None"}, {"name": "relevance_service", "type": "Optional[Any]", "default": "None"}, {"name": "pacer_repository", "type": "Optional[Any]", "default": "None"}, {"name": "deepseek_service", "type": "Optional[DeepSeekService]", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["file_operations_service", "navigator", "navigation_service", "s3_manager", "stability_config", "iso_date", "file_management_service", "relevance_service", "pacer_repository", "deepseek_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerDownloadOrchestrationService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "file_operations_service", "type": "Optional[PacerFileOperationsService]", "optional": true}, {"name": "navigation_service", "type": "Optional[Any]", "optional": true}, {"name": "stability_config", "type": "Optional[Dict[str, Any]]", "optional": true}, {"name": "file_management_service", "type": "Optional[Any]", "optional": true}, {"name": "relevance_service", "type": "Optional[Any]", "optional": true}, {"name": "pacer_repository", "type": "Optional[Any]", "optional": true}, {"name": "deepseek_service", "type": "Optional[DeepSeekService]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/case_processing_service.py", "constructor_info": [{"class_name": "PacerCaseProcessingService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "classification_service", "type": null, "default": "None"}, {"name": "html_processing_service", "type": null, "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["classification_service", "html_processing_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerCaseProcessingService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "classification_service", "type": null, "optional": true}, {"name": "html_processing_service", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/analytics_service.py", "constructor_info": [{"class_name": "PacerAnalyticsService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "repository", "type": "PacerRepository", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": ["logger", "repository"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerAnalyticsService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "repository", "type": "PacerRepository", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/export_service.py", "constructor_info": [{"class_name": "PacerExportService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}], "required_parameters": ["logger"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerExportService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/query_service.py", "constructor_info": [{"class_name": "PacerQueryService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "repository", "type": "PacerRepository", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": ["logger", "repository"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerQueryService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "repository", "type": "PacerRepository", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/navigation_service.py", "constructor_info": [{"class_name": "PacerNavigationService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "html_processing_service", "type": null, "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["html_processing_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerNavigationService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "html_processing_service", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/authentication_service.py", "constructor_info": [{"class_name": "PacerAuthenticationService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}], "required_parameters": ["logger", "config"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerAuthenticationService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/file_operations_service.py", "constructor_info": [{"class_name": "PacerFileOperationsService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "default": "None"}], "required_parameters": ["config"], "optional_parameters": ["s3_storage"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerFileOperationsService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/court_processing_service.py", "constructor_info": [{"class_name": "PacerCourtProcessingService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "deepseek_service", "type": null, "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["deepseek_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerCourtProcessingService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "deepseek_service", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/interactive_service.py", "constructor_info": [{"class_name": "PacerInteractiveService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "query_service", "type": "PacerQueryService", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": "None"}], "required_parameters": ["logger", "query_service"], "optional_parameters": ["config"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerInteractiveService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "query_service", "type": "PacerQueryService", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/case_verification_service.py", "constructor_info": [{"class_name": "PacerCaseVerificationService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "default": "None"}, {"name": "file_manager", "type": "Optional[PacerFileManagementService]", "default": "None"}, {"name": "end_date_obj", "type": "Optional[datetime]", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["pacer_repository", "file_manager", "end_date_obj"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerCaseVerificationService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/html_processing_service.py", "constructor_info": [{"class_name": "PacerHTMLProcessingService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "Optional[str]", "default": "None"}, {"name": "html_data_updater", "type": "Optional[DataUpdaterService]", "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["court_id", "html_data_updater"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerHTMLProcessingService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/configuration_service.py", "constructor_info": [{"class_name": "PacerConfigurationService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "config_dir", "type": "Path", "default": "DEFAULT_CONFIG_DIR"}], "required_parameters": ["logger", "config"], "optional_parameters": ["config_dir"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerConfigurationService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "config_dir", "type": "Path", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/report_service.py", "constructor_info": [{"class_name": "ReportService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "navigator", "type": "PacerNavigator", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "from_date_str", "type": "str", "default": null}, {"name": "to_date_str", "type": "str", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "iso_date", "type": "str", "default": "None"}, {"name": "ignore_download_service", "type": "Optional[Any]", "default": "None"}], "required_parameters": ["logger", "navigator", "court_id", "from_date_str", "to_date_str", "config"], "optional_parameters": ["iso_date", "ignore_download_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "ReportService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "ignore_download_service", "type": "Optional[Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/ignore_download_service.py", "constructor_info": [{"class_name": "PacerIgnoreDownloadService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}], "required_parameters": ["logger", "config"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerIgnoreDownloadService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/pacer_orchestrator_service.py", "constructor_info": [{"class_name": "PacerOrchestratorService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "service_factory", "type": "Optional[PacerServiceFactory]", "default": "None"}, {"name": "deepseek_service", "type": "Optional[Any]", "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["service_factory", "deepseek_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerOrchestratorService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "service_factory", "type": "Optional[PacerServiceFactory]", "optional": true}, {"name": "deepseek_service", "type": "Optional[Any]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/row_processing_service.py", "constructor_info": [{"class_name": "DocketProcessingJob", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}, {"class_name": "PacerRowProcessingService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "deepseek_service", "type": null, "default": "None"}], "required_parameters": ["logger", "config"], "optional_parameters": ["deepseek_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "DocketProcessingJob", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "PacerRowProcessingService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "deepseek_service", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/case_classification_service.py", "constructor_info": [{"class_name": "PacerCaseClassificationService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "transfer_handler", "type": "Optional[TransferHandler]", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["transfer_handler"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerCaseClassificationService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/file_management_service.py", "constructor_info": [{"class_name": "PacerFileManagementService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}], "required_parameters": ["logger", "config"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerFileManagementService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/transfer_service.py", "constructor_info": [{"class_name": "PacerTransferService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "default": "None"}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "default": "None"}, {"name": "gpt_interface", "type": "Optional[GPT4]", "default": "None"}, {"name": "court_lookup", "type": "Optional[Dict[str, str]]", "default": "None"}, {"name": "district_court_repository", "type": "Optional[DistrictCourtsRepository]", "default": "None"}], "required_parameters": ["logger", "config", "court_id"], "optional_parameters": ["pacer_repository", "s3_storage", "gpt_interface", "court_lookup", "district_court_repository"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerTransferService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "optional": true}, {"name": "district_court_repository", "type": "Optional[DistrictCourtsRepository]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/service_factory.py", "constructor_info": [{"class_name": "PacerServiceFactory", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}], "required_parameters": ["logger", "config"], "optional_parameters": [], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerServiceFactory", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/relevance_service.py", "constructor_info": [{"class_name": "RelevanceService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "relevance_config_dict", "type": "Dict[str, Any]", "default": null}, {"name": "relevant_defendants_list", "type": "List[str]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "usa_defendant_regex_str", "type": "Optional[str]", "default": "None"}], "required_parameters": ["logger", "config", "relevance_config_dict", "relevant_defendants_list", "court_id"], "optional_parameters": ["usa_defendant_regex_str"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "RelevanceService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "relevance_config_dict", "type": "Dict[str, Any]", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/docket_processing_orchestrator_service.py", "constructor_info": [{"class_name": "PacerDocketProcessingOrchestratorService", "parameters": [{"name": "logger", "type": "LoggerProtocol", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "court_id", "type": "str", "default": null}, {"name": "iso_date", "type": "str", "default": null}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "default": "None"}, {"name": "file_manager", "type": "Optional[PacerFileManagementService]", "default": "None"}, {"name": "transfer_handler", "type": "Optional[TransferHandler]", "default": "None"}, {"name": "navigator", "type": "Optional[PacerNavigationService]", "default": "None"}, {"name": "s3_storage", "type": "Optional[S3AsyncStorage]", "default": "None"}, {"name": "end_date_obj", "type": "Optional[datetime]", "default": "None"}, {"name": "deepseek_service", "type": null, "default": "None"}], "required_parameters": ["logger", "config", "court_id", "iso_date"], "optional_parameters": ["pacer_repository", "file_manager", "transfer_handler", "navigator", "s3_storage", "end_date_obj", "deepseek_service"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerDocketProcessingOrchestratorService", "dependencies": [{"name": "logger", "type": "LoggerProtocol", "optional": false}, {"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "pacer_repository", "type": "Optional[PacerRepository]", "optional": true}, {"name": "deepseek_service", "type": null, "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/reports/config/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}, {"file_path": "src/services/fb_ads/jobs/job_runner_service.py", "constructor_info": [{"class_name": "JobRunnerService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}], "required_parameters": ["config", "logger"], "optional_parameters": [], "special_initialization_patterns": []}, {"class_name": "AdProcessorUtils", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}], "dependency_info": [{"class_name": "JobRunnerService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}, {"class_name": "AdProcessorUtils", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/jobs/job_models.py", "constructor_info": [{"class_name": "ProcessFirmJob", "parameters": [], "required_parameters": [], "optional_parameters": [], "special_initialization_patterns": ["No __init__ method found, uses default constructor or inherits."]}], "dependency_info": [{"class_name": "ProcessFirmJob", "dependencies": [], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/fb_ads/jobs/job_orchestration_service.py", "constructor_info": [{"class_name": "JobOrchestrationService", "parameters": [{"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "job_runner_service", "type": "JobRunnerService", "default": null}, {"name": "logger", "type": "logging.Logger", "default": null}, {"name": "failed_firms_manager", "type": "Optional[FailedFirmsManager]", "default": "None"}], "required_parameters": ["config", "job_runner_service", "logger"], "optional_parameters": ["failed_firms_manager"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "JobOrchestrationService", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "job_runner_service", "type": "JobRunnerService", "optional": false}, {"name": "logger", "type": "logging.Logger", "optional": false}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/browser/browser_service.py", "constructor_info": [{"class_name": "BrowserService", "parameters": [{"name": "headless", "type": "bool", "default": "True"}, {"name": "timeout_ms", "type": "int", "default": "60000"}, {"name": "config", "type": "Optional[Dict[str, Any]]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": [], "optional_parameters": ["headless", "timeout_ms", "config", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "BrowserService", "dependencies": [{"name": "config", "type": "Optional[Dict[str, Any]]", "optional": true}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/browser/navigator.py", "constructor_info": [{"class_name": "PacerNavigator", "parameters": [{"name": "page", "type": "Page", "default": null}, {"name": "config", "type": "Dict[str, Any]", "default": null}, {"name": "screenshot_dir", "type": "str", "default": null}, {"name": "timeout_ms", "type": "Optional[int]", "default": "None"}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "default": "None"}], "required_parameters": ["page", "config", "screenshot_dir"], "optional_parameters": ["timeout_ms", "logger"], "special_initialization_patterns": []}], "dependency_info": [{"class_name": "PacerNavigator", "dependencies": [{"name": "config", "type": "Dict[str, Any]", "optional": false}, {"name": "logger", "type": "Optional[<PERSON>.Logger]", "optional": true}], "factory_methods_dependencies": [], "lazy_loaded_dependencies": [], "circular_dependency_risks": []}], "errors": []}, {"file_path": "src/services/pacer/browser/__init__.py", "constructor_info": [], "dependency_info": [], "errors": []}]