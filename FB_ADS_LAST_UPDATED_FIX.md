# Facebook Ads last_updated and end_date Fix

## Issue
The code was not properly updating the `last_updated` and `end_date` fields when saving ads to the database:
- `last_updated` was using `datetime.now()` instead of the iso_date from config
- `end_date` was only being updated for active ads, not all ads

## Requirements
- `last_updated` should ALWAYS be set to the current iso_date from config (processing_date)
- `end_date` should be set to the greater of:
  - The end_date from the JSON response
  - The existing end_date in the database (for updates)
  - The current iso_date (processing_date)

## Fix Applied

### File: `/src/services/fb_ads/ad_db_service.py`

1. **Fixed last_updated to use processing_date** (line 232):
```python
# BEFORE:
item_to_write["last_updated"] = datetime.now().strftime("%Y%m%d")

# AFTER:
item_to_write["last_updated"] = processing_date
```

2. **Fixed end_date logic for existing ads** (lines 238-257):
```python
# BEFORE: Only updated end_date for active ads
if is_active_new and processing_date:
    # ... update logic only for active ads

# AFTER: Always update end_date to maximum of all sources
if processing_date:
    final_end_date = processing_date
    
    if existing_end_date:
        final_end_date = max(final_end_date, existing_end_date)
    
    if new_end_date:
        final_end_date = max(final_end_date, new_end_date)
    
    item_to_write["end_date"] = final_end_date
```

3. **Fixed end_date logic for new ads** (lines 275-282):
```python
# BEFORE: Only for active ads
if is_active_new and processing_date:
    # ... update logic

# AFTER: For all new ads
if processing_date:
    if not current_end_date:
        item_to_write["end_date"] = processing_date
    else:
        item_to_write["end_date"] = max(current_end_date, processing_date)
```

## Data Flow
1. Config contains `iso_date` (e.g., "20250718")
2. WorkflowService reads `iso_date` from config
3. Job orchestration receives it as `current_process_date`
4. ProcessFirmJob stores it
5. JobRunnerService passes it as `processing_date` to `batch_save_processed_ads`
6. `batch_save_processed_ads` now correctly uses it for both `last_updated` and `end_date`

## Result
- All ads (new and existing) will have `last_updated` set to the config's iso_date
- All ads will have `end_date` set to the maximum of (json end_date, existing end_date, iso_date)
- This ensures proper tracking of when ads were last processed and their active date ranges