#!/usr/bin/env python3
"""
Debug script to test Facebook ads retrieval for a specific page ID.
This script helps diagnose why some pages show zero ads when they should have valid ads.
"""

import asyncio
import json
import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'debug_page_ads_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# Add project root to path
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.fb_ads.api_client import FacebookAPIClient
from src.services.fb_ads.session_manager import FacebookSessionManager
from src.services.fb_ads.camoufox.camoufox_session_manager import CamoufoxSessionManager
from src.services.fb_ads.graphql_response_parser import GraphQLResponseParser
from src.services.fb_ads.jobs.job_runner_service import JobRunnerService
from src.services.fb_ads.jobs.job_models import ProcessFirmJob
from src.repositories.law_firms_repository import LawFirmsRepository
from src.infrastructure.storage.dynamodb_async import AsyncDynamoDBStorage
from src.services.scraping.proxy.proxy_manager import ProxyManager
from src.services.scraping.browser.fingerprint_manager import FingerprintManager
import yaml
import uuid


async def test_page_ads(page_id: str, use_camoufox: bool = True):
    """Test ads retrieval for a specific page ID."""
    # Clean up the input - remove quotes if present
    page_id = page_id.strip().strip('"').strip("'")
    
    logger.info(f"=" * 80)
    logger.info(f"Testing page ID: {page_id}")
    logger.info(f"Using {'Camoufox (GraphQL)' if use_camoufox else 'Standard (Legacy API)'} session manager")
    logger.info(f"=" * 80)
    
    # Load config from YAML file
    config_path = "config/workflows/fb_ads.yml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Ensure proxy is enabled
    config['use_proxy'] = True
    config['mobile_proxy'] = True
    
    # Set proxy credentials - ProxyManager expects these at the top level of config
    config['oxy_labs_mobile_username'] = os.getenv('OXY_LABS_MOBILE_USERNAME', 'lexgeniusmob20250612_7jRlZ')
    config['oxy_labs_mobile_password'] = os.getenv('OXY_LABS_MOBILE_PASSWORD', '24gbfygq=RBhM2+')
    config['oxy_labs_residential_username'] = os.getenv('OXY_LABS_RESIDENTIAL_USERNAME', 'lexgenius20250612_wzykM')
    config['oxy_labs_residential_password'] = os.getenv('OXY_LABS_RESIDENTIAL_PASSWORD', '24gbfygq=RBhM2+')
    
    # Add required fields if missing
    if 'aws_region' not in config:
        config['aws_region'] = os.getenv('AWS_REGION', 'us-west-2')
    if 'iso_date' not in config:
        config['iso_date'] = datetime.now().strftime("%Y%m%d")
    
    # Enable GraphQL capture for Camoufox
    config['use_graphql_capture'] = use_camoufox
    if 'feature_flags' not in config:
        config['feature_flags'] = {}
    config['feature_flags']['graphql_enabled'] = use_camoufox
    config['feature_flags']['use_camoufox'] = use_camoufox
    
    # Calculate date range (14 days)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=14)
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    
    logger.info(f"Date range: {start_date_str} to {end_date_str}")
    
    # First, let's check if this page_id exists in the law_firms table
    logger.info(f"\n{'='*60}")
    logger.info("STEP 0: Checking law_firms database for page_id...")
    logger.info(f"{'='*60}")
    
    law_firms_repo = None
    page_name = None
    
    try:
        # Create law firms repository to look up the page name
        # AsyncDynamoDBStorage doesn't take table_name in constructor - it uses config
        dynamodb_config = config.copy()
        dynamodb_config['table_name'] = 'LawFirms'
        
        dynamodb_storage = AsyncDynamoDBStorage(
            config=dynamodb_config,
            logger=logger
        )
        law_firms_repo = LawFirmsRepository(dynamodb_storage)
        
        # Look up the page_id
        results = await law_firms_repo.get_by_id(page_id)
        if results and len(results) > 0:
            firm_data = results[0]
            logger.info(f"✅ Found law firm in database!")
            logger.info(f"  - ID: {firm_data.get('id', 'N/A')}")
            logger.info(f"  - Name: {firm_data.get('name', 'N/A')}")
            logger.info(f"  - Page Alias: {firm_data.get('page_alias', 'N/A')}")
            logger.info(f"  - All fields: {list(firm_data.keys())}")
            
            # The page name to use for search
            page_name = firm_data.get('page_alias') or firm_data.get('name')
            logger.info(f"  - Will search for: '{page_name}'")
        else:
            logger.warning(f"❌ Page ID {page_id} NOT found in law_firms database!")
            logger.warning("This is likely why the scraper cannot find ads - the page_id is not registered.")
            logger.warning("You may need to add this firm to the law_firms table first.")
            
            # Try hardcoded mapping as fallback
            hardcoded_mappings = {
                "344397646746": "UPDATE_ME_WITH_ACTUAL_PAGE_NAME",  # TODO: Replace with actual page name from database
                "295549338384": "Wisner Baum",
                "168495366537322": "Morgan & Morgan",
                "298057500050247": "Jennings & Earley",  # Add the known mapping
                # Add your specific page_id mapping here
            }
            
            if page_id in hardcoded_mappings:
                page_name = hardcoded_mappings[page_id]
                logger.info(f"📋 Using hardcoded mapping: '{page_name}' for ID: {page_id}")
            else:
                # For testing, let's use the page_id itself as fallback
                page_name = page_id
                logger.warning(f"No hardcoded mapping found. Using page_id '{page_id}' as search term.")
    except Exception as e:
        logger.error(f"Error checking law_firms database: {e}")
    
    session_manager = None
    api_client = None
    
    try:
        # Create proxy and fingerprint managers first
        logger.info("Creating proxy and fingerprint managers...")
        logger.info(f"Proxy config - use_proxy: {config.get('use_proxy')}, mobile_proxy: {config.get('mobile_proxy')}")
        logger.info(f"Proxy password configured: {'Yes' if config.get('proxies', {}).get('mobile_proxy_password') else 'No'}")
        
        proxy_manager = ProxyManager(config, logger)
        fingerprint_manager = FingerprintManager(config, logger)
        
        # Create session manager
        if use_camoufox:
            logger.info("Creating Camoufox session manager...")
            # Pass ALL required dependencies including law_firms_repo
            session_manager = CamoufoxSessionManager(
                config=config, 
                logger=logger, 
                fingerprint_manager=fingerprint_manager,
                proxy_manager=proxy_manager,
                law_firms_repository=law_firms_repo
            )
            # Initialize browser using public method
            success = await session_manager.create_new_session()
            if not success:
                logger.error("Failed to create Camoufox session")
                return
            
            # Take a screenshot to see the page state
            logger.info("Taking initial screenshot...")
            await session_manager.page.screenshot(path=f"debug_initial_{page_id}.png")
            logger.info(f"Screenshot saved as debug_initial_{page_id}.png")
        else:
            logger.info("Creating standard session manager...")
            session_manager = FacebookSessionManager(config, logger)
            # Create session
            success = await session_manager.create_new_session()
            if not success:
                logger.error("Failed to create session")
                return
        
        # Create API client with correct parameter order
        logger.info("Creating API client...")
        api_client = FacebookAPIClient(session_manager, config, logger)
        
        # If using Camoufox, test GraphQL capture directly
        if use_camoufox:
            logger.info(f"\n{'='*60}")
            logger.info("CAMOUFOX TEST: Testing GraphQL capture with JobRunnerService...")
            logger.info(f"{'='*60}")
            
            # Create a test job
            test_job = ProcessFirmJob(
                firm_id=page_id,
                firm_name=f"Test Page {page_id}",
                firm_data={'ID': page_id, 'Name': f"Test Page {page_id}"},
                config_snapshot=config,
                current_process_date=config['iso_date']
            )
            
            # Create JobRunnerService with GraphQL parser
            graphql_parser = GraphQLResponseParser(logger, config)
            job_runner = JobRunnerService(
                logger=logger,
                config=config,
                graphql_parser=graphql_parser
            )
            
            # Test GraphQL capture
            logger.info("Testing _should_use_graphql...")
            should_use_graphql = job_runner._should_use_graphql(session_manager)
            logger.info(f"Should use GraphQL: {should_use_graphql}")
            
            if should_use_graphql:
                logger.info("\nAttempting GraphQL capture...")
                try:
                    # Use the fetch_ads_via_graphql method
                    await job_runner._fetch_ads_via_graphql(test_job, session_manager, logger)
                except Exception as e:
                    logger.error(f"Error during GraphQL capture: {e}", exc_info=True)
                
                # Check results
                logger.info(f"\nGraphQL capture results:")
                logger.info(f"- Status: {test_job.status}")
                logger.info(f"- Raw ad groups: {len(test_job.raw_ad_groups) if test_job.raw_ad_groups else 0}")
                logger.info(f"- Ads fetched: {test_job.metrics.get('ads_fetched', 0)}")
                logger.info(f"- GraphQL responses: {test_job.metrics.get('graphql_responses_processed', 0)}")
                logger.info(f"- Errors: {test_job.error_message or 'None'}")
                
                if test_job.raw_ad_groups:
                    logger.info(f"\nFirst ad group sample:")
                    first_group = test_job.raw_ad_groups[0]
                    if first_group:
                        first_ad = first_group[0]
                        logger.info(f"Ad ID: {first_ad.get('adArchiveID', 'Unknown')}")
                        logger.info(f"Page ID: {first_ad.get('pageID', 'Unknown')}")
                        logger.info(f"Page Name: {first_ad.get('pageName', 'Unknown')}")
                else:
                    logger.warning("No raw ad groups captured")
                    
                    # Check GraphQL responses directly
                    if hasattr(session_manager, '_graphql_responses'):
                        responses = session_manager._graphql_responses
                        logger.info(f"\nDirect GraphQL responses: {len(responses)}")
                        for i, resp in enumerate(responses):
                            logger.info(f"Response {i+1} length: {len(resp)} chars")
                            logger.info(f"Response {i+1} preview: {resp[:200]}...")
            
            return
        
        # Regular flow for non-Camoufox
        logger.info(f"\n{'='*60}")
        logger.info("STEP 1: Getting page information...")
        logger.info(f"{'='*60}")
        
        # Try direct GraphQL query for page info
        page_info_payload = {
            "doc_id": "7057613277600026",  # Page info query
            "variables": json.dumps({
                "pageID": page_id
            })
        }
        
        try:
            # Use _make_request for GraphQL
            page_response = await api_client._make_request(
                url=api_client.GRAPHQL_URL,
                method="POST",
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Accept": "*/*"
                },
                data=page_info_payload
            )
            if page_response:
                logger.info(f"Page response received: {json.dumps(page_response, indent=2)[:500]}...")
                
                # Extract page name
                page_name = "Unknown"
                if 'data' in page_response and 'page' in page_response['data']:
                    page_data = page_response['data']['page']
                    page_name = page_data.get('name', 'Unknown')
                    logger.info(f"Page name: {page_name}")
        except Exception as e:
            logger.error(f"Error getting page info: {e}")
        
        # Test ads retrieval
        logger.info(f"\n{'='*60}")
        logger.info("STEP 2: Testing ads retrieval...")
        logger.info(f"{'='*60}")
        
        # Build the full payload
        test_payload = {
            "startDate": start_date_str,
            "endDate": end_date_str,
            "adType": "POLITICAL_AND_ISSUE_ADS",
            "bylines": [page_id],
            "collationToken": None,
            "count": 25,
            "countries": ["US"],
            "mediaType": "ALL",
            "pageIDs": [page_id],
            "potentialReach": None,
            "publishers": None,
            "regions": None,
            "searchTerm": None,
            "sessionID": str(uuid.uuid4()),  # Generate a new session ID
            "source": None,
            "v": "__RELAY_INTERNAL__"
        }
        
        logger.info(f"Payload: {json.dumps(test_payload, indent=2)}")
        
        # Make the request using the correct method
        logger.info("Calling fetch_ads_page...")
        raw_response = await api_client.fetch_ads_page(
            company_id=page_id,
            start_date=start_date_str,
            end_date=end_date_str,
            forward_cursor=None,
            backward_cursor=None
        )
        
        if not raw_response:
            logger.error("No response received from API")
            return
        
        # Log raw response
        logger.info(f"\n{'='*60}")
        logger.info("STEP 3: Raw response analysis...")
        logger.info(f"{'='*60}")
        logger.info(f"Response type: {type(raw_response)}")
        
        # Handle different response types
        if isinstance(raw_response, tuple) and len(raw_response) == 2:
            # fetch_ads_page returns (ads_list, has_next_page)
            ads_list, has_next_page = raw_response
            logger.info(f"Response is a tuple: ({len(ads_list) if ads_list else 0} ads, has_next={has_next_page})")
            raw_response = {"ads": ads_list, "has_next_page": has_next_page}
        elif isinstance(raw_response, dict):
            logger.info(f"Response keys: {list(raw_response.keys())}")
        else:
            logger.warning(f"Unexpected response type: {type(raw_response)}")
        
        # Save raw response for analysis
        with open(f'debug_page_{page_id}_raw_response.json', 'w') as f:
            json.dump(raw_response, f, indent=2)
        logger.info(f"Raw response saved to debug_page_{page_id}_raw_response.json")
        
        # Parse response
        logger.info(f"\n{'='*60}")
        logger.info("STEP 4: Parsing response...")
        logger.info(f"{'='*60}")
        
        parser = GraphQLResponseParser(logger, config)  # Note: logger comes first
        
        # Try to parse as JSON response (not NDJSON)
        if isinstance(raw_response, dict):
            logger.info("Parsing as regular JSON response...")
            parsed_data = await parser.parse_json_response(raw_response)
        else:
            logger.error(f"Unexpected response type: {type(raw_response)}")
            parsed_data = None
        
        if parsed_data:
            logger.info(f"Parsed data keys: {list(parsed_data.keys())}")
            logger.info(f"Number of ads: {len(parsed_data.get('ads', []))}")
            logger.info(f"Has more pages: {parsed_data.get('has_next_page', False)}")
            logger.info(f"Total count: {parsed_data.get('total_count', 'Unknown')}")
            
            # Log first few ads
            ads = parsed_data.get('ads', [])
            if ads:
                logger.info(f"\nFirst ad details:")
                logger.info(json.dumps(ads[0], indent=2)[:1000])
            else:
                logger.info("\nNo ads found in parsed response")
                
                # Check response structure
                if 'data' in raw_response:
                    logger.info("\nChecking response data structure...")
                    data = raw_response['data']
                    logger.info(f"Data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                    
                    # Look for ad library results
                    if 'ad_library_main' in data:
                        ad_lib = data['ad_library_main']
                        logger.info(f"ad_library_main keys: {list(ad_lib.keys()) if isinstance(ad_lib, dict) else 'Not a dict'}")
                        
                        if 'search_results' in ad_lib:
                            search_results = ad_lib['search_results']
                            logger.info(f"search_results type: {type(search_results)}")
                            logger.info(f"search_results: {json.dumps(search_results, indent=2)[:500]}")
        else:
            logger.error("Failed to parse response")
        
        # Test alternative queries
        logger.info(f"\n{'='*60}")
        logger.info("STEP 5: Testing alternative query approaches...")
        logger.info(f"{'='*60}")
        
        # Try alternative queries with different doc_ids and parameters
        logger.info(f"\n{'='*60}")
        logger.info("STEP 5: Testing alternative approaches...")
        logger.info(f"{'='*60}")
        
        # First, let's check if the page exists and has ads using the browser directly
        logger.info("\nChecking Facebook Ad Library URL directly...")
        ad_library_url = f"https://www.facebook.com/ads/library/?active_status=all&ad_type=all&country=US&view_all_page_id={page_id}"
        logger.info(f"Ad Library URL: {ad_library_url}")
        logger.info("If this page has ads, they should be visible at this URL.")
        
        # Test different doc_ids that might work
        doc_ids = [
            "8190055421000919",  # Standard ad library query
            "5341536822619642",  # Alternative doc_id
            "7959648184061220",  # Another alternative
        ]
        
        # Alternative parameters
        alt_params = [
            {"adType": "ALL", "mediaType": "ALL"},
            {"adType": "POLITICAL_AND_ISSUE_ADS", "mediaType": "ALL"},
            {"adType": "ALL", "mediaType": "IMAGE"},
            {"ad_type": "all", "active_status": "all"},  # Different parameter names
        ]
        
        for doc_id in doc_ids:
            logger.info(f"\nTesting doc_id: {doc_id}")
            
            for i, params in enumerate(alt_params):
                logger.info(f"  Parameters: {params}")
                test_payload.update(params)
                
                try:
                    # Try using _make_request instead
                    alt_response = await api_client._make_request(
                        url=api_client.GRAPHQL_URL,
                        method="POST",
                        headers={
                            "Content-Type": "application/x-www-form-urlencoded",
                            "Accept": "*/*"
                        },
                        data={
                            "doc_id": doc_id,
                            "variables": json.dumps(test_payload)
                        }
                    )
                
                    if alt_response and 'data' in alt_response:
                        data = alt_response['data']
                        if 'ad_library_main' in data:
                            ad_lib = data['ad_library_main']
                            if 'search_results_connection' in ad_lib:
                                # Newer format
                                results = ad_lib['search_results_connection']
                                edges = results.get('edges', [])
                            elif 'search_results' in ad_lib:
                                # Older format
                                results = ad_lib['search_results']
                                edges = results.get('edges', []) if isinstance(results, dict) else []
                            else:
                                edges = []
                                logger.warning(f"    No search_results found in ad_library_main")
                            
                            logger.info(f"    Found {len(edges)} edges")
                            
                            # Count total ads (including collated)
                            total_ads = 0
                            for edge in edges:
                                collated = edge.get('node', {}).get('collated_results', [])
                                total_ads += len(collated)
                            
                            logger.info(f"    Total ads (including collated): {total_ads}")
                            
                            if edges:
                                logger.info(f"    ✅ SUCCESS! Found ads with doc_id={doc_id}, params={params}")
                                with open(f'debug_page_{page_id}_success_{doc_id}.json', 'w') as f:
                                    json.dump(alt_response, f, indent=2)
                                return  # Exit on success
                        else:
                            logger.warning(f"    Response has no 'data' field")
                    else:
                        logger.warning(f"    No response received")
                except Exception as e:
                    logger.error(f"    Error with doc_id {doc_id}: {e}")
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)
    finally:
        # Cleanup
        if session_manager:
            if hasattr(session_manager, 'cleanup'):
                await session_manager.cleanup()
            logger.info("Session manager cleaned up")


async def verify_page_has_ads(page_id: str):
    """Quick verification that a page actually has ads in Facebook Ad Library."""
    logger.info(f"\n{'='*60}")
    logger.info(f"QUICK CHECK: Verifying page {page_id} has ads...")
    logger.info(f"{'='*60}")
    
    # The URL to check manually
    ad_library_url = f"https://www.facebook.com/ads/library/?active_status=all&ad_type=all&country=US&view_all_page_id={page_id}"
    logger.info(f"\nTo manually verify this page has ads, visit:")
    logger.info(f"{ad_library_url}")
    logger.info("\nThis script will now attempt to fetch these ads programmatically.")
    
    # Also provide direct GraphQL test URL
    logger.info(f"\nFor debugging, you can also check the GraphQL endpoint directly:")
    logger.info(f"Open browser dev tools, go to Network tab, then visit the Ad Library URL")
    logger.info(f"Look for requests to 'graphql' containing 'ad_library_main'")
    
    return True


async def main():
    """Main function to run the debug script."""
    # Get page ID from command line or use default
    if len(sys.argv) > 1:
        page_id = sys.argv[1]
    else:
        # Default test page
        page_id = "313942829424"  # Morgan & Morgan page ID that should have ads
        logger.info(f"No page ID provided, using default: {page_id}")
    
    # Quick verification
    await verify_page_has_ads(page_id)
    
    # Test with Camoufox session manager (GraphQL) ONLY
    logger.info("\n" + "="*80)
    logger.info("CAMOUFOX GraphQL TEST - NO LEGACY METHOD")
    logger.info("="*80)
    try:
        await test_page_ads(page_id, use_camoufox=True)
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
    
    # Summary and recommendations
    logger.info("\n" + "="*80)
    logger.info("SUMMARY AND RECOMMENDATIONS")
    logger.info("="*80)
    logger.info("\nTo troubleshoot why ads are not being received:")
    logger.info("1. Check if the page_id exists in the law_firms database (see STEP 0 above)")
    logger.info("2. If not in database, you need to:")
    logger.info("   a) Add the firm to the law_firms table with correct ID and Name")
    logger.info("   b) OR update the hardcoded_mappings in this script with the page name")
    logger.info("3. The Facebook Ad Library search uses the page NAME (not ID) in the dropdown")
    logger.info("4. Verify the page has ads by visiting the Ad Library URL shown above")
    logger.info("5. Check the debug log files created by this script")
    logger.info("6. If using Camoufox/GraphQL:")
    logger.info("   - The page name must match exactly what appears in Facebook's dropdown")
    logger.info("   - Look for GraphQL response interception messages in logs")
    logger.info("   - Check if responses contain 'collated_results' with multiple ads")
    logger.info("7. If using legacy API:")
    logger.info("   - Check if different doc_ids or parameters work")
    logger.info("   - Look for rate limiting or authentication issues")
    logger.info("\nKey files to check:")
    logger.info(f"- debug_page_{page_id}_raw_response.json")
    logger.info(f"- debug_page_{page_id}_success_*.json")
    logger.info(f"- debug_page_ads_*.log")
    
    logger.info("\n" + "="*80)
    logger.info("Debug script completed")
    logger.info("="*80)


if __name__ == "__main__":
    asyncio.run(main())