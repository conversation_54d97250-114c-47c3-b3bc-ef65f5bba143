#!/usr/bin/env python3
"""
Debug script to trace exactly what happens in the transfer handler.
"""
import asyncio
import json
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def debug_transfer_handler():
    """Debug the transfer handler step by step."""
    
    # Load the test data
    json_path = "/Users/<USER>/PycharmProjects/lexgenius/data/20250716/dockets/scd_25_07390_Armstrong_et_al_v_3M_Company_et_al.json"
    with open(json_path, 'r') as f:
        test_data = json.load(f)
    
    print("=" * 80)
    print("Debug Transfer Handler")
    print("=" * 80)
    
    print("\nTest data:")
    print(f"  court_id: {test_data.get('court_id')}")
    print(f"  case_in_other_court: {test_data.get('case_in_other_court')}")
    print(f"  lead_case: {test_data.get('lead_case')}")
    print(f"  mdl_num: {test_data.get('mdl_num', 'NOT PRESENT')}")
    
    # Try to import and create the transfer handler
    try:
        from src.services.transformer.transfer_handler import TransferHandler
        from src.services.transformer.mdl_processor import MDLProcessor
        from src.repositories.pacer_repository import PacerRepository
        from src.repositories.district_courts_repository import DistrictCourtsRepository
        
        print("\n✅ Successfully imported TransferHandler and dependencies")
        
        # Setup logging to see what's happening
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        logger = logging.getLogger('debug_transfer')
        
        # Create mock repositories and dependencies
        config = {
            'aws_region': 'us-east-1',
            'dynamodb': {'enabled': False}
        }
        
        # Create a minimal MDL processor for testing
        mdl_processor = MDLProcessor(config=config, logger=logger)
        
        # Create minimal repositories (these won't actually connect to DB)
        pacer_repo = PacerRepository(config=config, logger=logger)
        district_courts_repo = DistrictCourtsRepository(config=config, logger=logger)
        
        # Create the transfer handler
        transfer_handler = TransferHandler(
            config=config,
            pacer_db=pacer_repo,
            district_court_db=district_courts_repo,
            mdl_processor=mdl_processor,
            logger=logger
        )
        
        print("✅ Successfully created TransferHandler instance")
        
        # Test the transfer processing
        print("\n" + "-" * 80)
        print("Testing transfer processing...")
        print("-" * 80)
        
        # Make a copy of the test data to avoid modifying the original
        test_data_copy = test_data.copy()
        
        # Call the transfer handler
        await transfer_handler.execute_action({
            'action': 'process_transfers',
            'data': {
                'docket_data': test_data_copy
            }
        })
        
        print("\n" + "-" * 80)
        print("Transfer processing completed")
        print("-" * 80)
        
        # Check the results
        print("\nResults:")
        print(f"  mdl_num: {test_data_copy.get('mdl_num', 'NOT SET')}")
        print(f"  transferor_court_id: {test_data_copy.get('transferor_court_id', 'NOT SET')}")
        print(f"  transferor_docket_num: {test_data_copy.get('transferor_docket_num', 'NOT SET')}")
        print(f"  is_transferred: {test_data_copy.get('is_transferred', 'NOT SET')}")
        print(f"  is_removal: {test_data_copy.get('is_removal', 'NOT SET')}")
        print(f"  pending_cto: {test_data_copy.get('pending_cto', 'NOT SET')}")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("This suggests the transfer handler may not be properly integrated")
    except Exception as e:
        print(f"❌ Error creating transfer handler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_transfer_handler())