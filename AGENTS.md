# AGENTS.md - LexGenius Development Guide for AI Agents

## Build/Test Commands
```bash
# Run all tests with coverage
python run_tests.py --coverage

# Run specific test types
pytest tests/ -m unit                    # Fast unit tests only
pytest tests/path/to/test_file.py       # Single test file
pytest tests/ -k "test_function_name"    # Single test by name
pytest tests/ -m "not slow"              # Skip slow tests
pytest tests/ --parallel                 # Run tests in parallel

# Linting (no formal config, use standard Python practices)
python -m flake8 src/ --max-line-length=120
python -m mypy src/ --ignore-missing-imports
```

## Code Style Guidelines
- **Imports**: Standard library → Third-party → Local (src.*, lib.*). One import per line for clarity
- **Type Hints**: Use type annotations for all function signatures. Prefer `Optional[T]` over `T | None`
- **Async/Await**: All I/O operations must be async with proper cleanup in try/finally blocks
- **Service Pattern**: Extend `AsyncServiceBase` for new services, use `MainServiceFactory` for DI
- **Error Handling**: Use `ComponentImplementation` base class for standardized error handling
- **Naming**: PascalCase for classes/DynamoDB fields, snake_case for functions/variables
- **File Organization**: DO NOT create @folders, v1/v2 versions, or add "Generated by Claude" to commits
- **Console Output**: Use rich formatting and progress bars for all user-facing output
- **Resource Management**: Store caches in cache/, SQLite DBs in sqlite/, temp files in appropriate dirs
- **Reuse Code**: ALWAYS search existing code before creating new functionality (check src/services/ first)